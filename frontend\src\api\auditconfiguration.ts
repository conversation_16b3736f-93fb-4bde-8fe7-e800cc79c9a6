import request from "./request";

// 查询审核流程
async function getauditconfig (params: any) {
  return request(`/unifiedplatform/v1/audit/config`, {
      method: 'get',
      params
  })
}

// 添加审核流程
async function Addauditconfig (data: any) {
  return request(`/unifiedplatform/v1/audit/config`, {
      method: 'POST',
      data
  })
}

// 添加流程审核角色
async function Addauditrole (flowId:any,data: any) {
  return request(`/unifiedplatform/v1/audit/config/role?flowId=${flowId}`, {
      method: 'POST',
      data
  })
}

// 删除审核流程的角色
async function Deleteauditrole (flowId:any,data:any) {
  return request(`/unifiedplatform/v1/audit/config/role?flowId=${flowId}`, {
    method: 'DELETE',
    data
  })
}

// 获取已添加的所有角色和用户
async function Getauditinfo (flowId:any) {
  return request(`/unifiedplatform/v1/audit/role/user/info?flowId=${flowId}`, {
    method: 'GET',
  })
}

// 添加审核流程用户
async function Addaudituser (flowId:any,data:any) {
  return request(`/unifiedplatform/v1/audit/config/user?flowId=${flowId}`, {
    method: 'POST',
    data
  })
}


// 删除审核流程用户
async function Deleteaudituser (flowId:any,data:any) {
  return request(`/unifiedplatform/v1/audit/config/user?flowId=${flowId}`, {
    method: 'DELETE',
    data
  })
}


let auditconfiguration = {
  getauditconfig,
  Addauditconfig,
  Addauditrole,
  Deleteauditrole,
  Getauditinfo,
  Addaudituser,
  Deleteaudituser
}

export default auditconfiguration;
