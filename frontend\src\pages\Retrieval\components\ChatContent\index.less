#document-chat {
  width: 100%;
  height: 100%;
  position: relative;
  &.out {
    .right-header-wrp .anticon:last-child {
      margin-right: 80px;
      position: relative;
      &::after {
        content: '';
        position: absolute;
        right: -30px;
        top: -3px;
        display: inline-block;
        width: 1px;
        height: 24px;
        background-color: #e1e5e9;
      }
    }
  }

  .right-header-wrp {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px #edeff0 solid;
    padding: 20px 0;
    margin-right: 20px;

    .name {
      max-width: calc(100% - 120px);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #2e2e2e;
      font-size: 18px;
      font-weight: 600;
    }

    .anticon {
      font-size: 18px;
      color: #666666;
      margin-left: 30px;
      cursor: pointer;
    }
  }

  .chat-wrp {
    padding: 40px 20px 0 0;
    padding-bottom: 48px;
    height: calc(100% - 150px);
    overflow: auto;
    scroll-behavior: smooth;
  }

  .input-wrp {
    width: 100%;
    position: absolute;
    bottom: 20px;

    textarea {
      background: #f7f9fa;
      border-radius: 6px;
      border: none;
      padding: 10px 20px;

      &:-webkit-input-placeholder {
        color: #a4a4a4;
      }
    }

    .sub-btn {
      padding: 4px 6px;
      position: absolute;
      bottom: 10px;
      right: 20px;
    }
  }
}
