/**
 * letterspacing 1.2v 2021-1-12
 * The tinymce-plugins is used to set the word spacing
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("letterspacing",function(c,m){function g(e,f){return(e=f.match(new RegExp(e+':?(.+?)"?[;}]')))?e[1]:!1}function k(){var e=c.dom,f=c.selection.getSelectedBlocks();h.each(f,function(b){if(e.getStyle(b,"text-indent")){var d="",a="";b.attributes.style?a=g("letter-spacing",b.attributes.style.textContent):"";b&&b.children["0"]&&b.children["0"].attributes&&b.children["0"].attributes.style?d=(d=g("font-size",b.children["0"].attributes.style.textContent))?2*(parseInt(d)+parseInt(a?
    a:0))+"px":2*(parseInt(a?a:0)+16)+"px":a?d=2*(parseInt(a?a:0)+16)+"px":"";e.setStyle(b,"text-indent",d?d:"2em")}})}var h=tinymce.util.Tools.resolve("tinymce.util.Tools"),l=c.getParam("letterspacing","0px 1px 2px 4px 6px 8px 10px 20px 40px");c.on("init",function(){c.formatter.register({letterspacing:{selector:"p,h1,h2,h3,h4,h5,h6,td,th,div,ul,ol,li,table",styles:{"letter-spacing":"%value"}}})});c.ui.registry.getAll().icons.letterspacing||c.ui.registry.addIcon("letterspacing",'<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 1024 1024"><path d="M33.450667 3.413333h102.4v956.8256H33.450667V3.413333z m887.330133 1.8432h102.4v957.713067h-102.4V5.188267z m-425.301333 200.704h108.9536l223.6416 584.977067h-102.4l-53.248-146.6368H427.485867l-53.248 146.6368h-102.4l223.6416-584.9088z m-39.3216 359.697067H643.754667L552.004267 309.248h-3.2768L456.157867 565.6576z" ></path></svg>');
    c.ui.registry.addMenuButton("letterspacing",{icon:"letterspacing",tooltip:"\u8bbe\u7f6e\u95f4\u8ddd",fetch:function(e){var f=c.dom,b=c.selection.getSelectedBlocks(),d=0;h.each(b,function(a){0==d&&(d=f.getStyle(a,"letter-spacing")?f.getStyle(a,"letter-spacing"):0)});b=l.split(" ").map(function(a){return{type:"togglemenuitem",text:a,active:d==a?!0:!1,onAction:function(){c.formatter.apply("letterspacing",{value:a});c.fire("change",{});k()}}});e(b)}});return{getMetadata:function(){return{name:"\u8bbe\u7f6e\u95f4\u8ddd",
    url:"https://github.com/Five-great/tinymce-plugins"}}}});