// @ts-nocheck
import { Component } from 'react';
import { ApplyPluginsType } from 'umi';
import dva from 'dva';
// @ts-ignore
import createLoading from 'D:/公司项目/云上川大/aitools/frontend/node_modules/dva-loading/dist/index.esm.js';
import { plugin, history } from '../core/umiExports';
import ModelCoursemap0 from 'D:/公司项目/云上川大/aitools/frontend/src/models/coursemap.ts';
import ModelGlobal1 from 'D:/公司项目/云上川大/aitools/frontend/src/models/global.ts';
import ModelMicroCourse2 from 'D:/公司项目/云上川大/aitools/frontend/src/models/microCourse.ts';
import ModelThemes3 from 'D:/公司项目/云上川大/aitools/frontend/src/models/themes.ts';
import ModelUpdate4 from 'D:/公司项目/云上川大/aitools/frontend/src/models/update.ts';
import ModelUpload5 from 'D:/公司项目/云上川大/aitools/frontend/src/models/upload.ts';

let app:any = null;

export function _onCreate(options = {}) {
  const runtimeDva = plugin.applyPlugins({
    key: 'dva',
    type: ApplyPluginsType.modify,
    initialValue: {},
  });
  app = dva({
    history,
    
    ...(runtimeDva.config || {}),
    // @ts-ignore
    ...(typeof window !== 'undefined' && window.g_useSSR ? { initialState: window.g_initialProps } : {}),
    ...(options || {}),
  });
  
  app.use(createLoading());
  (runtimeDva.plugins || []).forEach((plugin:any) => {
    app.use(plugin);
  });
  app.model({ namespace: 'coursemap', ...ModelCoursemap0 });
app.model({ namespace: 'global', ...ModelGlobal1 });
app.model({ namespace: 'microCourse', ...ModelMicroCourse2 });
app.model({ namespace: 'themes', ...ModelThemes3 });
app.model({ namespace: 'update', ...ModelUpdate4 });
app.model({ namespace: 'upload', ...ModelUpload5 });
  return app;
}

export function getApp() {
  return app;
}

/**
 * whether browser env
 * 
 * @returns boolean
 */
function isBrowser(): boolean {
  return typeof window !== 'undefined' &&
  typeof window.document !== 'undefined' &&
  typeof window.document.createElement !== 'undefined'
}

export class _DvaContainer extends Component {
  constructor(props: any) {
    super(props);
    // run only in client, avoid override server _onCreate()
    if (isBrowser()) {
      _onCreate()
    }
  }

  componentWillUnmount() {
    let app = getApp();
    app._models.forEach((model:any) => {
      app.unmodel(model.namespace);
    });
    app._models = [];
    try {
      // 释放 app，for gc
      // immer 场景 app 是 read-only 的，这里 try catch 一下
      app = null;
    } catch(e) {
      console.error(e);
    }
  }

  render() {
    let app = getApp();
    app.router(() => this.props.children);
    return app.start()();
  }
}
