/*
 * @Author: 冉志诚
 * @Date: 2023-09-19 10:29:08
 * @LastEditTime: 2023-09-19 10:30:30
 * @FilePath: \frontend\src\pages\Home\index.tsx
 * @Description:
 */
import LeftMenu from '@/components/LeftMenu';
import Header, {
  CUSTOMER_NPU,
} from '@/components/header/header';
import { Affix, Tabs } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import style from './index.less';
import clip from '@/images/overview/clip.png';
import cropping from '@/images/overview/cropping.png';
import document from '@/images/overview/document.png';
import gif from '@/images/overview/gif.png';
import info from '@/images/overview/info.png';
import photo from '@/images/overview/photo.png';
import qa from '@/images/overview/qa.png';
import school from '@/images/overview/school.png';
import translate from '@/images/overview/translate.png';
import voice from '@/images/overview/voice.png';
import logo from '@/images/overview/logo.png';
import number from '@/images/overview/number.png';
import poster from '@/images/overview/poster.png';
import outdoor from '@/images/overview/outdoor.png';
import dict from '@/images/overview/dict.png';

import { history, useSelector } from 'umi';
import Card, { CardType } from './components/Card';
import { navigationQa } from '@/api/assistant';
import Fixed from '@/components/Fixed';
import Access from '@/components/Access';
import {
  useMobileMediaScreen,
  useSelfAdaptation,
} from '@/hooks';
import { useTargetCustomer } from '@/hooks/useTargetCustomer';

const Home: React.FC<AppProps> = () => {
  useSelfAdaptation();
  const isMobile = useMobileMediaScreen();
  const modules = useSelector<any>(
    (state) => state.global.modules,
  ) as string[];
  const [computedAICards, getAICards] = useState([]);
  const [computedClipCards, getcomputedClipCards] = useState([]);
  useEffect(() => {
    navigationsettings();
  }, [])
  // 获取AI应用 
  const navigationsettings = () => {
    navigationQa().then((res: any) => {
      const filteredClipCards = res.extendMessage
        .filter((item: any) => item.toolType === 2);
      getcomputedClipCards(filteredClipCards);
      const getAICardsLsit = res.extendMessage
        .filter((item: any) => item.toolType === 1);
      getAICards(getAICardsLsit);
    });

  }

  const target = useTargetCustomer();
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  const showOther =
    parameterConfig?.target_customer === CUSTOMER_NPU;
  return (
    <div className={style.home}>
      {!showOther ? (
        <Fixed
          top={0}
          left={0}
          style={{
            width: '100%',
          }}>
          <Header showNav={true} customTitle={JSON.parse(localStorage.getItem('theme_config') || '{}')?.title || '知了智慧教育平台'} />
        </Fixed>
      ) : (
        <Header showNav={true} customTitle={JSON.parse(localStorage.getItem('theme_config') || '{}')?.title || '知了智慧教育平台'} />
      )}

      <main>
        <Access accessible={!showOther && !isMobile}>
          <Fixed top={52} left={0}>
            <LeftMenu />
          </Fixed>
        </Access>

        <div className={style.content}>
          <Access accessible={computedAICards.length !== 0}>
            <Tabs
              items={[
                {
                  label: 'AI工具集',
                  key: '1',
                },
              ]}></Tabs>
            <div className={style.cards}>
              {computedAICards.map((card) => {
                return <Card card={card} key={card.key} />;
              })}
            </div>
          </Access>
          <Access
            accessible={computedClipCards.length !== 0}>
            <Tabs
              style={{
                marginTop: 40,
              }}
              items={[
                {
                  label: '剪辑工具',
                  key: '1',
                },
              ]}></Tabs>
            <div className={style.cards}>
              {computedClipCards.map((card) => {
                return <Card card={card} key={card.key} />;
              })}
            </div>
          </Access>
        </div>
      </main>
    </div>
  );
};

interface AppProps { }
export default Home;
Home.displayName = 'Home';
