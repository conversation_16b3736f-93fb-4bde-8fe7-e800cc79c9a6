import { proxy } from './config/proxy';
import { routes } from './config/routes';
import { defineConfig } from 'umi';
// const CompressionPlugin = require("compression-webpack-plugin");
// const productionGzipExtensions = /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i;

export default defineConfig({
  // nodeModulesTransform: {
  //   type: 'none',
  // },

  // mfsu: {
  //   production: { output: '.mfsu-production' },
  // },
  targets: {
    ie: 10,
  },
  title: 'AI工具',
  publicPath:
    process.env.NODE_ENV === 'production'
      ? '/aitools/'
      : './',
  hash: true,
  metas: [
    {
      httpEquiv: 'Cache-Control',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Pragma',
      content: 'no-cache',
    },
    {
      httpEquiv: 'Expires',
      content: '0',
    },
  ],
  history: { type: 'hash' },
  routes,
  cssLoader: {
    localsConvention: 'camelCase',
  },
  locale: {
    // 国际化配置
    default: 'zh-CN',
    antd: true,
    title: true,
  },
  dynamicImport: {
    loading: '@/components/loading/loading',
  },
  proxy,
  chunks:
    process.env.NODE_ENV === 'production'
      ? ['vendors', 'umi']
      : false,
  chainWebpack(config) {
    if (process.env.NODE_ENV === 'production') {
      config.merge({
        optimization: {
          minimize: true,
          splitChunks: {
            chunks: 'async',
            minSize: 30000,
            minChunks: 2,
            automaticNameDelimiter: '.',
            cacheGroups: {
              vendor: {
                name: 'vendors',
                test: /^.*node_modules[\\/](?!lodash|react-virtualized|antd).*$/,
                chunks: 'all',
                priority: 10,
              },
              antd: {
                name: 'antd',
                test: /[\\/]node_modules[\\/]antd[\\/]/,
                chunks: 'all',
                priority: 9,
              },
              lodash: {
                name: 'lodash',
                test: /[\\/]node_modules[\\/]lodash[\\/]/,
                chunks: 'all',
                priority: -2,
              },
            },
          },
        },
      });
      //过滤掉momnet的那些不使用的国际化文件
      config
        .plugin('replace')
        .use(require('webpack').ContextReplacementPlugin)
        .tap(() => {
          return [/moment[/\\]locale$/, /zh-cn/];
        });
    } else {
      // 为开发环境修改配置...
    }
  },
});
