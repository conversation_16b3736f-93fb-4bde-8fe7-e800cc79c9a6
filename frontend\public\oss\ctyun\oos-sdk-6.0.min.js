_xamzrequire=function(){function l(a,o,s){function u(r,e){if(!o[r]){if(!a[r]){var t="function"==typeof require&&require;if(!e&&t)return t(r,!0);if(c)return c(r,!0);var i=new Error("Cannot find module '"+r+"'");throw i.code="MODULE_NOT_FOUND",i}var n=o[r]={exports:{}};a[r][0].call(n.exports,function(e){var t=a[r][1][e];return u(t||e)},n,n.exports,l,a,o,s)}return o[r].exports}for(var c="function"==typeof require&&require,e=0;e<s.length;e++)u(s[e]);return u}return l}()({38:[function(e,t,r){var i={util:e("./util")};var n={};n.toString();t.exports=i;i.util.update(i,{VERSION:"2.296.0",Signers:{},Protocol:{Json:e("./protocol/json"),Query:e("./protocol/query"),Rest:e("./protocol/rest"),RestJson:e("./protocol/rest_json"),RestXml:e("./protocol/rest_xml")},XML:{Builder:e("./xml/builder"),Parser:null},JSON:{Builder:e("./json/builder"),Parser:e("./json/parser")},Model:{Api:e("./model/api"),Operation:e("./model/operation"),Shape:e("./model/shape"),Paginator:e("./model/paginator"),ResourceWaiter:e("./model/resource_waiter")},apiLoader:e("./api_loader")});e("./service");e("./config");e("./http");e("./sequential_executor");e("./event_listeners");e("./request");e("./response");e("./resource_waiter");e("./signers/request_signer");e("./param_validator");i.events=new i.SequentialExecutor},{"./api_loader":27,"./config":37,"./event_listeners":58,"./http":59,"./json/builder":61,"./json/parser":62,"./model/api":63,"./model/operation":65,"./model/paginator":66,"./model/resource_waiter":67,"./model/shape":68,"./param_validator":69,"./protocol/json":71,"./protocol/query":72,"./protocol/rest":73,"./protocol/rest_json":74,"./protocol/rest_xml":75,"./request":80,"./resource_waiter":81,"./response":82,"./sequential_executor":84,"./service":85,"./signers/request_signer":104,"./util":112,"./xml/builder":114}],114:[function(e,t,r){var c=e("../util");var l=e("./xml-node").XmlNode;var i=e("./xml-text").XmlText;function n(){}n.prototype.toXML=function(e,t,r,i){var n=new l(r);p(n,t,true);f(n,e,t);return n.children.length>0||i?n.toString():""};function f(e,t,r){switch(r.type){case"structure":return a(e,t,r);case"map":return o(e,t,r);case"list":return s(e,t,r);default:return u(e,t,r)}}function a(a,o,s){c.arrayEach(s.memberNames,function(e){var t=s.members[e];if(t.location!=="body")return;var r=o[e];var i=t.name;if(r!==undefined&&r!==null){if(t.isXmlAttribute){a.addAttribute(i,r)}else if(t.flattened){f(a,r,t)}else{var n=new l(i);a.addChildNode(n);p(n,t);f(n,r,t)}}})}function o(a,e,o){var s=o.key.name||"key";var u=o.value.name||"value";c.each(e,function(e,t){var r=new l(o.flattened?o.name:"entry");a.addChildNode(r);var i=new l(s);var n=new l(u);r.addChildNode(i);r.addChildNode(n);f(i,e,o.key);f(n,t,o.value)})}function s(i,e,n){if(n.flattened){c.arrayEach(e,function(e){var t=n.member.name||n.name;var r=new l(t);i.addChildNode(r);f(r,e,n.member)})}else{c.arrayEach(e,function(e){var t=n.member.name||"member";var r=new l(t);i.addChildNode(r);f(r,e,n.member)})}}function u(e,t,r){e.addChildNode(new i(r.toWireFormat(t)))}function p(e,t,r){var i,n="xmlns";if(t.xmlNamespaceUri){i=t.xmlNamespaceUri;if(t.xmlNamespacePrefix)n+=":"+t.xmlNamespacePrefix}else if(r&&t.api.xmlNamespaceUri){i=t.api.xmlNamespaceUri}if(i)e.addAttribute(n,i)}t.exports=n},{"../util":112,"./xml-node":117,"./xml-text":118}],118:[function(e,t,r){var i=e("./escape-element").escapeElement;function n(e){this.value=e}n.prototype.toString=function(){return i(""+this.value)};t.exports={XmlText:n}},{"./escape-element":116}],116:[function(e,t,r){function i(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}t.exports={escapeElement:i}},{}],117:[function(e,t,r){var s=e("./escape-attribute").escapeAttribute;function i(e,t){if(t===void 0){t=[]}this.name=e;this.children=t;this.attributes={}}i.prototype.addAttribute=function(e,t){this.attributes[e]=t;return this};i.prototype.addChildNode=function(e){this.children.push(e);return this};i.prototype.removeAttribute=function(e){delete this.attributes[e];return this};i.prototype.toString=function(){var e=Boolean(this.children.length);var t="<"+this.name;var r=this.attributes;for(var i=0,n=Object.keys(r);i<n.length;i++){var a=n[i];var o=r[a];if(typeof o!=="undefined"&&o!==null){t+=" "+a+'="'+s(""+o)+'"'}}return t+=!e?"/>":">"+this.children.map(function(e){return e.toString()}).join("")+"</"+this.name+">"};t.exports={XmlNode:i}},{"./escape-attribute":115}],115:[function(e,t,r){function i(e){return e.replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;")}t.exports={escapeAttribute:i}},{}],104:[function(e,t,r){var i=e("../core");var n=i.util.inherit;i.Signers.RequestSigner=n({constructor:function e(t){this.request=t},setServiceClientId:function e(t){this.serviceClientId=t},getServiceClientId:function e(){return this.serviceClientId}});i.Signers.RequestSigner.getVersion=function e(t){switch(t){case"v2":return i.Signers.V2;case"v3":return i.Signers.V3;case"v4":return i.Signers.V4;case"s3":return i.Signers.S3;case"v3https":return i.Signers.V3Https}throw new Error("Unknown signing version "+t)};e("./v2");e("./v3");e("./v3https");e("./v4");e("./s3");e("./presign")},{"../core":38,"./presign":103,"./s3":105,"./v2":106,"./v3":107,"./v3https":108,"./v4":109}],109:[function(e,t,r){var o=e("../core");var n=e("./v4_credentials");var i=o.util.inherit;var s="presigned-expires";o.Signers.V4=i(o.Signers.RequestSigner,{constructor:function e(t,r,i){o.Signers.RequestSigner.call(this,t);this.serviceName=r;i=i||{};this.signatureCache=typeof i.signatureCache==="boolean"?i.signatureCache:true;this.operation=i.operation},algorithm:"AWS4-HMAC-SHA256",addAuthorization:function e(t,r){var i=o.util.date.iso8601(r).replace(/[:\-]|\.\d{3}/g,"");if(this.isPresigned()){this.updateForPresigned(t,i)}else{this.addHeaders(t,i)}this.request.headers["Authorization"]=this.authorization(t,i)},addHeaders:function e(t,r){this.request.headers["X-Amz-Date"]=r;if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}},updateForPresigned:function e(t,r){var i=this.credentialString(r);var n={"X-Amz-Date":r,"X-Amz-Algorithm":this.algorithm,"X-Amz-Credential":t.accessKeyId+"/"+i,"X-Amz-Expires":this.request.headers[s],"X-Amz-SignedHeaders":this.signedHeaders()};if(t.sessionToken){n["X-Amz-Security-Token"]=t.sessionToken}if(this.request.headers["Content-Type"]){n["Content-Type"]=this.request.headers["Content-Type"]}if(this.request.headers["Content-MD5"]){n["Content-MD5"]=this.request.headers["Content-MD5"]}if(this.request.headers["Cache-Control"]){n["Cache-Control"]=this.request.headers["Cache-Control"]}o.util.each.call(this,this.request.headers,function(e,t){if(e===s)return;if(this.isSignableHeader(e)){var r=e.toLowerCase();if(r.indexOf("x-amz-meta-")===0){n[r]=t}else if(r.indexOf("x-amz-")===0){n[e]=t}}});var a=this.request.path.indexOf("?")>=0?"&":"?";this.request.path+=a+o.util.queryParamsToString(n)},authorization:function e(t,r){var i=[];var n=this.credentialString(r);i.push(this.algorithm+" Credential="+t.accessKeyId+"/"+n);i.push("SignedHeaders="+this.signedHeaders());i.push("Signature="+this.signature(t,r));return i.join(", ")},signature:function e(t,r){var i=n.getSigningKey(t,r.substr(0,8),this.request.region,this.serviceName,this.signatureCache);return o.util.crypto.hmac(i,this.stringToSign(r),"hex")},stringToSign:function e(t){var r=[];r.push("AWS4-HMAC-SHA256");r.push(t);r.push(this.credentialString(t));r.push(this.hexEncodedHash(this.canonicalString()));return r.join("\n")},canonicalString:function e(){var t=[],r=this.request.pathname();if(this.serviceName!=="s3")r=o.util.uriEscapePath(r);t.push(this.request.method);t.push(r);t.push(this.request.search());t.push(this.canonicalHeaders()+"\n");t.push(this.signedHeaders());t.push(this.hexEncodedBodyHash());return t.join("\n")},canonicalHeaders:function e(){var r=[];o.util.each.call(this,this.request.headers,function(e,t){r.push([e,t])});r.sort(function(e,t){return e[0].toLowerCase()<t[0].toLowerCase()?-1:1});var i=[];o.util.arrayEach.call(this,r,function(e){var t=e[0].toLowerCase();if(this.isSignableHeader(t)){var r=e[1];if(typeof r==="undefined"||r===null||typeof r.toString!=="function"){throw o.util.error(new Error("Header "+t+" contains invalid value"),{code:"InvalidHeader"})}i.push(t+":"+this.canonicalHeaderValues(r.toString()))}});return i.join("\n")},canonicalHeaderValues:function e(t){return t.replace(/\s+/g," ").replace(/^\s+|\s+$/g,"")},signedHeaders:function e(){var t=[];o.util.each.call(this,this.request.headers,function(e){e=e.toLowerCase();if(this.isSignableHeader(e))t.push(e)});return t.sort().join(";")},credentialString:function e(t){return n.createScope(t.substr(0,8),this.request.region,this.serviceName)},hexEncodedHash:function e(t){return o.util.crypto.sha256(t,"hex")},hexEncodedBodyHash:function e(){var t=this.request;if(this.isPresigned()&&this.serviceName==="s3"&&!t.body){return"UNSIGNED-PAYLOAD"}else if(t.headers["X-Amz-Content-Sha256"]){return t.headers["X-Amz-Content-Sha256"]}else{return this.hexEncodedHash(this.request.body||"")}},unsignableHeaders:["authorization","x-ctyun-data-location","content-length","user-agent",s,"expect","x-amzn-trace-id"],isSignableHeader:function e(t){if(t.toLowerCase().indexOf("x-amz-")===0)return true;return this.unsignableHeaders.indexOf(t)<0},isPresigned:function e(){return this.request.headers[s]?true:false}});t.exports=o.Signers.V4},{"../core":38,"./v4_credentials":110}],110:[function(e,t,r){var p=e("../core");var h={};var d=[];var m=50;var y="aws4_request";t.exports={createScope:function e(t,r,i){return[t.substr(0,8),r,i,y].join("/")},getSigningKey:function e(t,r,i,n,a){var o=p.util.crypto.hmac(t.secretAccessKey,t.accessKeyId,"base64");var s=[o,r,i,n].join("_");a=a!==false;if(a&&s in h){return h[s]}var u=p.util.crypto.hmac("AWS4"+t.secretAccessKey,r,"buffer");var c=p.util.crypto.hmac(u,i,"buffer");var l=p.util.crypto.hmac(c,n,"buffer");var f=p.util.crypto.hmac(l,y,"buffer");if(a){h[s]=f;d.push(s);if(d.length>m){delete h[d.shift()]}}return f},emptyCache:function e(){h={};d=[]}}},{"../core":38}],108:[function(e,t,r){var i=e("../core");var n=i.util.inherit;e("./v3");i.Signers.V3Https=n(i.Signers.V3,{authorization:function e(t){return"AWS3-HTTPS "+"AWSAccessKeyId="+t.accessKeyId+","+"Algorithm=HmacSHA256,"+"Signature="+this.signature(t)},stringToSign:function e(){return this.request.headers["X-Amz-Date"]}});t.exports=i.Signers.V3Https},{"../core":38,"./v3":107}],107:[function(e,t,r){var n=e("../core");var i=n.util.inherit;n.Signers.V3=i(n.Signers.RequestSigner,{addAuthorization:function e(t,r){var i=n.util.date.rfc822(r);this.request.headers["X-Amz-Date"]=i;if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}this.request.headers["X-Amzn-Authorization"]=this.authorization(t,i)},authorization:function e(t){return"AWS3 "+"AWSAccessKeyId="+t.accessKeyId+","+"Algorithm=HmacSHA256,"+"SignedHeaders="+this.signedHeaders()+","+"Signature="+this.signature(t)},signedHeaders:function e(){var r=[];n.util.arrayEach(this.headersToSign(),function e(t){r.push(t.toLowerCase())});return r.sort().join(";")},canonicalHeaders:function e(){var r=this.request.headers;var i=[];n.util.arrayEach(this.headersToSign(),function e(t){i.push(t.toLowerCase().trim()+":"+String(r[t]).trim())});return i.sort().join("\n")+"\n"},headersToSign:function e(){var r=[];n.util.each(this.request.headers,function e(t){if(t==="Host"||t==="Content-Encoding"||t.match(/^X-Amz/i)){r.push(t)}});return r},signature:function e(t){return n.util.crypto.hmac(t.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function e(){var t=[];t.push(this.request.method);t.push("/");t.push("");t.push(this.canonicalHeaders());t.push(this.request.body);return n.util.crypto.sha256(t.join("\n"))}});t.exports=n.Signers.V3},{"../core":38}],106:[function(e,t,r){var n=e("../core");var i=n.util.inherit;n.Signers.V2=i(n.Signers.RequestSigner,{addAuthorization:function e(t,r){if(!r)r=n.util.date.getDate();var i=this.request;i.params.Timestamp=n.util.date.iso8601(r);i.params.SignatureVersion="2";i.params.SignatureMethod="HmacSHA256";i.params.AWSAccessKeyId=t.accessKeyId;if(t.sessionToken){i.params.SecurityToken=t.sessionToken}delete i.params.Signature;i.params.Signature=this.signature(t);i.body=n.util.queryParamsToString(i.params);i.headers["Content-Length"]=i.body.length},signature:function e(t){return n.util.crypto.hmac(t.secretAccessKey,this.stringToSign(),"base64")},stringToSign:function e(){var t=[];t.push(this.request.method);t.push(this.request.endpoint.host.toLowerCase());t.push(this.request.pathname());t.push(n.util.queryParamsToString(this.request.params));return t.join("\n")}});t.exports=n.Signers.V2},{"../core":38}],105:[function(e,t,r){var s=e("../core");var i=s.util.inherit;s.Signers.S3=i(s.Signers.RequestSigner,{subResources:{acl:1,accelerate:1,analytics:1,cors:1,lifecycle:1,delete:1,inventory:1,location:1,logging:1,metrics:1,notification:1,partNumber:1,policy:1,requestPayment:1,replication:1,restore:1,tagging:1,torrent:1,uploadId:1,uploads:1,versionId:1,versioning:1,versions:1,website:1},responseHeaders:{"response-content-type":1,"response-content-language":1,"response-expires":1,"response-cache-control":1,"response-content-disposition":1,"response-content-encoding":1},addAuthorization:function e(t,r){if(!this.request.headers["presigned-expires"]){this.request.headers["X-Amz-Date"]=s.util.date.rfc822(r)}if(t.sessionToken){this.request.headers["x-amz-security-token"]=t.sessionToken}var i=this.sign(t.secretAccessKey,this.stringToSign());var n="AWS "+t.accessKeyId+":"+i;this.request.headers["Authorization"]=n},stringToSign:function e(){var t=this.request;var r=[];r.push(t.method);r.push(t.headers["Content-MD5"]||"");r.push(t.headers["Content-Type"]||"");r.push(t.headers["presigned-expires"]||"");var i=this.canonicalizedAmzHeaders();if(i)r.push(i);r.push(this.canonicalizedResource());return r.join("\n")},canonicalizedAmzHeaders:function e(){var t=[];s.util.each(this.request.headers,function(e){if(e.match(/^x-amz-/i))t.push(e)});t.sort(function(e,t){return e.toLowerCase()<t.toLowerCase()?-1:1});var r=[];s.util.arrayEach.call(this,t,function(e){r.push(e.toLowerCase()+":"+String(this.request.headers[e]))});return r.join("\n")},canonicalizedResource:function e(){var t=this.request;var r=t.path.split("?");var i=r[0];var n=r[1];var a="";if(t.virtualHostedBucket)a+="/"+t.virtualHostedBucket;a+=i;if(n&&n!=="accelerate"){var o=[];s.util.arrayEach.call(this,n.split("&"),function(e){var t=e.split("=")[0];var r=e.split("=")[1];if(this.subResources[t]||this.responseHeaders[t]){var i={name:t};if(r!==undefined){if(this.subResources[t]){i.value=r}else{i.value=decodeURIComponent(r)}}o.push(i)}});o.sort(function(e,t){return e.name<t.name?-1:1});if(o.length){n=[];s.util.arrayEach(o,function(e){if(e.value===undefined){n.push(e.name)}else{n.push(e.name+"="+e.value)}});a+="?"+n.join("&")}}return a},sign:function e(t,r){return s.util.crypto.hmac(t,r,"base64","sha1")}});t.exports=s.Signers.S3},{"../core":38}],103:[function(e,t,r){var s=e("../core");var i=s.util.inherit;var u="presigned-expires";function n(e){var t=e.httpRequest.headers[u];var r=e.service.getSignerClass(e);delete e.httpRequest.headers["User-Agent"];delete e.httpRequest.headers["X-Amz-User-Agent"];if(r===s.Signers.V4){if(t>604800){var i="Presigning does not support expiry time greater "+"than a week with SigV4 signing.";throw s.util.error(new Error,{code:"InvalidExpiryTime",message:i,retryable:false})}e.httpRequest.headers[u]=t}else if(r===s.Signers.S3){var n=e.service?e.service.getSkewCorrectedDate():s.util.date.getDate();e.httpRequest.headers[u]=parseInt(s.util.date.unixTimestamp(n)+t,10).toString()}else{throw s.util.error(new Error,{message:"Presigning only supports S3 or SigV4 signing.",code:"UnsupportedSigner",retryable:false})}}function a(e){var t=e.httpRequest.endpoint;var r=s.util.urlParse(e.httpRequest.path);var i={};if(r.search){i=s.util.queryStringParse(r.search.substr(1))}var n=e.httpRequest.headers["Authorization"].split(" ");if(n[0]==="AWS"){n=n[1].split(":");i["AWSAccessKeyId"]=n[0];i["Signature"]=n[1];s.util.each(e.httpRequest.headers,function(e,t){if(e===u)e="Expires";if(e.indexOf("x-amz-meta-")===0){delete i[e];e=e.toLowerCase()}i[e]=t});delete e.httpRequest.headers[u];delete i["Authorization"];delete i["Host"]}else if(n[0]==="AWS4-HMAC-SHA256"){n.shift();var a=n.join(" ");var o=a.match(/Signature=(.*?)(?:,|\s|\r?\n|$)/)[1];i["X-Amz-Signature"]=o;delete i["Expires"]}t.pathname=r.pathname;t.search=s.util.queryParamsToString(i)}s.Signers.Presign=i({sign:function e(t,r,i){t.httpRequest.headers[u]=r||3600;t.on("build",n);t.on("sign",a);t.removeListener("afterBuild",s.EventListeners.Core.SET_CONTENT_LENGTH);t.removeListener("afterBuild",s.EventListeners.Core.COMPUTE_SHA256);t.emit("beforePresign",[t]);if(i){t.build(function(){if(this.response.error)i(this.response.error);else{i(null,s.util.urlFormat(t.httpRequest.endpoint))}})}else{t.build();if(t.response.error)throw t.response.error;return s.util.urlFormat(t.httpRequest.endpoint)}}});t.exports=s.Signers.Presign},{"../core":38}],85:[function(e,t,r){var o=e("./core");var s=e("./model/api");var i=e("./region_config");var u=o.util.inherit;var a=0;o.Service=u({constructor:function e(t){if(!this.loadServiceClass){throw o.util.error(new Error,"Service must be constructed with `new' operator")}var r=this.loadServiceClass(t||{});if(r){var i=o.util.copy(t);var n=new r(t);Object.defineProperty(n,"_originalConfig",{get:function(){return i},enumerable:false,configurable:true});n._clientId=++a;return n}this.initialize(t)},initialize:function e(t){var r=o.config[this.serviceIdentifier];this.config=new o.Config(o.config);if(r)this.config.update(r,true);if(t)this.config.update(t,true);this.validateService();if(!this.config.endpoint)i(this);this.config.endpoint=this.endpointFromTemplate(this.config.endpoint);this.setEndpoint(this.config.endpoint)},validateService:function e(){},loadServiceClass:function e(t){var r=t;if(!o.util.isEmpty(this.api)){return null}else if(r.apiConfig){return o.Service.defineServiceApi(this.constructor,r.apiConfig)}else if(!this.constructor.services){return null}else{r=new o.Config(o.config);r.update(t,true);var i=r.apiVersions[this.constructor.serviceIdentifier];i=i||r.apiVersion;return this.getLatestServiceClass(i)}},getLatestServiceClass:function e(t){t=this.getLatestServiceVersion(t);if(this.constructor.services[t]===null){o.Service.defineServiceApi(this.constructor,t)}return this.constructor.services[t]},getLatestServiceVersion:function e(t){if(!this.constructor.services||this.constructor.services.length===0){throw new Error("No services defined on "+this.constructor.serviceIdentifier)}if(!t){t="latest"}else if(o.util.isType(t,Date)){t=o.util.date.iso8601(t).split("T")[0]}if(Object.hasOwnProperty(this.constructor.services,t)){return t}var r=Object.keys(this.constructor.services).sort();var i=null;for(var n=r.length-1;n>=0;n--){if(r[n][r[n].length-1]!=="*"){i=r[n]}if(r[n].substr(0,10)<=t){return i}}throw new Error("Could not find "+this.constructor.serviceIdentifier+" API to satisfy version constraint `"+t+"'")},api:{},defaultRetryCount:3,customizeRequests:function e(t){if(!t){this.customRequestHandler=null}else if(typeof t==="function"){this.customRequestHandler=t}else{throw new Error("Invalid callback type '"+typeof t+"' provided in customizeRequests")}},makeRequest:function e(t,r,i){if(typeof r==="function"){i=r;r=null}r=r||{};if(this.config.params){var n=this.api.operations[t];if(n){r=o.util.copy(r);o.util.each(this.config.params,function(e,t){if(n.input.members[e]){if(r[e]===undefined||r[e]===null){r[e]=t}}})}}var a=new o.Request(this,t,r);this.addAllRequestListeners(a);if(i)a.send(i);return a},makeUnauthenticatedRequest:function e(t,r,i){if(typeof r==="function"){i=r;r={}}var n=this.makeRequest(t,r).toUnauthenticated();return i?n.send(i):n},waitFor:function e(t,r,i){var n=new o.ResourceWaiter(this,t);return n.wait(r,i)},addAllRequestListeners:function e(t){var r=[o.events,o.EventListeners.Core,this.serviceInterface(),o.EventListeners.CorePost];for(var i=0;i<r.length;i++){if(r[i])t.addListeners(r[i])}if(!this.config.paramValidation){t.removeListener("validate",o.EventListeners.Core.VALIDATE_PARAMETERS)}if(this.config.logger){t.addListeners(o.EventListeners.Logger)}this.setupRequestListeners(t);if(typeof this.constructor.prototype.customRequestHandler==="function"){this.constructor.prototype.customRequestHandler(t)}if(Object.prototype.hasOwnProperty.call(this,"customRequestHandler")&&typeof this.customRequestHandler==="function"){this.customRequestHandler(t)}},setupRequestListeners:function e(){},getSignerClass:function e(t){var r;var i=null;var n="";if(t){var a=t.service.api.operations||{};i=a[t.operation]||null;n=i?i.authtype:""}if(this.config.signatureVersion){r=this.config.signatureVersion}else if(n==="v4"||n==="v4-unsigned-body"){r="v4"}else{r=this.api.signatureVersion}return o.Signers.RequestSigner.getVersion(r)},serviceInterface:function e(){switch(this.api.protocol){case"ec2":return o.EventListeners.Query;case"query":return o.EventListeners.Query;case"json":return o.EventListeners.Json;case"rest-json":return o.EventListeners.RestJson;case"rest-xml":return o.EventListeners.RestXml}if(this.api.protocol){throw new Error("Invalid service `protocol' "+this.api.protocol+" in API config")}},successfulResponse:function e(t){return t.httpResponse.statusCode<300},numRetries:function e(){if(this.config.maxRetries!==undefined){return this.config.maxRetries}else{return this.defaultRetryCount}},retryDelays:function e(t){return o.util.calculateRetryDelay(t,this.config.retryDelayOptions)},retryableError:function e(t){if(this.timeoutError(t))return true;if(this.networkingError(t))return true;if(this.expiredCredentialsError(t))return true;if(this.throttledError(t))return true;if(t.statusCode>=500)return true;return false},networkingError:function e(t){return t.code==="NetworkingError"},timeoutError:function e(t){return t.code==="TimeoutError"},expiredCredentialsError:function e(t){return t.code==="ExpiredTokenException"},clockSkewError:function e(t){switch(t.code){case"RequestTimeTooSkewed":case"RequestExpired":case"InvalidSignatureException":case"SignatureDoesNotMatch":case"AuthFailure":case"RequestInTheFuture":return true;default:return false}},getSkewCorrectedDate:function e(){return new Date(Date.now()+this.config.systemClockOffset)},applyClockOffset:function e(t){if(t){this.config.systemClockOffset=t-Date.now()}},isClockSkewed:function e(t){if(t){return Math.abs(this.getSkewCorrectedDate().getTime()-t)>=3e4}},throttledError:function e(t){switch(t.code){case"ProvisionedThroughputExceededException":case"Throttling":case"ThrottlingException":case"RequestLimitExceeded":case"RequestThrottled":return true;default:return false}},endpointFromTemplate:function e(t){if(typeof t!=="string")return t;var r=t;r=r.replace(/\{service\}/g,this.api.endpointPrefix);r=r.replace(/\{region\}/g,this.config.region);r=r.replace(/\{scheme\}/g,this.config.sslEnabled?"https":"http");return r},setEndpoint:function e(t){this.endpoint=new o.Endpoint(t,this.config)},paginationConfig:function e(t,r){var i=this.api.operations[t].paginator;if(!i){if(r){var n=new Error;throw o.util.error(n,"No pagination configuration for "+t)}return null}return i}});o.util.update(o.Service,{defineMethods:function e(i){o.util.each(i.prototype.api.operations,function e(r){if(i.prototype[r])return;var t=i.prototype.api.operations[r];if(t.authtype==="none"){i.prototype[r]=function(e,t){return this.makeUnauthenticatedRequest(r,e,t)}}else{i.prototype[r]=function(e,t){return this.makeRequest(r,e,t)}}})},defineService:function e(t,r,i){o.Service._serviceMap[t]=true;if(!Array.isArray(r)){i=r;r=[]}var n=u(o.Service,i||{});if(typeof t==="string"){o.Service.addVersions(n,r);var a=n.serviceIdentifier||t;n.serviceIdentifier=a}else{n.prototype.api=t;o.Service.defineMethods(n)}return n},addVersions:function e(t,r){if(!Array.isArray(r))r=[r];t.services=t.services||{};for(var i=0;i<r.length;i++){if(t.services[r[i]]===undefined){t.services[r[i]]=null}}t.apiVersions=Object.keys(t.services).sort()},defineServiceApi:function e(t,r,i){var n=u(t,{serviceIdentifier:t.serviceIdentifier});function a(e){if(e.isApi){n.prototype.api=e}else{n.prototype.api=new s(e)}}if(typeof r==="string"){if(i){a(i)}else{try{a(o.apiLoader(t.serviceIdentifier,r))}catch(e){throw o.util.error(e,{message:"Could not find API configuration "+t.serviceIdentifier+"-"+r})}}if(!Object.prototype.hasOwnProperty.call(t.services,r)){t.apiVersions=t.apiVersions.concat(r).sort()}t.services[r]=n}else{a(r)}o.Service.defineMethods(n);return n},hasService:function(e){return Object.prototype.hasOwnProperty.call(o.Service._serviceMap,e)},_serviceMap:{}});t.exports=o.Service},{"./core":38,"./model/api":63,"./region_config":78}],78:[function(e,t,r){var a=e("./util");var o=e("./region_config_data.json");function n(e){if(!e)return null;var t=e.split("-");if(t.length<3)return null;return t.slice(0,t.length-2).join("-")+"-*"}function s(e){var t=e.config.region;var r=n(t);var i=e.api.endpointPrefix;return[[t,i],[r,i],[t,"*"],[r,"*"],["*",i],["*","*"]].map(function(e){return e[0]&&e[1]?e.join("/"):null})}function u(r,e){a.each(e,function(e,t){if(e==="globalEndpoint")return;if(r.config[e]===undefined||r.config[e]===null){r.config[e]=t}})}function i(e){var t=s(e);for(var r=0;r<t.length;r++){var i=t[r];if(!i)continue;if(Object.prototype.hasOwnProperty.call(o.rules,i)){var n=o.rules[i];if(typeof n==="string"){n=o.patterns[n]}if(e.config.useDualstack&&a.isDualstackAvailable(e)){n=a.copy(n);n.endpoint="{service}.dualstack.{region}.amazonaws.com"}e.isGlobalEndpoint=!!n.globalEndpoint;if(!n.signatureVersion)n.signatureVersion="v4";u(e,n);return}}}t.exports=i},{"./region_config_data.json":79,"./util":112}],79:[function(e,t,r){t.exports={rules:{"*/*":{endpoint:"{service}.{region}.amazonaws.com"},"cn-*/*":{endpoint:"{service}.{region}.amazonaws.com.cn"},"*/budgets":"globalSSL","*/cloudfront":"globalSSL","*/iam":"globalSSL","*/sts":"globalSSL","*/importexport":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2",globalEndpoint:true},"*/route53":{endpoint:"https://{service}.amazonaws.com",signatureVersion:"v3https",globalEndpoint:true},"*/waf":"globalSSL","us-gov-*/iam":"globalGovCloud","us-gov-*/sts":{endpoint:"{service}.{region}.amazonaws.com"},"us-gov-west-1/s3":"s3signature","us-west-1/s3":"s3signature","us-west-2/s3":"s3signature","eu-west-1/s3":"s3signature","ap-southeast-1/s3":"s3signature","ap-southeast-2/s3":"s3signature","ap-northeast-1/s3":"s3signature","sa-east-1/s3":"s3signature","cn/s3":{endpoint:"{service}.amazonaws.com",signatureVersion:"s3"},"cn/sdb":{endpoint:"{service}.amazonaws.com",signatureVersion:"v2"},"*/sdb":{endpoint:"{service}.{region}.amazonaws.com",signatureVersion:"v2"}},patterns:{globalSSL:{endpoint:"https://{service}.amazonaws.com",globalEndpoint:true},globalGovCloud:{endpoint:"{service}.us-gov.amazonaws.com"},s3signature:{endpoint:"{service}.{region}.amazonaws.com",signatureVersion:"s3"}}}},{}],82:[function(e,t,r){var u=e("./core");var i=u.util.inherit;var n=e("jmespath");u.Response=i({constructor:function e(t){this.request=t;this.data=null;this.error=null;this.retryCount=0;this.redirectCount=0;this.httpResponse=new u.HttpResponse;if(t){this.maxRetries=t.service.numRetries();this.maxRedirects=t.service.config.maxRedirects}},nextPage:function e(t){var r;var i=this.request.service;var n=this.request.operation;try{r=i.paginationConfig(n,true)}catch(e){this.error=e}if(!this.hasNextPage()){if(t)t(this.error,null);else if(this.error)throw this.error;return null}var a=u.util.copy(this.request.params);if(!this.nextPageTokens){return t?t(null,null):null}else{var o=r.inputToken;if(typeof o==="string")o=[o];for(var s=0;s<o.length;s++){a[o[s]]=this.nextPageTokens[s]}return i.makeRequest(this.request.operation,a,t)}},hasNextPage:function e(){this.cacheNextPageTokens();if(this.nextPageTokens)return true;if(this.nextPageTokens===undefined)return undefined;else return false},cacheNextPageTokens:function e(){if(Object.prototype.hasOwnProperty.call(this,"nextPageTokens"))return this.nextPageTokens;this.nextPageTokens=undefined;var t=this.request.service.paginationConfig(this.request.operation);if(!t)return this.nextPageTokens;this.nextPageTokens=null;if(t.moreResults){if(!n.search(this.data,t.moreResults)){return this.nextPageTokens}}var r=t.outputToken;if(typeof r==="string")r=[r];u.util.arrayEach.call(this,r,function(e){var t=n.search(this.data,e);if(t){this.nextPageTokens=this.nextPageTokens||[];this.nextPageTokens.push(t)}});return this.nextPageTokens}})},{"./core":38,jmespath:8}],81:[function(e,t,r){var n=e("./core");var i=n.util.inherit;var o=e("jmespath");function a(r){var i=r.request._waiter;var e=i.config.acceptors;var n=false;var a="retry";e.forEach(function(e){if(!n){var t=i.matchers[e.matcher];if(t&&t(r,e.expected,e.argument)){n=true;a=e.state}}});if(!n&&r.error)a="failure";if(a==="success"){i.setSuccess(r)}else{i.setError(r,a==="retry")}}n.ResourceWaiter=i({constructor:function e(t,r){this.service=t;this.state=r;this.loadWaiterConfig(this.state)},service:null,state:null,config:null,matchers:{path:function(e,t,r){try{var i=o.search(e.data,r)}catch(e){return false}return o.strictDeepEqual(i,t)},pathAll:function(e,t,r){try{var i=o.search(e.data,r)}catch(e){return false}if(!Array.isArray(i))i=[i];var n=i.length;if(!n)return false;for(var a=0;a<n;a++){if(!o.strictDeepEqual(i[a],t)){return false}}return true},pathAny:function(e,t,r){try{var i=o.search(e.data,r)}catch(e){return false}if(!Array.isArray(i))i=[i];var n=i.length;for(var a=0;a<n;a++){if(o.strictDeepEqual(i[a],t)){return true}}return false},status:function(e,t){var r=e.httpResponse.statusCode;return typeof r==="number"&&r===t},error:function(e,t){if(typeof t==="string"&&e.error){return t===e.error.code}return t===!!e.error}},listeners:(new n.SequentialExecutor).addNamedListeners(function(e){e("RETRY_CHECK","retry",function(e){var t=e.request._waiter;if(e.error&&e.error.code==="ResourceNotReady"){e.error.retryDelay=(t.config.delay||0)*1e3}});e("CHECK_OUTPUT","extractData",a);e("CHECK_ERROR","extractError",a)}),wait:function e(t,r){if(typeof t==="function"){r=t;t=undefined}if(t&&t.$waiter){t=n.util.copy(t);if(typeof t.$waiter.delay==="number"){this.config.delay=t.$waiter.delay}if(typeof t.$waiter.maxAttempts==="number"){this.config.maxAttempts=t.$waiter.maxAttempts}delete t.$waiter}var i=this.service.makeRequest(this.config.operation,t);i._waiter=this;i.response.maxRetries=this.config.maxAttempts;i.addListeners(this.listeners);if(r)i.send(r);return i},setSuccess:function e(t){t.error=null;t.data=t.data||{};t.request.removeAllListeners("extractData")},setError:function e(t,r){t.data=null;t.error=n.util.error(t.error||new Error,{code:"ResourceNotReady",message:"Resource is not in the state "+this.state,retryable:r})},loadWaiterConfig:function e(t){if(!this.service.api.waiters[t]){throw new n.util.error(new Error,{code:"StateNotFoundError",message:"State "+t+" not found."})}this.config=n.util.copy(this.service.api.waiters[t])}})},{"./core":38,jmespath:8}],80:[function(n,e,t){(function(t){var h=n("./core");var s=n("./state_machine");var e=h.util.inherit;var u=h.util.domain;var c=n("jmespath");var r={success:1,error:1,complete:1};function i(e){return Object.prototype.hasOwnProperty.call(r,e._asm.currentState)}var l=new s;l.setupStates=function(){var e=function(e,t){var r=this;r._haltHandlersOnError=false;r.emit(r._asm.currentState,function(e){if(e){if(i(r)){if(u&&r.domain instanceof u.Domain){e.domainEmitter=r;e.domain=r.domain;e.domainThrown=false;r.domain.emit("error",e)}else{throw e}}else{r.response.error=e;t(e)}}else{t(r.response.error)}})};this.addState("validate","build","error",e);this.addState("build","afterBuild","restart",e);this.addState("afterBuild","sign","restart",e);this.addState("sign","send","retry",e);this.addState("retry","afterRetry","afterRetry",e);this.addState("afterRetry","sign","error",e);this.addState("send","validateResponse","retry",e);this.addState("validateResponse","extractData","extractError",e);this.addState("extractError","extractData","retry",e);this.addState("extractData","success","retry",e);this.addState("restart","build","error",e);this.addState("success","complete","complete",e);this.addState("error","complete","complete",e);this.addState("complete",null,null,e)};l.setupStates();h.Request=e({constructor:function e(t,r,i){var n=t.endpoint;var a=n.host.split("oos-")[1].split(".")[0];var o=t.config.customUserAgent;if(t.isGlobalEndpoint)a="cn";this.domain=u&&u.active;this.service=t;this.operation=r;this.params=i||{};this.httpRequest=new h.HttpRequest(n,a);this.httpRequest.appendToUserAgent(o);this.startTime=t.getSkewCorrectedDate();this.response=new h.Response(this);this._asm=new s(l.states,"validate");this._haltHandlersOnError=false;h.SequentialExecutor.call(this);this.emit=this.emitEvent},send:function e(t){if(t){this.httpRequest.appendToUserAgent("callback");this.on("complete",function(e){t.call(e,e.error,e.data)})}this.runTo();return this.response},build:function e(t){return this.runTo("send",t)},runTo:function e(t,r){this._asm.runTo(t,r,this);return this},abort:function e(){this.removeAllListeners("validateResponse");this.removeAllListeners("extractError");this.on("validateResponse",function e(t){t.error=h.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:false})});if(this.httpRequest.stream&&!this.httpRequest.stream.didCallback){this.httpRequest.stream.abort();if(this.httpRequest._abortCallback){this.httpRequest._abortCallback()}else{this.removeAllListeners("send")}}return this},eachPage:function e(r){r=h.util.fn.makeAsync(r,3);function i(t){r.call(t,t.error,t.data,function(e){if(e===false)return;if(t.hasNextPage()){t.nextPage().on("complete",i).send()}else{r.call(t,null,null,h.util.fn.noop)}})}this.on("complete",i).send()},eachItem:function e(o){var s=this;function t(e,t){if(e)return o(e,null);if(t===null)return o(null,null);var r=s.service.paginationConfig(s.operation);var i=r.resultKey;if(Array.isArray(i))i=i[0];var n=c.search(t,i);var a=true;h.util.arrayEach(n,function(e){a=o(null,e);if(a===false){return h.util.abort}});return a}this.eachPage(t)},isPageable:function e(){return this.service.paginationConfig(this.operation)?true:false},createReadStream:function e(){var l=h.util.stream;var f=this;var p=null;if(h.HttpClient.streamsApiVersion===2){p=new l.PassThrough;t.nextTick(function(){f.send()})}else{p=new l.Stream;p.readable=true;p.sent=false;p.on("newListener",function(e){if(!p.sent&&e==="data"){p.sent=true;t.nextTick(function(){f.send()})}})}this.on("error",function(e){p.emit("error",e)});this.on("httpHeaders",function e(t,r,i){if(t<300){f.removeListener("httpData",h.EventListeners.Core.HTTP_DATA);f.removeListener("httpError",h.EventListeners.Core.HTTP_ERROR);f.on("httpError",function e(t){i.error=t;i.error.retryable=false});var n=false;var a;if(f.httpRequest.method!=="HEAD"){a=parseInt(r["content-length"],10)}if(a!==undefined&&!isNaN(a)&&a>=0){n=true;var o=0}var s=function e(){if(n&&o!==a){p.emit("error",h.util.error(new Error("Stream content length mismatch. Received "+o+" of "+a+" bytes."),{code:"StreamContentLengthMismatch"}))}else if(h.HttpClient.streamsApiVersion===2){p.end()}else{p.emit("end")}};var u=i.httpResponse.createUnbufferedStream();if(h.HttpClient.streamsApiVersion===2){if(n){var c=new l.PassThrough;c._write=function(e){if(e&&e.length){o+=e.length}return l.PassThrough.prototype._write.apply(this,arguments)};c.on("end",s);p.on("error",function(e){n=false;u.unpipe(c);c.emit("end");c.end()});u.pipe(c).pipe(p,{end:false})}else{u.pipe(p)}}else{if(n){u.on("data",function(e){if(e&&e.length){o+=e.length}})}u.on("data",function(e){p.emit("data",e)});u.on("end",s)}u.on("error",function(e){n=false;p.emit("error",e)})}});return p},emitEvent:function e(t,r,i){if(typeof r==="function"){i=r;r=null}if(!i)i=function(){};if(!r)r=this.eventParameters(t,this.response);var n=h.SequentialExecutor.prototype.emit;n.call(this,t,r,function(e){if(e)this.response.error=e;i.call(this,e)})},eventParameters:function e(t){switch(t){case"restart":case"validate":case"sign":case"build":case"afterValidate":case"afterBuild":return[this];case"error":return[this.response.error,this.response];default:return[this.response]}},presign:function e(t,r){if(!r&&typeof t==="function"){r=t;t=null}return(new h.Signers.Presign).sign(this.toGet(),t,r)},isPresigned:function e(){return Object.prototype.hasOwnProperty.call(this.httpRequest.headers,"presigned-expires")},toUnauthenticated:function e(){this.removeListener("validate",h.EventListeners.Core.VALIDATE_CREDENTIALS);this.removeListener("sign",h.EventListeners.Core.SIGN);return this},toGet:function e(){if(this.service.api.protocol==="query"||this.service.api.protocol==="ec2"){this.removeListener("build",this.buildAsGet);this.addListener("build",this.buildAsGet)}return this},buildAsGet:function e(t){t.httpRequest.method="GET";t.httpRequest.path=t.service.endpoint.path+"?"+t.httpRequest.body;t.httpRequest.body="";delete t.httpRequest.headers["Content-Length"];delete t.httpRequest.headers["Content-Type"]},haltHandlersOnError:function e(){this._haltHandlersOnError=true}});h.Request.addPromisesToClass=function e(t){this.prototype.promise=function e(){var i=this;this.httpRequest.appendToUserAgent("promise");return new t(function(t,r){i.on("complete",function(e){if(e.error){r(e.error)}else{t(Object.defineProperty(e.data||{},"$response",{value:e}))}});i.runTo()})}};h.Request.deletePromisesFromClass=function e(){delete this.prototype.promise};h.util.addPromises(h.Request);h.util.mixin(h.Request,h.SequentialExecutor)}).call(this,n("_process"))},{"./core":38,"./state_machine":111,_process:9,jmespath:8}],111:[function(e,t,r){function i(e,t){this.currentState=t||null;this.states=e||{}}i.prototype.runTo=function e(t,r,i,n){if(typeof t==="function"){n=i;i=r;r=t;t=null}var a=this;var o=a.states[a.currentState];o.fn.call(i||a,n,function(e){if(e){if(o.fail)a.currentState=o.fail;else return r?r.call(i,e):null}else{if(o.accept)a.currentState=o.accept;else return r?r.call(i):null}if(a.currentState===t){return r?r.call(i,e):null}a.runTo(t,r,i,e)})};i.prototype.addState=function e(t,r,i,n){if(typeof r==="function"){n=r;r=null;i=null}else if(typeof i==="function"){n=i;i=null}if(!this.currentState)this.currentState=t;this.states[t]={accept:r,fail:i,fn:n};return this};t.exports=i},{}],69:[function(e,t,r){var c=e("./core");c.ParamValidator=c.util.inherit({constructor:function e(t){if(t===true||t===undefined){t={min:true}}this.validation=t},validate:function e(t,r,i){this.errors=[];this.validateMember(t,r||{},i||"params");if(this.errors.length>1){var n=this.errors.join("\n* ");n="There were "+this.errors.length+" validation errors:\n* "+n;throw c.util.error(new Error(n),{code:"MultipleValidationErrors",errors:this.errors})}else if(this.errors.length===1){throw this.errors[0]}else{return true}},fail:function e(t,r){this.errors.push(c.util.error(new Error(r),{code:t}))},validateStructure:function e(t,r,i){this.validateType(r,i,["object"],"structure");var n;for(var a=0;t.required&&a<t.required.length;a++){n=t.required[a];var o=r[n];if(o===undefined||o===null){this.fail("MissingRequiredParameter","Missing required key '"+n+"' in "+i)}}for(n in r){if(!Object.prototype.hasOwnProperty.call(r,n))continue;var s=r[n],u=t.members[n];if(u!==undefined){var c=[i,n].join(".");this.validateMember(u,s,c)}else{this.fail("UnexpectedParameter","Unexpected key '"+n+"' found in "+i)}}return true},validateMember:function e(t,r,i){switch(t.type){case"structure":return this.validateStructure(t,r,i);case"list":return this.validateList(t,r,i);case"map":return this.validateMap(t,r,i);default:return this.validateScalar(t,r,i)}},validateList:function e(t,r,i){if(this.validateType(r,i,[Array])){this.validateRange(t,r.length,i,"list member count");for(var n=0;n<r.length;n++){this.validateMember(t.member,r[n],i+"["+n+"]")}}},validateMap:function e(t,r,i){if(this.validateType(r,i,["object"],"map")){var n=0;for(var a in r){if(!Object.prototype.hasOwnProperty.call(r,a))continue;this.validateMember(t.key,a,i+"[key='"+a+"']");this.validateMember(t.value,r[a],i+"['"+a+"']");n++}this.validateRange(t,n,i,"map member count")}},validateScalar:function e(t,r,i){switch(t.type){case null:case undefined:case"string":return this.validateString(t,r,i);case"base64":case"binary":return this.validatePayload(r,i);case"integer":case"float":return this.validateNumber(t,r,i);case"boolean":return this.validateType(r,i,["boolean"]);case"timestamp":return this.validateType(r,i,[Date,/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d+)?Z$/,"number"],"Date object, ISO-8601 string, or a UNIX timestamp");default:return this.fail("UnkownType","Unhandled type "+t.type+" for "+i)}},validateString:function e(t,r,i){var n=["string"];if(t.isJsonValue){n=n.concat(["number","object","boolean"])}if(r!==null&&this.validateType(r,i,n)){this.validateEnum(t,r,i);this.validateRange(t,r.length,i,"string length");this.validatePattern(t,r,i)}},validatePattern:function e(t,r,i){if(this.validation["pattern"]&&t["pattern"]!==undefined){if(!new RegExp(t["pattern"]).test(r)){this.fail("PatternMatchError",'Provided value "'+r+'" '+"does not match regex pattern /"+t["pattern"]+"/ for "+i)}}},validateRange:function e(t,r,i,n){if(this.validation["min"]){if(t["min"]!==undefined&&r<t["min"]){this.fail("MinRangeError","Expected "+n+" >= "+t["min"]+", but found "+r+" for "+i)}}if(this.validation["max"]){if(t["max"]!==undefined&&r>t["max"]){this.fail("MaxRangeError","Expected "+n+" <= "+t["max"]+", but found "+r+" for "+i)}}},validateEnum:function e(t,r,i){if(this.validation["enum"]&&t["enum"]!==undefined){if(t["enum"].indexOf(r)===-1){this.fail("EnumError","Found string value of "+r+", but "+"expected "+t["enum"].join("|")+" for "+i)}}},validateType:function e(t,r,i,n){if(t===null||t===undefined)return false;var a=false;for(var o=0;o<i.length;o++){if(typeof i[o]==="string"){if(typeof t===i[o])return true}else if(i[o]instanceof RegExp){if((t||"").toString().match(i[o]))return true}else{if(t instanceof i[o])return true;if(c.util.isType(t,i[o]))return true;if(!n&&!a)i=i.slice();i[o]=c.util.typeName(i[o])}a=true}var s=n;if(!s){s=i.join(", ").replace(/,([^,]+)$/,", or$1")}var u=s.match(/^[aeiou]/i)?"n":"";this.fail("InvalidParameterType","Expected "+r+" to be a"+u+" "+s);return false},validateNumber:function e(t,r,i){if(r===null||r===undefined)return;if(typeof r==="string"){var n=parseFloat(r);if(n.toString()===r)r=n}if(this.validateType(r,i,["number"])){this.validateRange(t,r,i,"numeric value")}},validatePayload:function e(t,r){if(t===null||t===undefined)return;if(typeof t==="string")return;if(t&&typeof t.byteLength==="number")return;if(c.util.isNode()){var i=c.util.stream.Stream;if(c.util.Buffer.isBuffer(t)||t instanceof i)return}var n=["Buffer","Stream","File","Blob","ArrayBuffer","DataView"];if(t){for(var a=0;a<n.length;a++){if(c.util.isType(t,n[a]))return;if(c.util.typeName(t.constructor)===n[a])return}}this.fail("InvalidParameterType","Expected "+r+" to be a "+"string, Buffer, Stream, Blob, or typed array object")}})},{"./core":38}],63:[function(e,t,r){var i=e("./collection");var n=e("./operation");var a=e("./shape");var o=e("./paginator");var s=e("./resource_waiter");var u=e("../util");var c=u.property;var l=u.memoizedProperty;function f(t,r){t=t||{};r=r||{};r.api=this;t.metadata=t.metadata||{};c(this,"isApi",true,false);c(this,"apiVersion",t.metadata.apiVersion);c(this,"endpointPrefix",t.metadata.endpointPrefix);c(this,"signingName",t.metadata.signingName);c(this,"globalEndpoint",t.metadata.globalEndpoint);c(this,"signatureVersion",t.metadata.signatureVersion);c(this,"jsonVersion",t.metadata.jsonVersion);c(this,"targetPrefix",t.metadata.targetPrefix);c(this,"protocol",t.metadata.protocol);c(this,"timestampFormat",t.metadata.timestampFormat);c(this,"xmlNamespaceUri",t.metadata.xmlNamespace);c(this,"abbreviation",t.metadata.serviceAbbreviation);c(this,"fullName",t.metadata.serviceFullName);l(this,"className",function(){var e=t.metadata.serviceAbbreviation||t.metadata.serviceFullName;if(!e)return null;e=e.replace(/^Amazon|OOS\s*|\(.*|\s+|\W+/g,"");if(e==="ElasticLoadBalancing")e="ELB";return e});c(this,"operations",new i(t.operations,r,function(e,t){return new n(e,t,r)},u.string.lowerFirst));c(this,"shapes",new i(t.shapes,r,function(e,t){return a.create(t,r)}));c(this,"paginators",new i(t.paginators,r,function(e,t){return new o(e,t,r)}));c(this,"waiters",new i(t.waiters,r,function(e,t){return new s(e,t,r)},u.string.lowerFirst));if(r.documentation){c(this,"documentation",t.documentation);c(this,"documentationUrl",t.documentationUrl)}}t.exports=f},{"../util":112,"./collection":64,"./operation":65,"./paginator":66,"./resource_waiter":67,"./shape":68}],67:[function(e,t,r){var a=e("../util");var o=a.property;function i(e,r,t){t=t||{};o(this,"name",e);o(this,"api",t.api,false);if(r.operation){o(this,"operation",a.string.lowerFirst(r.operation))}var i=this;var n=["type","description","delay","maxAttempts","acceptors"];n.forEach(function(e){var t=r[e];if(t){o(i,e,t)}})}t.exports=i},{"../util":112}],66:[function(e,t,r){var i=e("../util").property;function n(e,t){i(this,"inputToken",t.input_token);i(this,"limitKey",t.limit_key);i(this,"moreResults",t.more_results);i(this,"outputToken",t.output_token);i(this,"resultKey",t.result_key)}t.exports=n},{"../util":112}],65:[function(e,t,r){var a=e("./shape");var i=e("../util");var o=i.property;var s=i.memoizedProperty;function n(e,r,i){var n=this;i=i||{};o(this,"name",r.name||e);o(this,"api",i.api,false);r.http=r.http||{};o(this,"httpMethod",r.http.method||"POST");o(this,"httpPath",r.http.requestUri||"/");o(this,"authtype",r.authtype||"");s(this,"input",function(){if(!r.input){return new a.create({type:"structure"},i)}return a.create(r.input,i)});s(this,"output",function(){if(!r.output){return new a.create({type:"structure"},i)}return a.create(r.output,i)});s(this,"errors",function(){var e=[];if(!r.errors)return null;for(var t=0;t<r.errors.length;t++){e.push(a.create(r.errors[t],i))}return e});s(this,"paginator",function(){return i.api.paginators[e]});if(i.documentation){o(this,"documentation",r.documentation);o(this,"documentationUrl",r.documentationUrl)}s(this,"idempotentMembers",function(){var e=[];var t=n.input;var r=t.members;if(!t.members){return e}for(var i in r){if(!r.hasOwnProperty(i)){continue}if(r[i].isIdempotent===true){e.push(i)}}return e});s(this,"hasEventOutput",function(){var e=n.output;return u(e)})}function u(e){var t=e.members;var r=e.payload;if(!e.members){return false}if(r){var i=t[r];return i.isEventStream}for(var n in t){if(!t.hasOwnProperty(n)){if(t[n].isEventStream===true){return true}}}return false}t.exports=n},{"../util":112,"./shape":68}],59:[function(e,t,r){var n=e("./core");var i=n.util.inherit;n.Endpoint=i({constructor:function e(t,r){n.util.hideProperties(this,["slashes","auth","hash","search","query"]);if(typeof t==="undefined"||t===null){throw new Error("Invalid endpoint: "+t)}else if(typeof t!=="string"){return n.util.copy(t)}if(!t.match(/^http/)){var i=r&&r.sslEnabled!==undefined?r.sslEnabled:n.config.sslEnabled;t=(i?"https":"http")+"://"+t}n.util.update(this,n.util.urlParse(t));if(this.port){this.port=parseInt(this.port,10)}else{this.port=this.protocol==="https:"?443:80}}});n.HttpRequest=i({constructor:function e(t,r){t=new n.Endpoint(t);this.method="POST";this.path=t.path||"/";this.headers={};this.body="";this.endpoint=t;this.region=r;this._userAgent="";this.setUserAgent()},setUserAgent:function e(){this._userAgent=this.headers[this.getUserAgentHeaderName()]=n.util.userAgent()},getUserAgentHeaderName:function e(){var t=n.util.isBrowser()?"X-Amz-":"";return t+"User-Agent"},appendToUserAgent:function e(t){if(typeof t==="string"&&t){this._userAgent+=" "+t}this.headers[this.getUserAgentHeaderName()]=this._userAgent},getUserAgent:function e(){return this._userAgent},pathname:function e(){return this.path.split("?",1)[0]},search:function e(){var t=this.path.split("?",2)[1];if(t){t=n.util.queryStringParse(t);return n.util.queryParamsToString(t)}return""}});n.HttpResponse=i({constructor:function e(){this.statusCode=undefined;this.headers={};this.body=undefined;this.streaming=false;this.stream=null},createUnbufferedStream:function e(){this.streaming=true;return this.stream}});n.HttpClient=i({});n.HttpClient.getInstance=function e(){if(this.singleton===undefined){this.singleton=new this}return this.singleton}},{"./core":38}],58:[function(p,e,t){var h=p("./core");var r=p("./sequential_executor");h.EventListeners={Core:{}};function n(e){if(!e.service.api.operations){return""}var t=e.service.api.operations[e.operation];return t?t.authtype:""}h.EventListeners={Core:(new r).addNamedListeners(function(e,t){t("VALIDATE_CREDENTIALS","validate",function e(t,r){if(!t.service.api.signatureVersion)return r();t.service.config.getCredentials(function(e){if(e){t.response.error=h.util.error(e,{code:"CredentialsError",message:"Missing credentials in config"})}r()})});e("VALIDATE_REGION","validate",function e(t){if(!t.service.config.region&&!t.service.isGlobalEndpoint){t.response.error=h.util.error(new Error,{code:"ConfigError",message:"Missing region in config"})}});e("BUILD_IDEMPOTENCY_TOKENS","validate",function e(t){if(!t.service.api.operations){return}var r=t.service.api.operations[t.operation];if(!r){return}var i=r.idempotentMembers;if(!i.length){return}var n=h.util.copy(t.params);for(var a=0,o=i.length;a<o;a++){if(!n[i[a]]){n[i[a]]=h.util.uuid.v4()}}t.params=n});e("VALIDATE_PARAMETERS","validate",function e(t){if(!t.service.api.operations){return}var r=t.service.api.operations[t.operation].input;var i=t.service.config.paramValidation;new h.ParamValidator(i).validate(r,t.params)});t("COMPUTE_SHA256","afterBuild",function e(r,i){r.haltHandlersOnError();if(!r.service.api.operations){return}var t=r.service.api.operations[r.operation];var n=t?t.authtype:"";if(!r.service.api.signatureVersion&&!n)return i();if(r.service.getSignerClass(r)===h.Signers.V4){var a=r.httpRequest.body||"";if(n.indexOf("unsigned-body")>=0){r.httpRequest.headers["X-Amz-Content-Sha256"]="UNSIGNED-PAYLOAD";return i()}h.util.computeSha256(a,function(e,t){if(e){i(e)}else{r.httpRequest.headers["X-Amz-Content-Sha256"]=t;i()}})}else{i()}});e("SET_CONTENT_LENGTH","afterBuild",function e(t){var r=n(t);if(t.httpRequest.headers["Content-Length"]===undefined){try{var i=h.util.string.byteLength(t.httpRequest.body);t.httpRequest.headers["Content-Length"]=i}catch(e){if(r.indexOf("unsigned-body")===-1){throw e}else{return}}}});e("SET_HTTP_HOST","afterBuild",function e(t){t.httpRequest.headers["Host"]=t.httpRequest.endpoint.host});e("RESTART","restart",function e(){var t=this.response.error;if(!t||!t.retryable)return;this.httpRequest=new h.HttpRequest(this.service.endpoint,this.service.region);if(this.response.retryCount<this.service.config.maxRetries){this.response.retryCount++}else{this.response.error=null}});t("SIGN","sign",function e(a,o){var s=a.service;var t=a.service.api.operations||{};var u=t[a.operation];var r=u?u.authtype:"";if(!s.api.signatureVersion&&!r)return o();s.config.getCredentials(function(e,t){if(e){a.response.error=e;return o()}try{var r=s.getSkewCorrectedDate();var i=s.getSignerClass(a);var n=new i(a.httpRequest,s.api.signingName||s.api.endpointPrefix,{signatureCache:s.config.signatureCache,operation:u});n.setServiceClientId(s._clientId);delete a.httpRequest.headers["Authorization"];delete a.httpRequest.headers["Date"];delete a.httpRequest.headers["X-Amz-Date"];n.addAuthorization(t,r);a.signedAt=r}catch(e){a.response.error=e}o()})});e("VALIDATE_RESPONSE","validateResponse",function e(t){if(this.service.successfulResponse(t,this)){t.data={};t.error=null}else{t.data=null;t.error=h.util.error(new Error,{code:"UnknownError",message:"An unknown error occurred."})}});t("SEND","send",function e(s,u){s.httpResponse._abortCallback=u;s.error=null;s.data=null;function i(n){s.httpResponse.stream=n;var t=s.request.httpRequest.stream;var a=s.request.service;var e=a.api;var r=s.request.operation;var o=e.operations[r]||{};n.on("headers",function e(t,r,i){s.request.emit("httpHeaders",[t,r,s,i]);if(!s.httpResponse.streaming){if(h.HttpClient.streamsApiVersion===2){if(o.hasEventOutput&&a.successfulResponse(s)){s.request.emit("httpDone");u();return}n.on("readable",function e(){var t=n.read();if(t!==null){s.request.emit("httpData",[t,s])}})}else{n.on("data",function e(t){s.request.emit("httpData",[t,s])})}}});n.on("end",function e(){if(!t||!t.didCallback){if(h.HttpClient.streamsApiVersion===2&&(o.hasEventOutput&&a.successfulResponse(s))){return}s.request.emit("httpDone");u()}})}function n(e){e.on("sendProgress",function e(t){s.request.emit("httpUploadProgress",[t,s])});e.on("receiveProgress",function e(t){s.request.emit("httpDownloadProgress",[t,s])})}function a(e){if(e.code!=="RequestAbortedError"){var t=e.code==="TimeoutError"?e.code:"NetworkingError";e=h.util.error(e,{code:t,region:s.request.httpRequest.region,hostname:s.request.httpRequest.endpoint.hostname,retryable:true})}s.error=e;s.request.emit("httpError",[s.error,s],function(){u()})}function t(){var e=h.HttpClient.getInstance();var t=s.request.service.config.httpOptions||{};try{var r=e.handleRequest(s.request.httpRequest,t,i,a);n(r)}catch(e){a(e)}}var r=(s.request.service.getSkewCorrectedDate()-this.signedAt)/1e3;if(r>=60*10){this.emit("sign",[this],function(e){if(e)u(e);else t()})}else{t()}});e("HTTP_HEADERS","httpHeaders",function e(t,r,i,n){i.httpResponse.statusCode=t;i.httpResponse.statusMessage=n;i.httpResponse.headers=r;i.httpResponse.body=new h.util.Buffer("");i.httpResponse.buffers=[];i.httpResponse.numBytes=0;var a=r.date||r.Date;var o=i.request.service;if(a){var s=Date.parse(a);if(o.config.correctClockSkew&&o.isClockSkewed(s)){o.applyClockOffset(s)}}});e("HTTP_DATA","httpData",function e(t,r){if(t){if(h.util.isNode()){r.httpResponse.numBytes+=t.length;var i=r.httpResponse.headers["content-length"];var n={loaded:r.httpResponse.numBytes,total:i};r.request.emit("httpDownloadProgress",[n,r])}r.httpResponse.buffers.push(new h.util.Buffer(t))}});e("HTTP_DONE","httpDone",function e(t){if(t.httpResponse.buffers&&t.httpResponse.buffers.length>0){var r=h.util.buffer.concat(t.httpResponse.buffers);t.httpResponse.body=r}delete t.httpResponse.numBytes;delete t.httpResponse.buffers});e("FINALIZE_ERROR","retry",function e(t){if(t.httpResponse.statusCode){t.error.statusCode=t.httpResponse.statusCode;if(t.error.retryable===undefined){t.error.retryable=this.service.retryableError(t.error,this)}}});e("INVALIDATE_CREDENTIALS","retry",function e(t){if(!t.error)return;switch(t.error.code){case"RequestExpired":case"ExpiredTokenException":case"ExpiredToken":t.error.retryable=true;t.request.service.config.credentials.expired=true}});e("EXPIRED_SIGNATURE","retry",function e(t){var r=t.error;if(!r)return;if(typeof r.code==="string"&&typeof r.message==="string"){if(r.code.match(/Signature/)&&r.message.match(/expired/)){t.error.retryable=true}}});e("CLOCK_SKEWED","retry",function e(t){if(!t.error)return;if(this.service.clockSkewError(t.error)&&this.service.config.correctClockSkew){t.error.retryable=true}});e("REDIRECT","retry",function e(t){if(t.error&&t.error.statusCode>=300&&t.error.statusCode<400&&t.httpResponse.headers["location"]){this.httpRequest.endpoint=new h.Endpoint(t.httpResponse.headers["location"]);this.httpRequest.headers["Host"]=this.httpRequest.endpoint.host;t.error.redirect=true;t.error.retryable=true}});e("RETRY_CHECK","retry",function e(t){if(t.error){if(t.error.redirect&&t.redirectCount<t.maxRedirects){t.error.retryDelay=0}else if(t.retryCount<t.maxRetries){t.error.retryDelay=this.service.retryDelays(t.retryCount)||0}}});t("RESET_RETRY_STATE","afterRetry",function e(t,r){var i,n=false;if(t.error){i=t.error.retryDelay||0;if(t.error.retryable&&t.retryCount<t.maxRetries){t.retryCount++;n=true}else if(t.error.redirect&&t.redirectCount<t.maxRedirects){t.redirectCount++;n=true}}if(n){t.error=null;setTimeout(r,i)}else{r()}})}),CorePost:(new r).addNamedListeners(function(e){e("EXTRACT_REQUEST_ID","extractData",h.util.extractRequestId);e("EXTRACT_REQUEST_ID","extractError",h.util.extractRequestId);e("ENOTFOUND_ERROR","httpError",function e(t){if(t.code==="NetworkingError"&&t.errno==="ENOTFOUND"){var r="Inaccessible host: `"+t.hostname+"'. This service may not be available in the `"+t.region+"' region.";this.response.error=h.util.error(new Error(r),{code:"UnknownEndpoint",region:t.region,hostname:t.hostname,retryable:true,originalError:t})}})}),Logger:(new r).addNamedListeners(function(e){e("LOG_REQUEST","complete",function e(u){var c=u.request;var l=c.service.config.logger;if(!l)return;function f(r,e){if(!e){return e}switch(r.type){case"structure":var i={};h.util.each(e,function(e,t){if(Object.prototype.hasOwnProperty.call(r.members,e)){i[e]=f(r.members[e],t)}else{i[e]=t}});return i;case"list":var n=[];h.util.arrayEach(e,function(e,t){n.push(f(r.member,e))});return n;case"map":var a={};h.util.each(e,function(e,t){a[e]=f(r.value,t)});return a;default:if(r.isSensitive){return"***SensitiveInformation***"}else{return e}}}function t(){var e=u.request.service.getSkewCorrectedDate().getTime();var t=(e-c.startTime.getTime())/1e3;var r=l.isTTY?true:false;var i=u.httpResponse.statusCode;var n=c.params;if(c.service.api.operations&&c.service.api.operations[c.operation]&&c.service.api.operations[c.operation].input){var a=c.service.api.operations[c.operation].input;n=f(a,c.params)}var o=p("util").inspect(n,true,null);var s="";if(r)s+="[33m";s+="[OOS "+c.service.serviceIdentifier+" "+i;s+=" "+t.toString()+"s "+u.retryCount+" retries]";if(r)s+="[0;1m";s+=" "+h.util.string.lowerFirst(c.operation);s+="("+o+")";if(r)s+="[0m";return s}var r=t();if(typeof l.log==="function"){l.log(r)}else if(typeof l.write==="function"){l.write(r+"\n")}})}),Json:(new r).addNamedListeners(function(e){var t=p("./protocol/json");e("BUILD","build",t.buildRequest);e("EXTRACT_DATA","extractData",t.extractData);e("EXTRACT_ERROR","extractError",t.extractError)}),Rest:(new r).addNamedListeners(function(e){var t=p("./protocol/rest");e("BUILD","build",t.buildRequest);e("EXTRACT_DATA","extractData",t.extractData);e("EXTRACT_ERROR","extractError",t.extractError)}),RestJson:(new r).addNamedListeners(function(e){var t=p("./protocol/rest_json");e("BUILD","build",t.buildRequest);e("EXTRACT_DATA","extractData",t.extractData);e("EXTRACT_ERROR","extractError",t.extractError)}),RestXml:(new r).addNamedListeners(function(e){var t=p("./protocol/rest_xml");e("BUILD","build",t.buildRequest);e("EXTRACT_DATA","extractData",t.extractData);e("EXTRACT_ERROR","extractError",t.extractError)}),Query:(new r).addNamedListeners(function(e){var t=p("./protocol/query");e("BUILD","build",t.buildRequest);e("EXTRACT_DATA","extractData",t.extractData);e("EXTRACT_ERROR","extractError",t.extractError)})}},{"./core":38,"./protocol/json":71,"./protocol/query":72,"./protocol/rest":73,"./protocol/rest_json":74,"./protocol/rest_xml":75,"./sequential_executor":84,util:20}],84:[function(e,t,r){var c=e("./core");c.SequentialExecutor=c.util.inherit({constructor:function e(){this._events={}},listeners:function e(t){return this._events[t]?this._events[t].slice(0):[]},on:function e(t,r){if(this._events[t]){this._events[t].push(r)}else{this._events[t]=[r]}return this},onAsync:function e(t,r){r._isAsync=true;return this.on(t,r)},removeListener:function e(t,r){var i=this._events[t];if(i){var n=i.length;var a=-1;for(var o=0;o<n;++o){if(i[o]===r){a=o}}if(a>-1){i.splice(a,1)}}return this},removeAllListeners:function e(t){if(t){delete this._events[t]}else{this._events={}}return this},emit:function e(t,r,i){if(!i)i=function(){};var n=this.listeners(t);var a=n.length;this.callListeners(n,r,i);return a>0},callListeners:function e(t,r,i,n){var a=this;var o=n||null;function s(e){if(e){o=c.util.error(o||new Error,e);if(a._haltHandlersOnError){return i.call(a,o)}}a.callListeners(t,r,i,o)}while(t.length>0){var u=t.shift();if(u._isAsync){u.apply(a,r.concat([s]));return}else{try{u.apply(a,r)}catch(e){o=c.util.error(o||new Error,e)}if(o&&a._haltHandlersOnError){i.call(a,o);return}}}i.call(a,o)},addListeners:function e(t){var r=this;if(t._events)t=t._events;c.util.each(t,function(t,e){if(typeof e==="function")e=[e];c.util.arrayEach(e,function(e){r.on(t,e)})});return r},addNamedListener:function e(t,r,i){this[t]=i;this.addListener(r,i);return this},addNamedAsyncListener:function e(t,r,i){i._isAsync=true;return this.addNamedListener(t,r,i)},addNamedListeners:function e(t){var r=this;t(function(){r.addNamedListener.apply(r,arguments)},function(){r.addNamedAsyncListener.apply(r,arguments)});return this}});c.SequentialExecutor.prototype.addListener=c.SequentialExecutor.prototype.on;t.exports=c.SequentialExecutor},{"./core":38}],75:[function(e,t,r){var l=e("../core");var f=e("../util");var p=e("./rest");function i(e){var t=e.service.api.operations[e.operation].input;var r=new l.XML.Builder;var i=e.params;var n=t.payload;if(n){var a=t.members[n];i=i[n];if(i===undefined)return;if(a.type==="structure"){var o=a.name;e.httpRequest.body=r.toXML(i,a,o,true)}else{e.httpRequest.body=i}}else{e.httpRequest.body=r.toXML(i,t,t.name||t.shape||f.string.upperFirst(e.operation)+"Request")}}function n(e){p.buildRequest(e);if(["GET","HEAD"].indexOf(e.httpRequest.method)<0){i(e)}}function a(t){p.extractError(t);var r;try{r=(new l.XML.Parser).parse(t.httpResponse.body.toString())}catch(e){r={Code:t.httpResponse.statusCode,Message:t.httpResponse.statusMessage}}if(r.Errors)r=r.Errors;if(r.Error)r=r.Error;if(r.Code){t.error=f.error(new Error,{code:r.Code,message:r.Message})}else{t.error=f.error(new Error,{code:t.httpResponse.statusCode,message:null})}}function o(e){p.extractData(e);var t;var r=e.request;var i=e.httpResponse.body;var n=r.service.api.operations[r.operation];var a=n.output;var o=n.hasEventOutput;var s=a.payload;if(s){var u=a.members[s];if(u.isEventStream){t=new l.XML.Parser;e.data[s]=f.createEventStream(l.HttpClient.streamsApiVersion===2?e.httpResponse.stream:e.httpResponse.body,t,u)}else if(u.type==="structure"){t=new l.XML.Parser;e.data[s]=t.parse(i.toString(),u)}else if(u.type==="binary"||u.isStreaming){e.data[s]=i}else{e.data[s]=u.toType(i)}}else if(i.length>0){t=new l.XML.Parser;var c=t.parse(i.toString(),a);f.update(e.data,c)}}t.exports={buildRequest:n,extractError:a,extractData:o}},{"../core":38,"../util":112,"./rest":73}],74:[function(e,t,r){var c=e("../util");var l=e("./rest");var f=e("./json");var a=e("../json/builder");var p=e("../json/parser");function i(e){var t=new a;var r=e.service.api.operations[e.operation].input;if(r.payload){var i={};var n=r.members[r.payload];i=e.params[r.payload];if(i===undefined)return;if(n.type==="structure"){e.httpRequest.body=t.build(i,n);o(e)}else{e.httpRequest.body=i;if(n.type==="binary"||n.isStreaming){o(e,true)}}}else{e.httpRequest.body=t.build(e.params,r);o(e)}}function o(e,t){var r=e.service.api.operations[e.operation];var i=r.input;if(!e.httpRequest.headers["Content-Type"]){var n=t?"binary/octet-stream":"application/json";e.httpRequest.headers["Content-Type"]=n}}function n(e){l.buildRequest(e);if(["GET","HEAD","DELETE"].indexOf(e.httpRequest.method)<0){i(e)}}function s(e){f.extractError(e)}function u(e){l.extractData(e);var t=e.request;var r=t.service.api.operations[t.operation];var i=t.service.api.operations[t.operation].output||{};var n;var a=r.hasEventOutput;if(i.payload){var o=i.members[i.payload];var s=e.httpResponse.body;if(o.isEventStream){n=new p;e.data[payload]=c.createEventStream(OOS.HttpClient.streamsApiVersion===2?e.httpResponse.stream:s,n,o)}else if(o.type==="structure"||o.type==="list"){var n=new p;e.data[i.payload]=n.parse(s,o)}else if(o.type==="binary"||o.isStreaming){e.data[i.payload]=s}else{e.data[i.payload]=o.toType(s)}}else{var u=e.data;f.extractData(e);e.data=c.merge(u,e.data)}}t.exports={buildRequest:n,extractError:s,extractData:u}},{"../json/builder":61,"../json/parser":62,"../util":112,"./json":71,"./rest":73}],73:[function(e,t,r){var c=e("../util");function i(e){e.httpRequest.method=e.service.api.operations[e.operation].httpMethod}function n(e,t,r,n){var a=[e,t].join("/");a=a.replace(/\/+/g,"/");var o={},s=false;c.each(r.members,function(e,t){var i=n[e];if(i===null||i===undefined)return;if(t.location==="uri"){var r=new RegExp("\\{"+t.name+"(\\+)?\\}");a=a.replace(r,function(e,t){var r=t?c.uriEscapePath:c.uriEscape;return r(String(i))})}else if(t.location==="querystring"){s=true;if(t.type==="list"){o[t.name]=i.map(function(e){return c.uriEscape(t.member.toWireFormat(e).toString())})}else if(t.type==="map"){c.each(i,function(e,t){if(Array.isArray(t)){o[e]=t.map(function(e){return c.uriEscape(String(e))})}else{o[e]=c.uriEscape(String(t))}})}else{o[t.name]=c.uriEscape(t.toWireFormat(i).toString())}}});if(s){a+=a.indexOf("?")>=0?"&":"?";var i=[];c.arrayEach(Object.keys(o).sort(),function(e){if(!Array.isArray(o[e])){o[e]=[o[e]]}for(var t=0;t<o[e].length;t++){i.push(c.uriEscape(String(e))+"="+o[e][t])}});a+=i.join("&")}return a}function a(e){var t=e.service.api.operations[e.operation];var r=t.input;var i=n(e.httpRequest.endpoint.path,t.httpPath,r,e.params);e.httpRequest.path=i}function o(i){var e=i.service.api.operations[i.operation];c.each(e.input.members,function(e,r){var t=i.params[e];if(t===null||t===undefined)return;if(r.location==="headers"&&r.type==="map"){c.each(t,function(e,t){i.httpRequest.headers[r.name+e]=t})}else if(r.location==="header"){t=r.toWireFormat(t).toString();if(r.isJsonValue){t=c.base64.encode(t)}i.httpRequest.headers[r.name]=t}})}function s(e){i(e);a(e);o(e)}function u(){}function l(e){var t=e.request;var o={};var s=e.httpResponse;var r=t.service.api.operations[t.operation];var i=r.output;var u={};c.each(s.headers,function(e,t){u[e.toLowerCase()]=t});c.each(i.members,function(i,e){var t=(e.name||i).toLowerCase();if(e.location==="headers"&&e.type==="map"){o[i]={};var r=e.isLocationName?e.name:"";var n=new RegExp("^"+r+"(.+)","i");c.each(s.headers,function(e,t){var r=e.match(n);if(r!==null){o[i][r[1]]=t}})}else if(e.location==="header"){if(u[t]!==undefined){var a=e.isJsonValue?c.base64.decode(u[t]):u[t];o[i]=e.toType(a)}}else if(e.location==="statusCode"){o[i]=parseInt(s.statusCode,10)}});e.data=o}t.exports={buildRequest:s,extractError:u,extractData:l,generateURI:n}},{"../util":112}],72:[function(e,t,r){var c=e("../core");var l=e("../util");var n=e("../query/query_param_serializer");var f=e("../model/shape");function i(e){var t=e.service.api.operations[e.operation];var r=e.httpRequest;r.headers["Content-Type"]="application/x-www-form-urlencoded; charset=utf-8";r.params={Version:e.service.api.apiVersion,Action:t.name};var i=new n;i.serialize(e.params,t.input,function(e,t){r.params[e]=t});r.body=l.queryParamsToString(r.params)}function a(t){var r,e=t.httpResponse.body.toString();if(e.match("<UnknownOperationException")){r={Code:"UnknownOperation",Message:"Unknown operation "+t.request.operation}}else{try{r=(new c.XML.Parser).parse(e)}catch(e){r={Code:t.httpResponse.statusCode,Message:t.httpResponse.statusMessage}}}if(r.requestId&&!t.requestId)t.requestId=r.requestId;if(r.Errors)r=r.Errors;if(r.Error)r=r.Error;if(r.Code){t.error=l.error(new Error,{code:r.Code,message:r.Message})}else{t.error=l.error(new Error,{code:t.httpResponse.statusCode,message:null})}}function o(e){var t=e.request;var r=t.service.api.operations[t.operation];var i=r.output||{};var n=i;if(n.resultWrapper){var a=f.create({type:"structure"});a.members[n.resultWrapper]=i;a.memberNames=[n.resultWrapper];l.property(i,"name",i.resultWrapper);i=a}var o=new c.XML.Parser;if(i&&i.members&&!i.members._XAMZRequestId){var s=f.create({type:"string"},{api:{protocol:"query"}},"requestId");i.members._XAMZRequestId=s}var u=o.parse(e.httpResponse.body.toString(),i);e.requestId=u._XAMZRequestId||u.requestId;if(u._XAMZRequestId)delete u._XAMZRequestId;if(n.resultWrapper){if(u[n.resultWrapper]){l.update(u,u[n.resultWrapper]);delete u[n.resultWrapper]}}e.data=u}t.exports={buildRequest:i,extractError:a,extractData:o}},{"../core":38,"../model/shape":68,"../query/query_param_serializer":76,"../util":112}],76:[function(e,t,r){var i=e("../util");function n(){}n.prototype.serialize=function(e,t,r){a("",e,t,r)};function u(e){if(e.isQueryName||e.api.protocol!=="ec2"){return e.name}else{return e.name[0].toUpperCase()+e.name.substr(1)}}function a(n,a,e,o){i.each(e.members,function(e,t){var r=a[e];if(r===null||r===undefined)return;var i=u(t);i=n?n+"."+i:i;l(i,r,t,o)})}function o(o,e,s,u){var c=1;i.each(e,function(e,t){var r=s.flattened?".":".entry.";var i=r+c+++".";var n=i+(s.key.name||"key");var a=i+(s.value.name||"value");l(o+n,e,s.key,u);l(o+a,t,s.value,u)})}function s(n,e,a,o){var s=a.member||{};if(e.length===0){o.call(this,n,null);return}i.arrayEach(e,function(e,t){var r="."+(t+1);if(a.api.protocol==="ec2"){r=r+""}else if(a.flattened){if(s.name){var i=n.split(".");i.pop();i.push(u(s));n=i.join(".")}}else{r="."+(s.name?s.name:"member")+r}l(n+r,e,s,o)})}function l(e,t,r,i){if(t===null||t===undefined)return;if(r.type==="structure"){a(e,t,r,i)}else if(r.type==="list"){s(e,t,r,i)}else if(r.type==="map"){o(e,t,r,i)}else{i(e,r.toWireFormat(t).toString())}}t.exports=n},{"../util":112}],68:[function(e,t,r){var o=e("./collection");var i=e("../util");function s(e,t,r){if(r!==null&&r!==undefined){i.property.apply(this,arguments)}}function u(e,t){if(!e.constructor.prototype[t]){i.memoizedProperty.apply(this,arguments)}}function c(e,t,r){t=t||{};s(this,"shape",e.shape);s(this,"api",t.api,false);s(this,"type",e.type);s(this,"enum",e.enum);s(this,"min",e.min);s(this,"max",e.max);s(this,"pattern",e.pattern);s(this,"location",e.location||this.location||"body");s(this,"name",this.name||e.xmlName||e.queryName||e.locationName||r);s(this,"isStreaming",e.streaming||this.isStreaming||false);s(this,"isComposite",e.isComposite||false);s(this,"isShape",true,false);s(this,"isQueryName",Boolean(e.queryName),false);s(this,"isLocationName",Boolean(e.locationName),false);s(this,"isIdempotent",e.idempotencyToken===true);s(this,"isJsonValue",e.jsonvalue===true);s(this,"isSensitive",e.sensitive===true||e.prototype&&e.prototype.sensitive===true);s(this,"isEventStream",Boolean(e.eventstream),false);s(this,"isEvent",Boolean(e.event),false);s(this,"isEventPayload",Boolean(e.eventpayload),false);s(this,"isEventHeader",Boolean(e.eventheader),false);s(this,"isTimestampFormatSet",Boolean(e.timestampFormat)||e.prototype&&e.prototype.isTimestampFormatSet===true,false);if(t.documentation){s(this,"documentation",e.documentation);s(this,"documentationUrl",e.documentationUrl)}if(e.xmlAttribute){s(this,"isXmlAttribute",e.xmlAttribute||false)}s(this,"defaultValue",null);this.toWireFormat=function(e){if(e===null||e===undefined)return"";return e};this.toType=function(e){return e}}c.normalizedTypes={character:"string",double:"float",long:"integer",short:"integer",biginteger:"integer",bigdecimal:"float",blob:"binary"};c.types={structure:n,list:a,map:f,boolean:g,timestamp:p,float:d,integer:m,string:h,base64:v,binary:y};c.resolve=function e(t,r){if(t.shape){var i=r.api.shapes[t.shape];if(!i){throw new Error("Cannot find shape reference: "+t.shape)}return i}else{return null}};c.create=function e(t,r,i){if(t.isShape)return t;var n=c.resolve(t,r);if(n){var a=Object.keys(t);if(!r.documentation){a=a.filter(function(e){return!e.match(/documentation/)})}var o=function(){n.constructor.call(this,t,r,i)};o.prototype=n;return new o}else{if(!t.type){if(t.members)t.type="structure";else if(t.member)t.type="list";else if(t.key)t.type="map";else t.type="string"}var s=t.type;if(c.normalizedTypes[t.type]){t.type=c.normalizedTypes[t.type]}if(c.types[t.type]){return new c.types[t.type](t,r,i)}else{throw new Error("Unrecognized shape type: "+s)}}};function l(e){c.apply(this,arguments);s(this,"isComposite",true);if(e.flattened){s(this,"flattened",e.flattened||false)}}function n(r,i){var a=this;var n=null,e=!this.isShape;l.apply(this,arguments);if(e){s(this,"defaultValue",function(){return{}});s(this,"members",{});s(this,"memberNames",[]);s(this,"required",[]);s(this,"isRequired",function(){return false})}if(r.members){s(this,"members",new o(r.members,i,function(e,t){return c.create(t,i,e)}));u(this,"memberNames",function(){return r.xmlOrder||Object.keys(r.members)});if(r.event){u(this,"eventPayloadMemberName",function(){var e=a.members;var t=a.memberNames;for(var r=0,i=t.length;r<i;r++){if(e[t[r]].isEventPayload){return t[r]}}});u(this,"eventHeaderMemberNames",function(){var e=a.members;var t=a.memberNames;var r=[];for(var i=0,n=t.length;i<n;i++){if(e[t[i]].isEventHeader){r.push(t[i])}}return r})}}if(r.required){s(this,"required",r.required);s(this,"isRequired",function(e){if(!n){n={};for(var t=0;t<r.required.length;t++){n[r.required[t]]=true}}return n[e]},false,true)}s(this,"resultWrapper",r.resultWrapper||null);if(r.payload){s(this,"payload",r.payload)}if(typeof r.xmlNamespace==="string"){s(this,"xmlNamespaceUri",r.xmlNamespace)}else if(typeof r.xmlNamespace==="object"){s(this,"xmlNamespacePrefix",r.xmlNamespace.prefix);s(this,"xmlNamespaceUri",r.xmlNamespace.uri)}}function a(e,t){var r=this,i=!this.isShape;l.apply(this,arguments);if(i){s(this,"defaultValue",function(){return[]})}if(e.member){u(this,"member",function(){return c.create(e.member,t)})}if(this.flattened){var n=this.name;u(this,"name",function(){return r.member.name||n})}}function f(e,t){var r=!this.isShape;l.apply(this,arguments);if(r){s(this,"defaultValue",function(){return{}});s(this,"key",c.create({type:"string"},t));s(this,"value",c.create({type:"string"},t))}if(e.key){u(this,"key",function(){return c.create(e.key,t)})}if(e.value){u(this,"value",function(){return c.create(e.value,t)})}}function p(e){var t=this;c.apply(this,arguments);if(e.timestampFormat){s(this,"timestampFormat",e.timestampFormat)}else if(t.isTimestampFormatSet&&this.timestampFormat){s(this,"timestampFormat",this.timestampFormat)}else if(this.location==="header"){s(this,"timestampFormat","rfc822")}else if(this.location==="querystring"){s(this,"timestampFormat","iso8601")}else if(this.api){switch(this.api.protocol){case"json":case"rest-json":s(this,"timestampFormat","unixTimestamp");break;case"rest-xml":case"query":case"ec2":s(this,"timestampFormat","iso8601");break}}this.toType=function(e){if(e===null||e===undefined)return null;if(typeof e.toUTCString==="function")return e;return typeof e==="string"||typeof e==="number"?i.date.parseTimestamp(e):null};this.toWireFormat=function(e){return i.date.format(e,t.timestampFormat)}}function h(){c.apply(this,arguments);var t=["rest-xml","query","ec2"];this.toType=function(e){e=this.api&&t.indexOf(this.api.protocol)>-1?e||"":e;if(this.isJsonValue){return JSON.parse(e)}return e&&typeof e.toString==="function"?e.toString():e};this.toWireFormat=function(e){return this.isJsonValue?JSON.stringify(e):e}}function d(){c.apply(this,arguments);this.toType=function(e){if(e===null||e===undefined)return null;return parseFloat(e)};this.toWireFormat=this.toType}function m(){c.apply(this,arguments);this.toType=function(e){if(e===null||e===undefined)return null;return parseInt(e,10)};this.toWireFormat=this.toType}function y(){c.apply(this,arguments);this.toType=i.base64.decode;this.toWireFormat=i.base64.encode}function v(){y.apply(this,arguments)}function g(){c.apply(this,arguments);this.toType=function(e){if(typeof e==="boolean")return e;if(e===null||e===undefined)return null;return e==="true"}}c.shapes={StructureShape:n,ListShape:a,MapShape:f,StringShape:h,BooleanShape:g,Base64Shape:v};t.exports=c},{"../util":112,"./collection":64}],64:[function(e,t,r){var n=e("../util").memoizedProperty;function o(e,t,r,i){n(this,i(e),function(){return r(e,t)})}function i(e,t,r,i){i=i||String;var n=this;for(var a in e){if(Object.prototype.hasOwnProperty.call(e,a)){o.call(n,a,e[a],r,i)}}}t.exports=i},{"../util":112}],71:[function(e,t,r){var n=e("../util");var s=e("../json/builder");var a=e("../json/parser");function i(e){var t=e.httpRequest;var r=e.service.api;var i=r.targetPrefix+"."+r.operations[e.operation].name;var n=r.jsonVersion||"1.0";var a=r.operations[e.operation].input;var o=new s;if(n===1)n="1.0";t.body=o.build(e.params||{},a);t.headers["Content-Type"]="application/x-amz-json-"+n;t.headers["X-Amz-Target"]=i}function o(e){var t={};var r=e.httpResponse;t.code=r.headers["x-amzn-errortype"]||"UnknownError";if(typeof t.code==="string"){t.code=t.code.split(":")[0]}if(r.body.length>0){try{var i=JSON.parse(r.body.toString());if(i.__type||i.code){t.code=(i.__type||i.code).split("#").pop()}if(t.code==="RequestEntityTooLarge"){t.message="Request body must be less than 1 MB"}else{t.message=i.message||i.Message||null}}catch(i){t.statusCode=r.statusCode;t.message=r.statusMessage}}else{t.statusCode=r.statusCode;t.message=r.statusCode.toString()}e.error=n.error(new Error,t)}function u(e){var t=e.httpResponse.body.toString()||"{}";if(e.request.service.config.convertResponseTypes===false){e.data=JSON.parse(t)}else{var r=e.request.service.api.operations[e.request.operation];var i=r.output||{};var n=new a;e.data=n.parse(t,i)}}t.exports={buildRequest:i,extractError:o,extractData:u}},{"../json/builder":61,"../json/parser":62,"../util":112}],62:[function(e,t,r){var s=e("../util");function i(){}i.prototype.parse=function(e,t){return u(JSON.parse(e),t)};function u(e,t){if(!t||e===undefined)return undefined;switch(t.type){case"structure":return n(e,t);case"map":return o(e,t);case"list":return a(e,t);default:return c(e,t)}}function n(a,e){if(a==null)return undefined;var o={};var t=e.members;s.each(t,function(e,t){var r=t.isLocationName?t.name:e;if(Object.prototype.hasOwnProperty.call(a,r)){var i=a[r];var n=u(i,t);if(n!==undefined)o[e]=n}});return o}function a(e,r){if(e==null)return undefined;var i=[];s.arrayEach(e,function(e){var t=u(e,r.member);if(t===undefined)i.push(null);else i.push(t)});return i}function o(e,i){if(e==null)return undefined;var n={};s.each(e,function(e,t){var r=u(t,i.value);if(r===undefined)n[e]=null;else n[e]=r});return n}function c(e,t){return t.toType(e)}t.exports=i},{"../util":112}],61:[function(e,t,r){var s=e("../util");function i(){}i.prototype.build=function(e,t){return JSON.stringify(u(e,t))};function u(e,t){if(!t||e===undefined||e===null)return undefined;switch(t.type){case"structure":return n(e,t);case"map":return o(e,t);case"list":return a(e,t);default:return c(e,t)}}function n(e,a){var o={};s.each(e,function(e,t){var r=a.members[e];if(r){if(r.location!=="body")return;var i=r.isLocationName?r.name:e;var n=u(t,r);if(n!==undefined)o[i]=n}});return o}function a(e,r){var i=[];s.arrayEach(e,function(e){var t=u(e,r.member);if(t!==undefined)i.push(t)});return i}function o(e,i){var n={};s.each(e,function(e,t){var r=u(t,i.value);if(r!==undefined)n[e]=r});return n}function c(e,t){return t.toWireFormat(e)}t.exports=i},{"../util":112}],112:[function(o,e,t){(function(r,i){var c;var f={environment:"nodejs",engine:function e(){if(f.isBrowser()&&typeof navigator!=="undefined"){return navigator.userAgent}else{var e=r.platform+"/"+r.version;if(r.env.AWS_EXECUTION_ENV){e+=" exec-env/"+r.env.AWS_EXECUTION_ENV}return e}},userAgent:function e(){var t=f.environment;var r="aws-sdk-"+t+"/"+o("./core").VERSION;if(t==="nodejs")r+=" "+f.engine();return r},isBrowser:function e(){return r&&r.browser},isNode:function e(){return!f.isBrowser()},uriEscape:function e(t){var r=encodeURIComponent(t);r=r.replace(/[^A-Za-z0-9_.~\-%]+/g,escape);r=r.replace(/[*]/g,function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()});return r},uriEscapePath:function e(t){var r=[];f.arrayEach(t.split("/"),function(e){r.push(f.uriEscape(e))});return r.join("/")},urlParse:function e(t){return f.url.parse(t)},urlFormat:function e(t){return f.url.format(t)},queryStringParse:function e(t){return f.querystring.parse(t)},queryParamsToString:function e(a){var o=[];var s=f.uriEscape;var t=Object.keys(a).sort();f.arrayEach(t,function(e){var t=a[e];var r=s(e);var i=r+"=";if(Array.isArray(t)){var n=[];f.arrayEach(t,function(e){n.push(s(e))});i=r+"="+n.sort().join("&"+r+"=")}else if(t!==undefined&&t!==null){i=r+"="+s(t)}o.push(i)});return o.join("&")},readFileSync:function e(t){if(f.isBrowser())return null;return o("fs").readFileSync(t,"utf-8")},base64:{encode:function e(t){if(typeof t==="number"){throw f.error(new Error("Cannot base64 encode number "+t))}if(t===null||typeof t==="undefined"){return t}var r=typeof f.Buffer.from==="function"&&f.Buffer.from!==Uint8Array.from?f.Buffer.from(t):new f.Buffer(t);return r.toString("base64")},decode:function e(t){if(typeof t==="number"){throw f.error(new Error("Cannot base64 decode number "+t))}if(t===null||typeof t==="undefined"){return t}return typeof f.Buffer.from==="function"&&f.Buffer.from!==Uint8Array.from?f.Buffer.from(t,"base64"):new f.Buffer(t,"base64")}},buffer:{toStream:function e(r){if(!f.Buffer.isBuffer(r))r=new f.Buffer(r);var i=new f.stream.Readable;var n=0;i._read=function(e){if(n>=r.length)return i.push(null);var t=n+e;if(t>r.length)t=r.length;i.push(r.slice(n,t));n=t};return i},concat:function(e){var t=0,r=0,i=null,n;for(n=0;n<e.length;n++){t+=e[n].length}i=new f.Buffer(t);for(n=0;n<e.length;n++){e[n].copy(i,r);r+=e[n].length}return i}},string:{byteLength:function e(t){if(t===null||t===undefined)return 0;if(typeof t==="string")t=new f.Buffer(t);if(typeof t.byteLength==="number"){return t.byteLength}else if(typeof t.length==="number"){return t.length}else if(typeof t.size==="number"){return t.size}else if(typeof t.path==="string"){return o("fs").lstatSync(t.path).size}else{throw f.error(new Error("Cannot determine length of "+t),{object:t})}},upperFirst:function e(t){return t[0].toUpperCase()+t.substr(1)},lowerFirst:function e(t){return t[0].toLowerCase()+t.substr(1)}},ini:{parse:function e(t){var i,n={};f.arrayEach(t.split(/\r?\n/),function(e){e=e.split(/(^|\s)[;#]/)[0];var t=e.match(/^\s*\[([^\[\]]+)\]\s*$/);if(t){i=t[1]}else if(i){var r=e.match(/^\s*(.+?)\s*=\s*(.+?)\s*$/);if(r){n[i]=n[i]||{};n[i][r[1]]=r[2]}}});return n}},fn:{noop:function(){},makeAsync:function e(i,t){if(t&&t<=i.length){return i}return function(){var e=Array.prototype.slice.call(arguments,0);var t=e.pop();var r=i.apply(null,e);t(r)}}},date:{getDate:function e(){if(!c)c=o("./core");if(c.config.systemClockOffset){return new Date((new Date).getTime()+c.config.systemClockOffset)}else{return new Date}},iso8601:function e(t){if(t===undefined){t=f.date.getDate()}return t.toISOString().replace(/\.\d{3}Z$/,"Z")},rfc822:function e(t){if(t===undefined){t=f.date.getDate()}return t.toUTCString()},unixTimestamp:function e(t){if(t===undefined){t=f.date.getDate()}return t.getTime()/1e3},from:function e(t){if(typeof t==="number"){return new Date(t*1e3)}else{return new Date(t)}},format:function e(t,r){if(!r)r="iso8601";return f.date[r](f.date.from(t))},parseTimestamp:function e(t){if(typeof t==="number"){return new Date(t*1e3)}else if(t.match(/^\d+$/)){return new Date(t*1e3)}else if(t.match(/^\d{4}/)){return new Date(t)}else if(t.match(/^\w{3},/)){return new Date(t)}else{throw f.error(new Error("unhandled timestamp format: "+t),{code:"TimestampParserError"})}}},crypto:{crc32Table:[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117],crc32:function e(t){var r=f.crypto.crc32Table;var i=0^-1;if(typeof t==="string"){t=new f.Buffer(t)}for(var n=0;n<t.length;n++){var a=t.readUInt8(n);i=i>>>8^r[(i^a)&255]}return(i^-1)>>>0},hmac:function e(t,r,i,n){if(!i)i="binary";if(i==="buffer"){i=undefined}if(!n)n="sha256";if(typeof r==="string")r=new f.Buffer(r);return f.crypto.lib.createHmac(n,t).update(r).digest(i)},md5:function e(t,r,i){return f.crypto.hash("md5",t,r,i)},sha256:function e(t,r,i){return f.crypto.hash("sha256",t,r,i)},hash:function(e,t,r,i){var n=f.crypto.createHash(e);if(!r){r="binary"}if(r==="buffer"){r=undefined}if(typeof t==="string")t=new f.Buffer(t);var a=f.arraySliceFn(t);var o=f.Buffer.isBuffer(t);if(f.isBrowser()&&typeof ArrayBuffer!=="undefined"&&t&&t.buffer instanceof ArrayBuffer)o=true;if(i&&typeof t==="object"&&typeof t.on==="function"&&!o){t.on("data",function(e){n.update(e)});t.on("error",function(e){i(e)});t.on("end",function(){i(null,n.digest(r))})}else if(i&&a&&!o&&typeof FileReader!=="undefined"){var s=0,u=1024*512;var c=new FileReader;c.onerror=function(){i(new Error("Failed to read data."))};c.onload=function(){var e=new f.Buffer(new Uint8Array(c.result));n.update(e);s+=e.length;c._continueReading()};c._continueReading=function(){if(s>=t.size){i(null,n.digest(r));return}var e=s+u;if(e>t.size)e=t.size;c.readAsArrayBuffer(a.call(t,s,e))};c._continueReading()}else{if(f.isBrowser()&&typeof t==="object"&&!o){t=new f.Buffer(new Uint8Array(t))}var l=n.update(t).digest(r);if(i)i(null,l);return l}},toHex:function e(t){var r=[];for(var i=0;i<t.length;i++){r.push(("0"+t.charCodeAt(i).toString(16)).substr(-2,2))}return r.join("")},createHash:function e(t){return f.crypto.lib.createHash(t)}},abort:{},each:function e(t,r){for(var i in t){if(Object.prototype.hasOwnProperty.call(t,i)){var n=r.call(this,i,t[i]);if(n===f.abort)break}}},arrayEach:function e(t,r){for(var i in t){if(Object.prototype.hasOwnProperty.call(t,i)){var n=r.call(this,t[i],parseInt(i,10));if(n===f.abort)break}}},update:function e(i,t){f.each(t,function e(t,r){i[t]=r});return i},merge:function e(t,r){return f.update(f.copy(t),r)},copy:function e(t){if(t===null||t===undefined)return t;var r={};for(var i in t){r[i]=t[i]}return r},isEmpty:function e(t){for(var r in t){if(Object.prototype.hasOwnProperty.call(t,r)){return false}}return true},arraySliceFn:function e(t){var r=t.slice||t.webkitSlice||t.mozSlice;return typeof r==="function"?r:null},isType:function e(t,r){if(typeof r==="function")r=f.typeName(r);return Object.prototype.toString.call(t)==="[object "+r+"]"},typeName:function e(t){if(Object.prototype.hasOwnProperty.call(t,"name"))return t.name;var r=t.toString();var i=r.match(/^\s*function (.+)\(/);return i?i[1]:r},error:function e(t,r){var i=null;if(typeof t.message==="string"&&t.message!==""){if(typeof r==="string"||r&&r.message){i=f.copy(t);i.message=t.message}}t.message=t.message||null;if(typeof r==="string"){t.message=r}else if(typeof r==="object"&&r!==null){f.update(t,r);if(r.message)t.message=r.message;if(r.code||r.name)t.code=r.code||r.name;if(r.stack)t.stack=r.stack}if(typeof Object.defineProperty==="function"){Object.defineProperty(t,"name",{writable:true,enumerable:false});Object.defineProperty(t,"message",{enumerable:true})}t.name=r&&r.name||t.name||t.code||"Error";t.time=new Date;if(i)t.originalError=i;return t},inherit:function e(t,r){var i=null;if(r===undefined){r=t;t=Object;i={}}else{var n=function e(){};n.prototype=t.prototype;i=new n}if(r.constructor===Object){r.constructor=function(){if(t!==Object){return t.apply(this,arguments)}}}r.constructor.prototype=i;f.update(r.constructor.prototype,r);r.constructor.__super__=t;return r.constructor},mixin:function e(){var t=arguments[0];for(var r=1;r<arguments.length;r++){for(var i in arguments[r].prototype){var n=arguments[r].prototype[i];if(i!=="constructor"){t.prototype[i]=n}}}return t},hideProperties:function e(t,r){if(typeof Object.defineProperty!=="function")return;f.arrayEach(r,function(e){Object.defineProperty(t,e,{enumerable:false,writable:true,configurable:true})})},property:function e(t,r,i,n,a){var o={configurable:true,enumerable:n!==undefined?n:true};if(typeof i==="function"&&!a){o.get=i}else{o.value=i;o.writable=true}Object.defineProperty(t,r,o)},memoizedProperty:function e(t,r,i,n){var a=null;f.property(t,r,function(){if(a===null){a=i()}return a},n)},hoistPayloadMember:function e(r){var t=r.request;var i=t.operation;var n=t.service.api.operations[i];var a=n.output;if(a.payload&&!n.hasEventOutput){var o=a.members[a.payload];var s=r.data[a.payload];if(o.type==="structure"){f.each(s,function(e,t){f.property(r.data,e,t,false)})}}},computeSha256:function e(t,r){if(f.isNode()){var i=f.stream.Stream;var n=o("fs");if(t instanceof i){if(typeof t.path==="string"){var a={};if(typeof t.start==="number"){a.start=t.start}if(typeof t.end==="number"){a.end=t.end}t=n.createReadStream(t.path,a)}else{return r(new Error("Non-file stream objects are "+"not supported with SigV4"))}}}f.crypto.sha256(t,"hex",function(e,t){if(e)r(e);else r(null,t)})},isClockSkewed:function e(t){if(t){f.property(c.config,"isClockSkewed",Math.abs((new Date).getTime()-t)>=3e5,false);return c.config.isClockSkewed}},applyClockOffset:function e(t){if(t)c.config.systemClockOffset=t-(new Date).getTime()},extractRequestId:function e(t){var r=t.httpResponse.headers["x-amz-request-id"]||t.httpResponse.headers["x-amzn-requestid"];if(!r&&t.data&&t.data.ResponseMetadata){r=t.data.ResponseMetadata.RequestId}if(r){t.requestId=r}if(t.error){t.error.requestId=r}},addPromises:function e(t,r){var i=false;if(r===undefined&&c&&c.config){r=c.config.getPromisesDependency()}if(r===undefined&&typeof Promise!=="undefined"){r=Promise}if(typeof r!=="function")i=true;if(!Array.isArray(t))t=[t];for(var n=0;n<t.length;n++){var a=t[n];if(i){if(a.deletePromisesFromClass){a.deletePromisesFromClass()}}else if(a.addPromisesToClass){a.addPromisesToClass(r)}}},promisifyMethod:function e(n,r){return function e(){var t=this;return new r(function(r,i){t[n](function(e,t){if(e){i(e)}else{r(t)}})})}},isDualstackAvailable:function e(t){if(!t)return false;var r=o("../apis/metadata.json");if(typeof t!=="string")t=t.serviceIdentifier;if(typeof t!=="string"||!r.hasOwnProperty(t))return false;return!!r[t].dualstackAvailable},calculateRetryDelay:function e(t,r){if(!r)r={};var i=r.customBackoff||null;if(typeof i==="function"){return i(t)}var n=typeof r.base==="number"?r.base:100;var a=Math.random()*(Math.pow(2,t)*n);return a},handleRequestWithRetries:function e(t,i,a){if(!i)i={};var r=c.HttpClient.getInstance();var o=i.httpOptions||{};var n=0;var s=function(e){var t=i.maxRetries||0;if(e&&e.code==="TimeoutError")e.retryable=true;if(e&&e.retryable&&n<t){n++;var r=f.calculateRetryDelay(n,i.retryDelayOptions);setTimeout(u,r+(e.retryAfter||0))}else{a(e)}};var u=function(){var n="";r.handleRequest(t,o,function(i){i.on("data",function(e){n+=e.toString()});i.on("end",function(){var e=i.statusCode;if(e<300){a(null,n)}else{var t=parseInt(i.headers["retry-after"],10)*1e3||0;var r=f.error(new Error,{retryable:e>=500||e===429});if(t&&r.retryable)r.retryAfter=t;s(r)}})},s)};c.util.defer(u)},uuid:{v4:function e(){return o("uuid").v4()}},convertPayloadToString:function e(t){var r=t.request;var i=r.operation;var n=r.service.api.operations[i].output||{};if(n.payload&&t.data[n.payload]){t.data[n.payload]=t.data[n.payload].toString()}},defer:function e(t){if(typeof r==="object"&&typeof r.nextTick==="function"){r.nextTick(t)}else if(typeof i==="function"){i(t)}else{setTimeout(t,0)}},defaultProfile:"default",configOptInEnv:"AWS_SDK_LOAD_CONFIG",sharedCredentialsFileEnv:"AWS_SHARED_CREDENTIALS_FILE",sharedConfigFileEnv:"AWS_CONFIG_FILE",imdsDisabledEnv:"AWS_EC2_METADATA_DISABLED"};e.exports=f}).call(this,o("_process"),o("timers").setImmediate)},{"../apis/metadata.json":26,"./core":38,_process:9,fs:2,timers:17,uuid:21}],37:[function(e,t,r){var s=e("./core");e("./credentials");e("./credentials/credential_provider_chain");var i;s.Config=s.util.inherit({constructor:function e(r){if(r===undefined)r={};r=this.extractCredentials(r);s.util.each.call(this,this.keys,function(e,t){this.set(e,r[e],t)})},getCredentials:function e(t){var r=this;function i(e){t(e,e?null:r.credentials)}function n(e,t){return new s.util.error(t||new Error,{code:"CredentialsError",message:e,name:"CredentialsError"})}function a(){r.credentials.get(function(e){if(e){var t="Could not load credentials from "+r.credentials.constructor.name;e=n(t,e)}i(e)})}function o(){var e=null;if(!r.credentials.accessKeyId||!r.credentials.secretAccessKey){e=n("Missing credentials")}i(e)}if(r.credentials){if(typeof r.credentials.get==="function"){a()}else{o()}}else if(r.credentialProvider){r.credentialProvider.resolve(function(e,t){if(e){e=n("Could not load credentials from any providers",e)}r.credentials=t;i(e)})}else{i(n("No credentials to load"))}},update:function e(t,r){r=r||false;t=this.extractCredentials(t);s.util.each.call(this,t,function(e,t){if(r||Object.prototype.hasOwnProperty.call(this.keys,e)||s.Service.hasService(e)){this.set(e,t)}})},loadFromPath:function e(t){this.clear();var r=JSON.parse(s.util.readFileSync(t));var i=new s.FileSystemCredentials(t);var n=new s.CredentialProviderChain;n.providers.unshift(i);n.resolve(function(e,t){if(e)throw e;else r.credentials=t});this.constructor(r);return this},clear:function e(){s.util.each.call(this,this.keys,function(e){delete this[e]});this.set("credentials",undefined);this.set("credentialProvider",undefined)},set:function e(t,r,i){if(r===undefined){if(i===undefined){i=this.keys[t]}if(typeof i==="function"){this[t]=i.call(this)}else{this[t]=i}}else if(t==="httpOptions"&&this[t]){this[t]=s.util.merge(this[t],r)}else{this[t]=r}},keys:{credentials:null,credentialProvider:null,region:null,logger:null,apiVersions:{},apiVersion:null,endpoint:undefined,httpOptions:{timeout:12e4},maxRetries:undefined,maxRedirects:10,paramValidation:true,sslEnabled:true,s3ForcePathStyle:false,s3BucketEndpoint:false,s3DisableBodySigning:true,computeChecksums:true,convertResponseTypes:true,correctClockSkew:false,customUserAgent:null,dynamoDbCrc32:true,systemClockOffset:0,signatureVersion:null,signatureCache:true,retryDelayOptions:{},useAccelerateEndpoint:false},extractCredentials:function e(t){if(t.accessKeyId&&t.secretAccessKey){t=s.util.copy(t);t.credentials=new s.Credentials(t)}return t},setPromisesDependency:function e(t){i=t;if(t===null&&typeof Promise==="function"){i=Promise}var r=[s.Request,s.Credentials,s.CredentialProviderChain];if(s.S3&&s.S3.ManagedUpload)r.push(s.S3.ManagedUpload);s.util.addPromises(r,i)},getPromisesDependency:function e(){return i}});s.config=new s.Config},{"./core":38,"./credentials":39,"./credentials/credential_provider_chain":41}],41:[function(e,t,r){var i=e("../core");i.CredentialProviderChain=i.util.inherit(i.Credentials,{constructor:function e(t){if(t){this.providers=t}else{this.providers=i.CredentialProviderChain.defaultProviders.slice(0)}},resolve:function e(i){if(this.providers.length===0){i(new Error("No providers"));return this}var n=0;var a=this.providers.slice(0);function o(e,t){if(!e&&t||n===a.length){i(e,t);return}var r=a[n++];if(typeof r==="function"){t=r.call()}else{t=r}if(t.get){t.get(function(e){o(e,e?null:t)})}else{o(null,t)}}o();return this}});i.CredentialProviderChain.defaultProviders=[];i.CredentialProviderChain.addPromisesToClass=function e(t){this.prototype.resolvePromise=i.util.promisifyMethod("resolve",t)};i.CredentialProviderChain.deletePromisesFromClass=function e(){delete this.prototype.resolvePromise};i.util.addPromises(i.CredentialProviderChain)},{"../core":38}],39:[function(e,t,r){var i=e("./core");i.Credentials=i.util.inherit({constructor:function e(){i.util.hideProperties(this,["secretAccessKey"]);this.expired=false;this.expireTime=null;if(arguments.length===1&&typeof arguments[0]==="object"){var t=arguments[0].credentials||arguments[0];this.accessKeyId=t.accessKeyId;this.secretAccessKey=t.secretAccessKey;this.sessionToken=t.sessionToken}else{this.accessKeyId=arguments[0];this.secretAccessKey=arguments[1];this.sessionToken=arguments[2]}},expiryWindow:15,needsRefresh:function e(){var t=i.util.date.getDate().getTime();var r=new Date(t+this.expiryWindow*1e3);if(this.expireTime&&r>this.expireTime){return true}else{return this.expired||!this.accessKeyId||!this.secretAccessKey}},get:function e(t){var r=this;if(this.needsRefresh()){this.refresh(function(e){if(!e)r.expired=false;if(t)t(e)})}else if(t){t()}},refresh:function e(t){this.expired=false;t()}});i.Credentials.addPromisesToClass=function e(t){this.prototype.getPromise=i.util.promisifyMethod("get",t);this.prototype.refreshPromise=i.util.promisifyMethod("refresh",t)};i.Credentials.deletePromisesFromClass=function e(){delete this.prototype.getPromise;delete this.prototype.refreshPromise};i.util.addPromises(i.Credentials)},{"./core":38}],27:[function(e,t,r){function i(e,t){if(!i.services.hasOwnProperty(e)){throw new Error("InvalidService: Failed to load api for "+e)}return i.services[e][t]}i.services={};t.exports=i},{}],26:[function(e,t,r){t.exports={acm:{name:"ACM",cors:true},apigateway:{name:"APIGateway",cors:true},applicationautoscaling:{prefix:"application-autoscaling",name:"ApplicationAutoScaling",cors:true},appstream:{name:"AppStream"},autoscaling:{name:"AutoScaling",cors:true},batch:{name:"Batch"},budgets:{name:"Budgets"},clouddirectory:{name:"CloudDirectory",versions:["2016-05-10*"]},cloudformation:{name:"CloudFormation",cors:true},cloudfront:{name:"CloudFront",versions:["2013-05-12*","2013-11-11*","2014-05-31*","2014-10-21*","2014-11-06*","2015-04-17*","2015-07-27*","2015-09-17*","2016-01-13*","2016-01-28*","2016-08-01*","2016-08-20*","2016-09-07*","2016-09-29*","2016-11-25*","2017-03-25*","2017-10-30*"],cors:true},cloudhsm:{name:"CloudHSM",cors:true},cloudsearch:{name:"CloudSearch"},cloudsearchdomain:{name:"CloudSearchDomain"},cloudtrail:{name:"CloudTrail",cors:true},cloudwatch:{prefix:"monitoring",name:"CloudWatch",cors:true},cloudwatchevents:{prefix:"events",name:"CloudWatchEvents",versions:["2014-02-03*"],cors:true},cloudwatchlogs:{prefix:"logs",name:"CloudWatchLogs",cors:true},codebuild:{name:"CodeBuild",cors:true},codecommit:{name:"CodeCommit",cors:true},codedeploy:{name:"CodeDeploy",cors:true},codepipeline:{name:"CodePipeline",cors:true},cognitoidentity:{prefix:"cognito-identity",name:"CognitoIdentity",cors:true},cognitoidentityserviceprovider:{prefix:"cognito-idp",name:"CognitoIdentityServiceProvider",cors:true},cognitosync:{prefix:"cognito-sync",name:"CognitoSync",cors:true},configservice:{prefix:"config",name:"ConfigService",cors:true},cur:{name:"CUR",cors:true},datapipeline:{name:"DataPipeline"},devicefarm:{name:"DeviceFarm",cors:true},directconnect:{name:"DirectConnect",cors:true},directoryservice:{prefix:"ds",name:"DirectoryService"},discovery:{name:"Discovery"},dms:{name:"DMS"},dynamodb:{name:"DynamoDB",cors:true},dynamodbstreams:{prefix:"streams.dynamodb",name:"DynamoDBStreams",cors:true},ec2:{name:"EC2",versions:["2013-06-15*","2013-10-15*","2014-02-01*","2014-05-01*","2014-06-15*","2014-09-01*","2014-10-01*","2015-03-01*","2015-04-15*","2015-10-01*","2016-04-01*","2016-09-15*"],cors:true},ecr:{name:"ECR",cors:true},ecs:{name:"ECS",cors:true},efs:{prefix:"elasticfilesystem",name:"EFS",cors:true},elasticache:{name:"ElastiCache",versions:["2012-11-15*","2014-03-24*","2014-07-15*","2014-09-30*"],cors:true},elasticbeanstalk:{name:"ElasticBeanstalk",cors:true},elb:{prefix:"elasticloadbalancing",name:"ELB",cors:true},elbv2:{prefix:"elasticloadbalancingv2",name:"ELBv2",cors:true},emr:{prefix:"elasticmapreduce",name:"EMR",cors:true},es:{name:"ES"},elastictranscoder:{name:"ElasticTranscoder",cors:true},firehose:{name:"Firehose",cors:true},gamelift:{name:"GameLift",cors:true},glacier:{name:"Glacier"},health:{name:"Health"},iam:{name:"IAM"},importexport:{name:"ImportExport"},inspector:{name:"Inspector",versions:["2015-08-18*"],cors:true},iot:{name:"Iot",cors:true},iotdata:{prefix:"iot-data",name:"IotData",cors:true},kinesis:{name:"Kinesis",cors:true},kinesisanalytics:{name:"KinesisAnalytics"},kms:{name:"KMS",cors:true},lambda:{name:"Lambda",cors:true},lexruntime:{prefix:"runtime.lex",name:"LexRuntime",cors:true},lightsail:{name:"Lightsail"},machinelearning:{name:"MachineLearning",cors:true},marketplacecommerceanalytics:{name:"MarketplaceCommerceAnalytics",cors:true},marketplacemetering:{prefix:"meteringmarketplace",name:"MarketplaceMetering"},mturk:{prefix:"mturk-requester",name:"MTurk",cors:true},mobileanalytics:{name:"MobileAnalytics",cors:true},opsworks:{name:"OpsWorks",cors:true},opsworkscm:{name:"OpsWorksCM"},organizations:{name:"Organizations"},pinpoint:{name:"Pinpoint"},polly:{name:"Polly",cors:true},rds:{name:"RDS",versions:["2014-09-01*"],cors:true},redshift:{name:"Redshift",cors:true},rekognition:{name:"Rekognition",cors:true},resourcegroupstaggingapi:{name:"ResourceGroupsTaggingAPI"},route53:{name:"Route53",cors:true},route53domains:{name:"Route53Domains",cors:true},s3:{name:"S3",dualstackAvailable:true,cors:true},servicecatalog:{name:"ServiceCatalog",cors:true},ses:{prefix:"email",name:"SES",cors:true},shield:{name:"Shield"},simpledb:{prefix:"sdb",name:"SimpleDB"},sms:{name:"SMS"},snowball:{name:"Snowball"},sns:{name:"SNS",cors:true},sqs:{name:"SQS",cors:true},ssm:{name:"SSM",cors:true},storagegateway:{name:"StorageGateway",cors:true},stepfunctions:{prefix:"states",name:"StepFunctions"},sts:{name:"STS",cors:true},support:{name:"Support"},swf:{name:"SWF"},xray:{name:"XRay"},waf:{name:"WAF",cors:true},wafregional:{prefix:"waf-regional",name:"WAFRegional"},workdocs:{name:"WorkDocs",cors:true},workspaces:{name:"WorkSpaces"},codestar:{name:"CodeStar"},lexmodelbuildingservice:{prefix:"lex-models",name:"LexModelBuildingService",cors:true},marketplaceentitlementservice:{prefix:"entitlement.marketplace",name:"MarketplaceEntitlementService"},athena:{name:"Athena"},greengrass:{name:"Greengrass"},dax:{name:"DAX"},migrationhub:{prefix:"AWSMigrationHub",name:"MigrationHub"},cloudhsmv2:{name:"CloudHSMV2"},glue:{name:"Glue"},mobile:{name:"Mobile"},pricing:{name:"Pricing"},costexplorer:{prefix:"ce",name:"CostExplorer"},mediaconvert:{name:"MediaConvert"},medialive:{name:"MediaLive"},mediapackage:{name:"MediaPackage"},mediastore:{name:"MediaStore"},mediastoredata:{prefix:"mediastore-data",name:"MediaStoreData"},appsync:{name:"AppSync"},guardduty:{name:"GuardDuty"},mq:{name:"MQ"},comprehend:{name:"Comprehend"},iotjobsdataplane:{prefix:"iot-jobs-data",name:"IoTJobsDataPlane"},kinesisvideoarchivedmedia:{prefix:"kinesis-video-archived-media",name:"KinesisVideoArchivedMedia",cors:true},kinesisvideomedia:{prefix:"kinesis-video-media",name:"KinesisVideoMedia"},kinesisvideo:{name:"KinesisVideo",cors:true},sagemakerruntime:{prefix:"runtime.sagemaker",name:"SageMakerRuntime"},sagemaker:{name:"SageMaker"},translate:{name:"Translate",cors:true},resourcegroups:{prefix:"resource-groups",name:"ResourceGroups"},alexaforbusiness:{name:"AlexaForBusiness"},cloud9:{name:"Cloud9"},serverlessapplicationrepository:{prefix:"serverlessrepo",name:"ServerlessApplicationRepository"},servicediscovery:{name:"ServiceDiscovery"},workmail:{name:"WorkMail"},autoscalingplans:{prefix:"autoscaling-plans",name:"AutoScalingPlans"},transcribeservice:{prefix:"transcribe",name:"TranscribeService"},connect:{name:"Connect"},acmpca:{prefix:"acm-pca",name:"ACMPCA"},fms:{name:"FMS"},secretsmanager:{name:"SecretsManager",cors:true},iotanalytics:{name:"IoTAnalytics"},iot1clickdevicesservice:{prefix:"iot1click-devices",name:"IoT1ClickDevicesService"},iot1clickprojects:{prefix:"iot1click-projects",name:"IoT1ClickProjects"},pi:{name:"PI"},neptune:{name:"Neptune"},mediatailor:{name:"MediaTailor"},eks:{name:"EKS"},macie:{name:"Macie"},dlm:{name:"DLM"}}},{}],21:[function(e,t,r){var i=e("./v1");var n=e("./v4");var a=n;a.v1=i;a.v4=n;t.exports=a},{"./v1":24,"./v4":25}],25:[function(e,t,r){var o=e("./lib/rng");var s=e("./lib/bytesToUuid");function i(e,t,r){var i=t&&r||0;if(typeof e=="string"){t=e=="binary"?new Array(16):null;e=null}e=e||{};var n=e.random||(e.rng||o)();n[6]=n[6]&15|64;n[8]=n[8]&63|128;if(t){for(var a=0;a<16;++a){t[i+a]=n[a]}}return t||s(n)}t.exports=i},{"./lib/bytesToUuid":22,"./lib/rng":23}],24:[function(e,t,r){var i=e("./lib/rng");var h=e("./lib/bytesToUuid");var n=i();var d=[n[0]|1,n[1],n[2],n[3],n[4],n[5]];var m=(n[6]<<8|n[7])&16383;var y=0,v=0;function a(e,t,r){var i=t&&r||0;var n=t||[];e=e||{};var a=e.clockseq!==undefined?e.clockseq:m;var o=e.msecs!==undefined?e.msecs:(new Date).getTime();var s=e.nsecs!==undefined?e.nsecs:v+1;var u=o-y+(s-v)/1e4;if(u<0&&e.clockseq===undefined){a=a+1&16383}if((u<0||o>y)&&e.nsecs===undefined){s=0}if(s>=1e4){throw new Error("uuid.v1(): Can't create more than 10M uuids/sec")}y=o;v=s;m=a;o+=122192928e5;var c=((o&268435455)*1e4+s)%4294967296;n[i++]=c>>>24&255;n[i++]=c>>>16&255;n[i++]=c>>>8&255;n[i++]=c&255;var l=o/4294967296*1e4&268435455;n[i++]=l>>>8&255;n[i++]=l&255;n[i++]=l>>>24&15|16;n[i++]=l>>>16&255;n[i++]=a>>>8|128;n[i++]=a&255;var f=e.node||d;for(var p=0;p<6;++p){n[i+p]=f[p]}return t?t:h(n)}t.exports=a},{"./lib/bytesToUuid":22,"./lib/rng":23}],23:[function(e,a,t){(function(e){var t;var r=e.crypto||e.msCrypto;if(r&&r.getRandomValues){var i=new Uint8Array(16);t=function e(){r.getRandomValues(i);return i}}if(!t){var n=new Array(16);t=function(){for(var e=0,t;e<16;e++){if((e&3)===0)t=Math.random()*4294967296;n[e]=t>>>((e&3)<<3)&255}return n}}a.exports=t}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],22:[function(e,t,r){var n=[];for(var i=0;i<256;++i){n[i]=(i+256).toString(16).substr(1)}function a(e,t){var r=t||0;var i=n;return i[e[r++]]+i[e[r++]]+i[e[r++]]+i[e[r++]]+"-"+i[e[r++]]+i[e[r++]]+"-"+i[e[r++]]+i[e[r++]]+"-"+i[e[r++]]+i[e[r++]]+"-"+i[e[r++]]+i[e[r++]]+i[e[r++]]+i[e[r++]]+i[e[r++]]+i[e[r++]]}t.exports=a},{}],20:[function(I,e,L){(function(n,a){var s=/%[sdj%]/g;L.format=function(e){if(!k(e)){var t=[];for(var r=0;r<arguments.length;r++){t.push(u(arguments[r]))}return t.join(" ")}var r=1;var i=arguments;var n=i.length;var a=String(e).replace(s,function(e){if(e==="%%")return"%";if(r>=n)return e;switch(e){case"%s":return String(i[r++]);case"%d":return Number(i[r++]);case"%j":try{return JSON.stringify(i[r++])}catch(e){return"[Circular]"}default:return e}});for(var o=i[r];r<n;o=i[++r]){if(f(o)||!w(o)){a+=" "+o}else{a+=" "+u(o)}}return a};L.deprecate=function(e,t){if(C(a.process)){return function(){return L.deprecate(e,t).apply(this,arguments)}}if(n.noDeprecation===true){return e}var r=false;function i(){if(!r){if(n.throwDeprecation){throw new Error(t)}else if(n.traceDeprecation){console.trace(t)}else{console.error(t)}r=true}return e.apply(this,arguments)}return i};var e={};var i;L.debuglog=function(t){if(C(i))i=n.env.NODE_DEBUG||"";t=t.toUpperCase();if(!e[t]){if(new RegExp("\\b"+t+"\\b","i").test(i)){var r=n.pid;e[t]=function(){var e=L.format.apply(L,arguments);console.error("%s %d: %s",t,r,e)}}else{e[t]=function(){}}}return e[t]};function u(e,t){var r={seen:[],stylize:c};if(arguments.length>=3)r.depth=arguments[2];if(arguments.length>=4)r.colors=arguments[3];if(l(t)){r.showHidden=t}else if(t){L._extend(r,t)}if(C(r.showHidden))r.showHidden=false;if(C(r.depth))r.depth=2;if(C(r.colors))r.colors=false;if(C(r.customInspect))r.customInspect=true;if(r.colors)r.stylize=o;return d(r,e,r.depth)}L.inspect=u;u.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]};u.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function o(e,t){var r=u.styles[t];if(r){return"["+u.colors[r][0]+"m"+e+"["+u.colors[r][1]+"m"}else{return e}}function c(e,t){return e}function h(e){var r={};e.forEach(function(e,t){r[e]=true});return r}function d(t,r,i){if(t.customInspect&&r&&R(r.inspect)&&r.inspect!==L.inspect&&!(r.constructor&&r.constructor.prototype===r)){var e=r.inspect(i,t);if(!k(e)){e=d(t,e,i)}return e}var n=m(t,r);if(n){return n}var a=Object.keys(r);var o=h(a);if(t.showHidden){a=Object.getOwnPropertyNames(r)}if(N(r)&&(a.indexOf("message")>=0||a.indexOf("description")>=0)){return y(r)}if(a.length===0){if(R(r)){var s=r.name?": "+r.name:"";return t.stylize("[Function"+s+"]","special")}if(E(r)){return t.stylize(RegExp.prototype.toString.call(r),"regexp")}if(x(r)){return t.stylize(Date.prototype.toString.call(r),"date")}if(N(r)){return y(r)}}var u="",c=false,l=["{","}"];if(S(r)){c=true;l=["[","]"]}if(R(r)){var f=r.name?": "+r.name:"";u=" [Function"+f+"]"}if(E(r)){u=" "+RegExp.prototype.toString.call(r)}if(x(r)){u=" "+Date.prototype.toUTCString.call(r)}if(N(r)){u=" "+y(r)}if(a.length===0&&(!c||r.length==0)){return l[0]+u+l[1]}if(i<0){if(E(r)){return t.stylize(RegExp.prototype.toString.call(r),"regexp")}else{return t.stylize("[Object]","special")}}t.seen.push(r);var p;if(c){p=v(t,r,i,o,a)}else{p=a.map(function(e){return g(t,r,i,o,e,c)})}t.seen.pop();return b(p,u,l)}function m(e,t){if(C(t))return e.stylize("undefined","undefined");if(k(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(p(t))return e.stylize(""+t,"number");if(l(t))return e.stylize(""+t,"boolean");if(f(t))return e.stylize("null","null")}function y(e){return"["+Error.prototype.toString.call(e)+"]"}function v(t,r,i,n,e){var a=[];for(var o=0,s=r.length;o<s;++o){if(P(r,String(o))){a.push(g(t,r,i,n,String(o),true))}else{a.push("")}}e.forEach(function(e){if(!e.match(/^\d+$/)){a.push(g(t,r,i,n,e,true))}});return a}function g(e,t,r,i,n,a){var o,s,u;u=Object.getOwnPropertyDescriptor(t,n)||{value:t[n]};if(u.get){if(u.set){s=e.stylize("[Getter/Setter]","special")}else{s=e.stylize("[Getter]","special")}}else{if(u.set){s=e.stylize("[Setter]","special")}}if(!P(i,n)){o="["+n+"]"}if(!s){if(e.seen.indexOf(u.value)<0){if(f(r)){s=d(e,u.value,null)}else{s=d(e,u.value,r-1)}if(s.indexOf("\n")>-1){if(a){s=s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2)}else{s="\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")}}}else{s=e.stylize("[Circular]","special")}}if(C(o)){if(a&&n.match(/^\d+$/)){return s}o=JSON.stringify(""+n);if(o.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)){o=o.substr(1,o.length-2);o=e.stylize(o,"name")}else{o=o.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'");o=e.stylize(o,"string")}}return o+": "+s}function b(e,t,r){var i=0;var n=e.reduce(function(e,t){i++;if(t.indexOf("\n")>=0)i++;return e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0);if(n>60){return r[0]+(t===""?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1]}return r[0]+t+" "+e.join(", ")+" "+r[1]}function S(e){return Array.isArray(e)}L.isArray=S;function l(e){return typeof e==="boolean"}L.isBoolean=l;function f(e){return e===null}L.isNull=f;function t(e){return e==null}L.isNullOrUndefined=t;function p(e){return typeof e==="number"}L.isNumber=p;function k(e){return typeof e==="string"}L.isString=k;function r(e){return typeof e==="symbol"}L.isSymbol=r;function C(e){return e===void 0}L.isUndefined=C;function E(e){return w(e)&&_(e)==="[object RegExp]"}L.isRegExp=E;function w(e){return typeof e==="object"&&e!==null}L.isObject=w;function x(e){return w(e)&&_(e)==="[object Date]"}L.isDate=x;function N(e){return w(e)&&(_(e)==="[object Error]"||e instanceof Error)}L.isError=N;function R(e){return typeof e==="function"}L.isFunction=R;function q(e){return e===null||typeof e==="boolean"||typeof e==="number"||typeof e==="string"||typeof e==="symbol"||typeof e==="undefined"}L.isPrimitive=q;L.isBuffer=I("./support/isBuffer");function _(e){return Object.prototype.toString.call(e)}function B(e){return e<10?"0"+e.toString(10):e.toString(10)}var T=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function A(){var e=new Date;var t=[B(e.getHours()),B(e.getMinutes()),B(e.getSeconds())].join(":");return[e.getDate(),T[e.getMonth()],t].join(" ")}L.log=function(){console.log("%s - %s",A(),L.format.apply(L,arguments))};L.inherits=I("inherits");L._extend=function(e,t){if(!t||!w(t))return e;var r=Object.keys(t);var i=r.length;while(i--){e[r[i]]=t[r[i]]}return e};function P(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}).call(this,I("_process"),typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"./support/isBuffer":19,_process:9,inherits:6}],19:[function(e,t,r){t.exports=function e(t){return t&&typeof t==="object"&&typeof t.copy==="function"&&typeof t.fill==="function"&&typeof t.readUInt8==="function"}},{}],17:[function(u,e,c){(function(e,t){var n=u("process/browser.js").nextTick;var r=Function.prototype.apply;var a=Array.prototype.slice;var o={};var s=0;c.setTimeout=function(){return new i(r.call(setTimeout,window,arguments),clearTimeout)};c.setInterval=function(){return new i(r.call(setInterval,window,arguments),clearInterval)};c.clearTimeout=c.clearInterval=function(e){e.close()};function i(e,t){this._id=e;this._clearFn=t}i.prototype.unref=i.prototype.ref=function(){};i.prototype.close=function(){this._clearFn.call(window,this._id)};c.enroll=function(e,t){clearTimeout(e._idleTimeoutId);e._idleTimeout=t};c.unenroll=function(e){clearTimeout(e._idleTimeoutId);e._idleTimeout=-1};c._unrefActive=c.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;if(e>=0){t._idleTimeoutId=setTimeout(function e(){if(t._onTimeout)t._onTimeout()},e)}};c.setImmediate=typeof e==="function"?e:function(t){var r=s++;var i=arguments.length<2?false:a.call(arguments,1);o[r]=true;n(function e(){if(o[r]){if(i){t.apply(null,i)}else{t.call(null)}c.clearImmediate(r)}});return r};c.clearImmediate=typeof t==="function"?t:function(e){delete o[e]}}).call(this,u("timers").setImmediate,u("timers").clearImmediate)},{"process/browser.js":9,timers:17}],9:[function(e,t,r){var i=t.exports={};var n;var a;function o(){throw new Error("setTimeout has not been defined")}function s(){throw new Error("clearTimeout has not been defined")}(function(){try{if(typeof setTimeout==="function"){n=setTimeout}else{n=o}}catch(e){n=o}try{if(typeof clearTimeout==="function"){a=clearTimeout}else{a=s}}catch(e){a=s}})();function u(t){if(n===setTimeout){return setTimeout(t,0)}if((n===o||!n)&&setTimeout){n=setTimeout;return setTimeout(t,0)}try{return n(t,0)}catch(e){try{return n.call(null,t,0)}catch(e){return n.call(this,t,0)}}}function c(t){if(a===clearTimeout){return clearTimeout(t)}if((a===s||!a)&&clearTimeout){a=clearTimeout;return clearTimeout(t)}try{return a(t)}catch(e){try{return a.call(null,t)}catch(e){return a.call(this,t)}}}var l=[];var f=false;var p;var h=-1;function d(){if(!f||!p){return}f=false;if(p.length){l=p.concat(l)}else{h=-1}if(l.length){m()}}function m(){if(f){return}var e=u(d);f=true;var t=l.length;while(t){p=l;l=[];while(++h<t){if(p){p[h].run()}}h=-1;t=l.length}p=null;f=false;c(e)}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1){for(var r=1;r<arguments.length;r++){t[r-1]=arguments[r]}}l.push(new y(e,t));if(l.length===1&&!f){u(m)}};function y(e,t){this.fun=e;this.array=t}y.prototype.run=function(){this.fun.apply(null,this.array)};i.title="browser";i.browser=true;i.env={};i.argv=[];i.version="";i.versions={};function v(){}i.on=v;i.addListener=v;i.once=v;i.off=v;i.removeListener=v;i.removeAllListeners=v;i.emit=v;i.prependListener=v;i.prependOnceListener=v;i.listeners=function(e){return[]};i.binding=function(e){throw new Error("process.binding is not supported")};i.cwd=function(){return"/"};i.chdir=function(e){throw new Error("process.chdir is not supported")};i.umask=function(){return 0}},{}],8:[function(e,t,r){(function(e){"use strict";function q(e){if(e!==null){return Object.prototype.toString.call(e)==="[object Array]"}else{return false}}function _(e){if(e!==null){return Object.prototype.toString.call(e)==="[object Object]"}else{return false}}function B(e,t){if(e===t){return true}var r=Object.prototype.toString.call(e);if(r!==Object.prototype.toString.call(t)){return false}if(q(e)===true){if(e.length!==t.length){return false}for(var i=0;i<e.length;i++){if(B(e[i],t[i])===false){return false}}return true}if(_(e)===true){var n={};for(var a in e){if(hasOwnProperty.call(e,a)){if(B(e[a],t[a])===false){return false}n[a]=true}}for(var o in t){if(hasOwnProperty.call(t,o)){if(n[o]!==true){return false}}}return true}return false}function T(e){if(e===""||e===false||e===null){return true}else if(q(e)&&e.length===0){return true}else if(_(e)){for(var t in e){if(e.hasOwnProperty(t)){return false}}return true}else{return false}}function A(e){var t=Object.keys(e);var r=[];for(var i=0;i<t.length;i++){r.push(e[t[i]])}return r}function t(e,t){var r={};for(var i in e){r[i]=e[i]}for(var n in t){r[n]=t[n]}return r}var o;if(typeof String.prototype.trimLeft==="function"){o=function(e){return e.trimLeft()}}else{o=function(e){return e.match(/^\s*(.*)/)[1]}}var c=0;var a=1;var l=2;var s=3;var r=4;var i=5;var n=6;var u=7;var f=8;var p=9;var h="EOF";var d="UnquotedIdentifier";var m="QuotedIdentifier";var y="Rbracket";var v="Rparen";var g="Comma";var b="Colon";var S="Rbrace";var k="Number";var P="Current";var I="Expref";var L="Pipe";var C="Or";var E="And";var M="EQ";var U="GT";var D="LT";var O="GTE";var z="LTE";var j="NE";var K="Flatten";var w="Star";var x="Filter";var N="Dot";var R="Not";var F="Lbrace";var H="Lbracket";var V="Lparen";var G="Literal";var W={".":N,"*":w,",":g,":":b,"{":F,"}":S,"]":y,"(":V,")":v,"@":P};var X={"<":true,">":true,"=":true,"!":true};var Y={" ":true,"\t":true,"\n":true};function J(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e==="_"}function Q(e){return e>="0"&&e<="9"||e==="-"}function $(e){return e>="a"&&e<="z"||e>="A"&&e<="Z"||e>="0"&&e<="9"||e==="_"}function Z(){}Z.prototype={tokenize:function(e){var t=[];this._current=0;var r;var i;var n;while(this._current<e.length){if(J(e[this._current])){r=this._current;i=this._consumeUnquotedIdentifier(e);t.push({type:d,value:i,start:r})}else if(W[e[this._current]]!==undefined){t.push({type:W[e[this._current]],value:e[this._current],start:this._current});this._current++}else if(Q(e[this._current])){n=this._consumeNumber(e);t.push(n)}else if(e[this._current]==="["){n=this._consumeLBracket(e);t.push(n)}else if(e[this._current]==='"'){r=this._current;i=this._consumeQuotedIdentifier(e);t.push({type:m,value:i,start:r})}else if(e[this._current]==="'"){r=this._current;i=this._consumeRawStringLiteral(e);t.push({type:G,value:i,start:r})}else if(e[this._current]==="`"){r=this._current;var a=this._consumeLiteral(e);t.push({type:G,value:a,start:r})}else if(X[e[this._current]]!==undefined){t.push(this._consumeOperator(e))}else if(Y[e[this._current]]!==undefined){this._current++}else if(e[this._current]==="&"){r=this._current;this._current++;if(e[this._current]==="&"){this._current++;t.push({type:E,value:"&&",start:r})}else{t.push({type:I,value:"&",start:r})}}else if(e[this._current]==="|"){r=this._current;this._current++;if(e[this._current]==="|"){this._current++;t.push({type:C,value:"||",start:r})}else{t.push({type:L,value:"|",start:r})}}else{var o=new Error("Unknown character:"+e[this._current]);o.name="LexerError";throw o}}return t},_consumeUnquotedIdentifier:function(e){var t=this._current;this._current++;while(this._current<e.length&&$(e[this._current])){this._current++}return e.slice(t,this._current)},_consumeQuotedIdentifier:function(e){var t=this._current;this._current++;var r=e.length;while(e[this._current]!=='"'&&this._current<r){var i=this._current;if(e[i]==="\\"&&(e[i+1]==="\\"||e[i+1]==='"')){i+=2}else{i++}this._current=i}this._current++;return JSON.parse(e.slice(t,this._current))},_consumeRawStringLiteral:function(e){var t=this._current;this._current++;var r=e.length;while(e[this._current]!=="'"&&this._current<r){var i=this._current;if(e[i]==="\\"&&(e[i+1]==="\\"||e[i+1]==="'")){i+=2}else{i++}this._current=i}this._current++;var n=e.slice(t+1,this._current-1);return n.replace("\\'","'")},_consumeNumber:function(e){var t=this._current;this._current++;var r=e.length;while(Q(e[this._current])&&this._current<r){this._current++}var i=parseInt(e.slice(t,this._current));return{type:k,value:i,start:t}},_consumeLBracket:function(e){var t=this._current;this._current++;if(e[this._current]==="?"){this._current++;return{type:x,value:"[?",start:t}}else if(e[this._current]==="]"){this._current++;return{type:K,value:"[]",start:t}}else{return{type:H,value:"[",start:t}}},_consumeOperator:function(e){var t=this._current;var r=e[t];this._current++;if(r==="!"){if(e[this._current]==="="){this._current++;return{type:j,value:"!=",start:t}}else{return{type:R,value:"!",start:t}}}else if(r==="<"){if(e[this._current]==="="){this._current++;return{type:z,value:"<=",start:t}}else{return{type:D,value:"<",start:t}}}else if(r===">"){if(e[this._current]==="="){this._current++;return{type:O,value:">=",start:t}}else{return{type:U,value:">",start:t}}}else if(r==="="){if(e[this._current]==="="){this._current++;return{type:M,value:"==",start:t}}}},_consumeLiteral:function(e){this._current++;var t=this._current;var r=e.length;var i;while(e[this._current]!=="`"&&this._current<r){var n=this._current;if(e[n]==="\\"&&(e[n+1]==="\\"||e[n+1]==="`")){n+=2}else{n++}this._current=n}var a=o(e.slice(t,this._current));a=a.replace("\\`","`");if(this._looksLikeJSON(a)){i=JSON.parse(a)}else{i=JSON.parse('"'+a+'"')}this._current++;return i},_looksLikeJSON:function(e){var t='[{"';var r=["true","false","null"];var i="-0123456789";if(e===""){return false}else if(t.indexOf(e[0])>=0){return true}else if(r.indexOf(e)>=0){return true}else if(i.indexOf(e[0])>=0){try{JSON.parse(e);return true}catch(e){return false}}else{return false}}};var ee={};ee[h]=0;ee[d]=0;ee[m]=0;ee[y]=0;ee[v]=0;ee[g]=0;ee[S]=0;ee[k]=0;ee[P]=0;ee[I]=0;ee[L]=1;ee[C]=2;ee[E]=3;ee[M]=5;ee[U]=5;ee[D]=5;ee[O]=5;ee[z]=5;ee[j]=5;ee[K]=9;ee[w]=20;ee[x]=21;ee[N]=40;ee[R]=45;ee[F]=50;ee[H]=55;ee[V]=60;function te(){}te.prototype={parse:function(e){this._loadTokens(e);this.index=0;var t=this.expression(0);if(this._lookahead(0)!==h){var r=this._lookaheadToken(0);var i=new Error("Unexpected token type: "+r.type+", value: "+r.value);i.name="ParserError";throw i}return t},_loadTokens:function(e){var t=new Z;var r=t.tokenize(e);r.push({type:h,value:"",start:e.length});this.tokens=r},expression:function(e){var t=this._lookaheadToken(0);this._advance();var r=this.nud(t);var i=this._lookahead(0);while(e<ee[i]){this._advance();r=this.led(i,r);i=this._lookahead(0)}return r},_lookahead:function(e){return this.tokens[this.index+e].type},_lookaheadToken:function(e){return this.tokens[this.index+e]},_advance:function(){this.index++},nud:function(e){var t;var r;var i;switch(e.type){case G:return{type:"Literal",value:e.value};case d:return{type:"Field",name:e.value};case m:var n={type:"Field",name:e.value};if(this._lookahead(0)===V){throw new Error("Quoted identifier not allowed for function names.")}else{return n}break;case R:r=this.expression(ee.Not);return{type:"NotExpression",children:[r]};case w:t={type:"Identity"};r=null;if(this._lookahead(0)===y){r={type:"Identity"}}else{r=this._parseProjectionRHS(ee.Star)}return{type:"ValueProjection",children:[t,r]};case x:return this.led(e.type,{type:"Identity"});case F:return this._parseMultiselectHash();case K:t={type:K,children:[{type:"Identity"}]};r=this._parseProjectionRHS(ee.Flatten);return{type:"Projection",children:[t,r]};case H:if(this._lookahead(0)===k||this._lookahead(0)===b){r=this._parseIndexExpression();return this._projectIfSlice({type:"Identity"},r)}else if(this._lookahead(0)===w&&this._lookahead(1)===y){this._advance();this._advance();r=this._parseProjectionRHS(ee.Star);return{type:"Projection",children:[{type:"Identity"},r]}}else{return this._parseMultiselectList()}break;case P:return{type:P};case I:i=this.expression(ee.Expref);return{type:"ExpressionReference",children:[i]};case V:var a=[];while(this._lookahead(0)!==v){if(this._lookahead(0)===P){i={type:P};this._advance()}else{i=this.expression(0)}a.push(i)}this._match(v);return a[0];default:this._errorToken(e)}},led:function(e,t){var r;switch(e){case N:var i=ee.Dot;if(this._lookahead(0)!==w){r=this._parseDotRHS(i);return{type:"Subexpression",children:[t,r]}}else{this._advance();r=this._parseProjectionRHS(i);return{type:"ValueProjection",children:[t,r]}}break;case L:r=this.expression(ee.Pipe);return{type:L,children:[t,r]};case C:r=this.expression(ee.Or);return{type:"OrExpression",children:[t,r]};case E:r=this.expression(ee.And);return{type:"AndExpression",children:[t,r]};case V:var n=t.name;var a=[];var o,s;while(this._lookahead(0)!==v){if(this._lookahead(0)===P){o={type:P};this._advance()}else{o=this.expression(0)}if(this._lookahead(0)===g){this._match(g)}a.push(o)}this._match(v);s={type:"Function",name:n,children:a};return s;case x:var u=this.expression(0);this._match(y);if(this._lookahead(0)===K){r={type:"Identity"}}else{r=this._parseProjectionRHS(ee.Filter)}return{type:"FilterProjection",children:[t,r,u]};case K:var c={type:K,children:[t]};var l=this._parseProjectionRHS(ee.Flatten);return{type:"Projection",children:[c,l]};case M:case j:case U:case O:case D:case z:return this._parseComparator(t,e);case H:var f=this._lookaheadToken(0);if(f.type===k||f.type===b){r=this._parseIndexExpression();return this._projectIfSlice(t,r)}else{this._match(w);this._match(y);r=this._parseProjectionRHS(ee.Star);return{type:"Projection",children:[t,r]}}break;default:this._errorToken(this._lookaheadToken(0))}},_match:function(e){if(this._lookahead(0)===e){this._advance()}else{var t=this._lookaheadToken(0);var r=new Error("Expected "+e+", got: "+t.type);r.name="ParserError";throw r}},_errorToken:function(e){var t=new Error("Invalid token ("+e.type+'): "'+e.value+'"');t.name="ParserError";throw t},_parseIndexExpression:function(){if(this._lookahead(0)===b||this._lookahead(1)===b){return this._parseSliceExpression()}else{var e={type:"Index",value:this._lookaheadToken(0).value};this._advance();this._match(y);return e}},_projectIfSlice:function(e,t){var r={type:"IndexExpression",children:[e,t]};if(t.type==="Slice"){return{type:"Projection",children:[r,this._parseProjectionRHS(ee.Star)]}}else{return r}},_parseSliceExpression:function(){var e=[null,null,null];var t=0;var r=this._lookahead(0);while(r!==y&&t<3){if(r===b){t++;this._advance()}else if(r===k){e[t]=this._lookaheadToken(0).value;this._advance()}else{var i=this._lookahead(0);var n=new Error("Syntax error, unexpected token: "+i.value+"("+i.type+")");n.name="Parsererror";throw n}r=this._lookahead(0)}this._match(y);return{type:"Slice",children:e}},_parseComparator:function(e,t){var r=this.expression(ee[t]);return{type:"Comparator",name:t,children:[e,r]}},_parseDotRHS:function(e){var t=this._lookahead(0);var r=[d,m,w];if(r.indexOf(t)>=0){return this.expression(e)}else if(t===H){this._match(H);return this._parseMultiselectList()}else if(t===F){this._match(F);return this._parseMultiselectHash()}},_parseProjectionRHS:function(e){var t;if(ee[this._lookahead(0)]<10){t={type:"Identity"}}else if(this._lookahead(0)===H){t=this.expression(e)}else if(this._lookahead(0)===x){t=this.expression(e)}else if(this._lookahead(0)===N){this._match(N);t=this._parseDotRHS(e)}else{var r=this._lookaheadToken(0);var i=new Error("Sytanx error, unexpected token: "+r.value+"("+r.type+")");i.name="ParserError";throw i}return t},_parseMultiselectList:function(){var e=[];while(this._lookahead(0)!==y){var t=this.expression(0);e.push(t);if(this._lookahead(0)===g){this._match(g);if(this._lookahead(0)===y){throw new Error("Unexpected token Rbracket")}}}this._match(y);return{type:"MultiSelectList",children:e}},_parseMultiselectHash:function(){var e=[];var t=[d,m];var r,i,n,a;for(;;){r=this._lookaheadToken(0);if(t.indexOf(r.type)<0){throw new Error("Expecting an identifier token, got: "+r.type)}i=r.value;this._advance();this._match(b);n=this.expression(0);a={type:"KeyValuePair",name:i,value:n};e.push(a);if(this._lookahead(0)===g){this._match(g)}else if(this._lookahead(0)===S){this._match(S);break}}return{type:"MultiSelectHash",children:e}}};function re(e){this.runtime=e}re.prototype={search:function(e,t){return this.visit(e,t)},visit:function(e,t){var r,i,n,a,o,s,u,c,l,f;switch(e.type){case"Field":if(t===null){return null}else if(_(t)){s=t[e.name];if(s===undefined){return null}else{return s}}else{return null}break;case"Subexpression":n=this.visit(e.children[0],t);for(f=1;f<e.children.length;f++){n=this.visit(e.children[1],n);if(n===null){return null}}return n;case"IndexExpression":u=this.visit(e.children[0],t);c=this.visit(e.children[1],u);return c;case"Index":if(!q(t)){return null}var p=e.value;if(p<0){p=t.length+p}n=t[p];if(n===undefined){n=null}return n;case"Slice":if(!q(t)){return null}var h=e.children.slice(0);var d=this.computeSliceParams(t.length,h);var m=d[0];var y=d[1];var v=d[2];n=[];if(v>0){for(f=m;f<y;f+=v){n.push(t[f])}}else{for(f=m;f>y;f+=v){n.push(t[f])}}return n;case"Projection":var g=this.visit(e.children[0],t);if(!q(g)){return null}l=[];for(f=0;f<g.length;f++){i=this.visit(e.children[1],g[f]);if(i!==null){l.push(i)}}return l;case"ValueProjection":g=this.visit(e.children[0],t);if(!_(g)){return null}l=[];var b=A(g);for(f=0;f<b.length;f++){i=this.visit(e.children[1],b[f]);if(i!==null){l.push(i)}}return l;case"FilterProjection":g=this.visit(e.children[0],t);if(!q(g)){return null}var S=[];var k=[];for(f=0;f<g.length;f++){r=this.visit(e.children[2],g[f]);if(!T(r)){S.push(g[f])}}for(var C=0;C<S.length;C++){i=this.visit(e.children[1],S[C]);if(i!==null){k.push(i)}}return k;case"Comparator":a=this.visit(e.children[0],t);o=this.visit(e.children[1],t);switch(e.name){case M:n=B(a,o);break;case j:n=!B(a,o);break;case U:n=a>o;break;case O:n=a>=o;break;case D:n=a<o;break;case z:n=a<=o;break;default:throw new Error("Unknown comparator: "+e.name)}return n;case K:var E=this.visit(e.children[0],t);if(!q(E)){return null}var w=[];for(f=0;f<E.length;f++){i=E[f];if(q(i)){w.push.apply(w,i)}else{w.push(i)}}return w;case"Identity":return t;case"MultiSelectList":if(t===null){return null}l=[];for(f=0;f<e.children.length;f++){l.push(this.visit(e.children[f],t))}return l;case"MultiSelectHash":if(t===null){return null}l={};var x;for(f=0;f<e.children.length;f++){x=e.children[f];l[x.name]=this.visit(x.value,t)}return l;case"OrExpression":r=this.visit(e.children[0],t);if(T(r)){r=this.visit(e.children[1],t)}return r;case"AndExpression":a=this.visit(e.children[0],t);if(T(a)===true){return a}return this.visit(e.children[1],t);case"NotExpression":a=this.visit(e.children[0],t);return T(a);case"Literal":return e.value;case L:u=this.visit(e.children[0],t);return this.visit(e.children[1],u);case P:return t;case"Function":var N=[];for(f=0;f<e.children.length;f++){N.push(this.visit(e.children[f],t))}return this.runtime.callFunction(e.name,N);case"ExpressionReference":var R=e.children[0];R.jmespathType=I;return R;default:throw new Error("Unknown node type: "+e.type)}},computeSliceParams:function(e,t){var r=t[0];var i=t[1];var n=t[2];var a=[null,null,null];if(n===null){n=1}else if(n===0){var o=new Error("Invalid slice, step cannot be 0");o.name="RuntimeError";throw o}var s=n<0?true:false;if(r===null){r=s?e-1:0}else{r=this.capSliceRange(e,r,n)}if(i===null){i=s?-1:e}else{i=this.capSliceRange(e,i,n)}a[0]=r;a[1]=i;a[2]=n;return a},capSliceRange:function(e,t,r){if(t<0){t+=e;if(t<0){t=r<0?-1:0}}else if(t>=e){t=r<0?e-1:e}return t}};function ie(e){this._interpreter=e;this.functionTable={abs:{_func:this._functionAbs,_signature:[{types:[c]}]},avg:{_func:this._functionAvg,_signature:[{types:[f]}]},ceil:{_func:this._functionCeil,_signature:[{types:[c]}]},contains:{_func:this._functionContains,_signature:[{types:[l,s]},{types:[a]}]},ends_with:{_func:this._functionEndsWith,_signature:[{types:[l]},{types:[l]}]},floor:{_func:this._functionFloor,_signature:[{types:[c]}]},length:{_func:this._functionLength,_signature:[{types:[l,s,r]}]},map:{_func:this._functionMap,_signature:[{types:[n]},{types:[s]}]},max:{_func:this._functionMax,_signature:[{types:[f,p]}]},merge:{_func:this._functionMerge,_signature:[{types:[r],variadic:true}]},max_by:{_func:this._functionMaxBy,_signature:[{types:[s]},{types:[n]}]},sum:{_func:this._functionSum,_signature:[{types:[f]}]},starts_with:{_func:this._functionStartsWith,_signature:[{types:[l]},{types:[l]}]},min:{_func:this._functionMin,_signature:[{types:[f,p]}]},min_by:{_func:this._functionMinBy,_signature:[{types:[s]},{types:[n]}]},type:{_func:this._functionType,_signature:[{types:[a]}]},keys:{_func:this._functionKeys,_signature:[{types:[r]}]},values:{_func:this._functionValues,_signature:[{types:[r]}]},sort:{_func:this._functionSort,_signature:[{types:[p,f]}]},sort_by:{_func:this._functionSortBy,_signature:[{types:[s]},{types:[n]}]},join:{_func:this._functionJoin,_signature:[{types:[l]},{types:[p]}]},reverse:{_func:this._functionReverse,_signature:[{types:[l,s]}]},to_array:{_func:this._functionToArray,_signature:[{types:[a]}]},to_string:{_func:this._functionToString,_signature:[{types:[a]}]},to_number:{_func:this._functionToNumber,_signature:[{types:[a]}]},not_null:{_func:this._functionNotNull,_signature:[{types:[a],variadic:true}]}}}ie.prototype={callFunction:function(e,t){var r=this.functionTable[e];if(r===undefined){throw new Error("Unknown function: "+e+"()")}this._validateArgs(e,t,r._signature);return r._func.call(this,t)},_validateArgs:function(e,t,r){var i;if(r[r.length-1].variadic){if(t.length<r.length){i=r.length===1?" argument":" arguments";throw new Error("ArgumentError: "+e+"() "+"takes at least"+r.length+i+" but received "+t.length)}}else if(t.length!==r.length){i=r.length===1?" argument":" arguments";throw new Error("ArgumentError: "+e+"() "+"takes "+r.length+i+" but received "+t.length)}var n;var a;var o;for(var s=0;s<r.length;s++){o=false;n=r[s].types;a=this._getTypeName(t[s]);for(var u=0;u<n.length;u++){if(this._typeMatches(a,n[u],t[s])){o=true;break}}if(!o){throw new Error("TypeError: "+e+"() "+"expected argument "+(s+1)+" to be type "+n+" but received type "+a+" instead.")}}},_typeMatches:function(e,t,r){if(t===a){return true}if(t===p||t===f||t===s){if(t===s){return e===s}else if(e===s){var i;if(t===f){i=c}else if(t===p){i=l}for(var n=0;n<r.length;n++){if(!this._typeMatches(this._getTypeName(r[n]),i,r[n])){return false}}return true}}else{return e===t}},_getTypeName:function(e){switch(Object.prototype.toString.call(e)){case"[object String]":return l;case"[object Number]":return c;case"[object Array]":return s;case"[object Boolean]":return i;case"[object Null]":return u;case"[object Object]":if(e.jmespathType===I){return n}else{return r}}},_functionStartsWith:function(e){return e[0].lastIndexOf(e[1])===0},_functionEndsWith:function(e){var t=e[0];var r=e[1];return t.indexOf(r,t.length-r.length)!==-1},_functionReverse:function(e){var t=this._getTypeName(e[0]);if(t===l){var r=e[0];var i="";for(var n=r.length-1;n>=0;n--){i+=r[n]}return i}else{var a=e[0].slice(0);a.reverse();return a}},_functionAbs:function(e){return Math.abs(e[0])},_functionCeil:function(e){return Math.ceil(e[0])},_functionAvg:function(e){var t=0;var r=e[0];for(var i=0;i<r.length;i++){t+=r[i]}return t/r.length},_functionContains:function(e){return e[0].indexOf(e[1])>=0},_functionFloor:function(e){return Math.floor(e[0])},_functionLength:function(e){if(!_(e[0])){return e[0].length}else{return Object.keys(e[0]).length}},_functionMap:function(e){var t=[];var r=this._interpreter;var i=e[0];var n=e[1];for(var a=0;a<n.length;a++){t.push(r.visit(i,n[a]))}return t},_functionMerge:function(e){var t={};for(var r=0;r<e.length;r++){var i=e[r];for(var n in i){t[n]=i[n]}}return t},_functionMax:function(e){if(e[0].length>0){var t=this._getTypeName(e[0][0]);if(t===c){return Math.max.apply(Math,e[0])}else{var r=e[0];var i=r[0];for(var n=1;n<r.length;n++){if(i.localeCompare(r[n])<0){i=r[n]}}return i}}else{return null}},_functionMin:function(e){if(e[0].length>0){var t=this._getTypeName(e[0][0]);if(t===c){return Math.min.apply(Math,e[0])}else{var r=e[0];var i=r[0];for(var n=1;n<r.length;n++){if(r[n].localeCompare(i)<0){i=r[n]}}return i}}else{return null}},_functionSum:function(e){var t=0;var r=e[0];for(var i=0;i<r.length;i++){t+=r[i]}return t},_functionType:function(e){switch(this._getTypeName(e[0])){case c:return"number";case l:return"string";case s:return"array";case r:return"object";case i:return"boolean";case n:return"expref";case u:return"null"}},_functionKeys:function(e){return Object.keys(e[0])},_functionValues:function(e){var t=e[0];var r=Object.keys(t);var i=[];for(var n=0;n<r.length;n++){i.push(t[r[n]])}return i},_functionJoin:function(e){var t=e[0];var r=e[1];return r.join(t)},_functionToArray:function(e){if(this._getTypeName(e[0])===s){return e[0]}else{return[e[0]]}},_functionToString:function(e){if(this._getTypeName(e[0])===l){return e[0]}else{return JSON.stringify(e[0])}},_functionToNumber:function(e){var t=this._getTypeName(e[0]);var r;if(t===c){return e[0]}else if(t===l){r=+e[0];if(!isNaN(r)){return r}}return null},_functionNotNull:function(e){for(var t=0;t<e.length;t++){if(this._getTypeName(e[t])!==u){return e[t]}}return null},_functionSort:function(e){var t=e[0].slice(0);t.sort();return t},_functionSortBy:function(e){var t=e[0].slice(0);if(t.length===0){return t}var n=this._interpreter;var a=e[1];var o=this._getTypeName(n.visit(a,t[0]));if([c,l].indexOf(o)<0){throw new Error("TypeError")}var s=this;var r=[];for(var i=0;i<t.length;i++){r.push([i,t[i]])}r.sort(function(e,t){var r=n.visit(a,e[1]);var i=n.visit(a,t[1]);if(s._getTypeName(r)!==o){throw new Error("TypeError: expected "+o+", received "+s._getTypeName(r))}else if(s._getTypeName(i)!==o){throw new Error("TypeError: expected "+o+", received "+s._getTypeName(i))}if(r>i){return 1}else if(r<i){return-1}else{return e[0]-t[0]}});for(var u=0;u<r.length;u++){t[u]=r[u][1]}return t},_functionMaxBy:function(e){var t=e[1];var r=e[0];var i=this.createKeyFunction(t,[c,l]);var n=-Infinity;var a;var o;for(var s=0;s<r.length;s++){o=i(r[s]);if(o>n){n=o;a=r[s]}}return a},_functionMinBy:function(e){var t=e[1];var r=e[0];var i=this.createKeyFunction(t,[c,l]);var n=Infinity;var a;var o;for(var s=0;s<r.length;s++){o=i(r[s]);if(o<n){n=o;a=r[s]}}return a},createKeyFunction:function(i,n){var a=this;var o=this._interpreter;var e=function(e){var t=o.visit(i,e);if(n.indexOf(a._getTypeName(t))<0){var r="TypeError: expected one of "+n+", received "+a._getTypeName(t);throw new Error(r)}return t};return e}};function ne(e){var t=new te;var r=t.parse(e);return r}function ae(e){var t=new Z;return t.tokenize(e)}function oe(e,t){var r=new te;var i=new ie;var n=new re(i);i._interpreter=n;var a=r.parse(t);return n.search(a,e)}e.tokenize=ae;e.compile=ne;e.search=oe;e.strictDeepEqual=B})(typeof r==="undefined"?this.jmespath={}:r)},{}],6:[function(e,t,r){if(typeof Object.create==="function"){t.exports=function e(t,r){t.super_=r;t.prototype=Object.create(r.prototype,{constructor:{value:t,enumerable:false,writable:true,configurable:true}})}}else{t.exports=function e(t,r){t.super_=r;var i=function(){};i.prototype=r.prototype;t.prototype=new i;t.prototype.constructor=t}}},{}],2:[function(e,t,r){},{}]},{},[]);_xamzrequire=function a(o,s,u){function c(r,e){if(!s[r]){if(!o[r]){var t=typeof _xamzrequire=="function"&&_xamzrequire;if(!e&&t)return t(r,!0);if(l)return l(r,!0);var i=new Error("Cannot find module '"+r+"'");throw i.code="MODULE_NOT_FOUND",i}var n=s[r]={exports:{}};o[r][0].call(n.exports,function(e){var t=o[r][1][e];return c(t?t:e)},n,n.exports,a,o,s,u)}return s[r].exports}var l=typeof _xamzrequire=="function"&&_xamzrequire;for(var e=0;e<u.length;e++)c(u[e]);return c}({28:[function(e,t,r){e("./browser_loader");var i=e("./core");if(typeof window!=="undefined")window.OOS=i;if(typeof t!=="undefined"){t.exports=i}if(typeof self!=="undefined")self.OOS=i},{"./browser_loader":35,"./core":38}],35:[function(i,n,e){(function(e){var t=i("./util");t.crypto.lib=i("./browserCryptoLib");t.Buffer=i("buffer/").Buffer;t.url=i("url/");t.querystring=i("querystring/");t.environment="js";t.createEventStream=i("./event-stream/buffered-create-event-stream").createEventStream;var r=i("./core");n.exports=r;i("./credentials");i("./credentials/credential_provider_chain");i("./credentials/temporary_credentials");i("./credentials/web_identity_credentials");i("./credentials/cognito_identity_credentials");i("./credentials/saml_credentials");r.XML.Parser=i("./xml/browser_parser");i("./http/xhr");if(typeof e==="undefined"){e={browser:true}}}).call(this,i("_process"))},{"./browserCryptoLib":29,"./core":38,"./credentials":39,"./credentials/cognito_identity_credentials":40,"./credentials/credential_provider_chain":41,"./credentials/saml_credentials":42,"./credentials/temporary_credentials":43,"./credentials/web_identity_credentials":44,"./event-stream/buffered-create-event-stream":51,"./http/xhr":60,"./util":112,"./xml/browser_parser":113,_process:9,"buffer/":3,"querystring/":16,"url/":18}],113:[function(e,t,r){var c=e("../util");var n=e("../model/shape");function i(){}i.prototype.parse=function(e,t){if(e.replace(/^\s+/,"")==="")return{};var r,i;try{if(window.DOMParser){try{var n=new DOMParser;r=n.parseFromString(e,"text/xml")}catch(e){throw c.error(new Error("Parse error in document"),{originalError:e,code:"XMLParserError",retryable:true})}if(r.documentElement===null){throw c.error(new Error("Cannot parse empty document."),{code:"XMLParserError",retryable:true})}var a=r.getElementsByTagName("parsererror")[0];if(a&&(a.parentNode===r||a.parentNode.nodeName==="body"||a.parentNode.parentNode===r||a.parentNode.parentNode.nodeName==="body")){var o=a.getElementsByTagName("div")[0]||a;throw c.error(new Error(o.textContent||"Parser error in document"),{code:"XMLParserError",retryable:true})}}else if(window.ActiveXObject){r=new window.ActiveXObject("Microsoft.XMLDOM");r.async=false;if(!r.loadXML(e)){throw c.error(new Error("Parse error in document"),{code:"XMLParserError",retryable:true})}}else{throw new Error("Cannot load XML parser")}}catch(e){i=e}if(r&&r.documentElement&&!i){var s=f(r.documentElement,t);var u=l(r.documentElement,"ResponseMetadata");if(u){s.ResponseMetadata=f(u,{})}return s}else if(i){throw c.error(i||new Error,{code:"XMLParserError",retryable:true})}else{return{}}};function l(e,t){var r=e.getElementsByTagName(t);for(var i=0,n=r.length;i<n;i++){if(r[i].parentNode===e){return r[i]}}}function f(e,t){if(!t)t={};switch(t.type){case"structure":return a(e,t);case"map":return o(e,t);case"list":return s(e,t);case undefined:case null:return p(e);default:return u(e,t)}}function a(n,e){var a={};if(n===null)return a;c.each(e.members,function(e,t){if(t.isXmlAttribute){if(Object.prototype.hasOwnProperty.call(n.attributes,t.name)){var r=n.attributes[t.name].value;a[e]=f({textContent:r},t)}}else{var i=t.flattened?n:l(n,t.name);if(i){a[e]=f(i,t)}else if(!t.flattened&&t.type==="list"){a[e]=t.defaultValue}}});return a}function o(e,t){var r={};var i=t.key.name||"key";var n=t.value.name||"value";var a=t.flattened?t.name:"entry";var o=e.firstElementChild;while(o){if(o.nodeName===a){var s=l(o,i).textContent;var u=l(o,n);r[s]=f(u,t.value)}o=o.nextElementSibling}return r}function s(e,t){var r=[];var i=t.flattened?t.name:t.member.name||"member";var n=e.firstElementChild;while(n){if(n.nodeName===i){r.push(f(n,t.member))}n=n.nextElementSibling}return r}function u(e,t){if(e.getAttribute){var r=e.getAttribute("encoding");if(r==="base64"){t=new n.create({type:r})}}var i=e.textContent;if(i==="")i=null;if(typeof t.toType==="function"){return t.toType(i)}else{return i}}function p(e){if(e===undefined||e===null)return"";if(!e.firstElementChild){if(e.parentNode.parentNode===null)return{};if(e.childNodes.length===0)return"";else return e.textContent}var t={type:"structure",members:{}};var r=e.firstElementChild;while(r){var i=r.nodeName;if(Object.prototype.hasOwnProperty.call(t.members,i)){t.members[i].type="list"}else{t.members[i]={name:i}}r=r.nextElementSibling}return a(e,t)}t.exports=i},{"../model/shape":68,"../util":112}],60:[function(e,t,r){var f=e("../core");var p=e("events").EventEmitter;e("../http");f.XHRClient=f.util.inherit({handleRequest:function e(t,r,i,n){var a=this;var o=t.endpoint;var s=new p;var u=o.protocol+"//"+o.hostname;if(o.port!==80&&o.port!==443){u+=":"+o.port}u+=t.path;var c=new XMLHttpRequest,l=false;t.stream=c;c.addEventListener("readystatechange",function(){try{if(c.status===0)return}catch(e){return}if(this.readyState>=this.HEADERS_RECEIVED&&!l){s.statusCode=c.status;s.headers=a.parseHeaders(c.getAllResponseHeaders());s.emit("headers",s.statusCode,s.headers,c.statusText);l=true}if(this.readyState===this.DONE){a.finishRequest(c,s)}},false);c.upload.addEventListener("progress",function(e){s.emit("sendProgress",e)});c.addEventListener("progress",function(e){s.emit("receiveProgress",e)},false);c.addEventListener("timeout",function(){n(f.util.error(new Error("Timeout"),{code:"TimeoutError"}))},false);c.addEventListener("error",function(){n(f.util.error(new Error("Network Failure"),{code:"NetworkingError"}))},false);c.addEventListener("abort",function(){n(f.util.error(new Error("Request aborted"),{code:"RequestAbortedError"}))},false);i(s);c.open(t.method,u,r.xhrAsync!==false);f.util.each(t.headers,function(e,t){if(e!=="Content-Length"&&e!=="User-Agent"&&e!=="Host"){c.setRequestHeader(e,t)}});if(r.timeout&&r.xhrAsync!==false){c.timeout=r.timeout}if(r.xhrWithCredentials){c.withCredentials=true}try{c.responseType="arraybuffer"}catch(e){}try{if(t.body){c.send(t.body)}else{c.send()}}catch(e){if(t.body&&typeof t.body.buffer==="object"){c.send(t.body.buffer)}else{throw e}}return s},parseHeaders:function e(t){var i={};f.util.arrayEach(t.split(/\r?\n/),function(e){var t=e.split(":",1)[0];var r=e.substring(t.length+2);if(t.length>0)i[t.toLowerCase()]=r});return i},finishRequest:function e(t,r){var i;if(t.responseType==="arraybuffer"&&t.response){var n=t.response;i=new f.util.Buffer(n.byteLength);var a=new Uint8Array(n);for(var o=0;o<i.length;++o){i[o]=a[o]}}try{if(!i&&typeof t.responseText==="string"){i=new f.util.Buffer(t.responseText)}}catch(e){}if(i)r.emit("data",i);r.emit("end")}});f.HttpClient.prototype=f.XHRClient.prototype;f.HttpClient.streamsApiVersion=1},{"../core":38,"../http":59,events:4}],51:[function(e,t,r){var o=e("../event-stream/event-message-chunker").eventMessageChunker;var s=e("./parse-event").parseEvent;function i(e,t,r){var i=o(e);var n=[];for(var a=0;a<i.length;a++){n.push(s(t,i[a],r))}return n}t.exports={createEventStream:i}},{"../event-stream/event-message-chunker":52,"./parse-event":54}],54:[function(e,t,r){var d=e("./parse-message").parseMessage;function i(e,t,r){var i=d(t);var n=i.headers[":message-type"];if(n){if(n.value==="error"){throw m(i)}else if(n.value!=="event"){return}}var a=i.headers[":event-type"];var o=r.members[a.value];if(!o){return}var s={};var u=o.eventPayloadMemberName;if(u){var c=o.members[u];if(c.type==="binary"){s[u]=i.body}else{s[u]=e.parse(i.body.toString(),c)}}var l=o.eventHeaderMemberNames;for(var f=0;f<l.length;f++){var p=l[f];if(i.headers[p]){s[p]=o.members[p].toType(i.headers[p].value)}}var h={};h[a.value]=s;return h}function m(e){var t=e.headers[":error-code"];var r=e.headers[":error-message"];var i=new Error(r.value||r);i.code=i.name=t.value||t;return i}t.exports={parseEvent:i}},{"./parse-message":55}],55:[function(e,t,r){var u=e("./int64").Int64;var i=e("./split-message").splitMessage;var c="boolean";var l="byte";var f="short";var p="integer";var h="long";var d="binary";var m="string";var y="timestamp";var v="uuid";function n(e){var t={};var r=0;while(r<e.length){var i=e.readUInt8(r++);var n=e.slice(r,r+i).toString();r+=i;switch(e.readUInt8(r++)){case 0:t[n]={type:c,value:true};break;case 1:t[n]={type:c,value:false};break;case 2:t[n]={type:l,value:e.readInt8(r++)};break;case 3:t[n]={type:f,value:e.readInt16BE(r)};r+=2;break;case 4:t[n]={type:p,value:e.readInt32BE(r)};r+=4;break;case 5:t[n]={type:h,value:new u(e.slice(r,r+8))};r+=8;break;case 6:var a=e.readUInt16BE(r);r+=2;t[n]={type:d,value:e.slice(r,r+a)};r+=a;break;case 7:var o=e.readUInt16BE(r);r+=2;t[n]={type:m,value:e.slice(r,r+o).toString()};r+=o;break;case 8:t[n]={type:y,value:new Date(new u(e.slice(r,r+8)).valueOf())};r+=8;break;case 9:var s=e.slice(r,r+16).toString("hex");r+=16;t[n]={type:v,value:s.substr(0,8)+"-"+s.substr(8,4)+"-"+s.substr(12,4)+"-"+s.substr(16,4)+"-"+s.substr(20)};break;default:throw new Error("Unrecognized header type tag")}}return t}function a(e){var t=i(e);return{headers:n(t.headers),body:t.body}}t.exports={parseMessage:a}},{"./int64":53,"./split-message":56}],56:[function(e,t,r){var a=e("../core").util;var o=e("./to-buffer").toBuffer;var s=4;var u=s*2;var c=4;var l=u+c*2;function i(e){if(!a.Buffer.isBuffer(e))e=o(e);if(e.length<l){throw new Error("Provided message too short to accommodate event stream message overhead")}if(e.length!==e.readUInt32BE(0)){throw new Error("Reported message length does not match received message length")}var t=e.readUInt32BE(u);if(t!==a.crypto.crc32(e.slice(0,u))){throw new Error("The prelude checksum specified in the message ("+t+") does not match the calculated CRC32 checksum.")}var r=e.readUInt32BE(e.length-c);if(r!==a.crypto.crc32(e.slice(0,e.length-c))){throw new Error("The message checksum did not match the expected value of "+r)}var i=u+c;var n=i+e.readUInt32BE(s);return{headers:e.slice(i,n),body:e.slice(n,e.length-c)}}t.exports={splitMessage:i}},{"../core":38,"./to-buffer":57}],53:[function(e,t,r){var i=e("../core").util;var n=e("./to-buffer").toBuffer;function a(e){if(e.length!==8){throw new Error("Int64 buffers must be exactly 8 bytes")}if(!i.Buffer.isBuffer(e))e=n(e);this.bytes=e}a.fromNumber=function(e){if(e>0x8000000000000000||e<-0x8000000000000000){throw new Error(e+" is too large (or, if negative, too small) to represent as an Int64")}var t=new Uint8Array(8);for(var r=7,i=Math.abs(Math.round(e));r>-1&&i>0;r--,i/=256){t[r]=i}if(e<0){o(t)}return new a(t)};a.prototype.valueOf=function(){var e=this.bytes.slice(0);var t=e[0]&128;if(t){o(e)}return parseInt(e.toString("hex"),16)*(t?-1:1)};a.prototype.toString=function(){return String(this.valueOf())};function o(e){for(var t=0;t<8;t++){e[t]^=255}for(var t=7;t>-1;t--){e[t]++;if(e[t]!==0){break}}}t.exports={Int64:a}},{"../core":38,"./to-buffer":57}],57:[function(e,t,r){var i=e("../core").util.Buffer;function n(e,t){return typeof i.from==="function"&&i.from!==Uint8Array.from?i.from(e,t):new i(e,t)}t.exports={toBuffer:n}},{"../core":38}],52:[function(e,t,r){function i(e){var t=[];var r=0;while(r<e.length){var i=e.readInt32BE(r);var n=e.slice(r,i+r);r+=i;t.push(n)}return t}t.exports={eventMessageChunker:i}},{}],44:[function(e,t,r){var i=e("../core");i.WebIdentityCredentials=i.util.inherit(i.Credentials,{constructor:function e(t,r){i.Credentials.call(this);this.expired=true;this.params=t;this.params.RoleSessionName=this.params.RoleSessionName||"web-identity";this.data=null;this._clientConfig=i.util.copy(r||{})},refresh:function e(r){var i=this;i.createClients();if(!r)r=function(e){if(e)throw e};i.service.assumeRoleWithWebIdentity(function(e,t){i.data=null;if(!e){i.data=t;i.service.credentialsFrom(t,i)}r(e)})},createClients:function(){if(!this.service){var e=i.util.merge({},this._clientConfig);e.params=this.params;this.service=new i.STS(e)}}})},{"../core":38}],43:[function(e,t,r){var i=e("../core");i.TemporaryCredentials=i.util.inherit(i.Credentials,{constructor:function e(t,r){i.Credentials.call(this);this.loadMasterCredentials(r);this.expired=true;this.params=t||{};if(this.params.RoleArn){this.params.RoleSessionName=this.params.RoleSessionName||"temporary-credentials"}},refresh:function e(r){var i=this;i.createClients();if(!r)r=function(e){if(e)throw e};i.masterCredentials.get(function(){i.service.config.credentials=i.masterCredentials;var e=i.params.RoleArn?i.service.assumeRole:i.service.getSessionToken;e.call(i.service,function(e,t){if(!e){i.service.credentialsFrom(t,i)}r(e)})})},loadMasterCredentials:function e(t){this.masterCredentials=t||i.config.credentials;while(this.masterCredentials.masterCredentials){this.masterCredentials=this.masterCredentials.masterCredentials}if(typeof this.masterCredentials.get!=="function"){this.masterCredentials=new i.Credentials(this.masterCredentials)}},createClients:function(){this.service=this.service||new i.STS({params:this.params})}})},{"../core":38}],42:[function(e,t,r){var i=e("../core");i.SAMLCredentials=i.util.inherit(i.Credentials,{constructor:function e(t){i.Credentials.call(this);this.expired=true;this.params=t},refresh:function e(r){var i=this;i.createClients();if(!r)r=function(e){if(e)throw e};i.service.assumeRoleWithSAML(function(e,t){if(!e){i.service.credentialsFrom(t,i)}r(e)})},createClients:function(){this.service=this.service||new i.STS({params:this.params})}})},{"../core":38}],40:[function(e,t,r){var o=e("../core");o.CognitoIdentityCredentials=o.util.inherit(o.Credentials,{localStorageKey:{id:"aws.cognito.identity-id.",providers:"aws.cognito.identity-providers."},constructor:function e(t,r){o.Credentials.call(this);this.expired=true;this.params=t;this.data=null;this._identityId=null;this._clientConfig=o.util.copy(r||{});this.loadCachedId();var i=this;Object.defineProperty(this,"identityId",{get:function(){i.loadCachedId();return i._identityId||i.params.IdentityId},set:function(e){i._identityId=e}})},refresh:function e(t){var r=this;r.createClients();r.data=null;r._identityId=null;r.getId(function(e){if(!e){if(!r.params.RoleArn){r.getCredentialsForIdentity(t)}else{r.getCredentialsFromSTS(t)}}else{r.clearIdOnNotAuthorized(e);t(e)}})},clearCachedId:function e(){this._identityId=null;delete this.params.IdentityId;var t=this.params.IdentityPoolId;var r=this.params.LoginId||"";delete this.storage[this.localStorageKey.id+t+r];delete this.storage[this.localStorageKey.providers+t+r]},clearIdOnNotAuthorized:function e(t){var r=this;if(t.code=="NotAuthorizedException"){r.clearCachedId()}},getId:function e(r){var i=this;if(typeof i.params.IdentityId==="string"){return r(null,i.params.IdentityId)}i.cognito.getId(function(e,t){if(!e&&t.IdentityId){i.params.IdentityId=t.IdentityId;r(null,t.IdentityId)}else{r(e)}})},loadCredentials:function e(t,r){if(!t||!r)return;r.expired=false;r.accessKeyId=t.Credentials.AccessKeyId;r.secretAccessKey=t.Credentials.SecretKey;r.sessionToken=t.Credentials.SessionToken;r.expireTime=t.Credentials.Expiration},getCredentialsForIdentity:function e(r){var i=this;i.cognito.getCredentialsForIdentity(function(e,t){if(!e){i.cacheId(t);i.data=t;i.loadCredentials(i.data,i)}else{i.clearIdOnNotAuthorized(e)}r(e)})},getCredentialsFromSTS:function e(r){var i=this;i.cognito.getOpenIdToken(function(e,t){if(!e){i.cacheId(t);i.params.WebIdentityToken=t.Token;i.webIdentityCredentials.refresh(function(e){if(!e){i.data=i.webIdentityCredentials.data;i.sts.credentialsFrom(i.data,i)}r(e)})}else{i.clearIdOnNotAuthorized(e);r(e)}})},loadCachedId:function e(){var t=this;if(o.util.isBrowser()&&!t.params.IdentityId){var r=t.getStorage("id");if(r&&t.params.Logins){var i=Object.keys(t.params.Logins);var n=(t.getStorage("providers")||"").split(",");var a=n.filter(function(e){return i.indexOf(e)!==-1});if(a.length!==0){t.params.IdentityId=r}}else if(r){t.params.IdentityId=r}}},createClients:function(){var e=this._clientConfig;this.webIdentityCredentials=this.webIdentityCredentials||new o.WebIdentityCredentials(this.params,e);if(!this.cognito){var t=o.util.merge({},e);t.params=this.params;this.cognito=new o.CognitoIdentity(t)}this.sts=this.sts||new o.STS(e)},cacheId:function e(t){this._identityId=t.IdentityId;this.params.IdentityId=this._identityId;if(o.util.isBrowser()){this.setStorage("id",t.IdentityId);if(this.params.Logins){this.setStorage("providers",Object.keys(this.params.Logins).join(","))}}},getStorage:function e(t){return this.storage[this.localStorageKey[t]+this.params.IdentityPoolId+(this.params.LoginId||"")]},setStorage:function e(t,r){try{this.storage[this.localStorageKey[t]+this.params.IdentityPoolId+(this.params.LoginId||"")]=r}catch(e){}},storage:function(){try{var e=o.util.isBrowser()&&window.localStorage!==null&&typeof window.localStorage==="object"?window.localStorage:{};e["aws.test-storage"]="foobar";delete e["aws.test-storage"];return e}catch(e){return{}}}()})},{"../core":38}],29:[function(e,t,r){var i=e("./browserHmac");var n=e("./browserMd5");var a=e("./browserSha1");var o=e("./browserSha256");t.exports=r={createHash:function e(t){t=t.toLowerCase();if(t==="md5"){return new n}else if(t==="sha256"){return new o}else if(t==="sha1"){return new a}throw new Error("Hash algorithm "+t+" is not supported in the browser SDK")},createHmac:function e(t,r){t=t.toLowerCase();if(t==="md5"){return new i(n,r)}else if(t==="sha256"){return new i(o,r)}else if(t==="sha1"){return new i(a,r)}throw new Error("HMAC algorithm "+t+" is not supported in the browser SDK")},createSign:function(){throw new Error("createSign is not implemented in the browser")}}},{"./browserHmac":31,"./browserMd5":32,"./browserSha1":33,"./browserSha256":34}],34:[function(e,t,r){var o=e("buffer/").Buffer;var i=e("./browserHashUtils");var v=64;var s=32;var g=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]);var n=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var a=Math.pow(2,53)-1;function u(){this.state=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];this.temp=new Int32Array(64);this.buffer=new Uint8Array(64);this.bufferLength=0;this.bytesHashed=0;this.finished=false}t.exports=r=u;u.BLOCK_SIZE=v;u.prototype.update=function(e){if(this.finished){throw new Error("Attempted to update an already finished hash.")}if(i.isEmptyData(e)){return this}e=i.convertToBuffer(e);var t=0;var r=e.byteLength;this.bytesHashed+=r;if(this.bytesHashed*8>a){throw new Error("Cannot hash more than 2^53 - 1 bits")}while(r>0){this.buffer[this.bufferLength++]=e[t++];r--;if(this.bufferLength===v){this.hashBuffer();this.bufferLength=0}}return this};u.prototype.digest=function(e){if(!this.finished){var t=this.bytesHashed*8;var r=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength);var i=this.bufferLength;r.setUint8(this.bufferLength++,128);if(i%v>=v-8){for(var n=this.bufferLength;n<v;n++){r.setUint8(n,0)}this.hashBuffer();this.bufferLength=0}for(var n=this.bufferLength;n<v-8;n++){r.setUint8(n,0)}r.setUint32(v-8,Math.floor(t/4294967296),true);r.setUint32(v-4,t);this.hashBuffer();this.finished=true}var a=new o(s);for(var n=0;n<8;n++){a[n*4]=this.state[n]>>>24&255;a[n*4+1]=this.state[n]>>>16&255;a[n*4+2]=this.state[n]>>>8&255;a[n*4+3]=this.state[n]>>>0&255}return e?a.toString(e):a};u.prototype.hashBuffer=function(){var e=this,t=e.buffer,r=e.state;var i=r[0],n=r[1],a=r[2],o=r[3],s=r[4],u=r[5],c=r[6],l=r[7];for(var f=0;f<v;f++){if(f<16){this.temp[f]=(t[f*4]&255)<<24|(t[f*4+1]&255)<<16|(t[f*4+2]&255)<<8|t[f*4+3]&255}else{var p=this.temp[f-2];var h=(p>>>17|p<<15)^(p>>>19|p<<13)^p>>>10;p=this.temp[f-15];var d=(p>>>7|p<<25)^(p>>>18|p<<14)^p>>>3;this.temp[f]=(h+this.temp[f-7]|0)+(d+this.temp[f-16]|0)}var m=(((s>>>6|s<<26)^(s>>>11|s<<21)^(s>>>25|s<<7))+(s&u^~s&c)|0)+(l+(g[f]+this.temp[f]|0)|0)|0;var y=((i>>>2|i<<30)^(i>>>13|i<<19)^(i>>>22|i<<10))+(i&n^i&a^n&a)|0;l=c;c=u;u=s;s=o+m|0;o=a;a=n;n=i;i=m+y|0}r[0]+=i;r[1]+=n;r[2]+=a;r[3]+=o;r[4]+=s;r[5]+=u;r[6]+=c;r[7]+=l}},{"./browserHashUtils":30,"buffer/":3}],33:[function(e,t,r){var n=e("buffer/").Buffer;var i=e("./browserHashUtils");var a=64;var o=20;var s=new Uint32Array([1518500249,1859775393,2400959708|0,3395469782|0]);var u=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var c=Math.pow(2,53)-1;function l(){this.h0=1732584193;this.h1=4023233417;this.h2=2562383102;this.h3=271733878;this.h4=3285377520;this.block=new Uint32Array(80);this.offset=0;this.shift=24;this.totalLength=0}t.exports=r=l;l.BLOCK_SIZE=a;l.prototype.update=function(e){if(this.finished){throw new Error("Attempted to update an already finished hash.")}if(i.isEmptyData(e)){return this}e=i.convertToBuffer(e);var t=e.length;this.totalLength+=t*8;for(var r=0;r<t;r++){this.write(e[r])}return this};l.prototype.write=function e(t){this.block[this.offset]|=(t&255)<<this.shift;if(this.shift){this.shift-=8}else{this.offset++;this.shift=24}if(this.offset===16)this.processBlock()};l.prototype.digest=function(e){this.write(128);if(this.offset>14||this.offset===14&&this.shift<24){this.processBlock()}this.offset=14;this.shift=24;this.write(0);this.write(0);this.write(this.totalLength>0xffffffffff?this.totalLength/1099511627776:0);this.write(this.totalLength>4294967295?this.totalLength/4294967296:0);for(var t=24;t>=0;t-=8){this.write(this.totalLength>>t)}var r=new n(o);var i=new DataView(r.buffer);i.setUint32(0,this.h0,false);i.setUint32(4,this.h1,false);i.setUint32(8,this.h2,false);i.setUint32(12,this.h3,false);i.setUint32(16,this.h4,false);return e?r.toString(e):r};l.prototype.processBlock=function e(){for(var t=16;t<80;t++){var r=this.block[t-3]^this.block[t-8]^this.block[t-14]^this.block[t-16];this.block[t]=r<<1|r>>>31}var i=this.h0;var n=this.h1;var a=this.h2;var o=this.h3;var s=this.h4;var u,c;for(t=0;t<80;t++){if(t<20){u=o^n&(a^o);c=1518500249}else if(t<40){u=n^a^o;c=1859775393}else if(t<60){u=n&a|o&(n|a);c=2400959708}else{u=n^a^o;c=3395469782}var l=(i<<5|i>>>27)+u+s+c+(this.block[t]|0);s=o;o=a;a=n<<30|n>>>2;n=i;i=l}this.h0=this.h0+i|0;this.h1=this.h1+n|0;this.h2=this.h2+a|0;this.h3=this.h3+o|0;this.h4=this.h4+s|0;this.offset=0;for(t=0;t<16;t++){this.block[t]=0}}},{"./browserHashUtils":30,"buffer/":3}],32:[function(e,t,r){var n=e("./browserHashUtils");var c=e("buffer/").Buffer;var l=64;var f=16;var i=[1732584193,4023233417,2562383102,271733878];function a(){this.state=[1732584193,4023233417,2562383102,271733878];this.buffer=new DataView(new ArrayBuffer(l));this.bufferLength=0;this.bytesHashed=0;this.finished=false}t.exports=r=a;a.BLOCK_SIZE=l;a.prototype.update=function(e){if(n.isEmptyData(e)){return this}else if(this.finished){throw new Error("Attempted to update an already finished hash.")}var t=n.convertToBuffer(e);var r=0;var i=t.byteLength;this.bytesHashed+=i;while(i>0){this.buffer.setUint8(this.bufferLength++,t[r++]);i--;if(this.bufferLength===l){this.hashBuffer();this.bufferLength=0}}return this};a.prototype.digest=function(e){if(!this.finished){var t=this,r=t.buffer,i=t.bufferLength,n=t.bytesHashed;var a=n*8;r.setUint8(this.bufferLength++,128);if(i%l>=l-8){for(var o=this.bufferLength;o<l;o++){r.setUint8(o,0)}this.hashBuffer();this.bufferLength=0}for(var o=this.bufferLength;o<l-8;o++){r.setUint8(o,0)}r.setUint32(l-8,a>>>0,true);r.setUint32(l-4,Math.floor(a/4294967296),true);this.hashBuffer();this.finished=true}var s=new DataView(new ArrayBuffer(f));for(var o=0;o<4;o++){s.setUint32(o*4,this.state[o],true)}var u=new c(s.buffer,s.byteOffset,s.byteLength);return e?u.toString(e):u};a.prototype.hashBuffer=function(){var e=this,t=e.buffer,r=e.state;var i=r[0],n=r[1],a=r[2],o=r[3];i=u(i,n,a,o,t.getUint32(0,true),7,3614090360);o=u(o,i,n,a,t.getUint32(4,true),12,3905402710);a=u(a,o,i,n,t.getUint32(8,true),17,606105819);n=u(n,a,o,i,t.getUint32(12,true),22,3250441966);i=u(i,n,a,o,t.getUint32(16,true),7,4118548399);o=u(o,i,n,a,t.getUint32(20,true),12,1200080426);a=u(a,o,i,n,t.getUint32(24,true),17,2821735955);n=u(n,a,o,i,t.getUint32(28,true),22,4249261313);i=u(i,n,a,o,t.getUint32(32,true),7,1770035416);o=u(o,i,n,a,t.getUint32(36,true),12,2336552879);a=u(a,o,i,n,t.getUint32(40,true),17,4294925233);n=u(n,a,o,i,t.getUint32(44,true),22,2304563134);i=u(i,n,a,o,t.getUint32(48,true),7,1804603682);o=u(o,i,n,a,t.getUint32(52,true),12,4254626195);a=u(a,o,i,n,t.getUint32(56,true),17,2792965006);n=u(n,a,o,i,t.getUint32(60,true),22,1236535329);i=p(i,n,a,o,t.getUint32(4,true),5,4129170786);o=p(o,i,n,a,t.getUint32(24,true),9,3225465664);a=p(a,o,i,n,t.getUint32(44,true),14,643717713);n=p(n,a,o,i,t.getUint32(0,true),20,3921069994);i=p(i,n,a,o,t.getUint32(20,true),5,3593408605);o=p(o,i,n,a,t.getUint32(40,true),9,38016083);a=p(a,o,i,n,t.getUint32(60,true),14,3634488961);n=p(n,a,o,i,t.getUint32(16,true),20,3889429448);i=p(i,n,a,o,t.getUint32(36,true),5,568446438);o=p(o,i,n,a,t.getUint32(56,true),9,3275163606);a=p(a,o,i,n,t.getUint32(12,true),14,4107603335);n=p(n,a,o,i,t.getUint32(32,true),20,1163531501);i=p(i,n,a,o,t.getUint32(52,true),5,2850285829);o=p(o,i,n,a,t.getUint32(8,true),9,4243563512);a=p(a,o,i,n,t.getUint32(28,true),14,1735328473);n=p(n,a,o,i,t.getUint32(48,true),20,2368359562);i=h(i,n,a,o,t.getUint32(20,true),4,4294588738);o=h(o,i,n,a,t.getUint32(32,true),11,2272392833);a=h(a,o,i,n,t.getUint32(44,true),16,1839030562);n=h(n,a,o,i,t.getUint32(56,true),23,4259657740);i=h(i,n,a,o,t.getUint32(4,true),4,2763975236);o=h(o,i,n,a,t.getUint32(16,true),11,1272893353);a=h(a,o,i,n,t.getUint32(28,true),16,4139469664);n=h(n,a,o,i,t.getUint32(40,true),23,3200236656);i=h(i,n,a,o,t.getUint32(52,true),4,681279174);o=h(o,i,n,a,t.getUint32(0,true),11,3936430074);a=h(a,o,i,n,t.getUint32(12,true),16,3572445317);n=h(n,a,o,i,t.getUint32(24,true),23,76029189);i=h(i,n,a,o,t.getUint32(36,true),4,3654602809);o=h(o,i,n,a,t.getUint32(48,true),11,3873151461);a=h(a,o,i,n,t.getUint32(60,true),16,530742520);n=h(n,a,o,i,t.getUint32(8,true),23,3299628645);i=d(i,n,a,o,t.getUint32(0,true),6,4096336452);o=d(o,i,n,a,t.getUint32(28,true),10,1126891415);a=d(a,o,i,n,t.getUint32(56,true),15,2878612391);n=d(n,a,o,i,t.getUint32(20,true),21,4237533241);i=d(i,n,a,o,t.getUint32(48,true),6,1700485571);o=d(o,i,n,a,t.getUint32(12,true),10,2399980690);a=d(a,o,i,n,t.getUint32(40,true),15,4293915773);n=d(n,a,o,i,t.getUint32(4,true),21,2240044497);i=d(i,n,a,o,t.getUint32(32,true),6,1873313359);o=d(o,i,n,a,t.getUint32(60,true),10,4264355552);a=d(a,o,i,n,t.getUint32(24,true),15,2734768916);n=d(n,a,o,i,t.getUint32(52,true),21,1309151649);i=d(i,n,a,o,t.getUint32(16,true),6,4149444226);o=d(o,i,n,a,t.getUint32(44,true),10,3174756917);a=d(a,o,i,n,t.getUint32(8,true),15,718787259);n=d(n,a,o,i,t.getUint32(36,true),21,3951481745);r[0]=i+r[0]&4294967295;r[1]=n+r[1]&4294967295;r[2]=a+r[2]&4294967295;r[3]=o+r[3]&4294967295};function s(e,t,r,i,n,a){t=(t+e&4294967295)+(i+a&4294967295)&4294967295;return(t<<n|t>>>32-n)+r&4294967295}function u(e,t,r,i,n,a,o){return s(t&r|~t&i,e,t,n,a,o)}function p(e,t,r,i,n,a,o){return s(t&i|r&~i,e,t,n,a,o)}function h(e,t,r,i,n,a,o){return s(t^r^i,e,t,n,a,o)}function d(e,t,r,i,n,a,o){return s(r^(t|~i),e,t,n,a,o)}},{"./browserHashUtils":30,"buffer/":3}],31:[function(e,t,r){var a=e("./browserHashUtils");function i(e,t){this.hash=new e;this.outer=new e;var r=o(e,t);var i=new Uint8Array(e.BLOCK_SIZE);i.set(r);for(var n=0;n<e.BLOCK_SIZE;n++){r[n]^=54;i[n]^=92}this.hash.update(r);this.outer.update(i);for(var n=0;n<r.byteLength;n++){r[n]=0}}t.exports=r=i;i.prototype.update=function(e){if(a.isEmptyData(e)||this.error){return this}try{this.hash.update(a.convertToBuffer(e))}catch(e){this.error=e}return this};i.prototype.digest=function(e){if(!this.outer.finished){this.outer.update(this.hash.digest())}return this.outer.digest(e)};function o(e,t){var r=a.convertToBuffer(t);if(r.byteLength>e.BLOCK_SIZE){var i=new e;i.update(r);r=i.digest()}var n=new Uint8Array(e.BLOCK_SIZE);n.set(r);return n}},{"./browserHashUtils":30}],30:[function(e,t,r){var i=e("buffer/").Buffer;if(typeof ArrayBuffer!=="undefined"&&typeof ArrayBuffer.isView==="undefined"){ArrayBuffer.isView=function(e){return n.indexOf(Object.prototype.toString.call(e))>-1}}var n=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]","[object DataView]"];function a(e){if(typeof e==="string"){return e.length===0}return e.byteLength===0}function o(e){if(typeof e==="string"){e=new i(e,"utf8")}if(ArrayBuffer.isView(e)){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT)}return new Uint8Array(e)}t.exports=r={isEmptyData:a,convertToBuffer:o}},{"buffer/":3}],18:[function(e,t,r){var T=e("punycode");r.parse=o;r.resolve=u;r.resolveObject=c;r.format=s;r.Url=g;function g(){this.protocol=null;this.slashes=null;this.auth=null;this.host=null;this.port=null;this.hostname=null;this.hash=null;this.search=null;this.query=null;this.pathname=null;this.path=null;this.href=null}var A=/^([a-z0-9.+-]+:)/i,i=/:[0-9]*$/,n=["<",">",'"',"`"," ","\r","\n","\t"],a=["{","}","|","\\","^","`"].concat(n),P=["'"].concat(a),I=["%","/","?",";","#"].concat(P),L=["/","?","#"],M=255,U=/^[a-z0-9A-Z_-]{0,63}$/,D=/^([a-z0-9A-Z_-]{0,63})(.*)$/,O={javascript:true,"javascript:":true},z={javascript:true,"javascript:":true},j={http:true,https:true,ftp:true,gopher:true,file:true,"http:":true,"https:":true,"ftp:":true,"gopher:":true,"file:":true},K=e("querystring");function o(e,t,r){if(e&&l(e)&&e instanceof g)return e;var i=new g;i.parse(e,t,r);return i}g.prototype.parse=function(e,t,r){if(!F(e)){throw new TypeError("Parameter 'url' must be a string, not "+typeof e)}var i=e;i=i.trim();var n=A.exec(i);if(n){n=n[0];var a=n.toLowerCase();this.protocol=a;i=i.substr(n.length)}if(r||n||i.match(/^\/\/[^@\/]+@[^@\/]+/)){var o=i.substr(0,2)==="//";if(o&&!(n&&z[n])){i=i.substr(2);this.slashes=true}}if(!z[n]&&(o||n&&!j[n])){var s=-1;for(var u=0;u<L.length;u++){var c=i.indexOf(L[u]);if(c!==-1&&(s===-1||c<s))s=c}var l,f;if(s===-1){f=i.lastIndexOf("@")}else{f=i.lastIndexOf("@",s)}if(f!==-1){l=i.slice(0,f);i=i.slice(f+1);this.auth=decodeURIComponent(l)}s=-1;for(var u=0;u<I.length;u++){var c=i.indexOf(I[u]);if(c!==-1&&(s===-1||c<s))s=c}if(s===-1)s=i.length;this.host=i.slice(0,s);i=i.slice(s);this.parseHost();this.hostname=this.hostname||"";var p=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!p){var h=this.hostname.split(/\./);for(var u=0,d=h.length;u<d;u++){var m=h[u];if(!m)continue;if(!m.match(U)){var y="";for(var v=0,g=m.length;v<g;v++){if(m.charCodeAt(v)>127){y+="x"}else{y+=m[v]}}if(!y.match(U)){var b=h.slice(0,u);var S=h.slice(u+1);var k=m.match(D);if(k){b.push(k[1]);S.unshift(k[2])}if(S.length){i="/"+S.join(".")+i}this.hostname=b.join(".");break}}}}if(this.hostname.length>M){this.hostname=""}else{this.hostname=this.hostname.toLowerCase()}if(!p){var C=this.hostname.split(".");var E=[];for(var u=0;u<C.length;++u){var w=C[u];E.push(w.match(/[^A-Za-z0-9_-]/)?"xn--"+T.encode(w):w)}this.hostname=E.join(".")}var x=this.port?":"+this.port:"";var N=this.hostname||"";this.host=N+x;this.href+=this.host;if(p){this.hostname=this.hostname.substr(1,this.hostname.length-2);if(i[0]!=="/"){i="/"+i}}}if(!O[a]){for(var u=0,d=P.length;u<d;u++){var R=P[u];var q=encodeURIComponent(R);if(q===R){q=escape(R)}i=i.split(R).join(q)}}var _=i.indexOf("#");if(_!==-1){this.hash=i.substr(_);i=i.slice(0,_)}var B=i.indexOf("?");if(B!==-1){this.search=i.substr(B);this.query=i.substr(B+1);if(t){this.query=K.parse(this.query)}i=i.slice(0,B)}else if(t){this.search="";this.query={}}if(i)this.pathname=i;if(j[a]&&this.hostname&&!this.pathname){this.pathname="/"}if(this.pathname||this.search){var x=this.pathname||"";var w=this.search||"";this.path=x+w}this.href=this.format();return this};function s(e){if(F(e))e=o(e);if(!(e instanceof g))return g.prototype.format.call(e);return e.format()}g.prototype.format=function(){var e=this.auth||"";if(e){e=encodeURIComponent(e);e=e.replace(/%3A/i,":");e+="@"}var t=this.protocol||"",r=this.pathname||"",i=this.hash||"",n=false,a="";if(this.host){n=e+this.host}else if(this.hostname){n=e+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]");if(this.port){n+=":"+this.port}}if(this.query&&l(this.query)&&Object.keys(this.query).length){a=K.stringify(this.query)}var o=this.search||a&&"?"+a||"";if(t&&t.substr(-1)!==":")t+=":";if(this.slashes||(!t||j[t])&&n!==false){n="//"+(n||"");if(r&&r.charAt(0)!=="/")r="/"+r}else if(!n){n=""}if(i&&i.charAt(0)!=="#")i="#"+i;if(o&&o.charAt(0)!=="?")o="?"+o;r=r.replace(/[?#]/g,function(e){return encodeURIComponent(e)});o=o.replace("#","%23");return t+n+r+o+i};function u(e,t){return o(e,false,true).resolve(t)}g.prototype.resolve=function(e){return this.resolveObject(o(e,false,true)).format()};function c(e,t){if(!e)return t;return o(e,false,true).resolveObject(t)}g.prototype.resolveObject=function(t){if(F(t)){var e=new g;e.parse(t,false,true);t=e}var r=new g;Object.keys(this).forEach(function(e){r[e]=this[e]},this);r.hash=t.hash;if(t.href===""){r.href=r.format();return r}if(t.slashes&&!t.protocol){Object.keys(t).forEach(function(e){if(e!=="protocol")r[e]=t[e]});if(j[r.protocol]&&r.hostname&&!r.pathname){r.path=r.pathname="/"}r.href=r.format();return r}if(t.protocol&&t.protocol!==r.protocol){if(!j[t.protocol]){Object.keys(t).forEach(function(e){r[e]=t[e]});r.href=r.format();return r}r.protocol=t.protocol;if(!t.host&&!z[t.protocol]){var i=(t.pathname||"").split("/");while(i.length&&!(t.host=i.shift()));if(!t.host)t.host="";if(!t.hostname)t.hostname="";if(i[0]!=="")i.unshift("");if(i.length<2)i.unshift("");r.pathname=i.join("/")}else{r.pathname=t.pathname}r.search=t.search;r.query=t.query;r.host=t.host||"";r.auth=t.auth;r.hostname=t.hostname||t.host;r.port=t.port;if(r.pathname||r.search){var n=r.pathname||"";var a=r.search||"";r.path=n+a}r.slashes=r.slashes||t.slashes;r.href=r.format();return r}var o=r.pathname&&r.pathname.charAt(0)==="/",s=t.host||t.pathname&&t.pathname.charAt(0)==="/",u=s||o||r.host&&t.pathname,c=u,l=r.pathname&&r.pathname.split("/")||[],i=t.pathname&&t.pathname.split("/")||[],f=r.protocol&&!j[r.protocol];if(f){r.hostname="";r.port=null;if(r.host){if(l[0]==="")l[0]=r.host;else l.unshift(r.host)}r.host="";if(t.protocol){t.hostname=null;t.port=null;if(t.host){if(i[0]==="")i[0]=t.host;else i.unshift(t.host)}t.host=null}u=u&&(i[0]===""||l[0]==="")}if(s){r.host=t.host||t.host===""?t.host:r.host;r.hostname=t.hostname||t.hostname===""?t.hostname:r.hostname;r.search=t.search;r.query=t.query;l=i}else if(i.length){if(!l)l=[];l.pop();l=l.concat(i);r.search=t.search;r.query=t.query}else if(!S(t.search)){if(f){r.hostname=r.host=l.shift();var p=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(p){r.auth=p.shift();r.host=r.hostname=p.shift()}}r.search=t.search;r.query=t.query;if(!b(r.pathname)||!b(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.href=r.format();return r}if(!l.length){r.pathname=null;if(r.search){r.path="/"+r.search}else{r.path=null}r.href=r.format();return r}var h=l.slice(-1)[0];var d=(r.host||t.host)&&(h==="."||h==="..")||h==="";var m=0;for(var y=l.length;y>=0;y--){h=l[y];if(h=="."){l.splice(y,1)}else if(h===".."){l.splice(y,1);m++}else if(m){l.splice(y,1);m--}}if(!u&&!c){for(;m--;m){l.unshift("..")}}if(u&&l[0]!==""&&(!l[0]||l[0].charAt(0)!=="/")){l.unshift("")}if(d&&l.join("/").substr(-1)!=="/"){l.push("")}var v=l[0]===""||l[0]&&l[0].charAt(0)==="/";if(f){r.hostname=r.host=v?"":l.length?l.shift():"";var p=r.host&&r.host.indexOf("@")>0?r.host.split("@"):false;if(p){r.auth=p.shift();r.host=r.hostname=p.shift()}}u=u||r.host&&l.length;if(u&&!v){l.unshift("")}if(!l.length){r.pathname=null;r.path=null}else{r.pathname=l.join("/")}if(!b(r.pathname)||!b(r.search)){r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")}r.auth=t.auth||r.auth;r.slashes=r.slashes||t.slashes;r.href=r.format();return r};g.prototype.parseHost=function(){var e=this.host;var t=i.exec(e);if(t){t=t[0];if(t!==":"){this.port=t.substr(1)}e=e.substr(0,e.length-t.length)}if(e)this.hostname=e};function F(e){return typeof e==="string"}function l(e){return typeof e==="object"&&e!==null}function b(e){return e===null}function S(e){return e==null}},{punycode:10,querystring:13}],16:[function(e,t,r){arguments[4][13][0].apply(r,arguments)},{"./decode":14,"./encode":15,dup:13}],15:[function(e,t,r){"use strict";var a=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(r,i,n,e){i=i||"&";n=n||"=";if(r===null){r=undefined}if(typeof r==="object"){return Object.keys(r).map(function(e){var t=encodeURIComponent(a(e))+n;if(Array.isArray(r[e])){return r[e].map(function(e){return t+encodeURIComponent(a(e))}).join(i)}else{return t+encodeURIComponent(a(r[e]))}}).join(i)}if(!e)return"";return encodeURIComponent(a(e))+n+encodeURIComponent(a(r))}},{}],14:[function(e,t,r){"use strict";function m(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,i){t=t||"&";r=r||"=";var n={};if(typeof e!=="string"||e.length===0){return n}var a=/\+/g;e=e.split(t);var o=1e3;if(i&&typeof i.maxKeys==="number"){o=i.maxKeys}var s=e.length;if(o>0&&s>o){s=o}for(var u=0;u<s;++u){var c=e[u].replace(a,"%20"),l=c.indexOf(r),f,p,h,d;if(l>=0){f=c.substr(0,l);p=c.substr(l+1)}else{f=c;p=""}h=decodeURIComponent(f);d=decodeURIComponent(p);if(!m(n,h)){n[h]=d}else if(Array.isArray(n[h])){n[h].push(d)}else{n[h]=[n[h],d]}}return n}},{}],13:[function(e,t,r){"use strict";r.decode=r.parse=e("./decode");r.encode=r.stringify=e("./encode")},{"./decode":11,"./encode":12}],12:[function(e,t,r){"use strict";var a=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};t.exports=function(r,i,n,e){i=i||"&";n=n||"=";if(r===null){r=undefined}if(typeof r==="object"){return s(u(r),function(e){var t=encodeURIComponent(a(e))+n;if(o(r[e])){return s(r[e],function(e){return t+encodeURIComponent(a(e))}).join(i)}else{return t+encodeURIComponent(a(r[e]))}}).join(i)}if(!e)return"";return encodeURIComponent(a(e))+n+encodeURIComponent(a(r))};var o=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"};function s(e,t){if(e.map)return e.map(t);var r=[];for(var i=0;i<e.length;i++){r.push(t(e[i],i))}return r}var u=Object.keys||function(e){var t=[];for(var r in e){if(Object.prototype.hasOwnProperty.call(e,r))t.push(r)}return t}},{}],11:[function(e,t,r){"use strict";function m(e,t){return Object.prototype.hasOwnProperty.call(e,t)}t.exports=function(e,t,r,i){t=t||"&";r=r||"=";var n={};if(typeof e!=="string"||e.length===0){return n}var a=/\+/g;e=e.split(t);var o=1e3;if(i&&typeof i.maxKeys==="number"){o=i.maxKeys}var s=e.length;if(o>0&&s>o){s=o}for(var u=0;u<s;++u){var c=e[u].replace(a,"%20"),l=c.indexOf(r),f,p,h,d;if(l>=0){f=c.substr(0,l);p=c.substr(l+1)}else{f=c;p=""}h=decodeURIComponent(f);d=decodeURIComponent(p);if(!m(n,h)){n[h]=d}else if(y(n[h])){n[h].push(d)}else{n[h]=[n[h],d]}}return n};var y=Array.isArray||function(e){return Object.prototype.toString.call(e)==="[object Array]"}},{}],10:[function(e,L,M){(function(I){(function(e){var t=typeof M=="object"&&M&&!M.nodeType&&M;var r=typeof L=="object"&&L&&!L.nodeType&&L;var i=typeof I=="object"&&I;if(i.global===i||i.window===i||i.self===i){e=i}var n,v=2147483647,g=36,b=1,S=26,a=38,o=700,k=72,C=128,E="-",s=/^xn--/,u=/[^\x20-\x7E]/,c=/[\x2E\u3002\uFF0E\uFF61]/g,l={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},f=g-b,w=Math.floor,x=String.fromCharCode,p;function N(e){throw RangeError(l[e])}function h(e,t){var r=e.length;var i=[];while(r--){i[r]=t(e[r])}return i}function d(e,t){var r=e.split("@");var i="";if(r.length>1){i=r[0]+"@";e=r[1]}e=e.replace(c,".");var n=e.split(".");var a=h(n,t).join(".");return i+a}function R(e){var t=[],r=0,i=e.length,n,a;while(r<i){n=e.charCodeAt(r++);if(n>=55296&&n<=56319&&r<i){a=e.charCodeAt(r++);if((a&64512)==56320){t.push(((n&1023)<<10)+(a&1023)+65536)}else{t.push(n);r--}}else{t.push(n)}}return t}function y(e){return h(e,function(e){var t="";if(e>65535){e-=65536;t+=x(e>>>10&1023|55296);e=56320|e&1023}t+=x(e);return t}).join("")}function q(e){if(e-48<10){return e-22}if(e-65<26){return e-65}if(e-97<26){return e-97}return g}function _(e,t){return e+22+75*(e<26)-((t!=0)<<5)}function B(e,t,r){var i=0;e=r?w(e/o):e>>1;e+=w(e/t);for(;e>f*S>>1;i+=g){e=w(e/f)}return w(i+(f+1)*e/(e+a))}function m(e){var t=[],r=e.length,i,n=0,a=C,o=k,s,u,c,l,f,p,h,d,m;s=e.lastIndexOf(E);if(s<0){s=0}for(u=0;u<s;++u){if(e.charCodeAt(u)>=128){N("not-basic")}t.push(e.charCodeAt(u))}for(c=s>0?s+1:0;c<r;){for(l=n,f=1,p=g;;p+=g){if(c>=r){N("invalid-input")}h=q(e.charCodeAt(c++));if(h>=g||h>w((v-n)/f)){N("overflow")}n+=h*f;d=p<=o?b:p>=o+S?S:p-o;if(h<d){break}m=g-d;if(f>w(v/m)){N("overflow")}f*=m}i=t.length+1;o=B(n-l,i,l==0);if(w(n/i)>v-a){N("overflow")}a+=w(n/i);n%=i;t.splice(n++,0,a)}return y(t)}function T(e){var t,r,i,n,a,o,s,u,c,l,f,p=[],h,d,m,y;e=R(e);h=e.length;t=C;r=0;a=k;for(o=0;o<h;++o){f=e[o];if(f<128){p.push(x(f))}}i=n=p.length;if(n){p.push(E)}while(i<h){for(s=v,o=0;o<h;++o){f=e[o];if(f>=t&&f<s){s=f}}d=i+1;if(s-t>w((v-r)/d)){N("overflow")}r+=(s-t)*d;t=s;for(o=0;o<h;++o){f=e[o];if(f<t&&++r>v){N("overflow")}if(f==t){for(u=r,c=g;;c+=g){l=c<=a?b:c>=a+S?S:c-a;if(u<l){break}y=u-l;m=g-l;p.push(x(_(l+y%m,0)));u=w(y/m)}p.push(x(_(u,0)));a=B(r,d,i==n);r=0;++i}}++r;++t}return p.join("")}function A(e){return d(e,function(e){return s.test(e)?m(e.slice(4).toLowerCase()):e})}function P(e){return d(e,function(e){return u.test(e)?"xn--"+T(e):e})}n={version:"1.3.2",ucs2:{decode:R,encode:y},decode:m,encode:T,toASCII:P,toUnicode:A};if(typeof define=="function"&&typeof define.amd=="object"&&define.amd){define("punycode",function(){return n})}else if(t&&r){if(L.exports==t){r.exports=n}else{for(p in n){n.hasOwnProperty(p)&&(t[p]=n[p])}}}else{e.punycode=n}})(this)}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{}],4:[function(e,t,r){function i(){this._events=this._events||{};this._maxListeners=this._maxListeners||undefined}t.exports=i;i.EventEmitter=i;i.prototype._events=undefined;i.prototype._maxListeners=undefined;i.defaultMaxListeners=10;i.prototype.setMaxListeners=function(e){if(!n(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");this._maxListeners=e;return this};i.prototype.emit=function(e){var t,r,i,n,a,o;if(!this._events)this._events={};if(e==="error"){if(!this._events.error||c(this._events.error)&&!this._events.error.length){t=arguments[1];if(t instanceof Error){throw t}else{var s=new Error('Uncaught, unspecified "error" event. ('+t+")");s.context=t;throw s}}}r=this._events[e];if(l(r))return false;if(u(r)){switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:n=Array.prototype.slice.call(arguments,1);r.apply(this,n)}}else if(c(r)){n=Array.prototype.slice.call(arguments,1);o=r.slice();i=o.length;for(a=0;a<i;a++)o[a].apply(this,n)}return true};i.prototype.addListener=function(e,t){var r;if(!u(t))throw TypeError("listener must be a function");if(!this._events)this._events={};if(this._events.newListener)this.emit("newListener",e,u(t.listener)?t.listener:t);if(!this._events[e])this._events[e]=t;else if(c(this._events[e]))this._events[e].push(t);else this._events[e]=[this._events[e],t];if(c(this._events[e])&&!this._events[e].warned){if(!l(this._maxListeners)){r=this._maxListeners}else{r=i.defaultMaxListeners}if(r&&r>0&&this._events[e].length>r){this._events[e].warned=true;console.error("(node) warning: possible EventEmitter memory "+"leak detected. %d listeners added. "+"Use emitter.setMaxListeners() to increase limit.",this._events[e].length);if(typeof console.trace==="function"){console.trace()}}}return this};i.prototype.on=i.prototype.addListener;i.prototype.once=function(e,t){if(!u(t))throw TypeError("listener must be a function");var r=false;function i(){this.removeListener(e,i);if(!r){r=true;t.apply(this,arguments)}}i.listener=t;this.on(e,i);return this};i.prototype.removeListener=function(e,t){var r,i,n,a;if(!u(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;r=this._events[e];n=r.length;i=-1;if(r===t||u(r.listener)&&r.listener===t){delete this._events[e];if(this._events.removeListener)this.emit("removeListener",e,t)}else if(c(r)){for(a=n;a-- >0;){if(r[a]===t||r[a].listener&&r[a].listener===t){i=a;break}}if(i<0)return this;if(r.length===1){r.length=0;delete this._events[e]}else{r.splice(i,1)}if(this._events.removeListener)this.emit("removeListener",e,t)}return this};i.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener){if(arguments.length===0)this._events={};else if(this._events[e])delete this._events[e];return this}if(arguments.length===0){for(t in this._events){if(t==="removeListener")continue;this.removeAllListeners(t)}this.removeAllListeners("removeListener");this._events={};return this}r=this._events[e];if(u(r)){this.removeListener(e,r)}else if(r){while(r.length)this.removeListener(e,r[r.length-1])}delete this._events[e];return this};i.prototype.listeners=function(e){var t;if(!this._events||!this._events[e])t=[];else if(u(this._events[e]))t=[this._events[e]];else t=this._events[e].slice();return t};i.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(u(t))return 1;else if(t)return t.length}return 0};i.listenerCount=function(e,t){return e.listenerCount(t)};function u(e){return typeof e==="function"}function n(e){return typeof e==="number"}function c(e){return typeof e==="object"&&e!==null}function l(e){return e===void 0}},{}],3:[function(ee,e,te){(function(e){"use strict";var i=ee("base64-js");var a=ee("ieee754");var s=ee("isarray");te.Buffer=p;te.SlowBuffer=v;te.INSPECT_MAX_BYTES=50;p.TYPED_ARRAY_SUPPORT=e.TYPED_ARRAY_SUPPORT!==undefined?e.TYPED_ARRAY_SUPPORT:t();te.kMaxLength=r();function t(){try{var e=new Uint8Array(1);e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}};return e.foo()===42&&typeof e.subarray==="function"&&e.subarray(1,1).byteLength===0}catch(e){return false}}function r(){return p.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function o(e,t){if(r()<t){throw new RangeError("Invalid typed array length")}if(p.TYPED_ARRAY_SUPPORT){e=new Uint8Array(t);e.__proto__=p.prototype}else{if(e===null){e=new p(t)}e.length=t}return e}function p(e,t,r){if(!p.TYPED_ARRAY_SUPPORT&&!(this instanceof p)){return new p(e,t,r)}if(typeof e==="number"){if(typeof t==="string"){throw new Error("If encoding is specified then the first argument must be a string")}return l(this,e)}return n(this,e,t,r)}p.poolSize=8192;p._augment=function(e){e.__proto__=p.prototype;return e};function n(e,t,r,i){if(typeof t==="number"){throw new TypeError('"value" argument must not be a number')}if(typeof ArrayBuffer!=="undefined"&&t instanceof ArrayBuffer){return d(e,t,r,i)}if(typeof t==="string"){return f(e,t,r)}return m(e,t)}p.from=function(e,t,r){return n(null,e,t,r)};if(p.TYPED_ARRAY_SUPPORT){p.prototype.__proto__=Uint8Array.prototype;p.__proto__=Uint8Array;if(typeof Symbol!=="undefined"&&Symbol.species&&p[Symbol.species]===p){Object.defineProperty(p,Symbol.species,{value:null,configurable:true})}}function u(e){if(typeof e!=="number"){throw new TypeError('"size" argument must be a number')}else if(e<0){throw new RangeError('"size" argument must not be negative')}}function c(e,t,r,i){u(t);if(t<=0){return o(e,t)}if(r!==undefined){return typeof i==="string"?o(e,t).fill(r,i):o(e,t).fill(r)}return o(e,t)}p.alloc=function(e,t,r){return c(null,e,t,r)};function l(e,t){u(t);e=o(e,t<0?0:y(t)|0);if(!p.TYPED_ARRAY_SUPPORT){for(var r=0;r<t;++r){e[r]=0}}return e}p.allocUnsafe=function(e){return l(null,e)};p.allocUnsafeSlow=function(e){return l(null,e)};function f(e,t,r){if(typeof r!=="string"||r===""){r="utf8"}if(!p.isEncoding(r)){throw new TypeError('"encoding" must be a valid string encoding')}var i=g(t,r)|0;e=o(e,i);var n=e.write(t,r);if(n!==i){e=e.slice(0,n)}return e}function h(e,t){var r=t.length<0?0:y(t.length)|0;e=o(e,r);for(var i=0;i<r;i+=1){e[i]=t[i]&255}return e}function d(e,t,r,i){t.byteLength;if(r<0||t.byteLength<r){throw new RangeError("'offset' is out of bounds")}if(t.byteLength<r+(i||0)){throw new RangeError("'length' is out of bounds")}if(r===undefined&&i===undefined){t=new Uint8Array(t)}else if(i===undefined){t=new Uint8Array(t,r)}else{t=new Uint8Array(t,r,i)}if(p.TYPED_ARRAY_SUPPORT){e=t;e.__proto__=p.prototype}else{e=h(e,t)}return e}function m(e,t){if(p.isBuffer(t)){var r=y(t.length)|0;e=o(e,r);if(e.length===0){return e}t.copy(e,0,0,r);return e}if(t){if(typeof ArrayBuffer!=="undefined"&&t.buffer instanceof ArrayBuffer||"length"in t){if(typeof t.length!=="number"||Z(t.length)){return o(e,0)}return h(e,t)}if(t.type==="Buffer"&&s(t.data)){return h(e,t.data)}}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function y(e){if(e>=r()){throw new RangeError("Attempt to allocate Buffer larger than maximum "+"size: 0x"+r().toString(16)+" bytes")}return e|0}function v(e){if(+e!=e){e=0}return p.alloc(+e)}p.isBuffer=function e(t){return!!(t!=null&&t._isBuffer)};p.compare=function e(t,r){if(!p.isBuffer(t)||!p.isBuffer(r)){throw new TypeError("Arguments must be Buffers")}if(t===r)return 0;var i=t.length;var n=r.length;for(var a=0,o=Math.min(i,n);a<o;++a){if(t[a]!==r[a]){i=t[a];n=r[a];break}}if(i<n)return-1;if(n<i)return 1;return 0};p.isEncoding=function e(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return true;default:return false}};p.concat=function e(t,r){if(!s(t)){throw new TypeError('"list" argument must be an Array of Buffers')}if(t.length===0){return p.alloc(0)}var i;if(r===undefined){r=0;for(i=0;i<t.length;++i){r+=t[i].length}}var n=p.allocUnsafe(r);var a=0;for(i=0;i<t.length;++i){var o=t[i];if(!p.isBuffer(o)){throw new TypeError('"list" argument must be an Array of Buffers')}o.copy(n,a);a+=o.length}return n};function g(e,t){if(p.isBuffer(e)){return e.length}if(typeof ArrayBuffer!=="undefined"&&typeof ArrayBuffer.isView==="function"&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer)){return e.byteLength}if(typeof e!=="string"){e=""+e}var r=e.length;if(r===0)return 0;var i=false;for(;;){switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case undefined:return X(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return r*2;case"hex":return r>>>1;case"base64":return Q(e).length;default:if(i)return X(e).length;t=(""+t).toLowerCase();i=true}}}p.byteLength=g;function b(e,t,r){var i=false;if(t===undefined||t<0){t=0}if(t>this.length){return""}if(r===undefined||r>this.length){r=this.length}if(r<=0){return""}r>>>=0;t>>>=0;if(r<=t){return""}if(!e)e="utf8";while(true){switch(e){case"hex":return L(this,t,r);case"utf8":case"utf-8":return B(this,t,r);case"ascii":return P(this,t,r);case"latin1":case"binary":return I(this,t,r);case"base64":return _(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,t,r);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase();i=true}}}p.prototype._isBuffer=true;function S(e,t,r){var i=e[t];e[t]=e[r];e[r]=i}p.prototype.swap16=function e(){var t=this.length;if(t%2!==0){throw new RangeError("Buffer size must be a multiple of 16-bits")}for(var r=0;r<t;r+=2){S(this,r,r+1)}return this};p.prototype.swap32=function e(){var t=this.length;if(t%4!==0){throw new RangeError("Buffer size must be a multiple of 32-bits")}for(var r=0;r<t;r+=4){S(this,r,r+3);S(this,r+1,r+2)}return this};p.prototype.swap64=function e(){var t=this.length;if(t%8!==0){throw new RangeError("Buffer size must be a multiple of 64-bits")}for(var r=0;r<t;r+=8){S(this,r,r+7);S(this,r+1,r+6);S(this,r+2,r+5);S(this,r+3,r+4)}return this};p.prototype.toString=function e(){var t=this.length|0;if(t===0)return"";if(arguments.length===0)return B(this,0,t);return b.apply(this,arguments)};p.prototype.equals=function e(t){if(!p.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(this===t)return true;return p.compare(this,t)===0};p.prototype.inspect=function e(){var t="";var r=te.INSPECT_MAX_BYTES;if(this.length>0){t=this.toString("hex",0,r).match(/.{2}/g).join(" ");if(this.length>r)t+=" ... "}return"<Buffer "+t+">"};p.prototype.compare=function e(t,r,i,n,a){if(!p.isBuffer(t)){throw new TypeError("Argument must be a Buffer")}if(r===undefined){r=0}if(i===undefined){i=t?t.length:0}if(n===undefined){n=0}if(a===undefined){a=this.length}if(r<0||i>t.length||n<0||a>this.length){throw new RangeError("out of range index")}if(n>=a&&r>=i){return 0}if(n>=a){return-1}if(r>=i){return 1}r>>>=0;i>>>=0;n>>>=0;a>>>=0;if(this===t)return 0;var o=a-n;var s=i-r;var u=Math.min(o,s);var c=this.slice(n,a);var l=t.slice(r,i);for(var f=0;f<u;++f){if(c[f]!==l[f]){o=c[f];s=l[f];break}}if(o<s)return-1;if(s<o)return 1;return 0};function k(e,t,r,i,n){if(e.length===0)return-1;if(typeof r==="string"){i=r;r=0}else if(r>2147483647){r=2147483647}else if(r<-2147483648){r=-2147483648}r=+r;if(isNaN(r)){r=n?0:e.length-1}if(r<0)r=e.length+r;if(r>=e.length){if(n)return-1;else r=e.length-1}else if(r<0){if(n)r=0;else return-1}if(typeof t==="string"){t=p.from(t,i)}if(p.isBuffer(t)){if(t.length===0){return-1}return C(e,t,r,i,n)}else if(typeof t==="number"){t=t&255;if(p.TYPED_ARRAY_SUPPORT&&typeof Uint8Array.prototype.indexOf==="function"){if(n){return Uint8Array.prototype.indexOf.call(e,t,r)}else{return Uint8Array.prototype.lastIndexOf.call(e,t,r)}}return C(e,[t],r,i,n)}throw new TypeError("val must be string, number or Buffer")}function C(e,t,r,i,n){var a=1;var o=e.length;var s=t.length;if(i!==undefined){i=String(i).toLowerCase();if(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le"){if(e.length<2||t.length<2){return-1}a=2;o/=2;s/=2;r/=2}}function u(e,t){if(a===1){return e[t]}else{return e.readUInt16BE(t*a)}}var c;if(n){var l=-1;for(c=r;c<o;c++){if(u(e,c)===u(t,l===-1?0:c-l)){if(l===-1)l=c;if(c-l+1===s)return l*a}else{if(l!==-1)c-=c-l;l=-1}}}else{if(r+s>o)r=o-s;for(c=r;c>=0;c--){var f=true;for(var p=0;p<s;p++){if(u(e,c+p)!==u(t,p)){f=false;break}}if(f)return c}}return-1}p.prototype.includes=function e(t,r,i){return this.indexOf(t,r,i)!==-1};p.prototype.indexOf=function e(t,r,i){return k(this,t,r,i,true)};p.prototype.lastIndexOf=function e(t,r,i){return k(this,t,r,i,false)};function E(e,t,r,i){r=Number(r)||0;var n=e.length-r;if(!i){i=n}else{i=Number(i);if(i>n){i=n}}var a=t.length;if(a%2!==0)throw new TypeError("Invalid hex string");if(i>a/2){i=a/2}for(var o=0;o<i;++o){var s=parseInt(t.substr(o*2,2),16);if(isNaN(s))return o;e[r+o]=s}return o}function w(e,t,r,i){return $(X(t,e.length-r),e,r,i)}function x(e,t,r,i){return $(Y(t),e,r,i)}function N(e,t,r,i){return x(e,t,r,i)}function R(e,t,r,i){return $(Q(t),e,r,i)}function q(e,t,r,i){return $(J(t,e.length-r),e,r,i)}p.prototype.write=function e(t,r,i,n){if(r===undefined){n="utf8";i=this.length;r=0}else if(i===undefined&&typeof r==="string"){n=r;i=this.length;r=0}else if(isFinite(r)){r=r|0;if(isFinite(i)){i=i|0;if(n===undefined)n="utf8"}else{n=i;i=undefined}}else{throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported")}var a=this.length-r;if(i===undefined||i>a)i=a;if(t.length>0&&(i<0||r<0)||r>this.length){throw new RangeError("Attempt to write outside buffer bounds")}if(!n)n="utf8";var o=false;for(;;){switch(n){case"hex":return E(this,t,r,i);case"utf8":case"utf-8":return w(this,t,r,i);case"ascii":return x(this,t,r,i);case"latin1":case"binary":return N(this,t,r,i);case"base64":return R(this,t,r,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,t,r,i);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase();o=true}}};p.prototype.toJSON=function e(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function _(e,t,r){if(t===0&&r===e.length){return i.fromByteArray(e)}else{return i.fromByteArray(e.slice(t,r))}}function B(e,t,r){r=Math.min(e.length,r);var i=[];var n=t;while(n<r){var a=e[n];var o=null;var s=a>239?4:a>223?3:a>191?2:1;if(n+s<=r){var u,c,l,f;switch(s){case 1:if(a<128){o=a}break;case 2:u=e[n+1];if((u&192)===128){f=(a&31)<<6|u&63;if(f>127){o=f}}break;case 3:u=e[n+1];c=e[n+2];if((u&192)===128&&(c&192)===128){f=(a&15)<<12|(u&63)<<6|c&63;if(f>2047&&(f<55296||f>57343)){o=f}}break;case 4:u=e[n+1];c=e[n+2];l=e[n+3];if((u&192)===128&&(c&192)===128&&(l&192)===128){f=(a&15)<<18|(u&63)<<12|(c&63)<<6|l&63;if(f>65535&&f<1114112){o=f}}}}if(o===null){o=65533;s=1}else if(o>65535){o-=65536;i.push(o>>>10&1023|55296);o=56320|o&1023}i.push(o);n+=s}return A(i)}var T=4096;function A(e){var t=e.length;if(t<=T){return String.fromCharCode.apply(String,e)}var r="";var i=0;while(i<t){r+=String.fromCharCode.apply(String,e.slice(i,i+=T))}return r}function P(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n){i+=String.fromCharCode(e[n]&127)}return i}function I(e,t,r){var i="";r=Math.min(e.length,r);for(var n=t;n<r;++n){i+=String.fromCharCode(e[n])}return i}function L(e,t,r){var i=e.length;if(!t||t<0)t=0;if(!r||r<0||r>i)r=i;var n="";for(var a=t;a<r;++a){n+=W(e[a])}return n}function M(e,t,r){var i=e.slice(t,r);var n="";for(var a=0;a<i.length;a+=2){n+=String.fromCharCode(i[a]+i[a+1]*256)}return n}p.prototype.slice=function e(t,r){var i=this.length;t=~~t;r=r===undefined?i:~~r;if(t<0){t+=i;if(t<0)t=0}else if(t>i){t=i}if(r<0){r+=i;if(r<0)r=0}else if(r>i){r=i}if(r<t)r=t;var n;if(p.TYPED_ARRAY_SUPPORT){n=this.subarray(t,r);n.__proto__=p.prototype}else{var a=r-t;n=new p(a,undefined);for(var o=0;o<a;++o){n[o]=this[o+t]}}return n};function U(e,t,r){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}p.prototype.readUIntLE=function e(t,r,i){t=t|0;r=r|0;if(!i)U(t,r,this.length);var n=this[t];var a=1;var o=0;while(++o<r&&(a*=256)){n+=this[t+o]*a}return n};p.prototype.readUIntBE=function e(t,r,i){t=t|0;r=r|0;if(!i){U(t,r,this.length)}var n=this[t+--r];var a=1;while(r>0&&(a*=256)){n+=this[t+--r]*a}return n};p.prototype.readUInt8=function e(t,r){if(!r)U(t,1,this.length);return this[t]};p.prototype.readUInt16LE=function e(t,r){if(!r)U(t,2,this.length);return this[t]|this[t+1]<<8};p.prototype.readUInt16BE=function e(t,r){if(!r)U(t,2,this.length);return this[t]<<8|this[t+1]};p.prototype.readUInt32LE=function e(t,r){if(!r)U(t,4,this.length);return(this[t]|this[t+1]<<8|this[t+2]<<16)+this[t+3]*16777216};p.prototype.readUInt32BE=function e(t,r){if(!r)U(t,4,this.length);return this[t]*16777216+(this[t+1]<<16|this[t+2]<<8|this[t+3])};p.prototype.readIntLE=function e(t,r,i){t=t|0;r=r|0;if(!i)U(t,r,this.length);var n=this[t];var a=1;var o=0;while(++o<r&&(a*=256)){n+=this[t+o]*a}a*=128;if(n>=a)n-=Math.pow(2,8*r);return n};p.prototype.readIntBE=function e(t,r,i){t=t|0;r=r|0;if(!i)U(t,r,this.length);var n=r;var a=1;var o=this[t+--n];while(n>0&&(a*=256)){o+=this[t+--n]*a}a*=128;if(o>=a)o-=Math.pow(2,8*r);return o};p.prototype.readInt8=function e(t,r){if(!r)U(t,1,this.length);if(!(this[t]&128))return this[t];return(255-this[t]+1)*-1};p.prototype.readInt16LE=function e(t,r){if(!r)U(t,2,this.length);var i=this[t]|this[t+1]<<8;return i&32768?i|4294901760:i};p.prototype.readInt16BE=function e(t,r){if(!r)U(t,2,this.length);var i=this[t+1]|this[t]<<8;return i&32768?i|4294901760:i};p.prototype.readInt32LE=function e(t,r){if(!r)U(t,4,this.length);return this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24};p.prototype.readInt32BE=function e(t,r){if(!r)U(t,4,this.length);return this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]};p.prototype.readFloatLE=function e(t,r){if(!r)U(t,4,this.length);return a.read(this,t,true,23,4)};p.prototype.readFloatBE=function e(t,r){if(!r)U(t,4,this.length);return a.read(this,t,false,23,4)};p.prototype.readDoubleLE=function e(t,r){if(!r)U(t,8,this.length);return a.read(this,t,true,52,8)};p.prototype.readDoubleBE=function e(t,r){if(!r)U(t,8,this.length);return a.read(this,t,false,52,8)};function D(e,t,r,i,n,a){if(!p.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>n||t<a)throw new RangeError('"value" argument is out of bounds');if(r+i>e.length)throw new RangeError("Index out of range")}p.prototype.writeUIntLE=function e(t,r,i,n){t=+t;r=r|0;i=i|0;if(!n){var a=Math.pow(2,8*i)-1;D(this,t,r,i,a,0)}var o=1;var s=0;this[r]=t&255;while(++s<i&&(o*=256)){this[r+s]=t/o&255}return r+i};p.prototype.writeUIntBE=function e(t,r,i,n){t=+t;r=r|0;i=i|0;if(!n){var a=Math.pow(2,8*i)-1;D(this,t,r,i,a,0)}var o=i-1;var s=1;this[r+o]=t&255;while(--o>=0&&(s*=256)){this[r+o]=t/s&255}return r+i};p.prototype.writeUInt8=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,1,255,0);if(!p.TYPED_ARRAY_SUPPORT)t=Math.floor(t);this[r]=t&255;return r+1};function O(e,t,r,i){if(t<0)t=65535+t+1;for(var n=0,a=Math.min(e.length-r,2);n<a;++n){e[r+n]=(t&255<<8*(i?n:1-n))>>>(i?n:1-n)*8}}p.prototype.writeUInt16LE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,2,65535,0);if(p.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8}else{O(this,t,r,true)}return r+2};p.prototype.writeUInt16BE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,2,65535,0);if(p.TYPED_ARRAY_SUPPORT){this[r]=t>>>8;this[r+1]=t&255}else{O(this,t,r,false)}return r+2};function z(e,t,r,i){if(t<0)t=4294967295+t+1;for(var n=0,a=Math.min(e.length-r,4);n<a;++n){e[r+n]=t>>>(i?n:3-n)*8&255}}p.prototype.writeUInt32LE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,4,4294967295,0);if(p.TYPED_ARRAY_SUPPORT){this[r+3]=t>>>24;this[r+2]=t>>>16;this[r+1]=t>>>8;this[r]=t&255}else{z(this,t,r,true)}return r+4};p.prototype.writeUInt32BE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,4,4294967295,0);if(p.TYPED_ARRAY_SUPPORT){this[r]=t>>>24;this[r+1]=t>>>16;this[r+2]=t>>>8;this[r+3]=t&255}else{z(this,t,r,false)}return r+4};p.prototype.writeIntLE=function e(t,r,i,n){t=+t;r=r|0;if(!n){var a=Math.pow(2,8*i-1);D(this,t,r,i,a-1,-a)}var o=0;var s=1;var u=0;this[r]=t&255;while(++o<i&&(s*=256)){if(t<0&&u===0&&this[r+o-1]!==0){u=1}this[r+o]=(t/s>>0)-u&255}return r+i};p.prototype.writeIntBE=function e(t,r,i,n){t=+t;r=r|0;if(!n){var a=Math.pow(2,8*i-1);D(this,t,r,i,a-1,-a)}var o=i-1;var s=1;var u=0;this[r+o]=t&255;while(--o>=0&&(s*=256)){if(t<0&&u===0&&this[r+o+1]!==0){u=1}this[r+o]=(t/s>>0)-u&255}return r+i};p.prototype.writeInt8=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,1,127,-128);if(!p.TYPED_ARRAY_SUPPORT)t=Math.floor(t);if(t<0)t=255+t+1;this[r]=t&255;return r+1};p.prototype.writeInt16LE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,2,32767,-32768);if(p.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8}else{O(this,t,r,true)}return r+2};p.prototype.writeInt16BE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,2,32767,-32768);if(p.TYPED_ARRAY_SUPPORT){this[r]=t>>>8;this[r+1]=t&255}else{O(this,t,r,false)}return r+2};p.prototype.writeInt32LE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,4,2147483647,-2147483648);if(p.TYPED_ARRAY_SUPPORT){this[r]=t&255;this[r+1]=t>>>8;this[r+2]=t>>>16;this[r+3]=t>>>24}else{z(this,t,r,true)}return r+4};p.prototype.writeInt32BE=function e(t,r,i){t=+t;r=r|0;if(!i)D(this,t,r,4,2147483647,-2147483648);if(t<0)t=4294967295+t+1;if(p.TYPED_ARRAY_SUPPORT){this[r]=t>>>24;this[r+1]=t>>>16;this[r+2]=t>>>8;this[r+3]=t&255}else{z(this,t,r,false)}return r+4};function j(e,t,r,i,n,a){if(r+i>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function K(e,t,r,i,n){if(!n){j(e,t,r,4,34028234663852886e22,-34028234663852886e22)}a.write(e,t,r,i,23,4);return r+4}p.prototype.writeFloatLE=function e(t,r,i){return K(this,t,r,true,i)};p.prototype.writeFloatBE=function e(t,r,i){return K(this,t,r,false,i)};function F(e,t,r,i,n){if(!n){j(e,t,r,8,17976931348623157e292,-17976931348623157e292)}a.write(e,t,r,i,52,8);return r+8}p.prototype.writeDoubleLE=function e(t,r,i){return F(this,t,r,true,i)};p.prototype.writeDoubleBE=function e(t,r,i){return F(this,t,r,false,i)};p.prototype.copy=function e(t,r,i,n){if(!i)i=0;if(!n&&n!==0)n=this.length;if(r>=t.length)r=t.length;if(!r)r=0;if(n>0&&n<i)n=i;if(n===i)return 0;if(t.length===0||this.length===0)return 0;if(r<0){throw new RangeError("targetStart out of bounds")}if(i<0||i>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");if(n>this.length)n=this.length;if(t.length-r<n-i){n=t.length-r+i}var a=n-i;var o;if(this===t&&i<r&&r<n){for(o=a-1;o>=0;--o){t[o+r]=this[o+i]}}else if(a<1e3||!p.TYPED_ARRAY_SUPPORT){for(o=0;o<a;++o){t[o+r]=this[o+i]}}else{Uint8Array.prototype.set.call(t,this.subarray(i,i+a),r)}return a};p.prototype.fill=function e(t,r,i,n){if(typeof t==="string"){if(typeof r==="string"){n=r;r=0;i=this.length}else if(typeof i==="string"){n=i;i=this.length}if(t.length===1){var a=t.charCodeAt(0);if(a<256){t=a}}if(n!==undefined&&typeof n!=="string"){throw new TypeError("encoding must be a string")}if(typeof n==="string"&&!p.isEncoding(n)){throw new TypeError("Unknown encoding: "+n)}}else if(typeof t==="number"){t=t&255}if(r<0||this.length<r||this.length<i){throw new RangeError("Out of range index")}if(i<=r){return this}r=r>>>0;i=i===undefined?this.length:i>>>0;if(!t)t=0;var o;if(typeof t==="number"){for(o=r;o<i;++o){this[o]=t}}else{var s=p.isBuffer(t)?t:X(new p(t,n).toString());var u=s.length;for(o=0;o<i-r;++o){this[o+r]=s[o%u]}}return this};var H=/[^+\/0-9A-Za-z-_]/g;function V(e){e=G(e).replace(H,"");if(e.length<2)return"";while(e.length%4!==0){e=e+"="}return e}function G(e){if(e.trim)return e.trim();return e.replace(/^\s+|\s+$/g,"")}function W(e){if(e<16)return"0"+e.toString(16);return e.toString(16)}function X(e,t){t=t||Infinity;var r;var i=e.length;var n=null;var a=[];for(var o=0;o<i;++o){r=e.charCodeAt(o);if(r>55295&&r<57344){if(!n){if(r>56319){if((t-=3)>-1)a.push(239,191,189);continue}else if(o+1===i){if((t-=3)>-1)a.push(239,191,189);continue}n=r;continue}if(r<56320){if((t-=3)>-1)a.push(239,191,189);n=r;continue}r=(n-55296<<10|r-56320)+65536}else if(n){if((t-=3)>-1)a.push(239,191,189)}n=null;if(r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,r&63|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,r&63|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,r&63|128)}else{throw new Error("Invalid code point")}}return a}function Y(e){var t=[];for(var r=0;r<e.length;++r){t.push(e.charCodeAt(r)&255)}return t}function J(e,t){var r,i,n;var a=[];for(var o=0;o<e.length;++o){if((t-=2)<0)break;r=e.charCodeAt(o);i=r>>8;n=r%256;a.push(n);a.push(i)}return a}function Q(e){return i.toByteArray(V(e))}function $(e,t,r,i){for(var n=0;n<i;++n){if(n+r>=t.length||n>=e.length)break;t[n+r]=e[n]}return n}function Z(e){return e!==e}}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"base64-js":1,ieee754:5,isarray:7}],7:[function(e,t,r){var i={}.toString;t.exports=Array.isArray||function(e){return i.call(e)=="[object Array]"}},{}],5:[function(e,t,r){r.read=function(e,t,r,i,n){var a,o;var s=n*8-i-1;var u=(1<<s)-1;var c=u>>1;var l=-7;var f=r?n-1:0;var p=r?-1:1;var h=e[t+f];f+=p;a=h&(1<<-l)-1;h>>=-l;l+=s;for(;l>0;a=a*256+e[t+f],f+=p,l-=8){}o=a&(1<<-l)-1;a>>=-l;l+=i;for(;l>0;o=o*256+e[t+f],f+=p,l-=8){}if(a===0){a=1-c}else if(a===u){return o?NaN:(h?-1:1)*Infinity}else{o=o+Math.pow(2,i);a=a-c}return(h?-1:1)*o*Math.pow(2,a-i)};r.write=function(e,t,r,i,n,a){var o,s,u;var c=a*8-n-1;var l=(1<<c)-1;var f=l>>1;var p=n===23?Math.pow(2,-24)-Math.pow(2,-77):0;var h=i?0:a-1;var d=i?1:-1;var m=t<0||t===0&&1/t<0?1:0;t=Math.abs(t);if(isNaN(t)||t===Infinity){s=isNaN(t)?1:0;o=l}else{o=Math.floor(Math.log(t)/Math.LN2);if(t*(u=Math.pow(2,-o))<1){o--;u*=2}if(o+f>=1){t+=p/u}else{t+=p*Math.pow(2,1-f)}if(t*u>=2){o++;u/=2}if(o+f>=l){s=0;o=l}else if(o+f>=1){s=(t*u-1)*Math.pow(2,n);o=o+f}else{s=t*Math.pow(2,f-1)*Math.pow(2,n);o=0}}for(;n>=8;e[r+h]=s&255,h+=d,s/=256,n-=8){}o=o<<n|s;c+=n;for(;c>0;e[r+h]=o&255,h+=d,o/=256,c-=8){}e[r+h-d]|=m*128}},{}],1:[function(e,t,r){"use strict";r.byteLength=o;r.toByteArray=s;r.fromByteArray=m;var u=[];var c=[];var l=typeof Uint8Array!=="undefined"?Uint8Array:Array;var i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(var n=0,a=i.length;n<a;++n){u[n]=i[n];c[i.charCodeAt(n)]=n}c["-".charCodeAt(0)]=62;c["_".charCodeAt(0)]=63;function f(e){var t=e.length;if(t%4>0){throw new Error("Invalid string. Length must be a multiple of 4")}var r=e.indexOf("=");if(r===-1)r=t;var i=r===t?0:4-r%4;return[r,i]}function o(e){var t=f(e);var r=t[0];var i=t[1];return(r+i)*3/4-i}function p(e,t,r){return(t+r)*3/4-r}function s(e){var t;var r=f(e);var i=r[0];var n=r[1];var a=new l(p(e,i,n));var o=0;var s=n>0?i-4:i;for(var u=0;u<s;u+=4){t=c[e.charCodeAt(u)]<<18|c[e.charCodeAt(u+1)]<<12|c[e.charCodeAt(u+2)]<<6|c[e.charCodeAt(u+3)];a[o++]=t>>16&255;a[o++]=t>>8&255;a[o++]=t&255}if(n===2){t=c[e.charCodeAt(u)]<<2|c[e.charCodeAt(u+1)]>>4;a[o++]=t&255}if(n===1){t=c[e.charCodeAt(u)]<<10|c[e.charCodeAt(u+1)]<<4|c[e.charCodeAt(u+2)]>>2;a[o++]=t>>8&255;a[o++]=t&255}return a}function h(e){return u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[e&63]}function d(e,t,r){var i;var n=[];for(var a=t;a<r;a+=3){i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(e[a+2]&255);n.push(h(i))}return n.join("")}function m(e){var t;var r=e.length;var i=r%3;var n=[];var a=16383;for(var o=0,s=r-i;o<s;o+=a){n.push(d(e,o,o+a>s?s:o+a))}if(i===1){t=e[r-1];n.push(u[t>>2]+u[t<<4&63]+"==")}else if(i===2){t=(e[r-2]<<8)+e[r-1];n.push(u[t>>10]+u[t>>4&63]+u[t<<2&63]+"=")}return n.join("")}},{}]},{},[28]);OOS.apiLoader.services["s3"]={};OOS.S3=OOS.Service.defineService("s3",["2006-03-01"]);_xamzrequire=function a(o,s,u){function c(r,e){if(!s[r]){if(!o[r]){var t=typeof _xamzrequire=="function"&&_xamzrequire;if(!e&&t)return t(r,!0);if(l)return l(r,!0);var i=new Error("Cannot find module '"+r+"'");throw i.code="MODULE_NOT_FOUND",i}var n=s[r]={exports:{}};o[r][0].call(n.exports,function(e){var t=o[r][1][e];return c(t?t:e)},n,n.exports,a,o,s,u)}return s[r].exports}var l=typeof _xamzrequire=="function"&&_xamzrequire;for(var e=0;e<u.length;e++)c(u[e]);return c}({99:[function(e,t,r){var d=e("../core");var m=e("../signers/v4_credentials");e("../s3/managed_upload");var n={completeMultipartUpload:true,copyObject:true,uploadPartCopy:true};var c=["AuthorizationHeaderMalformed","BadRequest","PermanentRedirect",301];d.util.update(d.S3.prototype,{getSignatureVersion:function e(t){var r=this.api.signatureVersion;var i=this._originalConfig?this._originalConfig.signatureVersion:null;var n=this.config.signatureVersion;var a=t?t.isPresigned():false;if(i){i=i==="v2"?"s3":i;return i}if(a!==true){r="v4"}else if(n){r=n}return r},getSignerClass:function e(t){var r=this.getSignatureVersion(t);return d.Signers.RequestSigner.getVersion(r)},validateService:function e(){var t;var r=[];if(!this.config.region)this.config.region="cn";if(!this.config.endpoint&&this.config.s3BucketEndpoint){r.push("An endpoint must be provided when configuring "+"`s3BucketEndpoint` to true.")}if(r.length===1){t=r[0]}else if(r.length>1){t="Multiple configuration errors:\n"+r.join("\n")}if(t){throw d.util.error(new Error,{name:"InvalidEndpoint",message:t})}},shouldDisableBodySigning:function e(t){var r=this.getSignerClass();if(this.config.s3DisableBodySigning===true&&r===d.Signers.V4&&t.httpRequest.endpoint.protocol==="https:"){return true}return false},setupRequestListeners:function e(t){t.addListener("validate",this.validateScheme);t.addListener("validate",this.validateBucketEndpoint);t.addListener("validate",this.correctBucketRegionFromCache);t.addListener("validate",this.validateBucketName);t.addListener("build",this.addContentType);t.addListener("build",this.populateURI);t.addListener("build",this.computeContentMd5);t.addListener("build",this.computeSseCustomerKeyMd5);t.addListener("afterBuild",this.addExpect100Continue);t.removeListener("validate",d.EventListeners.Core.VALIDATE_REGION);t.addListener("extractError",this.extractError);t.onAsync("extractError",this.requestBucketRegion);t.addListener("extractData",this.extractData);t.addListener("extractData",d.util.hoistPayloadMember);t.addListener("beforePresign",this.prepareSignedUrl);if(d.util.isBrowser()){t.onAsync("retry",this.reqRegionForNetworkingError)}if(this.shouldDisableBodySigning(t)){t.removeListener("afterBuild",d.EventListeners.Core.COMPUTE_SHA256);t.addListener("afterBuild",this.disableBodySigning)}},validateScheme:function(e){var t=e.params,r=e.httpRequest.endpoint.protocol,i=t.SSECustomerKey||t.CopySourceSSECustomerKey;if(i&&r!=="https:"){var n="Cannot send SSE keys over HTTP. Set 'sslEnabled'"+"to 'true' in your configuration";throw d.util.error(new Error,{code:"ConfigError",message:n})}},validateBucketEndpoint:function(e){if(!e.params.Bucket&&e.service.config.s3BucketEndpoint){var t="Cannot send requests to root API with `s3BucketEndpoint` set.";throw d.util.error(new Error,{code:"ConfigError",message:t})}},validateBucketName:function e(t){var r=t.service;var i=r.getSignatureVersion(t);if(i!=="v4"){return}var n=t.params&&t.params.Bucket;var a=t.params&&t.params.Key;var o=n&&n.indexOf("/");if(n&&o>=0){if(typeof a==="string"){t.params=d.util.copy(t.params);var s=n.substr(o+1)||"";t.params.Key=s+"/"+a;t.params.Bucket=n.substr(0,o)}else{var u="Bucket names cannot contain forward slashes. Bucket: "+n;throw d.util.error(new Error,{code:"InvalidBucket",message:u})}}},isValidAccelerateOperation:function e(t){var r=["createBucket","deleteBucket","listBuckets"];return r.indexOf(t)===-1},populateURI:function e(t){var r=t.httpRequest;var i=t.params.Bucket;var n=t.service;var a=r.endpoint;if(i){if(!n.pathStyleBucketName(i)){if(n.config.useAccelerateEndpoint&&n.isValidAccelerateOperation(t.operation)){if(n.config.useDualstack){a.hostname=i+".s3-accelerate.dualstack.amazonaws.com"}else{a.hostname=i+".s3-accelerate.amazonaws.com"}}else if(!n.config.s3BucketEndpoint){a.hostname=i+"."+a.hostname}var o=a.port;if(o!==80&&o!==443){a.host=a.hostname+":"+a.port}else{a.host=a.hostname}r.virtualHostedBucket=i;n.removeVirtualHostedBucketFromPath(t)}}},removeVirtualHostedBucketFromPath:function e(t){var r=t.httpRequest;var i=r.virtualHostedBucket;if(i&&r.path){r.path=r.path.replace(new RegExp("/"+i),"");if(r.path[0]!=="/"){r.path="/"+r.path}}},addExpect100Continue:function e(t){var r=t.httpRequest.headers["Content-Length"];if(d.util.isNode()&&r>=1024*1024){t.httpRequest.headers["Expect"]="100-continue"}},addContentType:function e(t){var r=t.httpRequest;if(r.method==="GET"||r.method==="HEAD"){delete r.headers["Content-Type"];return}if(!r.headers["Content-Type"]){r.headers["Content-Type"]="application/octet-stream"}var i=r.headers["Content-Type"];if(d.util.isBrowser()){if(typeof r.body==="string"&&!i.match(/;\s*charset=/)){var n="; charset=UTF-8";r.headers["Content-Type"]+=n}else{var a=function(e,t,r){return t+r.toUpperCase()};r.headers["Content-Type"]=i.replace(/(;\s*charset=)(.+)$/,a)}}},computableChecksumOperations:{putBucketCors:true,putBucketLifecycle:true,putBucketLifecycleConfiguration:true,putBucketTagging:true,deleteObjects:true,putBucketReplication:true},willComputeChecksums:function e(t){if(this.computableChecksumOperations[t.operation])return true;if(!this.config.computeChecksums)return false;if(!d.util.Buffer.isBuffer(t.httpRequest.body)&&typeof t.httpRequest.body!=="string"){return false}var r=t.service.api.operations[t.operation].input.members;if(t.service.shouldDisableBodySigning(t)&&!Object.prototype.hasOwnProperty.call(t.httpRequest.headers,"presigned-expires")){if(r.ContentMD5&&!t.params.ContentMD5){return true}}if(t.service.getSignerClass(t)===d.Signers.V4){if(r.ContentMD5&&!r.ContentMD5.required)return false}if(r.ContentMD5&&!t.params.ContentMD5)return true},computeContentMd5:function e(t){if(t.service.willComputeChecksums(t)){var r=d.util.crypto.md5(t.httpRequest.body,"base64");t.httpRequest.headers["Content-MD5"]=r}},computeSseCustomerKeyMd5:function e(i){var t={SSECustomerKey:"x-amz-server-side-encryption-customer-key-MD5",CopySourceSSECustomerKey:"x-amz-copy-source-server-side-encryption-customer-key-MD5"};d.util.each(t,function(e,t){if(i.params[e]){var r=d.util.crypto.md5(i.params[e],"base64");i.httpRequest.headers[t]=r}})},pathStyleBucketName:function e(t){if(this.config.s3ForcePathStyle)return true;if(this.config.s3BucketEndpoint)return false;if(this.dnsCompatibleBucketName(t)){return this.config.sslEnabled&&t.match(/\./)?true:false}else{return true}},dnsCompatibleBucketName:function e(t){var r=t;var i=new RegExp(/^[a-z0-9][a-z0-9\.\-]{1,61}[a-z0-9]$/);var n=new RegExp(/(\d+\.){3}\d+/);var a=new RegExp(/\.\./);return r.match(i)&&!r.match(n)&&!r.match(a)?true:false},successfulResponse:function e(t){var r=t.request;var i=t.httpResponse;if(n[r.operation]&&i.body.toString().match("<Error>")){return false}else{return i.statusCode<300}},retryableError:function e(t,r){if(n[r.operation]&&t.statusCode===200){return true}else if(r._requestRegionForBucket&&r.service.bucketRegionCache[r._requestRegionForBucket]){return false}else if(t&&t.code==="RequestTimeout"){return true}else if(t&&c.indexOf(t.code)!=-1&&t.region&&t.region!=r.httpRequest.region){r.httpRequest.region=t.region;if(t.statusCode===301){r.service.updateReqBucketRegion(r)}return true}else{var i=d.Service.prototype.retryableError;return i.call(this,t,r)}},updateReqBucketRegion:function e(t,r){var i=t.httpRequest;if(typeof r==="string"&&r.length){i.region=r}if(!i.endpoint.host.match(/s3(?!-accelerate).*\.amazonaws\.com$/)){return}var n=t.service;var a=n.config;var o=a.s3BucketEndpoint;if(o){delete a.s3BucketEndpoint}var s=d.util.copy(a);delete s.endpoint;s.region=i.region;i.endpoint=new d.S3(s).endpoint;n.populateURI(t);a.s3BucketEndpoint=o;i.headers.Host=i.endpoint.host;if(t._asm.currentState==="validate"){t.removeListener("build",n.populateURI);t.addListener("build",n.removeVirtualHostedBucketFromPath)}},extractData:function e(t){var r=t.request;if(r.operation==="getBucketLocation"){var i=t.httpResponse.body.toString().match(/>(.+)<\/Location/);delete t.data["_"];if(i){t.data.LocationConstraint=i[1]}else{t.data.LocationConstraint=""}}var n=r.params.Bucket||null;if(r.operation==="deleteBucket"&&typeof n==="string"&&!t.error){r.service.clearBucketRegionCache(n)}else{var a=t.httpResponse.headers||{};var o=a["x-amz-bucket-region"]||null;if(!o&&r.operation==="createBucket"&&!t.error){var s=r.params.CreateBucketConfiguration;if(!s){o="cn"}else if(s.LocationConstraint==="EU"){o="eu-west-1"}else{o=s.LocationConstraint}}if(o){if(n&&o!==r.service.bucketRegionCache[n]){r.service.bucketRegionCache[n]=o}}}r.service.extractRequestIds(t)},extractError:function e(t){var r={304:"NotModified",403:"Forbidden",400:"BadRequest",404:"NotFound"};var i=t.request;var n=t.httpResponse.statusCode;var a=t.httpResponse.body||"";var o=t.httpResponse.headers||{};var s=o["x-amz-bucket-region"]||null;var u=i.params.Bucket||null;var c=i.service.bucketRegionCache;if(s&&u&&s!==c[u]){c[u]=s}var l;if(r[n]&&a.length===0){if(u&&!s){l=c[u]||null;if(l!==i.httpRequest.region){s=l}}t.error=d.util.error(new Error,{code:r[n],message:null,region:s})}else{var f=(new d.XML.Parser).parse(a.toString());if(f.Region&&!s){s=f.Region;if(u&&s!==c[u]){c[u]=s}}else if(u&&!s&&!f.Region){l=c[u]||null;if(l!==i.httpRequest.region){s=l}}t.error=d.util.error(new Error,{code:f.Code||n,message:f.Message||null,region:s})}i.service.extractRequestIds(t)},requestBucketRegion:function e(t,r){var i=t.error;var n=t.request;var a=n.params.Bucket||null;if(!i||!a||i.region||n.operation==="listObjects"||d.util.isNode()&&n.operation==="headBucket"||i.statusCode===400&&n.operation!=="headObject"||c.indexOf(i.code)===-1){return r()}var o=d.util.isNode()?"headBucket":"listObjects";var s={Bucket:a};if(o==="listObjects")s.MaxKeys=0;var u=n.service[o](s);u._requestRegionForBucket=a;u.send(function(){var e=n.service.bucketRegionCache[a]||null;i.region=e;r()})},reqRegionForNetworkingError:function e(t,r){if(!d.util.isBrowser()){return r()}var i=t.error;var n=t.request;var a=n.params.Bucket;if(!i||i.code!=="NetworkingError"||!a||n.httpRequest.region==="cn"){return r()}var o=n.service;var s=o.bucketRegionCache;var u=s[a]||null;if(u&&u!==n.httpRequest.region){o.updateReqBucketRegion(n,u);r()}else if(!o.dnsCompatibleBucketName(a)){o.updateReqBucketRegion(n,"cn");if(s[a]!=="cn"){s[a]="cn"}r()}else if(n.httpRequest.virtualHostedBucket){var c=o.listObjects({Bucket:a,MaxKeys:0});o.updateReqBucketRegion(c,"cn");c._requestRegionForBucket=a;c.send(function(){var e=o.bucketRegionCache[a]||null;if(e&&e!==n.httpRequest.region){o.updateReqBucketRegion(n,e)}r()})}else{r()}},bucketRegionCache:{},clearBucketRegionCache:function(e){var t=this.bucketRegionCache;if(!e){e=Object.keys(t)}else if(typeof e==="string"){e=[e]}for(var r=0;r<e.length;r++){delete t[e[r]]}return t},correctBucketRegionFromCache:function e(t){var r=t.params.Bucket||null;if(r){var i=t.service;var n=t.httpRequest.region;var a=i.bucketRegionCache[r];if(a&&a!==n){i.updateReqBucketRegion(t,a)}}},extractRequestIds:function e(t){var r=t.httpResponse.headers?t.httpResponse.headers["x-amz-id-2"]:null;var i=t.httpResponse.headers?t.httpResponse.headers["x-amz-cf-id"]:null;t.extendedRequestId=r;t.cfId=i;if(t.error){t.error.requestId=t.requestId||null;t.error.extendedRequestId=r;t.error.cfId=i}},getSignedUrl:function e(t,r,i){r=d.util.copy(r||{});var n=r.Expires||900;delete r.Expires;var a=this.makeRequest(t,r);if(i){d.util.defer(function(){a.presign(n,i)})}else{return a.presign(n,i)}},createPresignedPost:function e(t,r){if(typeof t==="function"&&r===undefined){r=t;t=null}t=d.util.copy(t||{});var i=this.config.params||{};var n=t.Bucket||i.Bucket,a=this,o=this.config,s=d.util.copy(this.endpoint);if(!o.s3BucketEndpoint){s.pathname="/"+n}function u(){return{url:d.util.urlFormat(s),fields:a.preparePostFields(o.credentials,o.region,n,t.Fields,t.Conditions,t.Expires)}}if(r){o.getCredentials(function(e){if(e){r(e)}r(null,u())})}else{return u()}},preparePostFields:function e(t,r,i,n,a,o){var s=this.getSkewCorrectedDate();if(!t||!r||!i){throw new Error("Unable to create a POST object policy without a bucket,"+" region, and credentials")}n=d.util.copy(n||{});a=(a||[]).slice(0);o=o||3600;var u=d.util.date.iso8601(s).replace(/[:\-]|\.\d{3}/g,"");var c=u.substr(0,8);var l=m.createScope(c,r,"s3");var f=t.accessKeyId+"/"+l;n["bucket"]=i;n["X-Amz-Algorithm"]="AWS4-HMAC-SHA256";n["X-Amz-Credential"]=f;n["X-Amz-Date"]=u;if(t.sessionToken){n["X-Amz-Security-Token"]=t.sessionToken}for(var p in n){if(n.hasOwnProperty(p)){var h={};h[p]=n[p];a.push(h)}}n.Policy=this.preparePostPolicy(new Date(s.valueOf()+o*1e3),a);n["X-Amz-Signature"]=d.util.crypto.hmac(m.getSigningKey(t,c,r,"s3",true),n.Policy,"hex");return n},preparePostPolicy:function e(t,r){return d.util.base64.encode(JSON.stringify({expiration:d.util.date.iso8601(t),conditions:r}))},prepareSignedUrl:function e(t){t.addListener("validate",t.service.noPresignedContentLength);t.removeListener("build",t.service.addContentType);if(!t.params.Body){t.removeListener("build",t.service.computeContentMd5)}else{t.addListener("afterBuild",d.EventListeners.Core.COMPUTE_SHA256)}},disableBodySigning:function e(t){var r=t.httpRequest.headers;if(!Object.prototype.hasOwnProperty.call(r,"presigned-expires")){r["X-Amz-Content-Sha256"]="UNSIGNED-PAYLOAD"}},noPresignedContentLength:function e(t){if(t.params.ContentLength!==undefined){throw d.util.error(new Error,{code:"UnexpectedParameter",message:"ContentLength is not supported in pre-signed URLs."})}},createBucket:function e(t,r){if(typeof t==="function"||!t){r=r||t;t={}}var i=this.endpoint.hostname;if(i!==this.api.globalEndpoint&&!t.CreateBucketConfiguration){t.CreateBucketConfiguration={LocationConstraint:this.config.region}}return this.makeRequest("createBucket",t,r)},upload:function e(t,r,i){if(typeof r==="function"&&i===undefined){i=r;r=null}r=r||{};r=d.util.merge(r||{},{service:this,params:t});var n=new d.S3.ManagedUpload(r);if(typeof i==="function")n.send(i);return n}})},{"../core":38,"../s3/managed_upload":83,"../signers/v4_credentials":110}],83:[function(e,t,r){var s=e("../core");var a=s.util.string.byteLength;var o=s.util.Buffer;s.S3.ManagedUpload=s.util.inherit({constructor:function e(t){var r=this;s.SequentialExecutor.call(r);r.body=null;r.sliceFn=null;r.callback=null;r.parts={};r.completeInfo=[];r.fillQueue=function(){r.callback(new Error("Unsupported body payload "+typeof r.body))};r.configure(t)},configure:function e(t){t=t||{};this.partSize=this.minPartSize;if(t.queueSize)this.queueSize=t.queueSize;if(t.partSize)this.partSize=t.partSize;if(t.leavePartsOnError)this.leavePartsOnError=true;if(t.tags){if(!Array.isArray(t.tags)){throw new Error("Tags must be specified as an array; "+typeof t.tags+" provided.")}this.tags=t.tags}if(this.partSize<this.minPartSize){throw new Error("partSize must be greater than "+this.minPartSize)}this.service=t.service;this.bindServiceObject(t.params);this.validateBody();this.adjustTotalBytes()},leavePartsOnError:false,queueSize:4,partSize:null,minPartSize:1024*1024*5,maxTotalParts:1e4,send:function(e){var t=this;t.failed=false;t.callback=e||function(e){if(e)throw e};var r=true;if(t.sliceFn){t.fillQueue=t.fillBuffer}else if(s.util.isNode()){var i=s.util.stream.Stream;if(t.body instanceof i){r=false;t.fillQueue=t.fillStream;t.partBuffers=[];t.body.on("error",function(e){t.cleanup(e)}).on("readable",function(){t.fillQueue()}).on("end",function(){t.isDoneChunking=true;t.numParts=t.totalPartNumbers;t.fillQueue.call(t);if(t.isDoneChunking&&t.totalPartNumbers>=1&&t.doneParts===t.numParts){t.finishMultiPart()}})}}if(r)t.fillQueue.call(t)},abort:function(){this.cleanup(s.util.error(new Error("Request aborted by user"),{code:"RequestAbortedError",retryable:false}))},validateBody:function e(){var t=this;t.body=t.service.config.params.Body;if(typeof t.body==="string"){t.body=new s.util.Buffer(t.body)}else if(!t.body){throw new Error("params.Body is required")}t.sliceFn=s.util.arraySliceFn(t.body)},bindServiceObject:function e(t){t=t||{};var r=this;if(!r.service){r.service=new s.S3({params:t})}else{var i=r.service;var n=s.util.copy(i.config);n.signatureVersion=i.getSignatureVersion();r.service=new i.constructor.__super__(n);r.service.config.params=s.util.merge(r.service.config.params||{},t)}},adjustTotalBytes:function e(){var t=this;try{t.totalBytes=a(t.body)}catch(e){}if(t.totalBytes){var r=Math.ceil(t.totalBytes/t.maxTotalParts);if(r>t.partSize)t.partSize=r}else{t.totalBytes=undefined}},isDoneChunking:false,partPos:0,totalChunkedBytes:0,totalUploadedBytes:0,totalBytes:undefined,numParts:0,totalPartNumbers:0,activeParts:0,doneParts:0,parts:null,completeInfo:null,failed:false,multipartReq:null,partBuffers:null,partBufferLength:0,fillBuffer:function e(){var t=this;var r=a(t.body);if(r===0){t.isDoneChunking=true;t.numParts=1;t.nextChunk(t.body);return}while(t.activeParts<t.queueSize&&t.partPos<r){var i=Math.min(t.partPos+t.partSize,r);var n=t.sliceFn.call(t.body,t.partPos,i);t.partPos+=t.partSize;if(a(n)<t.partSize||t.partPos===r){t.isDoneChunking=true;t.numParts=t.totalPartNumbers+1}t.nextChunk(n)}},fillStream:function e(){var t=this;if(t.activeParts>=t.queueSize)return;var r=t.body.read(t.partSize-t.partBufferLength)||t.body.read();if(r){t.partBuffers.push(r);t.partBufferLength+=r.length;t.totalChunkedBytes+=r.length}if(t.partBufferLength>=t.partSize){var i=t.partBuffers.length===1?t.partBuffers[0]:o.concat(t.partBuffers);t.partBuffers=[];t.partBufferLength=0;if(i.length>t.partSize){var n=i.slice(t.partSize);t.partBuffers.push(n);t.partBufferLength+=n.length;i=i.slice(0,t.partSize)}t.nextChunk(i)}if(t.isDoneChunking&&!t.isDoneSending){i=t.partBuffers.length===1?t.partBuffers[0]:o.concat(t.partBuffers);t.partBuffers=[];t.partBufferLength=0;t.totalBytes=t.totalChunkedBytes;t.isDoneSending=true;if(t.numParts===0||i.length>0){t.numParts++;t.nextChunk(i)}}t.body.read(0)},nextChunk:function e(t){var r=this;if(r.failed)return null;var i=++r.totalPartNumbers;if(r.isDoneChunking&&i===1){var n={Body:t};if(this.tags){n.Tagging=this.getTaggingHeader()}var a=r.service.putObject(n);a._managedUpload=r;a.on("httpUploadProgress",r.progress).send(r.finishSinglePart);return null}else if(r.service.config.params.ContentMD5){var o=s.util.error(new Error("The Content-MD5 you specified is invalid for multi-part uploads."),{code:"InvalidDigest",retryable:false});r.cleanup(o);return null}if(r.completeInfo[i]&&r.completeInfo[i].ETag!==null){return null}r.activeParts++;if(!r.service.config.params.UploadId){if(!r.multipartReq){r.multipartReq=r.service.createMultipartUpload();r.multipartReq.on("success",function(e){r.service.config.params.UploadId=e.data.UploadId;r.multipartReq=null});r.queueChunks(t,i);r.multipartReq.on("error",function(e){r.cleanup(e)});r.multipartReq.send()}else{r.queueChunks(t,i)}}else{r.uploadPart(t,i)}},getTaggingHeader:function e(){var t=[];for(var r=0;r<this.tags.length;r++){t.push(s.util.uriEscape(this.tags[r].Key)+"="+s.util.uriEscape(this.tags[r].Value))}return t.join("&")},uploadPart:function e(t,r){var i=this;var n={Body:t,ContentLength:s.util.string.byteLength(t),PartNumber:r};var a={ETag:null,PartNumber:r};i.completeInfo[r]=a;var o=i.service.uploadPart(n);i.parts[r]=o;o._lastUploadedBytes=0;o._managedUpload=i;o.on("httpUploadProgress",i.progress);o.send(function(e,t){delete i.parts[n.PartNumber];i.activeParts--;if(!e&&(!t||!t.ETag)){var r="No access to ETag property on response.";if(s.util.isBrowser()){r+=" Check CORS configuration to expose ETag header."}e=s.util.error(new Error(r),{code:"ETagMissing",retryable:false})}if(e)return i.cleanup(e);a.ETag=t.ETag;i.doneParts++;if(i.isDoneChunking&&i.doneParts===i.numParts){i.finishMultiPart()}else{i.fillQueue.call(i)}})},queueChunks:function e(t,r){var i=this;i.multipartReq.on("success",function(){i.uploadPart(t,r)})},cleanup:function e(t){var r=this;if(r.failed)return;if(typeof r.body.removeAllListeners==="function"&&typeof r.body.resume==="function"){r.body.removeAllListeners("readable");r.body.removeAllListeners("end");r.body.resume()}if(r.multipartReq){r.multipartReq.removeAllListeners("success");r.multipartReq.removeAllListeners("error");r.multipartReq.removeAllListeners("complete");delete r.multipartReq}if(r.service.config.params.UploadId&&!r.leavePartsOnError){r.service.abortMultipartUpload().send()}else if(r.leavePartsOnError){r.isDoneChunking=false}s.util.each(r.parts,function(e,t){t.removeAllListeners("complete");t.abort()});r.activeParts=0;r.partPos=0;r.numParts=0;r.totalPartNumbers=0;r.parts={};r.failed=true;r.callback(t)},finishMultiPart:function e(){var i=this;var t={MultipartUpload:{Parts:i.completeInfo.slice(1)}};i.service.completeMultipartUpload(t,function(e,r){if(e){return i.cleanup(e)}if(r&&typeof r.Location==="string"){r.Location=r.Location.replace(/%2F/g,"/")}if(Array.isArray(i.tags)){i.service.putObjectTagging({Tagging:{TagSet:i.tags}},function(e,t){if(e){i.callback(e)}else{i.callback(e,r)}})}else{i.callback(e,r)}})},finishSinglePart:function e(t,r){var i=this.request._managedUpload;var n=this.request.httpRequest;var a=n.endpoint;if(t)return i.callback(t);r.Location=[a.protocol,"//",a.host,n.path].join("");r.key=this.request.params.Key;r.Key=this.request.params.Key;r.Bucket=this.request.params.Bucket;i.callback(t,r)},progress:function e(t){var r=this._managedUpload;if(this.operation==="putObject"){t.part=1;t.key=this.params.Key}else{r.totalUploadedBytes+=t.loaded-this._lastUploadedBytes;this._lastUploadedBytes=t.loaded;t={loaded:r.totalUploadedBytes,total:r.totalBytes,part:this.params.PartNumber,key:this.params.Key}}r.emit("httpUploadProgress",[t])}});s.util.mixin(s.S3.ManagedUpload,s.SequentialExecutor);s.S3.ManagedUpload.addPromisesToClass=function e(t){this.prototype.promise=s.util.promisifyMethod("send",t)};s.S3.ManagedUpload.deletePromisesFromClass=function e(){delete this.prototype.promise};s.util.addPromises(s.S3.ManagedUpload);t.exports=s.S3.ManagedUpload},{"../core":38}]},{},[99]);OOS.apiLoader.services["s3"]["2006-03-01"]={version:"2.0",metadata:{apiVersion:"2006-03-01",checksumFormat:"md5",endpointPrefix:"s3",globalEndpoint:"s3.amazonaws.com",protocol:"rest-xml",serviceAbbreviation:"Amazon S3",serviceFullName:"Amazon Simple Storage Service",serviceId:"S3",signatureVersion:"s3",timestampFormat:"rfc822",uid:"s3-2006-03-01"},operations:{AbortMultipartUpload:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CompleteMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MultipartUpload:{locationName:"CompleteMultipartUpload",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{ETag:{},PartNumber:{type:"integer"}}},flattened:true}}},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"MultipartUpload"},output:{type:"structure",members:{Location:{},Bucket:{},Key:{},Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},CopyObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},MetadataDirective:{location:"header",locationName:"x-amz-metadata-directive"},TaggingDirective:{location:"header",locationName:"x-amz-tagging-directive"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1c",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"},DataLocation:{location:"header",locationName:"x-ctyun-data-location"}}},output:{type:"structure",members:{CopyObjectResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},Expiration:{location:"header",locationName:"x-amz-expiration"},CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},VersionId:{location:"header",locationName:"x-amz-version-id"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyObjectResult"},alias:"PutObjectCopy"},CreateBucket:{http:{method:"PUT",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CreateBucketConfiguration:{locationName:"CreateBucketConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{LocationConstraint:{}}},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"CreateBucketConfiguration"},output:{type:"structure",members:{Location:{location:"header",locationName:"Location"}}},alias:"PutBucket"},CreateMultipartUpload:{http:{requestUri:"/{Bucket}/{Key+}?uploads"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"},DataLocation:{location:"header",locationName:"x-ctyun-data-location"}}},output:{type:"structure",members:{AbortDate:{location:"header",locationName:"x-amz-abort-date",type:"timestamp"},AbortRuleId:{location:"header",locationName:"x-amz-abort-rule-id"},Bucket:{locationName:"Bucket"},Key:{},UploadId:{},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}},alias:"InitiateMultipartUpload"},DeleteBucket:{http:{method:"DELETE",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketAnalyticsConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketCors:{http:{method:"DELETE",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketEncryption:{http:{method:"DELETE",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketInventoryConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketLifecycle:{http:{method:"DELETE",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketMetricsConfiguration:{http:{method:"DELETE",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}}},DeleteBucketPolicy:{http:{method:"DELETE",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketReplication:{http:{method:"DELETE",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketTagging:{http:{method:"DELETE",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteBucketWebsite:{http:{method:"DELETE",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},DeleteObject:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MFA:{location:"header",locationName:"x-amz-mfa"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},VersionId:{location:"header",locationName:"x-amz-version-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},DeleteObjectTagging:{http:{method:"DELETE",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"}}},output:{type:"structure",members:{VersionId:{location:"header",locationName:"x-amz-version-id"}}}},DeleteObjects:{http:{requestUri:"/{Bucket}?delete"},input:{type:"structure",required:["Bucket","Delete"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delete:{locationName:"Delete",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Objects"],members:{Objects:{locationName:"Object",type:"list",member:{type:"structure",required:["Key"],members:{Key:{},VersionId:{}}},flattened:true},Quiet:{type:"boolean"}}},MFA:{location:"header",locationName:"x-amz-mfa"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Delete"},output:{type:"structure",members:{Deleted:{type:"list",member:{type:"structure",members:{Key:{},VersionId:{},DeleteMarker:{type:"boolean"},DeleteMarkerVersionId:{}}},flattened:true},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},Errors:{locationName:"Error",type:"list",member:{type:"structure",members:{Key:{},VersionId:{},Code:{},Message:{}}},flattened:true}}},alias:"DeleteMultipleObjects"},GetBucketAccelerateConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?accelerate"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Status:{},IPWhiteLists:{shape:"S50"}}}},GetBucketAcl:{http:{method:"GET",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Owner:{shape:"S2v"},Grants:{shape:"S2y",locationName:"AccessControlList"}}}},GetBucketAnalyticsConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{AnalyticsConfiguration:{shape:"S37"}},payload:"AnalyticsConfiguration"}},GetBucketCors:{http:{method:"GET",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{CORSRules:{shape:"S3n",locationName:"CORSRule"}}}},GetBucketEncryption:{http:{method:"GET",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{ServerSideEncryptionConfiguration:{shape:"S40"}},payload:"ServerSideEncryptionConfiguration"}},GetBucketInventoryConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{InventoryConfiguration:{shape:"S46"}},payload:"InventoryConfiguration"}},GetBucketLifecycle:{http:{method:"GET",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Rules:{shape:"S4m",locationName:"Rule"}}},deprecated:true},GetBucketLifecycleConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Rules:{shape:"S51",locationName:"Rule"}}}},GetBucketLocation:{http:{method:"GET",requestUri:"/{Bucket}?location"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LocationConstraint:{}}}},GetBucketLogging:{http:{method:"GET",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{LoggingEnabled:{shape:"S5b"}}}},GetBucketMetricsConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"}}},output:{type:"structure",members:{MetricsConfiguration:{shape:"S5j"}},payload:"MetricsConfiguration"}},GetBucketNotification:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S5m"},output:{shape:"S5n"},deprecated:true},GetBucketNotificationConfiguration:{http:{method:"GET",requestUri:"/{Bucket}?notification"},input:{shape:"S5m"},output:{shape:"S5y"}},GetBucketPolicy:{http:{method:"GET",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Policy:{}},payload:"Policy"}},GetBucketReplication:{http:{method:"GET",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{ReplicationConfiguration:{shape:"S6h"}},payload:"ReplicationConfiguration"}},GetBucketRequestPayment:{http:{method:"GET",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Payer:{}}}},GetBucketTagging:{http:{method:"GET",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",required:["TagSet"],members:{TagSet:{shape:"S3d"}}}},GetBucketVersioning:{http:{method:"GET",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{Status:{},MFADelete:{locationName:"MfaDelete"}}}},GetBucketWebsite:{http:{method:"GET",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},output:{type:"structure",members:{RedirectAllRequestsTo:{shape:"S75"},IndexDocument:{shape:"S78"},ErrorDocument:{shape:"S7a"},RoutingRules:{shape:"S7b"}}}},GetObject:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},ResponseCacheControl:{location:"querystring",locationName:"response-cache-control"},ResponseContentDisposition:{location:"querystring",locationName:"response-content-disposition"},ResponseContentEncoding:{location:"querystring",locationName:"response-content-encoding"},ResponseContentLanguage:{location:"querystring",locationName:"response-content-language"},ResponseContentType:{location:"querystring",locationName:"response-content-type"},ResponseExpires:{location:"querystring",locationName:"response-expires",type:"timestamp"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"}}},output:{type:"structure",members:{Body:{streaming:true,type:"blob"},DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},DataLocation:{location:"header",locationName:"x-ctyun-data-location",type:"string"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentRange:{location:"header",locationName:"Content-Range"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},MetaDataLocation:{location:"header",locationName:"x-ctyun-metadata-location",type:"string"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"},PartsCount:{location:"header",locationName:"x-amz-mp-parts-count",type:"integer"},TagCount:{location:"header",locationName:"x-amz-tagging-count",type:"integer"}},payload:"Body"}},GetObjectAcl:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Owner:{shape:"S2v"},Grants:{shape:"S2y",locationName:"AccessControlList"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},GetObjectTagging:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"}}},output:{type:"structure",required:["TagSet"],members:{VersionId:{location:"header",locationName:"x-amz-version-id"},TagSet:{shape:"S3d"}}}},GetObjectTorrent:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}?torrent"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{Body:{streaming:true,type:"blob"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"Body"}},HeadBucket:{http:{method:"HEAD",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}}},HeadObject:{http:{method:"HEAD",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},IfMatch:{location:"header",locationName:"If-Match"},IfModifiedSince:{location:"header",locationName:"If-Modified-Since",type:"timestamp"},IfNoneMatch:{location:"header",locationName:"If-None-Match"},IfUnmodifiedSince:{location:"header",locationName:"If-Unmodified-Since",type:"timestamp"},Key:{location:"uri",locationName:"Key"},Range:{location:"header",locationName:"Range"},VersionId:{location:"querystring",locationName:"versionId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"}}},output:{type:"structure",members:{DeleteMarker:{location:"header",locationName:"x-amz-delete-marker",type:"boolean"},AcceptRanges:{location:"header",locationName:"accept-ranges"},Expiration:{location:"header",locationName:"x-amz-expiration"},Restore:{location:"header",locationName:"x-amz-restore"},LastModified:{location:"header",locationName:"Last-Modified",type:"timestamp"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ETag:{location:"header",locationName:"ETag"},MissingMeta:{location:"header",locationName:"x-amz-missing-meta",type:"integer"},VersionId:{location:"header",locationName:"x-amz-version-id"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"},ReplicationStatus:{location:"header",locationName:"x-amz-replication-status"},PartsCount:{location:"header",locationName:"x-amz-mp-parts-count",type:"integer"}}}},ListBucketAnalyticsConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},ContinuationToken:{},NextContinuationToken:{},AnalyticsConfigurationList:{locationName:"AnalyticsConfiguration",type:"list",member:{shape:"S37"},flattened:true}}}},ListBucketInventoryConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{ContinuationToken:{},InventoryConfigurationList:{locationName:"InventoryConfiguration",type:"list",member:{shape:"S46"},flattened:true},IsTruncated:{type:"boolean"},NextContinuationToken:{}}}},ListBucketMetricsConfigurations:{http:{method:"GET",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContinuationToken:{location:"querystring",locationName:"continuation-token"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},ContinuationToken:{},NextContinuationToken:{},MetricsConfigurationList:{locationName:"MetricsConfiguration",type:"list",member:{shape:"S5j"},flattened:true}}}},ListBuckets:{http:{method:"GET"},output:{type:"structure",members:{Buckets:{type:"list",member:{locationName:"Bucket",type:"structure",members:{Name:{},CreationDate:{type:"timestamp"}}}},Owner:{shape:"S2v"}}},alias:"GetService"},ListMultipartUploads:{http:{method:"GET",requestUri:"/{Bucket}?uploads"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxUploads:{location:"querystring",locationName:"max-uploads",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},UploadIdMarker:{location:"querystring",locationName:"upload-id-marker"}}},output:{type:"structure",members:{Bucket:{},KeyMarker:{},UploadIdMarker:{},NextKeyMarker:{},Prefix:{},Delimiter:{},NextUploadIdMarker:{},MaxUploads:{type:"integer"},IsTruncated:{type:"boolean"},Uploads:{locationName:"Upload",type:"list",member:{type:"structure",members:{UploadId:{},Key:{},Initiated:{type:"timestamp"},StorageClass:{},Owner:{shape:"S2v"},Initiator:{shape:"S97"}}},flattened:true},CommonPrefixes:{shape:"S98"},EncodingType:{}}}},ListObjectVersions:{http:{method:"GET",requestUri:"/{Bucket}?versions"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},KeyMarker:{location:"querystring",locationName:"key-marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},VersionIdMarker:{location:"querystring",locationName:"version-id-marker"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},KeyMarker:{},VersionIdMarker:{},NextKeyMarker:{},NextVersionIdMarker:{},Versions:{locationName:"Version",type:"list",member:{type:"structure",members:{ETag:{},Size:{type:"integer"},StorageClass:{},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"},Owner:{shape:"S2v"}}},flattened:true},DeleteMarkers:{locationName:"DeleteMarker",type:"list",member:{type:"structure",members:{Owner:{shape:"S2v"},Key:{},VersionId:{},IsLatest:{type:"boolean"},LastModified:{type:"timestamp"}}},flattened:true},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{}}},alias:"GetBucketObjectVersions"},ListObjects:{http:{method:"GET",requestUri:"/{Bucket}"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},Marker:{location:"querystring",locationName:"marker"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},Marker:{},NextMarker:{},Contents:{shape:"S9q"},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{}}},alias:"GetBucket"},ListObjectsV2:{http:{method:"GET",requestUri:"/{Bucket}?list-type=2"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},Delimiter:{location:"querystring",locationName:"delimiter"},EncodingType:{location:"querystring",locationName:"encoding-type"},MaxKeys:{location:"querystring",locationName:"max-keys",type:"integer"},Prefix:{location:"querystring",locationName:"prefix"},ContinuationToken:{location:"querystring",locationName:"continuation-token"},FetchOwner:{location:"querystring",locationName:"fetch-owner",type:"boolean"},StartAfter:{location:"querystring",locationName:"start-after"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{IsTruncated:{type:"boolean"},Contents:{shape:"S9q"},Name:{},Prefix:{},Delimiter:{},MaxKeys:{type:"integer"},CommonPrefixes:{shape:"S98"},EncodingType:{},KeyCount:{type:"integer"},ContinuationToken:{},NextContinuationToken:{},StartAfter:{}}}},ListParts:{http:{method:"GET",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},MaxParts:{location:"querystring",locationName:"max-parts",type:"integer"},PartNumberMarker:{location:"querystring",locationName:"part-number-marker",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{AbortDate:{location:"header",locationName:"x-amz-abort-date",type:"timestamp"},AbortRuleId:{location:"header",locationName:"x-amz-abort-rule-id"},Bucket:{},Key:{},UploadId:{},PartNumberMarker:{type:"integer"},NextPartNumberMarker:{type:"integer"},MaxParts:{type:"integer"},IsTruncated:{type:"boolean"},Parts:{locationName:"Part",type:"list",member:{type:"structure",members:{PartNumber:{type:"integer"},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"}}},flattened:true},Initiator:{shape:"S97"},Owner:{shape:"S2v"},StorageClass:{},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutBucketAccelerateConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?accelerate"},input:{type:"structure",required:["Bucket","AccelerateConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},AccelerateConfiguration:{locationName:"AccelerateConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Status"],members:{Status:{},IPWhiteLists:{shape:"S50"}}}},payload:"AccelerateConfiguration"}},PutBucketAcl:{http:{method:"PUT",requestUri:"/{Bucket}?acl"},input:{type:"structure",required:["Bucket"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"Sa8",locationName:"AccessControlPolicy",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"}},payload:"AccessControlPolicy"}},PutBucketAnalyticsConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?analytics"},input:{type:"structure",required:["Bucket","Id","AnalyticsConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},AnalyticsConfiguration:{shape:"S37",locationName:"AnalyticsConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"AnalyticsConfiguration"}},PutBucketCors:{http:{method:"PUT",requestUri:"/{Bucket}?cors"},input:{type:"structure",required:["Bucket","CORSConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},CORSConfiguration:{locationName:"CORSConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["CORSRules"],members:{CORSRules:{shape:"S3n",locationName:"CORSRule"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"CORSConfiguration"}},PutBucketEncryption:{http:{method:"PUT",requestUri:"/{Bucket}?encryption"},input:{type:"structure",required:["Bucket","ServerSideEncryptionConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ServerSideEncryptionConfiguration:{shape:"S40",locationName:"ServerSideEncryptionConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"ServerSideEncryptionConfiguration"}},PutBucketInventoryConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?inventory"},input:{type:"structure",required:["Bucket","Id","InventoryConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},InventoryConfiguration:{shape:"S46",locationName:"InventoryConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"InventoryConfiguration"}},PutBucketLifecycle:{http:{method:"PUT",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},LifecycleConfiguration:{locationName:"LifecycleConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Rules"],members:{Rules:{shape:"S4m",locationName:"Rule"}}}},payload:"LifecycleConfiguration"},deprecated:true},PutBucketLifecycleConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?lifecycle"},input:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"},LifecycleConfiguration:{locationName:"LifecycleConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Rules"],members:{Rules:{shape:"S51",locationName:"Rule"}}}},payload:"LifecycleConfiguration"}},PutBucketLogging:{http:{method:"PUT",requestUri:"/{Bucket}?logging"},input:{type:"structure",required:["Bucket","BucketLoggingStatus"],members:{Bucket:{location:"uri",locationName:"Bucket"},BucketLoggingStatus:{locationName:"BucketLoggingStatus",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{LoggingEnabled:{shape:"S5b"}}},ContentMD5:{location:"header",locationName:"Content-MD5"}},payload:"BucketLoggingStatus"}},PutBucketMetricsConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?metrics"},input:{type:"structure",required:["Bucket","Id","MetricsConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},Id:{location:"querystring",locationName:"id"},MetricsConfiguration:{shape:"S5j",locationName:"MetricsConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"MetricsConfiguration"}},PutBucketNotification:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},NotificationConfiguration:{shape:"S5n",locationName:"NotificationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"NotificationConfiguration"},deprecated:true},PutBucketNotificationConfiguration:{http:{method:"PUT",requestUri:"/{Bucket}?notification"},input:{type:"structure",required:["Bucket","NotificationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},NotificationConfiguration:{shape:"S5y",locationName:"NotificationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"NotificationConfiguration"}},PutBucketPolicy:{http:{method:"PUT",requestUri:"/{Bucket}?policy"},input:{type:"structure",required:["Bucket","Policy"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ConfirmRemoveSelfBucketAccess:{location:"header",locationName:"x-amz-confirm-remove-self-bucket-access",type:"boolean"},Policy:{}},payload:"Policy"}},PutBucketReplication:{http:{method:"PUT",requestUri:"/{Bucket}?replication"},input:{type:"structure",required:["Bucket","ReplicationConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},ReplicationConfiguration:{shape:"S6h",locationName:"ReplicationConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"ReplicationConfiguration"}},PutBucketRequestPayment:{http:{method:"PUT",requestUri:"/{Bucket}?requestPayment"},input:{type:"structure",required:["Bucket","RequestPaymentConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},RequestPaymentConfiguration:{locationName:"RequestPaymentConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Payer"],members:{Payer:{}}}},payload:"RequestPaymentConfiguration"}},PutBucketTagging:{http:{method:"PUT",requestUri:"/{Bucket}?tagging"},input:{type:"structure",required:["Bucket","Tagging"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},Tagging:{shape:"Sau",locationName:"Tagging",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"Tagging"}},PutBucketVersioning:{http:{method:"PUT",requestUri:"/{Bucket}?versioning"},input:{type:"structure",required:["Bucket","VersioningConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},MFA:{location:"header",locationName:"x-amz-mfa"},VersioningConfiguration:{locationName:"VersioningConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{MFADelete:{locationName:"MfaDelete"},Status:{}}}},payload:"VersioningConfiguration"}},PutBucketWebsite:{http:{method:"PUT",requestUri:"/{Bucket}?website"},input:{type:"structure",required:["Bucket","WebsiteConfiguration"],members:{Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},WebsiteConfiguration:{locationName:"WebsiteConfiguration",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{ErrorDocument:{shape:"S7a"},IndexDocument:{shape:"S78"},RedirectAllRequestsTo:{shape:"S75"},RoutingRules:{shape:"S7b"}}}},payload:"WebsiteConfiguration"}},PutObject:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},Body:{streaming:true,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},CacheControl:{location:"header",locationName:"Cache-Control"},ContentDisposition:{location:"header",locationName:"Content-Disposition"},ContentEncoding:{location:"header",locationName:"Content-Encoding"},ContentLanguage:{location:"header",locationName:"Content-Language"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ContentMD5:{location:"header",locationName:"Content-MD5"},ContentType:{location:"header",locationName:"Content-Type"},Expires:{location:"header",locationName:"Expires",type:"timestamp"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},Metadata:{shape:"S11",location:"headers",locationName:"x-amz-meta-"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},StorageClass:{location:"header",locationName:"x-amz-storage-class"},WebsiteRedirectLocation:{location:"header",locationName:"x-amz-website-redirect-location"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},Tagging:{location:"header",locationName:"x-amz-tagging"},DataLocation:{location:"header",locationName:"x-ctyun-data-location"}},payload:"Body"},output:{type:"structure",members:{Expiration:{location:"header",locationName:"x-amz-expiration"},ETag:{location:"header",locationName:"ETag"},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},VersionId:{location:"header",locationName:"x-amz-version-id"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutObjectAcl:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}?acl"},input:{type:"structure",required:["Bucket","Key"],members:{ACL:{location:"header",locationName:"x-amz-acl"},AccessControlPolicy:{shape:"Sa8",locationName:"AccessControlPolicy",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}},Bucket:{location:"uri",locationName:"Bucket"},ContentMD5:{location:"header",locationName:"Content-MD5"},GrantFullControl:{location:"header",locationName:"x-amz-grant-full-control"},GrantRead:{location:"header",locationName:"x-amz-grant-read"},GrantReadACP:{location:"header",locationName:"x-amz-grant-read-acp"},GrantWrite:{location:"header",locationName:"x-amz-grant-write"},GrantWriteACP:{location:"header",locationName:"x-amz-grant-write-acp"},Key:{location:"uri",locationName:"Key"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"},VersionId:{location:"querystring",locationName:"versionId"}},payload:"AccessControlPolicy"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},PutObjectTagging:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}?tagging"},input:{type:"structure",required:["Bucket","Key","Tagging"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},ContentMD5:{location:"header",locationName:"Content-MD5"},Tagging:{shape:"Sau",locationName:"Tagging",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"}}},payload:"Tagging"},output:{type:"structure",members:{VersionId:{location:"header",locationName:"x-amz-version-id"}}}},RestoreObject:{http:{requestUri:"/{Bucket}/{Key+}?restore"},input:{type:"structure",required:["Bucket","Key"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},VersionId:{location:"querystring",locationName:"versionId"},RestoreRequest:{locationName:"RestoreRequest",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",members:{Days:{type:"integer"},GlacierJobParameters:{type:"structure",required:["Tier"],members:{Tier:{}}},Type:{},Tier:{},Description:{},SelectParameters:{type:"structure",required:["InputSerialization","ExpressionType","Expression","OutputSerialization"],members:{InputSerialization:{shape:"Sbd"},ExpressionType:{},Expression:{},OutputSerialization:{shape:"Sbr"}}},OutputLocation:{type:"structure",members:{S3:{type:"structure",required:["BucketName","Prefix"],members:{BucketName:{},Prefix:{},Encryption:{type:"structure",required:["EncryptionType"],members:{EncryptionType:{},KMSKeyId:{shape:"Sj"},KMSContext:{}}},CannedACL:{},AccessControlList:{shape:"S2y"},Tagging:{shape:"Sau"},UserMetadata:{type:"list",member:{locationName:"MetadataEntry",type:"structure",members:{Name:{},Value:{}}}},StorageClass:{}}}}}}},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"RestoreRequest"},output:{type:"structure",members:{RequestCharged:{location:"header",locationName:"x-amz-request-charged"},RestoreOutputPath:{location:"header",locationName:"x-amz-restore-output-path"}}},alias:"PostObjectRestore"},SelectObjectContent:{http:{requestUri:"/{Bucket}/{Key+}?select&select-type=2"},input:{locationName:"SelectObjectContentRequest",xmlNamespace:{uri:"http://s3.amazonaws.com/doc/2006-03-01/"},type:"structure",required:["Bucket","Key","Expression","ExpressionType","InputSerialization","OutputSerialization"],members:{Bucket:{location:"uri",locationName:"Bucket"},Key:{location:"uri",locationName:"Key"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},Expression:{},ExpressionType:{},RequestProgress:{type:"structure",members:{Enabled:{type:"boolean"}}},InputSerialization:{shape:"Sbd"},OutputSerialization:{shape:"Sbr"}}},output:{type:"structure",members:{Payload:{type:"structure",members:{Records:{type:"structure",members:{Payload:{eventpayload:true,type:"blob"}},event:true},Stats:{type:"structure",members:{Details:{eventpayload:true,type:"structure",members:{BytesScanned:{type:"long"},BytesProcessed:{type:"long"},BytesReturned:{type:"long"}}}},event:true},Progress:{type:"structure",members:{Details:{eventpayload:true,type:"structure",members:{BytesScanned:{type:"long"},BytesProcessed:{type:"long"},BytesReturned:{type:"long"}}}},event:true},Cont:{type:"structure",members:{},event:true},End:{type:"structure",members:{},event:true}},eventstream:true}},payload:"Payload"}},UploadPart:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","Key","PartNumber","UploadId"],members:{Body:{streaming:true,type:"blob"},Bucket:{location:"uri",locationName:"Bucket"},ContentLength:{location:"header",locationName:"Content-Length",type:"long"},ContentMD5:{location:"header",locationName:"Content-MD5"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}},payload:"Body"},output:{type:"structure",members:{ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},ETag:{location:"header",locationName:"ETag"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}}}},UploadPartCopy:{http:{method:"PUT",requestUri:"/{Bucket}/{Key+}"},input:{type:"structure",required:["Bucket","CopySource","Key","PartNumber","UploadId"],members:{Bucket:{location:"uri",locationName:"Bucket"},CopySource:{location:"header",locationName:"x-amz-copy-source"},CopySourceIfMatch:{location:"header",locationName:"x-amz-copy-source-if-match"},CopySourceIfModifiedSince:{location:"header",locationName:"x-amz-copy-source-if-modified-since",type:"timestamp"},CopySourceIfNoneMatch:{location:"header",locationName:"x-amz-copy-source-if-none-match"},CopySourceIfUnmodifiedSince:{location:"header",locationName:"x-amz-copy-source-if-unmodified-since",type:"timestamp"},CopySourceRange:{location:"header",locationName:"x-amz-copy-source-range"},Key:{location:"uri",locationName:"Key"},PartNumber:{location:"querystring",locationName:"partNumber",type:"integer"},UploadId:{location:"querystring",locationName:"uploadId"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKey:{shape:"S19",location:"header",locationName:"x-amz-server-side-encryption-customer-key"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},CopySourceSSECustomerAlgorithm:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-algorithm"},CopySourceSSECustomerKey:{shape:"S1c",location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key"},CopySourceSSECustomerKeyMD5:{location:"header",locationName:"x-amz-copy-source-server-side-encryption-customer-key-MD5"},RequestPayer:{location:"header",locationName:"x-amz-request-payer"}}},output:{type:"structure",members:{CopySourceVersionId:{location:"header",locationName:"x-amz-copy-source-version-id"},CopyPartResult:{type:"structure",members:{ETag:{},LastModified:{type:"timestamp"}}},ServerSideEncryption:{location:"header",locationName:"x-amz-server-side-encryption"},SSECustomerAlgorithm:{location:"header",locationName:"x-amz-server-side-encryption-customer-algorithm"},SSECustomerKeyMD5:{location:"header",locationName:"x-amz-server-side-encryption-customer-key-MD5"},SSEKMSKeyId:{shape:"Sj",location:"header",locationName:"x-amz-server-side-encryption-aws-kms-key-id"},RequestCharged:{location:"header",locationName:"x-amz-request-charged"}},payload:"CopyPartResult"}}},shapes:{Sj:{type:"string",sensitive:true},S11:{type:"map",key:{},value:{}},S19:{type:"blob",sensitive:true},S1c:{type:"blob",sensitive:true},S2v:{type:"structure",members:{DisplayName:{},ID:{}}},S2y:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S30"},Permission:{}}}},S30:{type:"structure",required:["Type"],members:{DisplayName:{},EmailAddress:{},ID:{},Type:{locationName:"xsi:type",xmlAttribute:true},URI:{}},xmlNamespace:{prefix:"xsi",uri:"http://www.w3.org/2001/XMLSchema-instance"}},S37:{type:"structure",required:["Id","StorageClassAnalysis"],members:{Id:{},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}},StorageClassAnalysis:{type:"structure",members:{DataExport:{type:"structure",required:["OutputSchemaVersion","Destination"],members:{OutputSchemaVersion:{},Destination:{type:"structure",required:["S3BucketDestination"],members:{S3BucketDestination:{type:"structure",required:["Format","Bucket"],members:{Format:{},BucketAccountId:{},Bucket:{},Prefix:{}}}}}}}}}}},S3a:{type:"structure",required:["Key","Value"],members:{Key:{},Value:{}}},S3d:{type:"list",member:{shape:"S3a",locationName:"Tag"}},S3n:{type:"list",member:{type:"structure",required:["AllowedMethods","AllowedOrigins"],members:{AllowedHeaders:{locationName:"AllowedHeader",type:"list",member:{},flattened:true},AllowedMethods:{locationName:"AllowedMethod",type:"list",member:{},flattened:true},AllowedOrigins:{locationName:"AllowedOrigin",type:"list",member:{},flattened:true},ExposeHeaders:{locationName:"ExposeHeader",type:"list",member:{},flattened:true},MaxAgeSeconds:{type:"integer"}}},flattened:true},S40:{type:"structure",required:["Rules"],members:{Rules:{locationName:"Rule",type:"list",member:{type:"structure",members:{ApplyServerSideEncryptionByDefault:{type:"structure",required:["SSEAlgorithm"],members:{SSEAlgorithm:{},KMSMasterKeyID:{shape:"Sj"}}}}},flattened:true}}},S46:{type:"structure",required:["Destination","IsEnabled","Id","IncludedObjectVersions","Schedule"],members:{Destination:{type:"structure",required:["S3BucketDestination"],members:{S3BucketDestination:{type:"structure",required:["Bucket","Format"],members:{AccountId:{},Bucket:{},Format:{},Prefix:{},Encryption:{type:"structure",members:{SSES3:{locationName:"SSE-S3",type:"structure",members:{}},SSEKMS:{locationName:"SSE-KMS",type:"structure",required:["KeyId"],members:{KeyId:{shape:"Sj"}}}}}}}}},IsEnabled:{type:"boolean"},Filter:{type:"structure",required:["Prefix"],members:{Prefix:{}}},Id:{},IncludedObjectVersions:{},OptionalFields:{type:"list",member:{locationName:"Field"}},Schedule:{type:"structure",required:["Frequency"],members:{Frequency:{}}}}},S4m:{type:"list",member:{type:"structure",required:["Prefix","Status"],members:{Expiration:{shape:"S4o"},ID:{},Prefix:{},Status:{},Transition:{shape:"S4t"},NoncurrentVersionTransition:{shape:"S4v"},NoncurrentVersionExpiration:{shape:"S4w"},AbortIncompleteMultipartUpload:{shape:"S4x"}}},flattened:true},S4o:{type:"structure",members:{Date:{shape:"S4p"},Days:{type:"integer"},ExpiredObjectDeleteMarker:{type:"boolean"}}},S4p:{type:"timestamp",timestampFormat:"iso8601"},S4t:{type:"structure",members:{Date:{shape:"S4p"},Days:{type:"integer"},StorageClass:{}}},S4v:{type:"structure",members:{NoncurrentDays:{type:"integer"},StorageClass:{}}},S4w:{type:"structure",members:{NoncurrentDays:{type:"integer"}}},S4x:{type:"structure",members:{DaysAfterInitiation:{type:"integer"}}},S50:{type:"structure",members:{IP:{locationName:"IP",type:"list",member:{},flattened:true}}},S51:{type:"list",member:{type:"structure",required:["Status"],members:{Expiration:{shape:"S4o"},ID:{},Prefix:{deprecated:true},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}},Status:{},Transitions:{locationName:"Transition",type:"list",member:{shape:"S4t"},flattened:true},NoncurrentVersionTransitions:{locationName:"NoncurrentVersionTransition",type:"list",member:{shape:"S4v"},flattened:true},NoncurrentVersionExpiration:{shape:"S4w"},AbortIncompleteMultipartUpload:{shape:"S4x"}}},flattened:true},S5b:{type:"structure",required:["TargetBucket","TargetPrefix"],members:{TargetBucket:{},TargetGrants:{type:"list",member:{locationName:"Grant",type:"structure",members:{Grantee:{shape:"S30"},Permission:{}}}},TargetPrefix:{}}},S5j:{type:"structure",required:["Id"],members:{Id:{},Filter:{type:"structure",members:{Prefix:{},Tag:{shape:"S3a"},And:{type:"structure",members:{Prefix:{},Tags:{shape:"S3d",flattened:true,locationName:"Tag"}}}}}}},S5m:{type:"structure",required:["Bucket"],members:{Bucket:{location:"uri",locationName:"Bucket"}}},S5n:{type:"structure",members:{TopicConfiguration:{type:"structure",members:{Id:{},Events:{shape:"S5q",locationName:"Event"},Event:{deprecated:true},Topic:{}}},QueueConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:true},Events:{shape:"S5q",locationName:"Event"},Queue:{}}},CloudFunctionConfiguration:{type:"structure",members:{Id:{},Event:{deprecated:true},Events:{shape:"S5q",locationName:"Event"},CloudFunction:{},InvocationRole:{}}}}},S5q:{type:"list",member:{},flattened:true},S5y:{type:"structure",members:{TopicConfigurations:{locationName:"TopicConfiguration",type:"list",member:{type:"structure",required:["TopicArn","Events"],members:{Id:{},TopicArn:{locationName:"Topic"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true},QueueConfigurations:{locationName:"QueueConfiguration",type:"list",member:{type:"structure",required:["QueueArn","Events"],members:{Id:{},QueueArn:{locationName:"Queue"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true},LambdaFunctionConfigurations:{locationName:"CloudFunctionConfiguration",type:"list",member:{type:"structure",required:["LambdaFunctionArn","Events"],members:{Id:{},LambdaFunctionArn:{locationName:"CloudFunction"},Events:{shape:"S5q",locationName:"Event"},Filter:{shape:"S61"}}},flattened:true}}},S61:{type:"structure",members:{Key:{locationName:"S3Key",type:"structure",members:{FilterRules:{locationName:"FilterRule",type:"list",member:{type:"structure",members:{Name:{},Value:{}}},flattened:true}}}}},S6h:{type:"structure",required:["Role","Rules"],members:{Role:{},Rules:{locationName:"Rule",type:"list",member:{type:"structure",required:["Prefix","Status","Destination"],members:{ID:{},Prefix:{},Status:{},SourceSelectionCriteria:{type:"structure",members:{SseKmsEncryptedObjects:{type:"structure",required:["Status"],members:{Status:{}}}}},Destination:{type:"structure",required:["Bucket"],members:{Bucket:{},Account:{},StorageClass:{},AccessControlTranslation:{type:"structure",required:["Owner"],members:{Owner:{}}},EncryptionConfiguration:{type:"structure",members:{ReplicaKmsKeyID:{}}}}}}},flattened:true}}},S75:{type:"structure",required:["HostName"],members:{HostName:{},Protocol:{}}},S78:{type:"structure",required:["Suffix"],members:{Suffix:{}}},S7a:{type:"structure",required:["Key"],members:{Key:{}}},S7b:{type:"list",member:{locationName:"RoutingRule",type:"structure",required:["Redirect"],members:{Condition:{type:"structure",members:{HttpErrorCodeReturnedEquals:{},KeyPrefixEquals:{}}},Redirect:{type:"structure",members:{HostName:{},HttpRedirectCode:{},Protocol:{},ReplaceKeyPrefixWith:{},ReplaceKeyWith:{}}}}}},S97:{type:"structure",members:{ID:{},DisplayName:{}}},S98:{type:"list",member:{type:"structure",members:{Prefix:{}}},flattened:true},S9q:{type:"list",member:{type:"structure",members:{Key:{},LastModified:{type:"timestamp"},ETag:{},Size:{type:"integer"},StorageClass:{},Owner:{shape:"S2v"}}},flattened:true},Sa8:{type:"structure",members:{Grants:{shape:"S2y",locationName:"AccessControlList"},Owner:{shape:"S2v"}}},Sau:{type:"structure",required:["TagSet"],members:{TagSet:{shape:"S3d"}}},Sbd:{type:"structure",members:{CSV:{type:"structure",members:{FileHeaderInfo:{},Comments:{},QuoteEscapeCharacter:{},RecordDelimiter:{},FieldDelimiter:{},QuoteCharacter:{},AllowQuotedRecordDelimiter:{type:"boolean"}}},CompressionType:{},JSON:{type:"structure",members:{Type:{}}}}},Sbr:{type:"structure",members:{CSV:{type:"structure",members:{QuoteFields:{},QuoteEscapeCharacter:{},RecordDelimiter:{},FieldDelimiter:{},QuoteCharacter:{}}},JSON:{type:"structure",members:{RecordDelimiter:{}}}}}},paginators:{ListBuckets:{result_key:"Buckets"},ListMultipartUploads:{input_token:["KeyMarker","UploadIdMarker"],limit_key:"MaxUploads",more_results:"IsTruncated",output_token:["NextKeyMarker","NextUploadIdMarker"],result_key:["Uploads","CommonPrefixes"]},ListObjectVersions:{input_token:["KeyMarker","VersionIdMarker"],limit_key:"MaxKeys",more_results:"IsTruncated",output_token:["NextKeyMarker","NextVersionIdMarker"],result_key:["Versions","DeleteMarkers","CommonPrefixes"]},ListObjects:{input_token:"Marker",limit_key:"MaxKeys",more_results:"IsTruncated",output_token:"NextMarker || Contents[-1].Key",result_key:["Contents","CommonPrefixes"]},ListObjectsV2:{input_token:"ContinuationToken",limit_key:"MaxKeys",output_token:"NextContinuationToken",result_key:["Contents","CommonPrefixes"]},ListParts:{input_token:"PartNumberMarker",limit_key:"MaxParts",more_results:"IsTruncated",output_token:"NextPartNumberMarker",result_key:"Parts"}},waiters:{BucketExists:{delay:5,operation:"HeadBucket",maxAttempts:20,acceptors:[{expected:200,matcher:"status",state:"success"},{expected:301,matcher:"status",state:"success"},{expected:403,matcher:"status",state:"success"},{expected:404,matcher:"status",state:"retry"}]},BucketNotExists:{delay:5,operation:"HeadBucket",maxAttempts:20,acceptors:[{expected:404,matcher:"status",state:"success"}]},ObjectExists:{delay:5,operation:"HeadObject",maxAttempts:20,acceptors:[{expected:200,matcher:"status",state:"success"},{expected:404,matcher:"status",state:"retry"}]},ObjectNotExists:{delay:5,operation:"HeadObject",maxAttempts:20,acceptors:[{expected:404,matcher:"status",state:"success"}]}}};OOS.apiLoader.services["sts"]={};OOS.STS=OOS.Service.defineService("sts",["2011-06-15"]);_xamzrequire=function a(o,s,u){function c(r,e){if(!s[r]){if(!o[r]){var t=typeof _xamzrequire=="function"&&_xamzrequire;if(!e&&t)return t(r,!0);if(l)return l(r,!0);var i=new Error("Cannot find module '"+r+"'");throw i.code="MODULE_NOT_FOUND",i}var n=s[r]={exports:{}};o[r][0].call(n.exports,function(e){var t=o[r][1][e];return c(t?t:e)},n,n.exports,a,o,s,u)}return s[r].exports}var l=typeof _xamzrequire=="function"&&_xamzrequire;for(var e=0;e<u.length;e++)c(u[e]);return c}({101:[function(e,t,r){var i=e("../core");i.util.update(i.STS.prototype,{credentialsFrom:function e(t,r){if(!t)return null;if(!r)r=new i.TemporaryCredentials;r.expired=false;r.accessKeyId=t.Credentials.AccessKeyId;r.secretAccessKey=t.Credentials.SecretAccessKey;r.sessionToken=t.Credentials.SessionToken;r.expireTime=t.Credentials.Expiration;return r},assumeRoleWithWebIdentity:function e(t,r){return this.makeUnauthenticatedRequest("assumeRoleWithWebIdentity",t,r)},assumeRoleWithSAML:function e(t,r){return this.makeUnauthenticatedRequest("assumeRoleWithSAML",t,r)}})},{"../core":38}]},{},[101]);OOS.apiLoader.services["sts"]["2011-06-15"]={version:"2.0",metadata:{apiVersion:"2011-06-15",endpointPrefix:"sts",globalEndpoint:"sts.amazonaws.com",protocol:"query",serviceAbbreviation:"OOS STS",serviceFullName:"OOS Security Token Service",serviceId:"STS",signatureVersion:"v4",uid:"sts-2011-06-15",xmlNamespace:"https://sts.amazonaws.com/doc/2011-06-15/"},operations:{AssumeRole:{input:{type:"structure",required:["RoleArn","RoleSessionName"],members:{RoleArn:{},RoleSessionName:{},Policy:{},DurationSeconds:{type:"integer"},ExternalId:{},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"AssumeRoleResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"}}}},AssumeRoleWithSAML:{input:{type:"structure",required:["RoleArn","PrincipalArn","SAMLAssertion"],members:{RoleArn:{},PrincipalArn:{},SAMLAssertion:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithSAMLResult",type:"structure",members:{Credentials:{shape:"Sa"},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Subject:{},SubjectType:{},Issuer:{},Audience:{},NameQualifier:{}}}},AssumeRoleWithWebIdentity:{input:{type:"structure",required:["RoleArn","RoleSessionName","WebIdentityToken"],members:{RoleArn:{},RoleSessionName:{},WebIdentityToken:{},ProviderId:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"AssumeRoleWithWebIdentityResult",type:"structure",members:{Credentials:{shape:"Sa"},SubjectFromWebIdentityToken:{},AssumedRoleUser:{shape:"Sf"},PackedPolicySize:{type:"integer"},Provider:{},Audience:{}}}},DecodeAuthorizationMessage:{input:{type:"structure",required:["EncodedMessage"],members:{EncodedMessage:{}}},output:{resultWrapper:"DecodeAuthorizationMessageResult",type:"structure",members:{DecodedMessage:{}}}},GetCallerIdentity:{input:{type:"structure",members:{}},output:{resultWrapper:"GetCallerIdentityResult",type:"structure",members:{UserId:{},Account:{},Arn:{}}}},GetFederationToken:{input:{type:"structure",required:["Name"],members:{Name:{},Policy:{},DurationSeconds:{type:"integer"}}},output:{resultWrapper:"GetFederationTokenResult",type:"structure",members:{Credentials:{shape:"Sa"},FederatedUser:{type:"structure",required:["FederatedUserId","Arn"],members:{FederatedUserId:{},Arn:{}}},PackedPolicySize:{type:"integer"}}}},GetSessionToken:{input:{type:"structure",members:{DurationSeconds:{type:"integer"},SerialNumber:{},TokenCode:{}}},output:{resultWrapper:"GetSessionTokenResult",type:"structure",members:{Credentials:{shape:"Sa"}}}}},shapes:{Sa:{type:"structure",required:["AccessKeyId","SecretAccessKey","SessionToken","Expiration"],members:{AccessKeyId:{},SecretAccessKey:{},SessionToken:{},Expiration:{type:"timestamp"}}},Sf:{type:"structure",required:["AssumedRoleId","Arn"],members:{AssumedRoleId:{},Arn:{}}}},paginators:{}};