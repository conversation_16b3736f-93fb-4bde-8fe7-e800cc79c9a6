/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 14:03:18
 * @LastEditTime: 2023-09-14 14:03:25
 * @FilePath: \frontend\src\pages\Translate\components\Left\index.tsx
 * @Description:
 */

import { Affix } from 'antd';
import React, {
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import style from './index.less';
import {
  PlusCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useImmer } from 'use-immer';
import { useLiveQuery } from 'dexie-react-hooks';
import {
  deleteResult,
  getAllResult,
  getAllResultCount,
  storeResult,
} from '@/db/controllers/Result';
import { AI } from '@/constant/ai';
import { ClassNames } from '@/utils/utils';
import './reset.less';

const Left: React.FC<LeftProps> = ({
  actionRef: propsActionRef,
  createTitle,
  setIsEmpty,
  selectedKey,
  setSelectedKey,
}) => {
  const items = useLiveQuery(async () => {
    try {
      const result = await getAllResult(AI.TRANSLATE);
      return result;
    } catch (error) {
      console.log(
        '%c [ error ]-28',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error,
      );
      return [];
    }
  });
  const renderCountRef = useRef(0);

  useEffect(() => {
    // 默认选中第一个
    if (
      renderCountRef.current === 0 &&
      (items?.length ?? 0) > 0
    ) {
      setSelectedKey?.(items?.[0]?.id);
      renderCountRef.current++;
    }
    if (items) {
      setIsEmpty?.(items.length === 0);
    }
  }, [items]);
  useImperativeHandle(
    propsActionRef,
    (): ActionRefType => ({}),
    [],
  );

  return (
    <div className={style.left} id="left">
      <div
        className={style.new}
        onClick={async () => {
          const count = await getAllResultCount(
            AI.TRANSLATE,
          );
          const { deleteId, createId } = await storeResult({
            key: AI.TRANSLATE,
            title: '新的翻译',
          });
          // if (deleteId === selectedKey) {

          // }
          if (createId && (createId as number) > -1) {
            setSelectedKey?.(createId as number);
          }
        }}>
        <PlusCircleOutlined size={36} />
        <p>{createTitle}</p>
      </div>
      <div className={style.tabs}>
        {items?.map((item) => {
          return (
            <div
              className={ClassNames(
                style.tab,
                selectedKey === item.id
                  ? style.selected
                  : '',
              )}
              onClick={() => {
                setSelectedKey?.(item.id);
              }}
              key={item.id}>
              {item.title}
              <DeleteOutlined
                onClick={async (e) => {
                  e.stopPropagation();
                  if (selectedKey === item.id) {
                    setSelectedKey?.(undefined);
                  }
                  if (item.id) {
                    await deleteResult(item.id);
                  }
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export interface LeftProps {
  actionRef?: React.MutableRefObject<any>;
  createTitle: string;
  setIsEmpty?: (isEmpty: boolean) => void;
  selectedKey?: React.Key;
  setSelectedKey?: (key?: React.Key) => void;
}

export default Left;
Left.displayName = 'Left';
export type ActionRefType = {};
