/*
 * @Author: spring breeze
 * @Date: 2023-07-04 14:48:49
 * @LastEditTime: 2023-09-06 11:17:43
 * @FilePath: \frontend\src\app.ts
 * @Description:
 */

import {
  StyleProvider,
  legacyLogicalPropertiesTransformer,
} from '@ant-design/cssinjs';
import React from 'react';
// 兼容chrome56 umi request中的abort controller
import 'abortcontroller-polyfill/dist/polyfill-patch-fetch';


// 注入styleProvider 提供低版本浏览器支持
export function rootContainer(container: any) {
  return React.createElement(
    StyleProvider,
    {
      hashPriority: 'high',
      transformers: [legacyLogicalPropertiesTransformer],
    },
    container,
  );
}
