/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:50:43
 * @LastEditTime: 2024-01-24 11:28:36
 * @FilePath: \frontend\config\proxy.ts
 * @Description:
 */
const proxyUrl = 'http://*************';
const xieboUrl = 'http://***********:8003';

export const proxy = {
  '/pubagent': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: xieboUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/api': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    // target: 'http://localhost:8080',
    changeOrigin: true,
    secure: false, // 支持https代理
    pathRewrite: { '^/api': '/' },
  },
  '/cvod': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/cvodweb': {
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/rman': {
    target: proxyUrl,
    // target: 'http://localhost:8001',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket-z': {
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/edudata': {
    target: proxyUrl,
    // target: 'http://*************',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedlogin': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/busman': {
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/ipingestman': {
    target: proxyUrl,
    // target: 'http://*************:8000',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/unifiedplatform/v1/': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/terminator': {
    target: proxyUrl,
    // target: 'http://*************:8000',
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/flowmanager': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/livemanage': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/bucket': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/learn': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/obe': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/textclip': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/joveone': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/chatdoc': {
    // target: 'http://ecloud.scu.sobeylingyun.com',
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  },
  '/qa': {
    target: 'http://**************',
    changeOrigin: true,
    secure: false, // 支持https代理
    pathRewrite: { '^/qa': '/' },
  },
  '/translation': {
    target: proxyUrl,
    changeOrigin: true,
    secure: false, // 支持https代理
  }
  // '/third-api':{
  //   target: proxyUrl,
  //   changeOrigin: true,
  // secure: false, // 支持https代理
  // }

};
