/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 16:24:10
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-04-27 16:30:10
 */
tinymce.PluginManager.add('bubble', function(editor, url) {
  var pluginName = '气泡注释';
  window.bubble = {}; //扔外部公共变量，也可以扔一个自定义的位置

  // var baseURL=tinymce.baseURL;
  // var iframe1 = baseURL+'/plugins/bubble/upfiles.html';
  // bubble.images_upload_handler = editor.getParam('images_upload_handler', undefined, 'function');
  // bubble.images_upload_base_path = editor.getParam('images_upload_base_path', '', 'string');
  // bubble.axupimgs_filetype = editor.getParam('axupimgs_filetype', '.png,.gif,.jpg,.jpeg', 'string');
  bubble.res = [];
  var openDialog = function() {
    var node = editor.selection.getNode();
    var dataset = editor.selection.getNode().dataset;
    var initialData = {
      bubble: dataset?.bubble,
    };
    return editor.windowManager.open({
      title: pluginName,
      initialData: initialData,
      body: {
        type: 'panel',
        items: [
          {
            type: 'textarea',
            name: 'bubble',
          },
        ],
      },
      buttons: [
        {
          type: 'cancel',
          text: 'Close',
        },
        {
          type: 'submit',
          text: 'Save',
          primary: true,
        },
      ],
      onSubmit: function(api) {
        var node = editor.selection.getNode();
        var data = api.getData();
        console.log(data, 'datadatadatadata');
        if (initialData.bubble) {
          editor.selection.setContent(
            `<span id="sobey_bubble" contenteditable="false" style="color:#BECBFF;" data-bubble="${data.bubble}" data-mytooltip="${data.bubble}">${node.innerText}</span>`,
          );
        } else {
          editor.selection.setContent(
            `<span id="sobey_bubble" contenteditable="false" style="color:#BECBFF;" data-bubble="${
              data.bubble
            }" data-mytooltip="${
              data.bubble
            }" >${editor.selection.getContent()}</span>`,
          );
        }
        api.close();
      },
    });
  };

  editor.ui.registry.getAll().icons.bubble ||
    editor.ui.registry.addIcon(
      'bubble',
      '<svg t="1620724538175" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12252" width="20" height="20"><path d="M512 64a448 448 0 1 0 0 896A448 448 0 0 0 512 64z m0 820.032A372.032 372.032 0 0 1 512 139.968a372.032 372.032 0 0 1 0 744.064z" fill="#000000" p-id="12253"></path><path d="M464 688a48 48 0 1 0 96 0 48 48 0 0 0-96 0zM488 576h48a8 8 0 0 0 8-8v-272a8 8 0 0 0-8-8h-48a8 8 0 0 0-8 8v272c0 4.416 3.584 8 8 8z" fill="#000000" p-id="12254"></path></svg>',
    );

  editor.ui.registry.addButton('bubble', {
    icon: 'bubble',
    tooltip: pluginName,
    onAction: function() {
      openDialog();
    },
  });
  editor.ui.registry.addMenuItem('bubble', {
    icon: 'bubble',
    text: '气泡注释',
    onAction: function() {
      openDialog();
    },
  });
  return {
    getMetadata: function() {
      return {
        name: pluginName,
        url: 'http://tinymce.ax-z.cn/more-plugins/axupimgs.php',
      };
    },
  };
});
