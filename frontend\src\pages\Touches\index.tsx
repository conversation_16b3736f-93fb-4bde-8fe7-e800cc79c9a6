import React from 'react';
import { Row, Col, Button, Tooltip } from 'antd';
import './index.less';
import { DownloadOutlined } from '@ant-design/icons';
import copyicon1 from '@/images/sanhang/copy.png';
import pdficon1 from '@/images/sanhang/pdf.png';
import wordicon1 from '@/images/sanhang/word.png';
const Touches: React.FC = () => {
  return (
    <div className="touches_container">
       <div className='content'>
            <div className='left_view'>
                <div className='input_span'></div>
                <div className='option_view'>
                    <div className='upload_view'>
                        <Tooltip title="上传pdf文件">
                            <div className='pdf_view'>
                                <img src={pdficon1} alt="" />
                                <span>pdf</span>
                            </div>
                        </Tooltip>
                        <Tooltip title="上传word文件">
                            <div className='word_view'>
                                <img src={wordicon1} alt="" />
                                <span>doc/docx</span>
                            </div>
                        </Tooltip>
                    </div>
                    <Button type="primary">论文润色</Button>
                </div>
            </div>
            <div className='right_view'>
                <div className='output_span'></div>
                <div className='option_view'>
                    <img className='copy' src={copyicon1} alt="复制" />
                    <DownloadOutlined style={{fontSize:'28px',cursor:'pointer',color:'var(--primary-color)'}} />
                </div>
            </div>
       </div>
    </div>
  );
};

export default Touches;
