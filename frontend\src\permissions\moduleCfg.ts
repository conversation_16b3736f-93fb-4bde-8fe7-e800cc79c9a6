/*
 * @Description:
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-06-24 10:35:20
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-07-13 16:11:55
 */
enum ModuleCfg {
  teacher = 'wdkc', // 我的教学
  student = 'MyLearning', // 我的学习
  personal = 'PersonalCenter', // 个人中心
  admin = 'SystemManagement', // 系统管理
  manager = 'management_center', // 管理中心
  rman = 'zyzx', // 资源管理
  course = 'wdkc', //教学空间
  template = 'kczx', //课程中心
  jove = 'zxjj', // 在线剪辑
  textclip = 'textclip', // 在线剪辑
  work = 'workbench', // 工作台
  exam = 'examManagement', // 试题库
  statistisc = 'statistisc', // 统计
  platformOverview = 'platformOverview', // 平台概览
  resourceOverview = 'resourceOverview', // 资源建设总览
  courseOverview = 'courseOverview', // 课程建设总览
  accessOverview = 'accessOverview', // 访问统计总览
  // 利用管理
  Utilization_M_ = 'Utilization_M_',
  // 利用使用
  Utilization_U_ = 'Utilization_U_',
}

enum ModuleCapumanCfg {
  // 接收库
  Receive = 'Receive_',
  // 整理库
  Collation = 'Collation_',
  // 长期库
  Preservation = 'Preservation_',
  // 鉴定库
  Appraisal = 'Appraisal_',
  //挑选蓝
  Selection = 'Selection_',
  // 编目管理
  Cataloguing = 'Cataloguing_',
  // 我的任务
  Task = 'Task_',
  // 数据统计--系统都有这个权限
  // 流程编辑
  Flow = 'Flow_',
  // 回收站
  Recycle = 'Recycle_',
  // 个人中心
  Personal = 'Personal_',
  // 我的消息
  News = 'News_',

  Statistics = 'Statistics_', // 数据统计
  Archivemanage = 'Archivemanage_', // 归档应用
  FileMonitoring = 'FileMonitoring', // 档案监控
  QzManagement = 'QzManagement', // 全宗管理
}

export default ModuleCfg;
export { ModuleCapumanCfg };

const globalParams = {
  composite_display: 'composite_display', // 合成选项隐藏
  knowledge_analysis_display: 'knowledge_analysis_display', //知识点分析隐藏
  speech_analysis_display: 'speech_analysis_display', //是否显示语音分析
  smart_tag_display: 'smart_tag_display', //是否显示智能标签
  colorimprove_display: 'colorimprove_display', //校色
  noisereduce_display: 'noisereduce_display', // 去噪
  enhance_display: 'enhance_display', //声音增强
  file_group_display: 'file_group_display', //下载是否显示原格式
  file_group_proxy_display: 'file_group_proxy_display', //下载是否显示低质量
  my_collection_display: 'my_collection_display', //是否显示我的收藏
  my_shared_display: 'my_shared_display', //是否显示我的收藏
  folder_group_display: 'folder_group_display', //是否显示群组资源
  my_video_display: 'my_video_display', //是否显示我的录播
  knowledge_map_display: 'knowledge_map_display', //是否显示知识点绑定
};
export  {globalParams};
