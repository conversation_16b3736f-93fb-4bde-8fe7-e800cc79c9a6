import request from './request';

function XSRF() {
  return request(`/rman/v1/api/Security/xsrf-token`, {
    method: 'get',
  });
}

const platform = window.localStorage.getItem('upform_platform');
if (platform === 'ILA') {
  XSRF();
}

const getCookie = (key: any) => {
  const tips = document.cookie.split(';');
  let a: any = [];
  let x = '';
  tips.map((item: any) => {
    a = item.split('=');
    if (a[0].trim() == key) {
      x = a[1];
    }
  });
  return x;
};
async function getBasicData() {
  return request(`/unifiedplatform/v1/setting`, {
    method: 'get',
  });
}
async function setBasicData(params: any) {
  return request(`/unifiedplatform/v1/setting`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}
async function getConfiguration() {
  return request(
    `/unifiedplatform/v1/setting/single/navigation/configuration`,
    {
      method: 'get',
    },
  );
}
async function setConfiguration(params: any) {
  return request(
    `/unifiedplatform/v1/setting/insert/navigation/configuration`,
    {
      method: 'post',
      data: JSON.stringify(params),
    },
  );
}

async function setContact(params: any) {
  return request(`/unifiedplatform/v1/setting/link`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

async function setSeo(params: any) {
  return request(`/unifiedplatform/v1/setting/seo`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

async function getSeo() {
  return request(`/unifiedplatform/v1/setting/seo`, {
    method: 'get',
  });
}

interface IimgUpload {
  imageBase64: string;
  imageUse: number; // 图片使用类型。0 = ClipUse关联素材使用;1 = UserUse, 关联用户使用；2 = OtherUse
  relatedId: string;
}
/**
 * @description: rman的图片上传接口
 * @param {*}
 * @return {*}
 */
async function uploadImgRman(data: IimgUpload) {
  return request(`/rman/v1/upload/save/image`, {
    method: 'post',
    data,
  });
}
async function uploadfileimage(data: IimgUpload) {
  return request(`rman/v1/upload/save/fileimage`, {
    method: 'post',
    data,
  });
}

// 查询所有第三方平台
async function getcourseplatform() {
  return request(`/unifiedplatform/v1/courseplatform`, {
    method: 'get',
  });
}

// 添加第三方平台
async function courseplatform(params: any) {
  return request(`/unifiedplatform/v1/courseplatform`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

// 删除第三方平台
async function deletecourseplatform(id: any) {
  return request(`/unifiedplatform/v1/courseplatform/delete?id=${id}`, {
    method: 'get',
  });
}

// 拖拽从新排序
async function ordercourseplatform(params: any) {
  return request(`/unifiedplatform/v1/courseplatform/order`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

// 新增或修改新用户引导
async function addguidepage(params: any) {
  return request(`/unifiedplatform/v1/newhandguide/guidepage`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

// 查询新用户引导的所有页面
async function getguidepage() {
  return request(`/unifiedplatform/v1/newhandguide/guidepage`, {
    method: 'get',
  });
}

// 删除新用户引导的页面
async function deleteguidepage(id: any) {
  return request(`/unifiedplatform/v1/newhandguide/guidepage/delete?id=${id}`, {
    method: 'get',
  });
}

// 查询 页面下面的目录和引导
async function getguidedirectory(id: any) {
  return request(
    `/unifiedplatform/v1/newhandguide/guidedirectory?pageId=${id}&isAll=true`,
    {
      method: 'get',
    },
  );
}

// 新增或修改新用户引导的目录和引导
async function addguidedirectory(params: any) {
  return request(`/unifiedplatform/v1/newhandguide/guidedirectory`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

// 删除新用户引导的目录和引导
async function deleteguidedirectory(id: any) {
  return request(
    `/unifiedplatform/v1/newhandguide/guidedirectory/delete?id=${id}`,
    {
      method: 'get',
    },
  );
}

// 禁用或启用引导
async function updataguidedirectorydisable(id: any, disable: boolean) {
  return request(
    `/unifiedplatform/v1/newhandguide/guidedirectory/disable?id=${id}&disable=${disable}`,
    {
      method: 'get',
    },
  );
}

// 目录和引导排序
async function orderguidedirectory(params: any) {
  return request(`/unifiedplatform/v1/newhandguide/guidedirectory/order`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

//新增或修改步骤
async function addguideitem(params: any) {
  return request(`/unifiedplatform/v1/newhandguide/guideitem`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

// 删除步骤
async function deleteguideitem(id: any) {
  return request(`/unifiedplatform/v1/newhandguide/guideitem/delete?id=${id}`, {
    method: 'post',
  });
}

// 步骤排序
async function orderguideitem(params: any) {
  return request(`/unifiedplatform/v1/newhandguide/guideitem/order`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}
//水印位置上传
async function WaterMark(params: any) {
  return request(`/rman/v1/WaterMark/Insert`, {
    method: 'post',
    data: JSON.stringify(params),
    headers: { 'X-XSRF-TOKEN': getCookie('XSRF-TOKEN') },
  });
}
//初始化水印信息
async function GetAllWaterMark() {
  return request(`/rman/v1/WaterMark/GetAll`, {
    method: 'get',
  });
}
//修改水印信息
async function updataWaterMark(params: any) {
  return request(`/rman/v1/WaterMark/Update`, {
    method: 'post',
    data: JSON.stringify(params),
    headers: { 'X-XSRF-TOKEN': getCookie('XSRF-TOKEN') },
  });
}

let gatewayApis = {
  getBasicData,
  setBasicData,
  getConfiguration,
  setConfiguration,
  setContact,
  setSeo,
  getSeo,
  uploadImgRman,
  courseplatform,
  getcourseplatform,
  deletecourseplatform,
  ordercourseplatform,
  addguidepage,
  getguidepage,
  deleteguidepage,
  getguidedirectory,
  addguidedirectory,
  deleteguidedirectory,
  updataguidedirectorydisable,
  orderguidedirectory,
  addguideitem,
  deleteguideitem,
  orderguideitem,
  uploadfileimage,
  WaterMark,
  GetAllWaterMark,
  updataWaterMark,
};

export default gatewayApis;
