/**
 * 微课状态管理
 * 优化相关代码（to do）
 */

const initCourse = {
  contentId: '',
  name: '',
  courseType: 0,
  publishStatus: 0,
  classification: [],
  top: 0,
  describe: '',
  teacher: [],
  school: '',
  college: [],
  subject: [],
  unLoginShow: false,
  is_attachment_ownload: 1,
  major: [],
  cover: '',
  allowed_double_speed: false,
  allowed_drag: false,
  showWatermark: false,
  courseResources: [],
  resource_link: [],
  attachResources: [
    {
      groupType: 'other',
      status: 'ready',
      groupName: 'attachmentgroup',
      fileItems: [],
    },
  ],
};
const initSelect = {
  name: '',
  publishStatus: 0,
  isTop: null,
  keyword: '',
  courseType: '',
  classificationId: [],
  is_attachment_ownload: 0,
  teacher: [],
  subjectId: [],
  college: [],
  professionIds: [],
  startUpdateTime: '',
  endUpdateTime: '',
  order: [
    {
      field: 'lastUpdate_.timestamp',
      isDesc: true,
    },
  ],
  page: 1,
  size: 10,
};

export default {
  namespace: 'microCourse',
  state: {
    formList: [],
    activeKey: '1',
    selectedRow: [],
    loading: false,
    confirmLoading: false,
    refresh: 0,
    modalVisible: false,
    userInfo: {},
    teacherInfo: [],
    showTag: false,
    courseData: {
      ...initCourse,
    },
    selectstatus: {
      ...initSelect,
    },
    myReviewCount: 0
  },
  reducers: {
    updateState: (state: any, { payload }: { payload: any }) => {
      return {
        ...state,
        ...payload,
      };
    },
    initCourse: (state: any) => ({
      ...state,
      ...initCourse,
    }),
    initSelect: (state: any) => ({
      ...state,
      ...initSelect,
    }),
    /**
     * 修改课程单个属性
     * @param state
     * @param payload
     */
    updateCourseProps: (state: any, { payload }: { payload: any }) => ({
      ...state,
      courseData: {
        ...state.courseData,
        ...payload,
      },
    }),
    /**
     * 修改select 单个属性
     * @param state
     * @param payload
     */
    updateSelectProps: (state: any, { payload }: { payload: any }) => ({
      ...state,
      selectstatus: {
        ...state.selectstatus,
        ...payload,
      },
    }),
  },
};
