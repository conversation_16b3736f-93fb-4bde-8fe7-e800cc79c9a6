# Html5 视频、音频播放器

> 基于 MediaElement.js 修改的。http://mediaelementjs.com/

> 依赖于jquery、mam-timecode-convert

> 如果node-sass安装报错，可单独运行此命令：npm install --sass-binary-site=http://npm.taobao.org/mirrors/node-sass

---


## 初始化

``` js
var players = $("#h5player").h5mediaplayer({
	// 要播放的内容
	poster:'',
	// 帧率
	frameRate:25,
	// 拥有的功能特性
	features:['progress', 'postroll', 'backStart', 'skipback', 'backframe', 'playpause', 'stop', 'prevframe', 'skipforward', 'backEnd', 'settrimin', 'settrimout', 'cleantrim', 'getkeyframe', 'setCatalog', 'exportSection', 'saveSection', 'setSection', 'fullscreen', 'volume', 'speed', 'tracks', 'duration', 'current', 'contextmenu'],

	// 成功事件回调
	success: function(media, node, player) {

	}
})
```
> 所有默认参数定义在 src/h5player.js 2343行，可搜索 `mejs.MepDefaults`


### 初始化方法的所有事件回调方法

### success
媒体加载成功

`通过success返回的 player 对象，才能在后续对播放器进行操作`

``` js
success: function (media, node, player) { 
	// media
	// node
	// player，该对象里面带有播放器相关方法
}
```
---

### error
视频、音频加载失败

``` js
error: function (e) {
	// e
}
```
---

### getkeyframe
点击播放器上的 `打标记点` 按钮后

``` js
getKeyframe: function (media, currentTime, img) { 
	// media
	// currentTime
	// img
}
```
---

### setSection
点击播放器上的 `创建片段` 按钮后
``` js
setSection: function (media, img, trimin, trimout) {
	// media;
	// img;
	// trimin;
	// trimout;
}
```
---

### setCatalog
点击播放器上的 `创建片段` 按钮后
``` js
setCatalog: function (media, img, trimin, trimout) {
	console.log(media);
	console.log(img);
	console.log(trimin);
	console.log(trimout);
}
```
---

### exportSection
点击播放器上的 `片段导出` 按钮后
``` js
exportSection: function (media, img, trimin, trimout) {
	console.log(media);
	console.log(img);
	console.log(trimin);
	console.log(trimout);
}
```
---

### saveSection
点击播放器上的 `片段另存为` 按钮后
``` js
saveSection: function (media, img, trimin, trimout) {
	console.log(media);
	console.log(img);
	console.log(trimin);
	console.log(trimout);
}
```
---

### setTrimin
设置片段入点后
``` js
setTrimin: function (media, trimin, trimout) {
	console.log(media);
	console.log(trimin);
	console.log(trimout);
}
```
---

### setTrimout
设置片段出点后
``` js
setTrimout: function (media, trimin, trimout) {
	console.log(media);
	console.log(trimin);
	console.log(trimout);
}
```
---

### clearTrimRange
清空片段出入点后
``` js
clearTrimRange: function (media) {
	console.log(media);
}
```
---

## Player 方法

` player 对象是success事件中返回的`

### play
开始播放

``` js
player.play();
```
---

### pause
暂停播放

``` js
player.pause();
```
---

### stop
停止播放（播放器进度回到起点）

``` js
player.stop();
```
---


### setCurrentTime
设置播放器的当前时间（位置）

``` js
player.setCurrentTime(2);
```
---

### setTrimin
设置片段的入点
> 单位：帧
``` js
player.setTrimin(2);
```
---

### setTrimout
设置片段的出点
> 如果入点未设置，默认则为0
> 单位：帧

``` js
player.setTrimout(5);
```
---

### getTrimin
获取片段的入点

``` js
player.getTrimin();
```
---

### getTrimout
获取片段的出点

``` js
player.getTrimout();
```
---

### cleantrim
清除片段

``` js
player.cleantrim();
```
---

### 播放进度改变事件
``` js
players[0].addEventListener('accurateTimeUpdate', function (event) {
	console.info('time', event);
});
```

---
## 快捷键

相关代码在 h5player.js 的  2475行，因为代码会变动，所以最好通过 `keyActions` 来搜索

目前定义的快捷键列表

- `空格` 暂停和播放
- `PageUp` 到第一帧
- `PageDown` 到最后一帧
- `Up` 快退5秒
- `Down` 快进5秒
- `Left` 退1帧
- `Right` 进1帧
- `[` 打入点
- `]` 打出点
- `F` 全屏切换
- `M` 打标记点
- 

