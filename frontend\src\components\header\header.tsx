import React, {
  FC,
  useEffect,
  useMemo,
  useState,
} from 'react';
import './header.less';
import { useSelector } from '@@/plugin-dva/exports';
import { IGlobal } from '@/models/global';
import {
  Dropdown,
  Image,
  Menu,
  message,
  Divider,
  Badge,
  Popover,
} from 'antd';
import {
  Link,
  useDispatch,
  history,
  useHistory,
} from 'umi';
import config from '@/utils/config';
import api from '@/api';
// import { history } from '@@/core/history';
import IconFont from '@/components/iconFont/iconFont';
import { intersection, size } from 'lodash';
import moduleCfg, {
  globalParams,
  ModuleCapumanCfg,
} from '@/permissions/moduleCfg';
import perCfg from '@/permissions/perCfg';
import Icon from '@ant-design/icons';
import { ReactComponent as clip_icon } from '@/images/clip_icon.svg';
import { ReactComponent as literature_icon } from '@/images/literature_icon.svg';
import settingApis from '@/api/setting';
import userApi from '@/api/user';
import MessageBox from '@/components/Message';
import Access from '../Access';
import NPUHeader from '../NPUHeader';
interface LinkItem {
  key: string;
  name: string;
  href?: string;
  target?: string;
  disabled?: boolean;
}

interface HeaderProps {
  subtitle?: string;
  // navList?: LinkItem[];
  showNav: boolean;
  navActive?: string;
  ifBack?: boolean;
  /** 自定义title */
  customTitle?: string;
}

interface NavProps {
  list: LinkItem[];
  active?: string;
}

interface UserAvatarProps {
  username: string;
  avatar: string;
  work: boolean;
  // teacher: boolean;
  student: boolean;
  admin: boolean;
  // rman: boolean;
  // joveone: boolean;
  workUrl: string;
  // personal: boolean;
  platform: string;
  utilization: boolean;
  capuman: boolean;
  onLogout: () => void;
}

const UserAvatar: FC<UserAvatarProps> = ({
  username,
  avatar,
  work,
  // teacher,
  workUrl,
  student,
  admin,
  // personal,
  // rman,
  // joveone,
  onLogout,
  utilization,
  capuman,
  platform,
}) => {
  const { userInfo } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  // let workUrl = ""
  // let target = ""
  // if (teacher) {
  //   workUrl = "/learn/workbench/#/course"
  //   target = "my_teaching"
  // } else if (rman) {
  //   workUrl = "/rman/#/basic/rmanCenterList"
  //   target = "source_manage"
  // } else if (admin) {
  //   workUrl = "#/basic"
  //   target = "sys_manage"
  // } else if (joveone) {
  //   workUrl = "/joveone"
  //   target = "joveone"
  // }
  const menu = (
    <Menu>
      {work && (
        <Menu.Item>
          <a href={workUrl} target="work">
            {/* 工作台 */}
            {JSON.parse(localStorage.getItem('theme_config') || '{}')?.title || '知了智慧教育平台'}
          </a>
        </Menu.Item>
      )}
      {student && (
        <Menu.Item>
          <a
            href="/unifiedplatform/#/learn/mycourse"
            target="my_study">
            我的学习
          </a>
        </Menu.Item>
      )}
      {/* 只兰台屏蔽账号管理，知了不能屏蔽 */}
      {admin && platform != 'ILA' && (
        <Menu.Item>
          <a
            href="/unifiedplatform/#/management"
            target="sys_manage">
            管理中心
          </a>
        </Menu.Item>
      )}
      {/* {personal && (
        <Menu.Item>
          {platform === 'ILA' ? (
            <Link to="/personal/info" target="personal_center">
              账号管理
            </Link>
          ) : (
            <Link to="/personal/home" target="personal_center">
              个人中心
            </Link>
          )}
        </Menu.Item>
      )} */}
      {utilization && (
        <Menu.Item>
          <a
            // href="/capubmam/#/utilization/archives/subject/preview"
            target="_blank"
            onClick={() => {
              if (
                userInfo.roles.some(
                  (item: any) => item.roleName === '利用',
                )
              ) {
                window.location.href = `/capubmam/#/utilization/archives/open`;
              } else {
                window.location.href = `/capubmam/#/utilization/archives/subject/preview`;
              }
            }}>
            利用平台
          </a>
          {/* <Link to="/utilization">利用平台</Link> */}
        </Menu.Item>
      )}
      {capuman && platform === 'ILA' && (
        <Menu.Item>
          <a href="/capubmam/#/capubmam" target="_blank">
            管理平台
          </a>
        </Menu.Item>
      )}
      <Menu.Item onClick={onLogout}>退出登录</Menu.Item>
    </Menu>
  );

  return (
    <Dropdown
      overlay={menu}
      className="user-avatar-wrapper">
      <div>
        <Image
          src={avatar || config.defaultAvatar}
          fallback={config.defaultAvatar}
          preview={false}
        />
        <p className="user-name" title={username}>
          {username}
        </p>
      </div>
    </Dropdown>
  );
};

const Header: FC<HeaderProps> = ({
  subtitle,
  // navList,
  showNav,
  navActive,
  ifBack = false,
  customTitle,
}) => {
  const dispatch = useDispatch();
  const { userInfo, menuShow } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  const [headerList, setHeaderList] = useState<any[]>([]);
  const { title, logoUrl, isShow } = useSelector<any, any>(
    (state) => state.themes,
  );
  const {
    modules,
    permissions,
    rmanGlobalParameter,
  } = useSelector<
    { global: any },
    {
      modules: string[];
      permissions: string[];
      rmanGlobalParameter: string[];
    }
  >((state) => state.global);
  const handleLogout = async () => {
    window.location.href = `/unifiedlogin/v1/loginmanage/loginout/go`;
    // let res = await api.user.logout();
    // if (res && res.errorCode === 'success') {
    //   message.success('注销成功');
    //   // history.push('/basic?action=login');
    //   // window.location.replace(`/cvodweb`);
    // }
  };
  // console.log(111,modules)
  const [platform, setPlatform] = useState<any>(
    window.localStorage.getItem('upform_platform'),
  );
  const [unReadCount, setUnreadCount] = useState<number>();

  useEffect(() => {
    dispatch({
      type: 'global/initPlatform',
      callback: (res: any) => {
        setPlatform(res);
      },
    });
    if (window.innerWidth <= 768) {
      menuShowChange();
    }
    if (platform !== 'ILA') {
      getUnreadCount();
      const timer = setInterval(getUnreadCount, 10000);
      return () => {
        clearInterval(timer);
      };
    }
  }, []);

  useEffect(() => {
    if (platform !== 'ILA') {
      // settingApis.fetchHeaderList().then((res: any) => {
      //   if (res?.errorCode === 'success') {
      //     const list = res.extendMessage
      //       .filter((item: any) => item.name !== '在线剪辑')
      //       .map((item: any) => ({
      //         key: item.isSystem
      //           ? item.link.includes('rman')
      //             ? 'rman'
      //             : item.link.split('#/')?.[1]?.split('/')?.[0]
      //           : 'key',
      //         name: item.name,
      //         href: item.link,
      //         target: item.openWay
      //           ? item.link.split('/')?.[1] || item.name
      //           : null,
      //         disabled: item.disabled,
      //       }));
      //     setHeaderList(list);
      //   }
      // });
    }
  }, []);

  const stopJump = (e: any) => {
    if (platform === 'ILA') {
      e.preventDefault();
    }
  };

  const getUnreadCount = () => {
    userApi.reqUnreadMessageCount().then((res) => {
      if (res?.errorCode === 'success') {
        setUnreadCount(res.extendMessage);
      }
    });
  };

  const headerTitle = useMemo(() => {
    // 我的学习的路由
    let arr = [
      '/learn/mycourse',
      '/learn/mycollection',
      '/learn/mynotes',
      '/learn/certificates',
      '/learn/learningstatistics',
      '/learn/message',
      '/learn/info',
      '/learn/marjorpath',
      '/learn/myBookshelf',
    ];
    if (customTitle) return customTitle;
    if (arr.includes(history.location.pathname)) {
      return '我的学习';
    } else {
      return '系统管理';
    }
  }, [history.location.pathname, customTitle]);
  const menuShowChange = () => {
    console.log(menuShow);
    dispatch({
      type: 'global/menuShowChange',
      payload: !menuShow,
    });
  };

  return (
    <div
      className={
        platform === 'ILA'
          ? 'uf-header-wrapper-ILA'
          : 'uf-header-wrapper'
      }>
      <div className="uf-header-left-part">
        {platform !== 'ILA' && (
          <IconFont
            type="iconwenzhangpailie2-221"
            onClick={() => menuShowChange()}
          />
        )}
        <a
          href={
            (process.env.NODE_ENV === 'development'
              ? 'http://172.16.151.202'
              : '') +
            '/unifiedplatform/v1/app/mainpage/direction'
          }
          onClick={stopJump}
          className="home-part">
          {platform !== 'ILA' && (
            <img
              className="icon-home"
              src={require('@/assets/img/home.png')}
            />
          )}

          {ifBack && platform !== 'ILA' && (
            <div className="go-back-btn">
              <IconFont type="iconjiantouda" />
            </div>
          )}
          <div className="icon_box">
            <img
              src={
                logoUrl ||
                require('@/images/default_logo.png')
              }
              className="uf-header-icon"
            />
          </div>
          {platform !== 'ILA' && (
            <>
              <Access accessible={!!customTitle}>
                {/* <h2>{customTitle}</h2> */}
                <h2>工作台</h2>
              </Access>
              <Access accessible={!customTitle}>
              <h2>
                  {isShow
                    ? location.href.includes('personal') ||
                      location.href.includes('statistics/overview')
                      ? JSON.parse(localStorage.getItem('theme_config') || '{}')?.title || '知了智慧教育平台'
                      : headerTitle
                    : null}
                </h2>
              </Access>
            </>
          )}
        </a>
        {false && subtitle && platform !== 'ILA' && (
          <>
            {/* <Divider className="vertical-line" type="vertical" /> */}
            <h4 style={{ marginLeft: '26px' }}>|</h4>
            <h4>{subtitle}</h4>
          </>
        )}
      </div>
      <div className="uf-header-right-part">
        {/* {platform !== 'ILA' && showNav && (
          <Nav list={headerList} active={navActive} />
        )}
        {// subtitle === '系统管理' &&  //兼容统计模块
        platform !== 'ILA' && (
          <OtherNav
            list={[
              {
                key: 'joveone',
                name: '在线剪辑',
                href: '/joveone',
                target: 'joveone',
                disabled: !modules.includes(moduleCfg.jove),
                // ||!permissions.includes(perCfg.jove_use),
              },
              {
                key: 'textclip',
                name: '语音剪辑',
                href: '/textclip/#/clip/myTextClip',
                target: 'textclip',
                disabled: !modules.includes(moduleCfg.textclip),
              },
            ]}
          />
        )} */}
        {platform !== 'ILA' && (
          <Popover
            overlayClassName="message-popover"
            content={<MessageBox />}>
            <Badge count={unReadCount} offset={[5, 0]}>
              <div className="message-container">
                <IconFont type="iconxiaoxitongzhi" />
              </div>
            </Badge>
          </Popover>
        )}
        <UserAvatar
          platform={platform}
          username={userInfo?.nickName}
          avatar={userInfo?.avatar}
          workUrl={
            '/unifiedplatform/v1/app/workbench/direction'
          }
          work={modules.includes(moduleCfg.work)}
          // joveone={modules.includes(moduleCfg.jove) && permissions.includes(perCfg.jove_use)}
          // teacher={modules.includes(moduleCfg.teacher)}
          student={modules.includes(moduleCfg.student)}
          admin={modules.includes(moduleCfg.manager)}
          // rman={
          //   modules.includes(moduleCfg.rman) &&
          //   permissions.includes(perCfg.show_resource_management)
          // }
          // personal={modules.includes(moduleCfg.personal)}
          onLogout={handleLogout}
          capuman={
            size(
              intersection(
                modules,
                Object.values(ModuleCapumanCfg),
              ),
            ) > 0
          }
          utilization={
            modules.includes(moduleCfg.Utilization_M_) ||
            permissions.includes(moduleCfg.Utilization_U_)
          }
        />
      </div>
    </div>
  );
};
export const CUSTOMER_NPU = 'npu';

export default (props: any) => {
  const { parameterConfig, userInfo } = useSelector<
    { global: any },
    any
  >((state) => state.global);

  return (
    <>
      {parameterConfig?.target_customer === CUSTOMER_NPU ? (
        <div id="other-header">
          <NPUHeader
            token={userInfo?.extend?.jwt}
            {...props}
          />
        </div>
      ) : (
        <Header {...props} />
      )}
    </>
  );
};
