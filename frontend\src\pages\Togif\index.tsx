/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:29:57
 * @LastEditTime: 2023-09-14 10:30:05
 * @FilePath: \frontend\src\pages\IntelligentImage\index.tsx
 * @Description:
 */
import Header from '@/components/header/header';
import React, { useEffect, useState, useRef } from 'react';
import './index.less';
import {
  Affix,
  Button,
  Empty,
  Input,
  message,
  Upload,
  Slider,
  TreeSelect,
  Modal
} from 'antd';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
import Entity from '@/components/entity/entity.tsx'
import { useIntl, useSelector, useDispatch, IGlobal } from 'umi';
import WebUploader from './WebUploader';
import {
  fileToBlob,
} from '@/utils/utils';
import { CloudUploadOutlined, UploadOutlined } from '@ant-design/icons';
import { DraggerProps } from 'antd/lib/upload/Dragger';
import LoadingIcon from '@/images/IntelligentImage.png';
import timecodeconvert from '@/components/time-code/timeCode';
import downloadIcon from '@/images/download.png';
import copyIcon from '@/images/copy.png';
import regerateIcon from '@/images/regerate.png';
import { asyncLoadScript, copyObject, getUid } from '@/utils';
import _ from 'lodash';
import { togif } from '@/api/togif';
import {
  getResultById,
} from '@/db/controllers/Result';
import jQuery from 'jquery';
const Dragger = Upload.Dragger;

const Togif: React.FC<TranslateProps> = ({
  children,
}) => {
  const [isEmpty, setIsEmpty] = useState(false);
  const [text, setText] = useState('');
  const [selectedKey, setSelectedKey] = useImmer<
    React.Key | undefined
  >(undefined);
  const [renderBlob, setRenderBlob] = useImmer<{
    before?: Blob;
    after?: Blob;
  }>({
    before: undefined,
    after: undefined,
  });
  // 应该查询数据库
  useEffect(() => {
    (window as any).$ = (window as any).jQuery = jQuery;
    asyncLoadScript('/aitools/libs/webuploader/webuploader.js');
    asyncLoadScript('/aitools/libs/mam-timecode-convert/dist/mam-timecode-convert.js');
    getAllFields()
  }, []);

  const [loading, setLoading] = useState<boolean>(false)
  const dispatch = useDispatch();
  const intl = useIntl();
  const win = window as any;
  const [result, setResult] = useImmer<boolean>(false);
  const [gifModalVisible, setGifModalVisible] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<any>([]);
  const [showplaceholder, setShowplaceholder] = useState<string>('');
  const [file, setFile] = useImmer<any>(null);
  const [duration, setDuration] = useState<number>(25);
  const [finalFilePath, setFinalFilePath] = useImmer<string>('');
  const [metadata, setMetadata] = useState<any>({})
  const [inpoint, setInpoint] = useState<number>(0);
  const [outpoint, setOutpoint] = useState<number>(100);
  const {
    taskPanls,
  } = useSelector<{ upload: IUpload }, IUpload>(({ upload }) => {
    return upload;
  });
  const stateRef = useRef<any>()
  useEffect(() => {
    stateRef.current = taskPanls
  }, [taskPanls])
  useEffect(() => {
    fetchTree()
  }, [])
  const [imgUrl, setImgUrl] = useImmer<string>('')
  const [files, setFiles] = useState<any>([]);
  const player = useRef<any>(null);
  const typeMapping: { [propsName: string]: string } = {
    picture: 'biz_sobey_picture',
    video: 'biz_sobey_video',
    audio: 'biz_sobey_audio',
    document: 'biz_sobey_document',
    other: 'biz_sobey_other',
  };
  let sectionData: SmartTypes.SequenceMeta = {}
  const { userInfo } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  async function submit(blob: Blob, file?: any) {
    // setLoading((draft) => { draft.submit = true; });
    // 更新数据库
    try {
      // const suffix = getBlobTypeToSuffix(blob);
      let afterBlob: Blob | undefined;
      let title: string | undefined;

      title = file.name;
      // const {
      //   data,
      //   message,
      // } = await IntelligentImageService.uploadFile(
      //   blobToFile(blob, file.name),
      // );
      // setImgUrl(data)
      // IntelligentImageService.ocrTask({
      //   taskUid: file.uid,
      //   imagePath: 'http://172.16.151.202' + data
      // }).then((res) => {
      //   const timer = setInterval(async () => {
      //     const res = await IntelligentImageService.getOcrTask({ taskUid: file.uid })
      //     console.log(res, 'res')
      //     if (res?.data.extend_message[0].task_status === 3) {
      //       clearInterval(timer);
      //       let str = ''
      //       res?.data.extend_message[0].result.forEach((item: { words: string; }) => {
      //         str += item.words
      //       })
      //       setText(str)
      //       setResult(true)
      //       setLoading((draft) => { draft.submit = false; });
      //     }
      //   }, 500)
      // })
      // console.log(data, 'data')
    } catch (error) {
      setRenderBlob((draft) => {
        draft.before = undefined;
      });
      //@ts-ignore
      message.error(error.message);
    }

  }
  // 路径转译
  const getDirTeeStr1 = (dirTree?: string): string => {
    if (!dirTree) {
      return ''
    }
    if (dirTree.includes('global_sobey_defaultclass/public')) {
      return dirTree.replace('global_sobey_defaultclass/public', '').replace('/', '');
    } else if (dirTree.includes('global_sobey_defaultclass/private')) {
      let newdir = dirTree.replace(
        'global_sobey_defaultclass/private',
        '个人资源',
      );
      if (newdir.includes('/')) {
        let alldir = newdir.split('/');
        if (alldir.length >= 2) {
          newdir = newdir.replace('/' + alldir[1], '');
        }
      }
      return newdir;
    } else {
      return dirTree;
    }
  };
  const onSelect = (key: any) => {
    setShowplaceholder(getDirTeeStr1(key));
    setSelectedKey(key)
  };
  const fetchTree = () => {
    togif.gettreebylevel(2).then((res: any) => {
      if (res && res.data && res.success) {
        let data = res.data;
        let newData: any = [];
        data.map((item: any) => {
          if (item.name === '公共资源') {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        newData = newData.filter((item: any) => item.name != '录播资源');
        const rootData = newData.map((item: any) => {
          return {
            key: item.path,
            value: item.path,
            title: item.name,
            id: item.id,
            disabled: (item.name === '群组资源') ? true : false,
          };
        });
        setTreeData(rootData);
      }
    });
  };
  const getAllFields = () => {
    Object.keys(typeMapping).forEach(key => {
      togif.getAllFieldsByType(typeMapping[key]).then(res => {
        if (res && res.data) {
          res.data.forEach(item => {
            if (!item.value) {
              item.value = item.isArray ? [] : '';
            }
          });
          const value: any = {};
          value[typeMapping[key]] = res.data;
          setMetadata((pre: any) => Object.assign(pre, value))
        }
      });
    });
  };
  const uploadFile = async (files) => {
    setLoading(true)
    setResult(false)
    const newTaskPanls: any = copyObject(taskPanls);
    let showFlag = false;
    //获取每一个上传对象的存储方式 start
    const param = _.map(files, (item) => ({
      fileLength: item.size,
      fileName: item.name,
      fileType: item.type.includes("image")
        ? "picture"
        : item.type === "video"
          ? "video"
          : "other",
      poolType: window.localStorage.getItem('upform_platform') === 'Lark' ? 'ROLE' : '',
      // isPrivate:props.targetFolder.includes('global_sobey_defaultclass/private') //v3 暂时弃用
      pathType: 1
    }));
    const storageConfig: any = await togif.storageConfig(param);
    if (!storageConfig?.success) return;
    //获取每一个上传对象的存储方式 end
    for (let index = 0; index < files.length; index++) {
      const item: any = files[index];
      console.log(item, 'item')
      item.uid = getUid()
      const storage_: any = storageConfig.data[index];
      console.log(metadata, 'metadata')
      console.log(typeMapping[item.type.includes("image")
        ? "picture"
        : item.type === "video"
          ? "video"
          : "other"])
      const metadataTypes = metadata[typeMapping[item.type.includes("image")
        ? "picture"
        : item.type === "video"
          ? "video"
          : "other"]];
      const uploadMetas = _.map(metadataTypes, (item) => ({
        ...item,
        value:
          item.fieldName === "name" || item.fieldName === "name_"
            ? files[index].name.substring(0, files[index].name.lastIndexOf("."))
            : item.value,
      }));
      //判断存储方式
      if (!storage_.access_type) continue;
      if (storage_.access_type === 'NAS') {
        showFlag = true;
        
        const file = new (window as any).WebUploader.Lib.File(
          (window as any).WebUploader.Base.guid(),
          // orginTasks[index],
          item.originFileObj
          // item.file.originFileObj?.size || item.file //兼容拖拽有originFileObj的bug
        );
        file.guid = (window as any).WebUploader.Base.guid();
        file.metadata = uploadMetas;
        file.folderPath = `global_sobey_defaultclass/private/${userInfo.userCode}`;
        file.fileGuid = storage_.path;//针对同名文件增加不同标识
        console.log('NAS', file)
        // @ts-ignore
        const webUploader = new WebUploader({
          uploadError,
          uploadComplete,
        });
        webUploader.addFiles(file);
        const files = webUploader.getFiles();
        files.forEach((item: any) => {
          if (
            newTaskPanls.filter(i => i.guid === item.source.guid).length === 0
          ) {
            console.log(item.source.guid, 'item.source.guid')
            newTaskPanls.push({
              uploader: webUploader,
              name: item.name,
              size: item.size,
              status: 0,
              progress: 0,
              index,
              guid: item.source.guid,
              uploading: false,
              pause: false,
            });
          }
        });
        dispatch({
          type: 'upload/setTaskPanls',
          payload: {
            value: newTaskPanls,
          },
        });
      }
    }
  };
  const uploadError = (file: any) => {
    message.error(
      file.name +
      intl.formatMessage({
        id: 'upload-error',
        defaultMessage: '上传失败',
      }),
    );
    setLoading(false)
  };
  const uploadComplete = (file: any, newWebUploader: any) => {
    togif.filemerge(file.source.guid, file.name, file.source.fileGuid).then(res => {
      if (res?.success) {
        pollMergeStatus(file, newWebUploader);
      } else {
        uploadError(file);
      }
    });
  };
  /**
  * 轮询合并状态
  * @param file
  * @param newWebUploader
  */
  const pollMergeStatus = async (file: any, newWebUploader: any) => {

    const res = await togif.fetchMergeStatus(file.source.guid);
    if (res?.data?.state === 1 && res.data.finalFilePath) {
      setFinalFilePath(res.data.finalFilePath);
      togif
        .uploadImport(
          `global_sobey_defaultclass/private/${userInfo.userCode}`,
          res.data.finalFilePath,
          file.source.metadata as IFormItem[],
          file.size,
        )
        .then(dd => {
          if (dd && dd.data && dd.success) {
            dispatch({
              type: 'upload/updateTaskPanls',
              payload: {
                value: { guid: file.source.guid, progress: 1 },
              },
            });

            // 从队列中删除
            newWebUploader.removeFile(file, true);

            // 删除task
            // 弹窗提示
            setLoading(false)
            message.success(
              file.name +
              intl.formatMessage({
                id: 'upload-success',
                defaultMessage: '上传成功',
              }),
            );
            setResult(true)
          } else {
            uploadError(file);
          }
        });
    } else if (res?.data?.state === 0) {
      // 手动移除掉的任务 停止轮询
      const realTaskPanls = stateRef.current;
      if (realTaskPanls.some((item: any) => item.guid === file.source.guid)) {
        setTimeout(() => {
          pollMergeStatus(file, newWebUploader);
        }, 500);
      }
    } else if (res?.data?.state === -1 && res?.data.errorMsg) {
      message.error(res?.data.errorMsg);
      uploadError(file);
    } else {
      uploadError(file);
    }
  };
  const draggerConfig = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.mp4,.m4v,.avi,.flv,.webm,.wmv,.mkv',
    onChange(info) {
      // setRenderBlob((draft) => { draft.before = info.file.originFileObj });
      uploadFile(info.fileList)

      // submit(
      //   fileToBlob(info.file.originFileObj),
      //   info.file,
      // );
    },
    beforeUpload: (file) => {
      // 大于10mb
      // if (file.size > 10 * 1024 * 1024) {
      //   message.error('文件超过10MB');
      //   return false;
      // }
      return true;
    },
    className: 'dragger',
    // ...uploadProps,
  };
  const uploadProps = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.mp4,.m4v,.avi,.flv,.webm,.wmv,.mkv',
    onChange(info) {
      uploadFile(info.fileList)
    }
  };
  const uploadDefault = () => {
    return true
  }
  // 转gif
  const importGif = () => {
    console.log(player.current)
    console.log(player.current.duration * inpoint, inpoint)
    console.log(player.current.duration * outpoint, outpoint)
    togif.importGif({ inPoint: parseInt((player.current.duration * inpoint / 100 * 10000000).toString()), outPoint: parseInt((player.current.duration * outpoint / 100 * 10000000).toString()), videoPath: finalFilePath,saveTree:[selectedKey] }).then(res => {
      if (res?.success) {
        message.success('保存成功')
        setGifModalVisible(false)
      }
    })
  }
  const drag = (e) => {
    e.preventDefault()
    const container = e.currentTarget
    const width = container.offsetWidth
    const mousex = e.clientX
    const leftp = (mousex / width) * 100
    const rightp = 100 - leftp
    container.style.setProperty('--lfet-width', `${leftp}%`)
    container.style.setProperty('--right-width', `${rightp}%`)
  }
  const sliderChange = (value: number[]) => {
    setInpoint(value[0])
    setOutpoint(value[1])
    let _inpoint = 0
    if (value[0] === inpoint) {
      _inpoint = player.current.duration * value[1] / 100
    }
    else {
      _inpoint = player.current.duration * value[0] / 100
    }
    console.log(_inpoint, 'inoint')
    player.current?.setCurrentTime(_inpoint);
  }
  const handleSuccess = (play: Player) => {
    // console.log('permissions', permissions)
    player.current = play;
    setDuration(play.duration)
    if (!(play as any).isVideo) {  //如果不是视频就中断
      return
    }
  };
  const setPlayerSec = (inpoint: number, outpoint: number, data: any, canModify: boolean = true) => {
    sectionData = { ...data };
    const _inpoint = win.TimeCodeConvert.l100Ns2Second(inpoint),
      _outpoint = win.TimeCodeConvert.l100Ns2Second(outpoint);
    // console.log(inpoint, outpoint);
    // console.log(_inpoint, _outpoint);
    player.current?.hideModifyBtn(true);
    player.current?.setTrimin(_inpoint);
    player.current?.setTrimout(_outpoint);
    player.current?.setCurrentTime(_inpoint);
    if (canModify) {
      player.current?.showModifyBtn();
    }
    // player.current?.play();
    // setPlayFlag(true);
  };
  const onLoadChild = (node: any, isRoot: boolean = false) => {
    const { key, children, code, title } = node;
    return new Promise(async (resolve) => {
        if (key === 'add') {
            resolve(null);
            return;
        }
        if (children) {
            resolve(null);
            return;
        }
        function updateTreeData(list: any, key: React.Key, children: any): any {
            return list.map((node: any) => {
                if (node.key === key) {
                    return {
                        ...node,
                        children,
                    };
                } else if (node.children) {
                    return {
                        ...node,
                        children: updateTreeData(node.children, key, children),
                    };
                }
                return node;
            });
        }
        const res: any = await togif.loadChild(key, true);
        if (res && res.data && res.success) {
            let treeList = res.data;
            treeList = treeList.filter((item: any) => {
                return item.name !== '录播资源';
            });
            setTreeData((origin: any) =>
                updateTreeData(
                    origin,
                    key,
                    treeList.map((item: any) => {
                        return {
                            key: item.path,
                            title: item.name,
                            id: item.contentId,
                            value: item.path,
                        };
                    }),
                ),
            );
            resolve(null);
        }
    });
  };
  return (
    <div className='retrieval'>
      <Affix offsetTop={0}>
        <Header showNav={true} customTitle="视频转GIF工具" />
      </Affix>
      <div className='content'>
        <main>
          <div className='renderContent'>
            <div className='showArea'>
              <Access accessible={!loading && !result}>
                <div className='new'>
                  <div className='topTip'>
                    <p className='title'>视频转GIF工具</p>
                    <p style={{ margin: '15px 0' }}>上传视频，帮助您截取视频片段并生成GIF动图。</p>
                    <p>
                      上传要求：请上传mp4,m4v,avi,flv,webm,wmv,mkv格式视频。
                    </p>
                  </div>
                  <Dragger {...draggerConfig} customRequest={uploadDefault}>
                    <p className="ant-upload-drag-icon">
                      <CloudUploadOutlined />
                    </p>
                    <p className='uploadText'>
                      选择本地文档
                      <span className='strong'>
                        或拖拽至此区域
                      </span>
                    </p>
                  </Dragger>
                </div>
              </Access>
              <Access accessible={loading && !result}>
                <div className='loading'>
                  <img src={LoadingIcon} />
                  <p>上传中...</p>
                </div>
              </Access>
              <Access accessible={!loading && result}>
                <div className='entity_view'>
                  <div className='entity_view_wrapper'>
                    <Entity
                      id='toGifPlayer'
                      onSuccess={handleSuccess}
                      src={finalFilePath.substring(9, finalFilePath.length)}
                      type='video'
                    />
                  </div>
                  <div className='slide-box'>
                    <Slider range={{ draggableTrack: true }} value={[inpoint, outpoint]} onChange={sliderChange} />
                  </div>
                  <div className='bottom'>
                    <div className='bottom-text'>
                      拖拽两头，确认动画的开始与结束位置。
                    </div>
                    <div className='btn'>
                      <Upload {...uploadProps} customRequest={uploadDefault}>
                        <Button icon={<UploadOutlined />} type='primary'>重新上传</Button>
                      </Upload>
                      <Button onClick={()=> setGifModalVisible(true)}>生成GIF</Button>
                    </div>
                  </div>
                </div>
              </Access>
            </div>
          </div>
        </main>
      </div>
      <Modal
        destroyOnClose
        maskClosable={false}
        visible={gifModalVisible}
        title='保存路径'
        onOk={importGif}
        onCancel={() => setGifModalVisible(false)}
        className='uploadbox'
        width={500}
      >
        <div className='right'>
          <span>保存至</span>
          <TreeSelect
            // placeholder={showplaceholder}
            treeData={treeData}
            value={showplaceholder}
            onSelect={onSelect}
            loadData={onLoadChild}
          />
        </div>
      </Modal>
    </div>
  );
};

interface TranslateProps {
  children: React.ReactNode;
}
export default Togif;
Togif.displayName = 'Togif';
