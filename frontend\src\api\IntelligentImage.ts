import { request } from 'umi';
import { Request } from '@/constant/Request';
import {
  generatContentType,
  objToFormData,
} from '@/utils/utils';
import { IResponse, RmanResponse } from '.';

export namespace IntelligentImageService {
  export const uploadFile = (file: File) => {
    return request(`/v1/upload/save/file`, {
      method: 'post',
      data: objToFormData({
        file,
      }),
      prefix: Request.PREFIX.RMAN,
    });
  };
  export const downloadFile = (filename: string) => {
    return request<Blob>(
      `/v1/translation/download/${filename}`,
      {
        method: 'get',
        prefix: Request.PREFIX.LEARN,
        responseType: 'blob',
      },
    );
  };

  export const chineseAndEnglish = (data: {
    content: string;
  }) => {
    return request<RmanResponse<string>>(
      `/v1/translation/between/chinese/and/english`,
      {
        method: 'post',
        data,
        prefix: Request.PREFIX.LEARN,
      },
    );
  };
  export const ocrTask = (data: any) => {
    return request(`/v1/intelligent/ocr/task`, {
      method: 'post',
      data,
      prefix: Request.PREFIX.RMAN,
    });
  };
  export const getOcrTask = (params: any) => {
    return request(`/v1/intelligent/ocr/text`, {
      method: 'get',
      params,
      prefix: Request.PREFIX.RMAN,
    });
  };
}

