<?xml version="1.0" encoding="UTF-8"?>
<svg width="38px" height="36px" viewBox="0 0 38 36" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 13备份</title>
    <defs>
        <linearGradient x1="8.59041622%" y1="11.701757%" x2="95.8971422%" y2="94.8753463%" id="linearGradient-1">
            <stop stop-color="#FF6F3F" offset="0%"></stop>
            <stop stop-color="#FFC348" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="切图" transform="translate(-370.000000, -60.000000)">
            <g id="编组-13备份" transform="translate(370.000000, 60.000000)">
                <rect id="矩形" fill="url(#linearGradient-1)" x="0" y="0" width="38" height="36" rx="6"></rect>
                <rect id="矩形" fill="#FFFFFF" x="8" y="3" width="22" height="2" rx="1"></rect>
                <path d="M9.12499999,24.6448598 L6,29.8691589 C7.5,30.2336449 9,30.5981309 10.625,30.9626168 L10.75,31.0841122 C12.125,32.4205608 13.5,33.8785047 13.5,33.8785047 L16.375,29.0186916 C13.625,28.5327103 10.75,26.7102803 9.12499999,24.6448598 L9.12499999,24.6448598 Z" id="路径备份" fill="#FFD728" fill-rule="nonzero"></path>
                <path d="M28.625,24.6448598 C27,26.7102804 24.375,28.5327103 21.625,29.1401869 L24.5,34 C24.5,34 26.25,32.0560747 27.625,30.7196262 C29.625,30.2336449 31.875,29.8691589 32,29.8691589 L28.625,24.6448598 Z" id="路径备份-2" fill="#FFD728" fill-rule="nonzero"></path>
                <path d="M19,8 C24.375,8 29.125,12.4953271 29.125,17.8411215 C29.125,23.1869159 24.5,27.682243 19,27.682243 C13.5,27.682243 8.87499998,23.4299066 8.87499998,18.0841122 C8.87499998,12.7383178 13.625,8 19,8 Z M19,11.8878505 C18.5,11.8878505 18.125,12.1308411 18.125,12.3738318 L18.125,12.3738318 L17.125,14.5607476 C16.875,14.9252336 16.625,15.1682243 16.25,15.1682243 L16.25,15.1682243 L14,15.5327103 C13.375,15.6542056 12.875,16.1401869 13,16.7476636 C13,16.9906542 13.125,17.2336449 13.25,17.3551402 L13.25,17.3551402 L15,19.1775701 C15.25,19.4205607 15.25,19.7850467 15.25,20.0280374 L15.25,20.0280374 L14.875,22.5794393 C14.75,23.1869159 15.125,23.6728972 15.75,23.7943926 C16,23.7943926 16.25,23.7943926 16.5,23.6728972 L16.5,23.6728972 L18.5,22.7009346 C18.75,22.5794393 19.125,22.5794393 19.5,22.7009346 L19.5,22.7009346 L21.5,23.7943926 C22,24.0373832 22.75,23.7943926 23,23.3084112 C23.125,23.0654206 23.125,22.8224299 23.125,22.5794393 L23.125,22.5794393 L22.75,20.1495327 C22.625,19.906542 22.75,19.5420561 23,19.2990654 L23,19.2990654 L24.75,17.4766355 C25.25,16.9906542 25.125,16.3831776 24.75,15.8971963 C24.625,15.7757009 24.375,15.6542056 24.125,15.6542056 L24.125,15.6542056 L21.875,15.2897196 C21.5,15.2897196 21.125,15.046729 21,14.682243 L21,14.682243 L20,12.4953271 C19.875,12.1308411 19.5,11.8878505 19,11.8878505 Z" id="形状结合" fill="#FFFFFF" fill-rule="nonzero"></path>
            </g>
        </g>
    </g>
</svg>