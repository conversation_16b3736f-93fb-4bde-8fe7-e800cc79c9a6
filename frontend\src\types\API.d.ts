declare namespace API {
  interface IResponse<T = any> {
    status: number;
    data: any;
    errorCode: string;
    errorMsg: string;
    extendMessage: T;
  }
  interface IResponseEdu<T = any> {
    statusCode: number;
    message: string;
    data: T;
  }
  interface IResponseLearn<T = any> {
    status: number;
    message: string;
    data: T;
  }

  interface IResponseList<K = any> extends IResponse {
    extendMessage: {
      pageIndex: number;
      pageSize: number;
      pageTotal: number;
      recordTotal: number;
      results: K[];
    };
  }

  interface IResponseListV2<K = any> extends IResponse {
    extendMessage: {
      page: number;
      size: number;
      pageTotal: number;
      total: number;
      results: K[];
    };
  }

  interface IResponseEduList<K = any> extends IResponseEdu {
    data: {
      currentPage: number;
      pageSize: number;
      totalPages: number;
      totalItems: number;
      items: K[];
    };
  }

  interface ISetting {
    title: string;
    theme: string;
    logoUrl: string;
    themeColor:string
  }
  
  /**
   * 通用list data结构
   */
   export interface Data<T> {
    pageTotal?: number;
    pageIndex?: number;
    pageSize?: number;
    recordTotal?: number;
    results: Array<T>;
  }
   /**
   * query list 通用response
   */
    export interface ResponseList<T = object> extends IResponse<Data<T>> {}
}
