.retrieval {}

.content {
  width: 100%;
  height: calc(100vh - 52px);
  display: flex;

  >main {
    width: 100%;
    background-color: rgb(249, 249, 249);

    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .renderContent {
      height: 100%;
      display: flex;
      padding: 20px;

      >* {
        flex: 1;
        height: 100%;
        .title {
          width: 100%;
          height: 30px;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: 800;
          height: 36px;
        }

        .showArea {
          height: calc(100% - 52px);
          padding: 20px;
        }
      }

      .before {
        background-color: #fff;

        .title {
          background-color: #f7f9fa;
        }

        .textarea {
          position: relative;

          .btn {
            position: absolute;
            right: 20px;
            bottom: 20px;
          }
        }
      }

      .after {
        .title {
          background-color: #edf5ff;
          color: rgb(84, 156, 255);
        }
      }
    }
  }
}

.tip {
  font-size: 12px;
  color: gray;
  text-align: center;
}

.uploadText {
  .tip();
  font-size: 16px;
  padding-bottom: 20px;
  color: #2e2e2e;
  font-weight: 800;
  line-height: 22px;

  .strong {
    font-weight: 400;
    color: #868686;
    display: block;
    font-size: 12px;
    line-height: 22px;
  }
}
.topTip {
  margin-top: 100px;
  p {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: #525252;
  }
  .title {
    font-size: 18px;
    font-weight: 500;
    color: #2E2E2E;
  }
}
.dragger {
  margin-top: 30px;
  height: 380px !important;
  position: relative;

  .bottom {
    display: flex;
    justify-content: center;

    .resourceBtn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 32px;
      background: #ffffff;
      border-radius: 16px;
      border: 1px solid #a4a4a4;
      color: #868686;
      width: 124px;
    }
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #a4a4a4;
  flex-direction: column;
  height: 100%;

  img {
    width: 40px;
    height: 40px;
  }

  p {
    font-size: 14px;
    line-height: 20px;
    margin-top: 10px;
  }
}

.result {
  display: flex;
  justify-content: space-between;
  height: 100%;

  >div {
    flex: 1;
    background: #FFFFFF;
    padding: 30px;
  }

  .result-left {
    margin-right: 30px;

    .btn-upload {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 92px;
    }

    .img-preview {
      padding: 50px 0;
      background: #F7F9FA;
      border-radius: 10px;

      img {
        width: 100%;
        object-fit: contain;
        height: 630px;
      }
    }
  }

  .result-right {
    padding-top: 122px;

    .content-text {
      padding: 23px;
      background: #F7F9FA;
      margin-bottom: 10px;
      font-size: 14px;

      .text-title {
        font-weight: 400;
        color: #A4A4A4;
        margin-bottom: 20px;
      }

      .text {
        color: #2E2E2E;
      }
    }

    .btn-list {
      display: flex;
      gap: 20px;
      >div {
        font-size: 12px;
        font-weight: 400;
        cursor: pointer;
        color: #A4A4A4;
        img{
          margin-right: 5px;
        }
      }
    }
  }
}