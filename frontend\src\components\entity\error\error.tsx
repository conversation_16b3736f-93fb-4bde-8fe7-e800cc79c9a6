import React, { FC } from 'react'
import './style.less'
import { useIntl } from 'umi'

const Error:FC<any> = ({errorCode,describe}) => {
    const intl = useIntl()
    return (
        <div className="entity-error">
            <img src={errorCode?
                (errorCode===401?'/rman/static/images/no_access.png':errorCode===404?'/rman/static/images/not_found.png':
                errorCode===403?'/rman/static/images/load_error.png':'')
                :'/rman/static/images/load_error.png'}/>
            <span>
            { errorCode?
                (errorCode===401?'您无权访问该资源':errorCode===404?'资源已不存在':
                errorCode===403?'文件分析失败':'')
                :
                describe||intl.formatMessage({
                    id: 'loading-error',
                    defaultMessage: '加载出错了'
                })
            }
            </span>
        </div>
    )
}

export default Error