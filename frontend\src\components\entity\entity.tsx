import React, { FC, useEffect, useMemo, useRef, useState } from 'react';
import './style.less';
import { entityType } from '@/types/entityTypes';
import { useIntl } from 'umi';
import { H5PlayerProps } from '@/components/h5player';
import H5Player from '@/components/h5player';
interface IEntityProps extends H5PlayerProps {
  type: entityType;
  src: string;
  keyframes?: string;
  frameRate?: number;
  shareFlag?: boolean;
}

const Entity: FC<IEntityProps> = ({
  type,
  id,
  src,
  keyframes,
  frameRate,
  shareFlag,
  onSuccess,
  setSection,
  getKeyframe,
  translateFlag,
  onPlayChange,
  modifySection,
  errorCode,
  resetSection,
  config,
  isEditPermission,
  contendId,
  contrast,
  brightness,
  saturate,
}) => {
  const [error, setError] = useState<boolean>(false);
  const intl = useIntl();
  const render = () => {
    switch (type) {
      case 'video':
        return (
          <H5Player
            brightness={brightness}
            contrast={contrast}
            saturate={saturate}
            src={src}
            id={id}
            config={config}
            onError={() => setError(true)}
            onSuccess={onSuccess}
            frameRate={frameRate}
            setSection={setSection}
            translateFlag={translateFlag}
            getKeyframe={getKeyframe}
            onPlayChange={onPlayChange}
            modifySection={modifySection}
            resetSection={resetSection}
            isEditPermission={isEditPermission}
            contendId={contendId}
            shareFlag={shareFlag}
          />
        );
      // }else{
      //   return <PDF src={src} onError={() => setError(true)} />
      // }
      // return isHtml(src) ? (
      //   <HTML src={src} onError={() => setError(true)} />
      // ) : (
      //   <PDF src={src} onError={() => setError(true)} />
      // );
    }
  };
  return render();
};

export default Entity;
