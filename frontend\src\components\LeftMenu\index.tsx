import React, { useEffect, useRef } from 'react';
import { useSelector } from 'umi';

const LeftMenu: React.FC<any> = () => {
  const { userInfo, menuShow } = useSelector<any, any>(
    (state) => state.global,
  );

  const commonMenu = useRef<any>();
  const getMenus = (header: string) => {
    commonMenu.current = new CommonMenu('menu-header-box', {
      headerUrl: header,
      headerHeight: 'calc(100vh - 52px)',
    });
    commonMenu.current.open(); // 开启挂载
  };
  useEffect(() => {
    getMenus(require('@/images/default-avatar.png'));
  }, []);
  useEffect(() => {
    commonMenu.current.changeExpand(menuShow);
  }, [menuShow]);
  useEffect(() => {
    if (userInfo?.avatar) {
      commonMenu.current.setHeader(userInfo.avatar);
    }
  }, [userInfo]);
  return <div id="menu-header-box"></div>;
};

export default LeftMenu;
