import { message } from 'antd';
const abortController = new AbortController();
const signal = abortController.signal;


interface configTYpe {
    record_id: string;
    role: string;
    event: string;
    content: any;
}

let controller: any = '';

// 中断请求
export const stopRequest = () => {
    controller && controller?.abort('请求取消');
}

export async function requestSendMessage(url: string, options: any, overback: (content: any, config?: any) => void) {
    try {
        if (controller) controller = null;
        controller = new AbortController();
        const signal = controller.signal;
        const res = await fetch(url, { ...options, signal });
        let responseText = "";
        let uUint8arr: any = [];

        if (res.ok) {
            const reader = res.body?.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const content = await reader?.read();
                if (content?.value) {
                    uUint8arr.push(...content.value);
                    responseText = decoder.decode(new Uint8Array(uUint8arr));
                }
                console.info('返回的部分消息：', responseText);
                //   callback(responseText);
                const done = !content || content.done;
                if (done) {
                    // abortController.abort();
                    const responseTextTOString = responseText;
                    let responseTextItems = responseTextTOString.split('data:'); // 将字符串根据data:分割为一条一条的消息
                    responseTextItems = responseTextItems.filter(item => item); // 去掉空字符串对象
                    responseTextItems = responseTextItems.map(item => JSON.parse(item)); // 转为json对象


                    let config = responseTextItems.find((item: any) => item.role === 'tools'); // 返回的配置信息

                    if (!config) {
                        config = responseTextItems.find((item: any) => item.role === 'assistant'); // 返回的配置信息
                    }
                    const addResponseContent = responseTextItems.filter((item: any) => item.role === 'assistant'); // 返回的消息信息
                    const messageContent = addResponseContent.reduce((message, item: any) => message + item.content, '');
                    if (!messageContent) {
                        message.info('智能解析返回异常，请稍后重试！')
                        overback('');
                    } else {
                        overback(messageContent, config);
                    }

                    break;
                }
            }

        } else if (res.status === 401) {
            console.error("Anauthorized");
            message.info('智能解析服务未授权，请稍后再试！')
            overback('');

        } else {
            console.error("Stream Error");
            message.info('智能解析流异常，请稍后再试！')
            overback('');
        }
    } catch (err) {
        console.error("NetWork Error", err);
        // message.info('智能解析网络异常，请稍后再试！')
        overback('');
    }
}

/**
 * 流处理
 * @param url  url地址
 * @param data 请求body
 * @returns
 */
export const postStream: (url: string, data?: unknown) => Promise<any> | any = (
    url,
    data
  ) => {

    const headers: HeadersInit = { 'Content-Type': 'application/json' }
    return fetch(url, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      signal,
      headers: headers
    })
  }