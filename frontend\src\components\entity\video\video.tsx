import React, { FC, useEffect, useRef, useState } from 'react'
import { IBaseEntityTypes } from '@/types/entityTypes'
import { dynamic } from 'umi'
import { IPlayerProps } from '../player/player'
import Loading from '@/loading'



interface IVideoProps extends IBaseEntityTypes {
    keyframes: string // 封面的路径
}


const AsyncPlayer = (() => dynamic({
    loader: async () => {
        const { default: Player } = await import('../player/player')
        return Player
    },
    loading:Loading
}) as unknown as React.ComponentClass<IPlayerProps, any>)()

const Video: FC<IVideoProps> = ({ src, keyframes, onError }) => {
    return <AsyncPlayer type="video" src={src} keyframes={keyframes} onError={onError}></AsyncPlayer>
}

export default Video