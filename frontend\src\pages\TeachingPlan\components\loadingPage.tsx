import React, { <PERSON> } from 'react';
import <PERSON>tie from 'lottie-react';
import animationData from './generate.json';
interface LoadingPageProps {
  showLoading: boolean;
}
const LoadingPage: FC<LoadingPageProps> = ({ showLoading }) => {
  return (
    <div className='loading-page' style={{ display: showLoading ? 'block' : 'none' }}>
      <div className='loading-header'>
        <img src={require('@/assets/img/teachingPlan/ok.png')} />
        教案生成中...
      </div>
      <div className='loading-content'>
        <div className='animation-content'>
          <Lottie animationData={animationData} loop={true} style={{ width: '100%', height: '100%' }} />
        </div>
        <p>教案生成中...</p>
      </div>
    </div>
  );
};

export default LoadingPage;