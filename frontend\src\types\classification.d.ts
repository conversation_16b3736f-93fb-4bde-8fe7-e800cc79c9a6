/*
 * @Author: 李晋
 * @Date: 2021-10-29 14:27:30
 * @Email: <EMAIL>
 * @LastEditTime: 2021-11-24 15:42:07
 * @Description: file information
 * @Company: Sobey
 */
declare namespace ClassificationType {
  /**
   * 课程分类列表查询参数
   */
  export interface ClassificationParam {
    is_enable?: number; //是否启用，1：启用；0：禁用
    sort?: number; //排序方式，1：升序；0：降序
  }
  /**
   * 课程分类项
   */
  export interface CourseClassification {
    id: number; // int32
    code: string;
    name: string;
    isEnable?: number; // 是否启用，1：启用；0：禁用
    order?: number; // int32
    courseConfig?: string;
    rows: number;
  }
    /**
   * 课程分类项修改
   */
  export interface CourseClassificationAdd {
    name?: string;
    isEnable?: number; // 是否启用，1：启用；0：禁用
    order?: number; // int32
  }
  /**
   * 课程分类项--添加自定义属性
   */
  export interface CourseClassificationUI extends CourseClassification {
    ifEdit: boolean;
  }
}
