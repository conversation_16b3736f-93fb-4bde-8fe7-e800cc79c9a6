/*
 * @Author: 李晋
 * @Date: 2021-11-30 17:19:44
 * @Email: <EMAIL>
 * @LastEditTime: 2021-12-07 16:18:13
 * @Description: 授权相关类型
 * @Company: Sobey
 */
declare namespace LicenseType {
  // 模块
  export interface LicenseModule {
    moduleCode?: string;
    moduleName?: string;
    subModule?: LicenseSubModule[];
  }
  // 子模块
  export interface LicenseSubModule {
    subModuleCode?: string;
    subModuleName?: string;
  }
  export interface LicenseProduction {
    productionCode?: string;
    productionName?: string;
  }
  export type AuthorizedStatus = 0 | 1 | 2;
  /**
   * 授权信息
   *
   * @export
   * @interface LicenseInfo
   */
  export interface LicenseInfo {
    customerName?: string;
    projectName?: string;
    remark?: string;
    production?: LicenseProduction;
    authorizedPerson?: string;
    authorizedStatus?: AuthorizedStatus; // int32
    authorizationCode?: string;

    modules?: LicenseModule[]; //模块
    signUpMachineCode?: string; // 机器码
    expiredTime?: string; // 失效时间
    authorizedDate?: string; // 注册时间
  }
  /**
   * 授权机器节点
   *
   * @export
   * @interface MachineNode
   */
  export interface MachineNode {
    randomCode: string;
    hostName: string;
    ip: string;
    cupId: string;
    createdAt: string;
    updatedAt: string;
  }
  /**
   * 机器码信息
   *
   * @export
   * @interface MachineCodeInfo
   */
  export interface MachineCodeInfo {
    mchachineCode?: string;
    nodes?: MachineNode[];
  }
}
