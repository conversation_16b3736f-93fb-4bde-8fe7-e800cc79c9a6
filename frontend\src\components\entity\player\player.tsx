import React, { Component } from 'react'
import videoJs, { VideoJsPlayer } from 'video.js'
import zhCN from 'video.js/dist/lang/zh-CN.json'
import "video.js/dist/video-js.min.css"
import { IBaseEntityTypes } from '@/types/entityTypes'

export interface IPlayerProps extends IBaseEntityTypes {
    keyframes: string
    type: 'video' | 'audio'
}

class Player extends Component<IPlayerProps>{
    private player: VideoJsPlayer | null = null
    private videoNode: HTMLVideoElement | HTMLAudioElement | null = null
    private constructor(props: IPlayerProps) {
        super(props)
    }
    public componentDidMount() {
        if (this.videoNode) {
            videoJs.addLanguage('zh-CN', zhCN)
            this.player = videoJs(this.videoNode, {
                language: 'zh-CN',
                // poster: this.props.keyframes //切换首帧
            }, () => {
                const pictureInPciture = document.querySelector('.vjs-picture-in-picture-control')
                if (pictureInPciture) {
                    pictureInPciture.className += ' vjs-hidden'
                }
            })
            this.player.on('error', (err) => {
                if (this.props.onError) {
                    this.props.onError()
                }
                // player.dispose()
            })
        }
    }
    public componentWillUnmount() {
        if (this.player) {
            this.player.dispose()
        }
    }
    public render() {
        return (
            <div style={{height:'100%'}}>
                <div className="player-container" data-vjs-player>
                    {
                        this.props.type === 'video' ?
                            <video ref={node => this.videoNode = node} disablePictureInPicture preload="auto" className="video-js vjs-big-play-centered" controls style={{ width: '100%',height:'100%' }}>
                                <source src={this.props.src} type={'video/mp4'}></source>
                            </video> :
                            <audio ref={node => this.videoNode = node} preload="auto" className="video-js vjs-big-play-centered" controls style={{ width: '100%',height:'100%' }}>
                                <source src={this.props.src} type={'audio/mpeg'}></source>
                            </audio>
                    }
                </div>
            </div>
            // <video id="player" disablePictureInPicture preload="auto" className="video-js vjs-big-play-centered" controls style={{ width: '100%', height: 'calc(100vh - 110px)' }}>
            //     <source src={this.props.src} type="video/mp4"></source>
            // </video>
        )
    }
}

export default Player