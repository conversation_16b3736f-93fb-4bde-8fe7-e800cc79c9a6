.content {
  width: 100%;
  display: flex;
  > main {
    width: 100%;
    background-color: rgb(249, 249, 249);
    .empty {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .renderContent {
      height: 100%;
      display: flex;
      > * {
        flex: 1;
        min-height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        max-height: calc(100vh - 52px);
        .title {
          width: 100%;
          height: 30px;
          display: flex;
          align-items: center;
          font-weight: 800;
          height: 36px;
          position: relative;
        }
        .showArea {
          height: calc(100% - 52px);
          padding: 20px;
        }
      }
      .before {
        background-color: #fff;
        .title {
          background-color: #f7f9fa;
        }
        .textarea {
          position: relative;
          .btn {
            position: absolute;
            right: 20px;
            bottom: 20px;
          }
        }
      }
      .after {
        background-color: #fff;
        border-left: 1px solid #edeff0;
        padding: 0 20px;
        .title {
          padding: 25px 0;
          border-bottom: 1px solid #edeff0;
        }
      }
    }
  }
}
.tip {
  font-size: 12px;
  color: gray;
  text-align: center;
}
.uploadText {
  .tip();
  font-size: 16px;
  padding-bottom: 20px;
  color: #2e2e2e;
  font-weight: 800;
  line-height: 22px;
  .strong {
    font-weight: 400;
    color: #868686;
    display: block;
    font-size: 12px;
    line-height: 22px;
  }
}
.dragger {
  margin-top: 30px;
  height: 380px !important;
  position: relative;
  .bottom {
    display: flex;
    justify-content: center;
    .resourceBtn {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 32px;
      background: #ffffff;
      border-radius: 16px;
      border: 1px solid #a4a4a4;
      color: #868686;
      width: 124px;
    }
  }

  .topTip {
    position: absolute;
    top: 10px;
    left: 10px;
    p {
      text-align: left;
      color: #a4a4a4;
      font-size: 12px;
      line-height: 22px;
    }
  }
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #a4a4a4;
  flex-direction: column;
  height: 100%;
  img {
    width: 40px;
    height: 40px;
  }
  p {
    font-size: 14px;
    line-height: 20px;
    margin-top: 10px;
  }
}

.desc_empty {
  display: flex;
  flex-direction: column;
  flex: 1;
  p {
    color: #525252;
    font-size: 12px;
    text-align: left;
    &:first-child {
      font-size: 16px;
      line-height: 30px;
      color: black;
      text-align: center;
    }
  }
}
