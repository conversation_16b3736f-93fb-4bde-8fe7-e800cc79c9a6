import React, { useState } from 'react';
import './index.less';
import { Button, Select, Tooltip } from 'antd';
import { languageOptions } from '../../sanhangutil';
import uploadicon1 from '@/images/sanhang/upload_icon1.png';
import copyicon1 from '@/images/sanhang/copy.png';
import shengying_icon1 from '@/images/sanhang/shengying.png';
import stop_icon1 from '@/images/sanhang/stop.png';
// import labaicon1 from '@/images/sanhang/laba.png';

interface TranSlateDomProps {
  sourceLanguage: string;   
  targetLanguage: string;
  model: string;
  translatedText?: string;
  onTranslate?: (text: string) => void;
}

const TranSlateDom: React.FC<TranSlateDomProps> = ({
  sourceLanguage,
  targetLanguage,
  model,
  translatedText,
  onTranslate
}) => {
  const [inputText, setInputText] = useState<string>('');
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const handlePlay = () => {
    if (translatedText) {
      if (window.speechSynthesis.speaking) {
        window.speechSynthesis.cancel();
      }
      const utterance = new SpeechSynthesisUtterance(translatedText);
      utterance.onend = () => {
        setIsSpeaking(false);
      };
      window.speechSynthesis.speak(utterance);
      setIsSpeaking(true);
    }
  };

  const handleStop = () => {
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  };

  const handleTranslateClick = () => {
    if (onTranslate) {
      setLoading(true);
      onTranslate(inputText);
    }
  };

  return (
    <div className="translate_dom">
      <div className="translate_input">
        <Select
            defaultValue="en"
            style={{ width: 150}}
            size='large'
            onChange={()=>{}}
            options={languageOptions}
        />
        <textarea 
          placeholder="请输入要翻译的内容" 
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
        />
        <div className='option_box'>
          <Tooltip title="上传文件">
            <img src={uploadicon1} alt="上传" />
          </Tooltip>
          <Button type="primary" loading={loading} onClick={handleTranslateClick}>翻译</Button>
        </div>
      </div>
      <div className="translate_output">
        <Select
            defaultValue="cn"
            style={{ width: 150}}
            size='large'
            onChange={()=>{}}
            options={languageOptions}
        />
        <div className='output_box'>
            <span>{translatedText || '翻译结果'}</span>
        </div>
        <div className='option_box'>
            <Tooltip title="复制翻译结果">
              <img className='copy' src={copyicon1} alt="复制" />
            </Tooltip>
            <div className={`bofang ${!translatedText ? 'disabled' : ''}`}>
              {isSpeaking ? (
                <Tooltip title="停止朗读">
                  <img src={stop_icon1} alt="停止" onClick={handleStop} />
                </Tooltip>
              ) : (
                <Tooltip title="朗读翻译结果">
                  <img src={shengying_icon1} alt="朗读" onClick={handlePlay} />
                </Tooltip>
              )}
            </div>
        </div>
      </div>
    </div>
  );
};

export default TranSlateDom;
