declare namespace BasicData {
  interface ISubject {
    code: string;
    name: string;
    desc: string;
  }

  interface ISemester {
    id?: string;
    code?: string;
    name?: string;
    startTime: string;
    endTime: string;
    semester:string;
    startYear:string;
    endYear:string;
  }

  interface IFixItem {
    id?: string;
    site?: string;
    parent_id: string;
    name: string;
    description: string;
    requestSourceLogger: number | null
  }

  interface ICourseTable {
    id: string;
    code: string;
    name: string;
    isDefaultUsed: number;
  }

  interface ICourseTime {
    id: string;
    name: string;
    startTime: string;
    endTime: string;
    campus:string;
    section?:number;
  }

  interface ICourseTimeCategory {
    isDefaultUsed: boolean;
    timetableCode: string;
    timetableName: string;
    timeDetailsShows: ICourseTime[];
    campusInfo:any;
  }
}
