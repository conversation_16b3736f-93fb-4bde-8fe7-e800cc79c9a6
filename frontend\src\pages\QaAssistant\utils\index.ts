// 计算特定字符串长度
const getStrCount = (str: string, char: string) =>
  (str.match(new RegExp(char, 'g')) || []).length;

// 判断对象是否完整
export const verifyObjComplete = (str: string) =>
  getStrCount(str, '{') === getStrCount(str, '}');

const a = () => {
  let isUse = false;
  return (uint: any) => {
    const textDecoder = new TextDecoder('utf-8');
    const str = textDecoder.decode(uint);
    const strArr = str.split('data:  ').filter(Boolean); // 将字符串拆分
    console.info(strArr);
    const result = isUse
      ? strArr
      : getType(strArr) === 'text'
      ? strArr
      : str;
    if (!isUse) {
      isUse = true;
    }
    return result;
  };
};

export const dataToString = a();

const getType = (strArr: string[]) => {
  if (strArr.length > 0 && verifyObjComplete(strArr[0])) {
    return JSON.parse(strArr[0]).message_type;
  }
  return false;
};

export const dealText = (
  str: string | string[],
  restText: string = '',
): { text: string[]; rest: string; endText: any } => {
  if (Array.isArray(str)) {
    if (!verifyObjComplete(str[0])) {
      str[0] = restText + str[0];
    }
    const rest = verifyObjComplete(str[str.length - 1])
      ? ''
      : str[str.length - 1];
    const endText = verifyObjComplete(str[str.length - 1])
      ? JSON.parse(str[str.length - 1])
      : {};
    const textArr = str.map(
      (item: string, index: number) => {
        if (rest && index === str.length - 1) return '';
        return (
          JSON.parse(item)?.contents?.[0]?.sentence ?? ''
        );
      },
    );
    return {
      endText,
      text: textArr.filter(Boolean),
      rest,
    };
  } else {
    const arr = (str + restText)
      .split('data:  ')
      .filter(Boolean);
    if (arr.length > 1) {
      return dealText(arr);
    }
    return {
      endText: {},
      text: [],
      rest: str,
    };
  }
};

export const deepCopy = (data: any): string => {
  if (typeof data !== 'object') return data;
  const copyData = !Array.isArray(data) ? [data] : data;
  return copyData.reduce(
    (acc: string, cur: any) =>
      `${acc}${deepCopy(cur.props.children)}\n`,
    '',
  );
};

export const getDateValue = (data: any) =>
  new Date(data).valueOf();

export const sortByDate = (list: any[]) => {
  return list.toSorted(
    (a: any, b: any) =>
      getDateValue(b.create_time) -
      getDateValue(a.create_time),
  );
};

export const dealCourse = (list: any[]) => {
  return list.slice(0, 3).map((item: any) => ({
    cover: item.cover,
    name_: item.name_,
    name: highLight(item.name_, item.intelligent_keywords),
    link: item.link,
  }));
};

export const getStorageByKey = (
  key: string,
  sessionId: string,
) => {
  const jsonString = localStorage.getItem(key);
  if (jsonString) {
    return JSON.parse(jsonString)?.[sessionId] ?? {};
  }
  return {};
};

export const updateStorage = (
  key: string,
  sessionId: string,
  extend: any,
) => {
  const jsonString = localStorage.getItem(key);
  if (jsonString) {
    const data = JSON.parse(jsonString);
    data[sessionId] = extend;
    localStorage.setItem(key, JSON.stringify(data));
  } else {
    localStorage.setItem(
      key,
      JSON.stringify({ [sessionId]: extend }),
    );
  }
};

interface IHighLight {
  isLight: boolean;
  text: string;
}

export const highLight = (
  text: string,
  keys: string[],
): Array<IHighLight> => {
  keys.forEach((key) => {
    text = text.replaceAll(key, `<hight><span>${key}<span><hight>`);
  });
  return text.split("<hight>").filter(Boolean).map(item => ({
    text: item.replaceAll("<span>", ""),
    isLight: item.includes("<span>")
  }))
};
