.content-style {
    position: relative;

    .header {
        font-size: 13px;
        font-weight: bold;
        margin: 25px 0 10px 0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .shrink {
            color: rgba(0, 0, 0, 0.6);
            font-size: 13px;
            font-weight: normal;
            cursor: pointer;
        }
    }

    .list {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        height: 75px;
        padding-top: 10px ;
        overflow: hidden;
        margin-bottom: 12px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .content-item{
        height: 75px;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
        margin-bottom: 12px;

        .text {
            width: 100%;
            height: 15px;
            text-align: center;
            color: rgba(0, 0, 0, 0.6);
            font-size: 12px;
            margin-top: 5px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        
    }

    .more {
        width: 58px;
        position: absolute;
        bottom: 30px;
        right: 0;
        color: rgba(0, 0, 0, 0.6);
        font-size: 13px;
        cursor: pointer;
    }

    // .more:hover {
    //     color: #007DFB;
    //     text-decoration: underline;
    // }
}