import { IReducers } from '@/types/modelsTypes';
import uploadTypes from '@/types/uploadTypes';
import _ from 'lodash';
import { IFormItem } from '@/types/entityTypes';
import config from '@/config';
import { Modal, Button, Checkbox, message, Form, Input } from 'antd';
export interface IUpload {
  tasks: uploadTypes.ITask[];
  showModal: boolean;
  fields: {
    [propsName: string]: IFormItem[];
  };
  showPanl: boolean;
  taskPanls: uploadTypes.IUploadTask[];
  orginTasks: File[];
  uploadformat: string;
  uploadformatEnum: []; //保存一份原始数据 供上传时判断文件类型
}

function taskController(tasks: uploadTypes.IUploadTask[]) {
  const result = tasks.map(task => ({ ...task }));
  const uploadingCount = result.filter(task => task.uploading).length;
  const waitingUpload: number[] = [];
  const concurrency = config.uploadConfig.concurrency;
  result.forEach((task, index) => {
    if (task.progress !== 1 && !task.pause && !task.uploading) {
      waitingUpload.push(index);
    }
  });
  // 还有等待上传文件情况
  if (uploadingCount < concurrency && waitingUpload.length > 0) {
    for (
      let i = 0;
      i < concurrency - uploadingCount && i < waitingUpload.length;
      i++
    ) {
      result[waitingUpload[i]].uploading = true;
      if(result[waitingUpload[i]].storage){ //第三方存储
        if(result[waitingUpload[i]].storage==='OSS'){
          setTimeout(()=>{
            console.log('zzzzzzzzzzzzz',result[waitingUpload[i]].uploader)
            result[waitingUpload[i]].uploader.upload?.(); //加延时的目的是为了获取redux最新值（pause）
          })
        }
      }else{
        console.log(result[waitingUpload[i]].uploader, 'result[waitingUpload[i]].uploader')
        result[waitingUpload[i]].uploader.upload();
      }

    }
  }
  // 暂停文件
  result.forEach(task => {
    if(task.pause){
      if(task.storage){
        if(task.storage==='OSS'){
          // setTimeout(()=>{
            // task.uploader.upload?.(); //加延时的目的是为了获取redux最新值（pause）
          // })
          if(task.product==='aliyun'){
            task.uploader.client.cancel();
          }
        }
      }else{
        if(task.uploader.isInProgress()){
          task.uploader.stop(true);
        }
      }
    }
    // if (task.pause && task.uploader.isInProgress()) {
    //   task.uploader.stop(true);
    // }
  });
  return result;
}

export default {
  namespace: 'upload',
  subscriptions: {
    setup({ dispatch }: any) {
      // dispatch({
      //   type: 'initStorage'
      // });
    },
  },
  state: {
    tasks: [],
    showModal: false,
    fields: {},
    showPanl: false,
    taskPanls: [],
    orginTasks: [],
    uploadformat: '',
    uploadformatEnum: [],
  },
  effects: {
  },
  reducers: {
    changeUploadformat: (
      state: IUpload,
      { payload: data }: IReducers<string>,
    ) => {
      return {
        ...state,
        uploadformat: data.value,
      };
    },
    setUploadformatOrignal: (state: IUpload, { payload: data }: IReducers<string>) => {
      return {
        ...state,
        uploadformatEnum: data.value,
      };
    },
    changeTasks: (
      state: IUpload,
      { payload: data }: IReducers<uploadTypes.ITask[]>,
    ) => {
      return {
        ...state,
        tasks: data.value,
      };
    },
    changeModal: (state: IUpload, { payload: data }: IReducers<boolean>) => {
      return {
        ...state,
        showModal: data.value,
      };
    },
    changeFields: (
      state: IUpload,
      { payload: data }: IReducers<IFormItem[]>,
    ) => {
      return {
        ...state,
        fields: {
          ...state.fields,
          ...data.value,
        },
      };
    },
    changeShowPanl: (state: IUpload, { payload: data }: IReducers<boolean>) => {
      return {
        ...state,
        showPanl: data.value,
      };
    },
    setTaskPanls: (
      state: IUpload,
      { payload: data }: IReducers<uploadTypes.IUploadTask[]>,
    ) => {
      const taskPanls = taskController(data.value);
      return {
        ...state,
        taskPanls,
      };
    },
    updateTaskPanls: (
      state: IUpload,
      { payload: data }: IReducers<{ guid: string; progress: number }>,
    ) => {
      let newTaskPanls = state.taskPanls.map(item => {
        if (item.guid === data.value.guid) {
          return {
            ...item,
            progress: data.value.progress,
            uploading: data.value.progress !== 1,
          };
        }
        return {
          ...item,
        };
      });
      if (data.value.progress === 1) {
        newTaskPanls = taskController(newTaskPanls);
      }
      return {
        ...state,
        taskPanls: newTaskPanls,
      };
    },
    updateTaskPanls_: (
      state: IUpload,
      { payload: data }: IReducers<{ guid: string;param:any}>,
    ) => {
      let newTaskPanls = state.taskPanls.map(item => {
        if (item.guid === data.value.guid) {
          return {
            ...item,
            uploader: {
              ...item.uploader,
              ...data.value.param
            },
          };
        }
        return {
          ...item,
        };
      });
      newTaskPanls = taskController(newTaskPanls);
      return {
        ...state,
        taskPanls: newTaskPanls,
      };
    },
    setOrginTasks: (state: IUpload, { payload: data }: IReducers<File[]>) => {
      return {
        ...state,
        orginTasks: data.value,
      };
    },
    removeTask: (state: IUpload, { payload: data }: IReducers<string>) => {
      let taskPanls = state.taskPanls.map(task => ({ ...task }));
      let index: number | null = null;
      taskPanls.forEach((item, i) => {
        if (item.guid === data.value) {
          index = i;
        }
      });
      if (index !== null) {
        taskPanls.splice(index, 1);
        taskPanls = taskController(taskPanls);
      }
      return {
        ...state,
        taskPanls,
      };
    },
    errorTask: (state: IUpload, { payload: data }: IReducers<string>) => {
      let taskPanls = state.taskPanls.map(task => ({ ...task }));
      const task = _.find(taskPanls, item => item.guid === data.value);
      if (task) {
        task.status = -1;
        task.progress = 1;
        task.uploading = false;
        taskPanls = taskController(taskPanls);
      }
      return {
        ...state,
        taskPanls,
      };
    },
    updateTaskPause: (
      state: IUpload,
      {
        payload: {
          value: { pause, guid },
        },
      }: IReducers<{ pause: boolean; guid: string }>,
    ) => {
      let taskPanls = state.taskPanls.map(task => ({ ...task }));
      taskPanls.forEach(task => {
        if (task.guid === guid) {
          task.uploading = false;
          task.pause = pause;
        }
      });
      taskPanls = taskController(taskPanls);
      return {
        ...state,
        taskPanls,
      };
    },
  },
};
