import request from "./request";

async function addMenu(params: any) {
    return request(`/unifiedplatform/v1/navigationmenu`, {
        method: 'post',
        data: JSON.stringify(params)
    })
}

async function topMenusList() {
    return request(`/unifiedplatform/v1/navigationmenu/menu`, {
        method: 'get',
    })
}

async function childMenusList(id: number) {
    return request(`/unifiedplatform/v1/navigationmenu/menu/${id}`, {
        method: 'get',
    })
}

async function deleteMenus(id: number) {
    return request(`/unifiedplatform/v1/navigationmenu/${id}`, {
        method: "delete"
    })
}

async function disableMenus(disable: boolean,params:any) {
    return request(`/unifiedplatform/v1/navigationmenu/menu/disable?disable=${disable}`, {
        method: "post",
        data: JSON.stringify(params)
    })
}

let menus = {
    addMenu,
    topMenusList,
    childMenusList,
    deleteMenus,
    disableMenus
}

export default menus;