/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:29:57
 * @LastEditTime: 2023-09-14 10:30:05
 * @FilePath: \frontend\src\pages\Translate\index.tsx
 * @Description:
 */
import Header, {
  CUSTOMER_NPU,
} from '@/components/header/header';
import Left from './components/Left';
import React, { useEffect, useState } from 'react';
import style from './index.less';
import {
  Affix,
  Button,
  Empty,
  Input,
  message,
  Upload,
} from 'antd';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
import Content from '@/components/FilePreview';
import './reset.less';
import {
  ClassNames,
  blobToFile,
  changeBlobType,
  downloadBlob,
  fileToBlob,
  getBlobTypeToSuffix,
  getWords,
  sleep,
  stringToBlob,
  suffixToBlobType,
  txtToString,
} from '@/utils/utils';
import { CloudUploadOutlined } from '@ant-design/icons';
import { DraggerProps } from 'antd/lib/upload/Dragger';
import LoadingIcon from '@/images/loading.png';
import { TranslateService } from '@/api/translate';
import {
  getResultById,
  updateResult,
} from '@/db/controllers/Result';
import { ResultModelType } from '@/db/models/ResultModel';
import { useSelector } from 'umi';
const Dragger = Upload.Dragger;

const Translate: React.FC<TranslateProps> = ({
  children,
}) => {
  const [isEmpty, setIsEmpty] = useState(false);
  const [text, setText] = useState('');
  const [selectedKey, setSelectedKey] = useImmer<
    React.Key | undefined
  >(undefined);
  const [translateData, setTranslateData] = useImmer<
    Partial<ResultModelType>
  >({
    before: undefined,
    after: undefined,
  });
  const [supportTranstate, setSupportTranstate] = useState<boolean>(true)

  useEffect(() => {
    supportTranstateFun()
  }, [])
  // 获取是否能支持翻译
  const supportTranstateFun = async () => {
    const { data, message } = await TranslateService.isSupportTranstate();
    setSupportTranstate(data)
  }

  // 应该查询数据库
  useEffect(() => {
    setTranslateData({
      before: undefined,
      after: undefined,
    });
  }, [selectedKey]);
  const [loading, setLoading] = useImmer({
    submit: false,
    render: false,
  });
  async function submit(blob: Blob, filename?: string) {
    setTranslateData((draft) => {
      draft.before = blob;
    });
    setLoading((draft) => {
      draft.submit = true;
    });
    // 更新数据库
    try {
      const suffix = getBlobTypeToSuffix(blob);
      let afterBlob: Blob | undefined;
      let title: string | undefined;
      if (suffix === 'txt') {
        const text = await txtToString(blob);
        const {
          data,
          message,
        } = await TranslateService.chineseAndEnglish({
          content: text,
        });
        if (!data) {
          throw new Error(message);
        }
        afterBlob = stringToBlob(
          data.replaceAll('\n', '\n\n'),
        );
        title = filename ?? `${getWords(text, 5)}`;
      } else if (suffix === 'docx') {
        title = filename;
        const {
          data,
          message,
        } = await TranslateService.uploadFile(
          blobToFile(blob, filename!),
        );
        if (!data) {
          throw new Error(message);
        }
        afterBlob = changeBlobType(
          await TranslateService.downloadFile(data),
          suffixToBlobType('docx')!,
        );
      }
      const result = await getResultById(
        selectedKey as number,
      );
      if (result) {
        const newResult = {
          ...result,
          before: blob,
          after: afterBlob,
          title: title ?? '新的翻译',
        };
        await updateResult(newResult);
        setTranslateData(newResult);
      }
    } catch (error) {
      setTranslateData((draft) => {
        draft.before = undefined;
      });
      //@ts-ignore
      message.error(error.message);
    }
    setLoading((draft) => {
      draft.submit = false;
    });
  }
  const draggerConfig: DraggerProps = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.txt,.docx',
    onChange(info) {
      submit(
        fileToBlob(info.file.originFileObj!),
        info.file.name,
      );
    },
    beforeUpload: (file) => {
      // 大于10mb
      if (file.size > 10 * 1024 * 1024) {
        message.error('文件超过10MB');
        return false;
      }
      return true;
    },
    className: style.dragger,
    // ...uploadProps,
  };
  async function getData(id: number) {
    const result = await getResultById(id);
    if (result) {
      setTranslateData(result);
    }
  }
  useEffect(() => {
    (async () => {
      setLoading((draft) => {
        draft.render = true;
      });
      if (selectedKey) {
        await getData(selectedKey as number);
      }
      setLoading((draft) => {
        draft.render = false;
      });
    })();
    setText('');
  }, [selectedKey]);
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  const showOther =
    parameterConfig?.target_customer === CUSTOMER_NPU;
  return (
    <div
      id="translate"
      className={ClassNames(
        showOther ? 'other-tr-header' : '',
      )}>
      <Affix offsetTop={0}>
        <Header showNav={true} customTitle="中英翻译助手" />
      </Affix>
      <div className={style.content}>
        <Left
          createTitle="新的翻译"
          setIsEmpty={setIsEmpty}
          selectedKey={selectedKey}
          setSelectedKey={setSelectedKey}
        />
        <main>
          {isEmpty || !selectedKey ? (
            <div className={style.empty}>
              <Empty description="请在左侧选择或新建翻译" />
            </div>
          ) : (
            <div className={style.renderContent}>
              <div
                className={style.before}
                id="translate-before">
                <Affix
                  offsetTop={0}
                  target={() => {
                    return document.querySelector(
                      '#translate-before',
                    ) as HTMLElement;
                  }}>
                  <h2 className={style.title}>原文</h2>
                </Affix>
                <div className={style.showArea}>
                  <Access
                    accessible={
                      !translateData.before &&
                      !loading.render
                    }>
                    <div className={style.new}>
                      <div className={style.textarea}>
                        <Input.TextArea
                          placeholder="请输入待翻译内容"
                          // bordered={false}
                          value={text}
                          onChange={(e) => {
                            setText(e.target.value);
                          }}
                          autoSize={{
                            minRows: 10,
                            maxRows: 10,
                          }}></Input.TextArea>
                        <Button
                          type="primary"
                          loading={loading.submit}
                          onClick={async () => {
                            if (text.length >= 2000) {
                              return message.error(
                                '文本长度不能超过2000',
                              );
                            }
                            setLoading((draft) => {
                              draft.submit = true;
                            });
                            try {
                              const blob = stringToBlob(
                                text,
                              );
                              await submit(blob);
                            } catch (error) {
                              message.error('发起失败');
                            }
                            setLoading((draft) => {
                              draft.submit = false;
                            });
                          }}
                          className={style.btn}>
                          发起
                        </Button>
                      </div>
                      {supportTranstate && <Dragger {...draggerConfig}>
                        <div className={style.topTip}>
                          <p>
                            或上传文档，对文档进行整体翻译
                          </p>
                          <p>
                            上传要求：TXT、DOCX文档，10M以下10页以内，不含图片
                          </p>
                        </div>
                        <p className="ant-upload-drag-icon">
                          <CloudUploadOutlined />
                        </p>
                        <p className={style.uploadText}>
                          选择本地文档
                          <span className={style.strong}>
                            或拖拽至此区域
                          </span>
                        </p>
                        {/* <div className={style.bottom}>
                          <div
                            onClick={async (e) => {
                              e.stopPropagation();
                              e.preventDefault();
                            }}
                            className={style.resourceBtn}
                            role="button">
                            从资源库选择
                          </div>
                        </div> */}
                      </Dragger>
                      }
                    </div>
                  </Access>
                  <Access
                    accessible={!!translateData.before}>
                    <Access accessible={!loading.submit}>
                      <Content
                        blob={translateData.before}
                      />
                    </Access>
                    <Access accessible={loading.submit}>
                      <div className={style.loading}>
                        <img src={LoadingIcon} />
                        <p> 翻译中... </p>
                        <p>请耐心等待</p>
                      </div>
                    </Access>
                  </Access>
                </div>
              </div>
              <div
                className={style.after}
                id="translate-after">
                <Affix
                  offsetTop={0}
                  target={() => {
                    return document.querySelector(
                      '#translate-after',
                    ) as HTMLElement;
                  }}>
                  <h2 className={style.title}>
                    <p>译文</p>
                    <Access
                      accessible={!!translateData.after}>
                      <Button
                        className={style.download}
                        type="link"
                        onClick={async () => {
                          const close = message.loading(
                            '下载中...',
                          );
                          try {
                            await sleep(500);
                            const suffix = `.${getBlobTypeToSuffix(
                              translateData.after,
                            )}`;
                            const filename = `${translateData.title
                              }${translateData.title?.endsWith(
                                suffix!,
                              )
                                ? ''
                                : suffix
                              }`;

                            downloadBlob(
                              translateData.after!,
                              filename,
                            );
                            close();
                            message.success('下载成功');
                          } catch (error) {
                            message.error('下载失败');
                          }
                        }}>
                        下载文件
                      </Button>
                    </Access>
                  </h2>
                </Affix>
                <div className={style.showArea}>
                  <Content blob={translateData.after} />
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

interface TranslateProps {
  children: React.ReactNode;
}
export default Translate;
Translate.displayName = 'Translate';
