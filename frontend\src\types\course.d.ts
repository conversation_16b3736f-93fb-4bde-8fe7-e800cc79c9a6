// import NoteConstant from "@/constant/Note";

/*
 * @Author: 李晋
 * @Date: 2021-10-29 14:27:30
 * @Email: <EMAIL>
 * @LastEditTime: 2021-11-12 15:23:00
 * @Description: 课程--类型定义
 * @Company: Sobey
 */
declare namespace CourseType {
  export interface ICodeValue {
    code: string;
    value: string;
  }
  /**
   * 通用 query
   */
  export interface Query {
    page?: number;
    size?: number;
    ordering?: string;
  }
  export interface LearnCourseQuery extends Query {
    keyword?: string;
    isend?: number;
    courseType?: number;
  }

  export interface QueryCourse {
    page?: number;
    size?: number;
    ordering?: string;
    keyword?: string; // 关键字
    isTop?: number; // 是否置顶 1-true 0-false
    courseType?: number; // 课程类型 0-微课 1-Mooc 3-Spoc
    classificationId?: Array<string | undefined>; // 分类id
    subjectId?: Array<string>; // 学科id
    college?: Array<string>; // 学院id
    professionIds?: Array<string>; // 专业id
    order?: {
      field: string;
      isDesc: boolean;
    }[]; // 排序
  }
  export type CourseEntity = {
    contentId_: string;
    name: string;
    college: string;
    classification: string;
    cover: string;
    describe: string;
    major: string[];
    publishStatus: number;
    subject: string;
    target: string;
    teacher: string;
    courseType?: number;
  };
  /**
   * 课程item字段
   */
  export interface Course {
    contentId_: string; // 唯一id
    name_: string; // 名称
    courseType: number; // 课程类型
    school: string; // 学校
    cover: string; // 封面
    college: ICodeValue[]; // 学院
    major: ICodeValue[]; // 专业
    subject: ICodeValue[]; // 学科
    classification: ICodeValue[]; // 分类
    createDate_: string; // 创建时间
    createUser_: string; // 创建人
    type_: string; // file类型 video、image
    top: number; // 是否置顶 1-true 0-false
    order_: number; // 排序
    // teacher?: {
    //   // 教师信息
    //   code?: string;
    //   value?: string;
    //   teacherCode?: string;
    //   teacherName?: string;
    //   teacherAvatar?: string;
    // };
    teacher?: any;
    describe: string;
    collectionstotal: number;
  }
}
