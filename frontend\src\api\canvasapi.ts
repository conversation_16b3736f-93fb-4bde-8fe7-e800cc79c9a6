import HTTP from './request';

// 查询我的个人资源
export function GetUserFolders() {
    return HTTP(`/canvas-lms-adapter/Graph/UserFolders`,{
      method:'get'
    })
  }

//   查询我的课程资源 
export function GetUserCourses() {
    return HTTP(`/canvas-lms-adapter/Graph/UserCourses`,{
      method:'get'
    })
  }

//   查询课程的下一级
export function GetCoursesFoldersChild(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/CoursesFolders?course_id=${id}`)
  }

//   获取文件夹下的文件
export function GetFoldersFiles(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFiles?folder_id=${id}`)
}

// 获取文件夹下的子文件夹
export function GetFolderFolders(id:any) {
    return HTTP.get(`/canvas-lms-adapter/Graph/FolderFolders?folder_id=${id}`)
}


// 导入第三方资源
export function importThirdResource(data: any) {
  return HTTP('/rman/v1/upload/third-resource/import',{
    method:'post',
    data
  })
}
// 根据id查询地图基本信息
export const querymapinfo = (params: any) =>
  HTTP(`/learn/m1/knowledge/info`, {
    method: 'GET',
    params,
})