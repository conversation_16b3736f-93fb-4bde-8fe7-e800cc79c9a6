import { unLoginPages } from './../constant/PageConfig';
import api from '@/api';

export interface IGlobal {
  userInfo: {
    avatar: string;
    disabled: boolean;
    email: string;
    organizations: any;
    loginName: string;
    nickName: string;
    phone: string;
    userCode: string;
    roles: any[];
    extend: {
      [propsName: string]: string;
    };
  };
  showLoading: boolean;
  platform: string;
  menuShow: boolean;
  mobileFlag: boolean;
  rmanGlobalParameter: any[];
}

/**
 * @description: 设置configSafe、upform_platform到sessionStorage
 * @param {*}
 * @return {*}
 */
const initPlatform = async () => {
  if (
    !(window as any).sessionStorage.getItem(
      'configSafe_upform',
    )
  ) {
    const result: any = await api.setting.fetchSafeConfig();
    (window as any).sessionStorage.setItem(
      'configSafe_upform',
      JSON.stringify(result),
    );
    console.log(result);
  }
  const res: any = await api.setting.fetchPlatform();
  if (res && res.errorCode === 'success') {
    window.localStorage.setItem(
      'upform_platform',
      res.extendMessage,
    );
  }
};

export default {
  namespace: 'global',
  subscriptions: {
    setup: ({ dispatch, history }: any) => {
      // ----------------------安全模式配置、平台初始化------
      initPlatform();
      // ----------------------权限，模块，用户信息初始化------
      // 排除部分不用登陆的界面（授权界面）
      let blackList = unLoginPages;
      if (
        blackList.some((item) =>
          history.location.pathname.includes(item),
        )
      ) {
        return;
      }
      // 数据初始化
      dispatch({
        type: 'initData',
      });
      dispatch({
        type: 'initParameterConfig',
      });
      dispatch({
        type: 'fetchGlobalParameter',
      });
      dispatch({
        type: 'fetchPermissions',
      });
      dispatch({
        type: 'fetchSysLearnPermissions',
      });
      // dispatch({
      //   type: 'fetchStorageConfig',
      // });
    },
  },
  state: {
    baseInfo: {
      logoUrl: '',
      title: '',
    },
    userInfo: {},
    loading: false,
    parameterConfig: {}, // 系统--全局参数
    rmanGlobalParameter: [], // 系统--全局参数
    permissions: [], // 角色--权限
    modules: [], // 角色--模块
    modulesLearning: [], // 系统--学习门户的模块（用来控制我的学习菜单权限，不是角色相关的）
    storageConfig: [], // 多存储配置
    platform: '',
    menuShow: true,
    mobileFlag: true, //移动端标识
    showcatalog: false, //我的学习是否收起能力
    fullScreenState: false, // agent展示状态
  },
  effects: {
    *initPlatform(
      { payload, callback }: any,
      { call, put }: any,
    ) {
      const res = yield call(api.setting.fetchPlatform);
      console.log(res, 'initPlatform');

      if (res.errorCode === 'success') {
        window.localStorage.setItem(
          'upform_platform',
          res.extendMessage,
        );
        callback?.(res.extendMessage);
      }
    },

    *initData(_: any, { call, put }: any) {
      const userRes = yield call(api.user.fetchUserDetail);
      if (userRes.errorCode === 'success') {
        yield put({
          type: 'updateState',
          payload: {
            userInfo: {
              ...userRes.extendMessage,
              avatar: userRes.extendMessage?.avatar,
              loginName: userRes.extendMessage?.loginName,
              nickName: userRes.extendMessage?.nickName,
              userCode: userRes.extendMessage?.userCode,
            },
          },
        });
      }
    },
    *initParameterConfig(
      { payload }: any,
      { call, put }: any,
    ) {
      const res = yield call(
        api.setting.queryParameterConfigAll,
        [
          'mooc_kcgl',
          'spoc_kcgl',
          'wdkc',
          'microcourse_kcgl',
          'classreview_kcgl',
          'LearningPortal',
          'workspace_supervision',
          'workspace_tools',
          "LearningPortal"
        ],
      );
      if (
        res.errorCode === 'success' &&
        res.extendMessage?.length > 0
      ) {
        const parameterConfig: any = {};
        res.extendMessage.forEach((r: any) => {
          parameterConfig[r.code] = r.value;
        });

        yield put({
          type: 'updateState',
          payload: {
            parameterConfig,
          },
        });
      }
    },
    *initModulesLearning(
      { payload }: any,
      { call, put }: any,
    ) {
      const res = yield call(
        api.setting.queryParameterConfigAll,
        [
          'mooc_kcgl',
          'spoc_kcgl',
          'wdkc',
          'microcourse_kcgl',
          'classreview_kcgl',
        ],
      );
      if (
        res.errorCode === 'success' &&
        res.extendMessage?.length > 0
      ) {
        const parameterConfig: any = {};
        res.extendMessage.forEach((r: any) => {
          parameterConfig[r.code] = r.value;
        });

        yield put({
          type: 'updateState',
          payload: {
            parameterConfig,
          },
        });
      }
    },
    *fetchGlobalParameter(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.IResponse = yield call(
        api.setting.fetchRmanGlobalParam,
      );
      if (errorCode === 'success') {
        if (Array.isArray(extendMessage)) {
          const rmanGlobalParameter = extendMessage
            .filter((item) => item.value === 'true')
            .map((item) => item.code);
          console.log(rmanGlobalParameter);
          yield put({
            type: 'updateState',
            payload: {
              rmanGlobalParameter,
            },
          });
        }
      }
    },
    *fetchPermissions(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.IResponse = yield call(
        api.setting.fetchUserPermissionsV2,
      );
      if (errorCode === 'success') {
        let permissions = [];
        if (
          typeof extendMessage.moduleFeatures === 'object'
        ) {
          permissions = Object.keys(
            extendMessage.moduleFeatures,
          )
            .map((key) => extendMessage.moduleFeatures[key])
            .flat(2);
        }
        if (typeof extendMessage.modules === 'object') {
          permissions = permissions.concat(
            extendMessage.modules,
          );
        }
        yield put({
          type: 'updateState',
          payload: {
            permissions,
            modules: extendMessage.modules || [],
          },
        });
      }
    },
    *fetchSysLearnPermissions(_: any, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.IResponse = yield call(
        api.setting.fetchModuleSystem,
        'LearningPortal',
      );
      if (errorCode === 'success') {
        let modules = [];
        if (extendMessage && extendMessage.length >= 0) {
          modules = extendMessage.map(
            (item: { code: any }) => item.code,
          );
        }
        yield put({
          type: 'updateState',
          payload: {
            modulesLearning: modules,
          },
        });
      }
    },
    // *fetchStorageConfig(_: any, { call, put }: any) {
    //   const { errorCode, extendMessage }: API.IResponse = yield call(
    //     api.setting.fetchStorageConfig
    //   );
    //   if (errorCode === 'success') {
    //     let params = [];
    //     if (extendMessage && extendMessage.length >= 0) {
    //       params = extendMessage;
    //     }
    //     yield put({
    //       type: 'updateState',
    //       payload: {
    //         storageConfig: params,
    //       },
    //     });
    //   }
    // },
  },
  reducers: {
    updateState(state: IGlobal, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
    updateUserInfo(state: IGlobal, { payload }: any) {
      return {
        ...state,
        userInfo: {
          ...state.userInfo,
          ...payload,
        },
      };
    },
    updateShowLoading(
      state: IGlobal,
      { payload }: { payload: boolean },
    ) {
      return {
        ...state,
        showLoading: payload,
      };
    },
    menuShowChange: (
      state: IGlobal,
      { payload }: { payload: boolean },
    ) => {
      return {
        ...state,
        menuShow: payload,
      };
    },
    mobileFlagChange: (
      state: IGlobal,
      { payload }: { payload: boolean },
    ) => {
      return {
        ...state,
        mobileFlag: payload,
      };
    },
    showCatalogChange: (
      state: IGlobal,
      { payload }: { payload: boolean },
    ) => {
      return {
        ...state,
        showcatalog: payload,
      };
    },
    agentToolChange: (
      state: IGlobal,
      { payload }: { payload: boolean },
    ) => {
      return {
        ...state,
        fullScreenState: payload,
      };
    },
  },
};
