declare namespace IClassified {
  interface ICsCourse {
    Id?: number;
    courseClassificationCode?: number;
    courseId: string;
    order?: number;
  }
  interface IgetAddConfig {
    courseClassificationCode: string;
    courseId: string;
  }
  interface IgetDeleteConfig {
    course_ids: string[];
    courseclassification_codes: string[];
  }
  interface Itranslate {
    code: string;
    value: string;
  }
  interface IconfigList {
    code: string;
    id: number;
    isEnable: number;
    name: string;
    order: number;
  }
}
