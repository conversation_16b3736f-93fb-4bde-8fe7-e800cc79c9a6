import IconFont from '@/components/iconFont/iconFont';
import React, {
  FC,
  useEffect,
  useRef,
  useState,
} from 'react';
import ChatItem from '../ChatItem';
import { Button, Empty, Input, Spin, message } from 'antd';
import './index.less';

import {
  deepCopy,
  readTextEventStream,
} from '@/utils/utils';
import Access from '@/components/Access';
import { ChatAfterType } from '@/db/models/infoModel';
import { Info } from '@/db/controllers/info';
import { Chatdoc } from '@/api/chatdoc';
import moment from 'moment';
import { useImmer } from 'use-immer';

const { TextArea } = Input;

interface ChatProps {
  sessionId?: string;
  /** 只读模式只显示传入的聊天记录 */
  readonly?: boolean;
  defaultMessage?: React.ReactNode;
  historyId?: number;
  emptyDesc?: string;
  defaultTime?: number;
}

const ChatContent: FC<ChatProps> = ({
  sessionId,
  readonly,
  defaultMessage,
  historyId,
  emptyDesc,
  defaultTime,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [chatMessage, setChatMessage] = useState<
    string | null
  >(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [chatData, setChatData] = useImmer<ChatAfterType[]>(
    [],
  );
  const coRef = useRef(null);

  const handleInputChange = (e: any) => {
    handleToBottom();

    setInputValue(e.target.value);
  };

  const handleSubmit = async () => {
    const inputTime = Date.now();
    const value = inputValue?.trim();
    setLoading(true);
    setInputValue('');
    let index = chatData.length + 1;
    setChatData((d) => {
      d.push({
        isMine: true,
        content: value,
        createTime: inputTime,
      });
    });
    try {
      let outputTime = 0;
      let chatStr = '';
      let showStr = '';
      let time = 200;
      await Chatdoc.fetchDoc(
        {
          doc_id: sessionId!,
          input_text: value!,
          context: chatData.slice(0, 20).map((item) => {
            return {
              role: item.isMine ? 'user' : 'assistant',
              content: item.content,
            };
          }),
        },
        (e) => {
          chatStr += e.data;
          time += 200;
          setTimeout(() => {
            handleToBottom();
          }, 100);
          setTimeout(() => {
            showStr += e.data;
            setChatData((draft) => {
              if (!outputTime) {
                outputTime = Date.now();
              }
              if (draft.length === index) {
                draft.push({
                  isMine: false,
                  content: showStr,
                  createTime: outputTime,
                });
              } else {
                draft.splice(index, 1, {
                  isMine: false,
                  content: showStr,
                  createTime: outputTime,
                });
              }
            });
          }, time);
        },
        async () => {
          console.log('完成请求');
          try {
            const old = await Info.getResultById(
              historyId!,
            );
            console.log(
              '%c [ old ]-107',
              'font-size:13px; background:pink; color:#bf2c9f;',
              old,
            );

            // const newChatData: ChatAfterType[] = [
            //   ...chatData,
            //   {
            //     isMine: true,
            //     content: inputValue,
            //     createTime: inputTime,
            //   },
            // ].map((item, i) => {
            //   if (i === index) {
            //     return {
            //       isMine: false,
            //       content: chatStr,
            //       createTime: outputTime,
            //     };
            //   }
            //   return item;
            // });
            await Info.updateResult({
              ...old!,
              after: JSON.stringify([
                ...JSON.parse(old?.after ?? '[]'),
                {
                  isMine: true,
                  content: value,
                  createTime: inputTime,
                },
                {
                  isMine: false,
                  content: chatStr,
                  createTime: outputTime || Date.now(),
                },
              ]),
            });
            // setChatData(newChatData);
            setInputValue('');
            setTimeout(() => {
              handleToBottom();
            }, 100);
          } catch (error) {
            console.log(
              '%c [ error ]-99',
              'font-size:13px; background:pink; color:#bf2c9f;',
              error,
            );
          }
          setLoading(false);
        },
      );
      return;
    } catch (error) {
      console.log(
        '%c [ error ]-160',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error,
      );
      message.error('发送失败');
      setLoading(false);
    }
  };
  const handleEnter = (event: any) => {
    if (event.keyCode == 13) {
      if (!event.shiftKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
  };
  const handleToBottom = () => {
    const co: any = coRef.current;
    if (co) {
      co.scrollTop = co.scrollHeight;
    }
  };
  async function getData() {
    try {
      const result = await Info.getResultById(historyId!);
      if (result?.after) {
        setChatData(JSON.parse(result.after));
      }
      console.log(
        '%c [ result ]-156',
        'font-size:13px; background:pink; color:#bf2c9f;',
        result,
      );
    } catch (error) {
      message.error('获取聊天记录失败');
    }
  }
  useEffect(() => {
    if (historyId) {
      setInputValue('');
      setChatData([]);
      getData();
    }
  }, [historyId]);

  return (
    <>
      <Access accessible={!!sessionId}>
        <div className={`right-wrp `} id="document-chat">
          <div className="chat-wrp" ref={coRef}>
            <ChatItem
              key="default-msg"
              create_time={
                defaultTime
                  ? moment(defaultTime).format(
                      'YYYY-MM-DD HH:mm:ss',
                    )
                  : ''
              }>
              <p style={{ whiteSpace: 'pre-line' }}>
                {defaultMessage}
              </p>
            </ChatItem>
            {chatData.map((item, index) => {
              const { isMine, content, createTime } = item;
              const create_time = createTime
                ? moment(createTime).format(
                    'YYYY-MM-DD HH:mm:ss',
                  )
                : '';
              return (
                <ChatItem
                  key={index}
                  data={item}
                  content={<pre>{content}</pre>}
                  isMine={isMine}
                  create_time={create_time}
                />
              );
            })}
            {chatMessage !== null && (
              <ChatItem
                key="assistant-msg"
                tag="assistant-msg"
                content={
                  <pre>
                    {chatMessage === ''
                      ? '正在输入...'
                      : chatMessage}
                  </pre>
                }
              />
            )}
            {inputValue !== '' && inputValue != null && (
              <ChatItem
                key="user-msg"
                typing
                isMine
                content={<pre>{inputValue}</pre>}
              />
            )}
          </div>
          <Access accessible={!readonly}>
            <div className="input-wrp">
              <TextArea
                rows={3}
                disabled={loading}
                placeholder="请输入问题，按enter键发送"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleEnter}
              />
              <Button
                type="primary"
                loading={loading}
                className={`sub-btn ${
                  inputValue === '' ? 'ant-btn-loading' : ''
                }`}
                onClick={() => handleSubmit()}>
                <IconFont type="iconfabushu" />
                发送
              </Button>
            </div>
          </Access>
        </div>
      </Access>
      <Access accessible={!sessionId}>
        <Empty
          style={{
            marginTop: 30,
            color: '#A4A4A4',
          }}
          description={emptyDesc}
        />
      </Access>
    </>
  );
};

export default ChatContent;
