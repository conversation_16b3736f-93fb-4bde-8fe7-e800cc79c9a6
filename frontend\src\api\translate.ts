import { request } from 'umi';
import { Request } from '@/constant/Request';
import {
  generatContentType,
  objToFormData,
} from '@/utils/utils';
import { IResponse, AIResponse } from '.';

const PREFIX = Request.PREFIX.TRANSLATION;

export namespace TranslateService {
  export const uploadFile = (file: File) => {
    return request(`/v1/translation/file`, {
      method: 'post',
      data: objToFormData({
        file,
      }),
      prefix: PREFIX,
    });
  };
  export const downloadFile = (filename: string) => {
    return request<Blob>(
      `/v1/translation/download/${filename}`,
      {
        method: 'get',
        prefix: PREFIX,
        responseType: 'blob',
      },
    );
  };
  export const chineseAndEnglish = (data: {
    content: string;
  }) => {
    return request<AIResponse<string>>(
      `/v1/translation/between/chinese/and/english`,
      {
        method: 'post',
        data,
        prefix: PREFIX,
      },
    );
  };
  // 判断是否支持中英文翻译
  export const isSupportTranstate = () => {
    return request<AIResponse<any>>(
      `/v1/translation/can/support/document`,
      {
        method: 'post',
        prefix: PREFIX,
      },
    );
  };
}
