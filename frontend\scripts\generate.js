//@ts-check
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> ran
 * @Date: 2023-02-14 11:25:21
 * @LastEditTime: 2023-07-06 15:49:50
 * @FilePath: \frontend\scripts\generate.js
 * @Description:
 */
const path = require('path');
const fs = require('fs/promises');
/**
 * @description 命令行参数 yarn run generate path name(可选)
 * @example yarn run generate /pages/xxx 生成xxx文件夹 并在目录下生成 index.tsx index.less
 * @example yarn run generate /pages/xxx aaa 生成xxx文件夹 并在目录下生成 aaa.tsx aaa.less
 * @example yarn run generate /pages/xxx aaa bbb 生成xxx文件夹 并在目录下生成 aaa.tsx aaa.less 并且组件名为bbb
 */
const ARGV = process.argv.slice(2);
// 源代码根目录
const ROOT_DIR = path.resolve(__dirname, '../src');
console.log(ARGV);

// 生成模板代码
(async function generate() {
  try {
    const folderName = ARGV[0].startsWith('/')
      ? ARGV[0].slice(1)
      : ARGV[0];
    const fileName = ARGV[1] ?? 'index';
    const componentName =
      ARGV[2] ?? folderName.split('/').pop();
    const folderPath = path.resolve(ROOT_DIR, folderName);
    console.log(folderPath, ROOT_DIR);
    const indexFileUrl = path.resolve(
      folderPath,
      `${fileName}.tsx`,
    );
    const lessFileUrl = path.resolve(
      folderPath,
      `${fileName}.less`,
    );
    // 创建文件夹
    await fs.mkdir(folderPath, {
      recursive: true,
    });
    // 创建文件
    await fs.writeFile(
      indexFileUrl,
      tsxFileContent(componentName, fileName),
    );
    console.log('🚩 🚩 🚩 生成tsx文件模板成功!');
    await fs.writeFile(lessFileUrl, ``);
    console.log('🚩 🚩 🚩 生成less文件模板成功!');
  } catch (error) {
    console.log('🐛 🐛 🐛  生成失败!');
    console.error(error);
    return;
  }
})();

/**
 *
 * @param {string} componentName
 * @param {string} fileName
 */
function tsxFileContent(componentName, fileName) {
  return `
  import React from "react";
  import style from './${fileName}.less'

  const ${componentName}: React.FC<${componentName}Props> = () => {
    return <div className={style.${componentName.toLocaleLowerCase()}}>${componentName}</div>;
  };
  
  interface ${componentName}Props {
  
  }
  export default ${componentName};
  ${componentName}.displayName = '${componentName}';
  `;
}
