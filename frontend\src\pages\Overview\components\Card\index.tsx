import React from 'react';
import style from './index.less';
import { isDevelopment, openNewPage } from '@/utils/utils';
import { history } from 'umi';
import Access from '@/components/Access';

const Card: React.FC<CardProps> = ({ card }) => {
  if (card.disable) {
    return null;
  }

  return (
    <div
      className={style.card}
      key={card.icon}
      role="button"
      onClick={() => {
        if (card.link.startsWith('http')) {
          window.open(card.link);
          return;
        }
        if (card.link.startsWith('/')) {
          const links = card.link.split('#');
          openNewPage(
            links[0].slice(0, links[0].length - 1),
            links[1] ?? '',
          );
        } else {
          openNewPage(
            isDevelopment ? '' : '/aitools',
            `/${card.link}`,
          );
        }
      }}
      style={{
        backgroundColor: [
          'rgba(0, 128, 255, 0.04)',
          'rgba(255, 135, 85, 0.04)',
          'rgba(148, 88, 252, 0.04)',
          'rgba(61, 227, 186, 0.04)',
          'rgba(241, 64, 67, 0.04)',
          'rgba(0, 216, 220, 0.04)',
        ][Math.floor(Math.random() * 6)],
      }}>
      <img src={card.icon} className={style.icon} alt="" />
      <h3>{card?.name}</h3>
      <p>{card.desc}</p>
      <Access accessible={!!card.logo}>
        <img
          src={card.logo}
          className={style.logo}
          alt=""
        />
      </Access>
    </div>
  );
};

interface CardProps {
  card: CardType;
}
export default Card;
Card.displayName = 'Card';

export type CardType = {
  disable?: any;
  name?: string;
  icon: string;
  key: string;
  title: string;
  desc: string;
  /**
   * 外部链接/开头,内部不带/
   * @example 外部模块: /learn/#/test 内部模块: translate/test
   */
  link: string;
  bgColor?: string;
  logo?: string;
  code: string;
};
