import React, { <PERSON> } from 'react';
import { Spin } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './loading.less';
import { useSelector } from '@@/plugin-dva/exports';
import { IGlobal } from '@/models/global';

const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
const Loading: FC = () => {
  const { showLoading } = useSelector<any, IGlobal>(state => state.global);
  return showLoading ? (
    <div className="loading-container">
      <Spin indicator={antIcon} size="large" />
    </div>
  ) : null;
};
export default Loading;
