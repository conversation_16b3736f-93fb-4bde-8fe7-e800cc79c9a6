.touches_container{
    width: 100%;
    height: 100%;
    background-color: #E3E8FE;
    position: relative;
    padding-top: 20px;
    
    .content{
        width: calc(100% - 40px);
        height: calc(100% - 20px);
        background-color: #fff;
        margin-left: 20px;
        border-radius: 6px;
        padding: 20px;

        .left_view{
            width: 50%;
            height: 100%;
            float: left;
            padding-right: 20px;

            .input_span{
                width: 100%;
                height: calc(100% - 60px);
                background-color: #F2F2F2;
            }

            .option_view{
                width: 100%;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                .upload_view{
                    height: 100%;
                    display: flex;
                    align-items: flex-end;


                    .pdf_view{
                        width: 35px;
                        height: 100%;
                        display: flex;
                        align-items: flex-end;
                        align-content: flex-end;
                        flex-wrap: wrap;
                        justify-content: center;
                        margin-right: 10px;
                        cursor: pointer;

                        img{
                            width: 30px;                            
                        }

                        span{
                            font-size: 12px;
                            color: #AFA9A9;
                        }
                    }

                    .word_view{
                        width: 35px;
                        height: 100%;
                        display: flex;
                        align-items: flex-end;
                        align-content: flex-end;
                        flex-wrap: wrap;
                        justify-content: center;
                        cursor: pointer;

                        img{
                            width: 27px;                            
                        }

                        span{
                            font-size: 12px;
                            color: #AFA9A9;
                        }
                    }
                }
                
            }
        }

        .right_view{
            width: 50%;
            height: 100%;
            float: left;
            background-color: #F2F2F2;
            border-radius: 6px;
            padding: 20px;
            padding-bottom: 0;

            .output_span{
                width: 100%;
                height: calc(100% - 60px);
            }

            .option_view{
                width: 100%;
                height: 60px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                img{
                    width: 24px;
                    cursor: pointer;
                }


            }
        }
    }
}