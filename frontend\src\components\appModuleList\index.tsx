/*
 * @Author: 李武林
 * @Date: 2021-08-20 13:45:16
 * @LastEditors: 李武林
 * @LastEditTime: 2022-07-21 16:10:20
 * @FilePath: \frontend\src\components\appModuleList\index.tsx
 * @Description: 
 * 
 * Copyright (c) 2022 by 李武林/索贝数码, All Rights Reserved. 
 */
import React, { FC, useEffect, useState } from 'react';
import {
  Button,
  Popconfirm,
  Space,
  Table,
  Form,
  Input,
  message,
  Switch,
} from 'antd';
import AppModuleService from '@/api/appModule';

interface IAppModuleList {
  moduleId: number;
}

const AppModuleList: FC<IAppModuleList> = ({ moduleId }) => {
  const [data, setData] = useState<AppModule.ModuleItem[]>([]);

  useEffect(() => {
    fetchData();
  }, [moduleId]);

  const fetchData = async () => {
    const res = await AppModuleService.fetchModuleListV2(moduleId);
    if (res.errorCode === 'success') {
      setData(res.extendMessage);
    }
  };

  const changeStatus = async (moduleId: number, disable: boolean) => {
    const res = await AppModuleService.changeModuleStatus(moduleId, disable);
    if (res.errorCode === 'success') {
      message.success('操作成功');
      fetchData();
    }
  };

  const columns = [
    {
      title: '模块代码',
      dataIndex: 'code',
    },
    {
      title: '模块名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      align: 'center',
      dataIndex: 'disable',
      render: (disable: boolean, record: any) => (
        <Switch
          checkedChildren="启用"
          unCheckedChildren="禁用"
          checked={!disable}
          defaultChecked={!disable}
          onClick={() => changeStatus(record.id, !disable)}
        />
      ),
    },
  ];
  return (
    <div className="content-table-wrapper">
      <Table
        rowKey="id"
        dataSource={data}
        pagination={false}
        columns={columns as any}
      />
    </div>
  );
};
export default AppModuleList;
