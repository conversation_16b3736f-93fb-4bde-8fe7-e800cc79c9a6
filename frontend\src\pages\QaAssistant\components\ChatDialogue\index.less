.chatdialogue-style {
    width: 100%;
    height: calc(100% - 70px);
    position: relative;

    .chat-ls-wrp {
        padding: 20px;
        height: calc(100% - 80px);
        overflow: auto;
        scroll-behavior: smooth;
        // height: calc(100% - 54px);
        overflow: auto;
    }

    .dotting {
        display: inline-block;
        width: 10px;
        min-height: 2px;
        padding-right: 2px;
        margin-left: 2px;
        padding-left: 2px;
        border-left: 2px solid currentColor;
        border-right: 2px solid currentColor;
        background-color: currentColor;
        background-clip: content-box;
        box-sizing: border-box;
        -webkit-animation: dot 0.8s infinite step-start both;
        animation: dot 0.8s infinite step-start both;

        &:before {
            content: '...';
        }

        &::before {
            content: '';
        }
    }

    @-webkit-keyframes dot {
        25% {
            border-color: transparent;
            background-color: transparent;
        }

        50% {
            border-right-color: transparent;
            background-color: transparent;
        }

        75% {
            border-right-color: transparent;
        }
    }

    @keyframes dot {
        25% {
            border-color: transparent;
            background-color: transparent;
        }

        50% {
            border-right-color: transparent;
            background-color: transparent;
        }

        75% {
            border-right-color: transparent;
        }
    }
    
    .fooder-text-content {
        width: 100%;
        padding: 0 20px;
        position: absolute;
        bottom: 20px;
        display: flex;
        align-items: center;
        column-gap: 10px;

        .button {
            background-color: var(--primary-color);
            border-radius: 32px !important;
            // position: absolute;
            // right: 10px;
            // bottom: 10px;

            // padding: 4px 6px;
            // position: absolute;
            // bottom: 10px;
            // right: 20px;
        }

    }
}

@media sreen and (min-width: 750px) {
	color: red
}
