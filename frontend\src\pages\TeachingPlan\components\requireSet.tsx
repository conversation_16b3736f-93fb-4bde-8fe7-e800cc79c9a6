import React, { FC, useState, useRef, useImperativeHandle, useEffect } from 'react';
import { CloseCircleOutlined, PlusCircleFilled, PlusOutlined } from '@ant-design/icons'
import { Input, Upload, message } from 'antd'
import type { UploadProps } from 'antd';
import MapModal from './mapModal';
import ChapterModal from './chapterModal';
import ResourseUpload from './resourseUpload';
interface RequireSetProps {
  selectType: number | string;
  emitInputValue: (value: string) => void;
  mapRef: any;
}
const RequireSet: FC<RequireSetProps> = ({
  selectType,
  emitInputValue,
  mapRef
}) => {
  const [value, setValue] = React.useState('')
  const [fileList, setFileList] = React.useState<any[]>([])
  const [fileMethod, setFileMethod] = React.useState<any>(null) //判断是上传文件，还是从资源库选择的 0：是上传  1：从资源库选择
  const [mapVisible, setMapVisible] = React.useState(false)
  const [chapterVisible, setChapterVisible] = React.useState(false)
  const resourseRef: any = useRef(null);
  const [mapInfo, setMapInfo] = useState<any>(null)
  const [chapterInfo, setChapterInfo] = useState<any>([]) //章节
  const [chapterChild, setChapterChild] = useState<any>([]) //章节子集

  useImperativeHandle(mapRef, () => {
    return {
      mapInfo,
      fileList,
      fileMethod,
      chapterInfo
    }
  });
  const props: UploadProps = {
    name: 'file',
    multiple: false,
    showUploadList: false,
    accept: ".doc,.docx,.txt",
    onChange(info) {
      const { status } = info.file;
      setFileMethod(0)
      setFileList([...info.fileList]);
      if (status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (status === 'done') {
        message.success(`${info.file.name} file uploaded successfully.`);
      } else if (status === 'error') {
        message.error(`${info.file.name} file upload failed.`);
      }
    },
    beforeUpload: (file: any) => {
      const isExcel = file.type === 'application/msword' ||
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
        file.type === 'text/plain';
      if (!isExcel) {
        message.error(`只能上传word或txt文件!`);
        setFileList([]);
        return isExcel || Upload.LIST_IGNORE;
      } else {
        setFileList([...fileList]);
      }
      return false
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };
  useEffect(() => {
    setMapInfo(null)
    setFileList([])
    setFileMethod(null)
  }, [selectType]);

  const buildTree = (data: any) => {
    const idMap: any = {};
    const tree: any = [];
    data.forEach((item: any) => {
      idMap[item.id] = { ...item, children: [] };
    });
    data.forEach((item: any) => {
      const node = idMap[item.id];
      if (item.parentId === null || item.parentId === undefined || item.parentId == '') {
        tree.push(node);
      } else {
        const parent = idMap[item.parentId];
        if (parent) {
          parent.children.push(node);
        }
      }
    });
    return tree;
  }
  return (
    <>
      <div className='require-set-content'>
        {((selectType == 1 && chapterInfo.length == 0) || (selectType == 2 && !mapInfo)) && < div className='add-box' onClick={() => {
          if (selectType == 2) {
            setMapVisible(true)
          } else {
            setChapterVisible(true)
          }
        }}>
          <PlusCircleFilled />
          {selectType == 1 ? '选择课程章节' : '选择图谱'}
        </div>
        }
        {selectType == 2 && mapInfo && <div className='add-content'>
          <div className='file-list'>
            <div className='file-item'>
              <img src={require("@/assets/img/teachingPlan/tupu02.png")} alt="" />
              <span>{mapInfo.mapName}</span>
              <CloseCircleOutlined onClick={() => {
                setMapInfo(null)
              }} />
            </div>
          </div>
        </div>
        }
        {
          selectType == 1 && chapterChild.length > 0 && <div className='add-content'>
            <div className='title'>已选{chapterChild.length}个章节</div>
            <div className='file-list'>
              {chapterChild.map((item: any, i: number) => {
                return <div className='file-item' key={item.key}>
                  <img src={require("@/assets/img/teachingPlan/zhangjie02.png")} alt="" />
                  <span>{item.parentName + "-" + item.name}</span>
                  <CloseCircleOutlined onClick={() => {
                    const newChapterChild = chapterChild.filter((it: any) => it.key !== item.key)
                    setChapterChild(newChapterChild)
                    // 找到chapterInfo中children中与当前key相等的项删除
                    const newChapterInfo = chapterInfo.map((it: any) => {
                      let newChild = it.children
                      if (it.key == item.parentKey) {
                        newChild = it.children.filter((chi: any) => chi.key !== item.key)
                      }
                      return {
                        ...it,
                        children: newChild
                      }
                    })
                    // 去掉children为空的项
                    const info = newChapterInfo.filter((it: any) => it.children && it.children.length > 0)
                    setChapterInfo(info)
                  }} />
                </div>
              })
              }
            </div>
            <div className='add-btn' onClick={() => setChapterVisible(true)}>
              <PlusCircleFilled />
              添加
            </div>
          </div>
        }
        {selectType == 4 && <>{fileList.length == 0 ? <div className='cailiao-upload'>
          <Upload.Dragger {...props}>
            <p className="ant-upload-drag-icon">
              <PlusCircleFilled /> 点击上传或将文件拖拽至此处上传
            </p>
          </Upload.Dragger>
          <p className="ant-upload-text">1. 上传文件支持word、txt格式</p>
          <p className="ant-upload-text">1. 可从<a onClick={() => {
            resourseRef.current?.open()
            setFileMethod(1)
          }}>资源库</a>中选择上述格式与有语音文本的音视频</p>
        </div> : <div className='add-content'>
          <div className='file-list'>
            <div className='file-item'>
              <img src={require("@/assets/img/teachingPlan/cailiao02.png")} alt="" />
              <span className='file-name'>{fileList?.[0]?.name}</span>
              <CloseCircleOutlined onClick={() => {
                setFileList([])
              }} />
            </div>
          </div>
        </div>
        }
        </>
        }
        <div className='text-input-aera'>
          {selectType !== 3 && <div className='header'>补充说明 <span>（选填）</span></div>}
          <Input.TextArea
            value={value}
            bordered={false}
            onChange={(e) => {
              setValue(e.target.value)
              emitInputValue(e.target.value)
            }}
            placeholder={selectType == 3 ? '将根据你输入的文本内容来生成题目' : '你可在此补充教案要求'}
            autoSize={{ minRows: 5 }}
          />
        </div>
      </div >

      <MapModal
        modalVisible={mapVisible}
        onCloseModal={(val: any) => {
          setMapVisible(false)
        }}
        emitSelectMap={(val: any) => {
          setMapInfo(val)
        }}
      />
      <ChapterModal
        modalVisible={chapterVisible}
        onCloseModal={(val: any) => {
          setChapterVisible(false)
          if (val) {
            setChapterInfo((pre: any) => {
              const arr = [...pre, ...val]
              // 使用 Map 去重（保留后添加的元素）
              const uniqueArr = Array.from(
                new Map(arr.map(item => [item.key, item])).values()
              );
              return uniqueArr
            })

            const chapters: any = []
            val.forEach((item: any) => {
              item.children.forEach((chi: any) => {
                chapters.push({ ...chi, parentKey: item.key, parentName: item.name })
              })
            })
            setChapterChild((pre: any) => {
              const arr = [...pre, ...chapters]
              const uniqueArr = Array.from(
                new Map(arr.map(item => [item.key, item])).values()
              );
              return uniqueArr
            })
          }
        }}
      />
      <ResourseUpload
        resourseRef={resourseRef}
        emitResourse={(resourse: any) => {
          setFileList([{
            id: resourse[0]?.contentId,
            name: resourse[0]?.name,
          }])
        }}
      />
    </>
  );
};

export default RequireSet;