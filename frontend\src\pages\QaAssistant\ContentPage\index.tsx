import React, { FC, useEffect, useState } from 'react';
import { FullscreenExitOutlined, FullscreenOutlined } from '@ant-design/icons';
import { reqAllAgents, addQa } from '@/api/assistant';
import ChatDialogue from '../components/ChatDialogue';
import type { MenuProps } from 'antd';
import { assistantItem } from './models';
import HistoryImg from '@/images/chatTools/history.png';
import ToolsImg from '@/images/chatTools/tools.png';
import DownIcon from '@/images/chatTools/downIcon.png';
import TopIcon from '@/images/chatTools/topIcon.png';
import AssistantAvatar from '@/assets/img/qa-assistant.png';
import { Dropdown, Avatar, Space, message } from 'antd';
import styles from './index.less';
import { IGlobal } from '@/models/global';
import { history, useLocation, useSelector, useDispatch } from 'umi';
import './antd.less';

type baseType = string | number;

const ContentPage: FC = () => {

    const location: any = useLocation();
    const { query } = location;

    const dispatch = useDispatch();

    const [assistantList, setAssistantList] = useState<MenuProps['items']>([]); // 下拉Ai列表
    const [listBoxOpen, setListBoxOpen] = useState<boolean>(false); // 下来ai列表点击打开状态
    const [activeKey, setActiveKey] = useState<assistantItem | undefined>({}); // Ai列表选中项
    const [selectInfo, setSelectInfo] = useState<any>('');

    const { userInfo, fullScreenState } = useSelector<any, IGlobal>(
        (state: any) => state.global,
    );

    const userOnlyStudent = userInfo?.roles?.length === 1 && userInfo?.roles?.[0]?.roleCode === 'r_student';

    // 获取AI助手列表信息
    const getAssistantList = () => {
        reqAllAgents({ page: 1, page_size: 99999 }).then((res: any) => {
            let data = res?.extend_message?.records || [];

            if (data.length === 0) {
                message.warning('未获取到agent智能助手！')
                return;
            }
            data = data.map((item: any) => ({
                ...item,
                key: item.id,
                agent_uniq: item.id,
                label: <div>
                    <Avatar
                        size={40}
                        style={{ marginRight: 10 }}
                        src={item.icon}
                        icon={<img src={AssistantAvatar}></img>}
                        draggable={false}
                    />{item.name}</div>
            }))
            setAssistantList(data);
            if (query && query.assistant_id) {
                const source = data?.find((item: any) => item.id === query.assistant_id);
                setActiveKey(source);
            } else {
                setActiveKey(data?.[0] || {});
            }
        })
    }

    useEffect(() => {
        getAssistantList();
        if (query && query.frame_id) {
            changeSelectInfo(query.frame_id);
        }
    }, []);

    const frameDom = document.getElementById('frame-chat') as HTMLIFrameElement;

    const changeSelectInfo = (id: string) => {
        setSelectInfo(id);
        frameDom?.contentWindow && frameDom.contentWindow.postMessage(JSON.stringify({ frameId: id, agentInfo: activeKey }), '*');
    }

    // 开启一条新对话
    const handleAddQa = () => {
        changeSelectInfo('新对话');
    };

    useEffect(() => {
        !query?.frame_id && handleAddQa();
    }, [activeKey]);


    const changeAssistant: MenuProps['onClick'] = ({ key }) => {
        const data: assistantItem = assistantList?.find((item: any) => item.id === key);
        setActiveKey(data);
    };

    const linkPage = (router: string) => {
        history.push(router)
    }

    const dropBaseStyle = {
        minWidth: 200,
        color: 'red',
        maxHeight: 400,
        overflow: 'auto',
        boxShadow: '0px 10px 20px 0px rgba(84,156,255,0.1)'

    }

    // 改变agent宽高
    const changeFullScree = (state: boolean) => {
        window.parent.postMessage(
            JSON.stringify({ fullScreenState: state }),  // true:代表高度100vh， false: 代表高度80vh
            '*',
        );

        dispatch({
            type: 'global/agentToolChange',
            payload: state
        });
    }

    return (
        <div className={styles.contentPageStyle} >
            <div className={styles.dropdownStyle}>
                {/* <Dropdown
                    menu={{ items: assistantList, onClick: changeAssistant }}
                    trigger={['click']}
                    overlayClassName='change-antd-dropdown'
                    overlayStyle={{ ...dropBaseStyle }}
                >
                    <a onClick={e => {e.preventDefault(); setListBoxOpen(!listBoxOpen);} } className={styles.selectLabel}>
                        <Space>
                            {activeKey?.name}
                            <img src={listBoxOpen ? TopIcon : DownIcon} className={styles.openIcon} />
                        </Space>
                    </a>
                </Dropdown> */}
                <div style={{ height: 40, fontSize: 17, fontWeight: 'bold', color: 'rgba(0, 0, 0, 0.8)' }}>{activeKey?.name}</div>
                <div className={styles.icons}>
                    {/* 收起 */}
                    {fullScreenState && <FullscreenExitOutlined
                        style={{ fontSize: 20, color: 'rgba(0, 0, 0, 0.7)' }}
                        title='收起'
                        onClick={() => {
                            changeFullScree(false);
                        }} />}
                    {/* 全屏 */}
                    {!fullScreenState && <FullscreenOutlined
                        style={{ fontSize: 20, color: 'rgba(0, 0, 0, 0.7)' }}
                        title='展开'
                        onClick={() => {
                            changeFullScree(true);
                        }} />}
                    <img src={HistoryImg}
                        onClick={() => { linkPage(`/historyChat?assistant_id=${activeKey?.id}`) }}
                        title='历史记录'
                        className={styles.headerImg}
                    />
                    {!userOnlyStudent && <img src={ToolsImg}
                        onClick={() => { linkPage(`/treasureBox?assistant_id=${activeKey?.id}`) }}
                        title='百宝箱'
                        className={styles.headerImg}
                    />}
                </div>
            </div>
            {/* <div className={styles.line} /> */}
            <ChatDialogue frameId={selectInfo} assistantId={activeKey?.id} agentData={activeKey || {}} handleAddQa={handleAddQa} updateFirstList={(id) => changeSelectInfo(id)} />
        </div>
    )
}

export default ContentPage;
