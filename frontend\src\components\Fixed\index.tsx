import { useContainerDimensions } from '@/hooks';
import React, { useRef } from 'react';
import style from './index.less';

const Fixed: React.FC<AppProps> = ({
  children,
  left,
  top,
  right,
  bottom,
  style: propsStyle,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const { width, height } = useContainerDimensions(ref);

  return (
    <>
      <div
        ref={ref}
        className={style.fixed}
        style={{
          left,
          top,
          right,
          bottom,
          ...propsStyle,
        }}
      >
        {children}
      </div>
      <div
        style={{
          width,
          minWidth: width,
          height,
          minHeight: height,
        }}
      ></div>
    </>
  );
};

interface AppProps {
  /**单个子集 */
  children: React.ReactElement;
  left?: number;
  top?: number;
  right?: number;
  bottom?: number;
  style?: React.CSSProperties;
}
export default Fixed;
Fixed.displayName = 'fixed';
