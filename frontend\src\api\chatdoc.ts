import { request } from 'umi';
import { Request } from '@/constant/Request';
import {
  generatContentType,
  objToFormData,
} from '@/utils/utils';
import { IResponse, AIResponse } from '.';
import {
  FetchEventSourceInit,
  fetchEventSource,
} from '@microsoft/fetch-event-source';

export namespace Chatdoc {
  export const uploadFile = (file: File) => {
    return request<{
      doc_id: string;
    }>(`/upload/upload_docs`, {
      method: 'post',
      data: objToFormData({
        file,
      }),
      prefix: Request.PREFIX.CHATDOC,
    });
  };
  export const mainContent = (file: File) => {
    return request(`/doc/main_content`, {
      method: 'post',
      data: objToFormData({
        file,
      }),
      prefix: Request.PREFIX.CHATDOC,
    });
  };
  export const chatDocs = (data: {
    /**
     * 上下文
     */
    context: string[];
    /**
     * 文档id
     */
    doc_id: string;
    /**
     * 输出的问题
     */
    input_text: string;
  }) => {
    return request(`/chat/docs`, {
      method: 'post',
      data,
      prefix: Request.PREFIX.CHATDOC,
    });
  };
  export const transform = (data: string) => {
    return data
      .replaceAll('/n', '')
      .split('}{')
      .map((item, index, arr) => {
        const trimmedItem = `
        ${index !== 0 ? '{' : ''}${item.trim()}
        ${index === arr.length - 1 ? '' : '}'}
        `;
        return JSON.parse(trimmedItem);
      });
  };
  export const fetchChat = (body: {
    /**
     * 上下文
     */
    context: string[];
    /**
     * 文档id
     */
    doc_id: string;
    /**
     * 输出的问题
     */
    input_text: string;
  }) => {
    return fetch(`${Request.PREFIX.CHATDOC}/chat/docs`, {
      method: 'post',
      body: JSON.stringify(body),
      headers: {
        // 'Content-Type': 'text/event-stream',
        accept: 'text/event-stream',
        'Content-Type': 'application/json',
      },
    });
  };
  export const fetchDoc = (
    body: {
      /**
       * 上下文
       */
      context: any[];
      /**
       * 文档id
       */
      doc_id: string;
      /**
       * 输出的问题
       */
      input_text: string;
    },
    onmessage: FetchEventSourceInit['onmessage'],
    onclose: FetchEventSourceInit['onclose'],
  ) => {
    return fetchEventSource(
      `${Request.PREFIX.CHATDOC}/chat/docs`,
      {
        method: 'POST',
        headers: {
          // Accept: 'text/event-stream',
          'Content-Type': 'text/event-stream',
        },
        body: JSON.stringify(body),
        async onopen(res) {
          if (res.ok && res.status === 200) {
            console.log('Connection made ', res);
          } else if (
            res.status >= 400 &&
            res.status < 500 &&
            res.status !== 429
          ) {
            console.log('Client side error ', res);
          }
        },
        onmessage,
        onclose,
        onerror(err) {
          console.log(
            'There was an error from server',
            err,
          );
        },
      },
    );
  };
}
