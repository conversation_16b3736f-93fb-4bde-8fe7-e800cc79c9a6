.recommend_box {
  margin-left: 10px;
  padding: 10px;
  box-sizing: border-box;
  width: 550px;
  border-left: 1px solid #f0f0f0;
  .recommend_title{
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    .anticon{
      font-size: 24px;
      margin-right: 10px;
    }
  }
  .tips{
    margin-bottom: 10px;
    span{
      font-weight: 600;
    }
  }
  .list {
    .item {
      display: flex;
      padding: 4px;
      box-sizing: border-box;
      border: 1px solid #f0f0f0;
      img {
        width: 224px;
        height: 126px;
      }
      .content {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        .name_wrap{
          &>span{
            margin-left: 6px;
          }
        }
        .info {
          margin-top: auto;
          color: #666;
          & > div {
            margin-top: 8px;
          }
          .tag{
            color: var(--primary-color);
            background-color: rgba(var(--el-color-primary-rgb), 0.1);
            padding: 0 6px;
            border-radius: 99px;
            &+.tag{
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
}
