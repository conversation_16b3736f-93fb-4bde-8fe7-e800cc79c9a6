.chat-item-wrp {
  display: flex;
  margin-bottom: 40px;

  &.mine {
    flex-direction: row-reverse;

    .chat-message-box {
      margin-left: 0;
      margin-right: 20px;
    }

    .chat-message-content {
      background: var(--primary-color);
      color: #fff;

      &::before {
        right: -12px;
        left: unset;
        border-right-color: transparent;
        border-top-color: var(--primary-color);
        border-left: 6px solid var(--primary-color);
        // transform: skewY(-30deg);
      }
    }
  }

  .chat-message-box {
    max-width: 60%;
    margin-left: 20px;
    margin-top: 20px;
  }

  .chat-message-content {
    padding: 10px 12px;
    background: #f7f9fa;
    font-size: 14px;
    min-height: 38px;
    border-radius: 8px;
    margin-bottom: 8px;
    position: relative;
    color: #2e2e2e;
    line-height: 24px;
    word-break: break-word;

    p {
      margin-bottom: 10px;

      &:has(a) {
        text-indent: 14px;
      }

      &:last-child {
        margin: 0;
      }
    }

    pre {
      white-space: pre-wrap;
      word-wrap: break-word;
      line-height: 24px;
      overflow: hidden;
      // vertical-align: top;
    }

    a {
      &:hover {
        text-decoration: underline;
      }
    }

    &::before {
      content: '';
      position: absolute;
      width: 0;
      height: 0;
      /* skewY(50deg)和skewY(-50deg)可以调节箭头方向*/
      top: 10px;
      left: -12px;
      border-top: 4px solid #f7f9fa;
      border-left: 6px solid transparent;
      border-bottom: 4px solid transparent;
      border-right: 6px solid #f7f9fa;
      // transform: skewY(30deg);
    }
  }

  .chat-message-date {
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    color: #a4a4a4;

    .copy-btn {
      cursor: pointer;
      margin-right: 10px;

      .anticon {
        color: var(--primary-color);
        margin-right: 4px;
      }
    }
  }

  .course-list-wrp {
    display: flex;
    justify-content: space-between;
  }

  .more {
    width: 45px;
    margin-top: 5px;
    text-align: right;
    position: relative;
    a {
      bottom: 0;
      left: 0;
      position: absolute;
    }
  }

  .course-item-wrp {
    cursor: pointer;
    width: calc(30% - 15px);

    img {
      width: 100%;
      height: auto;
    }

    .name-wrp {
      background: #fff;
      padding: 5px 0 5px 5px;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    span {
      font-size: 16px;
      font-weight: bold;

      &.high {
        color: var(--primary-color);
      }
    }
  }
}
