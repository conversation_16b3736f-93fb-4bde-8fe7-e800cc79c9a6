{"version": 3, "sources": ["webpack:///mam-timecode-convert.min.js", "webpack:///webpack/bootstrap 69b94aa6aba7899e1ed8", "webpack:///./src/index.js"], "names": ["modules", "__webpack_require__", "moduleId", "installedModules", "exports", "module", "i", "l", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "configurable", "enumerable", "get", "n", "__esModule", "object", "property", "prototype", "hasOwnProperty", "p", "s", "Array", "S", "String", "fromCharCode", "contains", "e", "RegExp", "this", "test", "join", "replaceAll", "s1", "s2", "replace", "TimeCodeConvert", "timeCodeConvertHelper", "MpcVideoStandard", "mpcVideostandardUnknow", "mpcVideostandardPal", "mpcVideostandardNtsc2997", "mpcVideostandardNtsc30", "mpcVideostandardSecam", "mpcVideostandard1920108050I", "mpcVideostandard192010805994I", "mpcVideostandard1920108060I", "mpcVideostandard192010802398P", "mpcVideostandard1920108024P", "mpcVideostandard1920108025P", "mpcVideostandard192010802997P", "mpcVideostandard1920108030P", "mpcVideostandard12807202398P", "mpcVideostandard128072024P", "mpcVideostandard128072050P", "mpcVideostandard12807205994P", "mpcVideostandard1440108050I", "mpcVideostandard144010805994I", "mpcVideostandard1440108060I", "m_mpcStRate25", "MpcStFrameRate25", "MpcStScale25", "m_mpcStRate2997", "MpcStFrameRate2997", "MpcStScale2997", "m_mpcStRate30", "MpcStFrameRate30", "MpcStScale30", "m_mpcStRate24", "MpcStFrameRate24", "MpcStScale24", "m_mpcStRate2398", "MpcStFrameRate2398", "MpcStScale2398", "m_mpcStRate50", "MpcStFrameRate50", "MpcStScale50", "m_mpcStRate5994", "MpcStFrameRate5994", "MpcStScale5994", "m_mpcStRate60", "MpcStFrameRate60", "MpcStScale60", "MpcFramesSecond25", "MpcFramesMinute25", "MpcFramesHour25", "MpcFramesMinute24Drop", "MpcFrames10Minutes24Drop", "MpcFramesHour24Drop", "MpcFramesSecond24", "MpcFramesMinute24", "MpcFramesHour24", "MpcFramesSecondNodrop30", "MpcFramesMinuteNodrop30", "MpcFramesHourNodrop30", "MpcFramesMinute30Drop", "MpcFrames10Minutes30Drop", "MpcFramesHour30Drop", "MpcFramesSecond50", "MpcFramesMinute50", "MpcFramesHour50", "MpcFramesMinute60Drop", "MpcFrames10Minutes60Drop", "MpcFramesHour60Drop", "MpcFramesSecond60", "MpcFramesMinute60", "MpcFramesHour60", "frame2L100Ns$1", "lFrame", "dRate", "dScale", "dFrameRate", "v", "dFrameScale", "rate2ScaleFrameRateAndFrameScale", "parseInt", "Math", "floor", "pow", "frame2L100Ns", "dFrame", "frameRate2RateAndScale", "frame2Second$1", "frame2Second", "frame2Tc$1", "dropFrame", "getRateDropFrame", "strTc", "dHour", "dResidue", "dMin", "dSec", "dFra", "formatTimeCodeString", "dHour1", "dResidue1", "dMin1", "div", "dSec1", "dFra1", "dHour11", "dResidue11", "dMin11", "dSec11", "dFra11", "frame2Tc", "timeCode2Frame$1", "sTimeCode", "frameRate", "ftcFrames", "timeCode2NdfFrame", "lHour", "lMinute", "lSecond", "timeCode2Format", "lwReste", "lHour1", "lMinute1", "lSecond1", "lFrame1", "lwReste1", "lHour11", "lMinute11", "lSecond11", "lFrame11", "lwReste11", "timeCode2Frame", "formatTimeCodeString$1", "newTc", "getFrameByTimeCode", "abs", "l100Ns2Frame$1", "l100Ns", "round", "l100Ns2Frame", "l100Ns2Second", "l100Ns2Tc", "llResidue", "dFraction", "l100Ns2Tc$1", "second2Frame$1", "dbSec", "second2Frame", "hours", "minutes", "seconds", "frames", "wrap", "framesSeparator", "timeCode", "ftcs", "split", "length", "showframeRate", "ceil", "toString", "dropM", "drop5994F", "dropF", "ftcssf", "showframeRate1", "dropM1", "drop5994F1", "dropF1", "isAdded", "corr<PERSON><PERSON><PERSON>", "ftcNewFrames", "rate", "ftcSeconds", "ftcCodes", "SecondToTimeString_audio", "opts", "iseconds", "i10Milliseconds", "hoursStr", "minutesStr", "isecondsStr", "i10MillisecondsStr", "timeCode2L100Ns_audio", "I10Milliseconds", "undefined", "l100ns", "toFixed", "str", "a", "b", "hasValue", "obj", "window", "timecodeconvert"], "mappings": "CAAS,SAAUA,GCInB,QAAAC,GAAAC,GAGA,GAAAC,EAAAD,GACA,MAAAC,GAAAD,GAAAE,OAGA,IAAAC,GAAAF,EAAAD,IACAI,EAAAJ,EACAK,GAAA,EACAH,WAUA,OANAJ,GAAAE,GAAAM,KAAAH,EAAAD,QAAAC,IAAAD,QAAAH,GAGAI,EAAAE,GAAA,EAGAF,EAAAD,QAvBA,GAAAD,KA4BAF,GAAAQ,EAAAT,EAGAC,EAAAS,EAAAP,EAGAF,EAAAU,EAAA,SAAAP,EAAAQ,EAAAC,GACAZ,EAAAa,EAAAV,EAAAQ,IACAG,OAAAC,eAAAZ,EAAAQ,GACAK,cAAA,EACAC,YAAA,EACAC,IAAAN,KAMAZ,EAAAmB,EAAA,SAAAf,GACA,GAAAQ,GAAAR,KAAAgB,WACA,WAA2B,MAAAhB,GAAA,SAC3B,WAAiC,MAAAA,GAEjC,OADAJ,GAAAU,EAAAE,EAAA,IAAAA,GACAA,GAIAZ,EAAAa,EAAA,SAAAQ,EAAAC,GAAsD,MAAAR,QAAAS,UAAAC,eAAAjB,KAAAc,EAAAC,IAGtDtB,EAAAyB,EAAA,GAGAzB,IAAA0B,EAAA,KDMM,SAAUtB,EAAQD;;;;;;;AEvDxBwB,MAAAJ,UAAAK,EAAAC,OAAAC,aAAA,GACAH,MAAAJ,UAAAQ,SAAA,SAAAC,GAEA,MADA,IAAAC,QAAAC,KAAAN,EAAAI,EAAAE,KAAAN,GACAO,KAAAD,KAAAN,EAAAM,KAAAE,KAAAF,KAAAN,GAAAM,KAAAN,IAMAC,OAAAN,UAAAc,WAAA,SAAAC,EAAAC,GACA,MAAAL,MAAAM,QAAA,GAAAP,QAAAK,EAAA,MAAAC,GAGA,IAAAE,GAAA,WAQA,GAAAC,GAAA,YA85CA,OA35CAA,GAAAnB,WAMAoB,kBACAC,uBAAA,EACAC,oBAAA,EACAC,yBAAA,EACAC,uBAAA,EACAC,sBAAA,EACAC,4BAAA,GACAC,8BAAA,GACAC,4BAAA,GACAC,8BAAA,IACAC,4BAAA,IACAC,4BAAA,IACAC,8BAAA,KACAC,4BAAA,KACAC,6BAAA,KACAC,2BAAA,KACAC,2BAAA,MACAC,6BAAA,MACAC,4BAAA,MACAC,8BAAA,OACAC,4BAAA,QAYAC,cAAA,GAWAC,iBAAA,GAWAC,aAAA,EAUAC,gBAAA,IAWAC,mBAAA,IAWAC,eAAA,KAUAC,cAAA,GAWAC,iBAAA,GAWAC,aAAA,EAUAC,cAAA,GAWAC,iBAAA,GAWAC,aAAA,EAUAC,gBAAA,KAWAC,mBAAA,KAWAC,eAAA,KAUAC,cAAA,GAWAC,iBAAA,GAWAC,aAAA,EAUAC,gBAAA,IAWAC,mBAAA,IAWAC,eAAA,KAUAC,cAAA,GAWAC,iBAAA,GAWAC,aAAA,EAWAC,kBAAA,GAWAC,kBAAA,KAWAC,gBAAA,IAWAC,sBAAA,KAWAC,yBAAA,MAWAC,oBAAA,MAWAC,kBAAA,GAWAC,kBAAA,KAWAC,gBAAA,MAWAC,wBAAA,GAWAC,wBAAA,KAWAC,sBAAA,MAWAC,sBAAA,KAWAC,yBAAA,MAWAC,oBAAA,OAWAC,kBAAA,GAWAC,kBAAA,IAWAC,gBAAA,KAWAC,sBAAA,KAWAC,yBAAA,MAWAC,oBAAA,OAWAC,kBAAA,GAWAC,kBAAA,KAWAC,gBAAA,MAaAC,eAAA,SAAAC,EAAAC,EAAAC,GACA,GAAAC,IAA8BC,EAAAnF,KAAA+B,kBAC9BqD,GAA+BD,EAAAnF,KAAAgC,aAI/B,OAFAhC,MAAAqF,iCAAAL,EAAAC,EAAAC,EAAAE,GAEAE,SAAAC,KAAAC,MAAAT,EAAAQ,KAAAE,IAAA,MAAAP,EAAAC,EAAAC,EAAAD,KAaAO,aAAA,SAAAC,EAAAT,GACA,GAAAF,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAG1B,OAFAhC,MAAA4F,uBAAAV,EAAAF,EAAAC,GAEAK,SAAAC,KAAAC,MAAAG,EAAAV,EAAAE,EAAAI,KAAAE,IAAA,MAAAT,EAAAG,KAcAU,eAAA,SAAAd,EAAAC,EAAAC,GACA,GAAAC,IAA8BC,EAAAnF,KAAA+B,kBAC9BqD,GAA+BD,EAAAnF,KAAAgC,aAG/B,OAFAhC,MAAAqF,iCAAAL,EAAAC,EAAAC,EAAAE,GAEAL,EAAAK,EAAAD,EAAAD,EAAAC,GAaAW,aAAA,SAAAH,EAAAT,GACA,GAAAF,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAG1B,OAFAhC,MAAA4F,uBAAAV,EAAAF,EAAAC,GAEAU,EAAAV,EAAAE,EAAAH,EAAAG,GAeAY,WAAA,SAAAJ,EAAAX,EAAAC,EAAAe,GACAA,IACAA,EAAAhG,KAAAiG,iBAAAjB,GAEA,IAAAkB,GAAA,EACA,IAAAlB,IAAAhF,KAAA+B,kBAAAkD,IAAAjF,KAAAgC,cAAAgD,EAAAhF,KAAAgC,cAAAiD,EAAAjF,KAAA+B,iBAAA,CACA,GAAAoE,GAAAb,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAwD,kBACA4C,EAAAd,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAwD,kBACA6C,EAAAf,SAAAC,KAAAC,MAAAY,EAAApG,KAAAuD,mBACA6C,IAAApG,KAAAuD,iBACA,IAAA+C,GAAAhB,SAAAC,KAAAC,MAAAY,EAAApG,KAAAsD,oBACAiD,EAAAjB,SAAAC,KAAAC,MAAAY,EAAApG,KAAAsD,mBACA4C,GAAAlG,KAAAwG,qBAAAL,EAAAE,EAAAC,EAAAC,GAAA,OACa,IAAAvB,IAAAhF,KAAAkC,oBAAA+C,IAAAjF,KAAAmC,gBAAA6C,EAAAhF,KAAAmC,gBAAA8C,EAAAjF,KAAAkC,mBACb,GAAA8D,EAAA,CACA,GAAAS,GAAAnB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAoE,sBACAsC,EAAApB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAoE,sBACAuC,EAAArB,SAAAC,KAAAC,MAAA,GAAAD,KAAAC,MAAAxF,KAAA4G,IAAAF,EAAA1G,KAAAmE,4BACAuC,IAAA1G,KAAAmE,yBACAuC,GAAA1G,KAAAgE,0BACA0C,GAAA1G,KAAAgE,wBACA2C,GAAA,EAAArB,SAAAC,KAAAC,MAAAxF,KAAA4G,IAAAF,EAAA1G,KAAAkE,yBACAwC,GAAA1G,KAAAkE,sBACAwC,GAAA,EAEA,IAAAG,GAAAvB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA+D,0BACA+C,EAAAxB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA+D,yBACAmC,GAAAlG,KAAAwG,qBAAAC,EAAAE,EAAAE,EAAAC,GAAA,OACiB,CACjB,GAAAC,GAAAzB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAiE,wBACA+C,EAAA1B,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAiE,wBACAgD,EAAA3B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAAgE,yBACAgD,IAAAhH,KAAAgE,uBACA,IAAAkD,GAAA5B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA+D,0BACAoD,EAAA7B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA+D,yBACAmC,GAAAlG,KAAAwG,qBAAAO,EAAAE,EAAAC,EAAAC,GAAA,OAEa,IAAAnC,IAAAhF,KAAAqC,kBAAA4C,IAAAjF,KAAAsC,cAAA0C,EAAAhF,KAAAsC,cAAA2C,EAAAjF,KAAAqC,iBAAA,CACb,GAAAoE,GAAAnB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAiE,wBACAyC,EAAApB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAiE,wBACA0C,EAAArB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAAgE,yBACA0C,IAAA1G,KAAAgE,uBACA,IAAA6C,GAAAvB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA+D,0BACA+C,EAAAxB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA+D,yBACAmC,GAAAlG,KAAAwG,qBAAAC,EAAAE,EAAAE,EAAAC,GAAA,OACa,IAAA9B,IAAAhF,KAAAwC,kBAAAyC,IAAAjF,KAAAyC,cAAAuC,EAAAhF,KAAAyC,cAAAwC,EAAAjF,KAAAwC,iBAAA,CACb,GAAAuE,GAAAzB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA8D,kBACAkD,EAAA1B,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA8D,kBACAmD,EAAA3B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA6D,mBACAmD,IAAAhH,KAAA6D,iBACA,IAAAqD,GAAA5B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA4D,oBACAuD,EAAA7B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA4D,mBACAsC,GAAAlG,KAAAwG,qBAAAO,EAAAE,EAAAC,EAAAC,GAAA,OACa,IAAAnC,IAAAhF,KAAA2C,oBAAAsC,IAAAjF,KAAA4C,gBAAAoC,EAAAhF,KAAA4C,gBAAAqC,EAAAjF,KAAA2C,mBACb,GAAAqD,EAAA,CACA,GAAAS,GAAAnB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA2D,sBACA+C,EAAApB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA2D,sBACAgD,EAAArB,SAAAC,KAAAC,MAAA,GAAAxF,KAAA4G,IAAAF,EAAA1G,KAAA0D,2BACAgD,IAAA1G,KAAA0D,yBACAgD,GAAA1G,KAAA6D,oBACA6C,GAAA1G,KAAA6D,kBACA8C,GAAA,EAAArB,SAAAC,KAAAC,MAAAxF,KAAA4G,IAAAF,EAAA1G,KAAAyD,yBACAiD,GAAA1G,KAAAyD,sBACAiD,GAAA,EAEA,IAAAG,GAAAvB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA4D,oBACAkD,EAAAxB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA4D,mBACAsC,GAAAlG,KAAAwG,qBAAAC,EAAAE,EAAAE,EAAAC,GAAA,OACiB,CACjB,GAAAC,GAAAzB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA8D,kBACAkD,EAAA1B,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA8D,kBACAmD,EAAA3B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA6D,mBACAmD,IAAAhH,KAAA6D,iBACA,IAAAqD,GAAA5B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA4D,oBACAuD,EAAA7B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA4D,mBACAsC,GAAAlG,KAAAwG,qBAAAO,EAAAE,EAAAC,EAAAC,GAAA,OAEa,IAAAnC,IAAAhF,KAAA8C,kBAAAmC,IAAAjF,KAAA+C,cAAAiC,EAAAhF,KAAA+C,cAAAkC,EAAAjF,KAAA8C,iBAAA,CACb,GAAA2D,GAAAnB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAuE,kBACAmC,EAAApB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAAuE,kBACAoC,EAAArB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAAsE,mBACAoC,IAAA1G,KAAAsE,iBACA,IAAAuC,GAAAvB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAAqE,oBACAyC,EAAAxB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAAqE,mBACA6B,GAAAlG,KAAAwG,qBAAAC,EAAAE,EAAAE,EAAAC,GAAA,OACa,IAAA9B,IAAAhF,KAAAiD,oBAAAgC,IAAAjF,KAAAkD,gBAAA8B,EAAAhF,KAAAkD,gBAAA+B,EAAAjF,KAAAiD,mBACb,GAAA+C,EAAA,CACA,GAAAe,GAAAzB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA0E,sBACAsC,EAAA1B,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA0E,sBACAuC,EAAA3B,SAAAC,KAAAC,MAAA,GAAAxF,KAAA4G,IAAAI,EAAAhH,KAAAyE,2BACAuC,IAAAhH,KAAAyE,yBACAuC,GAAAhH,KAAA4E,oBACAoC,GAAAhH,KAAA4E,kBACAqC,GAAA,EAAA3B,SAAAC,KAAAC,MAAAxF,KAAA4G,IAAAI,EAAAhH,KAAAwE,yBACAwC,GAAAhH,KAAAwE,sBACAwC,GAAA,EAEA,IAAAE,GAAA5B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA2E,oBACAwC,EAAA7B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA2E,mBACAuB,GAAAlG,KAAAwG,qBAAAO,EAAAE,EAAAC,EAAAC,GAAA,OACiB,CACjB,GAAAV,GAAAnB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA6E,kBACA6B,EAAApB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA6E,kBACA8B,EAAArB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA4E,mBACA8B,IAAA1G,KAAA4E,iBACA,IAAAiC,GAAAvB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA2E,oBACAmC,EAAAxB,SAAAC,KAAAC,MAAAkB,EAAA1G,KAAA2E,mBACAuB,GAAAlG,KAAAwG,qBAAAC,EAAAE,EAAAE,EAAAC,GAAA,OAEa,IAAA9B,IAAAhF,KAAAoD,kBAAA6B,IAAAjF,KAAAqD,cAAA2B,EAAAhF,KAAAqD,cAAA4B,EAAAjF,KAAAoD,iBAAA,CACb,GAAA2D,GAAAzB,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA6E,kBACAmC,EAAA1B,SAAAC,KAAAC,MAAAG,EAAA3F,KAAA6E,kBACAoC,EAAA3B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA4E,mBACAoC,IAAAhH,KAAA4E,iBACA,IAAAsC,GAAA5B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA2E,oBACAwC,EAAA7B,SAAAC,KAAAC,MAAAwB,EAAAhH,KAAA2E,mBACAuB,GAAAlG,KAAAwG,qBAAAO,EAAAE,EAAAC,EAAAC,GAAA,GAEA,MAAAjB,IAcAkB,SAAA,SAAAzB,EAAAT,EAAAc,GACAA,IACAA,EAAAhG,KAAAiG,iBAAAf,GAEA,IAAAF,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAI1B,OAHAhC,MAAA4F,uBAAAV,EAAAF,EAAAC,GAEAjF,KAAA+F,WAAAJ,EAAAX,EAAAG,EAAAF,EAAAE,EAAAa,IAiBAqB,iBAAA,SAAAC,EAAAC,EAAAvC,EAAAC,EAAAe,GACAA,IACAA,EAAAhG,KAAAiG,iBAAAsB,GAEA,IAAAC,GAAA,CACA,IAAAxC,IAAAhF,KAAA+B,kBAAAkD,IAAAjF,KAAAgC,cAAAgD,EAAAhF,KAAAgC,cAAAiD,EAAAjF,KAAA+B,iBACAyF,EAAAxH,KAAAyH,kBAAAH,EAAAC,OACa,IAAAvC,IAAAhF,KAAAkC,oBAAA+C,IAAAjF,KAAAmC,gBAAA6C,EAAAhF,KAAAmC,gBAAA8C,EAAAjF,KAAAkC,mBAAA,CACb,GAAAwF,IAA6BvC,EAAA,GAC7BwC,GAA+BxC,EAAA,GAC/ByC,GAA+BzC,EAAA,GAC/BJ,GAA8BI,EAAA,EAI9B,IAFAnF,KAAA6H,gBAAAP,EAAAI,EAAAC,EAAAC,EAAA7C,EAAAwC,GAEAvB,EAAA,CACAwB,GAAAE,EAAAvC,EAAAnF,KAAAoE,mBAEA,IAAA0D,GAAA9H,KAAA4G,IAAAe,EAAAxC,EAAA,GACAqC,IAAAM,EAAA9H,KAAAmE,yBACA2D,EAAAH,EAAAxC,EAAA,GAEA2C,EAAA,IACAN,GAAAxH,KAAAgE,wBACAwD,IAAAM,EAAA,GAAA9H,KAAAkE,sBACAsD,GAAA,GAGAA,GAAAI,EAAAzC,EAAAnF,KAAA+D,wBACAyD,GAAAzC,EAAAI,MAEAqC,GAAAxH,KAAAyH,kBAAAH,EAAA,QAEa,IAAAtC,IAAAhF,KAAAqC,kBAAA4C,IAAAjF,KAAAsC,cAAA0C,EAAAhF,KAAAsC,cAAA2C,EAAAjF,KAAAqC,iBACbmF,EAAAxH,KAAAyH,kBAAAH,EAAAC,OACa,IAAAvC,IAAAhF,KAAAwC,kBAAAyC,IAAAjF,KAAAyC,cAAAuC,EAAAhF,KAAAyC,cAAAwC,EAAAjF,KAAAwC,iBACbgF,EAAAC,kBAAAH,EAAAC,OACa,IAAAvC,IAAAhF,KAAA2C,oBAAAsC,IAAAjF,KAAA4C,gBAAAoC,EAAAhF,KAAA4C,gBAAAqC,EAAAjF,KAAA2C,mBAAA,CACb,GAAAoF,IAA8B5C,EAAA,GAC9B6C,GAAgC7C,EAAA,GAChC8C,GAAgC9C,EAAA,GAChC+C,GAA+B/C,EAAA,EAI/B,IAFAnF,KAAA6H,gBAAAP,EAAAS,EAAAC,EAAAC,EAAAC,EAAAX,GAEAvB,EAAA,CACAwB,GAAAE,EAAAvC,EAAAnF,KAAA8D,eAEA,IAAAqE,GAAAnI,KAAA4G,IAAAe,EAAAxC,EAAA,GACAqC,IAAAW,EAAAnI,KAAA0D,yBACAyE,EAAAR,EAAAxC,EAAA,GACAgD,EAAA,IACAX,GAAAxH,KAAA4E,kBACA4C,IAAAW,EAAA,GAAAnI,KAAA6D,kBACA2D,GAAA,GAGAA,GAAAI,EAAAzC,EAAAnF,KAAA4D,kBACA4D,GAAAzC,EAAAI,MAEAqC,GAAAxH,KAAAyH,kBAAAH,EAAA,QAEa,IAAAtC,IAAAhF,KAAA8C,kBAAAmC,IAAAjF,KAAA+C,cAAAiC,EAAAhF,KAAA+C,cAAAkC,EAAAjF,KAAA8C,iBACb0E,EAAAxH,KAAAyH,kBAAAH,EAAAC,OACa,IAAAvC,IAAAhF,KAAAiD,oBAAAgC,IAAAjF,KAAAkD,gBAAA8B,EAAAhF,KAAAkD,gBAAA+B,EAAAjF,KAAAiD,mBAAA,CACb,GAAAmF,IAA+BjD,EAAA,GAC/BkD,GAAiClD,EAAA,GACjCmD,GAAiCnD,EAAA,GACjCoD,GAAgCpD,EAAA,EAGhC,IADAnF,KAAA6H,gBAAAP,EAAAc,EAAAC,EAAAC,EAAAC,EAAAhB,GACAvB,EAAA,CACAwB,GAAAE,EAAAvC,EAAAT,mBAEA,IAAA8D,GAAAxI,KAAA4G,IAAAe,EAAAxC,EAAA,GACAqC,IAAAgB,EAAAxI,KAAAyE,yBACA+D,EAAAb,EAAAxC,EAAA,GACAqD,EAAA,IACAhB,GAAAxH,KAAA4E,kBACA4C,IAAAgB,EAAA,GAAAxI,KAAAwE,sBACAgD,GAAA,GAGAA,GAAAI,EAAAzC,EAAAnF,KAAA2E,kBACA6C,GAAAzC,EAAAI,MAEAqC,GAAAxH,KAAAyH,kBAAAH,EAAA,SAEatC,IAAAhF,KAAAoD,kBAAA6B,IAAAjF,KAAAqD,cAAA2B,EAAAhF,KAAAqD,cAAA4B,EAAAjF,KAAAoD,oBACboE,EAAAxH,KAAAyH,kBAAAH,EAAAC,GAEA,OAAAC,IAcAiB,eAAA,SAAAnB,EAAApC,EAAAc,GACAA,IACAA,EAAAhG,KAAAiG,iBAAAf,IAGAoC,EAAAtH,KAAA0I,uBAAApB,EAAApC,EAAAc,EAGA,IAAAhB,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAC1BhC,MAAA4F,uBAAAV,EAAAF,EAAAC,EAEA,IAAAuC,GAAAxH,KAAAqH,iBAAAC,EAAApC,EAAAF,EAAAG,EAAAF,EAAAE,EAAAa,GAEA2C,EAAA3I,KAAAoH,SAAAI,EAAAtC,EAAAc,EAWA,OARA2C,GAAA3I,KAAAG,WAAAwI,EAAA,WACArB,EAAAtH,KAAAG,WAAAmH,EAAA,WAGAqB,IAAArB,IACAE,EAAAxH,KAAA4I,mBAAAtB,EAAAE,GAAA,IAAAtC,EAAAc,IAGAwB,GAcA5B,uBAAA,SAAAV,EAAAF,EAAAC,GACAM,KAAAsD,IAAA3D,EAAA,SACAF,EAAAG,EAAAnF,KAAA+B,iBACAkD,EAAAE,EAAAnF,KAAAgC,cACauD,KAAAsD,IAAA3D,EAAA,wBACbF,EAAAG,EAAAnF,KAAAkC,mBACA+C,EAAAE,EAAAnF,KAAAmC,gBACaoD,KAAAsD,IAAA3D,EAAA,SACbF,EAAAG,EAAAnF,KAAAqC,iBACA4C,EAAAE,EAAAnF,KAAAsC,cACaiD,KAAAsD,IAAA3D,EAAA,SACbF,EAAAG,EAAAnF,KAAAwC,iBACAyC,EAAAE,EAAAnF,KAAAyC,cACa8C,KAAAsD,IAAA3D,EAAA,yBACbF,EAAAG,EAAAnF,KAAA2C,mBACAsC,EAAAE,EAAAnF,KAAA4C,gBACa2C,KAAAsD,IAAA3D,EAAA,SACbF,EAAAG,EAAAnF,KAAA8C,iBACAmC,EAAAE,EAAAnF,KAAA+C,cACawC,KAAAsD,IAAA3D,EAAA,wBACbF,EAAAG,EAAAnF,KAAAiD,mBACAgC,EAAAE,EAAAnF,KAAAkD,gBACaqC,KAAAsD,IAAA3D,EAAA,UACbF,EAAAG,EAAAnF,KAAAoD,iBACA6B,EAAAE,EAAAnF,KAAAqD,eAeAyF,eAAA,SAAAC,EAAA/D,EAAAC,GACA,GAAAC,IAA8BC,EAAAnF,KAAA+B,kBAC9BqD,GAA+BD,EAAAnF,KAAAgC,aAG/B,OAFAhC,MAAAqF,iCAAAL,EAAAC,EAAAC,EAAAE,GAEAG,KAAAyD,MAAAD,EAAAxD,KAAAE,IAAA,MAAAP,EAAAC,EAAAC,EAAAD,EAAA,KAaA8D,aAAA,SAAAF,EAAA7D,GACA,GAAAF,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAG1B,OAFAhC,MAAA4F,uBAAAV,EAAAF,EAAAC,GAEAM,KAAAyD,MAAAD,EAAA/D,EAAAG,EAAAF,EAAAE,EAAAI,KAAAE,IAAA,WAYAyD,cAAA,SAAAH,GACA,MAAAA,GAAA,KAaAI,UAAA,SAAAJ,EAAA/C,GACA,GAAAG,GAAAZ,KAAAC,MAAAuD,GAAA,KAAAxD,KAAAE,IAAA,QACA2D,EAAAL,GAAA,KAAAxD,KAAAE,IAAA,OACAY,EAAAd,KAAAC,MAAA4D,GAAA,GAAA7D,KAAAE,IAAA,OACA2D,IAAA9D,SAAAC,KAAAC,MAAA,GAAAD,KAAAE,IAAA,OACA,IAAAa,GAAAf,KAAAC,MAAA4D,EAAA7D,KAAAE,IAAA,MACA2D,IAAA9D,SAAAC,KAAAE,IAAA,MACA,IAAA4D,GAAA9D,KAAAC,MAAA4D,EAAA,IACA,OAAApJ,MAAAwG,qBAAAL,EAAAE,EAAAC,EAAA+C,EAAArD,IAcAsD,YAAA,SAAAP,EAAA7D,EAAAc,GAIA,MAHAA,KACAA,EAAAhG,KAAAiG,iBAAAf,IAEAlF,KAAAoH,SAAApH,KAAAiJ,aAAAF,EAAA7D,KAAAc,IAeAX,iCAAA,SAAAL,EAAAC,EAAAC,EAAAE,GACAJ,IAAAhF,KAAA+B,kBAAAkD,IAAAjF,KAAAgC,cAAAgD,EAAAhF,KAAAgC,cAAAiD,EAAAjF,KAAA+B,kBACAmD,EAAAC,EAAAnF,KAAA+B,iBACAqD,EAAAD,EAAAnD,cACagD,IAAAhF,KAAAkC,oBAAA+C,IAAAjF,KAAAmC,gBAAA6C,EAAAhF,KAAAmC,gBAAA8C,EAAAjF,KAAAkC,oBACbgD,EAAAC,EAAAnF,KAAAkC,mBACAkD,EAAAD,EAAAnF,KAAAmC,gBACa6C,IAAAhF,KAAAqC,kBAAA4C,IAAAjF,KAAAsC,cAAA0C,EAAAhF,KAAAsC,cAAA2C,EAAAjF,KAAAqC,kBACb6C,EAAAC,EAAAnF,KAAAqC,iBACA+C,EAAAD,EAAAnF,KAAAsC,cACa0C,IAAAhF,KAAAwC,kBAAAyC,IAAAjF,KAAAyC,cAAAuC,EAAAhF,KAAAyC,cAAAwC,EAAAjF,KAAAwC,kBACb0C,EAAAC,EAAAnF,KAAAwC,iBACA4C,EAAAD,EAAAnF,KAAAyC,cACauC,IAAAhF,KAAA2C,oBAAAsC,IAAAjF,KAAA4C,gBAAAoC,EAAAhF,KAAA4C,gBAAAqC,EAAAjF,KAAA2C,oBACbuC,EAAAC,EAAAnF,KAAA2C,mBACAyC,EAAAD,EAAAnF,KAAA4C,gBACaoC,IAAAhF,KAAA8C,kBAAAmC,IAAAjF,KAAA+C,cAAAiC,EAAAhF,KAAA+C,cAAAkC,EAAAjF,KAAA8C,kBACboC,EAAAC,EAAAnF,KAAA8C,iBACAsC,EAAAD,EAAAnF,KAAA+C,cACaiC,IAAAhF,KAAAiD,oBAAAgC,IAAAjF,KAAAkD,gBAAA8B,EAAAhF,KAAAkD,gBAAA+B,EAAAjF,KAAAiD,oBACbiC,EAAAC,EAAAnF,KAAAiD,mBACAmC,EAAAD,EAAAnF,KAAAkD,iBACa8B,IAAAhF,KAAAoD,kBAAA6B,IAAAjF,KAAAqD,cAAA2B,EAAAhF,KAAAqD,cAAA4B,EAAAjF,KAAAoD,oBACb8B,EAAAC,EAAAnF,KAAAoD,iBACAgC,EAAAD,EAAAnF,KAAAqD,eAeAkG,eAAA,SAAAC,EAAAxE,EAAAC,GACAuE,GAAAjE,KAAAE,IAAA,KACA,IAAAP,IAA8BC,EAAAnF,KAAA+B,kBAC9BqD,GAA+BD,EAAAnF,KAAAgC,aAI/B,OAHAhC,MAAAqF,iCAAAL,EAAAC,EAAAC,EAAAE,GAGAG,KAAAyD,MAAAQ,EAAAtE,EAAAC,EAAAC,EAAAD,EAAAI,KAAAE,IAAA,QAaAgE,aAAA,SAAAD,EAAAtE,GAEA,GAAAsE,GAAA,MAGA,MAFAA,IAAA,MAEAxJ,KAAAyJ,aAAAD,EAAAtE,EAGAsE,IAAAjE,KAAAE,IAAA,KACA,IAAAT,IAAyBG,EAAAnF,KAAA+B,kBACzBkD,GAA0BE,EAAAnF,KAAAgC,aAK1B,OAJAhC,MAAA4F,uBAAAV,EAAAF,EAAAC,GAIAM,KAAAyD,MAAAQ,EAAAxE,EAAAG,EAAAF,EAAAE,EAAAI,KAAAE,IAAA,QAgBAe,qBAAA,SAAAkD,EAAAC,EAAAC,EAAAC,EAAA7D,GAWA,QAAA8D,GAAA7K,GACA,MAAAA,GAAA,OAAAA,IAXAyK,KAAA,GAAAA,EAAA,GAAAA,EACAC,KAAA,GAAAA,EAAA,GAAAA,EACAC,KAAA,GAAAA,EAAA,GAAAA,CAEA,IAAAG,GAAA,GAWA,OAVA/D,KACA+D,EAAA,KAGAL,EAAApE,SAAAC,KAAAC,MAAAkE,EAAA,KAKAI,EAAAJ,GAAA,IAAAI,EAAAH,GAAAI,EAAAD,EAAAF,GAAA,IAAAE,EAAAD,IAGAnB,uBAAA,SAAAsB,EAAA9E,EAAAc,GACA,GAAAgE,EAAA,CACA,GAAAN,GAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAI,EAAAD,EAAAE,MAAA,IAEA,IADAR,EAAApE,SAAA2E,EAAA,IACAA,EAAAE,QAAA,GAIA,GAHAR,EAAArE,SAAA2E,EAAA,IACAL,EAAAtE,SAAA2E,EAAA,IAEA3E,SAAA2E,EAAA,KAAA/E,EAAA,CAEA,GAAAkF,GAAA7E,KAAA8E,KAAAnF,GAAA,CACA+E,GAAA,GAAAG,EAAAE,eAIA,IAAAtE,EAAA,CAEA,GAAAuE,IAAA,+BACAC,GAAA,qBACAC,GAAA,UAEA,QAAAR,EAAA,KAAAM,EAAA1K,SAAAoK,EAAA,KAAAO,EAAA3K,SAAAoK,EAAA,MACA,GAAA/E,EAAA,GACA+E,EAAA,QAEAQ,EAAA5K,SAAAoK,EAAA,MACAA,EAAA,UAMAJ,EAAAvE,SAAA2E,EAAA,QACiB,CACjB,GAAAS,GAAAT,EAAA,GAAAC,MAAA,IAIA,IAHAP,EAAArE,SAAA2E,EAAA,IACAL,EAAAtE,SAAAoF,EAAA,IAEApF,SAAAoF,EAAA,KAAAxF,EAAA,CACA,GAAAyF,GAAApF,KAAA8E,KAAAnF,GAAA,CACAwF,GAAA,GAAAC,EAAAL,eAIA,IAAAtE,EAAA,CAEA,GAAA4E,IAAA,+BACAC,GAAA,qBACAC,GAAA,UAEA,QAAAJ,EAAA,KAAAE,EAAA/K,SAAAoK,EAAA,KAAAY,EAAAhL,SAAA6K,EAAA,MACA,GAAAxF,EAAA,GACAwF,EAAA,QAEAI,EAAAjL,SAAA6K,EAAA,MACAA,EAAA,UAOAb,EAAAvE,SAAAoF,EAAA,IAGA,MAAA1K,MAAAwG,qBAAAkD,EAAAC,EAAAC,EAAAC,EAAA7D,GAEA,qBAiBA4C,mBAAA,SAAAtB,EAAAE,EAAAuD,EAAAC,EAAA9F,EAAAc,GACA,GAAAiF,GAAA,CACAF,GACAE,EAAAzD,EAAAwD,GAEAC,EAAAzD,EAAAwD,EACAA,IAEA,IAAArC,GAAA3I,KAAAoH,SAAA6D,EAAA/F,EAAAc,EAOA,OAJA2C,GAAA3I,KAAAG,WAAAwI,EAAA,SACArB,EAAAtH,KAAAG,WAAAmH,EAAA,SAGAqB,IAAArB,EACAtH,KAAA4I,mBAAAtB,EAAAE,GAAAuD,EAAAC,EAAA9F,EAAAc,GAGAiF,GAYAhF,iBAAA,SAAAiF,GACA,eAAAA,KAAA,IAAAA,EAAA,IAEa,KAAAA,IAEA,KAAAA,IAEA,QAAAA,KAAA,IAAAA,EAAA,IAEA,KAAAA,IAEA,KAAAA,IAEA,QAAAA,KAAA,IAAAA,EAAA,QAkBbzD,kBAAA,SAAAH,EAAApC,GACA,GAAAiG,GAAA,EACA3D,EAAA,EAEAE,GAAyBvC,EAAA,GACzBwC,GAA2BxC,EAAA,GAC3ByC,GAA2BzC,EAAA,GAC3BJ,GAA0BI,EAAA,EAW1B,OATAnF,MAAA6H,gBAAAP,EAAAI,EAAAC,EAAAC,EAAA7C,EAAAG,GAEAiG,GAAA,GAAAzD,EAAAvC,EAAA,GACAgG,GAAA,GAAAxD,EAAAxC,EACAgG,GAAAvD,EAAAzC,EACAqC,GAAAzC,EAAAI,EAEAqC,GAAAlC,SAAAtF,KAAAyJ,aAAA0B,EAAAjG,KAmBA2C,gBAAA,SAAAP,EAAAI,EAAAC,EAAAC,EAAA7C,EAAAG,GACA,GAAAkG,GAAA9D,EAAA4C,MAAA,IAEA,IAAAkB,EAAAjB,QAAA,EACAzC,EAAAvC,EAAAG,SAAA8F,EAAA,IACAzD,EAAAxC,EAAAG,SAAA8F,EAAA,IACAxD,EAAAzC,EAAAG,SAAA8F,EAAA,IACArG,EAAAI,EAAAG,SAAA8F,EAAA,QACa,CACb,GAAAV,GAAAU,EAAA,GAAAlB,MAAA,IACAxC,GAAAvC,EAAAG,SAAA8F,EAAA,IACAzD,EAAAxC,EAAAG,SAAAoF,EAAA,IACA9C,EAAAzC,EAAAG,SAAAoF,EAAA,IACA3F,EAAAI,EAAAG,SAAA8F,EAAA,IAGA1D,EAAAvC,EAAAuC,EAAAvC,GAAA,GAAAuC,EAAAvC,EAAA,GAAAuC,EAAAvC,EACAwC,EAAAxC,EAAAwC,EAAAxC,GAAA,GAAAwC,EAAAxC,EAAA,GAAAwC,EAAAxC,EACAyC,EAAAzC,EAAAyC,EAAAzC,GAAA,GAAAyC,EAAAzC,EAAA,GAAAyC,EAAAzC,EACAJ,EAAAI,EAAAJ,EAAAI,GAAAI,KAAA8E,KAAAnF,GAAAH,EAAAI,EAAAG,SAAAC,KAAA8E,KAAAnF,IAAAH,EAAAI,GAIAkG,yBAAA,SAAAzB,EAAA0B,GACA,GAAA5B,GAAAnE,KAAAC,MAAAoE,EAAA,MACAD,EAAApE,KAAAC,OAAAoE,EAAA,KAAAF,GAAA,IACA6B,EAAAhG,KAAAC,MAAAoE,EAAA,GAAAD,EAAA,KAAAD,GACA8B,EAAAjG,KAAAC,MAAA,IAAAoE,EAAA,IAAArE,KAAAC,MAAAoE,GAEA4B,IAAA,MACAD,IACAC,GAAA,IAGA,IAAAC,GAAA/B,EAAA,OAAAA,IAAAY,WACAoB,EAAA/B,EAAA,OAAAA,IAAAW,WACAqB,EAAAJ,EAAA,OAAAA,IAAAjB,WACAsB,EAAAJ,EAAA,OAAAA,IAAAlB,UACA,OAAAmB,GAAA,IAAAC,EAAA,IAAAC,GAAA,cAAAL,EAAA,OAAAM,IAGAC,sBAAA,SAAAvE,GACA,GAAA8D,GAAA9D,EAAA4C,MAAA,KACAxC,GAAyBvC,EAAA,GACzBwC,GAA2BxC,EAAA,GAC3ByC,GAA2BzC,EAAA,GAC3B2G,GAAmC3G,EAAA,EAEnCuC,GAAAvC,EAAAG,SAAA8F,EAAA,IACAzD,EAAAxC,EAAAG,SAAA8F,EAAA,IACAxD,EAAAzC,EAAAG,SAAA8F,EAAA,QACAW,IAAAX,EAAA,KACAU,EAAA3G,EAAAG,SAAA8F,EAAA,IAEA,IAAAY,GAAA,CAOA,OANAA,IAAA,GAAAtE,EAAAvC,EAAA,GACA6G,GAAA,GAAArE,EAAAxC,EACA6G,GAAApE,EAAAzC,EACA6G,GAAAF,EAAA3G,EAAA,IACA6G,EAAA1G,UAAA0G,EAAAzG,KAAAE,IAAA,OAAAwG,QAAA,KAKA9L,WAAA,SAAA+L,EAAA9L,EAAAC,GACA,MAAA6L,GAAA/L,WAAAC,EAAAC,IAEAuG,IAAA,SAAAuF,EAAAC,GACA,MAAApM,MAAAqM,SAAAF,IAAAnM,KAAAqM,SAAAD,GAAAD,EAAAC,EAAA,MAEAC,SAAA,SAAAC,GACA,cAAAA,OAAAP,KAAAO,IAGAC,OAAAC,gBAAA,GAAAhM,GACA,GAAAA,KAEA+L,QAAAhM", "file": "mam-timecode-convert.min.js", "sourcesContent": ["/******/ (function(modules) { // webpackBootstrap\n/******/ \t// The module cache\n/******/ \tvar installedModules = {};\n/******/\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(installedModules[moduleId]) {\n/******/ \t\t\treturn installedModules[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = installedModules[moduleId] = {\n/******/ \t\t\ti: moduleId,\n/******/ \t\t\tl: false,\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/\n/******/ \t\t// Execute the module function\n/******/ \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n/******/\n/******/ \t\t// Flag the module as loaded\n/******/ \t\tmodule.l = true;\n/******/\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/\n/******/\n/******/ \t// expose the modules object (__webpack_modules__)\n/******/ \t__webpack_require__.m = modules;\n/******/\n/******/ \t// expose the module cache\n/******/ \t__webpack_require__.c = installedModules;\n/******/\n/******/ \t// define getter function for harmony exports\n/******/ \t__webpack_require__.d = function(exports, name, getter) {\n/******/ \t\tif(!__webpack_require__.o(exports, name)) {\n/******/ \t\t\tObject.defineProperty(exports, name, {\n/******/ \t\t\t\tconfigurable: false,\n/******/ \t\t\t\tenumerable: true,\n/******/ \t\t\t\tget: getter\n/******/ \t\t\t});\n/******/ \t\t}\n/******/ \t};\n/******/\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = function(module) {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\tfunction getDefault() { return module['default']; } :\n/******/ \t\t\tfunction getModuleExports() { return module; };\n/******/ \t\t__webpack_require__.d(getter, 'a', getter);\n/******/ \t\treturn getter;\n/******/ \t};\n/******/\n/******/ \t// Object.prototype.hasOwnProperty.call\n/******/ \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n/******/\n/******/ \t// __webpack_public_path__\n/******/ \t__webpack_require__.p = \"\";\n/******/\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(__webpack_require__.s = 0);\n/******/ })\n/************************************************************************/\n/******/ ([\n/* 0 */\n/***/ (function(module, exports) {\n\n/*!\r\n * TimeCode Convert v2.0.1 (http://www.sobey.com)\r\n * Copyright 2010-2016 Sobey, Inc. CDC\r\n * Licensed under the MIT license\r\n * Author: Liujianming (<EMAIL>)\r\n * Convert From C++(Sobey MPC) ->C#(Sobey MAM) -> Javascript\r\n */\r\n\r\n/**\r\n * Array Extended contains\r\n *\r\n */\r\nArray.prototype.S = String.fromCharCode(2);\r\nArray.prototype.contains = function (e) {\r\n    var r = new RegExp(this.S + e + this.S);\r\n    return (r.test(this.S + this.join(this.S) + this.S));\r\n};\r\n/**\r\n * String  Extended replaceAll\r\n *\r\n */\r\nString.prototype.replaceAll = function (s1, s2) {\r\n    return this.replace(new RegExp(s1, \"gm\"), s2);\r\n};\r\n\r\nvar TimeCodeConvert = (function () {\r\n    /** @namespace TimeCodeConvert */\r\n    /**\r\n     * 时码转换帮助类\r\n     *\r\n     * @public\r\n     * @class TimeCodeConvert.TimeCodeConvertHelper\r\n     */\r\n    var timeCodeConvertHelper = function () {\r\n    };\r\n\r\n    timeCodeConvertHelper.prototype = {\r\n        /**\r\n        * 桌面视频制式标准枚举定义\r\n        *\r\n        * @class TimeCodeConvert.MpcVideoStandard\r\n        */\r\n        MpcVideoStandard: {\r\n            mpcVideostandardUnknow: 0,\r\n            mpcVideostandardPal: 1,\r\n            mpcVideostandardNtsc2997: 2,\r\n            mpcVideostandardNtsc30: 4,\r\n            mpcVideostandardSecam: 8,\r\n            mpcVideostandard1920108050I: 16,\r\n            mpcVideostandard192010805994I: 32,\r\n            mpcVideostandard1920108060I: 64,\r\n            mpcVideostandard192010802398P: 128,\r\n            mpcVideostandard1920108024P: 256,\r\n            mpcVideostandard1920108025P: 512,\r\n            mpcVideostandard192010802997P: 1024,\r\n            mpcVideostandard1920108030P: 2048,\r\n            mpcVideostandard12807202398P: 4096,\r\n            mpcVideostandard128072024P: 8192,\r\n            mpcVideostandard128072050P: 16384,\r\n            mpcVideostandard12807205994P: 32768,\r\n            mpcVideostandard1440108050I: 65536,\r\n            mpcVideostandard144010805994I: 131072,\r\n            mpcVideostandard1440108060I: 262144\r\n        },\r\n\r\n        /**\r\n         * PAL field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        m_mpcStRate25: 50,\r\n        /**\r\n         * PAL frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 25\r\n         * @type number\r\n         */\r\n        MpcStFrameRate25: 25,\r\n        /**\r\n         * PAL scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale25: 1,\r\n        /**\r\n         * NTSC field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        m_mpcStRate2997: 60000,\r\n        /**\r\n         * NTSC frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate2997: 30000,\r\n        /**\r\n         * NTSC scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale2997: 1001,\r\n        /**\r\n         * 30-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        m_mpcStRate30: 60,\r\n        /**\r\n         * 30-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30\r\n         * @type number\r\n         */\r\n        MpcStFrameRate30: 30,\r\n        /**\r\n         * 30-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale30: 1,\r\n        /**\r\n         * 24-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 48\r\n         * @type number\r\n         */\r\n        m_mpcStRate24: 48,\r\n        /**\r\n         * 24-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24\r\n         * @type number\r\n         */\r\n        MpcStFrameRate24: 24,\r\n        /**\r\n         * 24-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale24: 1,\r\n        /**\r\n         * 2398-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 48000\r\n         * @type number\r\n         */\r\n        m_mpcStRate2398: 48000,\r\n        /**\r\n         * 2398-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate2398: 24000,\r\n        /**\r\n         * 2398-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale2398: 1001,\r\n        /**\r\n         * PAL field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        m_mpcStRate50: 50,\r\n        /**\r\n         * PAL frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        MpcStFrameRate50: 50,\r\n        /**\r\n         * PAL scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale50: 1,\r\n        /**\r\n         * NTSC field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        m_mpcStRate5994: 60000,\r\n        /**\r\n         * NTSC frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate5994: 60000,\r\n        /**\r\n         * NTSC scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale5994: 1001,\r\n        /**\r\n         * 60-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        m_mpcStRate60: 60,\r\n        /**\r\n         * 60-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        MpcStFrameRate60: 60,\r\n        /**\r\n         * 60-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale60: 1,\r\n        /**\r\n         * 25 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 25\r\n         * @type number\r\n         */\r\n        MpcFramesSecond25: 25,\r\n        /**\r\n         * 25 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1500\r\n         * @type number\r\n         */\r\n        MpcFramesMinute25: 1500,\r\n        /**\r\n         * 25 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 90000\r\n         * @type number\r\n         */\r\n        MpcFramesHour25: 90000,\r\n        /**\r\n         * 30 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1438\r\n         * @type number\r\n         */\r\n        MpcFramesMinute24Drop: 1438,\r\n        /**\r\n         * 30 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 14382\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes24Drop: 14382,\r\n        /**\r\n         * 30 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 86292\r\n         * @type number\r\n         */\r\n        MpcFramesHour24Drop: 86292,\r\n        /**\r\n         * 24 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24\r\n         * @type number\r\n         */\r\n        MpcFramesSecond24: 24,\r\n        /**\r\n         * 24 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1440\r\n         * @type number\r\n         */\r\n        MpcFramesMinute24: 1440,\r\n        /**\r\n         * 24 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 86400\r\n         * @type number\r\n         */\r\n        MpcFramesHour24: 86400,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30\r\n         * @type number\r\n         */\r\n        MpcFramesSecondNodrop30: 30,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1800\r\n         * @type number\r\n         */\r\n        MpcFramesMinuteNodrop30: 1800,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 108000\r\n         * @type number\r\n         */\r\n        MpcFramesHourNodrop30: 108000,\r\n        /**\r\n         * 30 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1798\r\n         * @type number\r\n         */\r\n        MpcFramesMinute30Drop: 1798,\r\n        /**\r\n         * 30 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 17982\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes30Drop: 17982,\r\n        /**\r\n         * 30 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 107892\r\n         * @type number\r\n         */\r\n        MpcFramesHour30Drop: 107892,\r\n        /**\r\n         * 50 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        MpcFramesSecond50: 50,\r\n        /**\r\n         * 50 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3000\r\n         * @type number\r\n         */\r\n        MpcFramesMinute50: 3000,\r\n        /**\r\n         * 50 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 180000\r\n         * @type number\r\n         */\r\n        MpcFramesHour50: 180000,\r\n        /**\r\n         * 60 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3596\r\n         * @type number\r\n         */\r\n        MpcFramesMinute60Drop: 3596,\r\n        /**\r\n         * 60 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 35964\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes60Drop: 35964,\r\n        /**\r\n         * 60 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 215784\r\n         * @type number\r\n         */\r\n        MpcFramesHour60Drop: 215784,\r\n        /**\r\n         * 60 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        MpcFramesSecond60: 60,\r\n        /**\r\n         * 60 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3600\r\n         * @type number\r\n         */\r\n        MpcFramesMinute60: 3600,\r\n        /**\r\n         * 60 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 216000\r\n         * @type number\r\n         */\r\n        MpcFramesHour60: 216000,\r\n        /**\r\n         * 帧转百纳秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    lFrame    帧\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    \r\n         * @return  {number}              百纳秒\r\n         */\r\n        frame2L100Ns$1: function (lFrame, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return parseInt((Math.floor(lFrame * Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v)));\r\n        },\r\n        /**\r\n         * 帧转百纳秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dFrame        帧\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  百纳秒\r\n         */\r\n        frame2L100Ns: function (dFrame, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return parseInt((Math.floor(dFrame * dScale.v * Math.pow(10.0, 7) / dRate.v)));\r\n        },\r\n        /**\r\n         * 帧转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    lFrame    帧\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    \r\n         * @return  {number}              秒\r\n         */\r\n        frame2Second$1: function (lFrame, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return (lFrame * dFrameScale.v / dFrameRate.v);\r\n        },\r\n        /**\r\n         * 帧转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dFrame        帧\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  秒\r\n         */\r\n        frame2Second: function (dFrame, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return (dFrame * dScale.v / dRate.v);\r\n        },\r\n        /**\r\n         * 帧转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     dFrame       帧\r\n         * @param   {number}     dRate        帧率\r\n         * @param   {number}     dScale       \r\n         * @param   {boolean}    dropFrame    \r\n         * @return  {string}                  时码字符串\r\n         */\r\n        frame2Tc$1: function (dFrame, dRate, dScale, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dRate);\r\n            }\r\n            var strTc = \"\";\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                var dHour = parseInt((Math.floor(dFrame / this.MpcFramesHour25)));\r\n                var dResidue = parseInt((Math.floor(dFrame % this.MpcFramesHour25)));\r\n                var dMin = parseInt((Math.floor(dResidue / this.MpcFramesMinute25)));\r\n                dResidue = dResidue % this.MpcFramesMinute25;\r\n                var dSec = parseInt((Math.floor(dResidue / this.MpcFramesSecond25)));\r\n                var dFra = parseInt((Math.floor(dResidue % this.MpcFramesSecond25)));\r\n                strTc = this.formatTimeCodeString(dHour, dMin, dSec, dFra, false);\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                if (dropFrame) {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour30Drop)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour30Drop)));\r\n                    var dMin1 = parseInt((Math.floor(10 * Math.floor(this.div(dResidue1, this.MpcFrames10Minutes30Drop)))));\r\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes30Drop;\r\n                    if (dResidue1 >= this.MpcFramesMinuteNodrop30) {\r\n                        dResidue1 -= this.MpcFramesMinuteNodrop30;\r\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute30Drop)));\r\n                        dResidue1 %= this.MpcFramesMinute30Drop;\r\n                        dResidue1 += 2;\r\n                    }\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\r\n                } else {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\r\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinuteNodrop30)));\r\n                    dResidue11 = dResidue11 % this.MpcFramesMinuteNodrop30;\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecondNodrop30)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecondNodrop30)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\r\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\r\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinuteNodrop30)));\r\n                dResidue1 = dResidue1 % this.MpcFramesMinuteNodrop30;\r\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\r\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\r\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\r\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\r\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\r\n                dResidue11 = dResidue11 % this.MpcFramesMinute24;\r\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\r\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\r\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                if (dropFrame) {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour24Drop)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour24Drop)));\r\n                    var dMin1 = parseInt((Math.floor(10 * (this.div(dResidue1, this.MpcFrames10Minutes24Drop)))));\r\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes24Drop;\r\n                    if (dResidue1 >= this.MpcFramesMinute24) {\r\n                        dResidue1 -= this.MpcFramesMinute24;\r\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute24Drop)));\r\n                        dResidue1 %= this.MpcFramesMinute24Drop;\r\n                        dResidue1 += 2;\r\n                    }\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond24)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond24)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\r\n                } else {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\r\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\r\n                    dResidue11 = dResidue11 % this.MpcFramesMinute24;\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour50)));\r\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour50)));\r\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute50)));\r\n                dResidue1 = dResidue1 % this.MpcFramesMinute50;\r\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond50)));\r\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond50)));\r\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                if (dropFrame) {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60Drop)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60Drop)));\r\n                    var dMin11 = parseInt((Math.floor(10 * (this.div(dResidue11, this.MpcFrames10Minutes60Drop)))));\r\n                    dResidue11 = dResidue11 % this.MpcFrames10Minutes60Drop;\r\n                    if (dResidue11 >= this.MpcFramesMinute60) {\r\n                        dResidue11 -= this.MpcFramesMinute60;\r\n                        dMin11 += 1 + parseInt(Math.floor(this.div(dResidue11, this.MpcFramesMinute60Drop)));\r\n                        dResidue11 %= this.MpcFramesMinute60Drop;\r\n                        dResidue11 += 4;\r\n                    }\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, true);\r\n                } else {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\r\n                    var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute60)));\r\n                    dResidue1 = dResidue1 % this.MpcFramesMinute60;\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond60)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond60)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\r\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\r\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute60)));\r\n                dResidue11 = dResidue11 % this.MpcFramesMinute60;\r\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\r\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\r\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n            }\r\n            return strTc;\r\n        },\r\n        /**\r\n         * 帧转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     dFrame        帧\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     是否丢帧\r\n         * @return  {string}                   时码字符串\r\n         */\r\n        frame2Tc: function (dFrame, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            var strTc = this.frame2Tc$1(dFrame, dRate.v, dScale.v, dropFrame);\r\n            return strTc;\r\n        },\r\n        /**\r\n         * 时码字符串转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode    时码\r\n         * @param   {number}     frameRate    帧率\r\n         * @param   {number}     dRate        \r\n         * @param   {number}     dScale       \r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {number}                  帧\r\n         */\r\n        timeCode2Frame$1: function (sTimeCode, frameRate, dRate, dScale, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(frameRate);\r\n            }\r\n            var ftcFrames = 0;\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                var lHour = { v: 0 };\r\n                var lMinute = { v: 0 };\r\n                var lSecond = { v: 0 };\r\n                var lFrame = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, frameRate);\r\n\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * this.MpcFramesHour30Drop;\r\n\r\n                    var lwReste = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste * this.MpcFrames10Minutes30Drop;\r\n                    lwReste = lMinute.v % 10;\r\n\r\n                    if (lwReste > 0) {\r\n                        ftcFrames += this.MpcFramesMinuteNodrop30;\r\n                        ftcFrames += (lwReste - 1) * this.MpcFramesMinute30Drop;\r\n                        ftcFrames -= 2;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecondNodrop30;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 30);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                ftcFrames = timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                var lHour1 = { v: 0 };\r\n                var lMinute1 = { v: 0 };\r\n                var lSecond1 = { v: 0 };\r\n                var lFrame1 = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour1, lMinute1, lSecond1, lFrame1, frameRate);\r\n\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * this.MpcFramesHour24;\r\n\r\n                    var lwReste1 = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste1 * this.MpcFrames10Minutes24Drop;\r\n                    lwReste1 = lMinute.v % 10;\r\n                    if (lwReste1 > 0) {\r\n                        ftcFrames += this.MpcFramesMinute60;\r\n                        ftcFrames += (lwReste1 - 1) * this.MpcFramesMinute24;\r\n                        ftcFrames -= 2;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecond24;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 24);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                var lHour11 = { v: 0 };\r\n                var lMinute11 = { v: 0 };\r\n                var lSecond11 = { v: 0 };\r\n                var lFrame11 = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour11, lMinute11, lSecond11, lFrame11, frameRate);\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * MpcFramesHour60Drop;\r\n\r\n                    var lwReste11 = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste11 * this.MpcFrames10Minutes60Drop;\r\n                    lwReste11 = lMinute.v % 10;\r\n                    if (lwReste11 > 0) {\r\n                        ftcFrames += this.MpcFramesMinute60;\r\n                        ftcFrames += (lwReste11 - 1) * this.MpcFramesMinute60Drop;\r\n                        ftcFrames -= 4;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecond60;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 60);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            }\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 时间字符串转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode     时码\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     是否是丢帧\r\n         * @return  {number}                   帧\r\n         */\r\n        timeCode2Frame: function (sTimeCode, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            //sTimeCode = FormatTimeCodeString(sTimeCode, dFrameRate, GetRateDropFrame(dFrameRate));\r\n            sTimeCode = this.formatTimeCodeString$1(sTimeCode, dFrameRate, dropFrame);\r\n\r\n\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            var ftcFrames = this.timeCode2Frame$1(sTimeCode, dFrameRate, dRate.v, dScale.v, dropFrame);\r\n\r\n            var newTc = this.frame2Tc(ftcFrames, dFrameRate, dropFrame);\r\n\r\n\r\n            newTc = this.replaceAll(newTc, \"\\\\.\", \":\");\r\n            sTimeCode = this.replaceAll(sTimeCode, \"\\\\.\", \":\");\r\n\r\n\r\n            if (newTc !== sTimeCode) {\r\n                ftcFrames = this.getFrameByTimeCode(sTimeCode, ftcFrames, true, 1, dFrameRate, dropFrame);\r\n            }\r\n\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 获取帧率和修正值\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}            dFrameRate    输入帧率\r\n         * @param   {System.Double&}    dRate         修正帧率\r\n         * @param   {System.Double&}    dScale        修正值\r\n         * @return  {void}                            \r\n         */\r\n        frameRate2RateAndScale: function (dFrameRate, dRate, dScale) {\r\n            if (Math.abs(dFrameRate - 25.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate25;\r\n                dScale.v = this.MpcStScale25;\r\n            } else if (Math.abs(dFrameRate - 29.970029970029969) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate2997;\r\n                dScale.v = this.MpcStScale2997;\r\n            } else if (Math.abs(dFrameRate - 30.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate30;\r\n                dScale.v = this.MpcStScale30;\r\n            } else if (Math.abs(dFrameRate - 24.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate24;\r\n                dScale.v = this.MpcStScale24;\r\n            } else if (Math.abs(dFrameRate - 23.976023976023978) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate2398;\r\n                dScale.v = this.MpcStScale2398;\r\n            } else if (Math.abs(dFrameRate - 50.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate50;\r\n                dScale.v = this.MpcStScale50;\r\n            } else if (Math.abs(dFrameRate - 59.940059940059939) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate5994;\r\n                dScale.v = this.MpcStScale5994;\r\n            } else if (Math.abs(dFrameRate - 60.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate60;\r\n                dScale.v = this.MpcStScale60;\r\n            }\r\n        },\r\n        /**\r\n         * 百纳秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns    百纳秒\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    修正值\r\n         * @return  {number}              帧\r\n         */\r\n        l100Ns2Frame$1: function (l100Ns, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return Math.round(l100Ns / Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v + 0.5);\r\n        },\r\n        /**\r\n         * 百纳秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns        百纳秒\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  帧\r\n         */\r\n        l100Ns2Frame: function (l100Ns, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return Math.round(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\r\n        },\r\n        /**\r\n         * 百纳秒转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns        百纳秒\r\n         * @return  {number}                  秒\r\n         */\r\n        l100Ns2Second: function (l100Ns) {\r\n            return l100Ns / 10000000;\r\n        },\r\n        /**\r\n         * 百纳秒转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     l100Ns       百纳秒\r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {string}                  时码字符串\r\n         */\r\n        l100Ns2Tc: function (l100Ns, dropFrame) {\r\n            var dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));\r\n            var llResidue = (l100Ns % (3600 * Math.pow(10.0, 7)));\r\n            var dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));\r\n            llResidue = llResidue % parseInt((Math.floor(60 * Math.pow(10.0, 7))));\r\n            var dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));\r\n            llResidue = llResidue % parseInt((Math.pow(10.0, 7)));\r\n            var dFraction = (Math.floor(llResidue / (100000)));\r\n            return this.formatTimeCodeString(dHour, dMin, dSec, dFraction, dropFrame);\r\n        },\r\n        /**\r\n         * 百纳秒转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     l100Ns        百纳秒\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     \r\n         * @return  {string}                   时码字符串\r\n         */\r\n        l100Ns2Tc$1: function (l100Ns, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            return this.frame2Tc(this.l100Ns2Frame(l100Ns, dFrameRate), dFrameRate, dropFrame);\r\n        },\r\n        /**\r\n         * 帧率修正\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}            dRate          帧率\r\n         * @param   {number}            dScale         修正值\r\n         * @param   {System.Double&}    dFrameRate     输出帧率\r\n         * @param   {System.Double&}    dFrameScale    输出修正值\r\n         * @return  {void}                             \r\n         */\r\n        rate2ScaleFrameRateAndFrameScale: function (dRate, dScale, dFrameRate, dFrameScale) {\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                dFrameRate.v = this.MpcStFrameRate25;\r\n                dFrameScale.v = MpcStScale25;\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                dFrameRate.v = this.MpcStFrameRate2997;\r\n                dFrameScale.v = this.MpcStScale2997;\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                dFrameRate.v = this.MpcStFrameRate30;\r\n                dFrameScale.v = this.MpcStScale30;\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                dFrameRate.v = this.MpcStFrameRate24;\r\n                dFrameScale.v = this.MpcStScale24;\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                dFrameRate.v = this.MpcStFrameRate2398;\r\n                dFrameScale.v = this.MpcStScale2398;\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                dFrameRate.v = this.MpcStFrameRate50;\r\n                dFrameScale.v = this.MpcStScale50;\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                dFrameRate.v = this.MpcStFrameRate5994;\r\n                dFrameScale.v = this.MpcStScale5994;\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                dFrameRate.v = this.MpcStFrameRate60;\r\n                dFrameScale.v = this.MpcStScale60;\r\n            }\r\n        },\r\n        /**\r\n         * 秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dbSec     秒数\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    修正值\r\n         * @return  {number}              帧数\r\n         */\r\n        second2Frame$1: function (dbSec, dRate, dScale) {\r\n            dbSec = dbSec * Math.pow(10.0, 7);\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            //return (long)( dbSec * dRate / dScale );\r\n            return Math.round(dbSec * dFrameRate.v / dFrameScale.v / Math.pow(10.0, 7));\r\n        },\r\n        /**\r\n         * 秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dbSec         秒数\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  帧数\r\n         */\r\n        second2Frame: function (dbSec, dFrameRate) {\r\n            //超过24的归零\r\n            if (dbSec >= 86400) {\r\n                dbSec = dbSec - 86400;\r\n\r\n                return this.second2Frame(dbSec, dFrameRate);\r\n            }\r\n\r\n            dbSec = dbSec * Math.pow(10.0, 7);\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            //return (long)( dbSec * dRate / dScale );\r\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\r\n            return Math.round(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7));\r\n        },\r\n        /**\r\n         * 格式化时码字符串\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     hours        小时数\r\n         * @param   {number}     minutes      分指数\r\n         * @param   {number}     seconds      秒数\r\n         * @param   {number}     frames       帧数\r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {string}                  格式化后的时码字符串\r\n         */\r\n        formatTimeCodeString: function (hours, minutes, seconds, frames, dropFrame) {\r\n            hours = hours >= 24 ? hours - 24 : hours;\r\n            minutes = minutes >= 60 ? minutes - 60 : minutes;\r\n            seconds = seconds >= 60 ? seconds - 60 : seconds;\r\n\r\n            var framesSeparator = \".\";\r\n            if (!dropFrame) {\r\n                framesSeparator = \":\";\r\n            }\r\n\r\n            hours = parseInt((Math.floor(hours % 24.0)));\r\n            function wrap(n) {\r\n                return ((n < 10) ? '0' + n : n);\r\n            }\r\n            //return string.format(\"{0:D2}:{1:D2}{4}{2:D2}:{3:D2}\", hours, minutes, seconds, frames, framesSeparator);\r\n            var smtp = (wrap(hours) + ':' + wrap(minutes) + framesSeparator + wrap(seconds) + ':' + wrap(frames));\r\n            return smtp;\r\n        },\r\n        formatTimeCodeString$1: function (timeCode, dFrameRate, dropFrame) {\r\n            if (timeCode) {\r\n                var hours = 0;\r\n                var minutes = 0;\r\n                var seconds = 0;\r\n                var frames = 0;\r\n                var ftcs = timeCode.split(\":\");\r\n                hours = parseInt(ftcs[0]);\r\n                if (ftcs.length >= 4) {\r\n                    minutes = parseInt(ftcs[1]);\r\n                    seconds = parseInt(ftcs[2]);\r\n\r\n                    if (parseInt(ftcs[3]) >= dFrameRate) {\r\n\r\n                        var showframeRate = Math.ceil(dFrameRate) - 1;\r\n                        ftcs[3] = showframeRate.toString();\r\n                    } else {\r\n\r\n                        //验证是否是丢帧时码 \r\n                        if (dropFrame) {\r\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧 5994强制设置为04帧\r\n                            var dropM = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\r\n                            var drop5994F = [\"00\", \"01\", \"02\", \"03\"];\r\n                            var dropF = [\"00\", \"01\"];\r\n\r\n                            if (ftcs[2] === \"00\" && !dropM.contains(ftcs[1]) && drop5994F.contains(ftcs[3])) {\r\n                                if (60.0 - dFrameRate < 0.1) {\r\n                                    ftcs[3] = \"04\";\r\n                                } else {\r\n                                    if (dropF.contains(ftcs[3])) {\r\n                                        ftcs[3] = \"02\";\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    frames = parseInt(ftcs[3]);\r\n                } else {\r\n                    var ftcssf = ftcs[2].split(\".\");\r\n                    minutes = parseInt(ftcs[1]);\r\n                    seconds = parseInt(ftcssf[0]);\r\n\r\n                    if (parseInt(ftcssf[1]) >= dFrameRate) {\r\n                        var showframeRate1 = Math.ceil(dFrameRate) - 1;\r\n                        ftcssf[1] = showframeRate1.toString();\r\n                    } else {\r\n\r\n                        //验证是否是丢帧时码 \r\n                        if (dropFrame) {\r\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧\r\n                            var dropM1 = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\r\n                            var drop5994F1 = [\"00\", \"01\", \"02\", \"03\"];\r\n                            var dropF1 = [\"00\", \"01\"];\r\n\r\n                            if (ftcssf[0] === \"00\" && !dropM1.contains(ftcs[1]) && drop5994F1.contains(ftcssf[1])) {\r\n                                if (60.0 - dFrameRate < 0.1) {\r\n                                    ftcssf[1] = \"04\";\r\n                                } else {\r\n                                    if (dropF1.contains(ftcssf[1])) {\r\n                                        ftcssf[1] = \"02\";\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                    }\r\n                    frames = parseInt(ftcssf[1]);\r\n                }\r\n\r\n                return this.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);\r\n            }\r\n            return \"--:--:--:--\";\r\n        },\r\n        /**\r\n         * 递归解决时码丢帧的问题\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode     \r\n         * @param   {number}     ftcFrames     \r\n         * @param   {boolean}    isAdded       是否加修正值 为false的时候 为减了修正值\r\n         * @param   {number}     corrValue     修正值\r\n         * @param   {number}     dFrameRate    \r\n         * @param   {boolean}    dropFrame     \r\n         * @return  {number}                   \r\n         */\r\n        getFrameByTimeCode: function (sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame) {\r\n            var ftcNewFrames = 0;\r\n            if (isAdded) {\r\n                ftcNewFrames = ftcFrames + corrValue;\r\n            } else {\r\n                ftcNewFrames = ftcFrames - corrValue;\r\n                corrValue++;\r\n            }\r\n            var newTc = this.frame2Tc(ftcNewFrames, dFrameRate, dropFrame);\r\n\r\n\r\n            newTc = this.replaceAll(newTc, \".\", \":\");\r\n            sTimeCode = this.replaceAll(sTimeCode, \".\", \":\");\r\n\r\n\r\n            if (newTc !== sTimeCode) {\r\n                return this.getFrameByTimeCode(sTimeCode, ftcFrames, !isAdded, corrValue, dFrameRate, dropFrame);\r\n            }\r\n\r\n            return ftcNewFrames;\r\n        },\r\n        /**\r\n         * 获取此帧率是否丢帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     rate    帧率\r\n         * @return  {boolean}            \r\n         */\r\n        getRateDropFrame: function (rate) {\r\n            if (rate === 23.98 || (rate < 24 && rate > 23)) {\r\n                return true;\r\n            } else if (rate === 24.0) {\r\n                return false;\r\n            } else if (rate === 25.0) {\r\n                return false;\r\n            } else if (rate === 29.97 || (rate < 30 && rate > 29)) {\r\n                return true;\r\n            } else if (rate === 30.0) {\r\n                return false;\r\n            } else if (rate === 50.0) {\r\n                return false;\r\n            } else if (rate === 59.94 || (rate < 60 && rate > 59)) {\r\n                return true;\r\n            } else if (rate === 60.0) {\r\n                return false;\r\n            }\r\n            return false;\r\n        },\r\n        /**\r\n         * 时间字符串转秒(未考虑丢帧的情况)\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}    sTimeCode     \r\n         * @param   {number}    dFrameRate    \r\n         * @return  {number}                  帧数\r\n         */\r\n        timeCode2NdfFrame: function (sTimeCode, dFrameRate) {\r\n            var ftcSeconds = 0;\r\n            var ftcFrames = 0;\r\n\r\n            var lHour = { v: 0 };\r\n            var lMinute = { v: 0 };\r\n            var lSecond = { v: 0 };\r\n            var lFrame = { v: 0 };\r\n\r\n            this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);\r\n\r\n            ftcSeconds += lHour.v * 60 * 60;\r\n            ftcSeconds += lMinute.v * 60;\r\n            ftcSeconds += lSecond.v;\r\n            ftcFrames += lFrame.v;\r\n            //这里以前有bug，因为second2Frame返回的是字符串，不转成int会变成字符串拼接了。。\r\n            ftcFrames += parseInt(this.second2Frame(ftcSeconds, dFrameRate));\r\n\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 时间字符串格式化\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}           sTimeCode     \r\n         * @param   {System.Int64&}    lHour         \r\n         * @param   {System.Int64&}    lMinute       \r\n         * @param   {System.Int64&}    lSecond       \r\n         * @param   {System.Int64&}    lFrame        \r\n         * @param   {number}           dFrameRate    \r\n         * @return  {void}                           帧数\r\n         */\r\n        timeCode2Format: function (sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate) {\r\n            var ftcCodes = sTimeCode.split(\":\");\r\n\r\n            if (ftcCodes.length >= 4) {\r\n                lHour.v = parseInt(ftcCodes[0]);\r\n                lMinute.v = parseInt(ftcCodes[1]);\r\n                lSecond.v = parseInt(ftcCodes[2]);\r\n                lFrame.v = parseInt(ftcCodes[3]);\r\n            } else {\r\n                var ftcssf = ftcCodes[1].split(\".\");\r\n                lHour.v = parseInt(ftcCodes[0]);\r\n                lMinute.v = parseInt(ftcssf[0]);\r\n                lSecond.v = parseInt(ftcssf[1]);\r\n                lFrame.v = parseInt(ftcCodes[2]);\r\n            }\r\n\r\n            lHour.v = lHour.v >= 24 ? lHour.v - 24 : lHour.v;\r\n            lMinute.v = lMinute.v >= 60 ? lMinute.v - 60 : lMinute.v;\r\n            lSecond.v = lSecond.v >= 60 ? lSecond.v - 60 : lSecond.v;\r\n            lFrame.v = lFrame.v >= Math.ceil(dFrameRate) ? lFrame.v - parseInt(Math.ceil(dFrameRate)) : lFrame.v;\r\n        },\r\n\r\n        //音频时间格式转换  2016-04-10 created by wangyu\r\n        SecondToTimeString_audio: function (seconds, opts) {\r\n            var hours = Math.floor(seconds / 3600);\r\n            var minutes = Math.floor((seconds - 3600 * hours) / 60);\r\n            var iseconds = Math.floor(seconds - (60 * minutes) - (3600 * hours));\r\n            var i10Milliseconds = Math.floor((seconds * 100 - Math.floor(seconds) * 100));\r\n\r\n            if (i10Milliseconds >= 100) {\r\n                iseconds++;\r\n                i10Milliseconds = i10Milliseconds - 100;\r\n            }\r\n\r\n            var hoursStr = hours < 10 ? \"0\" + hours : hours.toString();\r\n            var minutesStr = minutes < 10 ? \"0\" + minutes : minutes.toString();\r\n            var isecondsStr = iseconds < 10 ? \"0\" + iseconds : iseconds.toString();\r\n            var i10MillisecondsStr = i10Milliseconds < 10 ? \"0\" + i10Milliseconds : i10Milliseconds.toString();\r\n            return hoursStr + \":\" + minutesStr + \":\" + isecondsStr + (opts === 'exactTo_s' ? \"\" : (\":\" + i10MillisecondsStr));\r\n        },\r\n        //音频时码转百纳秒\r\n        timeCode2L100Ns_audio: function (sTimeCode) {\r\n            var ftcCodes = sTimeCode.split(\":\");\r\n            var lHour = { v: 0 };\r\n            var lMinute = { v: 0 };\r\n            var lSecond = { v: 0 };\r\n            var I10Milliseconds = { v: 0 };\r\n\r\n            lHour.v = parseInt(ftcCodes[0]);\r\n            lMinute.v = parseInt(ftcCodes[1]);\r\n            lSecond.v = parseInt(ftcCodes[2]);\r\n            if (ftcCodes[3] != undefined)\r\n                I10Milliseconds.v = parseInt(ftcCodes[3]);\r\n\r\n            var l100ns = 0;\r\n            l100ns += lHour.v * 60 * 60;\r\n            l100ns += lMinute.v * 60;\r\n            l100ns += lSecond.v\r\n            l100ns += I10Milliseconds.v / 100;\r\n            l100ns = parseInt((l100ns * Math.pow(10.0, 7)).toFixed(0));\r\n\r\n            return l100ns;\r\n        },\r\n\r\n        replaceAll: function (str, s1, s2) {\r\n            return str.replaceAll(s1, s2);\r\n        },\r\n        div: function (a, b) {\r\n            return this.hasValue(a) && this.hasValue(b) ? a / b : null;\r\n        },\r\n        hasValue: function (obj) {\r\n            return (obj !== null) && (obj !== undefined);\r\n        }\r\n    };\r\n    window.timecodeconvert = new timeCodeConvertHelper();\r\n    return new timeCodeConvertHelper();\r\n})();\r\nwindow.TimeCodeConvert = TimeCodeConvert;\n\n/***/ })\n/******/ ]);\n\n\n// WEBPACK FOOTER //\n// mam-timecode-convert.min.js", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, {\n \t\t\t\tconfigurable: false,\n \t\t\t\tenumerable: true,\n \t\t\t\tget: getter\n \t\t\t});\n \t\t}\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n\n\n\n// WEBPACK FOOTER //\n// webpack/bootstrap 69b94aa6aba7899e1ed8", "/*!\r\n * TimeCode Convert v2.0.1 (http://www.sobey.com)\r\n * Copyright 2010-2016 Sobey, Inc. CDC\r\n * Licensed under the MIT license\r\n * Author: <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)\r\n * Convert From C++(Sobey MPC) ->C#(Sobey MAM) -> Javascript\r\n */\r\n\r\n/**\r\n * Array Extended contains\r\n *\r\n */\r\nArray.prototype.S = String.fromCharCode(2);\r\nArray.prototype.contains = function (e) {\r\n    var r = new RegExp(this.S + e + this.S);\r\n    return (r.test(this.S + this.join(this.S) + this.S));\r\n};\r\n/**\r\n * String  Extended replaceAll\r\n *\r\n */\r\nString.prototype.replaceAll = function (s1, s2) {\r\n    return this.replace(new RegExp(s1, \"gm\"), s2);\r\n};\r\n\r\nvar TimeCodeConvert = (function () {\r\n    /** @namespace TimeCodeConvert */\r\n    /**\r\n     * 时码转换帮助类\r\n     *\r\n     * @public\r\n     * @class TimeCodeConvert.TimeCodeConvertHelper\r\n     */\r\n    var timeCodeConvertHelper = function () {\r\n    };\r\n\r\n    timeCodeConvertHelper.prototype = {\r\n        /**\r\n        * 桌面视频制式标准枚举定义\r\n        *\r\n        * @class TimeCodeConvert.MpcVideoStandard\r\n        */\r\n        MpcVideoStandard: {\r\n            mpcVideostandardUnknow: 0,\r\n            mpcVideostandardPal: 1,\r\n            mpcVideostandardNtsc2997: 2,\r\n            mpcVideostandardNtsc30: 4,\r\n            mpcVideostandardSecam: 8,\r\n            mpcVideostandard1920108050I: 16,\r\n            mpcVideostandard192010805994I: 32,\r\n            mpcVideostandard1920108060I: 64,\r\n            mpcVideostandard192010802398P: 128,\r\n            mpcVideostandard1920108024P: 256,\r\n            mpcVideostandard1920108025P: 512,\r\n            mpcVideostandard192010802997P: 1024,\r\n            mpcVideostandard1920108030P: 2048,\r\n            mpcVideostandard12807202398P: 4096,\r\n            mpcVideostandard128072024P: 8192,\r\n            mpcVideostandard128072050P: 16384,\r\n            mpcVideostandard12807205994P: 32768,\r\n            mpcVideostandard1440108050I: 65536,\r\n            mpcVideostandard144010805994I: 131072,\r\n            mpcVideostandard1440108060I: 262144\r\n        },\r\n\r\n        /**\r\n         * PAL field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        m_mpcStRate25: 50,\r\n        /**\r\n         * PAL frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 25\r\n         * @type number\r\n         */\r\n        MpcStFrameRate25: 25,\r\n        /**\r\n         * PAL scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale25: 1,\r\n        /**\r\n         * NTSC field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        m_mpcStRate2997: 60000,\r\n        /**\r\n         * NTSC frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate2997: 30000,\r\n        /**\r\n         * NTSC scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale2997: 1001,\r\n        /**\r\n         * 30-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        m_mpcStRate30: 60,\r\n        /**\r\n         * 30-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30\r\n         * @type number\r\n         */\r\n        MpcStFrameRate30: 30,\r\n        /**\r\n         * 30-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale30: 1,\r\n        /**\r\n         * 24-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 48\r\n         * @type number\r\n         */\r\n        m_mpcStRate24: 48,\r\n        /**\r\n         * 24-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24\r\n         * @type number\r\n         */\r\n        MpcStFrameRate24: 24,\r\n        /**\r\n         * 24-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale24: 1,\r\n        /**\r\n         * 2398-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 48000\r\n         * @type number\r\n         */\r\n        m_mpcStRate2398: 48000,\r\n        /**\r\n         * 2398-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate2398: 24000,\r\n        /**\r\n         * 2398-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale2398: 1001,\r\n        /**\r\n         * PAL field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        m_mpcStRate50: 50,\r\n        /**\r\n         * PAL frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        MpcStFrameRate50: 50,\r\n        /**\r\n         * PAL scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale50: 1,\r\n        /**\r\n         * NTSC field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        m_mpcStRate5994: 60000,\r\n        /**\r\n         * NTSC frame  frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60000\r\n         * @type number\r\n         */\r\n        MpcStFrameRate5994: 60000,\r\n        /**\r\n         * NTSC scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1001\r\n         * @type number\r\n         */\r\n        MpcStScale5994: 1001,\r\n        /**\r\n         * 60-F field frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        m_mpcStRate60: 60,\r\n        /**\r\n         * 60-F frame frequency\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        MpcStFrameRate60: 60,\r\n        /**\r\n         * 60-F scale\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1\r\n         * @type number\r\n         */\r\n        MpcStScale60: 1,\r\n        /**\r\n         * 25 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 25\r\n         * @type number\r\n         */\r\n        MpcFramesSecond25: 25,\r\n        /**\r\n         * 25 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1500\r\n         * @type number\r\n         */\r\n        MpcFramesMinute25: 1500,\r\n        /**\r\n         * 25 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 90000\r\n         * @type number\r\n         */\r\n        MpcFramesHour25: 90000,\r\n        /**\r\n         * 30 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1438\r\n         * @type number\r\n         */\r\n        MpcFramesMinute24Drop: 1438,\r\n        /**\r\n         * 30 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 14382\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes24Drop: 14382,\r\n        /**\r\n         * 30 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 86292\r\n         * @type number\r\n         */\r\n        MpcFramesHour24Drop: 86292,\r\n        /**\r\n         * 24 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 24\r\n         * @type number\r\n         */\r\n        MpcFramesSecond24: 24,\r\n        /**\r\n         * 24 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1440\r\n         * @type number\r\n         */\r\n        MpcFramesMinute24: 1440,\r\n        /**\r\n         * 24 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 86400\r\n         * @type number\r\n         */\r\n        MpcFramesHour24: 86400,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 30\r\n         * @type number\r\n         */\r\n        MpcFramesSecondNodrop30: 30,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1800\r\n         * @type number\r\n         */\r\n        MpcFramesMinuteNodrop30: 1800,\r\n        /**\r\n         * 30 NO_DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 108000\r\n         * @type number\r\n         */\r\n        MpcFramesHourNodrop30: 108000,\r\n        /**\r\n         * 30 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 1798\r\n         * @type number\r\n         */\r\n        MpcFramesMinute30Drop: 1798,\r\n        /**\r\n         * 30 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 17982\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes30Drop: 17982,\r\n        /**\r\n         * 30 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 107892\r\n         * @type number\r\n         */\r\n        MpcFramesHour30Drop: 107892,\r\n        /**\r\n         * 50 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 50\r\n         * @type number\r\n         */\r\n        MpcFramesSecond50: 50,\r\n        /**\r\n         * 50 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3000\r\n         * @type number\r\n         */\r\n        MpcFramesMinute50: 3000,\r\n        /**\r\n         * 50 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 180000\r\n         * @type number\r\n         */\r\n        MpcFramesHour50: 180000,\r\n        /**\r\n         * 60 DROP Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3596\r\n         * @type number\r\n         */\r\n        MpcFramesMinute60Drop: 3596,\r\n        /**\r\n         * 60 DROP Frame: frames per 10 minutes\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 35964\r\n         * @type number\r\n         */\r\n        MpcFrames10Minutes60Drop: 35964,\r\n        /**\r\n         * 60 DROP Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 215784\r\n         * @type number\r\n         */\r\n        MpcFramesHour60Drop: 215784,\r\n        /**\r\n         * 60 Frame: frames per second\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 60\r\n         * @type number\r\n         */\r\n        MpcFramesSecond60: 60,\r\n        /**\r\n         * 60 Frame: frames per minute\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 3600\r\n         * @type number\r\n         */\r\n        MpcFramesMinute60: 3600,\r\n        /**\r\n         * 60 Frame: frames per hour\r\n         *\r\n         * @static\r\n         * @private\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @constant\r\n         * @default 216000\r\n         * @type number\r\n         */\r\n        MpcFramesHour60: 216000,\r\n        /**\r\n         * 帧转百纳秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    lFrame    帧\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    \r\n         * @return  {number}              百纳秒\r\n         */\r\n        frame2L100Ns$1: function (lFrame, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return parseInt((Math.floor(lFrame * Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v)));\r\n        },\r\n        /**\r\n         * 帧转百纳秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dFrame        帧\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  百纳秒\r\n         */\r\n        frame2L100Ns: function (dFrame, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return parseInt((Math.floor(dFrame * dScale.v * Math.pow(10.0, 7) / dRate.v)));\r\n        },\r\n        /**\r\n         * 帧转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    lFrame    帧\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    \r\n         * @return  {number}              秒\r\n         */\r\n        frame2Second$1: function (lFrame, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return (lFrame * dFrameScale.v / dFrameRate.v);\r\n        },\r\n        /**\r\n         * 帧转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dFrame        帧\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  秒\r\n         */\r\n        frame2Second: function (dFrame, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return (dFrame * dScale.v / dRate.v);\r\n        },\r\n        /**\r\n         * 帧转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     dFrame       帧\r\n         * @param   {number}     dRate        帧率\r\n         * @param   {number}     dScale       \r\n         * @param   {boolean}    dropFrame    \r\n         * @return  {string}                  时码字符串\r\n         */\r\n        frame2Tc$1: function (dFrame, dRate, dScale, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dRate);\r\n            }\r\n            var strTc = \"\";\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                var dHour = parseInt((Math.floor(dFrame / this.MpcFramesHour25)));\r\n                var dResidue = parseInt((Math.floor(dFrame % this.MpcFramesHour25)));\r\n                var dMin = parseInt((Math.floor(dResidue / this.MpcFramesMinute25)));\r\n                dResidue = dResidue % this.MpcFramesMinute25;\r\n                var dSec = parseInt((Math.floor(dResidue / this.MpcFramesSecond25)));\r\n                var dFra = parseInt((Math.floor(dResidue % this.MpcFramesSecond25)));\r\n                strTc = this.formatTimeCodeString(dHour, dMin, dSec, dFra, false);\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                if (dropFrame) {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour30Drop)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour30Drop)));\r\n                    var dMin1 = parseInt((Math.floor(10 * Math.floor(this.div(dResidue1, this.MpcFrames10Minutes30Drop)))));\r\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes30Drop;\r\n                    if (dResidue1 >= this.MpcFramesMinuteNodrop30) {\r\n                        dResidue1 -= this.MpcFramesMinuteNodrop30;\r\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute30Drop)));\r\n                        dResidue1 %= this.MpcFramesMinute30Drop;\r\n                        dResidue1 += 2;\r\n                    }\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\r\n                } else {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\r\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinuteNodrop30)));\r\n                    dResidue11 = dResidue11 % this.MpcFramesMinuteNodrop30;\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecondNodrop30)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecondNodrop30)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHourNodrop30)));\r\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHourNodrop30)));\r\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinuteNodrop30)));\r\n                dResidue1 = dResidue1 % this.MpcFramesMinuteNodrop30;\r\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecondNodrop30)));\r\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecondNodrop30)));\r\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\r\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\r\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\r\n                dResidue11 = dResidue11 % this.MpcFramesMinute24;\r\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\r\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\r\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                if (dropFrame) {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour24Drop)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour24Drop)));\r\n                    var dMin1 = parseInt((Math.floor(10 * (this.div(dResidue1, this.MpcFrames10Minutes24Drop)))));\r\n                    dResidue1 = dResidue1 % this.MpcFrames10Minutes24Drop;\r\n                    if (dResidue1 >= this.MpcFramesMinute24) {\r\n                        dResidue1 -= this.MpcFramesMinute24;\r\n                        dMin1 += 1 + parseInt(Math.floor(this.div(dResidue1, this.MpcFramesMinute24Drop)));\r\n                        dResidue1 %= this.MpcFramesMinute24Drop;\r\n                        dResidue1 += 2;\r\n                    }\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond24)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond24)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, true);\r\n                } else {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour24)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour24)));\r\n                    var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute24)));\r\n                    dResidue11 = dResidue11 % this.MpcFramesMinute24;\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond24)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond24)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour50)));\r\n                var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour50)));\r\n                var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute50)));\r\n                dResidue1 = dResidue1 % this.MpcFramesMinute50;\r\n                var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond50)));\r\n                var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond50)));\r\n                strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                if (dropFrame) {\r\n                    var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60Drop)));\r\n                    var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60Drop)));\r\n                    var dMin11 = parseInt((Math.floor(10 * (this.div(dResidue11, this.MpcFrames10Minutes60Drop)))));\r\n                    dResidue11 = dResidue11 % this.MpcFrames10Minutes60Drop;\r\n                    if (dResidue11 >= this.MpcFramesMinute60) {\r\n                        dResidue11 -= this.MpcFramesMinute60;\r\n                        dMin11 += 1 + parseInt(Math.floor(this.div(dResidue11, this.MpcFramesMinute60Drop)));\r\n                        dResidue11 %= this.MpcFramesMinute60Drop;\r\n                        dResidue11 += 4;\r\n                    }\r\n                    var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\r\n                    var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\r\n                    strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, true);\r\n                } else {\r\n                    var dHour1 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\r\n                    var dResidue1 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\r\n                    var dMin1 = parseInt((Math.floor(dResidue1 / this.MpcFramesMinute60)));\r\n                    dResidue1 = dResidue1 % this.MpcFramesMinute60;\r\n                    var dSec1 = parseInt((Math.floor(dResidue1 / this.MpcFramesSecond60)));\r\n                    var dFra1 = parseInt((Math.floor(dResidue1 % this.MpcFramesSecond60)));\r\n                    strTc = this.formatTimeCodeString(dHour1, dMin1, dSec1, dFra1, false);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                var dHour11 = parseInt((Math.floor(dFrame / this.MpcFramesHour60)));\r\n                var dResidue11 = parseInt((Math.floor(dFrame % this.MpcFramesHour60)));\r\n                var dMin11 = parseInt((Math.floor(dResidue11 / this.MpcFramesMinute60)));\r\n                dResidue11 = dResidue11 % this.MpcFramesMinute60;\r\n                var dSec11 = parseInt((Math.floor(dResidue11 / this.MpcFramesSecond60)));\r\n                var dFra11 = parseInt((Math.floor(dResidue11 % this.MpcFramesSecond60)));\r\n                strTc = this.formatTimeCodeString(dHour11, dMin11, dSec11, dFra11, false);\r\n            }\r\n            return strTc;\r\n        },\r\n        /**\r\n         * 帧转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     dFrame        帧\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     是否丢帧\r\n         * @return  {string}                   时码字符串\r\n         */\r\n        frame2Tc: function (dFrame, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            var strTc = this.frame2Tc$1(dFrame, dRate.v, dScale.v, dropFrame);\r\n            return strTc;\r\n        },\r\n        /**\r\n         * 时码字符串转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode    时码\r\n         * @param   {number}     frameRate    帧率\r\n         * @param   {number}     dRate        \r\n         * @param   {number}     dScale       \r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {number}                  帧\r\n         */\r\n        timeCode2Frame$1: function (sTimeCode, frameRate, dRate, dScale, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(frameRate);\r\n            }\r\n            var ftcFrames = 0;\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                var lHour = { v: 0 };\r\n                var lMinute = { v: 0 };\r\n                var lSecond = { v: 0 };\r\n                var lFrame = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, frameRate);\r\n\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * this.MpcFramesHour30Drop;\r\n\r\n                    var lwReste = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste * this.MpcFrames10Minutes30Drop;\r\n                    lwReste = lMinute.v % 10;\r\n\r\n                    if (lwReste > 0) {\r\n                        ftcFrames += this.MpcFramesMinuteNodrop30;\r\n                        ftcFrames += (lwReste - 1) * this.MpcFramesMinute30Drop;\r\n                        ftcFrames -= 2;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecondNodrop30;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 30);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                ftcFrames = timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                var lHour1 = { v: 0 };\r\n                var lMinute1 = { v: 0 };\r\n                var lSecond1 = { v: 0 };\r\n                var lFrame1 = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour1, lMinute1, lSecond1, lFrame1, frameRate);\r\n\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * this.MpcFramesHour24;\r\n\r\n                    var lwReste1 = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste1 * this.MpcFrames10Minutes24Drop;\r\n                    lwReste1 = lMinute.v % 10;\r\n                    if (lwReste1 > 0) {\r\n                        ftcFrames += this.MpcFramesMinute60;\r\n                        ftcFrames += (lwReste1 - 1) * this.MpcFramesMinute24;\r\n                        ftcFrames -= 2;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecond24;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 24);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                var lHour11 = { v: 0 };\r\n                var lMinute11 = { v: 0 };\r\n                var lSecond11 = { v: 0 };\r\n                var lFrame11 = { v: 0 };\r\n\r\n                this.timeCode2Format(sTimeCode, lHour11, lMinute11, lSecond11, lFrame11, frameRate);\r\n                if (dropFrame) {\r\n                    ftcFrames += lHour.v * MpcFramesHour60Drop;\r\n\r\n                    var lwReste11 = this.div(lMinute.v, 10);\r\n                    ftcFrames += lwReste11 * this.MpcFrames10Minutes60Drop;\r\n                    lwReste11 = lMinute.v % 10;\r\n                    if (lwReste11 > 0) {\r\n                        ftcFrames += this.MpcFramesMinute60;\r\n                        ftcFrames += (lwReste11 - 1) * this.MpcFramesMinute60Drop;\r\n                        ftcFrames -= 4;\r\n                    }\r\n\r\n                    ftcFrames += lSecond.v * this.MpcFramesSecond60;\r\n                    ftcFrames += lFrame.v;\r\n                } else {\r\n                    ftcFrames = this.timeCode2NdfFrame(sTimeCode, 60);\r\n                }\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                ftcFrames = this.timeCode2NdfFrame(sTimeCode, frameRate);\r\n            }\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 时间字符串转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode     时码\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     是否是丢帧\r\n         * @return  {number}                   帧\r\n         */\r\n        timeCode2Frame: function (sTimeCode, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            //sTimeCode = FormatTimeCodeString(sTimeCode, dFrameRate, GetRateDropFrame(dFrameRate));\r\n            sTimeCode = this.formatTimeCodeString$1(sTimeCode, dFrameRate, dropFrame);\r\n\r\n\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            var ftcFrames = this.timeCode2Frame$1(sTimeCode, dFrameRate, dRate.v, dScale.v, dropFrame);\r\n\r\n            var newTc = this.frame2Tc(ftcFrames, dFrameRate, dropFrame);\r\n\r\n\r\n            newTc = this.replaceAll(newTc, \"\\\\.\", \":\");\r\n            sTimeCode = this.replaceAll(sTimeCode, \"\\\\.\", \":\");\r\n\r\n\r\n            if (newTc !== sTimeCode) {\r\n                ftcFrames = this.getFrameByTimeCode(sTimeCode, ftcFrames, true, 1, dFrameRate, dropFrame);\r\n            }\r\n\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 获取帧率和修正值\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}            dFrameRate    输入帧率\r\n         * @param   {System.Double&}    dRate         修正帧率\r\n         * @param   {System.Double&}    dScale        修正值\r\n         * @return  {void}                            \r\n         */\r\n        frameRate2RateAndScale: function (dFrameRate, dRate, dScale) {\r\n            if (Math.abs(dFrameRate - 25.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate25;\r\n                dScale.v = this.MpcStScale25;\r\n            } else if (Math.abs(dFrameRate - 29.970029970029969) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate2997;\r\n                dScale.v = this.MpcStScale2997;\r\n            } else if (Math.abs(dFrameRate - 30.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate30;\r\n                dScale.v = this.MpcStScale30;\r\n            } else if (Math.abs(dFrameRate - 24.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate24;\r\n                dScale.v = this.MpcStScale24;\r\n            } else if (Math.abs(dFrameRate - 23.976023976023978) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate2398;\r\n                dScale.v = this.MpcStScale2398;\r\n            } else if (Math.abs(dFrameRate - 50.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate50;\r\n                dScale.v = this.MpcStScale50;\r\n            } else if (Math.abs(dFrameRate - 59.940059940059939) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate5994;\r\n                dScale.v = this.MpcStScale5994;\r\n            } else if (Math.abs(dFrameRate - 60.0) < 0.01) {\r\n                dRate.v = this.MpcStFrameRate60;\r\n                dScale.v = this.MpcStScale60;\r\n            }\r\n        },\r\n        /**\r\n         * 百纳秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns    百纳秒\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    修正值\r\n         * @return  {number}              帧\r\n         */\r\n        l100Ns2Frame$1: function (l100Ns, dRate, dScale) {\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            return Math.round(l100Ns / Math.pow(10.0, 7) * dFrameRate.v / dFrameScale.v + 0.5);\r\n        },\r\n        /**\r\n         * 百纳秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns        百纳秒\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  帧\r\n         */\r\n        l100Ns2Frame: function (l100Ns, dFrameRate) {\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            return Math.round(l100Ns * dRate.v / dScale.v / Math.pow(10.0, 7) + 0.5);\r\n        },\r\n        /**\r\n         * 百纳秒转秒\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    l100Ns        百纳秒\r\n         * @return  {number}                  秒\r\n         */\r\n        l100Ns2Second: function (l100Ns) {\r\n            return l100Ns / 10000000;\r\n        },\r\n        /**\r\n         * 百纳秒转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     l100Ns       百纳秒\r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {string}                  时码字符串\r\n         */\r\n        l100Ns2Tc: function (l100Ns, dropFrame) {\r\n            var dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));\r\n            var llResidue = (l100Ns % (3600 * Math.pow(10.0, 7)));\r\n            var dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));\r\n            llResidue = llResidue % parseInt((Math.floor(60 * Math.pow(10.0, 7))));\r\n            var dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));\r\n            llResidue = llResidue % parseInt((Math.pow(10.0, 7)));\r\n            var dFraction = (Math.floor(llResidue / (100000)));\r\n            return this.formatTimeCodeString(dHour, dMin, dSec, dFraction, dropFrame);\r\n        },\r\n        /**\r\n         * 百纳秒转时码\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     l100Ns        百纳秒\r\n         * @param   {number}     dFrameRate    帧率\r\n         * @param   {boolean}    dropFrame     \r\n         * @return  {string}                   时码字符串\r\n         */\r\n        l100Ns2Tc$1: function (l100Ns, dFrameRate, dropFrame) {\r\n            if(!dropFrame) {\r\n                dropFrame =  this.getRateDropFrame(dFrameRate);\r\n            }\r\n            return this.frame2Tc(this.l100Ns2Frame(l100Ns, dFrameRate), dFrameRate, dropFrame);\r\n        },\r\n        /**\r\n         * 帧率修正\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}            dRate          帧率\r\n         * @param   {number}            dScale         修正值\r\n         * @param   {System.Double&}    dFrameRate     输出帧率\r\n         * @param   {System.Double&}    dFrameScale    输出修正值\r\n         * @return  {void}                             \r\n         */\r\n        rate2ScaleFrameRateAndFrameScale: function (dRate, dScale, dFrameRate, dFrameScale) {\r\n            if ((dRate === this.MpcStFrameRate25 && dScale === this.MpcStScale25) || (dRate * this.MpcStScale25 === dScale * this.MpcStFrameRate25)) {\r\n                dFrameRate.v = this.MpcStFrameRate25;\r\n                dFrameScale.v = MpcStScale25;\r\n            } else if ((dRate === this.MpcStFrameRate2997 && dScale === this.MpcStScale2997) || (dRate * this.MpcStScale2997 === dScale * this.MpcStFrameRate2997)) {\r\n                dFrameRate.v = this.MpcStFrameRate2997;\r\n                dFrameScale.v = this.MpcStScale2997;\r\n            } else if ((dRate === this.MpcStFrameRate30 && dScale === this.MpcStScale30) || (dRate * this.MpcStScale30 === dScale * this.MpcStFrameRate30)) {\r\n                dFrameRate.v = this.MpcStFrameRate30;\r\n                dFrameScale.v = this.MpcStScale30;\r\n            } else if ((dRate === this.MpcStFrameRate24 && dScale === this.MpcStScale24) || (dRate * this.MpcStScale24 === dScale * this.MpcStFrameRate24)) {\r\n                dFrameRate.v = this.MpcStFrameRate24;\r\n                dFrameScale.v = this.MpcStScale24;\r\n            } else if ((dRate === this.MpcStFrameRate2398 && dScale === this.MpcStScale2398) || (dRate * this.MpcStScale2398 === dScale * this.MpcStFrameRate2398)) {\r\n                dFrameRate.v = this.MpcStFrameRate2398;\r\n                dFrameScale.v = this.MpcStScale2398;\r\n            } else if ((dRate === this.MpcStFrameRate50 && dScale === this.MpcStScale50) || (dRate * this.MpcStScale50 === dScale * this.MpcStFrameRate50)) {\r\n                dFrameRate.v = this.MpcStFrameRate50;\r\n                dFrameScale.v = this.MpcStScale50;\r\n            } else if ((dRate === this.MpcStFrameRate5994 && dScale === this.MpcStScale5994) || (dRate * this.MpcStScale5994 === dScale * this.MpcStFrameRate5994)) {\r\n                dFrameRate.v = this.MpcStFrameRate5994;\r\n                dFrameScale.v = this.MpcStScale5994;\r\n            } else if ((dRate === this.MpcStFrameRate60 && dScale === this.MpcStScale60) || (dRate * this.MpcStScale60 === dScale * this.MpcStFrameRate60)) {\r\n                dFrameRate.v = this.MpcStFrameRate60;\r\n                dFrameScale.v = this.MpcStScale60;\r\n            }\r\n        },\r\n        /**\r\n         * 秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dbSec     秒数\r\n         * @param   {number}    dRate     帧率\r\n         * @param   {number}    dScale    修正值\r\n         * @return  {number}              帧数\r\n         */\r\n        second2Frame$1: function (dbSec, dRate, dScale) {\r\n            dbSec = dbSec * Math.pow(10.0, 7);\r\n            var dFrameRate = { v: this.MpcStFrameRate25 };\r\n            var dFrameScale = { v: this.MpcStScale25 };\r\n            this.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);\r\n\r\n            //return (long)( dbSec * dRate / dScale );\r\n            return Math.round(dbSec * dFrameRate.v / dFrameScale.v / Math.pow(10.0, 7));\r\n        },\r\n        /**\r\n         * 秒转帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}    dbSec         秒数\r\n         * @param   {number}    dFrameRate    帧率\r\n         * @return  {number}                  帧数\r\n         */\r\n        second2Frame: function (dbSec, dFrameRate) {\r\n            //超过24的归零\r\n            if (dbSec >= 86400) {\r\n                dbSec = dbSec - 86400;\r\n\r\n                return this.second2Frame(dbSec, dFrameRate);\r\n            }\r\n\r\n            dbSec = dbSec * Math.pow(10.0, 7);\r\n            var dRate = { v: this.MpcStFrameRate25 };\r\n            var dScale = { v: this.MpcStScale25 };\r\n            this.frameRate2RateAndScale(dFrameRate, dRate, dScale);\r\n\r\n            //return (long)( dbSec * dRate / dScale );\r\n            //return parseInt((Math.floor(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7))));\r\n            return Math.round(dbSec * dRate.v / dScale.v / Math.pow(10.0, 7));\r\n        },\r\n        /**\r\n         * 格式化时码字符串\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     hours        小时数\r\n         * @param   {number}     minutes      分指数\r\n         * @param   {number}     seconds      秒数\r\n         * @param   {number}     frames       帧数\r\n         * @param   {boolean}    dropFrame    是否是丢帧\r\n         * @return  {string}                  格式化后的时码字符串\r\n         */\r\n        formatTimeCodeString: function (hours, minutes, seconds, frames, dropFrame) {\r\n            hours = hours >= 24 ? hours - 24 : hours;\r\n            minutes = minutes >= 60 ? minutes - 60 : minutes;\r\n            seconds = seconds >= 60 ? seconds - 60 : seconds;\r\n\r\n            var framesSeparator = \".\";\r\n            if (!dropFrame) {\r\n                framesSeparator = \":\";\r\n            }\r\n\r\n            hours = parseInt((Math.floor(hours % 24.0)));\r\n            function wrap(n) {\r\n                return ((n < 10) ? '0' + n : n);\r\n            }\r\n            //return string.format(\"{0:D2}:{1:D2}{4}{2:D2}:{3:D2}\", hours, minutes, seconds, frames, framesSeparator);\r\n            var smtp = (wrap(hours) + ':' + wrap(minutes) + framesSeparator + wrap(seconds) + ':' + wrap(frames));\r\n            return smtp;\r\n        },\r\n        formatTimeCodeString$1: function (timeCode, dFrameRate, dropFrame) {\r\n            if (timeCode) {\r\n                var hours = 0;\r\n                var minutes = 0;\r\n                var seconds = 0;\r\n                var frames = 0;\r\n                var ftcs = timeCode.split(\":\");\r\n                hours = parseInt(ftcs[0]);\r\n                if (ftcs.length >= 4) {\r\n                    minutes = parseInt(ftcs[1]);\r\n                    seconds = parseInt(ftcs[2]);\r\n\r\n                    if (parseInt(ftcs[3]) >= dFrameRate) {\r\n\r\n                        var showframeRate = Math.ceil(dFrameRate) - 1;\r\n                        ftcs[3] = showframeRate.toString();\r\n                    } else {\r\n\r\n                        //验证是否是丢帧时码 \r\n                        if (dropFrame) {\r\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧 5994强制设置为04帧\r\n                            var dropM = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\r\n                            var drop5994F = [\"00\", \"01\", \"02\", \"03\"];\r\n                            var dropF = [\"00\", \"01\"];\r\n\r\n                            if (ftcs[2] === \"00\" && !dropM.contains(ftcs[1]) && drop5994F.contains(ftcs[3])) {\r\n                                if (60.0 - dFrameRate < 0.1) {\r\n                                    ftcs[3] = \"04\";\r\n                                } else {\r\n                                    if (dropF.contains(ftcs[3])) {\r\n                                        ftcs[3] = \"02\";\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                    frames = parseInt(ftcs[3]);\r\n                } else {\r\n                    var ftcssf = ftcs[2].split(\".\");\r\n                    minutes = parseInt(ftcs[1]);\r\n                    seconds = parseInt(ftcssf[0]);\r\n\r\n                    if (parseInt(ftcssf[1]) >= dFrameRate) {\r\n                        var showframeRate1 = Math.ceil(dFrameRate) - 1;\r\n                        ftcssf[1] = showframeRate1.toString();\r\n                    } else {\r\n\r\n                        //验证是否是丢帧时码 \r\n                        if (dropFrame) {\r\n                            //如果是丢帧帧率 分钟 除开 00 10 20 30 40 50 外所有的 00 01帧不存在 强制设置为02帧\r\n                            var dropM1 = [\"00\", \"10\", \"20\", \"30\", \"40\", \"50\"];\r\n                            var drop5994F1 = [\"00\", \"01\", \"02\", \"03\"];\r\n                            var dropF1 = [\"00\", \"01\"];\r\n\r\n                            if (ftcssf[0] === \"00\" && !dropM1.contains(ftcs[1]) && drop5994F1.contains(ftcssf[1])) {\r\n                                if (60.0 - dFrameRate < 0.1) {\r\n                                    ftcssf[1] = \"04\";\r\n                                } else {\r\n                                    if (dropF1.contains(ftcssf[1])) {\r\n                                        ftcssf[1] = \"02\";\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n\r\n                    }\r\n                    frames = parseInt(ftcssf[1]);\r\n                }\r\n\r\n                return this.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);\r\n            }\r\n            return \"--:--:--:--\";\r\n        },\r\n        /**\r\n         * 递归解决时码丢帧的问题\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}     sTimeCode     \r\n         * @param   {number}     ftcFrames     \r\n         * @param   {boolean}    isAdded       是否加修正值 为false的时候 为减了修正值\r\n         * @param   {number}     corrValue     修正值\r\n         * @param   {number}     dFrameRate    \r\n         * @param   {boolean}    dropFrame     \r\n         * @return  {number}                   \r\n         */\r\n        getFrameByTimeCode: function (sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame) {\r\n            var ftcNewFrames = 0;\r\n            if (isAdded) {\r\n                ftcNewFrames = ftcFrames + corrValue;\r\n            } else {\r\n                ftcNewFrames = ftcFrames - corrValue;\r\n                corrValue++;\r\n            }\r\n            var newTc = this.frame2Tc(ftcNewFrames, dFrameRate, dropFrame);\r\n\r\n\r\n            newTc = this.replaceAll(newTc, \".\", \":\");\r\n            sTimeCode = this.replaceAll(sTimeCode, \".\", \":\");\r\n\r\n\r\n            if (newTc !== sTimeCode) {\r\n                return this.getFrameByTimeCode(sTimeCode, ftcFrames, !isAdded, corrValue, dFrameRate, dropFrame);\r\n            }\r\n\r\n            return ftcNewFrames;\r\n        },\r\n        /**\r\n         * 获取此帧率是否丢帧\r\n         *\r\n         * @static\r\n         * @public\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {number}     rate    帧率\r\n         * @return  {boolean}            \r\n         */\r\n        getRateDropFrame: function (rate) {\r\n            if (rate === 23.98 || (rate < 24 && rate > 23)) {\r\n                return true;\r\n            } else if (rate === 24.0) {\r\n                return false;\r\n            } else if (rate === 25.0) {\r\n                return false;\r\n            } else if (rate === 29.97 || (rate < 30 && rate > 29)) {\r\n                return true;\r\n            } else if (rate === 30.0) {\r\n                return false;\r\n            } else if (rate === 50.0) {\r\n                return false;\r\n            } else if (rate === 59.94 || (rate < 60 && rate > 59)) {\r\n                return true;\r\n            } else if (rate === 60.0) {\r\n                return false;\r\n            }\r\n            return false;\r\n        },\r\n        /**\r\n         * 时间字符串转秒(未考虑丢帧的情况)\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}    sTimeCode     \r\n         * @param   {number}    dFrameRate    \r\n         * @return  {number}                  帧数\r\n         */\r\n        timeCode2NdfFrame: function (sTimeCode, dFrameRate) {\r\n            var ftcSeconds = 0;\r\n            var ftcFrames = 0;\r\n\r\n            var lHour = { v: 0 };\r\n            var lMinute = { v: 0 };\r\n            var lSecond = { v: 0 };\r\n            var lFrame = { v: 0 };\r\n\r\n            this.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);\r\n\r\n            ftcSeconds += lHour.v * 60 * 60;\r\n            ftcSeconds += lMinute.v * 60;\r\n            ftcSeconds += lSecond.v;\r\n            ftcFrames += lFrame.v;\r\n            //这里以前有bug，因为second2Frame返回的是字符串，不转成int会变成字符串拼接了。。\r\n            ftcFrames += parseInt(this.second2Frame(ftcSeconds, dFrameRate));\r\n\r\n            return ftcFrames;\r\n        },\r\n        /**\r\n         * 时间字符串格式化\r\n         *\r\n         * @static\r\n         * @private\r\n         * @this TimeCodeConvert.TimeCodeConvertHelper\r\n         * @memberof TimeCodeConvert.TimeCodeConvertHelper\r\n         * @param   {string}           sTimeCode     \r\n         * @param   {System.Int64&}    lHour         \r\n         * @param   {System.Int64&}    lMinute       \r\n         * @param   {System.Int64&}    lSecond       \r\n         * @param   {System.Int64&}    lFrame        \r\n         * @param   {number}           dFrameRate    \r\n         * @return  {void}                           帧数\r\n         */\r\n        timeCode2Format: function (sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate) {\r\n            var ftcCodes = sTimeCode.split(\":\");\r\n\r\n            if (ftcCodes.length >= 4) {\r\n                lHour.v = parseInt(ftcCodes[0]);\r\n                lMinute.v = parseInt(ftcCodes[1]);\r\n                lSecond.v = parseInt(ftcCodes[2]);\r\n                lFrame.v = parseInt(ftcCodes[3]);\r\n            } else {\r\n                var ftcssf = ftcCodes[1].split(\".\");\r\n                lHour.v = parseInt(ftcCodes[0]);\r\n                lMinute.v = parseInt(ftcssf[0]);\r\n                lSecond.v = parseInt(ftcssf[1]);\r\n                lFrame.v = parseInt(ftcCodes[2]);\r\n            }\r\n\r\n            lHour.v = lHour.v >= 24 ? lHour.v - 24 : lHour.v;\r\n            lMinute.v = lMinute.v >= 60 ? lMinute.v - 60 : lMinute.v;\r\n            lSecond.v = lSecond.v >= 60 ? lSecond.v - 60 : lSecond.v;\r\n            lFrame.v = lFrame.v >= Math.ceil(dFrameRate) ? lFrame.v - parseInt(Math.ceil(dFrameRate)) : lFrame.v;\r\n        },\r\n\r\n        //音频时间格式转换  2016-04-10 created by wangyu\r\n        SecondToTimeString_audio: function (seconds, opts) {\r\n            var hours = Math.floor(seconds / 3600);\r\n            var minutes = Math.floor((seconds - 3600 * hours) / 60);\r\n            var iseconds = Math.floor(seconds - (60 * minutes) - (3600 * hours));\r\n            var i10Milliseconds = Math.floor((seconds * 100 - Math.floor(seconds) * 100));\r\n\r\n            if (i10Milliseconds >= 100) {\r\n                iseconds++;\r\n                i10Milliseconds = i10Milliseconds - 100;\r\n            }\r\n\r\n            var hoursStr = hours < 10 ? \"0\" + hours : hours.toString();\r\n            var minutesStr = minutes < 10 ? \"0\" + minutes : minutes.toString();\r\n            var isecondsStr = iseconds < 10 ? \"0\" + iseconds : iseconds.toString();\r\n            var i10MillisecondsStr = i10Milliseconds < 10 ? \"0\" + i10Milliseconds : i10Milliseconds.toString();\r\n            return hoursStr + \":\" + minutesStr + \":\" + isecondsStr + (opts === 'exactTo_s' ? \"\" : (\":\" + i10MillisecondsStr));\r\n        },\r\n        //音频时码转百纳秒\r\n        timeCode2L100Ns_audio: function (sTimeCode) {\r\n            var ftcCodes = sTimeCode.split(\":\");\r\n            var lHour = { v: 0 };\r\n            var lMinute = { v: 0 };\r\n            var lSecond = { v: 0 };\r\n            var I10Milliseconds = { v: 0 };\r\n\r\n            lHour.v = parseInt(ftcCodes[0]);\r\n            lMinute.v = parseInt(ftcCodes[1]);\r\n            lSecond.v = parseInt(ftcCodes[2]);\r\n            if (ftcCodes[3] != undefined)\r\n                I10Milliseconds.v = parseInt(ftcCodes[3]);\r\n\r\n            var l100ns = 0;\r\n            l100ns += lHour.v * 60 * 60;\r\n            l100ns += lMinute.v * 60;\r\n            l100ns += lSecond.v\r\n            l100ns += I10Milliseconds.v / 100;\r\n            l100ns = parseInt((l100ns * Math.pow(10.0, 7)).toFixed(0));\r\n\r\n            return l100ns;\r\n        },\r\n\r\n        replaceAll: function (str, s1, s2) {\r\n            return str.replaceAll(s1, s2);\r\n        },\r\n        div: function (a, b) {\r\n            return this.hasValue(a) && this.hasValue(b) ? a / b : null;\r\n        },\r\n        hasValue: function (obj) {\r\n            return (obj !== null) && (obj !== undefined);\r\n        }\r\n    };\r\n    window.timecodeconvert = new timeCodeConvertHelper();\r\n    return new timeCodeConvertHelper();\r\n})();\r\nwindow.TimeCodeConvert = TimeCodeConvert;\n\n\n//////////////////\n// WEBPACK FOOTER\n// ./src/index.js\n// module id = 0\n// module chunks = 0"], "sourceRoot": ""}