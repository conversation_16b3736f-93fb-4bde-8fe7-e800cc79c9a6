import {
  InfoModel,
  InfoModelType,
} from '../models/infoModel';

export namespace Info {
  /*
   * @Author: <PERSON><PERSON><PERSON><PERSON> ran
   * @Date: 2023-02-15 10:41:19
   * @LastEditTime: 2023-09-26 10:52:48
   * @FilePath: \frontend\src\db\controllers\info.ts
   * @Description:
   */

  export async function getAllResult(key: string) {
    try {
      return await InfoModel.where('key')
        .equals(key)
        .toArray();
    } catch (error) {
      console.error(error);
      return [];
    }
  }

  export async function getAllResultCount(key: string) {
    try {
      return await InfoModel.where('key')
        .equals(key)
        .count();
    } catch (error) {
      console.error(error);
      return 0;
    }
  }

  /**
   * @description 存储数据在数据库中
   * @returns
   */
  export async function storeResult(result: InfoModelType) {
    try {
      const key = result.key;
      // 先获取所有的结果长度若大于10则删除最早的
      const count = await InfoModel.where('key')
        .equals(key)
        .count();
      let deleteId = -1;
      if (count > 10) {
        // 获取最早一条数据id然后删除
        const collection = await InfoModel.where('key')
          .equals(key)
          .first();
        if (collection) {
          await InfoModel.delete(collection.id!);
          deleteId = collection.id!;
        }
      }
      const createId = await InfoModel.put(result);
      return {
        createId,
        deleteId,
      };
    } catch (error) {
      console.error(error);
      return {
        createId: -1,
        deleteId: -1,
      };
    }
  }

  export async function deleteResult(id: number) {
    try {
      await InfoModel.delete(id);
      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  export async function updateResult(
    result: InfoModelType,
  ) {
    try {
      await InfoModel.update(result.id!, result);
      return true;
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  export async function getResultById(id: number) {
    try {
      return await InfoModel.get(id);
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }
}
