
/**
 * table (Enhancement 1.2v)
 * The tinymce-plugins is used to set up the conversion table (Enhancement)
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
eval(function(p,a,c,k,e,r){e=function(c){return(c<62?'':e(parseInt(c/62)))+((c=c%62)>35?String.fromCharCode(c+29):c.toString(36))};if('0'.replace(0,e)==0){while(c--)r[e(c)]=k[c];k=[function(e){return r[e]||e}];e=function(){return'([25-79b-dfhj-mo-tvwzA-Z]|[1-3]\\w)'};c=1};while(c--)if(k[c])p=p.replace(new RegExp('\\b'+e(c)+'\\b','g'),k[c]);return p}('(2(2A){\'use strict\';7 f=2B();7 13=2C();7 1n=2D();7 1o=2E();7 1T={J:1p,X:2F};7 z={1q:1q,1U:1U,1V:1V,Y:Y,1W:1W,A:{1n:1n,1o:1o,f:f,13:13,b:{}}};c(1X 2G!==\'1p\')2G.exports=z;K 2A.z=z;2 1q(6,b){b=b||{};2H(b);5 l.d(6).9(2(6){5 1r(6,b.v,2I)}).9(2J).9(2K).9(2L).9(2(h){5 2M(h,b.o||f.o(6),b.p||f.p(6))});2 2L(h){c(b.1s)h.j.backgroundColor=b.1s;c(b.o)h.j.o=b.o+\'L\';c(b.p)h.j.p=b.p+\'L\';c(b.j)Object.keys(b.j).M(2(1Y){h.j[1Y]=b.j[1Y]});5 h}}2 1W(6,b){5 19(6,b||{}).9(2(m){5 m.1Z(\'2d\').getImageData(0,0,f.o(6),f.p(6)).N})}2 1U(6,b){5 19(6,b||{}).9(2(m){5 m.1t()})}2 1V(6,b){b=b||{};5 19(6,b).9(2(m){5 m.1t(\'r/20\',b.quality||1.0)})}2 Y(6,b){5 19(6,b||{}).9(f.1u)}2 2H(b){c(1X(b.J)===\'1p\'){z.A.b.J=1T.J}K{z.A.b.J=b.J}c(1X(b.X)===\'1p\'){z.A.b.X=1T.X}K{z.A.b.X=b.X}}2 19(14,b){5 1q(14,b).9(f.1a).9(f.1v(21)).9(2(r){7 m=2N(14);m.1Z(\'2d\').drawImage(r,0,0);5 m});2 2N(14){7 m=O.1b(\'m\');m.o=b.o||f.o(14);m.p=b.p||f.p(14);c(b.1s){7 22=m.1Z(\'2d\');22.fillStyle=b.1s;22.fillRect(0,0,m.o,m.p)}5 m}}2 1r(6,v,2O){c(!2O&&v&&!v(6))5 l.d();5 l.d(6).9(2P).9(2(h){5 2Q(6,h,v)}).9(2(h){5 2R(6,h)});2 2P(6){c(6 P HTMLCanvasElement)5 f.1a(6.1t());5 6.1r(2F)}2 2Q(F,h,v){7 1c=F.2S;c(1c.Q===0)5 l.d(h);5 2T(h,f.G(1c),v).9(2(){5 h});2 2T(2U,1c,v){7 C=l.d();1c.M(2(1w){C=C.9(2(){5 1r(1w,v)}).9(2(24){c(24)2U.Z(24)})});5 C}}2 2R(F,h){c(!(h P 2V))5 h;5 l.d().9(2W).9(2X).9(2Y).9(2Z).9(2(){5 h});2 2W(){30(1d.25(F),h.j);2 30(R,1e){c(R.D)1e.D=R.D;K 31(R,1e);2 31(R,1e){f.G(R).M(2(S){1e.26(S,R.T(S),R.27(S))})}}}2 2X(){[\':before\',\':after\'].M(2(s){32(s)});2 32(s){7 j=1d.25(F,s);7 w=j.T(\'w\');c(w===\'\'||w===\'none\')5;7 10=f.1x();h.10=h.10+\' \'+10;7 28=O.1b(\'j\');28.Z(33(10,s,j));h.Z(28);2 33(10,s,j){7 34=\'.\'+10+\':\'+s;7 D=j.D?35(j):37(j);5 O.38(34+\'{\'+D+\'}\');2 35(j){7 w=j.T(\'w\');5 j.D+\' w: \'+w+\';\'}2 37(j){5 f.G(j).1y(39).3a(\'; \')+\';\';2 39(S){5 S+\': \'+j.T(S)+(j.27(S)?\' !important\':\'\')}}}}}2 2Y(){c(F P HTMLTextAreaElement)h.innerHTML=F.U;c(F P HTMLInputElement)h.29("U",F.U)}2 2Z(){c(!(h P SVGElement))5;h.29(\'2a\',\'2b://2c.w3.2f/3b/H\');c(!(h P SVGRectElement))5;[\'o\',\'p\'].M(2(2g){7 U=h.getAttribute(2g);c(!U)5;h.j.26(2g,U)})}}}2 2J(6){5 1n.1z().9(2(D){7 2h=O.1b(\'j\');6.Z(2h);2h.Z(O.38(D));5 6})}2 2K(6){5 1o.E(6).9(2(){5 6})}2 2M(6,o,p){5 l.d(6).9(2(6){6.29(\'2a\',\'2b://2c.w3.2f/1999/2i\');5 t XMLSerializer().serializeToString(6)}).9(f.1A).9(2(2i){5\'<1B x="0" y="0" o="21%" p="21%">\'+2i+\'</1B>\'}).9(2(1B){5\'<H 2a="2b://2c.w3.2f/3b/H" o="\'+o+\'" p="\'+p+\'">\'+1B+\'</H>\'}).9(2(H){5\'N:r/H+3c;charset=utf-8,\'+H})}2 2B(){5{1C:1C,1D:1D,1f:1f,1g:1g,1h:1h,1u:1u,1E:1E,1i:1i,1x:1x(),1v:1v,G:G,1A:1A,1a:1a,o:o,p:p};2 3d(){7 2j=\'2k/3e-3f\';7 2l=\'r/20\';5{\'3f\':2j,\'woff2\':2j,\'ttf\':\'2k/3e-truetype\',\'eot\':\'2k/vnd.ms-fontobject\',\'2m\':\'r/2m\',\'jpg\':2l,\'20\':2l,\'3g\':\'r/3g\',\'3h\':\'r/3h\',\'H\':\'r/H+3c\'}}2 1D(k){7 15=/\\.([^\\.\\/]*?)$/g.3i(k);c(15)5 15[1];K 5\'\'}2 1f(k){7 3j=1D(k).toLowerCase();5 3d()[3j]||\'\'}2 1h(k){5 k.3k(/^(N:)/)!==-1}2 Y(m){5 t l(2(d){7 2n=1d.atob(m.1t().11(\',\')[1]);7 Q=2n.Q;7 2o=t Uint8Array(Q);3l(7 i=0;i<Q;i++)2o[i]=2n.charCodeAt(i);d(t Blob([2o],{1G:\'r/2m\'}))})}2 1u(m){c(m.Y)5 t l(2(d){m.Y(d)});5 Y(m)}2 1E(k,I){7 1j=O.implementation.createHTMLDocument();7 1H=1j.1b(\'1H\');1j.head.Z(1H);7 a=1j.1b(\'a\');1j.body.Z(a);1H.1k=I;a.1k=k;5 a.1k}2 1x(){7 3m=0;5 2(){5\'u\'+3n()+3m++;2 3n(){5(\'0000\'+(3o.random()*3o.pow(36,4)<<0).3p(36)).slice(-4)}}}2 1a(3q){5 t l(2(d,1I){7 r=t Image();r.3r=2(){d(r)};r.3s=1I;r.V=3q})}2 1i(k){7 2p=30000;c(z.A.b.X){k+=((/\\?/).test(k)?"&":"?")+(t Date()).getTime()}5 t l(2(d){7 B=t XMLHttpRequest();B.onreadystatechange=C;B.ontimeout=1J;B.responseType=\'blob\';B.1J=2p;B.open(\'GET\',k,2I);B.send();7 16;c(z.A.b.J){7 11=z.A.b.J.11(/,/);c(11&&11[1]){16=11[1]}}2 C(){c(B.readyState!==4)5;c(B.2q!==200){c(16){d(16)}K{2r(\'cannot fetch 3t: \'+k+\', 2q: \'+B.2q)}5}7 1K=t FileReader();1K.onloadend=2(){7 w=1K.1L.11(/,/)[1];d(w)};1K.readAsDataURL(B.response)}2 1J(){c(16){d(16)}K{2r(\'1J of \'+2p+\'ms occured 2s fetching 3t: \'+k)}}2 2r(3u){3v.error(3u);d(\'\')}})}2 1g(w,1G){5\'N:\'+1G+\';base64,\'+w}2 1C(q){5 q.1l(/([.*+?^${}()|\\[\\]\\/\\\\])/g,\'\\\\$1\')}2 1v(ms){5 2(3w){5 t l(2(d){setTimeout(2(){d(3w)},ms)})}}2 G(2t){7 2u=[];7 Q=2t.Q;3l(7 i=0;i<Q;i++)2u.2v(2t[i]);5 2u}2 1A(q){5 q.1l(/#/g,\'%23\').1l(/\\n/g,\'%0A\')}2 o(6){7 3x=L(6,\'1M-left-o\');7 3y=L(6,\'1M-right-o\');5 6.scrollWidth+3x+3y}2 p(6){7 3z=L(6,\'1M-top-o\');7 3A=L(6,\'1M-bottom-o\');5 6.scrollHeight+3z+3A}2 L(6,3B){7 U=1d.25(6).T(3B);5 parseFloat(U.1l(\'L\',\'\'))}}2 2C(){7 2w=/k\\([\'"]?([^\'"]+?)[\'"]?\\)/g;5{E:E,1m:1m,A:{1N:1N,W:W}};2 1m(q){5 q.3k(2w)!==-1}2 1N(q){7 1L=[];7 15;2s((15=2w.3i(q))!==null){1L.2v(15[1])}5 1L.v(2(k){5!f.1h(k)})}2 W(q,k,I,17){5 l.d(k).9(2(k){5 I?f.1E(k,I):k}).9(17||f.1i).9(2(N){5 f.1g(N,f.1f(k))}).9(2(1O){5 q.1l(3C(k),\'$1\'+1O+\'$3\')});2 3C(k){5 t RegExp(\'(k\\\\([\\\'"]?)(\'+f.1C(k)+\')([\\\'"]?\\\\))\',\'g\')}}2 E(q,I,17){c(3D())5 l.d(q);5 l.d(q).9(1N).9(2(3E){7 C=l.d(q);3E.M(2(k){C=C.9(2(q){5 W(q,k,I,17)})});5 C});2 3D(){5!1m(q)}}}2 2D(){5{1z:1z,A:{1P:1P}};2 1z(){5 1P(O).9(2(3F){5 l.3G(3F.1y(2(3H){5 3H.d()}))}).9(2(3I){5 3I.3a(\'\\n\')})}2 1P(){5 l.d(f.G(O.2x)).9(3J).9(3K).9(2(2y){5 2y.1y(3L)});2 3K(12){5 12.v(2(1Q){5 1Q.1G===CSSRule.FONT_FACE_RULE}).v(2(1Q){5 13.1m(1Q.j.T(\'V\'))})}2 3J(2x){7 12=[];2x.M(2(2z){try{f.G(2z.12||[]).M(12.2v.bind(12))}catch(e){3v.log(\'Error 2s reading CSS 2y from \'+2z.1k,e.3p())}});5 12}2 3L(1R){5{d:2 d(){7 I=(1R.parentStyleSheet||{}).1k;5 13.E(1R.D,I)},V:2(){5 1R.j.T(\'V\')}}}}}2 2E(){5{E:E,A:{1S:1S}};2 1S(s){5{W:W};2 W(17){c(f.1h(s.V))5 l.d();5 l.d(s.V).9(17||f.1i).9(2(N){5 f.1g(N,f.1f(s.V))}).9(2(1O){5 t l(2(d,1I){s.3r=d;s.3s=1I;s.V=1O})})}}2 E(6){c(!(6 P 2V))5 l.d(6);5 3M(6).9(2(){c(6 P HTMLImageElement)5 1S(6).W();K 5 l.3G(f.G(6.2S).1y(2(1w){5 E(1w)}))});2 3M(6){7 18=6.j.T(\'18\');c(!18)5 l.d(6);5 13.E(18).9(2(3N){6.j.26(\'18\',3N,6.j.27(\'18\'))}).9(2(){5 6})}}}})(1d);',[],236,'||function|||return|node|var||then||options|if|resolve||util||clone||style|url|Promise|canvas||width|height|string|image|element|new||filter|content|||domtoimage|impl|request|done|cssText|inlineAll|original|asArray|svg|baseUrl|imagePlaceholder|else|px|forEach|data|document|instanceof|length|source|name|getPropertyValue|value|src|inline|cacheBust|toBlob|appendChild|className|split|cssRules|inliner|domNode|match|placeholder|get|background|draw|makeImage|createElement|children|window|target|mimeType|dataAsUrl|isDataUrl|getAndEncode|doc|href|replace|shouldProcess|fontFaces|images|undefined|toSvg|cloneNode|bgcolor|toDataURL|canvasToBlob|delay|child|uid|map|resolveAll|escapeXhtml|foreignObject|escape|parseExtension|resolveUrl||type|base|reject|timeout|encoder|result|border|readUrls|dataUrl|readAll|rule|webFontRule|newImage|defaultOptions|toPng|toJpeg|toPixelData|typeof|property|getContext|jpeg|100|ctx||childClone|getComputedStyle|setProperty|getPropertyPriority|styleElement|setAttribute|xmlns|http|www|||org|attribute|styleNode|xhtml|WOFF|application|JPEG|png|binaryString|binaryArray|TIMEOUT|status|fail|while|arrayLike|array|push|URL_REGEX|styleSheets|rules|sheet|global|newUtil|newInliner|newFontFaces|newImages|false|module|copyOptions|true|embedFonts|inlineImages|applyOptions|makeSvgDataUri|newCanvas|root|makeNodeCopy|cloneChildren|processClone|childNodes|cloneChildrenInOrder|parent|Element|cloneStyle|clonePseudoElements|copyUserInput|fixSvg|copyStyle|copyProperties|clonePseudoElement|formatPseudoElementStyle|selector|formatCssText||formatCssProperties|createTextNode|formatProperty|join|2000|xml|mimes|font|woff|gif|tiff|exec|extension|search|for|index|fourRandomChars|Math|toString|uri|onload|onerror|resource|message|console|arg|leftBorder|rightBorder|topBorder|bottomBorder|styleProperty|urlAsRegex|nothingToInline|urls|webFonts|all|webFont|cssStrings|getCssRules|selectWebFontRules|newWebFont|inlineBackground|inlined'.split('|'),0,{}));
(function(G,Ch){function F(a){for(var b=[],c=1;c<arguments.length;c++)b[c-1]=arguments[c];return function(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];d=b.concat(d);return a.apply(null,d)}}function Ge(a,b,c,d,e){return a(c,d)?l.some(c):Sa(e)&&e(c)?l.none():b(c,d,e)}function $b(){return{up:m({selector:Ta,closest:M,predicate:xb,all:He}),down:m({selector:bd,predicate:Ie}),styles:m({get:va,getRaw:wa,set:N,remove:xa}),attrs:m({get:ma,set:Y,remove:I,copyTo:function(a,b){a=Je(a);yb(b,a)}}),
insert:m({before:ac,after:Ha,afterAll:Dh,append:K,appendAll:Ua,prepend:cd,wrap:Eh}),remove:m({unwrap:Fh,remove:ba}),create:m({nu:u.fromTag,clone:function(a){return u.fromDom(a.dom().cloneNode(!1))},text:u.fromText}),query:m({comparePosition:function(a,b){return a.dom().compareDocumentPosition(b.dom())},prevSibling:Ke,nextSibling:bc}),property:m({children:W,name:D,parent:ya,document:function(a){return a.dom().ownerDocument},isText:Ia,isComment:Gh,isElement:Va,getText:cc,setText:Hh,isBoundary:function(a){return Va(a)?
"body"===D(a)?!0:P(Ih,D(a)):!1},isEmptyTag:function(a){return Va(a)?P(["br","img","hr","input"],D(a)):!1},isNonEditable:function(a){return Va(a)&&"false"===ma(a,"contenteditable")}}),eq:B,is:Jh}}function Le(a,b){var c=function(e){var f=b(e);return 0>=f||null===f?(e=va(e,a),parseFloat(e)||0):f},d=function(e,f){return za(f,function(g,h){h=va(e,h);h=void 0===h?0:parseInt(h,10);return isNaN(h)?g:g+h},0)};return{set:function(e,f){if(!dd(f)&&!f.match(/^[0-9]+$/))throw Error(a+".set accepts only positive integer values. Value was "+
f);e=e.dom();fb(e)&&(e.style[a]=f+"px")},get:c,getOuter:c,aggregate:d,max:function(e,f,g){e=d(e,g);return f>e?f-e:0}}}function Kh(){var a=l.none(),b=function(d,e){var f=a.map(function(g){return d.compare(g,e)});a=l.some(e);return f},c=gb({move:ia(["info"])});return{onEvent:function(d,e){e.extract(d).each(function(f){b(e,f).each(function(g){c.trigger.move(g)})})},reset:function(){a=l.none()},events:c.registry}}function Lh(){var a={onEvent:y,reset:y},b=Kh(),c=a;return{on:function(){c.reset();c=b},off:function(){c.reset();
c=a},isOn:function(){return c===b},onEvent:function(d,e){c.onEvent(d,e)},events:b.events}}function Mh(a,b,c,d){var e=l.none(),f=function(){e=l.none()};return{mousedown:function(g){d.clear(b);e=Me(g.target(),c)},mouseover:function(g){e.each(function(h){d.clearBeforeUpdate(b);Me(g.target(),c).each(function(k){dc(h,k,c).each(function(q){var r=q.boxes.getOr([]);if(1<r.length||1===r.length&&!B(h,k))d.selectRange(b,r,q.start,q.finish),a.selectContents(k)})})})},mouseup:function(g){e.each(f)}}}function Nh(a,
b,c){var d=Oh.byAttr(Ph,function(e,f,g){c.targets().each(function(h){J(f).each(function(k){var q=Ne(a);q=ed(y,u.fromDom(a.getDoc()),q);k=Qh(k,h,q);a.fire("TableSelectionChange",{cells:e,start:f,finish:g,otherCells:k})})})},function(){a.fire("TableSelectionClear")});a.on("init",function(e){var f=a.getWin(),g=Ja(a);e=fd(a);var h=Rh(f,g,e,d),k=Sh(f,g,e,d),q=Th(f,g,e,d);a.on("TableSelectorChange",function(p){return q(p.start,p.finish)});var r=function(p,n){!0===p.raw().shiftKey&&(n.kill()&&p.kill(),n.selection().each(function(t){t=
Ka.relative(t.start(),t.finish());t=gd(f,t);a.selection.setRng(t)}))};e=function(){var p=fa(u.fromDom(g)),n=fa(0);return{touchEnd:function(t){var v=u.fromDom(t.target);if("td"===D(v)||"th"===D(v)){var w=p.get(),A=n.get();B(w,v)&&300>t.timeStamp-A&&(t.preventDefault(),q(v,v))}p.set(v);n.set(t.timeStamp)}}}();a.on("mousedown",function(p){0===p.button&&hd(p)&&h.mousedown(zb(p))});a.on("mouseover",function(p){var n=void 0===p.buttons?!0:Oe.browser.isEdge()&&0===p.buttons?!0:0!==(p.buttons&1);n&&hd(p)&&
h.mouseover(zb(p))});a.on("mouseup",function(p){0===p.button&&hd(p)&&h.mouseup(zb(p))});a.on("touchend",e.touchEnd);a.on("keyup",function(p){var n=zb(p);if(n.raw().shiftKey&&id(n.raw().which)){p=a.selection.getRng();var t=u.fromDom(p.startContainer),v=u.fromDom(p.endContainer);k.keyup(n,t,p.startOffset,v,p.endOffset).each(function(w){r(n,w)})}});a.on("keydown",function(p){var n=zb(p);b().each(function(A){return A.hideBars()});p=a.selection.getRng();var t=u.fromDom(a.selection.getStart()),v=u.fromDom(p.startContainer),
w=u.fromDom(p.endContainer);t=Ab(t).isRtl()?Uh:Vh;k.keydown(n,v,p.startOffset,w,p.endOffset,t).each(function(A){r(n,A)});b().each(function(A){return A.showBars()})});a.on("NodeChange",function(){var p=a.selection,n=u.fromDom(p.getStart());p=u.fromDom(p.getEnd());jd(J,[n,p]).fold(function(){return d.clear(g)},y)})});return{clear:d.clear}}var Wh=tinymce.util.Tools.resolve("tinymce.PluginManager"),y=function(){},Xh=function(a,b){return function(){for(var c=[],d=0;d<arguments.length;d++)c[d]=arguments[d];
return a(b.apply(null,c))}},Yh=function(a,b){return function(c){return a(b(c))}},m=function(a){return function(){return a}},Q=function(a){return a},Wa=function(a){return function(b){return!a(b)}},Zh=function(a){return function(){throw Error(a);}},ca=m(!1),L=m(!0),ec=function(){return kd},kd=function(){var a=function(d){return d.isNone()},b=function(d){return d()},c=function(d){return d};return{fold:function(d,e){return d()},is:ca,isSome:ca,isNone:L,getOr:c,getOrThunk:b,getOrDie:function(d){throw Error(d||
"error: getOrDie called on none.");},getOrNull:m(null),getOrUndefined:m(void 0),or:c,orThunk:b,map:ec,each:y,bind:ec,exists:ca,forall:L,filter:ec,equals:a,equals_:a,toArray:function(){return[]},toString:m("none()")}}(),ld=function(a){var b=m(a),c=function(){return e},d=function(f){return f(a)},e={fold:function(f,g){return g(a)},is:function(f){return a===f},isSome:L,isNone:ca,getOr:b,getOrThunk:b,getOrDie:b,getOrNull:b,getOrUndefined:b,or:c,orThunk:c,map:function(f){return ld(f(a))},each:function(f){f(a)},
bind:d,exists:d,forall:d,filter:function(f){return f(a)?e:kd},toArray:function(){return[a]},toString:function(){return"some("+a+")"},equals:function(f){return f.is(a)},equals_:function(f,g){return f.fold(ca,function(h){return g(a,h)})}};return e},l={some:ld,none:ec,from:function(a){return null===a||void 0===a?kd:ld(a)}},md=function(a){return function(b){var c=typeof b;return(null===b?"null":"object"===c&&(Array.prototype.isPrototypeOf(b)||b.constructor&&"Array"===b.constructor.name)?"array":"object"===
c&&(String.prototype.isPrototypeOf(b)||b.constructor&&"String"===b.constructor.name)?"string":c)===a}},nd=function(a){return function(b){return typeof b===a}},La=md("string"),Pe=md("object"),fc=md("array"),$h=nd("boolean"),Qe=function(a){return null===a||void 0===a},Sa=nd("function"),dd=nd("number"),ai=Array.prototype.slice,bi=Array.prototype.indexOf,ci=Array.prototype.push,P=function(a,b){return-1<bi.call(a,b)},Xa=function(a,b){for(var c=0,d=a.length;c<d;c++)if(b(a[c],c))return!0;return!1},Ma=function(a,
b){for(var c=[],d=0;d<a;d++)c.push(b(d));return c},x=function(a,b){for(var c=a.length,d=Array(c),e=0;e<c;e++)d[e]=b(a[e],e);return d},z=function(a,b){for(var c=0,d=a.length;c<d;c++)b(a[c],c)},di=function(a,b){for(var c=a.length-1;0<=c;c--)b(a[c],c)},X=function(a,b){for(var c=[],d=0,e=a.length;d<e;d++){var f=a[d];b(f,d)&&c.push(f)}return c},hb=function(a,b,c){di(a,function(d){c=b(c,d)});return c},za=function(a,b,c){z(a,function(d){c=b(c,d)});return c},na=function(a,b){a:{for(var c=0,d=a.length;c<d;c++){var e=
a[c];if(b(e,c)){a=l.some(e);break a}else if(ca(e,c))break}a=l.none()}return a},gc=function(a,b){for(var c=0,d=a.length;c<d;c++)if(b(a[c],c))return l.some(c);return l.none()},od=function(a){for(var b=[],c=0,d=a.length;c<d;++c){if(!fc(a[c]))throw Error("Arr.flatten item "+c+" was not an array, input: "+a);ci.apply(b,a[c])}return b},oa=function(a,b){return od(x(a,b))},Bb=function(a,b){for(var c=0,d=a.length;c<d;++c)if(!0!==b(a[c],c))return!1;return!0},ei=function(a){a=ai.call(a,0);a.reverse();return a},
pd=function(a){return 0===a.length?l.none():l.some(a[a.length-1])},Na=function(a,b){for(var c=0;c<a.length;c++){var d=b(a[c],c);if(d.isSome())return d}return l.none()},ib=Object.keys,fi=Object.hasOwnProperty,ha=function(a,b){for(var c=ib(a),d=0,e=c.length;d<e;d++){var f=c[d];b(a[f],f)}},Re=function(a,b){return gi(a,function(c,d){return{k:d,v:b(c,d)}})},gi=function(a,b){var c={};ha(a,function(d,e){d=b(d,e);c[d.k]=d.v});return c},hi=function(a){return function(b,c){a[c]=b}},ii=function(a,b,c,d){ha(a,
function(e,f){(b(e,f)?c:d)(e,f)});return{}},ji=function(a,b){var c={};ii(a,b,hi(c),y);return c},pa=function(a,b){return fi.call(a,b)?l.from(a[b]):l.none()};"undefined"!==typeof G.window||Function("return this;")();var D=function(a){return a.dom().nodeName.toLowerCase()},hc=function(a){return function(b){return b.dom().nodeType===a}},Gh=function(a){return 8===a.dom().nodeType||"#comment"===D(a)},Va=hc(1),Ia=hc(3),ki=hc(9),li=hc(11),Se=function(a,b,c){if(La(c)||$h(c)||dd(c))a.setAttribute(b,c+"");else throw G.console.error("Invalid call to Attr.set. Key ",
b,":: Value ",c,":: Element ",a),Error("Attribute value was not simple");},Y=function(a,b,c){Se(a.dom(),b,c)},yb=function(a,b){var c=a.dom();ha(b,function(d,e){Se(c,e,d)})},ma=function(a,b){a=a.dom().getAttribute(b);return null===a?void 0:a},Te=function(a,b){return(a=a.dom())&&a.hasAttribute?a.hasAttribute(b):!1},I=function(a,b){a.dom().removeAttribute(b)},Je=function(a){return za(a.dom().attributes,function(b,c){b[c.name]=c.value;return b},{})},Ue=function(a,b,c){return""===b||a.length>=b.length&&
a.substr(c,c+b.length)===b},Z=function(a,b){return-1!==a.indexOf(b)},qd=function(a,b){return Ue(a,b,a.length-b.length)},mi=function(a){return function(b){return b.replace(a,"")}}(/^\s+|\s+$/g),ni=function(a){return 0<a.length},fb=function(a){return void 0!==a.style&&Sa(a.style.getPropertyValue)},Cb=function(a){if(null===a||void 0===a)throw Error("Node cannot be null or undefined");return{dom:m(a)}},u={fromHtml:function(a,b){b=(b||G.document).createElement("div");b.innerHTML=a;if(!b.hasChildNodes()||
1<b.childNodes.length)throw G.console.error("HTML does not have a single root node",a),Error("HTML must have a single root node");return Cb(b.childNodes[0])},fromTag:function(a,b){a=(b||G.document).createElement(a);return Cb(a)},fromText:function(a,b){a=(b||G.document).createTextNode(a);return Cb(a)},fromDom:Cb,fromPoint:function(a,b,c){a=a.dom();return l.from(a.elementFromPoint(b,c)).map(Cb)}},E=function(){E=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++){b=arguments[c];for(var e in b)Object.prototype.hasOwnProperty.call(b,
e)&&(a[e]=b[e])}return a};return E.apply(this,arguments)},jb=function(a){var b=!1,c;return function(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];b||(b=!0,c=a.apply(null,d));return c}},Ve=function(){return rd(0,0)},rd=function(a,b){return{major:a,minor:b}},ic={nu:rd,detect:function(a,b){b=String(b).toLowerCase();if(0===a.length)a=Ve();else{a:{for(var c=0;c<a.length;c++){var d=a[c];if(d.test(b)){a=d;break a}}a=void 0}a=a?rd(Number(b.replace(a,"$1")),Number(b.replace(a,"$2"))):{major:0,
minor:0}}return a},unknown:Ve},We=function(a){var b=a.current,c=function(d){return function(){return b===d}};return{current:b,version:a.version,isEdge:c("Edge"),isChrome:c("Chrome"),isIE:c("IE"),isOpera:c("Opera"),isFirefox:c("Firefox"),isSafari:c("Safari")}},oi=function(){return We({current:void 0,version:ic.unknown()})};m("Edge");m("Chrome");m("IE");m("Opera");m("Firefox");m("Safari");var Xe=function(a){var b=a.current,c=function(d){return function(){return b===d}};return{current:b,version:a.version,
isWindows:c("Windows"),isiOS:c("iOS"),isAndroid:c("Android"),isOSX:c("OSX"),isLinux:c("Linux"),isSolaris:c("Solaris"),isFreeBSD:c("FreeBSD"),isChromeOS:c("ChromeOS")}},pi=function(){return Xe({current:void 0,version:ic.unknown()})};m("Windows");m("iOS");m("Android");m("Linux");m("OSX");m("Solaris");m("FreeBSD");m("ChromeOS");var Ye=function(a,b){var c=String(b).toLowerCase();return na(a,function(d){return d.search(c)})},Ze={detectBrowser:function(a,b){return Ye(a,b).map(function(c){var d=ic.detect(c.versionRegexes,
b);return{current:c.name,version:d}})},detectOs:function(a,b){return Ye(a,b).map(function(c){var d=ic.detect(c.versionRegexes,b);return{current:c.name,version:d}})}},sd=/.*?version\/ ?([0-9]+)\.([0-9]+).*/,Aa=function(a){return function(b){return Z(b,a)}},qi=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(a){return Z(a,"edge/")&&Z(a,"chrome")&&Z(a,"safari")&&Z(a,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,sd],search:function(a){return Z(a,
"chrome")&&!Z(a,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(a){return Z(a,"msie")||Z(a,"trident")}},{name:"Opera",versionRegexes:[sd,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Aa("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/ ?([0-9]+)\.([0-9]+).*/],search:Aa("firefox")},{name:"Safari",versionRegexes:[sd,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(a){return(Z(a,"safari")||Z(a,"mobile/"))&&Z(a,"applewebkit")}}],
ri=[{name:"Windows",search:Aa("win"),versionRegexes:[/.*?windows nt ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(a){return Z(a,"iphone")||Z(a,"ipad")},versionRegexes:[/.*?version\/ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Aa("android"),versionRegexes:[/.*?android ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Aa("mac os x"),versionRegexes:[/.*?mac os x ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Aa("linux"),versionRegexes:[]},
{name:"Solaris",search:Aa("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Aa("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Aa("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],$e={browsers:m(qi),oses:m(ri)},si={detect:function(a,b){var c=$e.browsers(),d=$e.oses();c=Ze.detectBrowser(c,a).fold(oi,We);d=Ze.detectOs(d,a).fold(pi,Xe);var e=d.isiOS()&&!0===/ipad/i.test(a),f=d.isiOS()&&!e,g=d.isiOS()||d.isAndroid(),h=g||b("(pointer:coarse)");b=e||!f&&g&&b("(min-device-width:768px)");
g=f||g&&!b;a=c.isSafari()&&d.isiOS()&&!1===/safari/i.test(a);var k=!g&&!b&&!a;a={isiPad:m(e),isiPhone:m(f),isTablet:m(b),isPhone:m(g),isTouch:m(h),isAndroid:d.isAndroid,isiOS:d.isiOS,isWebView:m(a),isDesktop:m(k)};return{browser:c,os:d,deviceType:a}}},ti=function(a){return G.window.matchMedia(a).matches},jc=jb(function(){return si.detect(G.navigator.userAgent,ti)}),ja=function(a,b){a=a.dom();if(1!==a.nodeType)return!1;if(void 0!==a.matches)return a.matches(b);if(void 0!==a.msMatchesSelector)return a.msMatchesSelector(b);
if(void 0!==a.webkitMatchesSelector)return a.webkitMatchesSelector(b);if(void 0!==a.mozMatchesSelector)return a.mozMatchesSelector(b);throw Error("Browser lacks native selectors");},af=function(a){return 1!==a.nodeType&&9!==a.nodeType&&11!==a.nodeType||0===a.childElementCount},Ba=function(a,b){b=void 0===b?G.document:b.dom();return af(b)?[]:x(b.querySelectorAll(a),u.fromDom)},kb=function(a,b){b=void 0===b?G.document:b.dom();return af(b)?l.none():l.from(b.querySelector(a)).map(u.fromDom)},B=function(a,
b){return a.dom()===b.dom()},td=function(a,b){if(jc().browser.isIE()){a=a.dom();b=b.dom();var c=G.Node.DOCUMENT_POSITION_CONTAINED_BY;b=0!==(a.compareDocumentPosition(b)&c)}else a=a.dom(),b=b.dom(),b=a===b?!1:a.contains(b);return b},Jh=ja,Db=function(a){return u.fromDom(a.dom().ownerDocument)},ui=function(a){return ki(a)?a:Db(a)},ya=function(a){return l.from(a.dom().parentNode).map(u.fromDom)},He=function(a,b){b=Sa(b)?b:ca;var c=a.dom();for(a=[];null!==c.parentNode&&void 0!==c.parentNode;){c=c.parentNode;
var d=u.fromDom(c);a.push(d);if(!0===b(d))break}return a},Ke=function(a){return l.from(a.dom().previousSibling).map(u.fromDom)},bc=function(a){return l.from(a.dom().nextSibling).map(u.fromDom)},W=function(a){return x(a.dom().childNodes,u.fromDom)},ud=function(a,b){a=a.dom().childNodes;return l.from(a[b]).map(u.fromDom)},bf=Sa(G.Element.prototype.attachShadow)&&Sa(G.Node.prototype.getRootNode),vi=m(bf),wi=bf?function(a){return u.fromDom(a.dom().getRootNode())}:ui,xi=function(a){a=wi(a);return li(a)?
l.some(a):l.none()},yi=function(a){return u.fromDom(a.dom().host)},zi=function(a){if(vi()&&!Qe(a.target)){var b=u.fromDom(a.target);if(Va(b)&&!Qe(u.fromDom(a.target).dom().shadowRoot)&&a.composed&&a.composedPath&&(b=a.composedPath()))return 0===b.length?l.none():l.some(b[0])}return l.from(a.target)},Eb=function(a){var b=Ia(a)?a.dom().parentNode:a.dom();return void 0===b||null===b||null===b.ownerDocument?!1:xi(u.fromDom(b)).fold(function(){return b.ownerDocument.body.contains(b)},Yh(Eb,yi))},cf=function(a){a=
a.dom().body;if(null===a||void 0===a)throw Error("Body is not available yet");return u.fromDom(a)},df=function(a,b,c){if(!La(c))throw G.console.error("Invalid call to CSS.set. Property ",b,":: Value ",c,":: Element ",a),Error("CSS value must be a string: "+c);fb(a)&&a.style.setProperty(b,c)},N=function(a,b,c){a=a.dom();df(a,b,c)},Fb=function(a,b){var c=a.dom();ha(b,function(d,e){df(c,e,d)})},va=function(a,b){var c=a.dom(),d=G.window.getComputedStyle(c).getPropertyValue(b);return""!==d||Eb(a)?d:ef(c,
b)},ef=function(a,b){return fb(a)?a.style.getPropertyValue(b):""},wa=function(a,b){a=a.dom();b=ef(a,b);return l.from(b).filter(function(c){return 0<c.length})},xa=function(a,b){var c=a.dom();fb(c)&&c.style.removeProperty(b);l.from(ma(a,"style")).map(mi).is("")&&I(a,"style")},ac=function(a,b){ya(a).each(function(c){c.dom().insertBefore(b.dom(),a.dom())})},Ha=function(a,b){bc(a).fold(function(){ya(a).each(function(c){K(c,b)})},function(c){ac(c,b)})},cd=function(a,b){ud(a,0).fold(function(){K(a,b)},
function(c){a.dom().insertBefore(b.dom(),c.dom())})},K=function(a,b){a.dom().appendChild(b.dom())},Eh=function(a,b){ac(a,b);K(b,a)},Ai=function(a,b){z(b,function(c){ac(a,c)})},Dh=function(a,b){z(b,function(c,d){Ha(0===d?a:b[d-1],c)})},Ua=function(a,b){z(b,function(c){K(a,c)})},vd=function(a){a.dom().textContent="";z(W(a),function(b){ba(b)})},ba=function(a){a=a.dom();null!==a.parentNode&&a.parentNode.removeChild(a)},Fh=function(a){var b=W(a);0<b.length&&Ai(a,b);ba(a)},Bi=function(a,b){return{rows:m(a),
columns:m(b)}},Ci=function(a,b){return{row:m(a),column:m(b)}},ff=function(a,b,c){return{element:m(a),rowspan:m(b),colspan:m(c)}},wd=function(a,b,c){return{element:m(a),cells:m(b),section:m(c)}},da=function(a,b){return{element:m(a),isNew:m(b)}},kc=function(a,b){return{cells:m(a),section:m(b)}},Di=function(a,b){return{details:m(a),section:m(b)}},Ei=function(a,b,c){return X(He(a,c),b)},Fi=function(a,b){return X(W(a),b)},Ie=function(a,b){var c=[];z(W(a),function(d){b(d)&&(c=c.concat([d]));c=c.concat(Ie(d,
b))});return c},Gb=function(a,b,c){return Ei(a,function(d){return ja(d,b)},c)},gf=function(a,b){return Fi(a,function(c){return ja(c,b)})},bd=function(a,b){return Ba(b,a)},xb=function(a,b,c){a=a.dom();for(c=Sa(c)?c:m(!1);a.parentNode;){a=a.parentNode;var d=u.fromDom(a);if(b(d))return l.some(d);if(c(d))break}return l.none()},Gi=function(a,b,c){return Ge(function(d,e){return e(d)},xb,a,b,c)},Hi=function(a,b){return na(a.dom().childNodes,function(c){return b(u.fromDom(c))}).map(u.fromDom)},Ii=function(a,
b){var c=function(d){for(var e=0;e<d.childNodes.length;e++){var f=u.fromDom(d.childNodes[e]);if(b(f))return l.some(f);f=c(d.childNodes[e]);if(f.isSome())return f}return l.none()};return c(a.dom())},Ta=function(a,b,c){return xb(a,function(d){return ja(d,b)},c)},hf=function(a,b){return Hi(a,function(c){return ja(c,b)})},Ji=function(a,b){return kb(b,a)},M=function(a,b,c){return Ge(function(d,e){return ja(d,e)},Ta,a,b,c)},ka=function(a,b,c){void 0===c&&(c=0);return l.from(ma(a,b)).map(function(d){return parseInt(d,
10)}).getOr(c)},Ki=function(a){return 1<ka(a,"colspan",1)},Li=function(a){return 1<ka(a,"rowspan",1)},lc=function(a,b){return parseInt(va(a,b),10)},mc=m(10),xd=m(10),Oa=function(a,b,c){return oa(W(a),function(d){return ja(d,b)?c(d)?[d]:[]:Oa(d,b,c)})},Mi=function(a,b,c){void 0===c&&(c=ca);return c(b)?l.none():P(a,D(b))?l.some(b):Ta(b,a.join(","),function(d){return ja(d,"table")||c(d)})},Hb=function(a,b){return Mi(["td","th"],a,b)},Ya=function(a){return Oa(a,"th,td",m(!0))},J=function(a,b){return M(a,
"table",b)},jf=function(a){a=Oa(a,"tr",m(!0));return x(a,function(b){var c=ya(b).map(function(e){e=D(e);return"tfoot"===e||"thead"===e||"tbody"===e?e:"tbody"}).getOr("tbody"),d=x(Ya(b),function(e){var f=ka(e,"rowspan",1),g=ka(e,"colspan",1);return ff(e,f,g)});return wd(b,d,c)})},Ni=function(a,b){return x(a,function(c){var d=x(Ya(c),function(e){var f=ka(e,"rowspan",1),g=ka(e,"colspan",1);return ff(e,f,g)});return wd(c,d,b.section())})},kf=function(a,b){a=oa(a.all,function(c){return c.cells()});return X(a,
b)},lf=function(a){var b={},c=[],d=a.length,e=0;z(a,function(f,g){var h=[];z(f.cells(),function(k){for(var q=0;void 0!==b[g+","+q];)q++;var r=k.element();var p=k.rowspan(),n=k.colspan(),t=q;r={element:m(r),rowspan:m(p),colspan:m(n),row:m(g),column:m(t)};for(p=0;p<k.colspan();p++)for(n=0;n<k.rowspan();n++)t=q+p,b[g+n+","+t]=r,e=Math.max(e,t+1);h.push(r)});c.push(wd(f.element(),h,f.section()))});return{grid:Bi(d,e),access:b,all:c}},C={fromTable:function(a){a=jf(a);return lf(a)},generate:lf,getAt:function(a,
b,c){a=a.access[b+","+c];return void 0!==a?l.some(a):l.none()},findItem:function(a,b,c){a=kf(a,function(d){return c(b,d.element())});return 0<a.length?l.some(a[0]):l.none()},filterItems:kf,justCells:function(a){a=x(a.all,function(b){return b.cells()});return od(a)}},Oi=function(a,b){var c=a.grid.columns(),d=a.grid.rows(),e=c,f=0,g=0;ha(a.access,function(h){if(b(h)){var k=h.row(),q=k+h.rowspan()-1,r=h.column();h=r+h.colspan()-1;k<d?d=k:q>f&&(f=q);r<e?e=r:h>g&&(g=h)}});return{minRow:d,minCol:e,maxRow:f,
maxCol:g}},Pi=function(a,b,c,d){for(var e=b.grid.columns(),f=b.grid.rows(),g=0;g<f;g++)for(var h=!1,k=0;k<e;k++)if(!(g<c.minRow||g>c.maxRow||k<c.minCol||k>c.maxCol))if(C.getAt(b,g,k).filter(d).isNone()){var q=h,r=a[g].element(),p=u.fromTag("td");K(p,u.fromTag("br"));(q?K:cd)(r,p)}else h=!0},Qi=function(a,b){var c=X(Oa(a,"tr",m(!0)),function(d){return 0===d.dom().childElementCount});z(c,ba);b.minCol!==b.maxCol&&b.minRow!==b.maxRow||z(Oa(a,"th,td",m(!0)),function(d){I(d,"rowspan");I(d,"colspan")});
I(a,"width");I(a,"height");xa(a,"width");xa(a,"height")},Ri=function(a,b){var c=function(k){return ja(k.element(),b)},d=jf(a),e=C.generate(d),f=Oi(e,c),g="th:not("+b+"),td:not("+b+")",h=Oa(a,"th,td",function(k){return ja(k,g)});z(h,ba);Pi(d,e,f,c);Qi(a,f);return a},nc=function(a,b){var c=function(d){return a(d)?l.from(d.dom().nodeValue):l.none()};return{get:function(d){if(!a(d))throw Error("Can only get "+b+" value of a "+b+" node");return c(d).getOr("")},getOption:c,set:function(d,e){if(!a(d))throw Error("Can only set raw "+
b+" value of a "+b+" node");d.dom().nodeValue=e}}}(Ia,"text"),cc=function(a){return nc.get(a)},Hh=function(a,b){return nc.set(a,b)},Pa=function(a){return"img"===D(a)?1:nc.getOption(a).fold(function(){return W(a).length},function(b){return b.length})},Si=function(a){return nc.getOption(a).filter(function(b){return 0!==b.trim().length||-1<b.indexOf("\u00a0")}).isSome()},Ti=["img","br"],mf=function(a){return Si(a)||P(Ti,D(a))},oc=function(a){return Ii(a,mf)},yd=function(a){return Ui(a,mf)},Ui=function(a,
b){var c=function(d){d=W(d);for(var e=d.length-1;0<=e;e--){var f=d[e];if(b(f))return l.some(f);f=c(f);if(f.isSome())return f}return l.none()};return c(a)},zd=function(a,b){return u.fromDom(a.dom().cloneNode(b))},Ib=function(a){return zd(a,!0)},Vi=function(a,b){b=u.fromTag(b);var c=Je(a);yb(b,c);a=W(Ib(a));Ua(b,a);return b},Ad=function(){var a=u.fromTag("td");K(a,u.fromTag("br"));return a},Wi=function(a,b,c){var d=Vi(a,b);ha(c,function(e,f){null===e?I(d,f):Y(d,f,e)});return d},Xi=function(a){return a},
nf=function(a){return function(){return u.fromTag("tr",a.dom())}},Yi=function(a,b,c){return oc(a).map(function(d){var e=c.join(",");d=Gb(d,e,function(f){return B(f,a)});return hb(d,function(f,g){g=zd(g,!1);I(g,"contenteditable");K(f,g);return g},b)}).getOr(b)},ed=function(a,b,c){return{row:nf(b),cell:function(d){var e=Db(d.element());e=u.fromTag(D(d.element()),e.dom());var f=c.getOr("strong em b i span font h1 h2 h3 h4 h5 h6 p div".split(" "));f=0<f.length?Yi(d.element(),e,f):e;K(f,u.fromTag("br"));
f=d.element().dom();var g=e.dom();fb(f)&&fb(g)&&(g.style.cssText=f.style.cssText);xa(e,"height");1!==d.colspan()&&xa(d.element(),"width");a(d.element(),e);return e},replace:Wi,gap:Ad}},of=function(a){return{row:nf(a),cell:Ad,replace:Xi,gap:Ad}},Zi=function(a,b){b=(b||G.document).createElement("div");b.innerHTML=a;return W(u.fromDom(b))},$i=function(a,b){var c=b.column(),d=b.column()+b.colspan()-1,e=b.row();b=b.row()+b.rowspan()-1;return c<=a.finishCol()&&d>=a.startCol()&&e<=a.finishRow()&&b>=a.startRow()},
aj=function(a,b){return b.column()>=a.startCol()&&b.column()+b.colspan()-1<=a.finishCol()&&b.row()>=a.startRow()&&b.row()+b.rowspan()-1<=a.finishRow()},pf=function(a,b,c){b=C.findItem(a,b,B);var d=C.findItem(a,c,B);return b.bind(function(e){return d.map(function(f){var g=Math.min(e.row(),f.row()),h=Math.min(e.column(),f.column()),k=Math.max(e.row()+e.rowspan()-1,f.row()+f.rowspan()-1);f=Math.max(e.column()+e.colspan()-1,f.column()+f.colspan()-1);return{startRow:m(g),startCol:m(h),finishRow:m(k),finishCol:m(f)}})})},
bj=function(a,b,c){return pf(a,b,c).bind(function(d){for(var e=!0,f=F(aj,d),g=d.startRow();g<=d.finishRow();g++)for(var h=d.startCol();h<=d.finishCol();h++)e=e&&C.getAt(a,g,h).exists(f);return e?l.some(d):l.none()})},cj=function(a,b,c,d){return C.findItem(a,b,B).bind(function(e){var f=0<c?e.row()+e.rowspan()-1:e.row();e=0<d?e.column()+e.colspan()-1:e.column();return C.getAt(a,f+c,e+d).map(function(g){return g.element()})})},qf=function(a,b,c){return pf(a,b,c).map(function(d){d=C.filterItems(a,F($i,
d));return x(d,function(e){return e.element()})})},rf=function(a,b){return C.findItem(a,b,function(c,d){return td(d,c)}).map(function(c){return c.element()})},dj=function(a,b,c){return J(a).bind(function(d){d=pc(d);return cj(d,a,b,c)})},ej=function(a,b,c){a=pc(a);return qf(a,b,c)},Bd=function(a,b,c,d,e){var f=pc(a);b=B(a,c)?l.some(b):rf(f,b);var g=B(a,e)?l.some(d):rf(f,d);return b.bind(function(h){return g.bind(function(k){return qf(f,h,k)})})},pc=C.fromTable,Ih="body p div article aside figcaption figure footer header nav section ol ul li table thead tbody tfoot caption tr td th h1 h2 h3 h4 h5 h6 blockquote pre address".split(" "),
gj=function(a,b,c,d){c=b(a,c);return hb(d,function(e,f){f=b(a,f);return fj(a,e,f)},c)},fj=function(a,b,c){return b.bind(function(d){return c.filter(F(a.eq,d))})},hj=function(a,b,c){if(0<c.length){var d=c[0];c=c.slice(1);a=gj(a,b,d,c)}else a=l.none();return a},ij=function(a,b,c,d){void 0===d&&(d=ca);b=[b].concat(a.up().all(b));c=[c].concat(a.up().all(c));var e=function(g){return gc(g,d).fold(function(){return g},function(h){return g.slice(0,h+1)})};b=e(b);var f=e(c);c=na(b,function(g){return Xa(f,
F(a.eq,g))});return{firstpath:m(b),secondpath:m(f),shared:m(c)}},sf=$b(),jd=function(a,b){return hj(sf,function(c,d){return a(d)},b)},Cd=function(a){return Ta(a,"table")},dc=function(a,b,c){var d=function(e){return function(f){return void 0!==c&&c(f)||B(f,e)}};return B(a,b)?l.some({boxes:l.some([a]),start:a,finish:b}):Cd(a).bind(function(e){return Cd(b).bind(function(f){if(B(e,f))return l.some({boxes:ej(e,a,b),start:a,finish:b});if(td(e,f)){var g=Gb(b,"td,th",d(e));g=0<g.length?g[g.length-1]:b;return l.some({boxes:Bd(e,
a,e,b,f),start:a,finish:g})}return td(f,e)?(g=Gb(a,"td,th",d(f)),g=0<g.length?g[g.length-1]:a,l.some({boxes:Bd(f,a,e,b,f),start:a,finish:g})):ij(sf,a,b,void 0).shared().bind(function(h){return M(h,"table",c).bind(function(k){var q=Gb(b,"td,th",d(k));q=0<q.length?q[q.length-1]:b;var r=Gb(a,"td,th",d(k));r=0<r.length?r[r.length-1]:a;return l.some({boxes:Bd(k,a,e,b,f),start:r,finish:q})})})})})},Dd=function(a,b){a=Ba(b,a);return 0<a.length?l.some(a):l.none()},jj=function(a,b){return na(a,function(c){return ja(c,
b)})},tf=function(a,b,c){return kb(b,a).bind(function(d){return kb(c,a).bind(function(e){return jd(Cd,[d,e]).map(function(f){return{first:m(d),last:m(e),table:m(f)}})})})},kj=function(a,b){return Ta(a,"table").bind(function(c){return kb(b,c).bind(function(d){return dc(d,a).bind(function(e){return e.boxes.map(function(f){return{boxes:f,start:e.start,finish:e.finish}})})})})},lj=function(a,b,c,d,e){return jj(a,e).bind(function(f){return dj(f,b,c).bind(function(g){return kj(g,d)})})},mj=function(a,b,
c){return tf(a,b,c).bind(function(d){var e=function(h){return B(a,h)},f=Ta(d.first(),"thead,tfoot,tbody,table",e),g=Ta(d.last(),"thead,tfoot,tbody,table",e);return f.bind(function(h){return g.bind(function(k){if(B(h,k)){var q=d.table();k=d.first();var r=d.last();q=pc(q);k=bj(q,k,r)}else k=l.none();return k})})})},Ph=Object.freeze({__proto__:null,selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",attributeSelector:"[data-mce-selected]",firstSelected:"data-mce-first-selected",
firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"}),Ca={generate:function(a){if(!fc(a))throw Error("cases must be an array");if(0===a.length)throw Error("there must be at least one case");var b=[],c={};z(a,function(d,e){var f=ib(d);if(1!==f.length)throw Error("one and only one name per case");var g=f[0],h=d[g];if(void 0!==c[g])throw Error("duplicate key detected:"+
g);if("cata"===g)throw Error("cannot have a case named cata (sorry)");if(!fc(h))throw Error("case arguments must be an array");b.push(g);c[g]=function(){var k=arguments.length;if(k!==h.length)throw Error("Wrong number of arguments to case "+g+". Expected "+h.length+" ("+h+"), got "+k);var q=Array(k);for(k=0;k<q.length;k++)q[k]=arguments[k];return{fold:function(){if(arguments.length!==a.length)throw Error("Wrong number of arguments to fold. Expected "+a.length+", got "+arguments.length);return arguments[e].apply(null,
q)},match:function(r){var p=ib(r);if(b.length!==p.length)throw Error("Wrong number of arguments to match. Expected: "+b.join(",")+"\nActual: "+p.join(","));if(!Bb(b,function(n){return P(p,n)}))throw Error("Not all branches were specified when using match. Specified: "+p.join(", ")+"\nRequired: "+b.join(", "));return r[g].apply(null,q)},log:function(r){G.console.log(r,{constructors:b,constructor:g,params:q})}}}});return c}},Ed=Ca.generate([{none:[]},{multiple:["elements"]},{single:["selection"]}]),
Fd=function(a,b,c,d){return a.fold(b,c,d)},nj=Ed.none,oj=Ed.multiple,pj=Ed.single,qc=function(a,b){return Fd(b.get(),m([]),Q,m([a]))},qj=function(a,b){var c=function(d){return Te(d,"rowspan")&&1<parseInt(ma(d,"rowspan"),10)||Te(d,"colspan")&&1<parseInt(ma(d,"colspan"),10)};a=qc(a,b);return 0<a.length&&Bb(a,c)?l.some(a):l.none()},rj=function(a,b){return Fd(b.get(),l.none,function(c,d){return 0===c.length?l.none():mj(a,"td[data-mce-first-selected],th[data-mce-first-selected]","td[data-mce-last-selected],th[data-mce-last-selected]").bind(function(e){return 1<
c.length?l.some({bounds:m(e),cells:m(c)}):l.none()})},l.none)},uf=function(a){return{element:m(a),mergable:l.none,unmergable:l.none,selection:m([a])}},Jb=function(a,b,c){return{element:m(c),mergable:m(rj(b,a)),unmergable:m(qj(c,a)),selection:m(qc(c,a))}},sj=function(a,b,c){return{element:m(a),clipboard:m(b),generators:m(c)}},tj=function(a,b,c,d){return{selection:m(qc(b,a)),clipboard:m(c),generators:m(d)}},uj=function(a){return J(a[0]).map(Ib).map(function(b){return[Ri(b,"[data-mce-selected]")]})},
vj=function(a,b){return x(b,function(c){return a.selection.serializer.serialize(c.dom(),{})}).join("")},wj=function(a){return x(a,function(b){return b.dom().innerText}).join("")},xj=function(a,b,c,d){a.on("BeforeGetContent",function(e){var f=function(g){e.preventDefault();uj(g).each(function(h){e.content="text"===e.format?wj(h):vj(a,h)})};!0===e.selection&&Fd(b.get(),y,f,y)});a.on("BeforeSetContent",function(e){!0===e.selection&&!0===e.paste&&l.from(a.dom.getParent(a.selection.getStart(),"th,td")).each(function(f){var g=
u.fromDom(f);J(g).each(function(h){var k=X(Zi(e.content),function(r){return"meta"!==D(r)});if(1===k.length&&"table"===D(k[0])){e.preventDefault();var q=u.fromDom(a.getDoc());q=of(q);k=sj(g,k[0],q);c.pasteCells(h,k).each(function(r){a.selection.setRng(r);a.focus();d.clear(h)})}})})})},vf=function(a,b){return{left:m(a),top:m(b),translate:function(c,d){return vf(a+c,b+d)}}},Da=vf,rc=function(a,b){return void 0!==a?a:void 0!==b?b:0},Kb=function(a){var b=a.dom().ownerDocument,c=b.body,d=b.defaultView,
e=b.documentElement;if(c===a.dom())return Da(c.offsetLeft,c.offsetTop);b=rc(d.pageYOffset,e.scrollTop);d=rc(d.pageXOffset,e.scrollLeft);var f=rc(e.clientTop,c.clientTop);c=rc(e.clientLeft,c.clientLeft);e=a.dom();var g=e.ownerDocument.body;g===e?a=Da(g.offsetLeft,g.offsetTop):Eb(a)?(a=e.getBoundingClientRect(),a=Da(a.left,a.top)):a=Da(0,0);return a.translate(d-c,b-f)},Gd={only:function(a){var b=l.from(a.dom().documentElement).map(u.fromDom).getOr(a);return{parent:m(b),view:m(a),origin:m(Da(0,0))}},
detached:function(a,b){return{parent:m(b),view:m(a),origin:function(){return Kb(b)}}},body:function(a,b){return{parent:m(b),view:m(a),origin:m(Da(0,0))}}},Hd=Le("height",function(a){var b=a.dom();return Eb(a)?b.getBoundingClientRect().height:b.offsetHeight}),sc=function(a){return Hd.get(a)},ea=Le("width",function(a){return a.dom().offsetWidth}),wf=function(a){return Kb(a).left()+ea.getOuter(a)},xf=function(a){return Kb(a).left()},yf=function(a,b){b=xf(b);return{col:a,x:b}},zf=function(a,b){b=wf(b);
return{col:a,x:b}},Id=function(a){return Kb(a).top()},yj=function(a,b){b=Id(b);return{row:a,y:b}},zj=function(a,b){b=Id(b)+Hd.getOuter(b);return{row:a,y:b}},Jd=function(a,b,c){if(0===c.length)return[];var d=x(c.slice(1),function(f,g){return f.map(function(h){return a(g,h)})}),e=c[c.length-1].map(function(f){return b(c.length-1,f)});return d.concat([e])},Lb={delta:Q,positions:function(a){return Jd(yj,zj,a)},edge:Id},Kd={delta:Q,edge:xf,positions:function(a){return Jd(yf,zf,a)}},Ld={delta:function(a){return-a},
edge:wf,positions:function(a){return Jd(zf,yf,a)}},Bf=function(a){var b=a.grid,c=Ma(b.columns(),Q),d=Ma(b.rows(),Q);return x(c,function(e){return Af(function(){return oa(d,function(f){return C.getAt(a,f,e).filter(function(g){return g.column()===e}).fold(m([]),function(g){return[g]})})},function(f){return 1===f.colspan()},function(){return C.getAt(a,0,e)})})},Af=function(a,b,c){var d=a();return na(d,b).orThunk(function(){return l.from(d[0]).orThunk(c)}).map(function(e){return e.element()})},Cf=function(a){var b=
a.grid,c=Ma(b.rows(),Q),d=Ma(b.columns(),Q);return x(c,function(e){return Af(function(){return oa(d,function(f){return C.getAt(a,e,f).filter(function(g){return g.row()===e}).fold(m([]),function(g){return[g]})})},function(f){return 1===f.rowspan()},function(){return C.getAt(a,e,0)})})},Df=function(a,b){if(0>b||b>=a.length-1)return l.none();var c=a[b].fold(function(){var e=ei(a.slice(0,b));return Na(e,function(f,g){return f.map(function(h){return{value:h,delta:g+1}})})},function(e){return l.some({value:e,
delta:0})}),d=a[b+1].fold(function(){var e=a.slice(b+1);return Na(e,function(f,g){return f.map(function(h){return{value:h,delta:g+1}})})},function(e){return l.some({value:e,delta:1})});return c.bind(function(e){return d.map(function(f){return Math.abs(f.value-e.value)/(f.delta+e.delta)})})},Ef=function(){var a=jc().browser;return a.isIE()||a.isEdge()},qa=function(a,b,c){a=va(a,b);a=parseFloat(a);return isNaN(a)?c:a},Md=function(a){if(Ef()){var b=a.dom().getBoundingClientRect().width;if("border-box"!==
va(a,"box-sizing")){var c=qa(a,"padding-left",0),d=qa(a,"padding-right",0),e=qa(a,"border-left-width",0);a=qa(a,"border-right-width",0);b=b-c-d-(e+a)}}else b=qa(a,"width",ea.get(a));return b},Aj=/(\d+(\.\d+)?)(\w|%)*/,tc=/(\d+(\.\d+)?)%/,Nd=/(\d+(\.\d+)?)px|em/,Ff=function(a,b){N(a,"width",b+"px")},Gf=function(a,b){N(a,"width",b+"%")},uc=function(a,b){N(a,"height",b+"px")},Bj=function(a){return wa(a,"height").getOrThunk(function(){if(Ef()){var b=a.dom().getBoundingClientRect().height;if("border-box"!==
va(a,"box-sizing")){var c=qa(a,"padding-top",0),d=qa(a,"padding-bottom",0),e=qa(a,"border-top-width",0),f=qa(a,"border-bottom-width",0);b=b-c-d-(e+f)}}else b=qa(a,"height",sc(a));return b+"px"})},Cj=function(a,b,c,d){var e=J(a).map(function(f){f=c(f);return Math.floor(b/100*f)}).getOr(b);d(a,e);return e},Za=function(a){return wa(a,"width").fold(function(){return l.from(ma(a,"width"))},function(b){return l.some(b)})},Dj=function(a,b){return Za(a).fold(function(){return ea.get(a)/b.pixelWidth()*100},
function(c){c=tc.exec(c);c=null!==c?parseFloat(c[1]):Md(a)/b.pixelWidth()*100;return c})},Hf=function(a,b){return Za(a).fold(function(){return Md(a)},function(c){var d=Nd.exec(c);null!==d?c=parseInt(d[1],10):(c=tc.exec(c),c=null!==c?parseFloat(c[1])/100*b.pixelWidth():Md(a));return c})},If=function(a){var b;if(b=Bj(a)){var c=parseInt(b,10);b=qd(b,"%")&&"table"!==D(a)?Cj(a,c,sc,uc):c}else b=sc(a);a=ka(a,"rowspan",1);return b/a},Ej=function(a){return Za(a).bind(function(b){b=Aj.exec(b);return null!==
b?l.some({width:m(parseFloat(b[1])),unit:m(b[3])}):l.none()})},Jf=function(a){var b=l.from(a.dom().offsetParent).map(u.fromDom).getOr(cf(Db(a)));return ea.get(a)/ea.get(b)*100+"%"},Fj=m(tc),Gj=m(Nd),Kf=function(a,b,c){return wa(a,b).fold(function(){return c(a)+"px"},function(d){return d})},Hj=function(a,b){return Kf(a,"width",function(c){return Hf(c,b)})},Ij=function(a){return Kf(a,"height",If)},Od=function(a,b,c,d,e){a=Bf(a);var f=x(a,function(g){return g.map(b.edge)});return x(a,function(g,h){return g.filter(Wa(Ki)).fold(function(){var k=
Df(f,h);return d(k)},function(k){return c(k,e)})})},Lf=function(a){return a.map(function(b){return b+"px"}).getOr("")},Jj=function(a,b,c){return Od(a,b,Dj,function(d){return d.fold(function(){return c.minCellWidth()},function(e){return e/c.pixelWidth()*100})},c)},Mf=function(a,b,c){return Od(a,b,Hf,function(d){return d.getOrThunk(c.minCellWidth)},c)},Nf=function(a,b,c,d){a=Cf(a);var e=x(a,function(f){return f.map(b.edge)});return x(a,function(f,g){return f.filter(Wa(Li)).fold(function(){var h=Df(e,
g);return d(h)},function(h){return c(h)})})},Kj=function(a,b){return Nf(a,b,If,function(c){return c.getOrThunk(xd)})},Mb=Ca.generate([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),Of=function(a,b,c){a=c.substring(0,c.length-a.length);var d=parseFloat(a);return a===d.toString()?b(d):Mb.invalid(c)},$a=E(E({},Mb),{from:function(a){return qd(a,"%")?Of("%",Mb.percent,a):qd(a,"px")?Of("px",Mb.pixels,a):Mb.invalid(a)}}),Lj=function(a,b){return x(a,function(c){return $a.from(c).fold(function(){return c},
function(d){return d/b*100+"%"},function(d){return d+"%"})})},Mj=function(a,b,c){var d=c/b;return x(a,function(e){return $a.from(e).fold(function(){return e},function(f){return f*d+"px"},function(f){return f/100*c+"px"})})},Nj=function(a,b){a=a.fold(function(){return m("")},function(c){return m(c/b+"px")},function(c){return m(c/b+"px")});return Ma(b,a)},Oj=function(a,b,c){return a.fold(function(){return b},function(d){return Mj(b,c,d)},function(d){return Lj(b,c)})},Pf=function(a,b,c){c=$a.from(c);
a=Bb(a,function(d){return"0px"===d})?Nj(c,a.length):Oj(c,a,b);return Pj(a)},Qf=function(a,b){return 0===a.length?b:hb(a,function(c,d){return $a.from(d).fold(m(0),Q,Q)+c},0)},Qj=function(a,b){return $a.from(a).fold(m(a),function(c){return c+b+"px"},function(c){return c+b+"%"})},Pj=function(a){if(0===a.length)return a;a=hb(a,function(c,d){var e=$a.from(d).fold(function(){return{value:d,remainder:0}},function(f){var g=Math.floor(f);return{value:g+"px",remainder:f-g}},function(f){return{value:f+"%",remainder:0}});
return{output:[e.value].concat(c.output),remainder:c.remainder+e.remainder}},{output:[],remainder:0});var b=a.output;return b.slice(0,b.length-1).concat([Qj(b[b.length-1],Math.round(a.remainder))])},Rf=$a.from,Rj=function(a,b,c){z(b,function(d){var e=a.slice(d.column(),d.colspan()+d.column());e=Qf(e,mc());N(d.element(),"width",e+c)})},Sj=function(a,b,c,d){z(c,function(e){var f=a.slice(e.row(),e.rowspan()+e.row());f=Qf(f,xd());N(e.element(),"height",f+d)});z(b,function(e,f){N(e.element(),"height",
a[f])})},Sf=function(a,b,c,d,e){var f=C.fromTable(a),g=f.all,h=C.justCells(f);b.each(function(k){var q=Rf(k).fold(m("px"),m("px"),m("%")),r=ea.get(a),p=Od(f,d,Hj,Lf,e);r=Pf(p,r,k);Rj(r,h,q);N(a,"width",k)});c.each(function(k){var q=Rf(k).fold(m("px"),m("px"),m("%")),r=sc(a),p=Nf(f,Lb,Ij,Lf);r=Pf(p,r,k);Sj(r,g,h,q);N(a,"height",k)})},Tf=function(a){return Za(a).exists(function(b){return tc.test(b)})},Uf=function(a){return Za(a).exists(function(b){return Nd.test(b)})},vc=function(a){return{delta:function(b,
c){return(a(c).isRtl()?Ld:Kd).delta(b,c)},edge:function(b){return(a(b).isRtl()?Ld:Kd).edge(b)},positions:function(b,c){return(a(c).isRtl()?Ld:Kd).positions(b,c)}}},Tj=function(){for(var a=[],b=0;b<arguments.length;b++)a[b]=arguments[b];return function(){for(var c=[],d=0;d<arguments.length;d++)c[d]=arguments[d];if(a.length!==c.length)throw Error('Wrong number of arguments to struct. Expected "['+a.length+']", got '+c.length+" arguments");var e={};z(a,function(f,g){e[f]=m(c[g])});return e}},ia=function(a){var b=
Tj.apply(null,a),c=[];return{bind:function(d){if(void 0===d)throw Error("Event bind error: undefined handler");c.push(d)},unbind:function(d){c=X(c,function(e){return e!==d})},trigger:function(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];var f=b.apply(null,d);z(c,function(g){g(f)})}}},gb=function(a){var b=Re(a,function(c){return{bind:c.bind,unbind:c.unbind}});a=Re(a,function(c){return c.trigger});return{registry:b,trigger:a}},Uj=Ca.generate([{none:[]},{only:["index"]},{left:["index",
"next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),lb=E({},Uj),Vj=function(a,b){return 0===a.length?lb.none():1===a.length?lb.only(0):0===b?lb.left(0,1):b===a.length-1?lb.right(b-1,b):0<b&&b<a.length-1?lb.middle(b-1,b,b+1):lb.none()},Wj=function(a,b,c,d){var e=a.slice(0);a=Vj(a,b);var f=function(h){return x(h,m(0))};b=m(f(e));var g=function(h,k){if(0<=c){var q=Math.max(d.minCellWidth(),e[k]-c);return f(e.slice(0,h)).concat([c,q-e[k]]).concat(f(e.slice(k+1)))}q=Math.max(d.minCellWidth(),
e[h]+c);var r=e[h]-q;return f(e.slice(0,h)).concat([q-e[h],r]).concat(f(e.slice(k+1)))};return a.fold(b,function(h){return d.singleColumnWidth(e[h],c)},g,function(h,k,q){return g(k,q)},function(h,k){if(0<=c)return f(e.slice(0,k)).concat([c]);h=Math.max(d.minCellWidth(),e[k]+c);return f(e.slice(0,k)).concat([h-e[k]])})},Vf=function(a,b,c){for(var d=0;a<b;a++)d+=void 0!==c[a]?c[a]:0;return d},Wf=function(a,b){a=C.justCells(a);return x(a,function(c){var d=Vf(c.column(),c.column()+c.colspan(),b);return{element:c.element(),
width:d,colspan:c.colspan()}})},Xj=function(a,b){a=C.justCells(a);return x(a,function(c){var d=Vf(c.row(),c.row()+c.rowspan(),b);return{element:c.element,height:m(d),rowspan:c.rowspan}})},Yj=function(a,b){return x(a.all,function(c,d){return{element:c.element,height:m(b[d])}})},Zj=function(a){return hb(a,function(b,c){return b+c},0)},ak=function(a,b,c,d,e){b=e.getCellDelta(b);a=C.fromTable(a);var f=e.getWidths(a,d,e);d=Wj(f,c,b,e);d=x(d,function(g,h){return g+f[h]});d=Wf(a,d);z(d,function(g){e.setElementWidth(g.element,
g.width)});c===a.grid.columns()-1&&e.adjustTableWidth(b)},bk=function(a,b,c,d){var e=C.fromTable(a);d=Kj(e,d);d=x(d,function(g,h){return c===h?Math.max(b+g,xd()):g});var f=Xj(e,d);e=Yj(e,d);z(e,function(g){uc(g.element(),g.height())});z(f,function(g){uc(g.element(),g.height())});e=Zj(d);uc(a,e)},ck=function(a,b,c,d,e,f,g){return{target:m(a),x:m(b),y:m(c),stop:d,prevent:e,kill:f,raw:m(g)}},Xf=function(a){var b=u.fromDom(zi(a).getOr(a.target)),c=function(){return a.stopPropagation()},d=function(){return a.preventDefault()},
e=Xh(d,c);return ck(b,a.clientX,a.clientY,c,d,e,a)},dk=function(a,b){return function(c){a(c)&&b(Xf(c))}},ek=function(a,b,c,d){a.dom().removeEventListener(b,c,d)},fk=m(!0),mb=function(a,b,c){c=dk(fk,c);a.dom().addEventListener(b,c,!1);return{unbind:F(ek,a,b,c,!1)}},zb=Xf,Pd=function(a,b){a=ma(a,b);return void 0===a||""===a?[]:a.split(" ")},gk=function(a,b,c){var d=X(Pd(a,b),function(e){return e!==c});0<d.length?Y(a,b,d.join(" ")):I(a,b);return!1},wc=function(a){return void 0!==a.dom().classList},Ea=
function(a,b){wc(a)?a.dom().classList.add(b):(b=Pd(a,"class").concat([b]),Y(a,"class",b.join(" ")))},Qd=function(a,b){return wc(a)&&a.dom().classList.contains(b)},Yf=function(a){var b=a.replace(/\./g,"-");return{resolve:function(c){return b+"-"+c}}},Zf=Yf("ephox-dragster").resolve,hk=function(a){a=E({layerClass:Zf("blocker")},a);var b=u.fromTag("div");Y(b,"role","presentation");Fb(b,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"});Ea(b,Zf("blocker"));Ea(b,a.layerClass);return{element:function(){return b},
destroy:function(){ba(b)}}},ik=function(a,b){throw Error("All required keys ("+a.slice(0).sort().join(", ")+") were not specified. Specified keys were: "+b.slice(0).sort().join(", ")+".");},jk=function(a,b){if(!fc(b))throw Error("The "+a+" fields must be an array. Was: "+b+".");z(b,function(c){if(!La(c))throw Error("The value "+c+" in the "+a+" fields was not a string.");})},kk=function(a){var b=a.slice(0).sort();na(b,function(c,d){return d<b.length-1&&c===b[d+1]}).each(function(c){throw Error("The field: "+
c+" occurs more than once in the combined fields: ["+b.join(", ")+"].");})},xc=function(a,b){return lk(a,b,{validate:Sa,label:"function"})},lk=function(a,b,c){if(0===b.length)throw Error("You must specify at least one required field.");jk("required",b);kk(b);return function(d){var e=ib(d);Bb(b,function(g){return P(e,g)})||ik(b,e);a(b,e);var f=X(b,function(g){return!c.validate(d[g],g)});if(0<f.length)throw Error("All values need to be of type: "+c.label+". Keys ("+f.slice(0).sort().join(", ")+") were not.");
return d}},yc=function(a,b){b=X(b,function(c){return!P(a,c)});if(0<b.length)throw Error("Unsupported keys for object: "+b.slice(0).sort().join(", "));},mk=xc(yc,["compare","extract","mutate","sink"]),nk=xc(yc,["element","start","stop","destroy"]),ok=xc(yc,["forceDrop","drop","move","delayDrop"]),pk=mk({compare:function(a,b){return Da(b.left()-a.left(),b.top()-a.top())},extract:function(a){return l.some(Da(a.x(),a.y()))},sink:function(a,b){var c=hk(b),d=mb(c.element(),"mousedown",a.forceDrop),e=mb(c.element(),
"mouseup",a.drop),f=mb(c.element(),"mousemove",a.move),g=mb(c.element(),"mouseout",a.delayDrop);return nk({element:c.element,start:function(h){K(h,c.element())},stop:function(){ba(c.element())},destroy:function(){c.destroy();e.unbind();f.unbind();g.unbind();d.unbind()}})},mutate:function(a,b){a.mutate(b.left(),b.top())}}),qk=function(a,b){var c=null;return{cancel:function(){null!==c&&(G.clearTimeout(c),c=null)},throttle:function(){for(var d=[],e=0;e<arguments.length;e++)d[e]=arguments[e];null!==c&&
G.clearTimeout(c);c=G.setTimeout(function(){a.apply(null,d);c=null},b)}}},rk=function(a,b,c){var d=!1,e=gb({start:ia([]),stop:ia([])}),f=Lh(),g=function(){q.stop();f.isOn()&&(f.off(),e.trigger.stop())},h=qk(g,200);f.events.move.bind(function(r){b.mutate(a,r.info())});var k=function(r){return function(){for(var p=[],n=0;n<arguments.length;n++)p[n]=arguments[n];d&&r.apply(null,p)}},q=b.sink(ok({forceDrop:g,drop:k(g),move:k(function(r){h.cancel();f.onEvent(r,b)}),delayDrop:k(h.throttle)}),c);return{element:q.element,
go:function(r){q.start(r);f.on();e.trigger.start()},on:function(){d=!0},off:function(){d=!1},destroy:function(){q.destroy()},events:e.registry}},sk=function(a,b){void 0===b&&(b={});return rk(a,void 0!==b.mode?b.mode:pk,b)},tk=function(a){return"true"===ma(a,"contenteditable")},zc=Yf("ephox-snooker").resolve,uk=function(){var a=gb({drag:ia(["xDelta","yDelta"])});return{mutate:function(b,c){a.trigger.drag(b,c)},events:a.registry}},vk=function(){var a=gb({drag:ia(["xDelta","yDelta","target"])}),b=l.none(),
c=uk();c.events.drag.bind(function(d){b.each(function(e){a.trigger.drag(d.xDelta(),d.yDelta(),e)})});return{assign:function(d){b=l.some(d)},get:function(){return b},mutate:c.mutate,events:a.registry}},Rd=zc("resizer-bar"),$f=zc("resizer-rows"),ag=zc("resizer-cols"),Sd=function(a){a=bd(a.parent(),"."+Rd);z(a,ba)},bg=function(a,b,c){var d=a.origin();z(b,function(e){e.each(function(f){f=c(d,f);Ea(f,Rd);K(a.parent(),f)})})},wk=function(a,b,c,d){bg(a,b,function(e,f){var g=f.col;f=f.x-e.left();e=c.top()-
e.top();var h=u.fromTag("div");Fb(h,{position:"absolute",left:f-3.5+"px",top:e+"px",height:d+"px",width:"7px"});yb(h,{"data-column":g,role:"presentation"});Ea(h,ag);return h})},xk=function(a,b,c,d){bg(a,b,function(e,f){var g=f.row,h=c.left()-e.left();e=f.y-e.top();f=u.fromTag("div");Fb(f,{position:"absolute",left:h+"px",top:e-3.5+"px",height:"7px",width:d+"px"});yb(f,{"data-row":g,role:"presentation"});Ea(f,$f);return f})},Ac=function(a,b,c,d){Sd(a);var e=C.fromTable(b),f=Cf(e),g=Bf(e);e=Kb(b);c=
0<f.length?c.positions(f,b):[];xk(a,c,e,ea.getOuter(b));d=0<g.length?d.positions(g,b):[];wk(a,d,e,Hd.getOuter(b))},cg=function(a,b){a=bd(a.parent(),"."+Rd);z(a,b)},yk=function(a){cg(a,function(b){N(b,"display","none")})},zk=function(a){cg(a,function(b){N(b,"display","block")})},Ak=zc("resizer-bar-dragging"),Bk=function(a,b,c){var d=vk(),e=sk(d,{}),f=l.none(),g=function(v,w){return l.from(ma(v,w))};d.events.drag.bind(function(v){g(v.target(),"data-row").each(function(w){w=lc(v.target(),"top");N(v.target(),
"top",w+v.yDelta()+"px")});g(v.target(),"data-column").each(function(w){w=lc(v.target(),"left");N(v.target(),"left",w+v.xDelta()+"px")})});var h=function(v,w){var A=lc(v,w);v=ka(v,"data-initial-"+w,0);return A-v};e.events.stop.bind(function(){d.get().each(function(v){f.each(function(w){g(v,"data-row").each(function(A){var H=h(v,"top");I(v,"data-initial-top");t.trigger.adjustHeight(w,H,parseInt(A,10))});g(v,"data-column").each(function(A){var H=h(v,"left");I(v,"data-initial-left");t.trigger.adjustWidth(w,
H,parseInt(A,10))});Ac(a,w,c,b)})})});var k=function(v,w){t.trigger.startAdjust();d.assign(v);Y(v,"data-initial-"+w,lc(v,w));Ea(v,Ak);N(v,"opacity","0.2");e.go(a.parent())},q=mb(a.parent(),"mousedown",function(v){Qd(v.target(),$f)&&k(v.target(),"top");Qd(v.target(),ag)&&k(v.target(),"left")}),r=function(v){return B(v,a.view())},p=function(v){return M(v,"table",r).filter(function(w){return M(w,"[contenteditable]",r).exists(tk)})},n=mb(a.view(),"mouseover",function(v){p(v.target()).fold(function(){Eb(v.target())&&
Sd(a)},function(w){f=l.some(w);Ac(a,w,c,b)})}),t=gb({adjustHeight:ia(["table","delta","row"]),adjustWidth:ia(["table","delta","column"]),startAdjust:ia([])});return{destroy:function(){q.unbind();n.unbind();e.destroy();Sd(a)},refresh:function(v){Ac(a,v,c,b)},on:e.on,off:e.off,hideBars:F(yk,a),showBars:F(zk,a),events:t.registry}},Ck={create:function(a,b,c){a=Bk(a,b,Lb);var d=gb({beforeResize:ia(["table"]),afterResize:ia(["table"]),startDrag:ia([])});a.events.adjustHeight.bind(function(e){var f=e.table();
d.trigger.beforeResize(f);var g=Lb.delta(e.delta(),f);bk(f,g,e.row(),Lb);d.trigger.afterResize(f)});a.events.startAdjust.bind(function(e){d.trigger.startDrag()});a.events.adjustWidth.bind(function(e){var f=e.table();d.trigger.beforeResize(f);var g=b.delta(e.delta(),f),h=c(f);ak(f,g,e.column(),b,h);d.trigger.afterResize(f)});return{on:a.on,off:a.off,hideBars:a.hideBars,showBars:a.showBars,destroy:a.destroy,events:d.registry}}},dg=function(a,b){return a.fire("newrow",{node:b})},eg=function(a,b){return a.fire("newcell",
{node:b})},Dk=function(a,b,c,d){a.fire("ObjectResized",{target:b,width:c,height:d})},Td={"border-collapse":"collapse",width:"100%"},Ek=function(a){return Nb(a)?(a=a.getBody().offsetWidth,E(E({},Td),{width:a+"px"})):Bc(a)?ji(Td,function(b,c){return"width"!==c}):Td},Fk={border:"1"},Ud=function(a){return a.getParam("table_sizing_mode","auto")},Vd=function(a){return a.getParam("table_default_attributes",Fk,"object")},Wd=function(a){return a.getParam("table_default_styles",Ek(a),"object")},Xd=function(a){return a.getParam("table_cell_advtab",
!0,"boolean")},Yd=function(a){return a.getParam("table_row_advtab",!0,"boolean")},ab=function(a){return a.getParam("table_advtab",!0,"boolean")},Ob=function(a){return a.getParam("table_style_by_css",!1,"boolean")},Cc=function(a){return"relative"===Ud(a)||!0===a.getParam("table_responsive_width")},Nb=function(a){return"fixed"===Ud(a)||!1===a.getParam("table_responsive_width")},Bc=function(a){return"responsive"===Ud(a)},fg=function(a){a=a.getParam("table_header_type","section","string");return P(["section",
"cells","sectionCells","auto"],a)?a:"section"},Ne=function(a){a=a.getParam("table_clone_elements");return La(a)?l.some(a.split(/[ ,]/)):Array.isArray(a)?l.some(a):l.none()},Gk=function(a){a=a.getParam("object_resizing",!0);return La(a)?"table"===a:a},Fa=function(a){return a.nodeName.toLowerCase()},Ja=function(a){return u.fromDom(a.getBody())},fd=function(a){return function(b){return B(b,Ja(a))}},ra=function(a){return/^\d+(\.\d+)?$/.test(a)?a+"px":a},nb=function(a){I(a,"data-mce-style");z(Ya(a),function(b){return I(b,
"data-mce-style")})},gg=function(a,b){a=a.dom.getStyle(b,"width")||a.dom.getAttrib(b,"width");return l.from(a).filter(ni)},Zd=function(a){return/^(\d+(\.\d+)?)%$/.test(a)},Hk={isRtl:m(!1)},Ik={isRtl:m(!0)},Ab=function(a){return"rtl"===("rtl"===va(a,"direction")?"rtl":"ltr")?Ik:Hk},fa=function(a){var b=a;return{get:function(){return b},set:function(c){b=c}}},hg=function(a){var b=function(){return ea.get(a)},c=m(0);return{width:b,pixelWidth:b,getWidths:Mf,getCellDelta:c,singleColumnWidth:m([0]),minCellWidth:c,
setElementWidth:y,adjustTableWidth:y,label:"none"}},ig=function(a,b){var c=fa(parseFloat(a)),d=fa(ea.get(b));return{width:c.get,pixelWidth:d.get,getWidths:Jj,getCellDelta:function(e){return e/d.get()*100},singleColumnWidth:function(e,f){return[100-e]},minCellWidth:function(){return mc()/d.get()*100},setElementWidth:Gf,adjustTableWidth:function(e){var f=c.get();e=f+e/100*f;Gf(b,e);c.set(e);d.set(ea.get(b))},label:"percent"}},$d=function(a,b){var c=fa(a),d=c.get;return{width:d,pixelWidth:d,getWidths:Mf,
getCellDelta:Q,singleColumnWidth:function(e,f){return[Math.max(mc(),e+f)-e]},minCellWidth:mc,setElementWidth:Ff,adjustTableWidth:function(e){e=d()+e;Ff(b,e);c.set(e)},label:"pixel"}},Dc={getTableSize:function(a){return Za(a).fold(function(){return hg(a)},function(b){var c=Fj().exec(b);null!==c?b=ig(c[1],a):(b=Gj().exec(b),null!==b?(b=parseInt(b[1],10),b=$d(b,a)):(b=ea.get(a),b=$d(b,a)));return b})},pixelSize:$d,percentageSize:ig,noneSize:hg},Ec=function(a,b){return Cc(a)?(a=gg(a,b.dom()).filter(Zd).getOrThunk(function(){return Jf(b)}),
Dc.percentageSize(a,b)):Nb(a)?Dc.pixelSize(ea.get(b),b):Dc.getTableSize(b)},Fc=function(a){I(a,"width")},Gc=function(a,b){var c=vc(Ab);a=Ec(a,b);var d=Jf(b);Sf(b,l.some(d),l.none(),c,a);Fc(b)},ae=function(a,b){var c=vc(Ab);a=Ec(a,b);var d=ea.get(b)+"px";Sf(b,l.some(d),l.none(),c,a);Fc(b)},jg=function(a){xa(a,"width");z(Ya(a),function(b){xa(b,"width");Fc(b)});Fc(a)},Jk=function(a){z(Ya(a),function(b){var c=va(b,"width");N(b,"width",c);I(b,"width")})},Kk=function(){var a=u.fromTag("div");Fb(a,{position:"static",
height:"0",width:"0",padding:"0",margin:"0",border:"0"});K(cf(u.fromDom(G.document)),a);return a},Lk=function(a,b){return a.inline?Gd.body(Ja(a),Kk()):Gd.only(u.fromDom(a.getDoc()))},Mk=function(a){var b=l.none(),c=l.none(),d=l.none(),e,f;a.on("init",function(){var g=vc(Ab),h=Lk(a);d=l.some(h);Gk(a)&&a.getParam("table_resize_bars",!0,"boolean")&&(g=Ck.create(h,g,function(k){return Ec(a,k)}),g.on(),g.events.startDrag.bind(function(k){b=l.some(a.selection.getRng())}),g.events.beforeResize.bind(function(k){k=
k.table().dom();var q=k.getBoundingClientRect().width,r=k.getBoundingClientRect().height;a.fire("ObjectResizeStart",{target:k,width:q,height:r})}),g.events.afterResize.bind(function(k){k=k.table();var q=k.dom();nb(k);b.each(function(r){a.selection.setRng(r);a.focus()});Dk(a,q,q.getBoundingClientRect().width,q.getBoundingClientRect().height);a.undoManager.add()}),c=l.some(g))});a.on("ObjectResizeStart",function(g){var h=g.target;if("TABLE"===h.nodeName){var k=u.fromDom(h);!Uf(k)&&Nb(a)?ae(a,k):!Tf(k)&&
Cc(a)&&Gc(a,k);e=g.width;f=gg(a,h).getOr("")}});a.on("ObjectResized",function(g){var h=g.target;if("TABLE"===h.nodeName){h=u.fromDom(h);if(""===f||!Zd(f)&&Bc(a))Gc(a,h);else if(Zd(f)){var k=parseFloat(f.replace("%",""));N(h,"width",g.width*k/e+"%")}else Jk(h);nb(h)}});a.on("SwitchMode",function(){c.each(function(g){a.mode.isReadOnly()?g.hideBars():g.showBars()})});return{lazyResize:function(){return c},lazyWire:function(){return d.getOr(Gd.only(u.fromDom(a.getBody())))},destroy:function(){c.each(function(g){g.destroy()});
d.each(function(g){a.inline&&ba(g.parent())})}}},Pb=function(a,b){return{element:m(a),offset:m(b)}},kg=function(a,b,c){return a.property().isText(b)&&0===a.property().getText(b).trim().length||a.property().isComment(b)?c(b).bind(function(d){return kg(a,d,c).orThunk(function(){return l.some(d)})}):l.none()},lg=function(a,b){return a.property().isText(b)?a.property().getText(b).length:a.property().children(b).length},mg=function(a,b){b=kg(a,b,a.query().prevSibling).getOr(b);if(a.property().isText(b))return Pb(b,
lg(a,b));var c=a.property().children(b);return 0<c.length?mg(a,c[c.length-1]):Pb(b,lg(a,b))},Nk=mg,Ok=$b(),ng=function(a,b){Ej(a).each(function(c){var d=c.width()/2,e=c.unit();N(a,"width",d+e);c=c.unit();N(b,"width",d+c)})},og=function(a){for(var b=[],c=function(e){b.push(e)},d=0;d<a.length;d++)a[d].each(c);return b},Pk=function(a,b,c){return a.isSome()&&b.isSome()?l.some(c(a.getOrDie(),b.getOrDie())):l.none()},Hc=function(a,b,c,d){c===d?I(a,b):Y(a,b,c)},Qk=function(a,b){var c=[],d=[],e=pd(gf(a,"caption,colgroup")).fold(function(){return F(cd,
a)},function(r){return F(Ha,r)}),f=function(r,p){var n=hf(a,p).getOrThunk(function(){var t=u.fromTag(p,Db(a).dom());"thead"===p?e(t):K(a,t);return t});vd(n);r=x(r,function(t){t.isNew()&&c.push(t.element());var v=t.element();vd(v);z(t.cells(),function(w){w.isNew()&&d.push(w.element());Hc(w.element(),"colspan",w.colspan(),1);Hc(w.element(),"rowspan",w.rowspan(),1);K(v,w.element())});return v});Ua(n,r)},g=function(r,p){0<r.length?f(r,p):hf(a,p).each(ba)},h=[],k=[],q=[];z(b,function(r){switch(r.section()){case "thead":h.push(r);
break;case "tbody":k.push(r);break;case "tfoot":q.push(r)}});g(h,"thead");g(k,"tbody");g(q,"tfoot");return{newRows:c,newCells:d}},Rk=function(a){return x(a,function(b){var c=zd(b.element(),!1);z(b.cells(),function(d){var e=Ib(d.element());Hc(e,"colspan",d.colspan(),1);Hc(e,"rowspan",d.rowspan(),1);K(c,e)});return c})},Ic=function(a,b){return kc(b,a.section())},pg=function(a,b){var c=a.cells();b=x(c,b);return kc(b,a.section())},ob=function(a,b){return a.cells()[b]},R=function(a,b){return ob(a,b).element()},
Ga=function(a){return a.cells().length},Sk=function(a,b){return x(a,function(c){return ob(c,b)})},qg=function(a,b){if(0===a.length)return 0;var c=a[0];return gc(a,function(d){return!b(c.element(),d.element())}).fold(function(){return a.length},function(d){return d})},Tk=function(a,b){var c=x(a,function(d){return x(d.cells(),function(){return!1})});return x(a,function(d,e){var f=oa(d.cells(),function(g,h){if(!1===c[e][h]){var k=a[e].cells().slice(h);k=qg(k,b);var q=Sk(a,h).slice(e);q=qg(q,b);for(var r=
e;r<e+q;r++)for(var p=h;p<h+k;p++)c[r][p]=!0;h=g.element();g=g.isNew();return[{element:m(h),rowspan:m(q),colspan:m(k),isNew:m(g)}]}return[]});return Di(f,d.section())})},Qb=function(a,b,c){for(var d=[],e=0;e<a.grid.rows();e++){for(var f=[],g=0;g<a.grid.columns();g++){var h=C.getAt(a,e,g).map(function(k){return da(k.element(),c)}).getOrThunk(function(){return da(b.gap(),!0)});f.push(h)}f=kc(f,a.all[e].section());d.push(f)}return d},Uk=function(a,b){var c=function(d){return Na(d,function(e){return ya(e.element()).map(function(f){var g=
ya(f).isNone();return da(f,g)})}).getOrThunk(function(){return da(b.row(),!0)})};return x(a,function(d){var e=c(d.details()),f=e.element(),g=d.details();d=d.section();e=e.isNew();return{element:m(f),cells:m(g),section:m(d),isNew:m(e)}})},Jc=function(a,b){a=Tk(a,B);return Uk(a,b)},Kc=function(a,b){return Na(a.all,function(c){return na(c.cells(),function(d){return B(b,d.element())})})},S=function(a,b,c,d,e){return function(f,g,h,k,q,r){var p=C.fromTable(g);return b(p,h).map(function(n){var t=Qb(p,k,
!1);n=a(t,n,B,e(k));t=Jc(n.grid(),k);return{grid:m(t),cursor:n.cursor}}).fold(function(){return l.none()},function(n){var t=Qk(g,n.grid()),v=l.from(r).getOrThunk(function(){return Dc.getTableSize(g)});c(g,n.grid(),q,v);d(g);Ac(f,g,Lb,q);return l.some({cursor:n.cursor,newRows:m(t.newRows),newCells:m(t.newCells)})})}},Lc=function(a,b){return Hb(b.element()).bind(function(c){return Kc(a,c)})},Mc=function(a,b){var c=x(b.selection(),function(d){return Hb(d).bind(function(e){return Kc(a,e)})});c=og(c);
return 0<c.length?l.some({cells:c,generators:b.generators,clipboard:b.clipboard}):l.none()},sa=function(a,b){b=x(b.selection(),function(c){return Hb(c).bind(function(d){return Kc(a,d)})});b=og(b);return 0<b.length?l.some(b):l.none()},rg=function(a,b,c,d){for(var e=!0,f=0;f<a.length;f++)for(var g=0;g<Ga(a[0]);g++){var h=R(a[f],g);h=c(h,b);if(!0===h&&!1===e){h=g;var k=da(d(),!0);a[f].cells()[h]=k}else!0===h&&(e=!1)}return a},Vk=function(a,b){return za(a,function(c,d){return Xa(c,function(e){return b(e.element(),
d.element())})?c:c.concat([d])},[])},Wk=function(a,b,c,d){0<b&&b<a[0].cells().length&&z(a,function(e){var f=e.cells()[b-1],g=e.cells()[b];c(g.element(),f.element())&&(f=da(d(),!0),e.cells()[b]=f)});return a},Xk=function(a,b,c,d){if(0<b&&b<a.length){var e=a[b-1].cells();e=Vk(e,c);z(e,function(f){for(var g=l.none(),h=function(q){for(var r=function(n){var t=a[q].cells()[n];c(t.element(),f.element())&&(g.isNone()&&(g=l.some(d())),g.each(function(v){v=da(v,!0);a[q].cells()[n]=v}))},p=0;p<Ga(a[0]);p++)r(p)},
k=b;k<a.length;k++)h(k)})}return a},pb=function(a){return{is:function(b){return a===b},isValue:L,isError:ca,getOr:m(a),getOrThunk:m(a),getOrDie:m(a),or:function(b){return pb(a)},orThunk:function(b){return pb(a)},fold:function(b,c){return c(a)},map:function(b){return pb(b(a))},mapError:function(b){return pb(a)},each:function(b){b(a)},bind:function(b){return b(a)},exists:function(b){return b(a)},forall:function(b){return b(a)},toOption:function(){return l.some(a)}}},Rb=function(a){return{is:ca,isValue:ca,
isError:L,getOr:Q,getOrThunk:function(b){return b()},getOrDie:function(){return Zh(String(a))()},or:function(b){return b},orThunk:function(b){return b()},fold:function(b,c){return b(a)},map:function(b){return Rb(a)},mapError:function(b){return Rb(b(a))},each:y,bind:function(b){return Rb(a)},exists:ca,forall:L,toOption:l.none}},sg={value:pb,error:Rb,fromOption:function(a,b){return a.fold(function(){return Rb(b)},pb)}},Yk=function(a,b,c){if(a.row()>=b.length||a.column()>Ga(b[0]))return sg.error("invalid start address out of table bounds, row: "+
a.row()+", column: "+a.column());b=b.slice(a.row());a=b[0].cells().slice(a.column());var d=Ga(c[0]);return sg.value({rowDelta:b.length-c.length,colDelta:a.length-d})},tg=function(a,b){a=Ga(a[0]);b=Ga(b[0]);return{rowDelta:0,colDelta:a-b}},ug=function(a,b){return x(a,function(){return da(b.cell(),!0)})},Zk=function(a,b,c){return a.concat(Ma(b,function(){return Ic(a[a.length-1],ug(a[a.length-1].cells(),c))}))},$k=function(a,b,c){return x(a,function(d){return Ic(d,d.cells().concat(ug(Ma(b,Q),c)))})},
Sb=function(a,b,c){var d=0>b.rowDelta?Zk:Q;a=(0>b.colDelta?$k:Q)(a,Math.abs(b.colDelta),c);return d(a,Math.abs(b.rowDelta),c)},al=function(a,b,c,d,e){return Yk(a,b,c).map(function(f){f=Sb(b,f,d);var g=a.row(),h=a.column(),k=c.length,q=Ga(c[0]);k=g+k;q=h+q;for(var r=g;r<k;r++)for(var p=h;p<q;p++){var n=f,t=r,v=p,w=e,A=ob(n[t],v);w=F(w,A.element());A=n[t];1<n.length&&1<Ga(A)&&(0<v&&w(R(A,v-1))||v<A.cells().length-1&&w(R(A,v+1))||0<t&&w(R(n[t-1],v))||t<n.length-1&&w(R(n[t+1],v)))&&rg(f,R(f[r],p),e,d.cell);
n=R(c[r-g],p-h);t=d.replace(n);n=p;t=da(t,!0);f[r].cells()[n]=t}return f})},vg=function(a,b,c,d,e){Wk(b,a,e,d.cell);var f=Sb(c,{rowDelta:c.length-b.length,colDelta:0},d);b=Sb(b,{rowDelta:b.length-f.length,colDelta:0},d);return x(b,function(g,h){h=g.cells().slice(0,a).concat(f[h].cells()).concat(g.cells().slice(a,g.cells().length));return Ic(g,h)})},wg=function(a,b,c,d,e){Xk(b,a,e,d.cell);e=tg(c,b);c=Sb(c,e,d);e=tg(b,c);b=Sb(b,e,d);return b.slice(0,a).concat(c).concat(b.slice(a,b.length))},xg=function(a,
b,c,d,e){var f=a.slice(0,b),g=a.slice(b);c=pg(a[c],function(h,k){return 0<b&&b<a.length&&d(R(a[b-1],k),R(a[b],k))?ob(a[b],k):da(e(h.element(),d),!0)});return f.concat([c]).concat(g)},yg=function(a,b,c,d,e){return x(a,function(f){var g=0<b&&b<Ga(f)&&d(R(f,b-1),R(f,b))?ob(f,b):da(e(R(f,c),d),!0),h=f.cells(),k=h.slice(0,b);h=h.slice(b);g=k.concat([g]).concat(h);return Ic(f,g)})},bl=function(a,b,c){a=x(a,function(d){var e=d.cells().slice(0,b).concat(d.cells().slice(c+1));return kc(e,d.section())});return X(a,
function(d){return 0<d.cells().length})},zg=function(a,b,c,d){var e=function(f){return Xa(b,function(g){return c(f.element(),g.element())})};return x(a,function(f){return pg(f,function(g){return e(g)?da(d(g.element(),c),!0):g})})},Ag=function(a,b,c,d){return void 0!==R(a[b],c)&&0<b&&d(R(a[b-1],c),R(a[b],c))},Bg=function(a,b,c){return 0<b&&c(R(a,b-1),R(a,b))},Cg=function(a,b,c,d){var e=oa(a,function(f,g){return Ag(a,g,b,c)||Bg(f,b,c)?[]:[ob(f,b)]});return zg(a,e,c,d)},Dg=function(a,b,c,d){var e=a[b],
f=oa(e.cells(),function(g,h){return Ag(a,b,h,c)||Bg(e,h,c)?[]:[g]});return zg(a,f,c,d)},be=xc(yc,["cell","row","replace","gap"]),cl=function(a){var b=ka(a,"colspan",1),c=ka(a,"rowspan",1);return{element:m(a),colspan:m(b),rowspan:m(c)}},T={modification:function(a,b){void 0===b&&(b=cl);be(a);var c=fa(l.none()),d=function(f){var g=b(f);g=a.cell(g);c.get().isNone()&&c.set(l.some(g));e=l.some({item:f,replacement:g});return g},e=l.none();return{getOrInit:function(f,g){return e.fold(function(){return d(f)},
function(h){return g(f,h.item)?h.replacement:d(f)})},cursor:c.get}},transform:function(a,b){return function(c){var d=fa(l.none());be(c);var e=[],f=function(h,k){return na(e,function(q){return k(q.item,h)})},g=function(h){var k=c.replace(h,b,{scope:a});e.push({item:h,sub:k});d.get().isNone()&&d.set(l.some(k));return k};return{replaceOrInit:function(h,k){return f(h,k).fold(function(){return g(h)},function(q){return k(h,q.item)?q.sub:g(h)})},cursor:d.get}}},merging:function(a){be(a);var b=fa(l.none());
return{combine:function(c){b.get().isNone()&&b.set(l.some(c));return function(){var d=a.cell({element:m(c),colspan:m(1),rowspan:m(1)});xa(d,"width");xa(c,"width");return d}},cursor:b.get}}},dl="body p div article aside figcaption figure footer header nav section ol ul table thead tfoot tbody caption tr td th h1 h2 h3 h4 h5 h6 blockquote pre address".split(" "),ce=$b(),de=function(a){a=ce.property().name(a);return P(dl,a)},el=function(a){a=ce.property().name(a);return P(["ol","ul"],a)},fl=function(a){var b=
function(f){return Bb(f,function(g){return"br"===D(g)||Ia(g)&&0===cc(g).trim().length})},c=function(f){return bc(f).map(function(g){return de(g)?!0:P(["br","img","hr","input"],ce.property().name(g))?"img"===D(g)?!1:!0:!1}).getOr(!1)},d=function(f){return yd(f).bind(function(g){var h=c(g);return ya(g).map(function(k){var q;(q=!0===h)||(q="li"===D(k)||xb(k,el).isSome());return q||"br"===D(g)||de(k)&&!B(f,k)?[]:[u.fromTag("br")]})}).getOr([])},e=function(){var f=oa(a,function(g){var h=W(g);return b(h)?
[]:h.concat(d(g))});return 0===f.length?[u.fromTag("br")]:f}();vd(a[0]);Ua(a[0],e)},Eg=function(a){0===Ya(a).length&&ba(a)},la=function(a,b){return{grid:m(a),cursor:m(b)}},bb=function(a,b,c){return ee(a,b,c).orThunk(function(){return ee(a,0,0)})},ee=function(a,b,c){return l.from(a[b]).bind(function(d){return l.from(d.cells()[c]).bind(function(e){return l.from(e.element())})})},Qa=function(a,b,c){return la(a,ee(a,b,c))},fe=function(a){return za(a,function(b,c){return Xa(b,function(d){return d.row()===
c.row()})?b:b.concat([c])},[]).sort(function(b,c){return b.row()-c.row()})},ge=function(a){return za(a,function(b,c){return Xa(b,function(d){return d.column()===c.column()})?b:b.concat([c])},[]).sort(function(b,c){return b.column()-c.column()})},Nc=function(a,b,c){a=Ni(a,c);a=C.generate(a);return Qb(a,b,!0)},Fg=function(a,b){b=X(a,b);return 0===b.length?l.some("td"):b.length===a.length?l.some("th"):l.none()},Tb=function(a,b,c,d){a=C.generate(b);c=d.getWidths(a,c,d);c=Wf(a,c);z(c,function(e){d.setElementWidth(e.element,
e.width)})},gl=S(function(a,b,c,d){var e=b[0].row(),f=b[0].row(),g=fe(b);a=za(g,function(h,k){return xg(h,f,e,c,d.getOrInit)},a);return Qa(a,f,b[0].column())},sa,y,y,T.modification),hl=S(function(a,b,c,d){var e=fe(b),f=e[e.length-1].row(),g=e[e.length-1].row()+e[e.length-1].rowspan();a=za(e,function(h,k){return xg(h,g,f,c,d.getOrInit)},a);return Qa(a,g,b[0].column())},sa,y,y,T.modification),il=S(function(a,b,c,d){var e=ge(b),f=e[0].column(),g=e[0].column();a=za(e,function(h,k){return yg(h,g,f,c,d.getOrInit)},
a);return Qa(a,b[0].row(),g)},sa,Tb,y,T.modification),jl=S(function(a,b,c,d){var e=b[b.length-1].column(),f=b[b.length-1].column()+b[b.length-1].colspan(),g=ge(b);a=za(g,function(h,k){return yg(h,f,e,c,d.getOrInit)},a);return Qa(a,b[0].row(),f)},sa,Tb,y,T.modification),kl=S(function(a,b,c,d){c=ge(b);a=bl(a,c[0].column(),c[c.length-1].column());b=bb(a,b[0].row(),b[0].column());return la(a,b)},sa,Tb,Eg,T.modification),ll=S(function(a,b,c,d){d=fe(b);c=d[0].row();d=d[d.length-1].row();a=a.slice(0,c).concat(a.slice(d+
1));b=bb(a,b[0].row(),b[0].column());return la(a,b)},sa,y,Eg,T.modification),ml=S(function(a,b,c,d){a=Cg(a,b.column(),c,d.replaceOrInit);return Qa(a,b.row(),b.column())},Lc,y,y,T.transform("row","th")),nl=S(function(a,b,c,d){a=Cg(a,b.column(),c,d.replaceOrInit);return Qa(a,b.row(),b.column())},Lc,y,y,T.transform(null,"td"));S(function(a,b,c,d){a=Dg(a,b.row(),c,d.replaceOrInit);return Qa(a,b.row(),b.column())},Lc,y,y,T.transform("col","th"));S(function(a,b,c,d){a=Dg(a,b.row(),c,d.replaceOrInit);return Qa(a,
b.row(),b.column())},Lc,y,y,T.transform(null,"td"));var ol=S(function(a,b,c,d){c=b.cells();fl(c);b=b.bounds();d=m(c[0]);if(0!==a.length)for(var e=b.startRow();e<=b.finishRow();e++)for(var f=b.startCol();f<=b.finishCol();f++){var g=f,h=da(d(),!1);a[e].cells()[g]=h}return la(a,l.from(c[0]))},function(a,b){return b.mergable()},y,y,T.merging),pl=S(function(a,b,c,d){a=hb(b,function(e,f){return rg(e,f,c,d.combine(f))},a);return la(a,l.from(b[0]))},function(a,b){return b.unmergable()},Tb,y,T.merging),ql=
S(function(a,b,c,d){d=function(f,g){f=C.fromTable(f);return Qb(f,g,!0)}(b.clipboard(),b.generators());var e=Ci(b.row(),b.column());return al(e,a,d,b.generators(),c).fold(function(){return la(a,l.some(b.element()))},function(f){var g=bb(f,b.row(),b.column());return la(f,g)})},function(a,b){return Hb(b.element()).bind(function(c){return Kc(a,c).map(function(d){return E(E({},d),{generators:b.generators,clipboard:b.clipboard})})})},Tb,y,T.modification),rl=S(function(a,b,c,d){var e=a[b.cells[0].row()];
d=b.cells[0].column();e=Nc(b.clipboard(),b.generators(),e);a=vg(d,a,e,b.generators(),c);b=bb(a,b.cells[0].row(),b.cells[0].column());return la(a,b)},Mc,y,y,T.modification),sl=S(function(a,b,c,d){var e=a[b.cells[0].row()];d=b.cells[b.cells.length-1].column()+b.cells[b.cells.length-1].colspan();e=Nc(b.clipboard(),b.generators(),e);a=vg(d,a,e,b.generators(),c);b=bb(a,b.cells[0].row(),b.cells[0].column());return la(a,b)},Mc,y,y,T.modification),tl=S(function(a,b,c,d){var e=a[b.cells[0].row()];d=b.cells[0].row();
e=Nc(b.clipboard(),b.generators(),e);a=wg(d,a,e,b.generators(),c);b=bb(a,b.cells[0].row(),b.cells[0].column());return la(a,b)},Mc,y,y,T.modification),ul=S(function(a,b,c,d){var e=a[b.cells[0].row()];d=b.cells[b.cells.length-1].row()+b.cells[b.cells.length-1].rowspan();e=Nc(b.clipboard(),b.generators(),e);a=wg(d,a,e,b.generators(),c);b=bb(a,b.cells[0].row(),b.cells[0].column());return la(a,b)},Mc,y,y,T.modification),vl=function(a,b){var c=C.fromTable(a);return sa(c,b).bind(function(d){var e=d[d.length-
1],f=d[0].column(),g=e.column()+e.colspan();d=od(x(c.all,function(h){return X(h.cells(),function(k){return k.column()>=f&&k.column()<g})}));return Fg(d,function(h){return"th"===D(h.element())})}).getOr("")},wl=function(a){return"thead"===a?"header":"tfoot"===a?"footer":"body"},Gg=function(a,b){a="thead"===Fa(b.parentNode);b=!Xa(b.cells,function(c){return"th"!==Fa(c)});return a||b?l.some({thead:a,ths:b}):l.none()},Hg=function(a,b){return wl(Gg(a,b).fold(function(){return Fa(b.parentNode)},function(c){return"thead"}))},
Ig=function(a,b,c){var d=a.getParent(b,"table"),e=b.parentNode,f=Fa(e);if(c!==f){var g=a.select(c,d)[0];if(!g){g=a.create(c);var h=d.firstChild;"thead"===c?pd(gf(u.fromDom(d),"caption,colgroup")).fold(function(){return d.insertBefore(g,h)},function(k){return a.insertAfter(g,k.dom())}):d.appendChild(g)}"tbody"===c&&"thead"===f&&g.firstChild?g.insertBefore(b,g.firstChild):g.appendChild(b);e.hasChildNodes()||a.remove(e)}},he=function(a,b,c,d){return z(b,function(e){e=Fa(e)!==c?a.rename(e,c):e;a.setAttrib(e,
"scope",d)})},Jg=function(a,b,c){var d=function(){var f=J(u.fromDom(b.cells[0])).map(function(g){return Oa(g,"tr",m(!0))}).getOr([]);return Na(f,function(g){return Gg(a,g.dom())}).map(function(g){return g.thead&&g.ths?"sectionCells":g.thead?"section":"cells"}).getOr("section")},e=a.dom;"header"===c?(c=fg(a),d="auto"===c?d():c,he(e,b.cells,"section"===d?"td":"th","col"),Ig(e,b,"cells"===d?"tbody":"thead")):(he(e,b.cells,"td",null),Ig(e,b,"footer"===c?"tfoot":"tbody"))},Kg=function(a){return{get:function(){var b=
Ja(a);return Dd(b,"td[data-mce-selected],th[data-mce-selected]").fold(function(){return void 0===a.selection.getStart()?nj():pj(a.selection)},function(c){return oj(c)})}}},Lg=function(a){return function(b){return l.from(b.dom.getParent(b.selection.getStart(),a)).map(function(c){return u.fromDom(c)})}},cb=Lg("th,td"),db=Lg("th,td,caption"),Oc=function(a){return cb(a).map(function(b){return qc(b,Kg(a))}).map(function(b){return x(b,function(c){return c.dom()})}).getOr([])},ie=function(a){var b=cb(a),
c=b.bind(function(d){return J(d)}).map(function(d){return Oa(d,"tr",m(!0))}).map(function(d){return x(d,function(e){return e.dom()})});return Pk(b,c,function(d,e){return X(e,function(f){return Xa(f.cells,function(g){return"1"===a.dom.getAttrib(g,"data-mce-selected")||g===d.dom()})})}).getOr([])},Bl=function(a,b){var c=Ne(a),d=function(O,V,aa,eb){return function(qb,xl){nb(qb);var yl=eb(),je=u.fromDom(a.getDoc()),zl=vc(Ab);je=ed(aa,je,c);var Al=Ec(a,qb);return V(qb)?O(yl,qb,xl,je,zl,Al).bind(function(ke){z(ke.newRows(),
function(ta){dg(a,ta.dom())});z(ke.newCells(),function(ta){eg(a,ta.dom())});return ke.cursor().map(function(ta){ta=Nk(Ok,ta);var le=a.dom.createRng();le.setStart(ta.element().dom(),ta.offset());le.setEnd(ta.element().dom(),ta.offset());return le})}):l.none()}},e=d(ll,function(O){return"table"===D(Ja(a))===!1||1<C.fromTable(O).grid.rows()},y,b),f=d(kl,function(O){return"table"===D(Ja(a))===!1||1<C.fromTable(O).grid.columns()},y,b),g=d(gl,L,y,b),h=d(hl,L,y,b),k=d(il,L,ng,b),q=d(jl,L,ng,b),r=d(ol,L,
y,b),p=d(pl,L,y,b),n=d(rl,L,y,b),t=d(sl,L,y,b),v=d(tl,L,y,b),w=d(ul,L,y,b),A=d(ql,L,y,b),H=function(O,V){return pa(O,"type").filter(function(aa){return P(V,aa)})},Ub=d(ml,L,y,b);b=d(nl,L,y,b);return{deleteRow:e,deleteColumn:f,insertRowsBefore:g,insertRowsAfter:h,insertColumnsBefore:k,insertColumnsAfter:q,mergeCells:r,unmergeCells:p,pasteColsBefore:n,pasteColsAfter:t,pasteRowsBefore:v,pasteRowsAfter:w,pasteCells:A,setTableCellType:function(O,V){return H(V,["td","th"]).each(function(aa){he(O.dom,Oc(O),
aa,null)})},setTableRowType:function(O,V){return H(V,["header","body","footer"]).each(function(aa){x(ie(O),function(eb){return Jg(O,eb,aa)})})},makeColumnHeader:Ub,unmakeColumnHeader:b,getTableRowType:function(O){var V=ie(O);if(0<V.length){var aa=x(V,function(qb){return Hg(O,qb)});V=P(aa,"header");var eb=P(aa,"footer");return V||eb?(aa=P(aa,"body"),!V||aa||eb?V||aa||!eb?"":"footer":"header"):"body"}},getTableCellType:function(O){return Fg(Oc(O),function(V){return"th"===Fa(V)}).getOr("")},getTableColType:vl}},
Cl={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"}},Mg=function(a,b,c,d){return Ma(a,function(e){for(var f=u.fromTag("tr"),g=0;g<b;g++){var h=e<c||g<d?u.fromTag("th"):u.fromTag("td");g<d&&Y(h,"scope","row");e<c&&Y(h,"scope","col");K(h,u.fromTag("br"));K(f,h)}return f})},Dl=function(a,b,c,d,e,f){void 0===f&&(f=Cl);var g=u.fromTag("table"),h="cells"!==e;Fb(g,f.styles);yb(g,f.attributes);f=Math.min(a,c);if(h&&0<c){var k=u.fromTag("thead");K(g,k);e=Mg(c,b,"sectionCells"===
e?f:0,d);Ua(k,e)}e=u.fromTag("tbody");K(g,e);a=Mg(h?a-f:a,b,h?0:c,d);Ua(e,a);return g},El=function(a){var b=u.fromTag("div");a=u.fromDom(a.dom().cloneNode(!0));K(b,a);return b.dom().innerHTML},Fl=function(a,b){a.selection.select(b.dom(),!0);a.selection.collapse(!0)},Gl=function(a,b){z(Ba("tr",b),function(c){dg(a,c.dom());z(Ba("th,td",c),function(d){eg(a,d.dom())})})},me=function(a,b,c,d,e){var f=Wd(a),g={styles:f,attributes:Vd(a)};b=Dl(c,b,e,d,fg(a),g);Y(b,"data-mce-id","__mce");b=El(b);a.insertContent(b);
return Ji(Ja(a),'table[data-mce-id="__mce"]').map(function(h){if(Nb(a))ae(a,h);else if(Bc(a))jg(h);else{var k;(k=Cc(a))||(k=f.width,k=La(k)&&-1!==k.indexOf("%"));k&&Gc(a,h)}nb(h);I(h,"data-mce-id");Gl(a,h);kb("td,th",h).each(F(Fl,a));return h.dom()}).getOr(null)},Ng=function(a,b,c,d,e){void 0===d&&(d={});var f=function(g){return dd(g)&&0<g};if(f(b)&&f(c))return me(a,c,b,d.headerColumns||0,d.headerRows||0);console.error(e);return null},Og=function(a){return function(){return a().fold(function(){return[]},
function(b){return x(b,function(c){return c.dom()})})}},Pg=function(a){return function(b){b=0<b.length?l.some(x(b,u.fromDom)):l.none();a(b)}},Hl=function(a,b,c,d){return{insertTable:function(e,f,g){void 0===g&&(g={});return Ng(a,f,e,g,"Invalid values for insertTable - rows and columns values are required to insert a table.")},setClipboardRows:Pg(b.setRows),getClipboardRows:Og(b.getRows),setClipboardCols:Pg(b.setColumns),getClipboardCols:Og(b.getColumns),resizeHandler:c,selectionTargets:d}},Il=function(a,
b){var c=C.fromTable(a);return sa(c,b).map(function(d){var e=d[d.length-1],f=d[0].column(),g=e.column()+e.colspan();return x(c.all,function(h){h=X(h.cells(),function(q){return q.column()>=f&&q.column()<g});h=x(h,function(q){q=Ib(q.element());var r=g-f,p=ka(q,"colspan",1);1===r||1>=p?I(q,"colspan"):Y(q,"colspan",Math.min(r,p));return q});var k=u.fromTag("tr");Ua(k,h);return k})})},Jl=function(a,b,c){var d=C.fromTable(a);return sa(d,b).map(function(e){e=Qb(d,c,!1).slice(e[0].row(),e[e.length-1].row()+
e[e.length-1].rowspan());e=Jc(e,c);return Rk(e)})},Qg=tinymce.util.Tools.resolve("tinymce.util.Tools"),ne=function(a,b,c){b=a.select("td,th",b);a:{for(var d=void 0,e=0;e<b.length;e++){var f=a.getStyle(b[e],c);"undefined"===typeof d&&(d=f);if(d!==f){a="";break a}}a=d}return a},oe=function(a,b,c){c&&a.formatter.apply("align"+c,{},b)},pe=function(a,b){Qg.each(["left","center","right"],function(c){a.formatter.remove("align"+c,{},b)})},Kl=function(a,b){Qg.each(["top","middle","bottom"],function(c){a.formatter.remove("valign"+
c,{},b)})},qe=function(a,b){return function(c,d){return d.concat(x(c,function(e){return{text:e.text||e.title,value:e.value}}))}(a,b||[])},Pc=function(a){return function(b){return Ue(b,"rgb",0)?a.toHex(b):b}},re=function(a,b){b=u.fromDom(b);return{borderwidth:wa(b,"border-width").getOr(""),borderstyle:wa(b,"border-style").getOr(""),bordercolor:wa(b,"border-color").map(Pc(a)).getOr(""),backgroundcolor:wa(b,"background-color").map(Pc(a)).getOr("")}},Rg=function(a){var b=a[0];a=a.slice(1);z(a,function(c){z(ib(b),
function(d){ha(c,function(e,f){var g=b[d];""!==g&&d===f&&g!==e&&(b[d]="")})})});return b},se=function(a){var b=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},
{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}],c={name:"borderwidth",type:"input",label:"Border width"};return{title:"Advanced",name:"advanced",items:"cell"===a?[c].concat(b):b}},Sg=function(a,b,c,d){return na(a,function(e){return c.formatter.matchNode(d,b+e)}).getOr("")},te=F(Sg,["left","center","right"],"align"),Ll=F(Sg,["top","middle","bottom"],"valign"),Ml=function(a,b){var c=Wd(a),d=Vd(a),e=function(f){return{borderstyle:pa(c,
"border-style").getOr(""),bordercolor:Pc(f)(pa(c,"border-color").getOr("")),backgroundcolor:Pc(f)(pa(c,"background-color").getOr(""))}};b=b?e(a.dom):{};return E(E(E(E(E(E({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:""}),c),d),b),function(){var f=c["border-width"];return Ob(a)&&f?{border:f}:pa(d,"border").fold(function(){return{}},function(g){return{border:g}})}()),function(){var f=pa(c,"border-spacing").or(pa(d,"cellspacing")).fold(function(){return{}},
function(h){return{cellspacing:h}}),g=pa(c,"border-padding").or(pa(d,"cellpadding")).fold(function(){return{}},function(h){return{cellpadding:h}});return E(E({},f),g)}())},Nl=function(a){a=qe(a.getParam("table_cell_class_list",[],"array"));return 0<a.length?l.some({name:"class",type:"selectbox",label:"Class",items:a}):l.none()},Tg=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},
{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},
{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],Ug=function(a){return Nl(a).fold(function(){return Tg},function(b){return Tg.concat(b)})},Vg=function(a){return function(b,c){var d=b.dom;return{setAttrib:function(e,f){a&&!f||d.setAttrib(c,e,f)},setStyle:function(e,f){a&&!f||d.setStyle(c,e,f)},setFormat:function(e,f){if(!a||f)""===f?b.formatter.remove(e,{value:null},c,!0):b.formatter.apply(e,{value:f},c)}}}},Vb={normal:Vg(!1),ifTruthy:Vg(!0)},Ol=function(a,b,c){var d=a.dom,e=1===b.length;
z(b,function(f){f=c.celltype&&Fa(f)!==c.celltype?d.rename(f,c.celltype):f;var g=e?Vb.normal(a,f):Vb.ifTruthy(a,f);g.setAttrib("scope",c.scope);g.setAttrib("class",c.class);g.setStyle("width",ra(c.width));g.setStyle("height",ra(c.height));Xd(a)&&(g.setFormat("tablecellbackgroundcolor",c.backgroundcolor),g.setFormat("tablecellbordercolor",c.bordercolor),g.setFormat("tablecellborderstyle",c.borderstyle),g.setFormat("tablecellborderwidth",ra(c.borderwidth)));e&&(pe(a,f),Kl(a,f));c.halign&&oe(a,f,c.halign);
c.valign&&(g=c.valign)&&a.formatter.apply("valign"+g,{},f)})},Pl=function(a,b,c){var d=c.getData();c.close();a.undoManager.transact(function(){Ol(a,b,d);a.focus()})},Ql=function(a){var b=Oc(a);if(0!==b.length){var c=x(b,function(f){var g=Xd(a),h=a.dom;return E({width:h.getStyle(f,"width")||h.getAttrib(f,"width"),height:h.getStyle(f,"height")||h.getAttrib(f,"height"),scope:h.getAttrib(f,"scope"),celltype:Fa(f),class:h.getAttrib(f,"class",""),halign:te(a,f),valign:Ll(a,f)},g?re(h,f):{})});c=Rg(c);var d=
{type:"tabpanel",tabs:[{title:"General",name:"general",items:Ug(a)},se("cell")]},e={type:"panel",items:[{type:"grid",columns:2,items:Ug(a)}]};a.windowManager.open({title:"Cell Properties",size:"normal",body:Xd(a)?d:e,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:c,onSubmit:F(Pl,a,b)})}},Rl=function(a){a=qe(a.getParam("table_row_class_list",[],"array"));return 0<a.length?l.some({name:"class",type:"selectbox",label:"Class",items:a}):
l.none()},Wg=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Xg=function(a){return Rl(a).fold(function(){return Wg},function(b){return Wg.concat(b)})},Sl=function(a,b,c,d){var e=1===b.length;
z(b,function(f){d.type!==Fa(f.parentNode)&&Jg(a,f,d.type);var g=e?Vb.normal(a,f):Vb.ifTruthy(a,f);g.setAttrib("scope",d.scope);g.setAttrib("class",d.class);g.setStyle("height",ra(d.height));Yd(a)&&(g.setStyle("background-color",d.backgroundcolor),g.setStyle("border-color",d.bordercolor),g.setStyle("border-style",d.borderstyle));d.align!==c.align&&(pe(a,f),oe(a,f,d.align))})},Tl=function(a,b,c,d){var e=d.getData();d.close();a.undoManager.transact(function(){Sl(a,b,c,e);a.focus()})},Ul=function(a){var b=
ie(a);if(0!==b.length){var c=x(b,function(f){var g=Yd(a),h=a.dom;return E({height:h.getStyle(f,"height")||h.getAttrib(f,"height"),scope:h.getAttrib(f,"scope"),class:h.getAttrib(f,"class",""),type:Hg(a,f),align:te(a,f)},g?re(h,f):{})});c=Rg(c);var d={type:"tabpanel",tabs:[{title:"General",name:"general",items:Xg(a)},se("row")]},e={type:"panel",items:[{type:"grid",columns:2,items:Xg(a)}]};a.windowManager.open({title:"Row Properties",size:"normal",body:Yd(a)?d:e,buttons:[{type:"cancel",name:"cancel",
text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:c,onSubmit:F(Tl,a,b,c)})}},Oe=tinymce.util.Tools.resolve("tinymce.Env"),ue=function(a,b,c,d){if("TD"===b.tagName||"TH"===b.tagName)La(c)?a.setStyle(b,c,d):a.setStyle(b,c);else if(b.children)for(var e=0;e<b.children.length;e++)ue(a,b.children[e],c,d)},Vl=function(a,b,c){var d=a.dom,e,f=c.getData();c.close();""===f.class&&delete f.class;a.undoManager.transact(function(){if(!b){var g=parseInt(f.cols,10)||1,h=parseInt(f.rows,
10)||1;b=me(a,g,h,0,0)}g=b;h=a.dom;var k={},q={};k.class=f.class;q.height=ra(f.height);if(h.getAttrib(g,"width")&&!Ob(a)){var r=(r=f.width)?r.replace(/px$/,""):"";k.width=r}else q.width=ra(f.width);Ob(a)?(q["border-width"]=ra(f.border),q["border-spacing"]=ra(f.cellspacing)):(k.border=f.border,k.cellpadding=f.cellpadding,k.cellspacing=f.cellspacing);if(Ob(a)&&g.children)for(r=0;r<g.children.length;r++)ue(h,g.children[r],{"border-width":ra(f.border),padding:ra(f.cellpadding)}),ab(a)&&ue(h,g.children[r],
{"border-color":f.bordercolor});ab(a)&&(q["background-color"]=f.backgroundcolor,q["border-color"]=f.bordercolor,q["border-style"]=f.borderstyle);k.style=h.serializeStyle(E(E({},Wd(a)),q));h.setAttribs(g,E(E({},Vd(a)),k));(e=d.select("caption",b)[0])&&!f.caption&&d.remove(e);!e&&f.caption&&(e=d.create("caption"),e.innerHTML=Oe.ie?"\u00a0":'<br data-mce-bogus="1"/>',b.insertBefore(e,b.firstChild));""===f.align?pe(a,b):oe(a,b,f.align);a.focus();a.addVisual()})},Yg=function(a,b){var c=a.dom,d,e=Ml(a,
ab(a));if(!1===b)if(d=c.getParent(a.selection.getStart(),"table")){e=d;c=ab(a);var f=a.dom,g=E,h=f.getStyle(e,"width")||f.getAttrib(e,"width"),k=f.getStyle(e,"height")||f.getAttrib(e,"height"),q=f.getStyle(e,"border-spacing")||f.getAttrib(e,"cellspacing"),r=f.getAttrib(e,"cellpadding")||ne(a.dom,e,"padding");var p=wa(u.fromDom(e),"border-width");p=Ob(a)&&p.isSome()?p.getOr(""):f.getAttrib(e,"border")||ne(a.dom,e,"border-width")||ne(a.dom,e,"border");e=g({width:h,height:k,cellspacing:q,cellpadding:r,
border:p,caption:!!f.select("caption",e)[0],class:f.getAttrib(e,"class",""),align:te(a,e)},c?re(f,e):{})}else ab(a)&&(e.borderstyle="",e.bordercolor="",e.backgroundcolor="");else e.cols="1",e.rows="1",ab(a)&&(e.borderstyle="",e.bordercolor="",e.backgroundcolor="");c=qe(a.getParam("table_class_list",[],"array"));0<c.length&&e.class&&(e.class=e.class.replace(/\s*mce\-item\-table\s*/g,""));b=b?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:
[];f=a.getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[];c=0<c.length?[{type:"selectbox",name:"class",label:"Class",items:c}]:[];b={type:"grid",columns:2,items:b.concat([{type:"input",name:"width",label:"Width"},
{type:"input",name:"height",label:"Height"}]).concat(f).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(c)};b=ab(a)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[b]},se("table")]}:{type:"panel",items:[b]};a.windowManager.open({title:"Table Properties",size:"normal",body:b,onSubmit:F(Vl,a,d),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",
name:"save",text:"Save",primary:!0}],initialData:e})},Wl=function(a,b,c,d,e){var f=fd(a),g=function(p){return db(a).each(function(n){Bc(a)||Nb(a)||Cc(a)||J(n,f).each(function(t){"relative"!==p||Tf(t)?"fixed"!==p||Uf(t)?"responsive"!==p||Za(t).isNone()||jg(t):ae(a,t):Gc(a,t);nb(t)})})},h=function(p){return cb(a).each(function(n){J(n,f).each(function(t){var v=Jb(d,t,n);p(t,v).each(function(w){a.selection.setRng(w);a.focus();c.clear(t);nb(t)})})})},k=function(){return cb(a).map(function(p){return J(p,
f).bind(function(n){var t=Jb(d,n,p),v=ed(y,u.fromDom(a.getDoc()),l.none());return Jl(n,t,v)})})},q=function(){return cb(a).map(function(p){return J(p,f).bind(function(n){var t=Jb(d,n,p);return Il(n,t)})})},r=function(p,n){return n().each(function(t){var v=x(t,function(w){return Ib(w)});cb(a).each(function(w){return J(w,f).each(function(A){var H=of(u.fromDom(a.getDoc()));H=tj(d,w,v,H);p(A,H).each(function(Ub){a.selection.setRng(Ub);a.focus();c.clear(A)})})})})};ha({mceTableSplitCells:function(){return h(b.unmergeCells)},
mceTableMergeCells:function(){return h(b.mergeCells)},mceTableInsertRowBefore:function(){return h(b.insertRowsBefore)},mceTableInsertRowAfter:function(){return h(b.insertRowsAfter)},mceTableInsertColBefore:function(){return h(b.insertColumnsBefore)},mceTableInsertColAfter:function(){return h(b.insertColumnsAfter)},mceTableDeleteCol:function(){return h(b.deleteColumn)},mceTableDeleteRow:function(){return h(b.deleteRow)},mceTableCutCol:function(p){return q().each(function(n){e.setColumns(n);h(b.deleteColumn)})},
mceTableCutRow:function(p){return k().each(function(n){e.setRows(n);h(b.deleteRow)})},mceTableCopyCol:function(p){return q().each(function(n){return e.setColumns(n)})},mceTableCopyRow:function(p){return k().each(function(n){return e.setRows(n)})},mceTablePasteColBefore:function(p){return r(b.pasteColsBefore,e.getColumns)},mceTablePasteColAfter:function(p){return r(b.pasteColsAfter,e.getColumns)},mceTablePasteRowBefore:function(p){return r(b.pasteRowsBefore,e.getRows)},mceTablePasteRowAfter:function(p){return r(b.pasteRowsAfter,
e.getRows)},mceTableDelete:function(){return db(a).each(function(p){J(p,f).filter(Wa(f)).each(function(n){var t=u.fromText("");Ha(n,t);ba(n);a.dom.isEmpty(a.getBody())?(a.setContent(""),a.selection.setCursorLocation()):(n=a.dom.createRng(),n.setStart(t.dom(),0),n.setEnd(t.dom(),0),a.selection.setRng(n),a.nodeChanged())})})},mceTableAlignLeft:function(){db(a).each(function(p){J(p,f).filter(Wa(f)).each(function(n){var t=u.fromText("");Ha(n,t);a.formatter.apply("alignleft",{},n.dom());a.dom.isEmpty(a.getBody())?
(a.setContent(""),a.selection.setCursorLocation()):(n=a.dom.createRng(),n.setStart(t.dom(),0),n.setEnd(t.dom(),0),a.selection.setRng(n),a.nodeChanged())})})},mceTableAlignCenter:function(){db(a).each(function(p){J(p,f).filter(Wa(f)).each(function(n){var t=u.fromText("");Ha(n,t);a.dom.setStyle(n.dom(),"float","none");console.log(a.dom);a.formatter.apply("aligncenter",{},n.dom());a.dom.isEmpty(a.getBody())?(a.setContent(""),a.selection.setCursorLocation()):(n=a.dom.createRng(),n.setStart(t.dom(),0),
n.setEnd(t.dom(),0),a.selection.setRng(n),a.nodeChanged())})})},mceTableAlignRight:function(){db(a).each(function(p){J(p,f).filter(Wa(f)).each(function(n){var t=u.fromText("");Ha(n,t);a.formatter.apply("alignright",{},n.dom());a.dom.isEmpty(a.getBody())?(a.setContent(""),a.selection.setCursorLocation()):(n=a.dom.createRng(),n.setStart(t.dom(),0),n.setEnd(t.dom(),0),a.selection.setRng(n),a.nodeChanged())})})},mceTableToImg:function(){db(a).each(function(p){J(p,f).filter(Wa(f)).each(function(n){var t=
u.fromText("");Ha(n,t);a.dom.setStyle(n.dom(),"width",n.dom().offsetWidth);a.dom.setStyle(n.dom(),"height",n.dom().offsetHeight);Ch.toBlob(n.dom()).then(function(v){var w=a.getParam("images_upload_handler",void 0,"function");v.lastModifiedDate=new Date;v.name="toimg"+(new Date).getTime()+".png";w({file:null,blob:function(){return v}},function(A){ba(n);if(a.dom.isEmpty(a.getBody()))a.setContent(""),a.selection.setCursorLocation();else{var H=a.dom.createRng();H.setStart(t.dom(),0);H.setEnd(t.dom(),
0);a.selection.setRng(H);a.nodeChanged()}a.insertContent('<img src="'+A+'" />')},function(A){console.log(A)})}).catch(function(v){console.error("oops, something went wrong!",v)})})})},mceTableSizingMode:function(p,n){return g(n)}},function(p,n){return a.addCommand(n,p)});ha({mceTableCellType:function(p,n){return b.setTableCellType(a,n)},mceTableRowType:function(p,n){return b.setTableRowType(a,n)}},function(p,n){return a.addCommand(n,p)});a.addCommand("mceTableColType",function(p,n){return pa(n,"type").each(function(t){return h("th"===
t?b.makeColumnHeader:b.unmakeColumnHeader)})});ha({mceTableProps:F(Yg,a,!1),mceTableRowProps:F(Ul,a),mceTableCellProps:F(Ql,a)},function(p,n){return a.addCommand(n,function(){return p()})});a.addCommand("mceInsertTable",function(p,n){Pe(n)&&0<ib(n).length?Ng(a,n.rows,n.columns,n.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):Yg(a,!0)});a.addCommand("mceTableApplyCellStyle",function(p,n){if(Pe(n)){var t=Oc(a);0!==t.length&&ha(n,function(v,w){var A=
"tablecell"+w.toLowerCase().replace("-","");a.formatter.has(A)&&La(v)&&z(t,function(H){Vb.normal(a,H).setFormat(A,v)})})}})},Xl=function(a,b,c){var d=fd(a);ha({mceTableRowType:function(){return b.getTableRowType(a)},mceTableCellType:function(){return b.getTableCellType(a)},mceTableColType:function(){return cb(a).bind(function(e){return J(e,d).map(function(f){var g=Jb(c,f,e);return b.getTableColType(f,g)})}).getOr("")}},function(e,f){return a.addQueryValueHandler(f,e)})},Yl=function(){var a=fa(l.none()),
b=fa(l.none()),c=function(d){d.set(l.none())};return{getRows:a.get,setRows:function(d){a.set(d);c(b)},clearRows:function(){return c(a)},getColumns:b.get,setColumns:function(d){b.set(d);c(a)},clearColumns:function(){return c(b)}}},Zg=Ca.generate([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),rb=E(E({},Zg),{none:function(a){void 0===a&&(a=void 0);return Zg.none(a)}}),$g=function(a,b){return J(a,b).bind(function(c){var d=Ya(c);return gc(d,function(e){return B(a,
e)}).map(function(e){return{index:e,all:d}})})},Zl=function(a,b){return $g(a,b).fold(function(){return rb.none(a)},function(c){return c.index+1<c.all.length?rb.middle(a,c.all[c.index+1]):rb.last(a)})},$l=function(a,b){return $g(a,b).fold(function(){return rb.none()},function(c){return 0<=c.index-1?rb.middle(a,c.all[c.index-1]):rb.first(a)})},Wb={create:function(a,b,c,d){return{start:m(a),soffset:m(b),finish:m(c),foffset:m(d)}}},ve=Ca.generate([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),
U={before:ve.before,on:ve.on,after:ve.after,cata:function(a,b,c,d){return a.fold(b,c,d)},getStart:function(a){return a.fold(Q,Q,Q)}},Qc=Ca.generate([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),am=function(a){return a.match({domRange:function(b){return u.fromDom(b.startContainer)},relative:function(b,c){return U.getStart(b)},exact:function(b,c,d,e){return b}})},Ka={domRange:Qc.domRange,relative:Qc.relative,exact:Qc.exact,exactFromRange:function(a){return Qc.exact(a.start(),
a.soffset(),a.finish(),a.foffset())},getWin:function(a){a=am(a);return u.fromDom(a.dom().ownerDocument.defaultView)},range:Wb.create},bm=function(a,b){b.fold(function(c){a.setStartBefore(c.dom())},function(c,d){a.setStart(c.dom(),d)},function(c){a.setStartAfter(c.dom())})},cm=function(a,b){b.fold(function(c){a.setEndBefore(c.dom())},function(c,d){a.setEnd(c.dom(),d)},function(c){a.setEndAfter(c.dom())})},ah=function(a,b,c){a=a.document.createRange();bm(a,b);cm(a,c);return a},sb=function(a,b,c,d,e){a=
a.document.createRange();a.setStart(b.dom(),c);a.setEnd(d.dom(),e);return a},dm=function(a){return{left:m(a.left),top:m(a.top),right:m(a.right),bottom:m(a.bottom),width:m(a.width),height:m(a.height)}},we=Ca.generate([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),bh=function(a,b,c){return b(u.fromDom(c.startContainer),c.startOffset,u.fromDom(c.endContainer),c.endOffset)},em=function(a,b){return b.match({domRange:function(c){return{ltr:m(c),rtl:l.none}},
relative:function(c,d){return{ltr:jb(function(){return ah(a,c,d)}),rtl:jb(function(){return l.some(ah(a,d,c))})}},exact:function(c,d,e,f){return{ltr:jb(function(){return sb(a,c,d,e,f)}),rtl:jb(function(){return l.some(sb(a,e,f,c,d))})}}})},fm=function(a,b){var c=b.ltr();return c.collapsed?b.rtl().filter(function(d){return!1===d.collapsed}).map(function(d){return we.rtl(u.fromDom(d.endContainer),d.endOffset,u.fromDom(d.startContainer),d.startOffset)}).getOrThunk(function(){return bh(a,we.ltr,c)}):
bh(a,we.ltr,c)},xe=function(a,b){b=em(a,b);return fm(a,b)},gd=function(a,b){return xe(a,b).match({ltr:function(c,d,e,f){var g=a.document.createRange();g.setStart(c.dom(),d);g.setEnd(e.dom(),f);return g},rtl:function(c,d,e,f){var g=a.document.createRange();g.setStart(e.dom(),f);g.setEnd(c.dom(),d);return g}})},gm=function(a,b,c,d,e){if(0===e)return 0;if(b===d)return e-1;for(var f=1;f<e;f++){var g=a(f),h=Math.abs(b-g.left);if(c<=g.bottom){if(c<g.top||h>d)return f-1;d=h}}return 0},ch=function(a,b,c){return b>=
a.left&&b<=a.right&&c>=a.top&&c<=a.bottom},hm=function(a,b,c,d,e){var f=function(h){var k=a.dom().createRange();k.setStart(b.dom(),h);k.collapse(!0);return k},g=cc(b).length;c=gm(function(h){return f(h).getBoundingClientRect()},c,d,e.right,g);return f(c)},im=function(a,b,c,d){var e=a.dom().createRange();e.selectNode(b.dom());e=e.getClientRects();return Na(e,function(f){return ch(f,c,d)?l.some(f):l.none()}).map(function(f){return hm(a,b,c,d,f)})},jm=function(a,b,c,d){var e=a.dom().createRange();b=
W(b);return Na(b,function(f){e.selectNode(f.dom());return ch(e.getBoundingClientRect(),c,d)?dh(a,f,c,d):l.none()})},dh=function(a,b,c,d){return Ia(b)?im(a,b,c,d):jm(a,b,c,d)},eh=function(a,b,c){a=a.dom().createRange();a.selectNode(b.dom());a.collapse(c);return a},km=function(a,b,c){var d=a.dom().createRange();d.selectNode(b.dom());d=d.getBoundingClientRect();var e=c-d.left<d.right-c?!0:!1;return(!0===e?oc:yd)(b).map(function(f){return eh(a,f,e)})},lm=function(a,b,c){var d=b.dom().getBoundingClientRect();
return l.some(eh(a,b,c-d.left<d.right-c?!0:!1))},mm=function(a,b,c){return l.from(a.dom().caretPositionFromPoint(b,c)).bind(function(d){if(null===d.offsetNode)return l.none();var e=a.dom().createRange();e.setStart(d.offsetNode,d.offset);e.collapse();return l.some(e)})},nm=function(a,b,c){return l.from(a.dom().caretRangeFromPoint(b,c))},om=function(a,b,c,d){var e=a.dom().createRange();e.selectNode(b.dom());e=e.getBoundingClientRect();c=Math.max(e.left,Math.min(e.right,c));d=Math.max(e.top,Math.min(e.bottom,
d));e=a.dom().createRange();e.selectNode(b.dom());e=e.getBoundingClientRect();return dh(a,b,Math.max(e.left,Math.min(e.right,c)),Math.max(e.top,Math.min(e.bottom,d)))},pm=function(a,b,c){return u.fromPoint(a,b,c).bind(function(d){var e=function(){return(0===W(d).length?lm:km)(a,d,b)};return 0===W(d).length?e():om(a,d,b,c).orThunk(e)})},qm=document.caretPositionFromPoint?mm:document.caretRangeFromPoint?nm:pm,rm=function(a,b,c){a=u.fromDom(a.document);return qm(a,b,c).map(function(d){return Wb.create(u.fromDom(d.startContainer),
d.startOffset,u.fromDom(d.endContainer),d.endOffset)})},Rc=function(a,b){var c=D(a);return"input"===c?U.after(a):P(["br","img"],c)?0===b?U.before(a):U.after(a):U.on(a,b)},ye=function(a,b){a=a.fold(U.before,Rc,U.after);b=b.fold(U.before,Rc,U.after);return Ka.relative(a,b)},Sc=function(a,b,c,d){a=Rc(a,b);c=Rc(c,d);return Ka.relative(a,c)},sm=function(a){return a.match({domRange:function(b){var c=u.fromDom(b.startContainer),d=u.fromDom(b.endContainer);return Sc(c,b.startOffset,d,b.endOffset)},relative:ye,
exact:Sc})},Tc=function(a,b){l.from(a.getSelection()).each(function(c){c.removeAllRanges();c.addRange(b)})},Uc=function(a,b){return xe(a,b).match({ltr:function(c,d,e,f){c=sb(a,c,d,e,f);Tc(a,c)},rtl:function(c,d,e,f){var g=a.getSelection();if(g.setBaseAndExtent)g.setBaseAndExtent(c.dom(),d,e.dom(),f);else if(g.extend)try{g.collapse(c.dom(),d),g.extend(e.dom(),f)}catch(h){c=sb(a,e,f,c,d),Tc(a,c)}else c=sb(a,e,f,c,d),Tc(a,c)}})},tm=function(a){var b=Ka.getWin(a).dom(),c=function(d,e,f,g){return sb(b,
d,e,f,g)};a=sm(a);return xe(b,a).match({ltr:c,rtl:c})},um=function(a){var b=u.fromDom(a.anchorNode),c=u.fromDom(a.focusNode),d=a.anchorOffset,e=a.focusOffset,f=Db(b).dom().createRange();f.setStart(b.dom(),d);f.setEnd(c.dom(),e);d=B(b,c)&&d===e;f.collapsed&&!d?a=l.some(Wb.create(b,a.anchorOffset,c,a.focusOffset)):0<a.rangeCount?(b=a.getRangeAt(0),a=a.getRangeAt(a.rangeCount-1),a=l.some(Wb.create(u.fromDom(b.startContainer),b.startOffset,u.fromDom(a.endContainer),a.endOffset))):a=l.none();return a},
vm=function(a){return l.from(a.getSelection()).filter(function(b){return 0<b.rangeCount}).bind(um)},fh=function(a){return vm(a).map(function(b){return Ka.exact(b.start(),b.soffset(),b.finish(),b.foffset())})},wm=tinymce.util.Tools.resolve("tinymce.util.VK"),xm=function(a,b,c,d){return gh(a,b,Zl(c),d)},ym=function(a,b,c,d){return gh(a,b,$l(c),d)},hh=function(a,b){a=Ka.exact(b,0,b,0);return tm(a)},zm=function(a,b){b=Ba("tr",b);return pd(b).bind(function(c){return kb("td,th",c).map(function(d){return hh(a,
d)})})},gh=function(a,b,c,d,e){return c.fold(l.none,l.none,function(f,g){return oc(g).map(function(h){return hh(a,h)})},function(f){return J(f,b).bind(function(g){var h=uf(f);a.undoManager.transact(function(){d.insertRowsAfter(g,h)});return zm(a,g)})})},Am=["table","li","dl"],Bm=function(a,b,c,d){if(a.keyCode===wm.TAB){var e=Ja(b),f=function(h){var k=D(h);return B(h,e)||P(Am,k)},g=b.selection.getRng();g.collapsed&&(g=u.fromDom(g.startContainer),Hb(g,f).each(function(h){a.preventDefault();(a.shiftKey?
ym:xm)(b,f,h,c,d).each(function(k){b.selection.setRng(k)})}))}},tb={create:function(a,b){return{selection:m(a),kill:m(b)}}},ih={create:function(a,b,c,d){return{start:m(U.on(a,b)),finish:m(U.on(c,d))}}},jh=function(a,b){a=gd(a,b);return Wb.create(u.fromDom(a.startContainer),a.startOffset,u.fromDom(a.endContainer),a.endOffset)},Vc=ih.create,Cm=function(a,b,c,d,e,f,g){return B(c,e)&&d===f?l.none():M(c,"td,th",b).bind(function(h){return M(e,"td,th",b).bind(function(k){return kh(a,b,h,k,g)})})},kh=function(a,
b,c,d,e){return B(c,d)?l.none():dc(c,d,b).bind(function(f){var g=f.boxes.getOr([]);return 0<g.length?(e(a,g,f.start,f.finish),l.some(tb.create(l.some(Vc(c,0,c,Pa(c))),!0))):l.none()})},Dm=function(a,b,c,d,e){return lj(d,a,b,e.firstSelectedSelector,e.lastSelectedSelector).map(function(f){e.clearBeforeUpdate(c);e.selectRange(c,f.boxes,f.start,f.finish);return f.boxes})},ze=function(a,b){return{item:m(a),mode:m(b)}},lh=function(a,b,c,d){void 0===d&&(d=Ra);return a.property().parent(b).map(function(e){return ze(e,
d)})},Ra=function(a,b,c,d){void 0===d&&(d=Xb);return c.sibling(a,b).map(function(e){return ze(e,d)})},Xb=function(a,b,c,d){void 0===d&&(d=Xb);a=a.property().children(b);return c.first(a).map(function(e){return ze(e,d)})},Em=[{current:lh,next:Ra,fallback:l.none()},{current:Ra,next:Xb,fallback:l.some(lh)},{current:Xb,next:Xb,fallback:l.some(Ra)}],mh=function(a,b,c,d,e){void 0===e&&(e=Em);return na(e,function(f){return f.current===c}).bind(function(f){return f.current(a,b,d,f.next).orThunk(function(){return f.fallback.bind(function(g){return mh(a,
b,g,d)})})})},Wc={left:function(){return{sibling:function(a,b){return a.query().prevSibling(b)},first:function(a){return 0<a.length?l.some(a[a.length-1]):l.none()}}},right:function(){return{sibling:function(a,b){return a.query().nextSibling(b)},first:function(a){return 0<a.length?l.some(a[0]):l.none()}}}},Yb=function(a,b,c,d,e,f){return mh(a,b,d,e).bind(function(g){return f(g.item())?l.none():c(g.item())?l.some(g.item()):Yb(a,g.item(),c,g.mode(),e,f)})},nh=function(a){return function(b){return 0===
a.property().children(b).length}},ub=$b(),oh=function(a,b){var c=nh(ub);return Yb(ub,a,c,Ra,Wc.left(),b)},ph=function(a,b){var c=nh(ub);return Yb(ub,a,c,Ra,Wc.right(),b)},Fm=function(a,b,c){return Yb(ub,a,b,Ra,Wc.left(),c)},Gm=function(a,b,c){return Yb(ub,a,b,Ra,Wc.right(),c)},Hm=function(a,b,c){return xb(a,b,c).isSome()},Xc=Ca.generate([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Im=function(a){return M(a,"tr")},vb=E(E({},Xc),{verify:function(a,b,c,d,e,f,g){return M(d,
"td,th",g).bind(function(h){return M(b,"td,th",g).map(function(k){return B(h,k)?B(d,h)&&Pa(h)===e?f(k):Xc.none("in same cell"):jd(Im,[h,k]).fold(function(){var q=a.getRect(k),r=a.getRect(h);return r.right>q.left&&r.left<q.right?Xc.success():f(k)},function(q){return f(k)})})}).getOr(Xc.none("default"))},cata:function(a,b,c,d,e){return a.fold(b,c,d,e)}}),Jm=function(a){return ya(a).bind(function(b){var c=W(b);return gc(c,F(B,a)).map(function(d){return{parent:m(b),children:m(c),element:m(a),index:m(d)}})})},
Ae=function(a){return"br"===D(a)},Be=function(a,b,c){return b(a,c).bind(function(d){return Ia(d)&&0===cc(d).trim().length?Be(d,b,c):l.some(d)})},Km=function(a,b,c){return c.traverse(b).orThunk(function(){return Be(b,c.gather,a)}).map(c.relative)},Lm=function(a,b){return ud(a,b).filter(Ae).orThunk(function(){return ud(a,b-1).filter(Ae)})},Mm=function(a,b,c,d){return Lm(b,c).bind(function(e){return d.traverse(e).fold(function(){return Be(e,d.gather,a).map(d.relative)},function(f){return Jm(f).map(function(g){return U.on(g.parent(),
g.index())})})})},Nm=function(a,b,c,d){return(Ae(b)?Km(a,b,d):Mm(a,b,c,d)).map(function(e){return{start:m(e),finish:m(e)}})},Om=function(a){return vb.cata(a,function(b){return l.none()},function(){return l.none()},function(b){return l.some(Pb(b,0))},function(b){return l.some(Pb(b,Pa(b)))})},Yc=function(a,b){return{left:a.left,top:a.top+b,right:a.right,bottom:a.bottom+b}},Zc=function(a,b){return{left:a.left,top:a.top-b,right:a.right,bottom:a.bottom-b}},qh=function(a,b,c){return{left:a.left+b,top:a.top+
c,right:a.right+b,bottom:a.bottom+c}},$c=function(a){return{left:a.left,top:a.top,right:a.right,bottom:a.bottom}},rh=function(a,b,c){return Va(b)?l.some(a.getRect(b)).map($c):Ia(b)?(a=0<=c&&c<Pa(b)?a.getRangedRect(b,c,b,c+1):0<c?a.getRangedRect(b,c-1,b,c):l.none(),a.map($c)):l.none()},sh=function(a,b){return Va(b)?l.some(a.getRect(b)).map($c):Ia(b)?a.getRangedRect(b,0,b,Pa(b)).map($c):l.none()},ua=Ca.generate([{none:[]},{retry:["caret"]}]),th=function(a,b,c){return Gi(b,de).fold(m(!1),function(d){return sh(a,
d).exists(function(e){return c.left<e.left||1>Math.abs(e.right-c.left)||c.left>e.right})})},Pm={point:function(a){return a.bottom},adjuster:function(a,b,c,d,e){var f=Yc(e,5);return 1>Math.abs(c.bottom-d.bottom)?ua.retry(f):c.top>e.bottom?ua.retry(f):c.top===e.bottom?ua.retry(Yc(e,1)):th(a,b,e)?ua.retry(qh(f,5,0)):ua.none()},move:Yc,gather:ph},Qm=function(a,b,c){return a.elementFromPoint(b,c).filter(function(d){return"table"===D(d)}).isSome()},Rm=function(a,b,c,d,e){return Ce(a,b,c,b.move(d,5),e)},
Ce=function(a,b,c,d,e){return 0===e?l.some(d):Qm(a,d.left,b.point(d))?Rm(a,b,c,d,e-1):a.situsFromPoint(d.left,b.point(d)).bind(function(f){return f.start().fold(l.none,function(g){return sh(a,g).bind(function(h){return b.adjuster(a,g,h,c,d).fold(l.none,function(k){return Ce(a,b,c,k,e-1)})}).orThunk(function(){return l.some(d)})},l.none)})},Sm=function(a,b,c){return a.point(b)>c.getInnerHeight()?l.some(a.point(b)-c.getInnerHeight()):0>a.point(b)?l.some(-a.point(b)):l.none()},uh=function(a,b,c){var d=
a.move(c,5),e=Ce(b,a,c,d,100).getOr(d);return Sm(a,e,b).fold(function(){return b.situsFromPoint(e.left,a.point(e))},function(f){b.scrollBy(0,f);return b.situsFromPoint(e.left,a.point(e)-f)})},Zb={tryUp:F(uh,{point:function(a){return a.top},adjuster:function(a,b,c,d,e){var f=Zc(e,5);return 1>Math.abs(c.top-d.top)?ua.retry(f):c.bottom<e.top?ua.retry(f):c.bottom===e.top?ua.retry(Zc(e,1)):th(a,b,e)?ua.retry(qh(f,5,0)):ua.none()},move:Zc,gather:oh}),tryDown:F(uh,Pm),ieTryUp:function(a,b){return a.situsFromPoint(b.left,
b.top-5)},ieTryDown:function(a,b){return a.situsFromPoint(b.left,b.bottom+5)},getJumpSize:m(5)},Tm=function(a,b,c){return a.getSelection().bind(function(d){return Nm(b,d.finish(),d.foffset(),c).fold(function(){return l.some(Pb(d.finish(),d.foffset()))},function(e){e=a.fromSitus(e);e=vb.verify(a,d.finish(),d.foffset(),e.finish(),e.foffset(),c.failure,b);return Om(e)})})},De=function(a,b,c,d,e,f){return 0===f?l.none():Um(a,b,c,d,e).bind(function(g){var h=a.fromSitus(g);h=vb.verify(a,c,d,h.finish(),
h.foffset(),e.failure,b);return vb.cata(h,function(){return l.none()},function(){return l.some(g)},function(k){return B(c,k)&&0===d?vh(a,c,d,Zc,e):De(a,b,k,0,e,f-1)},function(k){return B(c,k)&&d===Pa(k)?vh(a,c,d,Yc,e):De(a,b,k,Pa(k),e,f-1)})})},vh=function(a,b,c,d,e){return rh(a,b,c).bind(function(f){return wh(a,e,d(f,Zb.getJumpSize()))})},wh=function(a,b,c){var d=jc().browser;return d.isChrome()||d.isSafari()||d.isFirefox()||d.isEdge()?b.otherRetry(a,c):d.isIE()?b.ieRetry(a,c):l.none()},Um=function(a,
b,c,d,e){return rh(a,c,d).bind(function(f){return wh(a,e,f)})},Vm=function(a,b,c){return Tm(a,b,c).bind(function(d){return De(a,b,d.element(),d.offset(),c,20).map(a.fromSitus)})},Wm=function(a,b){return Hm(a,function(c){return ya(c).exists(function(d){return B(d,b)})})},xh=function(a,b,c,d,e){return M(d,"td,th",b).bind(function(f){return M(f,"table",b).bind(function(g){return Wm(e,g)?Vm(a,b,c).bind(function(h){return M(h.finish(),"td,th",b).map(function(k){return{start:m(f),finish:m(k),range:m(h)}})}):
l.none()})})},yh=function(a,b,c,d,e,f){return jc().browser.isIE()?l.none():f(d,b).orThunk(function(){return xh(a,b,c,d,e).map(function(g){g=g.range();return tb.create(l.some(Vc(g.start(),g.soffset(),g.finish(),g.foffset())),!0)})})},Xm=function(a,b){return M(a,"tr",b).bind(function(c){return M(c,"table",b).bind(function(d){var e=Ba("tr",d);return B(c,e[0])?Fm(d,function(f){return yd(f).isSome()},b).map(function(f){var g=Pa(f);return tb.create(l.some(Vc(f,g,f,g)),!0)}):l.none()})})},Ym=function(a,
b){return M(a,"tr",b).bind(function(c){return M(c,"table",b).bind(function(d){var e=Ba("tr",d);return B(c,e[e.length-1])?Gm(d,function(f){return oc(f).isSome()},b).map(function(f){return tb.create(l.some(Vc(f,0,f,0)),!0)}):l.none()})})},zh=function(a,b,c,d,e,f,g){return xh(a,c,d,e,f).bind(function(h){return kh(b,c,h.start(),h.finish(),g)})},Me=function(a,b){return M(a,"td,th",b)},Ah={traverse:bc,gather:ph,relative:U.before,otherRetry:Zb.tryDown,ieRetry:Zb.ieTryDown,failure:vb.failedDown},Bh={traverse:Ke,
gather:oh,relative:U.before,otherRetry:Zb.tryUp,ieRetry:Zb.ieTryUp,failure:vb.failedUp},wb=function(a){return function(b){return b===a}},Ee=wb(38),ad=wb(40),id=function(a){return 37<=a&&40>=a},Vh={isBackward:wb(37),isForward:wb(39)},Uh={isBackward:wb(39),isForward:wb(37)},Zm=function(a){return{left:a.left(),top:a.top(),right:a.right(),bottom:a.bottom(),width:a.width(),height:a.height()}},Fe=function(a){return{elementFromPoint:function(b,c){return u.fromPoint(u.fromDom(a.document),b,c)},getRect:function(b){return b.dom().getBoundingClientRect()},
getRangedRect:function(b,c,d,e){b=Ka.exact(b,c,d,e);b=gd(a,b);c=b.getClientRects();b=0<c.length?c[0]:b.getBoundingClientRect();return(0<b.width||0<b.height?l.some(b).map(dm):l.none()).map(Zm)},getSelection:function(){return fh(a).map(function(b){return jh(a,b)})},fromSitus:function(b){b=Ka.relative(b.start(),b.finish());return jh(a,b)},situsFromPoint:function(b,c){return rm(a,b,c).map(function(d){return ih.create(d.start(),d.soffset(),d.finish(),d.foffset())})},clearSelection:function(){a.getSelection().removeAllRanges()},
collapseSelection:function(b){void 0===b&&(b=!1);fh(a).each(function(c){return c.fold(function(d){return d.collapse(b)},function(d,e){d=b?d:e;d=ye(d,d);Uc(a,d)},function(d,e,f,g){d=b?d:f;e=b?e:g;e=Sc(d,e,d,e);Uc(a,e)})})},setSelection:function(b){var c=b.start(),d=b.soffset(),e=b.finish();b=b.foffset();c=Sc(c,d,e,b);Uc(a,c)},setRelativeSelection:function(b,c){b=ye(b,c);Uc(a,b)},selectContents:function(b){var c=a.document.createRange();c.selectNodeContents(b.dom());Tc(a,c)},getInnerHeight:function(){return a.innerHeight},
getScrollY:function(){var b=u.fromDom(a.document);b=void 0!==b?b.dom():G.document;return Da(b.body.scrollLeft||b.documentElement.scrollLeft,b.body.scrollTop||b.documentElement.scrollTop).top()},scrollBy:function(b,c){var d=u.fromDom(a.document);(void 0!==d?d.dom():G.document).defaultView.scrollBy(b,c)}}},Rh=function(a,b,c,d){a=Fe(a);b=Mh(a,b,c,d);return{mousedown:b.mousedown,mouseover:b.mouseover,mouseup:b.mouseup}},Sh=function(a,b,c,d){var e=Fe(a),f=function(){d.clear(b);return l.none()};return{keydown:function(g,
h,k,q,r,p){g=g.raw();var n=g.which,t=!0===g.shiftKey;return Dd(b,d.selectedSelector).fold(function(){return ad(n)&&t?F(zh,e,b,c,Ah,q,h,d.selectRange):Ee(n)&&t?F(zh,e,b,c,Bh,q,h,d.selectRange):ad(n)?F(yh,e,c,Ah,q,h,Ym):Ee(n)?F(yh,e,c,Bh,q,h,Xm):l.none},function(v){var w=function(A){return function(){return Na(A,function(H){return Dm(H.rows,H.cols,b,v,d)}).fold(function(){return tf(b,d.firstSelectedSelector,d.lastSelectedSelector).map(function(H){var Ub=ad(n)||p.isForward(n)?U.after:U.before;e.setRelativeSelection(U.on(H.first(),
0),Ub(H.table()));d.clear(b);return tb.create(l.none(),!0)})},function(H){return l.some(tb.create(l.none(),!0))})}};return ad(n)&&t?w([{rows:1,cols:0}]):Ee(n)&&t?w([{rows:-1,cols:0}]):p.isBackward(n)&&t?w([{rows:0,cols:-1},{rows:-1,cols:0}]):p.isForward(n)&&t?w([{rows:0,cols:1},{rows:1,cols:0}]):id(n)&&!1===t?f:l.none})()},keyup:function(g,h,k,q,r){return Dd(b,d.selectedSelector).fold(function(){var p=g.raw(),n=p.which;return!0===p.shiftKey===!1?l.none():id(n)?Cm(b,c,h,k,q,r,d.selectRange):l.none()},
l.none)}}},Th=function(a,b,c,d){var e=Fe(a);return function(f,g){d.clearBeforeUpdate(b);dc(f,g,c).each(function(h){var k=h.boxes.getOr([]);d.selectRange(b,k,h.start,h.finish);e.selectContents(g);e.collapseSelection()})}},$m=function(a,b){z(b,function(c){wc(a)?a.dom().classList.remove(c):gk(a,"class",c);0===(wc(a)?a.dom().classList:Pd(a,"class")).length&&I(a,"class")})},an=function(a){return function(b){Ea(b,a)}},bn=function(a){return function(b){$m(b,a)}},Oh={byClass:function(a){var b=an(a.selected),
c=bn([a.selected,a.lastSelected,a.firstSelected]),d=function(e){e=Ba(a.selectedSelector,e);z(e,c)};return{clearBeforeUpdate:d,clear:d,selectRange:function(e,f,g,h){d(e);z(f,b);Ea(g,a.firstSelected);Ea(h,a.lastSelected)},selectedSelector:a.selectedSelector,firstSelectedSelector:a.firstSelectedSelector,lastSelectedSelector:a.lastSelectedSelector}},byAttr:function(a,b,c){var d=function(h){I(h,a.selected);I(h,a.firstSelected);I(h,a.lastSelected)},e=function(h){Y(h,a.selected,"1")},f=function(h){g(h);
c()},g=function(h){h=Ba(a.selectedSelector,h);z(h,d)};return{clearBeforeUpdate:g,clear:f,selectRange:function(h,k,q,r){f(h);z(k,e);Y(q,a.firstSelected,"1");Y(r,a.lastSelected,"1");b(k,q,r)},selectedSelector:a.selectedSelector,firstSelectedSelector:a.firstSelectedSelector,lastSelectedSelector:a.lastSelectedSelector}}},cn=function(a,b,c){a=a.slice(0,b[b.length-1].row()+1);c=Jc(a,c);return oa(c,function(d){d=d.cells().slice(0,b[b.length-1].column()+1);return x(d,function(e){return e.element()})})},dn=
function(a,b,c){a=a.slice(b[0].row()+b[0].rowspan()-1,a.length);c=Jc(a,c);return oa(c,function(d){d=d.cells().slice(b[0].column()+b[0].colspan()-1,+d.cells().length);return x(d,function(e){return e.element()})})},Qh=function(a,b,c){var d=C.fromTable(a);return sa(d,b).map(function(e){var f=Qb(d,c,!1),g=cn(f,e,c);e=dn(f,e,c);return{upOrLeftCells:g,downOrRightCells:e}})},hd=function(a){return!1===Qd(u.fromDom(a.target),"ephox-snooker-resizer-bar")},en=function(a,b){var c=fa(l.none()),d=fa([]),e=function(){return db(a).bind(function(h){return J(h).map(function(k){return"caption"===
D(h)?uf(h):Jb(b,k,h)})})},f=function(){c.set(jb(e)());z(d.get(),function(h){return h()})},g=function(h,k){var q=function(){return c.get().fold(function(){h.setDisabled(!0)},function(r){h.setDisabled(k(r))})};q();d.set(d.get().concat([q]));return function(){d.set(X(d.get(),function(r){return r!==q}))}};a.on("NodeChange ExecCommand TableSelectorChange",f);return{onSetupTable:function(h){return g(h,function(k){return!1})},onSetupCellOrRow:function(h){return g(h,function(k){return"caption"===D(k.element())})},
onSetupPasteable:function(h){return function(k){return g(k,function(q){return"caption"===D(q.element())||h().isNone()})}},onSetupMergeable:function(h){return g(h,function(k){return k.mergable().isNone()})},onSetupUnmergeable:function(h){return g(h,function(k){return k.unmergable().isNone()})},resetTargets:f,targets:function(){return c.get()}}},fn=function(a,b,c){a.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});
var d=function(e){return function(){return a.execCommand(e)}};a.ui.registry.addButton("tablealignleft",{tooltip:"\u8868\u683c\u5c45\u5de6",onAction:d("mceTableAlignLeft"),icon:"align-left-table",onSetup:b.onSetupTable});a.ui.registry.addButton("tablealigncenter",{tooltip:"\u8868\u683c\u5c45\u4e2d",onAction:d("mceTableAlignCenter"),icon:"align-center-table",onSetup:b.onSetupTable});a.ui.registry.addButton("tableToimg",{tooltip:"\u8868\u683c\u8f6c\u4e3a\u56fe\u7247",onAction:d("mceTableToImg"),icon:"table-to-img",
onSetup:b.onSetupTable});a.ui.registry.addButton("tablealignright",{tooltip:"\u8868\u683c\u5c45\u53f3",onAction:d("mceTableAlignRight"),icon:"align-right-table",onSetup:b.onSetupTable});a.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:d("mceTableProps"),icon:"table",onSetup:b.onSetupTable});a.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:d("mceTableDelete"),icon:"table-delete-table",onSetup:b.onSetupTable});a.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",
onAction:d("mceTableCellProps"),icon:"table-cell-properties",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:d("mceTableMergeCells"),icon:"table-merge-cells",onSetup:b.onSetupMergeable});a.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:d("mceTableSplitCells"),icon:"table-split-cells",onSetup:b.onSetupUnmergeable});a.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:d("mceTableInsertRowBefore"),
icon:"table-insert-row-above",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:d("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:d("mceTableDeleteRow"),icon:"table-delete-row",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:d("mceTableRowProps"),icon:"table-row-properties",
onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:d("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:d("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:d("mceTableDeleteCol"),icon:"table-delete-column",
onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:d("mceTableCutRow"),onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:d("mceTableCopyRow"),onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:d("mceTablePasteRowBefore"),onSetup:b.onSetupPasteable(c.getRows)});a.ui.registry.addButton("tablepasterowafter",
{tooltip:"Paste row after",icon:"paste-row-after",onAction:d("mceTablePasteRowAfter"),onSetup:b.onSetupPasteable(c.getRows)});a.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:d("mceTableCutCol"),onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:d("mceTableCopyCol"),onSetup:b.onSetupCellOrRow});a.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",
onAction:d("mceTablePasteColBefore"),onSetup:b.onSetupPasteable(c.getColumns)});a.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:d("mceTablePasteColAfter"),onSetup:b.onSetupPasteable(c.getColumns)});a.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:d("mceInsertTable"),icon:"table"})},gn=function(a){var b=function(d){return a.dom.is(d,"table")&&a.getBody().contains(d)},c=a.getParam("table_toolbar","tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol | tablealignleft tablealigncenter tablealignright tabletoimg tableprops tabledelete");
0<c.length&&a.ui.registry.addContextToolbar("table",{predicate:b,items:c,scope:"node",position:"node"})},hn=function(a,b,c){var d=function(q){return function(){return a.execCommand(q)}},e=function(q){var r=q.numRows,p=q.numColumns;a.undoManager.transact(function(){me(a,p,r,0,0)});a.addVisual()},f={text:"Table properties",onSetup:b.onSetupTable,onAction:d("mceTableProps")},g={text:"Delete table",icon:"table-delete-table",onSetup:b.onSetupTable,onAction:d("mceTableDelete")};a.ui.registry.addMenuItem("tableinsertrowbefore",
{text:"Insert row before",icon:"table-insert-row-above",onAction:d("mceTableInsertRowBefore"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:d("mceTableInsertRowAfter"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:d("mceTableDeleteRow"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",
icon:"table-row-properties",onAction:d("mceTableRowProps"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:d("mceTableCutRow"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:d("mceTableCopyRow"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:d("mceTablePasteRowBefore"),onSetup:b.onSetupPasteable(c.getRows)});
a.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:d("mceTablePasteRowAfter"),onSetup:b.onSetupPasteable(c.getRows)});a.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:d("mceTableInsertColBefore"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:d("mceTableInsertColAfter"),
onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:d("mceTableDeleteCol"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:d("mceTableCutCol"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:d("mceTableCopyCol"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablepastecolumnbefore",
{text:"Paste column before",icon:"paste-column-before",onAction:d("mceTablePasteColBefore"),onSetup:b.onSetupPasteable(c.getColumns)});a.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:d("mceTablePasteColAfter"),onSetup:b.onSetupPasteable(c.getColumns)});a.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:d("mceTableCellProps"),onSetup:b.onSetupCellOrRow});a.ui.registry.addMenuItem("tablemergecells",
{text:"Merge cells",icon:"table-merge-cells",onAction:d("mceTableMergeCells"),onSetup:b.onSetupMergeable});a.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:d("mceTableSplitCells"),onSetup:b.onSetupUnmergeable});!1===a.getParam("table_grid",!0,"boolean")?a.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:d("mceInsertTable")}):a.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",
fancytype:"inserttable",onAction:e}]}});a.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:d("mceInsertTable")});c={"align-right-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm6 4h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm-6-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',"align-left-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2zm0-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',
"align-center-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm3 4h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm-3-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',"table-to-img":'<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path fill-rule="evenodd" d="M794.4448 85.41696a20.79232 20.79232 0 0 0-29.35296-1.84832l-81.96608 72.50432-0.12288 0.14848c-1.44384 1.31584-2.70848 2.816-3.75808 4.46976-0.46592 0.7424-0.64 1.62816-0.98816 2.39104a20.93568 20.93568 0 0 0-2.06336 10.26048v0.04096c0.19968 2.40128 0.92672 4.77184 1.95072 6.9632 0.08192 0.1536 0.08192 0.42496 0.16384 0.60416l43.86304 87.68512a20.81792 20.81792 0 0 0 37.20192-18.60608l-28.00128-55.96672c54.85056 5.22752 96.62976 22.41536 124.53376 51.44064 38.272 39.7568 37.05344 89.59488 37.08928 90.00448-0.48128 11.20256 8.51456 21.09952 19.968 21.53984a20.7616 20.7616 0 0 0 21.55008-19.83488c0.07168-2.70848 2.26816-67.02592-48.05632-119.85408-32.70656-34.31424-79.31904-55.29088-138.624-63.02208l44.78464-39.6288 0.06656-0.0512a20.72576 20.72576 0 0 0 1.76128-29.24032zM497.59744 334.39232c31.54432-0.0256 57.1136-25.59488 57.1392-57.1392-0.0256-31.54944-25.59488-57.1136-57.1392-57.1392-31.54432 0.0256-57.1136 25.59488-57.1392 57.1392 0.0256 31.54944 25.58976 57.1136 57.1392 57.1392z m0-83.1232a25.984 25.984 0 1 1 0 51.92704 25.97888 25.97888 0 0 1-24.87296-27.04896 25.97376 25.97376 0 0 1 24.87296-24.87808z" p-id="34952" fill="#2c2c2c"></path><path d="M946.33472 519.02976c-8.44288-37.59104-44.98432-65.9456-88.6528-65.9456h-207.95904l-0.4608-231.49056c0-45.55264-40.62208-82.60096-90.54208-82.60096h-393.4208c-49.92512 0-90.5472 37.04832-90.5472 82.60096v307.8144c0 45.55264 40.61696 82.58048 90.5472 82.58048h208.39424v231.56224c0 5.64224 0.66048 11.2384 1.8432 16.5888 8.46336 37.63712 45.00992 65.99168 88.6784 65.99168h393.4464c49.92 0 90.5216-37.0688 90.5216-82.58048v-307.8144a73.64608 73.64608 0 0 0-1.84832-16.70656zM116.3264 221.568c0-22.61504 21.95968-41.05216 48.96768-41.05216h393.44128c27.008 0 48.96768 18.432 48.96768 41.05216v231.49056h-101.7856c-11.60704-20.22912-14.1568-24.23296-19.29728-31.9488-0.72704-1.18272-1.52576-2.98496-2.50368-5.05344-6.64576-13.952-19.0464-39.86944-45.37344-44.32896-18.0224-3.02592-36.75136 5.21216-55.79776 24.50944-28.98944 29.37856-35.04128 23.39328-41.3696 17.13664a42.46528 42.46528 0 0 0-3.13856-2.93376c-2.02752-2.43712-7.5776-12.53888-12.0576-20.66432-20.95616-38.13888-49.69984-90.3424-87.08608-91.49952l-0.77312-0.02048c-30.40256 0-78.30016 57.82016-122.19904 122.10688V221.568z m790.30272 412.47232v209.4592c0 22.5792-21.98528 41.03168-48.93184 41.03168H464.23552c-27.008 0-48.96768-18.45248-48.96768-41.03168v-307.80928c0-22.64064 21.9392-41.05216 48.96768-41.05216h393.4464c27.008 0 48.96768 18.41152 48.96768 41.05216v98.35008h-0.02048z" p-id="34953" fill="#2c2c2c"></path><path d="M658.64192 591.19104a20.78208 20.78208 0 0 1-20.80256 20.75136H471.60832a20.79232 20.79232 0 0 1 0-41.5488h166.23104a20.81792 20.81792 0 0 1 20.80256 20.79744z m-41.53344 173.11744a20.80256 20.80256 0 0 1-20.80256 20.80256h-124.672a20.77696 20.77696 0 0 1 0-41.55392h124.67712c11.4432 0.00512 20.79744 9.33376 20.79744 20.75136z m145.4336-86.53312a20.74112 20.74112 0 0 1-20.78208 20.70528H471.63392a20.70528 20.70528 0 0 1-20.77696-20.63872v-0.06656a20.7872 20.7872 0 0 1 20.77696-20.80256h270.12608a20.82304 20.82304 0 0 1 20.78208 20.80256z"></path></svg>'};
d=a.getParam("table_icons",{"align-right-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm6 4h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1h-8a1 1 0 0 1 0-2zm-6-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',"align-left-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 4h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2zm0-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',
"align-center-table":'<svg width="24" height="24"><path d="M5 5h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 1 1 0-2zm3 4h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 1 1 0-2zm0 8h8c.6 0 1 .4 1 1s-.4 1-1 1H8a1 1 0 0 1 0-2zm-3-4h14c.6 0 1 .4 1 1s-.4 1-1 1H5a1 1 0 0 1 0-2z" fill-rule="evenodd"></path></svg>',"table-to-img":'<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path fill-rule="evenodd" d="M794.4448 85.41696a20.79232 20.79232 0 0 0-29.35296-1.84832l-81.96608 72.50432-0.12288 0.14848c-1.44384 1.31584-2.70848 2.816-3.75808 4.46976-0.46592 0.7424-0.64 1.62816-0.98816 2.39104a20.93568 20.93568 0 0 0-2.06336 10.26048v0.04096c0.19968 2.40128 0.92672 4.77184 1.95072 6.9632 0.08192 0.1536 0.08192 0.42496 0.16384 0.60416l43.86304 87.68512a20.81792 20.81792 0 0 0 37.20192-18.60608l-28.00128-55.96672c54.85056 5.22752 96.62976 22.41536 124.53376 51.44064 38.272 39.7568 37.05344 89.59488 37.08928 90.00448-0.48128 11.20256 8.51456 21.09952 19.968 21.53984a20.7616 20.7616 0 0 0 21.55008-19.83488c0.07168-2.70848 2.26816-67.02592-48.05632-119.85408-32.70656-34.31424-79.31904-55.29088-138.624-63.02208l44.78464-39.6288 0.06656-0.0512a20.72576 20.72576 0 0 0 1.76128-29.24032zM497.59744 334.39232c31.54432-0.0256 57.1136-25.59488 57.1392-57.1392-0.0256-31.54944-25.59488-57.1136-57.1392-57.1392-31.54432 0.0256-57.1136 25.59488-57.1392 57.1392 0.0256 31.54944 25.58976 57.1136 57.1392 57.1392z m0-83.1232a25.984 25.984 0 1 1 0 51.92704 25.97888 25.97888 0 0 1-24.87296-27.04896 25.97376 25.97376 0 0 1 24.87296-24.87808z" p-id="34952" fill="#2c2c2c"></path><path d="M946.33472 519.02976c-8.44288-37.59104-44.98432-65.9456-88.6528-65.9456h-207.95904l-0.4608-231.49056c0-45.55264-40.62208-82.60096-90.54208-82.60096h-393.4208c-49.92512 0-90.5472 37.04832-90.5472 82.60096v307.8144c0 45.55264 40.61696 82.58048 90.5472 82.58048h208.39424v231.56224c0 5.64224 0.66048 11.2384 1.8432 16.5888 8.46336 37.63712 45.00992 65.99168 88.6784 65.99168h393.4464c49.92 0 90.5216-37.0688 90.5216-82.58048v-307.8144a73.64608 73.64608 0 0 0-1.84832-16.70656zM116.3264 221.568c0-22.61504 21.95968-41.05216 48.96768-41.05216h393.44128c27.008 0 48.96768 18.432 48.96768 41.05216v231.49056h-101.7856c-11.60704-20.22912-14.1568-24.23296-19.29728-31.9488-0.72704-1.18272-1.52576-2.98496-2.50368-5.05344-6.64576-13.952-19.0464-39.86944-45.37344-44.32896-18.0224-3.02592-36.75136 5.21216-55.79776 24.50944-28.98944 29.37856-35.04128 23.39328-41.3696 17.13664a42.46528 42.46528 0 0 0-3.13856-2.93376c-2.02752-2.43712-7.5776-12.53888-12.0576-20.66432-20.95616-38.13888-49.69984-90.3424-87.08608-91.49952l-0.77312-0.02048c-30.40256 0-78.30016 57.82016-122.19904 122.10688V221.568z m790.30272 412.47232v209.4592c0 22.5792-21.98528 41.03168-48.93184 41.03168H464.23552c-27.008 0-48.96768-18.45248-48.96768-41.03168v-307.80928c0-22.64064 21.9392-41.05216 48.96768-41.05216h393.4464c27.008 0 48.96768 18.41152 48.96768 41.05216v98.35008h-0.02048z" p-id="34953" fill="#2c2c2c"></path><path d="M658.64192 591.19104a20.78208 20.78208 0 0 1-20.80256 20.75136H471.60832a20.79232 20.79232 0 0 1 0-41.5488h166.23104a20.81792 20.81792 0 0 1 20.80256 20.79744z m-41.53344 173.11744a20.80256 20.80256 0 0 1-20.80256 20.80256h-124.672a20.77696 20.77696 0 0 1 0-41.55392h124.67712c11.4432 0.00512 20.79744 9.33376 20.79744 20.75136z m145.4336-86.53312a20.74112 20.74112 0 0 1-20.78208 20.70528H471.63392a20.70528 20.70528 0 0 1-20.77696-20.63872v-0.06656a20.7872 20.7872 0 0 1 20.77696-20.80256h270.12608a20.82304 20.82304 0 0 1 20.78208 20.80256z"></path></svg>'});
for(var h in c)a.ui.registry.addIcon(h,c[h]);for(var k in d)a.ui.registry.addIcon(k,d[k]);a.ui.registry.addMenuItem("tableprops",f);a.ui.registry.addMenuItem("deletetable",g);a.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return"tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter"}});a.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",
getSubmenuItems:function(){return"tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn"}});a.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return"tablecellprops tablemergecells tablesplitcells"}});a.ui.registry.addContextMenu("table",{update:function(){b.resetTargets();return b.targets().fold(function(){return""},function(q){return"caption"===D(q.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})},
jn={tablecellbackgroundcolor:{selector:"td,th",styles:{backgroundColor:"%value"},remove_similar:!0},tablecellbordercolor:{selector:"td,th",styles:{borderColor:"%value"},remove_similar:!0},tablecellborderstyle:{selector:"td,th",styles:{borderStyle:"%value"},remove_similar:!0},tablecellborderwidth:{selector:"td,th",styles:{borderWidth:"%value"},remove_similar:!0}};Wh.add("table",function(a){var b=Kg(a),c=en(a,b),d=Mk(a),e=Nh(a,d.lazyResize,c),f=Bl(a,d.lazyWire),g=Yl();Wl(a,f,e,b,g);Xl(a,f,b);xj(a,b,
f,e);hn(a,c,g);fn(a,c,g);gn(a);a.on("PreInit",function(){a.serializer.addTempAttr("data-mce-first-selected");a.serializer.addTempAttr("data-mce-last-selected");a.formatter.register(jn)});if(a.getParam("table_tab_navigation",!0,"boolean"))a.on("keydown",function(h){Bm(h,a,f,d.lazyWire)});a.on("remove",function(){d.destroy()});return Hl(a,g,d,c)})})(window,domtoimage);