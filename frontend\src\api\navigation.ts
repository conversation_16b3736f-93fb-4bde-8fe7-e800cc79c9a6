import request from "./request";

async function addnavigationsettings(params: any) {
    return request(`/unifiedplatform/v1/navigationsettings`, {
        method: 'post',
        data: JSON.stringify(params)
    })
}

async function getnavigationAll(type: any) {
    return request(`/unifiedplatform/v1/navigationsettings/navigation?type=${type}`, {
        method: 'get'
    })
}

async function deletenavigation(id: any) {
    return request(`/unifiedplatform/v1/navigationsettings/delete?id=${id}`, {
        method: 'get'
    })
}

async function disablednavigation(params: any) {
    return request(`/unifiedplatform/v1/navigationsettings/disabled?id=${params.id}&disabled=${params.disabled}`, {
        method: 'get',
    })
}

async function ordernavigation(params: any) {
    return request(`/unifiedplatform/v1/navigationsettings/order`, {
        method: 'post',
        data: JSON.stringify(params)
    })
}




let navigation = {
    addnavigationsettings,
    getnavigationAll,
    deletenavigation,
    disablednavigation,
    ordernavigation
  }
  
  export default navigation;