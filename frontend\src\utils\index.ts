import {cloneDeep} from 'lodash';
export const getUid = () => Date.now().toString(36);

/**
 * 加载icon
 * @param url
 */
export const loadFavicon = (url: string) => {
  const icon = document.createElement('link');
  icon.href = url;
  icon.type = 'image/x-icon';
  icon.rel = 'shortcut icon';
  document.getElementsByTagName('head')[0].appendChild(icon);
};

export function bytesToSize(bytes: number) {
  if (bytes === 0) return '0 B';
  const k = 1024,
    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(bytes) / Math.log(k));
  return (bytes / Math.pow(k, i)).toFixed(1) + ' ' + sizes[i];
}

/**************************颜色处理***********************************/
//hex颜色转rgb颜色
function HexToRgb(str: string) {
  const r = /^#?[0-9A-F]{6}$/;
  //test方法检查在字符串中是否存在一个模式，如果存在则返回true，否则返回false
  if (!r.test(str)) return '';
  //replace替换查找的到的字符串
  str = str.replace('#', '');
  //match得到查询数组
  let hxs: any = str.match(/../g);
  //alert('bf:'+hxs)
  for (let i = 0; i < 3; i++) hxs[i] = parseInt(hxs[i], 16);
  //alert(parseInt(80, 16))
  //console.log(hxs);
  return hxs;
}

//GRB颜色转Hex颜色
function RgbToHex(a: number, b: number, c: number) {
  const r = /^\d{1,3}$/;
  if (!r.test(a + '') || !r.test(b + '') || !r.test(c + '')) return '';
  const hexs = [a.toString(16), b.toString(16), c.toString(16)];
  for (let i = 0; i < 3; i++) if (hexs[i].length == 1) hexs[i] = '0' + hexs[i];
  return '#' + hexs.join('');
}

//得到hex颜色值为color的加深颜色值，level为加深的程度，限0-1之间
export function getDarkColor(color: string, level: number) {
  const r = /^#?[0-9a-fA-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  //floor 向下取整
  for (let i = 0; i < 3; i++) rgbc[i] = Math.floor(rgbc[i] * (1 - level));
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}

//得到hex颜色值为color的减淡颜色值，level为加深的程度，限0-1之间
export function getLightColor(color: string, level: number) {
  const r = /^#?[0-9a-fA-F]{6}$/;
  if (!r.test(color)) return '';
  const rgbc = HexToRgb(color);
  for (let i = 0; i < 3; i++)
    rgbc[i] = Math.floor((255 - rgbc[i]) * level + rgbc[i]);
  return RgbToHex(rgbc[0], rgbc[1], rgbc[2]);
}
/****************************时间处理**************************************/

/**
 * 百纳秒转时码
 * @param ns
 * @param dFrameRate
 *  @param dropFrame
 */
export const l100Ns2Tc$1 = (
  ns: number,
  dFrameRate?: boolean,
  dropFrame?: boolean,
) =>
  Number.isNaN(ns)
    ? ''
    : (window as any).TimeCodeConvert?.l100Ns2Tc$1(ns, dFrameRate, dropFrame);

// 时间戳获取年月日 "2020年12月31日"
export const formatDateNoTime = (time = +new Date()) => {
  const dateTime = new Date(time);
  const year = dateTime.getFullYear();
  const month = dateTime.getMonth() + 1;
  const date = dateTime.getDate();
  return `${year}年${month}月${date}日`;
};

// 时间戳获取年月日 "2020.12.31"
export const formatDate = (time = +new Date()) => {
  const dateTime = new Date(time);
  const year = dateTime.getFullYear();
  const month = dateTime.getMonth() + 1;
  const date = dateTime.getDate();
  return `${year}.${month}.${date}`;
};
// 时间戳获取年月日 "2020-12-31 10:00:00"
export const formatDateHours = (time = +new Date()) => {
  const dateTime = new Date(time);
  const year = dateTime.getFullYear();
  const month = dateTime.getMonth() + 1;
  const date = dateTime.getDate();
  const H = dateTime.getHours();
  const m = dateTime.getMinutes();
  const s = dateTime.getSeconds();
  return `${year}-${month}-${date} ${H<10?'0'+H:H}:${m<10?'0'+m:m}:${s<10?'0'+s:s}`;
};
// 毫秒转时分 "0h0min0s"
export const millisecondFormat = (time:any) => {
  if(time === 0 || !time) return '0h0min';
  const h = (time % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60);
  const m = (time % (1000 * 60 * 60)) / (1000 * 60);
  const s = (time % (1000 * 60)) / 1000;
  return `${h.toFixed(0)}h${m.toFixed(0)}min`;
};

/**************************脚本文件异步加载***********************************/
export const getScriptDomFromUrl = (url: string) => {
  let dom;
  if (/.+\.js$/.test(url)) {
    dom = document.createElement('SCRIPT');
    dom.setAttribute('type', 'text/javascript');
    dom.setAttribute('src', url);
  } // css
  else {
    dom = document.createElement('link');
    dom.href = url;
    dom.type = 'text/css';
    dom.rel = 'stylesheet';
  }
  return dom;
};
const asyncLoadedScripts: any = {};
const asyncLoadedScriptsCallbackQueue: any = {};
/**
 * 异步加载script
 * @param url
 * @param callback
 */
export const asyncLoadScript = (
  url: string,
  callback?: (() => void) | undefined,
): Promise<void> => {
  return new Promise(resolve => {
    if (asyncLoadedScripts[url] !== undefined) {
      // 已加载script标签
      if (callback && typeof callback === 'function') {
        if (asyncLoadedScripts[url] === 0) {
          // 未执行首个script标签的回调
          if (!asyncLoadedScriptsCallbackQueue[url]) {
            asyncLoadedScriptsCallbackQueue[url] = [];
          }
          asyncLoadedScriptsCallbackQueue[url].push(callback);
        } else {
          callback.apply(window, []);
        }
      }
      resolve();
      return;
    }
    asyncLoadedScripts[url] = 0;
    const scriptDom: any = getScriptDomFromUrl(url);
    if (scriptDom.readyState) {
      scriptDom.onreadystatechange = () => {
        if (
          scriptDom.readyState === 'loaded' ||
          scriptDom.readyState === 'complete'
        ) {
          scriptDom.onreadystatechange = null;
          asyncLoadedScripts[url] = 1;
          resolve();
          if (callback && typeof callback === 'function') {
            callback.apply(window, []);
          }
          if (asyncLoadedScriptsCallbackQueue[url]) {
            for (
              let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
              i < j;
              i++
            ) {
              asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
            }
            asyncLoadedScriptsCallbackQueue[url] = undefined;
          }
        }
      };
    } else {
      scriptDom.onload = () => {
        asyncLoadedScripts[url] = 1;
        resolve();
        if (callback && typeof callback === 'function') {
          callback.apply(window, []);
        }
        if (asyncLoadedScriptsCallbackQueue[url]) {
          for (
            let i = 0, j = asyncLoadedScriptsCallbackQueue[url].length;
            i < j;
            i++
          ) {
            asyncLoadedScriptsCallbackQueue[url][i].apply(window, []);
          }
          asyncLoadedScriptsCallbackQueue[url] = undefined;
        }
      };
    }
    document.getElementsByTagName('head')[0].appendChild(scriptDom);
  });
};

// 单位换算
export const unitTransfer = (data:any) => {
  //默认传过来的是GB 需要处理一下
  let temp:any = 0 + 'MB';
  if(data<1){
    temp = (data*1024).toFixed(1)+'MB';
    // return temp??0 + 'MB';
  }
  else if(data<1000){
    temp = data.toFixed(1)+'GB';
  }else if(data<100000){
    temp = (data/1024).toFixed(1)+'TB';
  }else{

  }
  return temp;
};
//字节单位智能转换
export const byteTransfer=( bytes:any)=>{
  let temp:any = 0 + 'B';
  if (bytes === 0) {
    temp='0B'
  }else if(bytes === -1){
    temp='不限'
  }else{
    let k = 1024, // or 1000
    sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
    i = Math.floor(Math.log(bytes) / Math.log(k));
    const size:any = ((bytes / Math.pow(k, i)).toPrecision(3));
    temp = (isNaN(size)?0:size) + (sizes[i] || 'B');
  }
   return temp;
};
//百纳秒转换时分秒
export const l100Ns2Hms = (l100Ns:any) => {
  const dHour = (Math.floor(l100Ns / (3600 * Math.pow(10.0, 7))));
  let llResidue:any = (l100Ns % (3600 * Math.pow(10.0, 7)));
  const dMin = (Math.floor(llResidue / (60 * Math.pow(10.0, 7))));
  llResidue = llResidue % (Math.floor(60 * Math.pow(10.0, 7)));
  const dSec = (Math.floor(llResidue / (Math.pow(10.0, 7))));
  return `${dHour?(dHour + 'h'):''}${dMin?(dMin + 'min'):''}${dSec}s`
};
// 去除复制剪切的内容样式
export  const textInit = (e:any,editor:any) => {
  const win = (window as any);
  const doc = (document as any);
  e.preventDefault();
  var text;
  var clp = (e.originalEvent || e).clipboardData;
  if (clp === undefined || clp === null) {
      text = win.clipboardData.getData("text") || "";
      if (text !== "") {
          if (win.getSelection) {
              var newNode = doc.createElement("span");
              newNode.innerHTML = text;
              win.getSelection().getRangeAt(0).insertNode(newNode);
          } else {
            doc.selection.createRange().pasteHTML(text);
          }
      }
  } else {
      text = clp.getData('text/plain') || "";
      if (text !== "") {
        // doc.execCommand('insertText', false, text); //这个官网已经弃用
        editor.insertContent(text);
      }
  }
};
//html防注入转换
//这么做的意义在于，当你不是有意地使用 <div dangerouslySetInnerHTML={createMarkup()} /> 时候，
// 它并不会被渲染，因为 createMarkup() 返回的格式是 字符串 而不是一个 {__html: ''} 对象。{__html:...} 
// 背后的目的是表明它会被当成 "type/taint" 类型处理。 这种包裹对象，可以通过方法调用返回净化后的数据，随后这种标记过的数据可以被传递给dangerouslySetInnerHTML 
export  const createMarkup = (detail:any) => {
  return { __html: detail};
};
export const copyObject: <T>(obj: T) => T = oldObj => {
  if (oldObj instanceof Object) {
    // return JSON.parse(JSON.stringify(oldObj)); //这种强转会使文件丢失orignalFile属性
    return cloneDeep(oldObj);
  } else {
    return oldObj;
  }
};
export const getUuid = () => {
  if (typeof crypto === 'object') {
    if (typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID().replaceAll("-", "");
    }
    if (
      typeof crypto.getRandomValues === 'function' &&
      typeof Uint8Array === 'function'
    ) {
      const callback = (c: any) => {
        const num = Number(c);
        return (
          num ^
          (crypto.getRandomValues(new Uint8Array(1))[0] &
            (15 >> (num / 4)))
        ).toString(16);
      };
      return (
        '' +
        [1e7] +
        -1e3 +
        -4e3 +
        -8e3 +
        -1e11
      ).replace(/[018]/g, callback).replaceAll("-", "");
    }
  }
  let timestamp = new Date().getTime();
  let perForNow =
    (typeof performance !== 'undefined' &&
      performance.now &&
      performance.now() * 1000) ||
    0;
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    (c) => {
      let random = Math.random() * 16;
      if (timestamp > 0) {
        random = (timestamp + random) % 16 | 0;
        timestamp = Math.floor(timestamp / 16);
      } else {
        random = (perForNow + random) % 16 | 0;
        perForNow = Math.floor(perForNow / 16);
      }
      return (c === 'x'
        ? random
        : (random & 0x3) | 0x8
      ).toString(16);
    },
  ).replaceAll("-", "");
};
/**
   * @description 类型
   */
  export type Type =
    | 'number'
    | 'string'
    | 'boolean'
    | 'symbol'
    | 'array'
    | 'function'
    | 'error'
    | 'date'
    | 'object'
    | 'regexp'
    | 'blob'
    | 'null';

  /**
   * @description 获取准确类型
   */
  export const getType = (o: any) => {
    return Object.prototype.toString
      .call(o)
      .slice(8, -1)
      .toLowerCase() as unknown as Type;
  };
  export const isArray = (value: any): value is any[] => {
    return getType(value) === 'array';
  };

  /**
   * @description: 二进制流下载
   * @param  fileStream 文件流
   * @param  fileName 文件名
   */
  export async function download(
    fileStream: any | Blob,
    fileName?: string,
  ) {
    const blob =
      getType(fileStream) === 'blob'
        ? fileStream
        : new Blob([fileStream]);
    // 通过fileStream生成url；
    const url = window.URL.createObjectURL(blob);
    downloadUrl(url, fileName);
    // 释放url；
    window.URL.revokeObjectURL(url);
  }
  export function downloadUrl(
    url: string,
    filename?: string,
  ) {
    const defaultFilename = getFilenameFromUrl(url);
    const link = document.createElement('a');
    // 编辑a标签的href属性；
    link.href = url;

    // 编辑a标签的download属性(下载文件时生成的默认文件名);
    link.setAttribute(
      'download',
      filename ?? defaultFilename,
    );
    link.setAttribute('target', '_blank');
    // 将a标签添加到body中；
    document.body.appendChild(link);
    // 点击事件；
    link.click();
    // 移除a标签；
    document.body.removeChild(link);
  }
  export function getFilenameFromUrl(url: string) {
    const filename = url.split('/').pop();
    if (filename) {
      return decodeURIComponent(filename);
    }
    return 'file';
  }
  export function downloadBase64(
    base64: string,
    fileName?: string,
  ) {
    const a = document.createElement('a');
    a.href = base64;
    a.download = fileName ?? 'file';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  }