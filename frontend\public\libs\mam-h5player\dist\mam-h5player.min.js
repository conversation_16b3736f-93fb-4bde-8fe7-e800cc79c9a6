!function(e){function t(i){if(n[i])return n[i].exports;var o=n[i]={i:i,l:!1,exports:{}};return e[i].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var n={};t.m=e,t.c=n,t.d=function(e,n,i){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:i})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/libs/mam-h5player/dist/",t(t.s=8)}([function(e,t){e.exports="data:image/svg+xml;base64,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"},function(e,t){e.exports="data:image/svg+xml;base64,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"},function(e,t,n){"use strict";var i,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};i=function(){return this}();try{i=i||Function("return this")()||(0,eval)("this")}catch(e){"object"===("undefined"==typeof window?"undefined":o(window))&&(i=window)}e.exports=i},function(e,t){
e.exports="data:image/svg+xml;base64,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"},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=function(){function e(t){i(this,e),this.volumnTrack=t}return o(e,[{key:"createAudioMeter",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.98,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.95,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:750,o=this,a=e.createScriptProcessor(512,2,2);return a.clippingL=!1,a.clippingR=!1,a.lastLClip=0,a.lastRClip=0,a.volume=0,a.volumeL=0,a.volumeR=0,a.clipLevel=t,a.averaging=n,a.clipLag=i,a.volumnTrack=o.volumnTrack,a.checkLClipping=function(){return!!this.clippingL&&(this.lastLClip+this.clipLag<window.performance.now()&&(this.clippingL=!1),this.clippingL)},a.checkRClipping=function(){return!!this.clippingR&&(this.lastRClip+this.clipLag<window.performance.now()&&(this.clippingR=!1),this.clippingR)},a.shutdown=function(){this.disconnect(),this.onaudioprocess=null},a.shutup=function(){this.onaudioprocess=o.volumeAudioProcess,this.connect(e.destination)},a}},{key:"volumeAudioProcess",value:function(e){for(var t,n,i=e.inputBuffer,o=i.getChannelData(0),a=i.getChannelData(1),r=o.length,s=a.length,l=Math.max(r,s),c=0,d=0,u=0,m=0;m<l;++m)Number.isNaN(+o[m])||(t=o[m]),Number.isNaN(+a[m])||(n=a[m]),this.volumnTrack.isDoubleChannel?(c=Math.max(Math.abs(t),c),d=Math.max(Math.abs(n),d)):u=Math.max(Math.abs(t+n),u);this.volumnTrack.isDoubleChannel?(this.volumeL=c,this.volumeR=d,this.volumnTrack.render(c,d)):this.volume=u}}]),e}();t.default=a},function(e,t,n){"use strict";e.exports=function(e){return"string"!=typeof e?e:(/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),/["'() \t\n]/.test(e)?'"'+e.replace(/"/g,'\\"').replace(/\n/g,"\\n")+'"':e)}},function(e,t,n){"use strict";function i(e,t){var n=e[1]||"",i=e[3];if(!i)return n;if(t&&"function"==typeof btoa){var a=o(i);return[n].concat(i.sources.map(function(e){return"/*# sourceURL="+i.sourceRoot+e+" */"})).concat([a]).join("\n")}return[n].join("\n")}function o(e){return"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(e))))+" */"}e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n=i(t,e);return t[2]?"@media "+t[2]+"{"+n+"}":n}).join("")},t.i=function(e,n){"string"==typeof e&&(e=[[null,e,""]]);for(var i={},o=0;o<this.length;o++){var a=this[o][0];"number"==typeof a&&(i[a]=!0)}for(o=0;o<e.length;o++){var r=e[o];"number"==typeof r[0]&&i[r[0]]||(n&&!r[2]?r[2]=n:n&&(r[2]="("+r[2]+") and ("+n+")"),t.push(r))}},t}},function(e,t,n){function i(e,t){for(var n=0;n<e.length;n++){var i=e[n],o=f[i.id];if(o){o.refs++;for(var a=0;a<o.parts.length;a++)o.parts[a](i.parts[a]);for(;a<i.parts.length;a++)o.parts.push(d(i.parts[a],t))}else{for(var r=[],a=0;a<i.parts.length;a++)r.push(d(i.parts[a],t));f[i.id]={id:i.id,refs:1,parts:r}}}}function o(e,t){for(var n=[],i={},o=0;o<e.length;o++){var a=e[o],r=t.base?a[0]+t.base:a[0],s=a[1],l=a[2],c=a[3],d={css:s,media:l,sourceMap:c};i[r]?i[r].parts.push(d):n.push(i[r]={id:r,parts:[d]})}return n}function a(e,t){var n=v(e.insertInto);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid.");var i=b[b.length-1];if("top"===e.insertAt)i?i.nextSibling?n.insertBefore(t,i.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),b.push(t);else if("bottom"===e.insertAt)n.appendChild(t);else{if("object"!=typeof e.insertAt||!e.insertAt.before)throw new Error("[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n");var o=v(e.insertInto+" "+e.insertAt.before);n.insertBefore(t,o)}}function r(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e);var t=b.indexOf(e);t>=0&&b.splice(t,1)}function s(e){var t=document.createElement("style");return e.attrs.type="text/css",c(t,e.attrs),a(e,t),t}function l(e){var t=document.createElement("link");return e.attrs.type="text/css",e.attrs.rel="stylesheet",c(t,e.attrs),a(e,t),t}function c(e,t){Object.keys(t).forEach(function(n){e.setAttribute(n,t[n])})}function d(e,t){var n,i,o,a;if(t.transform&&e.css){if(!(a=t.transform(e.css)))return function(){};e.css=a}if(t.singleton){var c=I++;n=y||(y=s(t)),i=u.bind(null,n,c,!1),o=u.bind(null,n,c,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=l(t),i=p.bind(null,n,t),o=function(){r(n),n.href&&URL.revokeObjectURL(n.href)}):(n=s(t),i=m.bind(null,n),o=function(){r(n)});return i(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;i(e=t)}else o()}}function u(e,t,n,i){var o=n?"":i.css;if(e.styleSheet)e.styleSheet.cssText=k(t,o);else{var a=document.createTextNode(o),r=e.childNodes;r[t]&&e.removeChild(r[t]),r.length?e.insertBefore(a,r[t]):e.appendChild(a)}}function m(e,t){var n=t.css,i=t.media;if(i&&e.setAttribute("media",i),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function p(e,t,n){var i=n.css,o=n.sourceMap,a=void 0===t.convertToAbsoluteUrls&&o;(t.convertToAbsoluteUrls||a)&&(i=x(i)),o&&(i+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(o))))+" */");var r=new Blob([i],{type:"text/css"}),s=e.href;e.href=URL.createObjectURL(r),s&&URL.revokeObjectURL(s)}var f={},g=function(e){var t;return function(){return void 0===t&&(t=e.apply(this,arguments)),t}}(function(){return window&&document&&document.all&&!window.atob}),h=function(e){return document.querySelector(e)},v=function(e){var t={};return function(e){if("function"==typeof e)return e();if(void 0===t[e]){var n=h.call(this,e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}}(),y=null,I=0,b=[],x=n(36);e.exports=function(e,t){if("undefined"!=typeof DEBUG&&DEBUG&&"object"!=typeof document)throw new Error("The style-loader cannot be used in a non-browser environment");t=t||{},t.attrs="object"==typeof t.attrs?t.attrs:{},t.singleton||"boolean"==typeof t.singleton||(t.singleton=g()),t.insertInto||(t.insertInto="head"),t.insertAt||(t.insertAt="bottom");var n=o(e,t);return i(n,t),function(e){for(var a=[],r=0;r<n.length;r++){var s=n[r],l=f[s.id];l.refs--,a.push(l)}if(e){i(o(e,t),t)}for(var r=0;r<a.length;r++){var l=a[r];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete f[l.id]}}}};var k=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t,n){"use strict";var i=document.currentScript.src.split("/");i.pop(),window.currentScriptSrc=i.join("/"),console.log("当前路径",i,window.currentScriptSrc),n(9),n(13),n(14),n(15),n(16),n(17),n(18),n(19),n(20),n(21),n(22),n(23),n(24),n(25),n(26),n(28),n(29),n(30),n(31),n(32),n(33),n(34),n(37),n(41),n(42),n(43),n(44),$.fn.h5mediaplayer=function(e){var t={pluginPath:"/dist/",playText:"播放".l("player.play"),pauseText:"暂停",stopText:"停止",fullscreenText:"全屏",muteText:"Mute volume",unmuteText:"Unmute volume",alwaysShowControls:!0,playbackRate:.5,showPosterWhenEnded:!0,showPosterWhenPaused:!0,dash:{path:"../dist/dash.all.min.js"},features:["playpause","current","progress","duration","tracks","volume","fullscreen","contextmenu","speed","backframe","prevframe","skipback","skipforward","trimin","trimout","cleantrim","seeking","getKeyframe","setSection","exportSection","saveSection","togglekeypoint","selectScreen","createGif"],success:function(e){},setTrimin:function(e,t,n){},setTrimout:function(e,t,n){},cleanTrim:function(e){},getKeyframe:function(e,t,n){},exportSection:function(e,t,n,i){},saveSection:function(e,t,n,i){}};if(e){e.pluginPath&&(t.dash.path=e.pluginPath+"dash.all.min.js"),e.frameRate&&(t.framesPerSecond=e.frameRate),t.isVideo="video"===e.playType,t.isAudio="audio"===e.playType;var n=function(e){$(e.controls).find(".btn-no-use-by-trim").hasClass("btn_can_use")||$(e.controls).find(".btn-no-use-by-trim").addClass("btn_can_use")},i=function(e){$(e.controls).find(".btn-no-use-by-trim").removeClass("btn_can_use")};if(e.setTrimin){var o=e.setTrimin;e.setTrimin=function(e,t,i){n(e),o(e,t,i)}}if(e.setTrimout){var o=e.setTrimout;e.setTrimout=function(e,t,i){n(e),o(e,t,i)}}if(e.cleanTrim){var o=e.cleanTrim;e.cleanTrim=function(e,t,n){i(e),o(e,t,n)}}}var a=new MediaElementPlayer($(this)[0],$.extend(t,e));a.node.addEventListener("loadedmetadata",function(){"VIDEO"===a.node.tagName&&r()});var r=function e(){var t=a.getCurrentKeyframe();if(!t)return void setTimeout(function(){e()},500);var n=new Image;n.crossOrigin="Anonymous",n.src=t,n.onload=function(){a.playerNaturalWidth=n.naturalWidth,a.playerNaturalHeight=n.naturalHeight}};return a}},function(e,t,n){"use strict";(function(e,t){var n,n,i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function e(t,i,o){function a(s,l){if(!i[s]){if(!t[s]){var c="function"==typeof n&&n;if(!l&&c)return n(s,!0);if(r)return r(s,!0);var d=new Error("Cannot find module '"+s+"'");throw d.code="MODULE_NOT_FOUND",d}var u=i[s]={exports:{}};t[s][0].call(u.exports,function(e){var n=t[s][1][e];return a(n||e)},u,u.exports,e,t,i,o)}return i[s].exports}for(var r="function"==typeof n&&n,s=0;s<o.length;s++)a(o[s]);return a}({1:[function(e,t,n){},{}],2:[function(t,n,i){(function(e){var i,o=void 0!==e?e:"undefined"!=typeof window?window:{},a=t(1);"undefined"!=typeof document?i=document:(i=o["__GLOBAL_DOCUMENT_CACHE@4"])||(i=o["__GLOBAL_DOCUMENT_CACHE@4"]=a),n.exports=i}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{1:1}],3:[function(t,n,i){(function(e){var t;t="undefined"!=typeof window?window:void 0!==e?e:"undefined"!=typeof self?self:{},n.exports=t}).call(this,void 0!==e?e:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(e,n,o){!function(e){function o(){}function a(e,t){return function(){e.apply(t,arguments)}}function r(e){if("object"!==i(this))throw new TypeError("Promises must be constructed via new");if("function"!=typeof e)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],m(e,this)}function s(e,t){for(;3===e._state;)e=e._value;if(0===e._state)return void e._deferreds.push(t);e._handled=!0,r._immediateFn(function(){var n=1===e._state?t.onFulfilled:t.onRejected;if(null===n)return void(1===e._state?l:c)(t.promise,e._value);var i;try{i=n(e._value)}catch(e){return void c(t.promise,e)}l(t.promise,i)})}function l(e,t){try{if(t===e)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"===(void 0===t?"undefined":i(t))||"function"==typeof t)){var n=t.then;if(t instanceof r)return e._state=3,e._value=t,void d(e);if("function"==typeof n)return void m(a(n,t),e)}e._state=1,e._value=t,d(e)}catch(t){c(e,t)}}function c(e,t){e._state=2,e._value=t,d(e)}function d(e){2===e._state&&0===e._deferreds.length&&r._immediateFn(function(){e._handled||r._unhandledRejectionFn(e._value)});for(var t=0,n=e._deferreds.length;t<n;t++)s(e,e._deferreds[t]);e._deferreds=null}function u(e,t,n){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.promise=n}function m(e,t){var n=!1;try{e(function(e){n||(n=!0,l(t,e))},function(e){n||(n=!0,c(t,e))})}catch(e){if(n)return;n=!0,c(t,e)}}var p=setTimeout;r.prototype.catch=function(e){return this.then(null,e)},r.prototype.then=function(e,t){var n=new this.constructor(o);return s(this,new u(e,t,n)),n},r.all=function(e){var t=Array.prototype.slice.call(e);return new r(function(e,n){function o(r,s){try{if(s&&("object"===(void 0===s?"undefined":i(s))||"function"==typeof s)){var l=s.then;if("function"==typeof l)return void l.call(s,function(e){o(r,e)},n)}t[r]=s,0==--a&&e(t)}catch(e){n(e)}}if(0===t.length)return e([]);for(var a=t.length,r=0;r<t.length;r++)o(r,t[r])})},r.resolve=function(e){return e&&"object"===(void 0===e?"undefined":i(e))&&e.constructor===r?e:new r(function(t){t(e)})},r.reject=function(e){return new r(function(t,n){n(e)})},r.race=function(e){return new r(function(t,n){for(var i=0,o=e.length;i<o;i++)e[i].then(t,n)})},r._immediateFn="function"==typeof t&&function(e){t(e)}||function(e){p(e,0)},r._unhandledRejectionFn=function(e){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",e)},r._setImmediateFn=function(e){r._immediateFn=e},r._setUnhandledRejectionFn=function(e){r._unhandledRejectionFn=e},void 0!==n&&n.exports?n.exports=r:e.Promise||(e.Promise=r)}(this)},{}],5:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var o="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},a=e(7),r=function(e){return e&&e.__esModule?e:{default:e}}(a),s=e(15),l=e(27),c={lang:"en",en:s.EN};c.language=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(null!==t&&void 0!==t&&t.length){if("string"!=typeof t[0])throw new TypeError("Language code must be a string value");if(!/^[a-z]{2,3}((\-|_)[a-z]{2})?$/i.test(t[0]))throw new TypeError("Language code must have format 2-3 letters and. optionally, hyphen, underscore followed by 2 more letters");c.lang=t[0],void 0===c[t[0]]?(t[1]=null!==t[1]&&void 0!==t[1]&&"object"===o(t[1])?t[1]:{},c[t[0]]=(0,l.isObjectEmpty)(t[1])?s.EN:t[1]):null!==t[1]&&void 0!==t[1]&&"object"===o(t[1])&&(c[t[0]]=t[1])}return c.lang},c.t=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof e&&e.length){var n=void 0,i=void 0,a=c.language(),r=function(e,t,n){return"object"!==(void 0===e?"undefined":o(e))||"number"!=typeof t||"number"!=typeof n?e:function(){return[function(){return arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 0===(arguments.length<=0?void 0:arguments[0])||1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:0!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])||11===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])||12===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>0&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:[3]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1&&(arguments.length<=0?void 0:arguments[0])%100!=11?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])>=2&&(arguments.length<=0?void 0:arguments[0])<=4?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return(arguments.length<=0?void 0:arguments[0])%100==1?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100==2?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100==3||(arguments.length<=0?void 0:arguments[0])%100==4?arguments.length<=4?void 0:arguments[4]:arguments.length<=1?void 0:arguments[1]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])>2&&(arguments.length<=0?void 0:arguments[0])<7?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])>6&&(arguments.length<=0?void 0:arguments[0])<11?arguments.length<=4?void 0:arguments[4]:arguments.length<=5?void 0:arguments[5]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:(arguments.length<=0?void 0:arguments[0])%100>=3&&(arguments.length<=0?void 0:arguments[0])%100<=10?arguments.length<=4?void 0:arguments[4]:(arguments.length<=0?void 0:arguments[0])%100>=11?arguments.length<=5?void 0:arguments[5]:arguments.length<=6?void 0:arguments[6]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:0===(arguments.length<=0?void 0:arguments[0])||(arguments.length<=0?void 0:arguments[0])%100>1&&(arguments.length<=0?void 0:arguments[0])%100<11?arguments.length<=2?void 0:arguments[2]:(arguments.length<=0?void 0:arguments[0])%100>10&&(arguments.length<=0?void 0:arguments[0])%100<20?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10==2?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 11!==(arguments.length<=0?void 0:arguments[0])&&(arguments.length<=0?void 0:arguments[0])%10==1?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:(arguments.length<=0?void 0:arguments[0])%10>=2&&(arguments.length<=0?void 0:arguments[0])%10<=4&&((arguments.length<=0?void 0:arguments[0])%100<10||(arguments.length<=0?void 0:arguments[0])%100>=20)?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:8!==(arguments.length<=0?void 0:arguments[0])&&11!==(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:arguments.length<=2?void 0:arguments[2]},function(){return 1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:2===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:3===(arguments.length<=0?void 0:arguments[0])?arguments.length<=3?void 0:arguments[3]:arguments.length<=4?void 0:arguments[4]},function(){return 0===(arguments.length<=0?void 0:arguments[0])?arguments.length<=1?void 0:arguments[1]:1===(arguments.length<=0?void 0:arguments[0])?arguments.length<=2?void 0:arguments[2]:arguments.length<=3?void 0:arguments[3]}]}()[n].apply(null,[t].concat(e))};return void 0!==c[a]&&(n=c[a][e],null!==t&&"number"==typeof t&&(i=c[a]["mejs.plural-form"],n=r.apply(null,[n,t,i]))),!n&&c.en&&(n=c.en[e],null!==t&&"number"==typeof t&&(i=c.en["mejs.plural-form"],n=r.apply(null,[n,t,i]))),n=n||e,null!==t&&"number"==typeof t&&(n=n.replace("%1",t)),(0,l.escapeHTML)(n)}return e},r.default.i18n=c,"undefined"!=typeof mejsL10n&&r.default.i18n.language(mejsL10n.language,mejsL10n.strings),n.default=c},{15:15,27:27,7:7}],6:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var r="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},s=e(3),l=o(s),c=e(2),d=o(c),u=e(7),m=o(u),p=e(27),f=e(28),g=e(8),h=e(25),v=function e(t,n,i){var o=this;a(this,e);var s=this;i=Array.isArray(i)?i:null,s.defaults={renderers:[],fakeNodeName:"mediaelementwrapper",pluginPath:"build/",shimScriptAccess:"sameDomain"},n=Object.assign(s.defaults,n),s.mediaElement=d.default.createElement(n.fakeNodeName);var c=t,u=!1;if("string"==typeof t?s.mediaElement.originalNode=d.default.getElementById(t):(s.mediaElement.originalNode=t,c=t.id),void 0===s.mediaElement.originalNode||null===s.mediaElement.originalNode)return null;s.mediaElement.options=n,c=c||"mejs_"+Math.random().toString().slice(2),s.mediaElement.originalNode.setAttribute("id",c+"_from_mejs"),["video","audio"].indexOf(s.mediaElement.originalNode.tagName.toLowerCase())>-1&&!s.mediaElement.originalNode.getAttribute("preload")&&s.mediaElement.originalNode.setAttribute("preload","none"),s.mediaElement.originalNode.parentNode.insertBefore(s.mediaElement,s.mediaElement.originalNode),s.mediaElement.appendChild(s.mediaElement.originalNode);var v=function(e,t){if("https:"===l.default.location.protocol&&0===e.indexOf("http:")&&h.IS_IOS&&m.default.html5media.mediaTypes.indexOf(t)>-1){var n=new XMLHttpRequest;n.onreadystatechange=function(){if(4===this.readyState&&200===this.status){var t=l.default.URL||l.default.webkitURL,n=t.createObjectURL(this.response);return s.mediaElement.originalNode.setAttribute("src",n),n}return e},n.open("GET",e),n.responseType="blob",n.send()}return e},y=void 0;if(null!==i)y=i;else if(null!==s.mediaElement.originalNode)switch(y=[],s.mediaElement.originalNode.nodeName.toLowerCase()){case"iframe":y.push({type:"",src:s.mediaElement.originalNode.getAttribute("src")});break;case"audio":case"video":var I=s.mediaElement.originalNode.children.length,b=s.mediaElement.originalNode.getAttribute("src");if(b){var x=s.mediaElement.originalNode,k=(0,f.formatType)(b,x.getAttribute("type"));y.push({type:k,src:v(b,k)})}for(var j=0;j<I;j++){var C=s.mediaElement.originalNode.children[j];if("source"===C.tagName.toLowerCase()){var S=C.getAttribute("src"),M=(0,f.formatType)(S,C.getAttribute("type"));y.push({type:M,src:v(S,M)})}}}s.mediaElement.id=c,s.mediaElement.renderers={},s.mediaElement.events={},s.mediaElement.promises=[],s.mediaElement.renderer=null,s.mediaElement.rendererName=null,s.mediaElement.changeRenderer=function(e,t){var n=o,i=Object.keys(t[0]).length>2?t[0]:t[0].src;if(void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&n.mediaElement.renderer.name===e)return n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.show(),n.mediaElement.renderer.setSrc(i),!0;void 0!==n.mediaElement.renderer&&null!==n.mediaElement.renderer&&(n.mediaElement.renderer.pause(),n.mediaElement.renderer.stop&&n.mediaElement.renderer.stop(),n.mediaElement.renderer.hide());var a=n.mediaElement.renderers[e],r=null;if(void 0!==a&&null!==a)return a.show(),a.setSrc(i),n.mediaElement.renderer=a,n.mediaElement.rendererName=e,!0;for(var s=n.mediaElement.options.renderers.length?n.mediaElement.options.renderers:g.renderer.order,l=0,c=s.length;l<c;l++){var d=s[l];if(d===e){r=g.renderer.renderers[d];var u=Object.assign(r.options,n.mediaElement.options);return a=r.create(n.mediaElement,u,t),a.name=e,n.mediaElement.renderers[r.name]=a,n.mediaElement.renderer=a,n.mediaElement.rendererName=e,a.show(),!0}}return!1},s.mediaElement.setSize=function(e,t){void 0!==s.mediaElement.renderer&&null!==s.mediaElement.renderer&&s.mediaElement.renderer.setSize(e,t)},s.mediaElement.generateError=function(e,t){e=e||"",t=Array.isArray(t)?t:[];var n=(0,p.createEvent)("error",s.mediaElement);n.message=e,n.urls=t,s.mediaElement.dispatchEvent(n),u=!0}
;var w=m.default.html5media.properties,T=m.default.html5media.methods,N=function(e,t,n,i){var o=e[t],a=function(){return n.apply(e,[o])},r=function(t){return o=i.apply(e,[t])};Object.defineProperty(e,t,{get:a,set:r})},E=function(){return void 0!==s.mediaElement.renderer&&null!==s.mediaElement.renderer?s.mediaElement.renderer.getSrc():null},_=function(e){var t=[];if("string"==typeof e)t.push({src:e,type:e?(0,f.getTypeFromFile)(e):""});else if("object"===(void 0===e?"undefined":r(e))&&void 0!==e.src){var n=(0,f.absolutizeUrl)(e.src),i=e.type,o=Object.assign(e,{src:n,type:""!==i&&null!==i&&void 0!==i||!n?i:(0,f.getTypeFromFile)(n)});t.push(o)}else if(Array.isArray(e))for(var a=0,l=e.length;a<l;a++){var c=(0,f.absolutizeUrl)(e[a].src),d=e[a].type,u=Object.assign(e[a],{src:c,type:""!==d&&null!==d&&void 0!==d||!c?d:(0,f.getTypeFromFile)(c)});t.push(u)}var m=g.renderer.select(t,s.mediaElement.options.renderers.length?s.mediaElement.options.renderers:[]),h=void 0;return s.mediaElement.paused||(s.mediaElement.pause(),h=(0,p.createEvent)("pause",s.mediaElement),s.mediaElement.dispatchEvent(h)),s.mediaElement.originalNode.src=t[0].src||"",null===m&&t[0].src?void s.mediaElement.generateError("No renderer found",t):t[0].src?s.mediaElement.changeRenderer(m.rendererName,t):null},P=function(e,t){try{if("play"===e&&"native_dash"===s.mediaElement.rendererName){var n=s.mediaElement.renderer[e](t);n&&"function"==typeof n.then&&n.catch(function(){s.mediaElement.paused&&setTimeout(function(){var e=s.mediaElement.renderer.play();void 0!==e&&e.catch(function(){s.mediaElement.renderer.paused||s.mediaElement.renderer.pause()})},150)})}else s.mediaElement.renderer[e](t)}catch(e){s.mediaElement.generateError(e,y)}};N(s.mediaElement,"src",E,_),s.mediaElement.getSrc=E,s.mediaElement.setSrc=_;for(var A=0,D=w.length;A<D;A++)!function(e){if("src"!==e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1),n=function(){return void 0!==s.mediaElement.renderer&&null!==s.mediaElement.renderer&&"function"==typeof s.mediaElement.renderer["get"+t]?s.mediaElement.renderer["get"+t]():null},i=function(e){void 0!==s.mediaElement.renderer&&null!==s.mediaElement.renderer&&"function"==typeof s.mediaElement.renderer["set"+t]&&s.mediaElement.renderer["set"+t](e)};N(s.mediaElement,e,n,i),s.mediaElement["get"+t]=n,s.mediaElement["set"+t]=i}}(w[A]);for(var L=0,z=T.length;L<z;L++)!function(e){s.mediaElement[e]=function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return void 0!==s.mediaElement.renderer&&null!==s.mediaElement.renderer&&"function"==typeof s.mediaElement.renderer[e]&&(s.mediaElement.promises.length?Promise.all(s.mediaElement.promises).then(function(){P(e,n)}).catch(function(e){s.mediaElement.generateError(e,y)}):P(e,n)),null}}(T[L]);return s.mediaElement.addEventListener=function(e,t){s.mediaElement.events[e]=s.mediaElement.events[e]||[],s.mediaElement.events[e].push(t)},s.mediaElement.removeEventListener=function(e,t){if(!e)return s.mediaElement.events={},!0;var n=s.mediaElement.events[e];if(!n)return!0;if(!t)return s.mediaElement.events[e]=[],!0;for(var i=0;i<n.length;i++)if(n[i]===t)return s.mediaElement.events[e].splice(i,1),!0;return!1},s.mediaElement.dispatchEvent=function(e){var t=s.mediaElement.events[e.type];if(t)for(var n=0;n<t.length;n++)t[n].apply(null,[e])},s.mediaElement.destroy=function(){var e=s.mediaElement.originalNode.cloneNode(!0),t=s.mediaElement.parentElement;e.removeAttribute("id"),e.remove(),s.mediaElement.remove(),t.append(e)},y.length&&(s.mediaElement.src=y),s.mediaElement.promises.length?Promise.all(s.mediaElement.promises).then(function(){s.mediaElement.options.success&&s.mediaElement.options.success(s.mediaElement,s.mediaElement.originalNode)}).catch(function(){u&&s.mediaElement.options.error&&s.mediaElement.options.error(s.mediaElement,s.mediaElement.originalNode)}):(s.mediaElement.options.success&&s.mediaElement.options.success(s.mediaElement,s.mediaElement.originalNode),u&&s.mediaElement.options.error&&s.mediaElement.options.error(s.mediaElement,s.mediaElement.originalNode)),s.mediaElement};l.default.MediaElement=v,m.default.MediaElement=v,n.default=v},{2:2,25:25,27:27,28:28,3:3,7:7,8:8}],7:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});var i=e(3),o=function(e){return e&&e.__esModule?e:{default:e}}(i),a={};a.version="4.2.7",a.html5media={properties:["volume","src","currentTime","muted","duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable","currentSrc","preload","bufferedBytes","bufferedTime","initialTime","startOffsetTime","defaultPlaybackRate","playbackRate","played","autoplay","loop","controls"],readOnlyProperties:["duration","paused","ended","buffered","error","networkState","readyState","seeking","seekable"],methods:["load","play","pause","canPlayType"],events:["loadstart","durationchange","loadedmetadata","loadeddata","progress","canplay","canplaythrough","suspend","abort","error","emptied","stalled","play","playing","pause","waiting","seeking","seeked","timeupdate","ended","ratechange","volumechange"],mediaTypes:["audio/mp3","audio/ogg","audio/oga","audio/wav","audio/x-wav","audio/wave","audio/x-pn-wav","audio/mpeg","audio/mp4","video/mp4","video/webm","video/ogg","video/ogv"]},o.default.mejs=a,n.default=a},{3:3}],8:[function(e,t,n){function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0}),n.renderer=void 0;var a="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},r=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),s=e(7),l=function(e){return e&&e.__esModule?e:{default:e}}(s),c=function(){function e(){o(this,e),this.renderers={},this.order=[]}return r(e,[{key:"add",value:function(e){if(void 0===e.name)throw new TypeError("renderer must contain at least `name` property");this.renderers[e.name]=e,this.order.push(e.name)}},{key:"select",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=t.length;if(t=t.length?t:this.order,!n){var i=[/^(html5|native)/i,/^flash/i,/iframe$/i],o=function(e){for(var t=0,n=i.length;t<n;t++)if(i[t].test(e))return t;return i.length};t.sort(function(e,t){return o(e)-o(t)})}for(var a=0,r=t.length;a<r;a++){var s=t[a],l=this.renderers[s];if(null!==l&&void 0!==l)for(var c=0,d=e.length;c<d;c++)if("function"==typeof l.canPlayType&&"string"==typeof e[c].type&&l.canPlayType(e[c].type))return{rendererName:l.name,src:e[c].src}}return null}},{key:"order",set:function(e){if(!Array.isArray(e))throw new TypeError("order must be an array of strings.");this._order=e},get:function(){return this._order}},{key:"renderers",set:function(e){if(null!==e&&"object"!==(void 0===e?"undefined":a(e)))throw new TypeError("renderers must be an array of objects.");this._renderers=e},get:function(){return this._renderers}}]),e}(),d=n.renderer=new c;l.default.Renderers=d},{7:7}],9:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(3),a=i(o),r=e(2),s=i(r),l=e(5),c=i(l),d=e(16),u=i(d),m=e(25),p=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(m),f=e(27),g=e(26),h=e(28);Object.assign(d.config,{usePluginFullScreen:!0,fullscreenText:null,useFakeFullscreen:!1}),Object.assign(u.default.prototype,{isFullScreen:!1,isNativeFullScreen:!1,isInIframe:!1,isPluginClickThroughCreated:!1,fullscreenMode:"",containerSizeTimeout:null,buildfullscreen:function(e){if(e.isVideo){e.isInIframe=a.default.location!==a.default.parent.location,e.detectFullscreenMode();var t=this,n=(0,f.isString)(t.options.fullscreenText)?t.options.fullscreenText:c.default.t("mejs.fullscreen"),i=s.default.createElement("div");if(i.className=t.options.classPrefix+"button "+t.options.classPrefix+"fullscreen-button",i.innerHTML='<button type="button" aria-controls="'+t.id+'" title="'+n+'" aria-label="'+n+'" tabindex="0"></button>',t.addControlElement(i,"fullscreen"),$(i).find("button").css("background-image","url("+t.options.pluginPath+"content/btns.png)"),i.addEventListener("click",function(){p.HAS_TRUE_NATIVE_FULLSCREEN&&p.IS_FULLSCREEN||e.isFullScreen?e.exitFullScreen():e.enterFullScreen()}),e.fullscreenBtn=i,t.options.keyActions.push({keys:[70],action:function(e,t,n,i){i.ctrlKey||void 0!==e.enterFullScreen&&(e.isFullScreen?e.exitFullScreen():e.enterFullScreen())}}),t.exitFullscreenCallback=function(n){27===(n.which||n.keyCode||0)&&(p.HAS_TRUE_NATIVE_FULLSCREEN&&p.IS_FULLSCREEN||t.isFullScreen)&&e.exitFullScreen()},t.globalBind("keydown",t.exitFullscreenCallback),t.normalHeight=0,t.normalWidth=0,p.HAS_TRUE_NATIVE_FULLSCREEN){var o=function(){e.isFullScreen&&(p.isFullScreen()?(e.isNativeFullScreen=!0,e.setControlsSize()):(e.isNativeFullScreen=!1,e.exitFullScreen()))};e.globalBind(p.FULLSCREEN_EVENT_NAME,o)}}},cleanfullscreen:function(e){e.exitFullScreen(),e.globalUnbind("keydown",e.exitFullscreenCallback)},detectFullscreenMode:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName),n="";return p.HAS_TRUE_NATIVE_FULLSCREEN&&t?n="native-native":p.HAS_TRUE_NATIVE_FULLSCREEN&&!t?n="plugin-native":e.usePluginFullScreen&&p.SUPPORT_POINTER_EVENTS&&(n="plugin-click"),e.fullscreenMode=n,n},enterFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(html5|native)/i.test(e.media.rendererName),n=getComputedStyle(e.getElement(e.container));if(!1===e.options.useFakeFullscreen&&p.IS_IOS&&p.HAS_IOS_FULLSCREEN&&"function"==typeof e.media.originalNode.webkitEnterFullscreen&&e.media.originalNode.canPlayType((0,h.getTypeFromFile)(e.media.getSrc())))return void e.media.originalNode.webkitEnterFullscreen();if((0,g.addClass)(s.default.documentElement,e.options.classPrefix+"fullscreen"),(0,g.addClass)(e.getElement(e.container),e.options.classPrefix+"container-fullscreen"),e.normalHeight=parseFloat(n.height),e.normalWidth=parseFloat(n.width),"native-native"!==e.fullscreenMode&&"plugin-native"!==e.fullscreenMode||(p.requestFullScreen(e.getElement(e.container)),e.isInIframe&&setTimeout(function t(){if(e.isNativeFullScreen){var n=a.default.innerWidth||s.default.documentElement.clientWidth||s.default.body.clientWidth,i=screen.width;Math.abs(i-n)>.002*i?e.exitFullScreen():setTimeout(t,500)}},1e3)),e.getElement(e.container).style.width="100%",e.getElement(e.container).style.height="100%",e.containerSizeTimeout=setTimeout(function(){e.getElement(e.container).style.width="100%",e.getElement(e.container).style.height="100%",e.setControlsSize()},500),t)e.node.style.width="100%",e.node.style.height="100%";else for(var i=e.getElement(e.container).querySelectorAll("embed, object, video"),o=i.length,r=0;r<o;r++)i[r].style.width="100%",i[r].style.height="100%";e.options.setDimensions&&"function"==typeof e.media.setSize&&e.media.setSize(screen.width,screen.height);for(var l=e.getElement(e.layers).children,c=l.length,d=0;d<c;d++)l[d].style.width="100%",l[d].style.height="100%";e.fullscreenBtn&&((0,g.removeClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen"),(0,g.addClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen")),e.setControlsSize(),e.isFullScreen=!0;var u=Math.min(screen.width/e.width,screen.height/e.height),m=e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-text");m&&(m.style.fontSize=100*u+"%",m.style.lineHeight="normal",e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-position").style.bottom=(screen.height-e.normalHeight)/2-e.getElement(e.controls).offsetHeight/2+u+15+"px");var v=(0,f.createEvent)("enteredfullscreen",e.getElement(e.container));e.getElement(e.container).dispatchEvent(v)},exitFullScreen:function(){var e=this,t=null!==e.media.rendererName&&/(native|html5)/i.test(e.media.rendererName);if(clearTimeout(e.containerSizeTimeout),p.HAS_TRUE_NATIVE_FULLSCREEN&&(p.IS_FULLSCREEN||e.isFullScreen)&&p.cancelFullScreen(),(0,g.removeClass)(s.default.documentElement,e.options.classPrefix+"fullscreen"),(0,g.removeClass)(e.getElement(e.container),e.options.classPrefix+"container-fullscreen"),e.options.setDimensions){if(e.getElement(e.container).style.width=e.normalWidth+"px",e.getElement(e.container).style.height=e.normalHeight+"px",t)e.node.style.width=e.normalWidth+"px",e.node.style.height=e.normalHeight+"px";else for(var n=e.getElement(e.container).querySelectorAll("embed, object, video"),i=n.length,o=0;o<i;o++)n[o].style.width=e.normalWidth+"px",n[o].style.height=e.normalHeight+"px";"function"==typeof e.media.setSize&&e.media.setSize(e.normalWidth,e.normalHeight);for(var a=e.getElement(e.layers).children,r=a.length,l=0;l<r;l++);}e.fullscreenBtn&&((0,g.removeClass)(e.fullscreenBtn,e.options.classPrefix+"unfullscreen"),(0,g.addClass)(e.fullscreenBtn,e.options.classPrefix+"fullscreen")),e.setControlsSize(),e.isFullScreen=!1;var c=e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-text");c&&(c.style.fontSize="",c.style.lineHeight="",e.getElement(e.container).querySelector("."+e.options.classPrefix+"captions-position").style.bottom="");var d=(0,f.createEvent)("exitedfullscreen",e.getElement(e.container));e.getElement(e.container).dispatchEvent(d)}})},{16:16,2:2,25:25,26:26,27:27,28:28,3:3,5:5}],10:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(16),s=i(r),l=e(5),c=i(l),d=e(27),u=e(26);Object.assign(r.config,{playText:null,pauseText:null}),Object.assign(s.default.prototype,{buildplaypause:function(e,t,n,i){function o(e){"play"===e?((0,u.removeClass)(p,r.options.classPrefix+"play"),(0,u.removeClass)(p,r.options.classPrefix+"replay"),(0,u.addClass)(p,r.options.classPrefix+"pause"),f.setAttribute("title",m),f.setAttribute("aria-label",m)):((0,u.removeClass)(p,r.options.classPrefix+"pause"),(0,u.removeClass)(p,r.options.classPrefix+"replay"),(0,u.addClass)(p,r.options.classPrefix+"play"),f.setAttribute("title",l),f.setAttribute("aria-label",l))}var r=this,s=r.options,l=(0,d.isString)(s.playText)?s.playText:c.default.t("mejs.play"),m=(0,d.isString)(s.pauseText)?s.pauseText:c.default.t("mejs.pause"),p=a.default.createElement("div");p.className=r.options.classPrefix+"button "+r.options.classPrefix+"playpause-button "+r.options.classPrefix+"play",p.innerHTML='<button type="button" aria-controls="'+r.id+'" title="'+l+'" aria-label="'+m+'" tabindex="0"></button>',p.addEventListener("click",function(){r.paused?r.play():r.pause()});var f=p.querySelector("button");r.addControlElement(p,"playpause"),o("pse"),i.addEventListener("loadedmetadata",function(){-1===i.rendererName.indexOf("flash")&&o("pse")}),i.addEventListener("play",function(){o("play")}),i.addEventListener("playing",function(){o("play")}),i.addEventListener("pause",function(){o("pse")}),i.addEventListener("ended",function(){e.options.loop||r.isLockPlayRange||((0,u.removeClass)(p,r.options.classPrefix+"pause"),(0,u.removeClass)(p,r.options.classPrefix+"play"),(0,u.addClass)(p,r.options.classPrefix+"replay"),f.setAttribute("title",l),f.setAttribute("aria-label",l))})}})},{16:16,2:2,26:26,27:27,5:5}],11:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(16),s=i(r),l=e(5),c=i(l),d=e(25),u=e(30),m=e(26);Object.assign(r.config,{enableProgressTooltip:!0,useSmoothHover:!0,forceLive:!1}),Object.assign(s.default.prototype,{buildprogress:function(e,t,n,i){var o=0,r=!1,s=!1,l=this,p=e.options.autoRewind,f=e.options.enableProgressTooltip?'<span class="'+l.options.classPrefix+'time-float"><span class="'+l.options.classPrefix+'time-float-current">00:00</span><span class="'+l.options.classPrefix+'time-float-corner"></span></span>':"",g=a.default.createElement("div");g.className=l.options.classPrefix+"time-rail",g.innerHTML='<span class="'+l.options.classPrefix+"time-total "+l.options.classPrefix+'time-slider"><span class="'+l.options.classPrefix+'time-buffering"></span><span class="'+l.options.classPrefix+'time-loaded"></span><span class="'+l.options.classPrefix+'time-current"></span><span class="'+l.options.classPrefix+'time-hovered no-hover"></span><span class="'+l.options.classPrefix+'time-handle"><span class="'+l.options.classPrefix+'time-handle-content"></span></span>'+f+"</span>",l.addControlElement(g,"progress"),l.rail=t.querySelector("."+l.options.classPrefix+"time-rail"),l.total=t.querySelector("."+l.options.classPrefix+"time-total"),l.loaded=t.querySelector("."+l.options.classPrefix+"time-loaded"),l.current=t.querySelector("."+l.options.classPrefix+"time-current"),l.handle=t.querySelector("."+l.options.classPrefix+"time-handle"),l.timefloat=t.querySelector("."+l.options.classPrefix+"time-float"),l.timefloatcurrent=t.querySelector("."+l.options.classPrefix+"time-float-current"),l.slider=t.querySelector("."+l.options.classPrefix+"time-slider"),l.hovered=t.querySelector("."+l.options.classPrefix+"time-hovered"),l.buffer=t.querySelector("."+l.options.classPrefix+"time-buffering"),l.newTime=0,l.forcedHandlePause=!1,l.setTransformStyle=function(e,t){e.style.transform=t,e.style.webkitTransform=t,e.style.MozTransform=t,e.style.msTransform=t,e.style.OTransform=t},l.buffer.style.display="none";var h=function(t){var n=getComputedStyle(l.total),i=(0,m.offset)(l.total),o=l.total.offsetWidth,a=function(){return void 0!==n.webkitTransform?"webkitTransform":void 0!==n.mozTransform?"mozTransform ":void 0!==n.oTransform?"oTransform":void 0!==n.msTransform?"msTransform":"transform"}(),s=function(){return"WebKitCSSMatrix"in window?"WebKitCSSMatrix":"MSCSSMatrix"in window?"MSCSSMatrix":"CSSMatrix"in window?"CSSMatrix":void 0}(),c=0,p=0,f=0,g=void 0;if(g=t.originalEvent&&t.originalEvent.changedTouches?t.originalEvent.changedTouches[0].pageX:t.changedTouches?t.changedTouches[0].pageX:t.pageX,l.getDuration()){if(g<i.left?g=i.left:g>o+i.left&&(g=o+i.left),f=g-i.left,c=f/o,l.newTime=c*l.getDuration(),l.newTime>=l.getDuration()&&(l.newTime=l.getDuration()-.01),r&&null!==l.getCurrentTime()&&l.newTime.toFixed(4)!==l.getCurrentTime().toFixed(4)&&(l.setCurrentRailHandle(l.newTime),l.setCurrentTime(l.newTime+5e-5),l.updateCurrent(l.newTime)),!d.IS_IOS&&!d.IS_ANDROID){if(f<0&&(f=0),l.options.useSmoothHover&&null!==s&&void 0!==window[s]){var h=new window[s](getComputedStyle(l.handle)[a]),v=h.m41,y=f/parseFloat(getComputedStyle(l.total).width)-v/parseFloat(getComputedStyle(l.total).width);l.hovered.style.left=v+"px",l.setTransformStyle(l.hovered,"scaleX("+y+")"),l.hovered.setAttribute("pos",f),y>=0?(0,m.removeClass)(l.hovered,"negative"):(0,m.addClass)(l.hovered,"negative")}if(l.timefloat){var I=l.timefloat.offsetWidth/2,b=mejs.Utils.offset(l.getElement(l.container)),x=getComputedStyle(l.timefloat);p=g-b.left<l.timefloat.offsetWidth?I:g-b.left>=l.getElement(l.container).offsetWidth-I?l.total.offsetWidth-I:f,(0,m.hasClass)(l.getElement(l.container),l.options.classPrefix+"long-video")&&(p+=parseFloat(x.marginLeft)/2+l.timefloat.offsetWidth/2),l.timefloat.style.left=p+"px",l.timefloatcurrent.innerHTML=(0,u.secondsToTimeCode$)(l.newTime,e.options),l.timefloat.style.display="block"}}}else d.IS_IOS||d.IS_ANDROID||!l.timefloat||(p=l.timefloat.offsetWidth+o>=l.getElement(l.container).offsetWidth?l.timefloat.offsetWidth/2:0,l.timefloat.style.left=p+"px",l.timefloat.style.left=p+"px",l.timefloat.style.display="block")},v=function(){var t=l.getCurrentTime(),n=c.default.t("mejs.time-slider"),o=(0,u.secondsToTimeCode)(t,e.options),a=l.getDuration();l.slider.setAttribute("role","slider"),l.slider.tabIndex=0,i.paused?(l.slider.setAttribute("aria-label",n),l.slider.setAttribute("aria-valuemin",0),l.slider.setAttribute("aria-valuemax",a),l.slider.setAttribute("aria-valuenow",t),l.slider.setAttribute("aria-valuetext",o)):(l.slider.removeAttribute("aria-label"),l.slider.removeAttribute("aria-valuemin"),l.slider.removeAttribute("aria-valuemax"),l.slider.removeAttribute("aria-valuenow"),l.slider.removeAttribute("aria-valuetext"))},y=function(){new Date-o>=1e3&&l.play()},I=function(){r&&null!==l.getCurrentTime()&&l.newTime.toFixed(4)!==l.getCurrentTime().toFixed(4)&&(l.setCurrentTime(l.newTime+5e-5),l.setCurrentRail(),l.updateCurrent(l.newTime+5e-5)),l.forcedHandlePause&&(l.slider.focus(),l.play()),l.forcedHandlePause=!1};l.slider.addEventListener("focus",function(){e.options.autoRewind=!1}),l.slider.addEventListener("blur",function(){e.options.autoRewind=p}),l.slider.addEventListener("keydown",function(t){if(new Date-o>=1e3&&(s=l.paused),l.options.keyActions.length){var n=t.which||t.keyCode||0,a=l.getDuration(),r=(e.options.defaultSeekForwardInterval(i),e.options.defaultSeekBackwardInterval(i),l.getCurrentTime()),c=l.getElement(l.container).querySelector("."+l.options.classPrefix+"volume-slider");if(38===n||40===n)return void(c.style.display="none");switch(c&&(c.style.display="none"),n){case 37:case 39:case 36:case 35:return;case 13:case 32:return void(d.IS_FIREFOX&&(l.paused?l.play():l.pause()));default:return}r=r<0?0:r>=a?a:Math.floor(r),o=new Date,s||e.pause(),r<l.getDuration()&&!s&&setTimeout(y,1100),l.setCurrentTime(r),e.showControls(),t.preventDefault(),t.stopPropagation()}});var b=["mousedown","touchstart"];l.slider.addEventListener("dragstart",function(){return!1});for(var x=0,k=b.length;x<k;x++)l.slider.addEventListener(b[x],function(e){if(l.forcedHandlePause=!1,l.getDuration()!==1/0&&(1===e.which||0===e.which)){l.paused||(l.pause(),l.forcedHandlePause=!0),r=!0,h(e);for(var t=["mouseup","touchend"],n=0,i=t.length;n<i;n++)l.getElement(l.container).addEventListener(t[n],function(e){var t=e.target;(t===l.slider||t.closest("."+l.options.classPrefix+"time-slider"))&&h(e)});l.globalBind("mouseup.dur touchend.dur",function(){I(),r=!1,l.timefloat&&(l.timefloat.style.display="none")})}},!(!d.SUPPORT_PASSIVE_EVENT||"touchstart"!==b[x])&&{passive:!0});l.slider.addEventListener("mouseenter",function(e){e.target===l.slider&&l.getDuration()!==1/0&&(l.getElement(l.container).addEventListener("mousemove",function(e){var t=e.target;(t===l.slider||t.closest("."+l.options.classPrefix+"time-slider"))&&h(e)}),!l.timefloat||d.IS_IOS||d.IS_ANDROID||(l.timefloat.style.display="block"),l.hovered&&!d.IS_IOS&&!d.IS_ANDROID&&l.options.useSmoothHover&&(0,m.removeClass)(l.hovered,"no-hover"))}),l.slider.addEventListener("mouseleave",function(){l.getDuration()!==1/0&&(r||(l.timefloat&&(l.timefloat.style.display="none"),l.hovered&&l.options.useSmoothHover&&(0,m.addClass)(l.hovered,"no-hover")))}),l.broadcastCallback=function(n){var i=t.querySelector("."+l.options.classPrefix+"broadcast");if(l.options.forceLive||l.getDuration()===1/0){if(!i||l.options.forceLive){var o=a.default.createElement("span");o.className=l.options.classPrefix+"broadcast",o.innerText=c.default.t("mejs.live-broadcast"),l.slider.style.display="none",l.rail.appendChild(o)}}else i&&(l.slider.style.display="",i.remove()),e.setProgressRail(n),l.forcedHandlePause||e.setCurrentRail(n),v()},i.addEventListener("progress",l.broadcastCallback),i.addEventListener("timeupdate",l.broadcastCallback),i.addEventListener("play",function(){l.buffer.style.display="none"}),i.addEventListener("playing",function(){l.buffer.style.display="none"}),i.addEventListener("seeking",function(){l.buffer.style.display=""}),i.addEventListener("seeked",function(){l.buffer.style.display="none"}),i.addEventListener("pause",function(){l.buffer.style.display="none"}),i.addEventListener("waiting",function(){l.buffer.style.display=""}),i.addEventListener("loadeddata",function(){l.buffer.style.display=""}),i.addEventListener("canplay",function(){l.buffer.style.display="none"}),i.addEventListener("error",function(){l.buffer.style.display="none"}),l.getElement(l.container).addEventListener("controlsresize",function(t){l.getDuration()!==1/0&&(e.setProgressRail(t),l.forcedHandlePause||e.setCurrentRail(t))})},cleanprogress:function(e,t,n,i){i.removeEventListener("progress",e.broadcastCallback),i.removeEventListener("timeupdate",e.broadcastCallback),e.rail&&e.rail.remove()},setProgressRail:function(e){var t=this,n=void 0!==e?e.detail.target||e.target:t.media,i=null;n&&n.buffered&&n.buffered.length>0&&n.buffered.end&&t.getDuration()?i=n.buffered.end(n.buffered.length-1)/t.getDuration():n&&void 0!==n.bytesTotal&&n.bytesTotal>0&&void 0!==n.bufferedBytes?i=n.bufferedBytes/n.bytesTotal:e&&e.lengthComputable&&0!==e.total&&(i=e.loaded/e.total),null!==i&&(i=Math.min(1,Math.max(0,i)),t.loaded&&t.setTransformStyle(t.loaded,"scaleX("+i+")"))},setCurrentRailHandle:function(e){var t=this;t.setCurrentRailMain(t,e)},setCurrentRail:function(){var e=this;e.setCurrentRailMain(e)},setCurrentRailMain:function(e,t){if(void 0!==e.getCurrentTime()&&e.getDuration()){var n=void 0===t?e.getCurrentTime():t;if(e.total&&e.handle){var i=parseFloat(getComputedStyle(e.total).width),o=Math.round(i*n/e.getDuration()),a=o-Math.round(e.handle.offsetWidth/2);if(a=a<0?0:a,e.setTransformStyle(e.current,"scaleX("+o/i+")"),e.setTransformStyle(e.handle,"translateX("+a+"px)"),e.options.useSmoothHover&&!(0,m.hasClass)(e.hovered,"no-hover")){var r=parseInt(e.hovered.getAttribute("pos"),10);r=isNaN(r)?0:r;var s=r/i-a/i;e.hovered.style.left=a+"px",e.setTransformStyle(e.hovered,"scaleX("+s+")"),s>=0?(0,m.removeClass)(e.hovered,"negative"):(0,m.addClass)(e.hovered,"negative")}}}}})},{16:16,2:2,25:25,26:26,30:30,5:5}],12:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(16),s=i(r),l=e(30),c=e(26);Object.assign(r.config,{duration:0,timeAndDurationSeparator:"<span> | </span>"}),Object.assign(s.default.prototype,{buildcurrent:function(e,t,n,i){var o=this,r=a.default.createElement("div");r.className=o.options.classPrefix+"time",r.setAttribute("role","timer"),r.setAttribute("aria-live","off"),r.innerHTML='<span class="'+o.options.classPrefix+'currenttime">'+(0,l.secondsToTimeCode)(0,e.options)+"</span>",o.$currentTime=$(r).find("."+o.options.classPrefix+"currenttime"),o.$currentTimeInput=o.$currentTime.find("input"),o.addControlElement(r,"current"),e.updateCurrent(),o.updateTimeCallback=function(){o.controlsAreVisible&&e.updateCurrent()},i.addEventListener("timeupdate",o.updateTimeCallback)},cleancurrent:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateTimeCallback)},buildduration:function(e,t,n,i){var o=this;if(t.lastChild.querySelector("."+o.options.classPrefix+"currenttime"))t.querySelector("."+o.options.classPrefix+"time").innerHTML+=o.options.timeAndDurationSeparator+'<span class="'+o.options.classPrefix+'duration">'+(0,l.secondsToTimeCode)(o.options.duration,o.options)+"</span>";else{t.querySelector("."+o.options.classPrefix+"currenttime")&&(0,c.addClass)(t.querySelector("."+o.options.classPrefix+"currenttime").parentNode,o.options.classPrefix+"currenttime-container");var r=a.default.createElement("div");r.className=o.options.classPrefix+"time "+o.options.classPrefix+"duration-container",r.innerHTML='<span class="'+o.options.classPrefix+'duration">'+(0,l.secondsToTimeCode)(o.options.duration,o.options)+"</span>",o.addControlElement(r,"duration")}o.updateDurationCallback=function(){o.controlsAreVisible&&e.updateDuration()},i.addEventListener("timeupdate",o.updateDurationCallback)},cleanduration:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateDurationCallback)},updateCurrent:function(){var e=this,t=e.getCurrentTime();isNaN(t)&&(t=0);var n=(0,l.secondsToTimeCode)(t,e.options);n.length>5?(0,c.addClass)(e.getElement(e.container),e.options.classPrefix+"long-video"):(0,c.removeClass)(e.getElement(e.container),e.options.classPrefix+"long-video"),e.getElement(e.controls).querySelector("."+e.options.classPrefix+"currenttime")&&(e.getElement(e.controls).querySelector("."+e.options.classPrefix+"currenttime").innerText=n)},updateDuration:function(){var e=this,t=e.getDuration();(isNaN(t)||t===1/0||t<0)&&(e.media.duration=e.options.duration=t=0),e.options.duration>0&&(t=e.options.duration);var n=(0,l.secondsToTimeCode)(t,e.options);n.length>5?(0,c.addClass)(e.getElement(e.container),e.options.classPrefix+"long-video"):(0,c.removeClass)(e.getElement(e.container),e.options.classPrefix+"long-video"),e.getElement(e.controls).querySelector("."+e.options.classPrefix+"duration")&&t>0&&(e.getElement(e.controls).querySelector("."+e.options.classPrefix+"duration").innerHTML=n)}})},{16:16,2:2,26:26,30:30}],13:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(7),s=i(r),l=e(5),c=i(l),d=e(16),u=i(d),m=e(30),p=e(27),f=e(26);Object.assign(d.config,{startLanguage:"",tracksText:null,chaptersText:null,tracksAriaLive:!1,hideCaptionsButtonWhenEmpty:!0,toggleCaptionsButtonWhenOnlyOne:!1,slidesSelector:""}),Object.assign(u.default.prototype,{hasChapters:!1,buildtracks:function(e,t,n,i){if(this.findTracks(),e.tracks.length||e.trackFiles&&0!==!e.trackFiles.length){var o=this,r=o.options.tracksAriaLive?' role="log" aria-live="assertive" aria-atomic="false"':"",s=(0,p.isString)(o.options.tracksText)?o.options.tracksText:c.default.t("mejs.captions-subtitles"),l=(0,p.isString)(o.options.chaptersText)?o.options.chaptersText:c.default.t("mejs.captions-chapters"),d=null===e.trackFiles?e.tracks.length:e.trackFiles.length;if(o.domNode.textTracks)for(var u=o.domNode.textTracks.length-1;u>=0;u--)o.domNode.textTracks[u].mode="hidden";o.cleartracks(e),e.captions=a.default.createElement("div"),e.captions.className=o.options.classPrefix+"captions-layer "+o.options.classPrefix+"layer",e.captions.innerHTML='<div class="'+o.options.classPrefix+"captions-position "+o.options.classPrefix+'captions-position-hover"'+r+'><span class="'+o.options.classPrefix+'captions-text"></span></div>',e.captions.style.display="none",n.insertBefore(e.captions,n.firstChild),e.captionsText=e.captions.querySelector("."+o.options.classPrefix+"captions-text"),e.captionsButton=a.default.createElement("div"),e.captionsButton.className=o.options.classPrefix+"button "+o.options.classPrefix+"captions-button",e.captionsButton.innerHTML='<button type="button" aria-controls="'+o.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button><div class="'+o.options.classPrefix+"captions-selector "+o.options.classPrefix+'offscreen"><ul class="'+o.options.classPrefix+'captions-selector-list"><li class="'+o.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+o.options.classPrefix+'captions-selector-input" name="'+e.id+'_captions" id="'+e.id+'_captions_none" value="none" checked disabled><label class="'+o.options.classPrefix+"captions-selector-label "+o.options.classPrefix+'captions-selected" for="'+e.id+'_captions_none">'+c.default.t("mejs.none")+"</label></li></ul></div>",o.addControlElement(e.captionsButton,"tracks"),e.captionsButton.querySelector("."+o.options.classPrefix+"captions-selector-input").disabled=!1,e.chaptersButton=a.default.createElement("div"),e.chaptersButton.className=o.options.classPrefix+"button "+o.options.classPrefix+"chapters-button",e.chaptersButton.innerHTML='<button type="button" aria-controls="'+o.id+'" title="'+l+'" aria-label="'+l+'" tabindex="0"></button><div class="'+o.options.classPrefix+"chapters-selector "+o.options.classPrefix+'offscreen"><ul class="'+o.options.classPrefix+'chapters-selector-list"></ul></div>';for(var m=0,g=0;g<d;g++){var h=e.tracks[g].kind;e.tracks[g].src.trim()&&("subtitles"===h||"captions"===h?m++:"chapters"!==h||t.querySelector("."+o.options.classPrefix+"chapter-selector")||e.captionsButton.parentNode.insertBefore(e.chaptersButton,e.captionsButton))}e.trackToLoad=-1,e.selectedTrack=null,e.isLoadingTrack=!1;for(var v=0;v<d;v++){var y=e.tracks[v].kind;!e.tracks[v].src.trim()||"subtitles"!==y&&"captions"!==y||e.addTrackButton(e.tracks[v].trackId,e.tracks[v].srclang,e.tracks[v].label)}e.loadNextTrack()
;var I=["mouseenter","focusin"],b=["mouseleave","focusout"];if(o.options.toggleCaptionsButtonWhenOnlyOne&&1===m)e.captionsButton.addEventListener("click",function(t){var n="none";null===e.selectedTrack&&(n=e.tracks[0].trackId);var i=t.keyCode||t.which;e.setTrack(n,void 0!==i)});else{for(var x=e.captionsButton.querySelectorAll("."+o.options.classPrefix+"captions-selector-label"),k=e.captionsButton.querySelectorAll("input[type=radio]"),j=0,C=I.length;j<C;j++)e.captionsButton.addEventListener(I[j],function(){(0,f.removeClass)(this.querySelector("."+o.options.classPrefix+"captions-selector"),o.options.classPrefix+"offscreen")});for(var S=0,M=b.length;S<M;S++)e.captionsButton.addEventListener(b[S],function(){(0,f.addClass)(this.querySelector("."+o.options.classPrefix+"captions-selector"),o.options.classPrefix+"offscreen")});for(var w=0,T=k.length;w<T;w++)k[w].addEventListener("click",function(t){var n=t.keyCode||t.which;e.setTrack(this.value,void 0!==n)});for(var N=0,E=x.length;N<E;N++)x[N].addEventListener("click",function(e){var t=(0,f.siblings)(this,function(e){return"INPUT"===e.tagName})[0],n=(0,p.createEvent)("click",t);t.dispatchEvent(n),e.preventDefault()});e.captionsButton.addEventListener("keydown",function(e){e.stopPropagation()})}for(var _=0,P=I.length;_<P;_++)e.chaptersButton.addEventListener(I[_],function(){this.querySelector("."+o.options.classPrefix+"chapters-selector-list").children.length&&(0,f.removeClass)(this.querySelector("."+o.options.classPrefix+"chapters-selector"),o.options.classPrefix+"offscreen")});for(var A=0,D=b.length;A<D;A++)e.chaptersButton.addEventListener(b[A],function(){(0,f.addClass)(this.querySelector("."+o.options.classPrefix+"chapters-selector"),o.options.classPrefix+"offscreen")});e.chaptersButton.addEventListener("keydown",function(e){e.stopPropagation()}),e.options.alwaysShowControls?(0,f.addClass)(e.getElement(e.container).querySelector("."+o.options.classPrefix+"captions-position"),o.options.classPrefix+"captions-position-hover"):(e.getElement(e.container).addEventListener("controlsshown",function(){(0,f.addClass)(e.getElement(e.container).querySelector("."+o.options.classPrefix+"captions-position"),o.options.classPrefix+"captions-position-hover")}),e.getElement(e.container).addEventListener("controlshidden",function(){i.paused||(0,f.removeClass)(e.getElement(e.container).querySelector("."+o.options.classPrefix+"captions-position"),o.options.classPrefix+"captions-position-hover")})),i.addEventListener("timeupdate",function(){e.displayCaptions()}),""!==e.options.slidesSelector&&(e.slidesContainer=a.default.querySelectorAll(e.options.slidesSelector),i.addEventListener("timeupdate",function(){e.displaySlides()}))}},cleartracks:function(e){e&&(e.captions&&e.captions.remove(),e.chapters&&e.chapters.remove(),e.captionsText&&e.captionsText.remove(),e.captionsButton&&e.captionsButton.remove(),e.chaptersButton&&e.chaptersButton.remove())},rebuildtracks:function(){var e=this;e.findTracks(),e.buildtracks(e,e.getElement(e.controls),e.getElement(e.layers),e.media)},findTracks:function(){var e=this,t=null===e.trackFiles?e.node.querySelectorAll("track"):e.trackFiles,n=t.length;e.tracks=[];for(var i=0;i<n;i++){var o=t[i],a=o.getAttribute("srclang").toLowerCase()||"",r=e.id+"_track_"+i+"_"+o.getAttribute("kind")+"_"+a;e.tracks.push({trackId:r,srclang:a,src:o.getAttribute("src"),kind:o.getAttribute("kind"),label:o.getAttribute("label")||"",entries:[],isLoaded:!1})}},setTrack:function(e,t){for(var n=this,i=n.captionsButton.querySelectorAll('input[type="radio"]'),o=n.captionsButton.querySelectorAll("."+n.options.classPrefix+"captions-selected"),a=n.captionsButton.querySelector('input[value="'+e+'"]'),r=0,s=i.length;r<s;r++)i[r].checked=!1;for(var l=0,c=o.length;l<c;l++)(0,f.removeClass)(o[l],n.options.classPrefix+"captions-selected");a.checked=!0;for(var d=(0,f.siblings)(a,function(e){return(0,f.hasClass)(e,n.options.classPrefix+"captions-selector-label")}),u=0,m=d.length;u<m;u++)(0,f.addClass)(d[u],n.options.classPrefix+"captions-selected");if("none"===e)n.selectedTrack=null,(0,f.removeClass)(n.captionsButton,n.options.classPrefix+"captions-enabled");else for(var g=0,h=n.tracks.length;g<h;g++){var v=n.tracks[g];if(v.trackId===e){null===n.selectedTrack&&(0,f.addClass)(n.captionsButton,n.options.classPrefix+"captions-enabled"),n.selectedTrack=v,n.captions.setAttribute("lang",n.selectedTrack.srclang),n.displayCaptions();break}}var y=(0,p.createEvent)("captionschange",n.media);y.detail.caption=n.selectedTrack,n.media.dispatchEvent(y),t||setTimeout(function(){n.getElement(n.container).focus()},500)},loadNextTrack:function(){var e=this;e.trackToLoad++,e.trackToLoad<e.tracks.length?(e.isLoadingTrack=!0,e.loadTrack(e.trackToLoad)):(e.isLoadingTrack=!1,e.checkForTracks())},loadTrack:function(e){var t=this,n=t.tracks[e];void 0===n||void 0===n.src&&""===n.src||(0,f.ajax)(n.src,"text",function(e){n.entries="string"==typeof e&&/<tt\s+xml/gi.exec(e)?s.default.TrackFormatParser.dfxp.parse(e):s.default.TrackFormatParser.webvtt.parse(e),n.isLoaded=!0,t.enableTrackButton(n),t.loadNextTrack(),"slides"===n.kind?t.setupSlides(n):"chapters"!==n.kind||t.hasChapters||(t.drawChapters(n),t.hasChapters=!0)},function(){t.removeTrackButton(n.trackId),t.loadNextTrack()})},enableTrackButton:function(e){var t=this,n=e.srclang,i=a.default.getElementById(""+e.trackId);if(i){var o=e.label;""===o&&(o=c.default.t(s.default.language.codes[n])||n),i.disabled=!1;for(var r=(0,f.siblings)(i,function(e){return(0,f.hasClass)(e,t.options.classPrefix+"captions-selector-label")}),l=0,d=r.length;l<d;l++)r[l].innerHTML=o;if(t.options.startLanguage===n){i.checked=!0;var u=(0,p.createEvent)("click",i);i.dispatchEvent(u)}}},removeTrackButton:function(e){var t=a.default.getElementById(""+e);if(t){var n=t.closest("li");n&&n.remove()}},addTrackButton:function(e,t,n){var i=this;""===n&&(n=c.default.t(s.default.language.codes[t])||t),i.captionsButton.querySelector("ul").innerHTML+='<li class="'+i.options.classPrefix+'captions-selector-list-item"><input type="radio" class="'+i.options.classPrefix+'captions-selector-input" name="'+i.id+'_captions" id="'+e+'" value="'+e+'" disabled><label class="'+i.options.classPrefix+'captions-selector-label"for="'+e+'">'+n+" (loading)</label></li>"},checkForTracks:function(){var e=this,t=!1;if(e.options.hideCaptionsButtonWhenEmpty){for(var n=0,i=e.tracks.length;n<i;n++){var o=e.tracks[n].kind;if(("subtitles"===o||"captions"===o)&&e.tracks[n].isLoaded){t=!0;break}}e.captionsButton.style.display=t?"":"none",e.setControlsSize()}},displayCaptions:function(){if(void 0!==this.tracks){var e=this,t=e.selectedTrack;if(null!==t&&t.isLoaded){var n=e.searchTrackPosition(t.entries,e.media.currentTime);if(n>-1)return e.captionsText.innerHTML=function(e){var t=a.default.createElement("div");t.innerHTML=e;for(var n=t.getElementsByTagName("script"),i=n.length;i--;)n[i].remove();for(var o=t.getElementsByTagName("*"),r=0,s=o.length;r<s;r++)for(var l=o[r].attributes,c=Array.prototype.slice.call(l),d=0,u=c.length;d<u;d++)c[d].name.startsWith("on")||c[d].value.startsWith("javascript")?o[r].remove():"style"===c[d].name&&o[r].removeAttribute(c[d].name);return t.innerHTML}(t.entries[n].text),e.captionsText.className=e.options.classPrefix+"captions-text "+(t.entries[n].identifier||""),e.captions.style.display="",void(e.captions.style.height="0px");e.captions.style.display="none"}else e.captions.style.display="none"}},setupSlides:function(e){var t=this;t.slides=e,t.slides.entries.imgs=[t.slides.entries.length],t.showSlide(0)},showSlide:function(e){var t=this,n=this;if(void 0!==n.tracks&&void 0!==n.slidesContainer){var i=n.slides.entries[e].text,o=n.slides.entries[e].imgs;if(void 0===o||void 0===o.fadeIn){var r=a.default.createElement("img");r.src=i,r.addEventListener("load",function(){var e=t,i=(0,f.siblings)(e,function(e){return i(e)});e.style.display="none",n.slidesContainer.innerHTML+=e.innerHTML,(0,f.fadeIn)(n.slidesContainer.querySelector(r));for(var o=0,a=i.length;o<a;o++)(0,f.fadeOut)(i[o],400)}),n.slides.entries[e].imgs=o=r}else if(!(0,f.visible)(o)){var s=(0,f.siblings)(self,function(e){return s(e)});(0,f.fadeIn)(n.slidesContainer.querySelector(o));for(var l=0,c=s.length;l<c;l++)(0,f.fadeOut)(s[l])}}},displaySlides:function(){var e=this;if(void 0!==this.slides){var t=e.slides,n=e.searchTrackPosition(t.entries,e.media.currentTime);n>-1&&e.showSlide(n)}},drawChapters:function(e){var t=this,n=e.entries.length;if(n){t.chaptersButton.querySelector("ul").innerHTML="";for(var i=0;i<n;i++)t.chaptersButton.querySelector("ul").innerHTML+='<li class="'+t.options.classPrefix+'chapters-selector-list-item" role="menuitemcheckbox" aria-live="polite" aria-disabled="false" aria-checked="false"><input type="radio" class="'+t.options.classPrefix+'captions-selector-input" name="'+t.id+'_chapters" id="'+t.id+"_chapters_"+i+'" value="'+e.entries[i].start+'" disabled><label class="'+t.options.classPrefix+'chapters-selector-label"for="'+t.id+"_chapters_"+i+'">'+e.entries[i].text+"</label></li>";for(var o=t.chaptersButton.querySelectorAll('input[type="radio"]'),a=t.chaptersButton.querySelectorAll("."+t.options.classPrefix+"chapters-selector-label"),r=0,s=o.length;r<s;r++)o[r].disabled=!1,o[r].checked=!1,o[r].addEventListener("click",function(e){var n=this,i=t.chaptersButton.querySelectorAll("li"),o=(0,f.siblings)(n,function(e){return(0,f.hasClass)(e,t.options.classPrefix+"chapters-selector-label")})[0];n.checked=!0,n.parentNode.setAttribute("aria-checked",!0),(0,f.addClass)(o,t.options.classPrefix+"chapters-selected"),(0,f.removeClass)(t.chaptersButton.querySelector("."+t.options.classPrefix+"chapters-selected"),t.options.classPrefix+"chapters-selected");for(var a=0,r=i.length;a<r;a++)i[a].setAttribute("aria-checked",!1);void 0===(e.keyCode||e.which)&&setTimeout(function(){t.getElement(t.container).focus()},500),t.media.setCurrentTime(parseFloat(n.value)),t.media.paused&&t.media.play()});for(var l=0,c=a.length;l<c;l++)a[l].addEventListener("click",function(e){var t=(0,f.siblings)(this,function(e){return"INPUT"===e.tagName})[0],n=(0,p.createEvent)("click",t);t.dispatchEvent(n),e.preventDefault()})}},searchTrackPosition:function(e,t){for(var n=0,i=e.length-1,o=void 0,a=void 0,r=void 0;n<=i;){if(o=n+i>>1,a=e[o].start,r=e[o].stop,t>=a&&t<r)return o;a<t?n=o+1:a>t&&(i=o-1)}return-1}}),s.default.language={codes:{af:"mejs.afrikaans",sq:"mejs.albanian",ar:"mejs.arabic",be:"mejs.belarusian",bg:"mejs.bulgarian",ca:"mejs.catalan",zh:"mejs.chinese","zh-cn":"mejs.chinese-simplified","zh-tw":"mejs.chines-traditional",hr:"mejs.croatian",cs:"mejs.czech",da:"mejs.danish",nl:"mejs.dutch",en:"mejs.english",et:"mejs.estonian",fl:"mejs.filipino",fi:"mejs.finnish",fr:"mejs.french",gl:"mejs.galician",de:"mejs.german",el:"mejs.greek",ht:"mejs.haitian-creole",iw:"mejs.hebrew",hi:"mejs.hindi",hu:"mejs.hungarian",is:"mejs.icelandic",id:"mejs.indonesian",ga:"mejs.irish",it:"mejs.italian",ja:"mejs.japanese",ko:"mejs.korean",lv:"mejs.latvian",lt:"mejs.lithuanian",mk:"mejs.macedonian",ms:"mejs.malay",mt:"mejs.maltese",no:"mejs.norwegian",fa:"mejs.persian",pl:"mejs.polish",pt:"mejs.portuguese",ro:"mejs.romanian",ru:"mejs.russian",sr:"mejs.serbian",sk:"mejs.slovak",sl:"mejs.slovenian",es:"mejs.spanish",sw:"mejs.swahili",sv:"mejs.swedish",tl:"mejs.tagalog",th:"mejs.thai",tr:"mejs.turkish",uk:"mejs.ukrainian",vi:"mejs.vietnamese",cy:"mejs.welsh",yi:"mejs.yiddish"}},s.default.TrackFormatParser={webvtt:{pattern:/^((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{1,3})?) --\> ((?:[0-9]{1,2}:)?[0-9]{2}:[0-9]{2}([,.][0-9]{3})?)(.*)$/,parse:function(e){for(var t=e.split(/\r?\n/),n=[],i=void 0,o=void 0,a=void 0,r=0,s=t.length;r<s;r++){if((i=this.pattern.exec(t[r]))&&r<t.length){for(r-1>=0&&""!==t[r-1]&&(a=t[r-1]),r++,o=t[r],r++;""!==t[r]&&r<t.length;)o=o+"\n"+t[r],r++;o=o.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),n.push({identifier:a,start:0===(0,m.convertSMPTEtoSeconds)(i[1])?.2:(0,m.convertSMPTEtoSeconds)(i[1]),stop:(0,m.convertSMPTEtoSeconds)(i[3]),text:o,settings:i[5]})}a=""}return n}},dfxp:{parse:function(e){e=$(e).filter("tt");var t=e.firstChild,n=t.querySelectorAll("p"),i=e.getElementById(""+t.attr("style")),o=[],a=void 0;if(i.length){i.removeAttribute("id");var r=i.attributes;if(r.length){a={};for(var s=0,l=r.length;s<l;s++)a[r[s].name.split(":")[1]]=r[s].value}}for(var c=0,d=n.length;c<d;c++){var u=void 0,p={start:null,stop:null,style:null,text:null};if(n.eq(c).attr("begin")&&(p.start=(0,m.convertSMPTEtoSeconds)(n.eq(c).attr("begin"))),!p.start&&n.eq(c-1).attr("end")&&(p.start=(0,m.convertSMPTEtoSeconds)(n.eq(c-1).attr("end"))),n.eq(c).attr("end")&&(p.stop=(0,m.convertSMPTEtoSeconds)(n.eq(c).attr("end"))),!p.stop&&n.eq(c+1).attr("begin")&&(p.stop=(0,m.convertSMPTEtoSeconds)(n.eq(c+1).attr("begin"))),a){u="";for(var f in a)u+=f+":"+a[f]+";"}u&&(p.style=u),0===p.start&&(p.start=.2),p.text=n.eq(c).innerHTML.trim().replace(/(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi,"<a href='$1' target='_blank'>$1</a>"),o.push(p)}return o}}}},{16:16,2:2,26:26,27:27,30:30,5:5,7:7}],14:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(16),s=i(r),l=e(5),c=i(l),d=e(25),u=e(27),m=e(26);Object.assign(r.config,{muteText:null,unmuteText:null,allyVolumeControlText:null,hideVolumeOnTouchDevices:!0,audioVolume:"horizontal",videoVolume:"vertical",startVolume:.8}),Object.assign(s.default.prototype,{buildvolume:function(e,t,n,i){if(!d.IS_ANDROID&&!d.IS_IOS||!this.options.hideVolumeOnTouchDevices){var o=this,r=o.isVideo?o.options.videoVolume:o.options.audioVolume,s=(0,u.isString)(o.options.muteText)?o.options.muteText:c.default.t("mejs.mute"),l=(0,u.isString)(o.options.unmuteText)?o.options.unmuteText:c.default.t("mejs.unmute"),p=(0,u.isString)(o.options.allyVolumeControlText)?o.options.allyVolumeControlText:c.default.t("mejs.volume-help-text"),f=a.default.createElement("div");if(f.className=o.options.classPrefix+"button "+o.options.classPrefix+"volume-button "+o.options.classPrefix+"mute",f.innerHTML="horizontal"===r?'<button type="button" aria-controls="'+o.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button>':'<button type="button" aria-controls="'+o.id+'" title="'+s+'" aria-label="'+s+'" tabindex="0"></button><a href="javascript:void(0);" class="'+o.options.classPrefix+'volume-slider" aria-label="'+c.default.t("mejs.volume-slider")+'" aria-valuemin="0" aria-valuemax="100" role="slider" aria-orientation="vertical"><span class="'+o.options.classPrefix+'offscreen">'+p+'</span><div class="'+o.options.classPrefix+'volume-total"><div class="'+o.options.classPrefix+'volume-current"></div><div class="'+o.options.classPrefix+'volume-handle"></div></div></a>',o.addControlElement(f,"volume"),"horizontal"===r){var g=a.default.createElement("a");g.className=o.options.classPrefix+"horizontal-volume-slider",g.href="javascript:void(0);",g.setAttribute("aria-label",c.default.t("mejs.volume-slider")),g.setAttribute("aria-valuemin",0),g.setAttribute("aria-valuemax",100),g.setAttribute("role","slider"),g.innerHTML+='<span class="'+o.options.classPrefix+'offscreen">'+p+'</span><div class="'+o.options.classPrefix+'horizontal-volume-total"><div class="'+o.options.classPrefix+'horizontal-volume-current"></div><div class="'+o.options.classPrefix+'horizontal-volume-handle"></div></div>',f.parentNode.insertBefore(g,f.nextSibling)}var h=!1,v=!1,y=!1,I="vertical"===r?o.getElement(o.container).querySelector("."+o.options.classPrefix+"volume-slider"):o.getElement(o.container).querySelector("."+o.options.classPrefix+"horizontal-volume-slider"),b="vertical"===r?o.getElement(o.container).querySelector("."+o.options.classPrefix+"volume-total"):o.getElement(o.container).querySelector("."+o.options.classPrefix+"horizontal-volume-total"),x="vertical"===r?o.getElement(o.container).querySelector("."+o.options.classPrefix+"volume-current"):o.getElement(o.container).querySelector("."+o.options.classPrefix+"horizontal-volume-current"),k="vertical"===r?o.getElement(o.container).querySelector("."+o.options.classPrefix+"volume-handle"):o.getElement(o.container).querySelector("."+o.options.classPrefix+"horizontal-volume-handle"),j=function(e){if(null!==e&&!isNaN(e)&&void 0!==e){if(e=Math.max(0,e),0===(e=Math.min(e,1))){(0,m.removeClass)(f,o.options.classPrefix+"mute"),(0,m.addClass)(f,o.options.classPrefix+"unmute");var t=f.firstElementChild;t.setAttribute("title",l),t.setAttribute("aria-label",l)}else{(0,m.removeClass)(f,o.options.classPrefix+"unmute"),(0,m.addClass)(f,o.options.classPrefix+"mute");var n=f.firstElementChild;n.setAttribute("title",s),n.setAttribute("aria-label",s)}var i=100*e+"%",a=getComputedStyle(k);"vertical"===r?(x.style.bottom=0,x.style.height=i,k.style.bottom=i,k.style.marginBottom=-parseFloat(a.height)/2+"px"):(x.style.left=0,x.style.width=i,k.style.left=i,k.style.marginLeft=-parseFloat(a.width)/2+"px")}},C=function(e){var t=(0,m.offset)(b),n=getComputedStyle(b);y=!0;var i=null;if("vertical"===r){var a=parseFloat(n.height);if(i=(a-(e.pageY-t.top))/a,0===t.top||0===t.left)return}else{var s=parseFloat(n.width);i=(e.pageX-t.left)/s}i=Math.max(0,i),i=Math.min(i,1),j(i),o.setMuted(0===i),o.setVolume(i),e.preventDefault(),e.stopPropagation()},S=function(){o.muted?(j(0),(0,m.removeClass)(f,o.options.classPrefix+"mute"),(0,m.addClass)(f,o.options.classPrefix+"unmute")):(j(i.volume),(0,m.removeClass)(f,o.options.classPrefix+"unmute"),(0,m.addClass)(f,o.options.classPrefix+"mute"))};e.getElement(e.container).addEventListener("keydown",function(e){!!e.target.closest("."+o.options.classPrefix+"container")||"vertical"!==r||(I.style.display="none")}),f.addEventListener("mouseenter",function(e){e.target===f&&(I.style.display="block",v=!0,e.preventDefault(),e.stopPropagation())}),f.addEventListener("focusin",function(){I.style.display="block",v=!0}),f.addEventListener("focusout",function(e){e.relatedTarget&&(!e.relatedTarget||e.relatedTarget.matches("."+o.options.classPrefix+"volume-slider"))||"vertical"!==r||(I.style.display="none")}),f.addEventListener("mouseleave",function(){v=!1,h||"vertical"!==r||(I.style.display="none")}),f.addEventListener("focusout",function(){v=!1}),f.querySelector("button").addEventListener("click",function(){i.setMuted(!i.muted);var e=(0,u.createEvent)("volumechange",i);i.dispatchEvent(e),S()}),I.addEventListener("dragstart",function(){return!1}),I.addEventListener("mouseover",function(){v=!0}),I.addEventListener("focusin",function(){I.style.display="block",v=!0}),I.addEventListener("focusout",function(){v=!1,h||"vertical"!==r||(I.style.display="none")}),I.addEventListener("mousedown",function(e){C(e),o.globalBind("mousemove.vol",function(e){var t=e.target;h&&(t===I||t.closest("vertical"===r?"."+o.options.classPrefix+"volume-slider":"."+o.options.classPrefix+"horizontal-volume-slider"))&&C(e)}),o.globalBind("mouseup.vol",function(){h=!1,v||"vertical"!==r||(I.style.display="none")}),h=!0,e.preventDefault(),e.stopPropagation()});var M=!1;i.addEventListener("rendererready",function(){y||setTimeout(function(){M=!0,(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0),i.setVolume(e.options.startVolume),o.setControlsSize()},250)}),i.addEventListener("loadedmetadata",function(){setTimeout(function(){y||M||((0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0),i.setVolume(e.options.startVolume),o.setControlsSize()),M=!1},250)}),(0===e.options.startVolume||i.originalNode.muted)&&(i.setMuted(!0),e.options.startVolume=0,S()),o.getElement(o.container).addEventListener("controlsresize",function(){S()})}}})},{16:16,2:2,25:25,26:26,27:27,5:5}],15:[function(e,t,n){Object.defineProperty(n,"__esModule",{value:!0});n.EN={"mejs.plural-form":1,"mejs.download-file":"Download File","mejs.install-flash":"You are using a browser that does not have Flash player enabled or installed. Please turn on your Flash player plugin or download the latest version from https://get.adobe.com/flashplayer/","mejs.fullscreen":"Fullscreen","mejs.play":"Play","mejs.pause":"Pause","mejs.time-slider":"Time Slider","mejs.time-help-text":"Use Left/Right Arrow keys to advance one second, Up/Down arrows to advance ten seconds.","mejs.live-broadcast":"Live Broadcast","mejs.volume-help-text":"Use Up/Down Arrow keys to increase or decrease volume.","mejs.unmute":"Unmute","mejs.mute":"Mute","mejs.volume-slider":"Volume Slider","mejs.video-player":"Video Player","mejs.audio-player":"Audio Player","mejs.captions-subtitles":"Captions/Subtitles","mejs.captions-chapters":"Chapters","mejs.none":"None","mejs.afrikaans":"Afrikaans","mejs.albanian":"Albanian","mejs.arabic":"Arabic","mejs.belarusian":"Belarusian","mejs.bulgarian":"Bulgarian","mejs.catalan":"Catalan","mejs.chinese":"Chinese","mejs.chinese-simplified":"Chinese (Simplified)","mejs.chinese-traditional":"Chinese (Traditional)","mejs.croatian":"Croatian","mejs.czech":"Czech","mejs.danish":"Danish","mejs.dutch":"Dutch","mejs.english":"English","mejs.estonian":"Estonian","mejs.filipino":"Filipino","mejs.finnish":"Finnish","mejs.french":"French","mejs.galician":"Galician","mejs.german":"German","mejs.greek":"Greek","mejs.haitian-creole":"Haitian Creole","mejs.hebrew":"Hebrew","mejs.hindi":"Hindi","mejs.hungarian":"Hungarian","mejs.icelandic":"Icelandic","mejs.indonesian":"Indonesian","mejs.irish":"Irish","mejs.italian":"Italian","mejs.japanese":"Japanese","mejs.korean":"Korean","mejs.latvian":"Latvian","mejs.lithuanian":"Lithuanian","mejs.macedonian":"Macedonian","mejs.malay":"Malay","mejs.maltese":"Maltese","mejs.norwegian":"Norwegian","mejs.persian":"Persian","mejs.polish":"Polish","mejs.portuguese":"Portuguese","mejs.romanian":"Romanian","mejs.russian":"Russian","mejs.serbian":"Serbian","mejs.slovak":"Slovak","mejs.slovenian":"Slovenian","mejs.spanish":"Spanish","mejs.swahili":"Swahili","mejs.swedish":"Swedish","mejs.tagalog":"Tagalog","mejs.thai":"Thai","mejs.turkish":"Turkish","mejs.ukrainian":"Ukrainian","mejs.vietnamese":"Vietnamese","mejs.welsh":"Welsh","mejs.yiddish":"Yiddish"}},{}],16:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0}),n.config=void 0;var r="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},s=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),l=e(3),c=o(l),d=e(2),u=o(d),m=e(7),p=o(m),f=e(6),g=o(f),h=e(17),v=o(h),y=e(5),I=o(y),b=e(25),x=e(27),k=e(30),j=e(28),C=e(26),S=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(C);p.default.mepIndex=0,p.default.players={};var M=n.config={poster:"",showPosterWhenEnded:!1,showPosterWhenPaused:!1,defaultVideoWidth:480,defaultVideoHeight:270,videoWidth:-1,videoHeight:-1,defaultAudioWidth:400,defaultAudioHeight:40,defaultSeekBackwardInterval:function(e){return e.options.backframeInterval/e.options.framesPerSecond},defaultSeekForwardInterval:function(e){return e.options.prevframeInterval/e.options.framesPerSecond},setDimensions:!0,audioWidth:-1,audioHeight:-1,loop:!1,autoRewind:!0,enableAutosize:!0,timeFormat:"",alwaysShowHours:!1,showTimecodeFrameCount:!1,framesPerSecond:25,alwaysShowControls:!1,hideVideoControlsOnLoad:!1,hideVideoControlsOnPause:!1,clickToPlayPause:!0,controlsTimeoutDefault:1500,controlsTimeoutMouseEnter:2500,controlsTimeoutMouseLeave:1e3,iPadUseNativeControls:!1,iPhoneUseNativeControls:!1,AndroidUseNativeControls:!1,features:["playpause","current","progress","duration","tracks","volume","fullscreen"],useDefaultControls:!1,isVideo:!0,stretching:"auto",classPrefix:"mejs__",enableKeyboard:!0,pauseOtherPlayers:!0,secondsDecimalLength:0,customError:null,keyActions:[{keys:[32,179],action:function(e){b.IS_FIREFOX||(e.paused||e.ended?e.play():e.pause())}}]};p.default.MepDefaults=M;var w=function(){function e(t,n){a(this,e);var i=this,o="string"==typeof t?u.default.getElementById(t):t;if(!(i instanceof e))return new e(o,n);if(i.node=i.media=o,i.node){if(i.media.player)return i.media.player;if(i.hasFocus=!1,i.controlsAreVisible=!0,i.controlsEnabled=!0,i.controlsTimer=null,i.currentMediaTime=0,i.proxy=null,void 0===n){var r=i.node.getAttribute("data-mejsoptions");n=r?JSON.parse(r):{}}return i.options=Object.assign({},M,n),i.options.loop&&!i.media.getAttribute("loop")?(i.media.loop=!0,i.node.loop=!0):i.media.loop&&(i.options.loop=!0),i.options.timeFormat||(i.options.timeFormat="mm:ss",i.options.alwaysShowHours&&(i.options.timeFormat="hh:mm:ss"),i.options.showTimecodeFrameCount&&(i.options.timeFormat+=":ff")),(0,k.calculateTimeFormat)(0,i.options,i.options.framesPerSecond||25),i.id="mep_"+p.default.mepIndex++,p.default.players[i.id]=i,i.init(),i}}return s(e,[{key:"getElement",value:function(e){return e}},{key:"init",value:function(){var e=this,t=Object.assign({},e.options,{success:function(t,n){e._meReady(t,n)},error:function(t){e._handleError(t)}}),n=e.node.tagName.toLowerCase();if(e.isDynamic="audio"!==n&&"video"!==n&&"iframe"!==n,e.isVideo=e.isDynamic?e.options.isVideo:"audio"!==n&&e.options.isVideo,e.mediaFiles=null,e.trackFiles=null,b.IS_IPAD&&e.options.iPadUseNativeControls||b.IS_IPHONE&&e.options.iPhoneUseNativeControls)e.node.setAttribute("controls",!0),b.IS_IPAD&&e.node.getAttribute("autoplay")&&e.play();else if(!e.isVideo&&(e.isVideo||!e.options.features.length&&!e.options.useDefaultControls)||b.IS_ANDROID&&e.options.AndroidUseNativeControls)e.isVideo||e.options.features.length||e.options.useDefaultControls||(e.node.style.display="none");else{e.node.removeAttribute("controls");var i=e.isVideo?I.default.t("mejs.video-player"):I.default.t("mejs.audio-player"),o=u.default.createElement("span");if(o.className=e.options.classPrefix+"offscreen",o.innerText=i,e.media.parentNode.insertBefore(o,e.media),e.container=u.default.createElement("div"),e.getElement(e.container).id=e.id,e.getElement(e.container).className=e.options.classPrefix+"container "+e.options.classPrefix+"container-keyboard-inactive "+e.media.className,e.getElement(e.container).tabIndex=0,e.getElement(e.container).setAttribute("role","application"),e.getElement(e.container).setAttribute("aria-label",i),e.getElement(e.container).innerHTML='<div class="'+e.options.classPrefix+'inner"><div class="'+e.options.classPrefix+'mediaelement"></div><div class="'+e.options.classPrefix+'layers"></div><div class="'+e.options.classPrefix+'controls"></div></div>',e.getElement(e.container).addEventListener("focus",function(t){if(!e.controlsAreVisible&&!e.hasFocus&&e.controlsEnabled){e.showControls(!0);var n=(0,x.isNodeAfter)(t.relatedTarget,e.getElement(e.container))?"."+e.options.classPrefix+"controls ."+e.options.classPrefix+"button:last-child > button":"."+e.options.classPrefix+"playpause-button > button";e.getElement(e.container).querySelector(n).focus()}}),e.node.parentNode.insertBefore(e.getElement(e.container),e.node),e.options.features.length||e.options.useDefaultControls||(e.getElement(e.container).style.background="transparent",e.getElement(e.container).querySelector("."+e.options.classPrefix+"controls").style.display="none"),e.isVideo&&"fill"===e.options.stretching&&!S.hasClass(e.getElement(e.container).parentNode,e.options.classPrefix+"fill-container")){e.outerContainer=e.media.parentNode;var a=u.default.createElement("div");a.className=e.options.classPrefix+"fill-container",e.getElement(e.container).parentNode.insertBefore(a,e.getElement(e.container)),a.appendChild(e.getElement(e.container))}if(b.IS_ANDROID&&S.addClass(e.getElement(e.container),e.options.classPrefix+"android"),b.IS_IOS&&S.addClass(e.getElement(e.container),e.options.classPrefix+"ios"),b.IS_IPAD&&S.addClass(e.getElement(e.container),e.options.classPrefix+"ipad"),b.IS_IPHONE&&S.addClass(e.getElement(e.container),e.options.classPrefix+"iphone"),S.addClass(e.getElement(e.container),e.isVideo?e.options.classPrefix+"video":e.options.classPrefix+"audio"),b.IS_SAFARI&&!b.IS_IOS){S.addClass(e.getElement(e.container),e.options.classPrefix+"hide-cues");for(var r=e.node.cloneNode(),s=e.node.children,l=[],c=[],d=0,m=s.length;d<m;d++){var f=s[d];!function(){switch(f.tagName.toLowerCase()){case"source":var e={};Array.prototype.slice.call(f.attributes).forEach(function(t){e[t.name]=t.value}),e.type=(0,j.formatType)(e.src,e.type),l.push(e);break;case"track":f.mode="hidden",c.push(f);break;default:r.appendChild(f)}}()}e.node.remove(),e.node=e.media=r,l.length&&(e.mediaFiles=l),c.length&&(e.trackFiles=c)}e.getElement(e.container).querySelector("."+e.options.classPrefix+"mediaelement").appendChild(e.node),e.media.player=e,e.controls=e.getElement(e.container).querySelector("."+e.options.classPrefix+"controls"),e.layers=e.getElement(e.container).querySelector("."+e.options.classPrefix+"layers");var h=e.isVideo?"video":"audio",v=h.substring(0,1).toUpperCase()+h.substring(1);e.options[h+"Width"]>0||e.options[h+"Width"].toString().indexOf("%")>-1?e.width=e.options[h+"Width"]:""!==e.node.style.width&&null!==e.node.style.width?e.width=e.node.style.width:e.node.getAttribute("width")?e.width=e.node.getAttribute("width"):e.width=e.options["default"+v+"Width"],e.options[h+"Height"]>0||e.options[h+"Height"].toString().indexOf("%")>-1?e.height=e.options[h+"Height"]:""!==e.node.style.height&&null!==e.node.style.height?e.height=e.node.style.height:e.node.getAttribute("height")?e.height=e.node.getAttribute("height"):e.height=e.options["default"+v+"Height"],e.initialAspectRatio=e.height>=e.width?e.width/e.height:e.height/e.width,e.setPlayerSize(e.width,e.height),t.pluginWidth=e.width,t.pluginHeight=e.height}if(p.default.MepDefaults=t,new g.default(e.media,t,e.mediaFiles),void 0!==e.getElement(e.container)&&e.options.features.length&&e.controlsAreVisible&&!e.options.hideVideoControlsOnLoad){var y=(0,x.createEvent)("controlsshown",e.getElement(e.container));e.getElement(e.container).dispatchEvent(y)}}},{key:"showControls",value:function(e){var t=this;if(e=void 0===e||e,!t.controlsAreVisible&&t.isVideo){if(e)!function(){S.fadeIn(t.getElement(t.controls),200,function(){S.removeClass(t.getElement(t.controls),t.options.classPrefix+"offscreen");var e=(0,x.createEvent)("controlsshown",t.getElement(t.container));t.getElement(t.container).dispatchEvent(e)});for(var e=t.getElement(t.container).querySelectorAll("."+t.options.classPrefix+"control"),n=0,i=e.length;n<i;n++)!function(n,i){S.fadeIn(e[n],200,function(){S.removeClass(e[n],t.options.classPrefix+"offscreen")})}(n)}();else{S.removeClass(t.getElement(t.controls),t.options.classPrefix+"offscreen"),t.getElement(t.controls).style.display="",t.getElement(t.controls).style.opacity=1;for(var n=t.getElement(t.container).querySelectorAll("."+t.options.classPrefix+"control"),i=0,o=n.length;i<o;i++)S.removeClass(n[i],t.options.classPrefix+"offscreen"),n[i].style.display="";var a=(0,x.createEvent)("controlsshown",t.getElement(t.container));t.getElement(t.container).dispatchEvent(a)}t.controlsAreVisible=!0,t.setControlsSize()}}},{key:"hideControls",value:function(e,t){var n=this;if(e=void 0===e||e,!0===t||!(!n.controlsAreVisible||n.options.alwaysShowControls||n.paused&&4===n.readyState&&(!n.options.hideVideoControlsOnLoad&&n.currentTime<=0||!n.options.hideVideoControlsOnPause&&n.currentTime>0)||n.isVideo&&!n.options.hideVideoControlsOnLoad&&!n.readyState||n.ended)){if(e)!function(){S.fadeOut(n.getElement(n.controls),200,function(){S.addClass(n.getElement(n.controls),n.options.classPrefix+"offscreen"),n.getElement(n.controls).style.display=""
;var e=(0,x.createEvent)("controlshidden",n.getElement(n.container));n.getElement(n.container).dispatchEvent(e)});for(var e=n.getElement(n.container).querySelectorAll("."+n.options.classPrefix+"control"),t=0,i=e.length;t<i;t++)!function(t,i){S.fadeOut(e[t],200,function(){S.addClass(e[t],n.options.classPrefix+"offscreen"),e[t].style.display=""})}(t)}();else{S.addClass(n.getElement(n.controls),n.options.classPrefix+"offscreen"),n.getElement(n.controls).style.display="",n.getElement(n.controls).style.opacity=0;for(var i=n.getElement(n.container).querySelectorAll("."+n.options.classPrefix+"control"),o=0,a=i.length;o<a;o++)S.addClass(i[o],n.options.classPrefix+"offscreen"),i[o].style.display="";var r=(0,x.createEvent)("controlshidden",n.getElement(n.container));n.getElement(n.container).dispatchEvent(r)}n.controlsAreVisible=!1}}},{key:"startControlsTimer",value:function(e){var t=this;e=void 0!==e?e:t.options.controlsTimeoutDefault,t.killControlsTimer("start"),t.controlsTimer=setTimeout(function(){t.hideControls(),t.killControlsTimer("hide")},e)}},{key:"killControlsTimer",value:function(){var e=this;null!==e.controlsTimer&&(clearTimeout(e.controlsTimer),delete e.controlsTimer,e.controlsTimer=null)}},{key:"disableControls",value:function(){var e=this;e.killControlsTimer(),e.controlsEnabled=!1,e.hideControls(!1,!0)}},{key:"enableControls",value:function(){var e=this;e.controlsEnabled=!0,e.showControls(!1)}},{key:"_setDefaultPlayer",value:function(){var e=this;e.proxy&&e.proxy.pause(),e.proxy=new v.default(e),e.media.addEventListener("loadedmetadata",function(){e.getCurrentTime()>0&&e.currentMediaTime>0&&(e.setCurrentTime(e.currentMediaTime),b.IS_IOS||b.IS_ANDROID||e.play())})}},{key:"_meReady",value:function(e,t){var n=this,i=t.getAttribute("autoplay"),o=!(void 0===i||null===i||"false"===i),a=null!==e.rendererName&&/(native|html5)/i.test(n.media.rendererName);if(n.getElement(n.controls)&&n.enableControls(),n.getElement(n.container)&&n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-play")&&(n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-play").style.display=""),!n.created){if(n.created=!0,n.media=e,n.domNode=t,!(b.IS_ANDROID&&n.options.AndroidUseNativeControls||b.IS_IPAD&&n.options.iPadUseNativeControls||b.IS_IPHONE&&n.options.iPhoneUseNativeControls)){var r=function(){n.playInLockRange();var t=TimeCodeConvert.second2Frame(n.media.currentTime,n.options.framesPerSecond),i=n.getCurrTcTimecode(t);n.$currentTimeInput.val(i),e.dispatchEvent(new CustomEvent("accurateTimeUpdate",{detail:{media:n.media,currentTime:n.media.currentTime,currentTimeCode:i}}))},s=function(){var e=n;e.playInterval&&l(),e.playInterval=setInterval(r,Math.floor(1e3/(2*n.options.framesPerSecond)))},l=function(){var e=n;clearInterval(e.playInterval),e.playInterval=null};if(!n.isVideo&&!n.options.features.length&&!n.options.useDefaultControls)return o&&a&&n.play(),void(n.options.success&&("string"==typeof n.options.success?c.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n)));if(n.featurePosition={},n._setDefaultPlayer(),n.buildposter(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.buildkeyboard(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.buildoverlays(n,n.getElement(n.controls),n.getElement(n.layers),n.media),n.options.useDefaultControls){var d=["playpause","current","progress","duration","tracks","volume","fullscreen"];n.options.features=d.concat(n.options.features.filter(function(e){return-1===d.indexOf(e)}))}n.buildfeatures(n,n.getElement(n.controls),n.getElement(n.layers),n.media);var m=(0,x.createEvent)("controlsready",n.getElement(n.container));n.getElement(n.container).dispatchEvent(m),n.setPlayerSize(n.width,n.height),n.setControlsSize(),n.isVideo&&(n.clickToPlayPauseCallback=function(){if(n.options.clickToPlayPause){var e=n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");n.paused&&t?n.pause():n.paused?n.play():n.pause(),e.setAttribute("aria-pressed",!t),n.getElement(n.container).focus()}},n.createIframeLayer(),n.media.addEventListener("click",n.clickToPlayPauseCallback),!b.IS_ANDROID&&!b.IS_IOS||n.options.alwaysShowControls?(n.getElement(n.container).addEventListener("mouseenter",function(){n.controlsEnabled&&(n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter)))}),n.getElement(n.container).addEventListener("mousemove",function(){n.controlsEnabled&&(n.controlsAreVisible||n.showControls(),n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseEnter))}),n.getElement(n.container).addEventListener("mouseleave",function(){n.controlsEnabled&&(n.paused||n.options.alwaysShowControls||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))})):n.node.addEventListener("touchstart",function(){n.controlsAreVisible?n.hideControls(!1):n.controlsEnabled&&n.showControls(!1)},!!b.SUPPORT_PASSIVE_EVENT&&{passive:!0}),n.options.hideVideoControlsOnLoad&&n.hideControls(!1),n.options.enableAutosize&&n.media.addEventListener("loadedmetadata",function(e){var t=void 0!==e?e.detail.target||e.target:n.media;n.options.videoHeight<=0&&!n.domNode.getAttribute("height")&&!n.domNode.style.height&&null!==t&&!isNaN(t.videoHeight)&&(n.setPlayerSize(t.videoWidth,t.videoHeight),n.setControlsSize(),n.media.setSize(t.videoWidth,t.videoHeight))})),n.media.addEventListener("play",function(){n.startPlayInLockRange(),n.hasFocus=!0;for(var e in p.default.players)if(p.default.players.hasOwnProperty(e)){var t=p.default.players[e];t.id===n.id||!n.options.pauseOtherPlayers||t.paused||t.ended||(t.pause(),t.hasFocus=!1)}b.IS_ANDROID||b.IS_IOS||n.options.alwaysShowControls||!n.isVideo||n.hideControls(),s()}),n.media.addEventListener("pause",function(){l()},!1),n.media.addEventListener("ended",function(){if(n.options.autoRewind)try{n.isLockPlayRange||n.setCurrentTime(0),setTimeout(function(){var e=n.getElement(n.container).querySelector("."+n.options.classPrefix+"overlay-loading");e&&e.parentNode&&(e.parentNode.style.display="none")},20)}catch(e){}"function"==typeof n.media.renderer.stop?n.media.renderer.stop():n.pause(),n.setProgressRail&&n.setProgressRail(),n.setCurrentRail&&n.setCurrentRail(),n.playInLockRangeForEnd(),n.options.loop?n.play():!n.options.alwaysShowControls&&n.controlsEnabled&&n.showControls()}),n.media.addEventListener("loadedmetadata",function(){(0,k.calculateTimeFormat)(n.getDuration(),n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.isFullScreen||(n.setPlayerSize(n.width,n.height),n.setControlsSize())});var f=null;n.media.addEventListener("timeupdate",function(){isNaN(n.getDuration())||f===n.getDuration()||(f=n.getDuration(),(0,k.calculateTimeFormat)(f,n.options,n.options.framesPerSecond||25),n.updateDuration&&n.updateDuration(),n.updateCurrent&&n.updateCurrent(),n.setControlsSize())}),n.getElement(n.container).addEventListener("click",function(e){S.addClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive")}),n.getElement(n.container).addEventListener("focusin",function(e){S.removeClass(e.currentTarget,n.options.classPrefix+"container-keyboard-inactive"),!n.isVideo||b.IS_ANDROID||b.IS_IOS||!n.controlsEnabled||n.options.alwaysShowControls||(n.killControlsTimer("enter"),n.showControls(),n.startControlsTimer(n.options.controlsTimeoutMouseEnter))}),n.getElement(n.container).addEventListener("focusout",function(e){setTimeout(function(){e.relatedTarget&&n.keyboardAction&&!e.relatedTarget.closest("."+n.options.classPrefix+"container")&&(n.keyboardAction=!1,!n.isVideo||n.options.alwaysShowControls||n.paused||n.startControlsTimer(n.options.controlsTimeoutMouseLeave))},0)}),setTimeout(function(){n.setPlayerSize(n.width,n.height),n.setControlsSize()},0),n.globalResizeCallback=function(){n.isFullScreen||b.HAS_TRUE_NATIVE_FULLSCREEN&&u.default.webkitIsFullScreen||n.setPlayerSize(n.width,n.height),n.setControlsSize()},n.globalBind("resize",n.globalResizeCallback)}o&&a&&n.play(),n.options.success&&("string"==typeof n.options.success?c.default[n.options.success](n.media,n.domNode,n):n.options.success(n.media,n.domNode,n))}}},{key:"_handleError",value:function(e,t,n){var i=this,o=i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-play");o&&(o.style.display="none"),i.options.error&&i.options.error(e,t,n),i.getElement(i.container).querySelector("."+i.options.classPrefix+"cannotplay")&&i.getElement(i.container).querySelector("."+i.options.classPrefix+"cannotplay").remove();var a=u.default.createElement("div");a.className=i.options.classPrefix+"cannotplay",a.style.width="100%",a.style.height="100%";var r="function"==typeof i.options.customError?i.options.customError(i.media,i.media.originalNode):i.options.customError,s="";if(!r){var l=i.media.originalNode.getAttribute("poster");if(l&&(s='<img src="'+l+'" alt="'+p.default.i18n.t("mejs.download-file")+'">'),e.message&&(r="<p>"+e.message+"</p>"),e.urls)for(var c=0,d=e.urls.length;c<d;c++){var m=e.urls[c];r+='<a href="'+m.src+'" data-type="'+m.type+'"><span>'+p.default.i18n.t("mejs.download-file")+": "+m.src+"</span></a>"}}r&&i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error")&&(a.innerHTML=r,i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error").innerHTML=""+s+a.outerHTML,i.getElement(i.layers).querySelector("."+i.options.classPrefix+"overlay-error").parentNode.style.display="block"),i.controlsEnabled&&i.disableControls()}},{key:"setPlayerSize",value:function(e,t){var n=this;if(!n.options.setDimensions)return!1;switch(void 0!==e&&(n.width=e),void 0!==t&&(n.height=t),n.options.stretching){case"fill":n.isVideo?n.setFillMode():n.setDimensions(n.width,n.height);break;case"responsive":n.setResponsiveMode();break;case"none":n.setDimensions(n.width,n.height);break;default:!0===n.hasFluidMode()?n.setResponsiveMode():n.setDimensions(n.width,n.height)}}},{key:"hasFluidMode",value:function(){var e=this;return-1!==e.height.toString().indexOf("%")||e.node&&e.node.style.maxWidth&&"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width||e.node&&e.node.currentStyle&&"100%"===e.node.currentStyle.maxWidth}},{key:"setResponsiveMode",value:function(){var e=this,t=function(){for(var t=void 0,n=e.getElement(e.container);n;){try{if(b.IS_FIREFOX&&"html"===n.tagName.toLowerCase()&&c.default.self!==c.default.top&&null!==c.default.frameElement)return c.default.frameElement;t=n.parentElement}catch(e){t=n.parentElement}if(t&&S.visible(t))return t;n=t}return null}(),n=t?getComputedStyle(t,null):getComputedStyle(u.default.body,null),i=function(){return e.isVideo?e.node.videoWidth&&e.node.videoWidth>0?e.node.videoWidth:e.node.getAttribute("width")?e.node.getAttribute("width"):e.options.defaultVideoWidth:e.options.defaultAudioWidth}(),o=function(){return e.isVideo?e.node.videoHeight&&e.node.videoHeight>0?e.node.videoHeight:e.node.getAttribute("height")?e.node.getAttribute("height"):e.options.defaultVideoHeight:e.options.defaultAudioHeight}(),a=function(){var t=1;return e.isVideo?(t=e.node.videoWidth&&e.node.videoWidth>0&&e.node.videoHeight&&e.node.videoHeight>0?e.height>=e.width?e.node.videoWidth/e.node.videoHeight:e.node.videoHeight/e.node.videoWidth:e.initialAspectRatio,(isNaN(t)||t<.01||t>100)&&(t=1),t):t}(),r=parseFloat(n.height),s=void 0,l=parseFloat(n.width);if(s=e.isVideo?"100%"===e.height?parseFloat(l*o/i,10):e.height>=e.width?parseFloat(l/a,10):parseFloat(l*a,10):o,isNaN(s)&&(s=r),e.getElement(e.container).parentNode&&e.getElement(e.container).parentNode.length>0&&"body"===e.getElement(e.container).parentNode.tagName.toLowerCase()&&(l=c.default.innerWidth||u.default.documentElement.clientWidth||u.default.body.clientWidth,s=c.default.innerHeight||u.default.documentElement.clientHeight||u.default.body.clientHeight),s&&l){e.getElement(e.container).style.width=l+"px",e.getElement(e.container).style.height=s+"px",e.node.style.width="100%",e.node.style.height="100%",e.isVideo&&e.media.setSize&&e.media.setSize(l,s);for(var d=e.getElement(e.layers).children,m=0,p=d.length;m<p;m++)d[m].style.width="100%",d[m].style.height="100%"}}},{key:"setFillMode",value:function(){var e=this,t=c.default.self!==c.default.top&&null!==c.default.frameElement,n=function(){for(var t=void 0,n=e.getElement(e.container);n;){try{if(b.IS_FIREFOX&&"html"===n.tagName.toLowerCase()&&c.default.self!==c.default.top&&null!==c.default.frameElement)return c.default.frameElement;t=n.parentElement}catch(e){t=n.parentElement}if(t&&S.visible(t))return t;n=t}return null}(),i=n?getComputedStyle(n,null):getComputedStyle(u.default.body,null);"none"!==e.node.style.height&&e.node.style.height!==e.height&&(e.node.style.height="auto"),"none"!==e.node.style.maxWidth&&e.node.style.maxWidth!==e.width&&(e.node.style.maxWidth="none"),"none"!==e.node.style.maxHeight&&e.node.style.maxHeight!==e.height&&(e.node.style.maxHeight="none"),e.node.currentStyle&&("100%"===e.node.currentStyle.height&&(e.node.currentStyle.height="auto"),"100%"===e.node.currentStyle.maxWidth&&(e.node.currentStyle.maxWidth="none"),"100%"===e.node.currentStyle.maxHeight&&(e.node.currentStyle.maxHeight="none")),t||parseFloat(i.width)||(n.style.width=e.media.offsetWidth+"px"),t||parseFloat(i.height)||(n.style.height=e.media.offsetHeight+"px"),i=getComputedStyle(n);var o=parseFloat(i.width),a=parseFloat(i.height);e.setDimensions("100%","100%");var r=e.getElement(e.container).querySelector("."+e.options.classPrefix+"poster>img");r&&(r.style.display="");for(var s=e.getElement(e.container).querySelectorAll("object, embed, iframe, video"),l=e.height,d=e.width,m=o,p=l*o/d,f=d*a/l,g=a,h=f>o==!1,v=h?Math.floor(m):Math.floor(f),y=h?Math.floor(p):Math.floor(g),I=h?o+"px":v+"px",x=h?y+"px":a+"px",k=0,j=s.length;k<j;k++)s[k].style.height=x,s[k].style.width=I,e.media.setSize&&e.media.setSize(I,x),s[k].style.marginLeft=Math.floor((o-v)/2)+"px",s[k].style.marginTop=0}},{key:"setDimensions",value:function(e,t){var n=this;e=(0,x.isString)(e)&&e.indexOf("%")>-1?e:parseFloat(e)+"px",t=(0,x.isString)(t)&&t.indexOf("%")>-1?t:parseFloat(t)+"px",n.getElement(n.container).style.width=e,n.getElement(n.container).style.height=t;for(var i=n.getElement(n.layers).children,o=0,a=i.length;o<a;o++)i[o].style.width=e,i[o].style.height=t}},{key:"setControlsSize",value:function(){var e=this;if(S.visible(e.getElement(e.container)))if(e.rail&&S.visible(e.rail)){for(var t=e.total?getComputedStyle(e.total,null):null,n=t?parseFloat(t.marginLeft)+parseFloat(t.marginRight):0,i=getComputedStyle(e.rail),o=parseFloat(i.marginLeft)+parseFloat(i.marginRight),a=0,r=S.siblings(e.rail,function(t){return t!==e.rail}),s=r.length,l=0;l<s;l++)a+=r[l].offsetWidth;a+=n+(0===n?2*o:o)+1,e.getElement(e.container).style.minWidth=a+"px";var c=(0,x.createEvent)("controlsresize",e.getElement(e.container));e.getElement(e.container).dispatchEvent(c)}else{for(var d=e.getElement(e.controls).children,u=0,m=0,p=d.length;m<p;m++)u+=d[m].offsetWidth;e.getElement(e.container).style.minWidth=u+"px"}}},{key:"addControlElement",value:function(e,t){var n=this;if(void 0!==n.featurePosition[t]){var i=n.getElement(n.controls).children[n.featurePosition[t]-1];i.parentNode.insertBefore(e,i.nextSibling)}else{n.getElement(n.controls).appendChild(e);for(var o=n.getElement(n.controls).children,a=0,r=o.length;a<r;a++)if(e===o[a]){n.featurePosition[t]=a;break}}}},{key:"createIframeLayer",value:function(){var e=this;if(e.isVideo&&null!==e.media.rendererName&&e.media.rendererName.indexOf("iframe")>-1&&!u.default.getElementById(e.media.id+"-iframe-overlay")){var t=u.default.createElement("div"),n=u.default.getElementById(e.media.id+"_"+e.media.rendererName);t.id=e.media.id+"-iframe-overlay",t.className=e.options.classPrefix+"iframe-overlay",t.addEventListener("click",function(t){e.options.clickToPlayPause&&(e.paused?e.play():e.pause(),t.preventDefault(),t.stopPropagation())}),n.parentNode.insertBefore(t,n)}}},{key:"resetSize",value:function(){var e=this;setTimeout(function(){e.setPlayerSize(e.width,e.height),e.setControlsSize()},50)}},{key:"setPoster",value:function(e){var t=this;if(t.getElement(t.container)){var n=t.getElement(t.container).querySelector("."+t.options.classPrefix+"poster");n||(n=u.default.createElement("div"),n.className=t.options.classPrefix+"poster "+t.options.classPrefix+"layer",t.getElement(t.layers).appendChild(n));var i=n.querySelector("img");!i&&e&&(i=u.default.createElement("img"),i.className=t.options.classPrefix+"poster-img",i.width="100%",i.height="100%",n.style.display="",n.appendChild(i)),e?(i.setAttribute("src",e),n.style.backgroundImage='url("'+e+'")',n.style.display=""):i?(n.style.backgroundImage="none",n.style.display="none",i.remove()):n.style.display="none"}else(b.IS_IPAD&&t.options.iPadUseNativeControls||b.IS_IPHONE&&t.options.iPhoneUseNativeControls||b.IS_ANDROID&&t.options.AndroidUseNativeControls)&&(t.media.originalNode.poster=e)}},{key:"changeSkin",value:function(e){var t=this;t.getElement(t.container).className=t.options.classPrefix+"container "+e,t.setPlayerSize(t.width,t.height),t.setControlsSize()}},{key:"globalBind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:u.default;if(e=(0,x.splitEvents)(e,n.id),e.d)for(var o=e.d.split(" "),a=0,r=o.length;a<r;a++)o[a].split(".").reduce(function(e,n){return i.addEventListener(n,t,!1),n},"");if(e.w)for(var s=e.w.split(" "),l=0,d=s.length;l<d;l++)s[l].split(".").reduce(function(e,n){return c.default.addEventListener(n,t,!1),n},"")}},{key:"globalUnbind",value:function(e,t){var n=this,i=n.node?n.node.ownerDocument:u.default;if(e=(0,x.splitEvents)(e,n.id),e.d)for(var o=e.d.split(" "),a=0,r=o.length;a<r;a++)o[a].split(".").reduce(function(e,n){return i.removeEventListener(n,t,!1),n},"");if(e.w)for(var s=e.w.split(" "),l=0,d=s.length;l<d;l++)s[l].split(".").reduce(function(e,n){return c.default.removeEventListener(n,t,!1),n},"")}},{key:"buildfeatures",value:function(e,t,n,i){for(var o=this,a=0,r=o.options.features.length;a<r;a++){var s=o.options.features[a];if(o["build"+s])try{o["build"+s](e,t,n,i)}catch(e){console.error("error building "+s,e)}}}},{key:"buildposter",value:function(e,t,n,i){var o=this,a=u.default.createElement("div");a.className=o.options.classPrefix+"poster "+o.options.classPrefix+"layer",n.appendChild(a);var r=i.originalNode.getAttribute("poster");""!==e.options.poster&&(r&&b.IS_IOS&&i.originalNode.removeAttribute("poster"),r=e.options.poster),r?o.setPoster(r):null!==o.media.renderer&&"function"==typeof o.media.renderer.getPosterUrl?o.setPoster(o.media.renderer.getPosterUrl()):a.style.display="none",i.addEventListener("play",function(){a.style.display="none"}),i.addEventListener("playing",function(){a.style.display=""}),e.options.showPosterWhenEnded&&e.options.autoRewind&&i.addEventListener("ended",function(){a.style.display=""}),i.addEventListener("error",function(){a.style.display="none"}),e.options.showPosterWhenPaused&&i.addEventListener("pause",function(){e.ended||(a.style.display="")})}},{key:"buildoverlays",value:function(e,t,n,i){if(e.isVideo){var o=this,a=u.default.createElement("div"),r=u.default.createElement("div"),s=u.default.createElement("div");if(a.style.display="none",a.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",a.innerHTML='<div class="'+o.options.classPrefix+'overlay-loading"><span class="'+o.options.classPrefix+'overlay-loading-bg-img"></span></div>',n.appendChild(a),r.style.display="none",r.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer",r.innerHTML='<div class="'+o.options.classPrefix+'overlay-error"></div>',n.appendChild(r),s.className=o.options.classPrefix+"overlay "+o.options.classPrefix+"layer "+o.options.classPrefix+"overlay-play",s.innerHTML='<div class="'+o.options.classPrefix+'overlay-button" role="button" tabindex="0" aria-label="'+I.default.t("mejs.play")+'" aria-pressed="false"></div>',s.addEventListener("click",function(){if(o.options.clickToPlayPause){var e=o.getElement(o.container).querySelector("."+o.options.classPrefix+"overlay-button"),t=e.getAttribute("aria-pressed");o.paused?o.play():o.pause(),e.setAttribute("aria-pressed",!!t),o.getElement(o.container).focus()}}),s.addEventListener("keydown",function(e){var t=e.keyCode||e.which||0;if(13===t||b.IS_FIREFOX&&32===t){var n=(0,x.createEvent)("click",s);return s.dispatchEvent(n),!1}}),n.appendChild(s),window.nxt&&window.nxt.user&&window.nxt.user.current&&window.nxt.config&&window.nxt.config.theme&&"zb"===window.nxt.config.theme.name)for(var l=0;l<1920;l+=300)for(var c=80;c<1080;c+=300)!function(e,t){var i=u.default.createElement("div");i.style.left=e+"px",i.style.top=t+"px",i.className="layer-overlay-watermark";var o=u.default.createElement("div");o.className="container",i.appendChild(o);var a=nxt.user.current.loginName+" "+(new Date).getFullYear()+"-"+((new Date).getMonth()+1)+"-"+(new Date).getDate();nxt.user.current.organizations&&nxt.user.current.organizations.length>0&&nxt.user.current.organizations[0].organizationName&&(a+="<br>"+nxt.user.current.organizations[0].organizationName),o.innerHTML=a,n.appendChild(i),$(i).click(function(){$(s).click()})}(l,c);null!==o.media.rendererName&&(/(youtube|facebook)/i.test(o.media.rendererName)&&!(o.media.originalNode.getAttribute("poster")||e.options.poster||"function"==typeof o.media.renderer.getPosterUrl&&o.media.renderer.getPosterUrl())||b.IS_STOCK_ANDROID||o.media.originalNode.getAttribute("autoplay"))&&(s.style.display="none");var d=!1;i.addEventListener("play",function(){s.style.display="none",a.style.display="none",r.style.display="none",d=!1}),i.addEventListener("playing",function(){s.style.display="none",a.style.display="none",r.style.display="none",d=!1}),i.addEventListener("seeking",function(){s.style.display="none",a.style.display="",d=!1}),i.addEventListener("seeked",function(){s.style.display=o.paused&&!b.IS_STOCK_ANDROID?"":"none",a.style.display="none",d=!1}),i.addEventListener("pause",function(){a.style.display="none",b.IS_STOCK_ANDROID||d||(s.style.display=""),d=!1}),i.addEventListener("waiting",function(){a.style.display="",d=!1}),i.addEventListener("loadeddata",function(){a.style.display="",b.IS_ANDROID&&(i.canplayTimeout=setTimeout(function(){if(u.default.createEvent){var e=u.default.createEvent("HTMLEvents");return e.initEvent("canplay",!0,!0),i.dispatchEvent(e)}},300)),d=!1}),i.addEventListener("canplay",function(){a.style.display="none",clearTimeout(i.canplayTimeout),d=!1}),i.addEventListener("error",function(e){o._handleError(e,o.media,o.node),a.style.display="none",s.style.display="none",d=!0}),i.addEventListener("loadedmetadata",function(){o.controlsEnabled||o.enableControls()}),i.addEventListener("keydown",function(t){o.onkeydown(e,i,t),d=!1})}}},{key:"buildkeyboard",value:function(e,t,n,i){var o=this;o.getElement(o.container).addEventListener("keydown",function(){o.keyboardAction=!0}),o.globalKeydownCallback=function(t){var n=u.default.activeElement.closest("."+o.options.classPrefix+"container"),a=o.media.closest("."+o.options.classPrefix+"container");return o.hasFocus=!(!n||!a||n.id!==a.id),o.onkeydown(e,i,t)},o.globalClickCallback=function(e){o.hasFocus=!!e.target.closest("."+o.options.classPrefix+"container")},o.globalBind("keydown",o.globalKeydownCallback),o.globalBind("click",o.globalClickCallback)}},{key:"onkeydown",value:function(e,t,n){if(e.hasFocus&&e.options.enableKeyboard)for(var i=0,o=e.options.keyActions.length;i<o;i++)for(var a=e.options.keyActions[i],r=0,s=a.keys.length;r<s;r++)if(n.keyCode===a.keys[r])return a.action(e,t,n.keyCode,n),n.preventDefault(),void n.stopPropagation();return!0}},{key:"play",value:function(){this.proxy.play()}},{key:"pause",value:function(){this.proxy.pause()}},{key:"load",value:function(){this.proxy.load()}},{key:"setCurrentTime",value:function(e){this.proxy.setCurrentTime(e)}},{key:"getCurrentTime",value:function(){return this.proxy.currentTime}},{key:"getDuration",value:function(){return this.proxy.duration}},{key:"setVolume",value:function(e){this.proxy.volume=e}},{key:"getVolume",value:function(){return this.proxy.getVolume()}},{key:"setMuted",value:function(e){this.proxy.setMuted(e)}},{key:"setSrc",value:function(e){this.controlsEnabled||this.enableControls(),this.proxy.setSrc(e)}},{key:"getSrc",value:function(){return this.proxy.getSrc()}},{key:"canPlayType",value:function(e){return this.proxy.canPlayType(e)}},{key:"remove",value:function(){var e=this,t=e.media.rendererName,n=e.media.originalNode.src;for(var i in e.options.features){var o=e.options.features[i];if(e["clean"+o])try{e["clean"+o](e,e.getElement(e.layers),e.getElement(e.controls),e.media)}catch(e){console.error("error cleaning "+o,e)}}var a=e.node.getAttribute("width"),s=e.node.getAttribute("height");if(a?-1===a.indexOf("%")&&(a+="px"):a="auto",s?-1===s.indexOf("%")&&(s+="px"):s="auto",e.node.style.width=a,e.node.style.height=s,e.setPlayerSize(0,0),e.isDynamic?e.getElement(e.container).parentNode.insertBefore(e.node,e.getElement(e.container)):function(){e.node.setAttribute("controls",!0),e.node.setAttribute("id",e.node.getAttribute("id").replace("_"+t,"").replace("_from_mejs",""));var i=e.getElement(e.container).querySelector("."+e.options.classPrefix+"poster>img");if(i&&e.node.setAttribute("poster",i.src),delete e.node.autoplay,""!==e.media.canPlayType((0,j.getTypeFromFile)(n))&&e.node.setAttribute("src",n),~t.indexOf("iframe")){u.default.getElementById(e.media.id+"-iframe-overlay").remove()}var o=e.node.cloneNode();if(o.style.display="",e.getElement(e.container).parentNode.insertBefore(o,e.getElement(e.container)),e.node.remove(),e.mediaFiles)for(var a=0,r=e.mediaFiles.length;a<r;a++){var s=u.default.createElement("source");s.setAttribute("src",e.mediaFiles[a].src),s.setAttribute("type",e.mediaFiles[a].type),o.appendChild(s)}if(e.trackFiles)for(var l=0,c=e.trackFiles.length;l<c;l++)!function(t,n){var i=e.trackFiles[t],a=u.default.createElement("track");a.kind=i.kind,a.label=i.label,a.srclang=i.srclang,a.src=i.src,o.appendChild(a),a.addEventListener("load",function(){this.mode="showing",o.textTracks[t].mode="showing"})}(l);delete e.node,delete e.mediaFiles,delete e.trackFiles}(),"function"==typeof e.media.renderer.destroy&&e.media.renderer.destroy(),delete p.default.players[e.id],"object"===r(e.getElement(e.container))){e.getElement(e.container).parentNode.querySelector("."+e.options.classPrefix+"offscreen").remove(),e.getElement(e.container).remove()}e.globalUnbind("resize",e.globalResizeCallback),e.globalUnbind("keydown",e.globalKeydownCallback),e.globalUnbind("click",e.globalClickCallback),delete e.media.player}},{key:"paused",get:function(){return this.proxy.paused}},{key:"muted",get:function(){return this.proxy.muted},set:function(e){this.setMuted(e)}},{key:"ended",get:function(){return this.proxy.ended}},{key:"readyState",get:function(){return this.proxy.readyState}},{key:"currentTime",set:function(e){this.setCurrentTime(e)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(e){this.setVolume(e)},get:function(){return this.getVolume()}},{key:"src",set:function(e){this.setSrc(e)},get:function(){return this.getSrc()}}]),e}();c.default.MediaElementPlayer=w,p.default.MediaElementPlayer=w,n.default=w},{17:17,2:2,25:25,26:26,27:27,28:28,3:3,30:30,5:5,6:6,7:7}],17:[function(e,t,n){function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=e(3),r=function(e){return e&&e.__esModule?e:{default:e}}(a),s=function(){function e(t){return i(this,e),this.media=t.media,this.isVideo=t.isVideo,this.classPrefix=t.options.classPrefix,this.createIframeLayer=function(){return t.createIframeLayer()},this.setPoster=function(e){return t.setPoster(e)},this}return o(e,[{key:"play",value:function(){this.media.play()}},{key:"pause",value:function(){this.media.pause()}},{key:"load",value:function(){var e=this;e.isLoaded||e.media.load(),e.isLoaded=!0}},{key:"setCurrentTime",value:function(e,t){this.media.setCurrentTime(e+5e-5),!1!==t&&this.media.dispatchEvent(new CustomEvent("accurateTimeUpdate",{detail:e}))}},{key:"getCurrentTime",value:function(){return void 0!==this.media.currentTime&&null!==this.media.currentTime?parseFloat(this.media.currentTime.toFixed(3)):this.media.currentTime}},{key:"getDuration",value:function(){return this.media.getDuration()}},{key:"setVolume",value:function(e){this.media.setVolume(e)}},{key:"getVolume",value:function(){return this.media.getVolume()}},{key:"setMuted",value:function(e){this.media.setMuted(e)}},{key:"setSrc",value:function(e){var t=this,n=document.getElementById(t.media.id+"-iframe-overlay");n&&n.remove(),t.media.setSrc(e),t.createIframeLayer(),null!==t.media.renderer&&"function"==typeof t.media.renderer.getPosterUrl&&t.setPoster(t.media.renderer.getPosterUrl())}},{key:"getSrc",value:function(){return this.media.getSrc()}},{key:"canPlayType",value:function(e){return this.media.canPlayType(e)}},{key:"paused",get:function(){return this.media.paused}},{key:"muted",set:function(e){this.setMuted(e)},get:function(){return this.media.muted}},{key:"ended",get:function(){return this.media.ended}},{key:"readyState",get:function(){return this.media.readyState}},{key:"currentTime",set:function(e){this.setCurrentTime(e)},get:function(){return this.getCurrentTime()}},{key:"duration",get:function(){return this.getDuration()}},{key:"volume",set:function(e){this.setVolume(e)},get:function(){return this.getVolume()}},{key:"src",set:function(e){this.setSrc(e)},get:function(){return this.getSrc()}}]),e}();n.default=s,r.default.DefaultPlayer=s},{3:3}],18:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(3),a=i(o),r=e(7),s=i(r),l=e(16),c=i(l);"undefined"!=typeof jQuery?s.default.$=a.default.jQuery=a.default.$=jQuery:"undefined"!=typeof Zepto?s.default.$=a.default.Zepto=a.default.$=Zepto:"undefined"!=typeof ender&&(s.default.$=a.default.ender=a.default.$=ender),function(e){void 0!==e&&(e.fn.mediaelementplayer=function(t){return!1===t?this.each(function(){var t=e(this).data("mediaelementplayer");t&&t.remove(),e(this).removeData("mediaelementplayer")}):this.each(function(){e(this).data("mediaelementplayer",new c.default(this,t))}),this},e(document).ready(function(){e("."+s.default.MepDefaults.classPrefix+"player").mediaelementplayer()}))}(s.default.$)},{16:16,3:3,7:7}],19:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}var a="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},r=e(3),s=o(r),l=e(7),c=o(l),d=e(8),u=e(27),m=e(28),p=e(25),f=e(26),g={promise:null,load:function(e){return"undefined"!=typeof dashjs?g.promise=new Promise(function(e){e()}).then(function(){g._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:"https://cdn.dashjs.org/latest/dash.all.min.js",g.promise=g.promise||(0,f.loadScript)(e.options.path),g.promise.then(function(){g._createPlayer(e)})),g.promise},_createPlayer:function(e){var t=dashjs.MediaPlayer().create();return s.default["__ready__"+e.id](t),t}},h={name:"native_dash",options:{prefix:"native_dash",dash:{path:"https://cdn.dashjs.org/latest/dash.all.min.js",debug:!1,drm:{},robustnessLevel:""}},canPlayType:function(e){return p.HAS_MSE&&["application/dash+xml"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,o=e.id+"_"+t.prefix,r=i.autoplay,l=i.children,m=null,p=null;i.removeAttribute("type")
;for(var f=0,h=l.length;f<h;f++)l[f].removeAttribute("type");m=i.cloneNode(!0),t=Object.assign(t,e.options);for(var v=c.default.html5media.properties,y=c.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),I=function(t){var n=(0,u.createEvent)(t.type,e);e.dispatchEvent(n)},b=0,x=v.length;b<x;b++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);m["get"+n]=function(){return null!==p?m[e]:null},m["set"+n]=function(n){if(-1===c.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){var i="object"===(void 0===n?"undefined":a(n))&&n.src?n.src:n;if(m[e]=i,null!==p){p.reset();for(var s=0,l=y.length;s<l;s++)m.removeEventListener(y[s],I);p=g._createPlayer({options:t.dash,id:o}),n&&"object"===(void 0===n?"undefined":a(n))&&"object"===a(n.drm)&&(p.setProtectionData(n.drm),(0,u.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(i),r&&p.play()}}else m[e]=n}}(v[b]);if(s.default["__ready__"+o]=function(n){e.dashPlayer=p=n;for(var i=dashjs.MediaPlayer.events,o=0,r=y.length;o<r;o++)!function(e){if("loadedmetadata"===e){var n=p.getSettings();n.debug.logLevel=1,n.streaming.fastSwitchEnabled=!0,p.updateSettings(n),p.initialize(),p.attachView(m),p.setAutoPlay(!1),"object"!==a(t.dash.drm)||c.default.Utils.isObjectEmpty(t.dash.drm)||(p.setProtectionData(t.dash.drm),(0,u.isString)(t.dash.robustnessLevel)&&t.dash.robustnessLevel&&p.getProtectionController().setRobustnessLevel(t.dash.robustnessLevel)),p.attachSource(m.getSrc())}m.addEventListener(e,I)}(y[o]);var s=function(t){if("error"===t.type.toLowerCase())e.generateError(t.message,m.src),console.error(t);else{var n=(0,u.createEvent)(t.type,e);n.data=t,e.dispatchEvent(n)}};for(var l in i)i.hasOwnProperty(l)&&p.on(i[l],function(e){return s(e)})},n&&n.length>0)for(var k=0,j=n.length;k<j;k++)if(d.renderer.renderers[t.prefix].canPlayType(n[k].type)){m.setAttribute("src",n[k].src),void 0!==n[k].drm&&(t.dash.drm=n[k].drm);break}m.setAttribute("id",o),i.parentNode.insertBefore(m,i),i.autoplay=!1,i.style.display="none",m.setSize=function(e,t){return m.style.width=e+"px",m.style.height=t+"px",m},m.hide=function(){return m.pause(),m.style.display="none",m},m.show=function(){return m.style.display="",m},m.destroy=function(){null!==p&&p.reset()};var C=(0,u.createEvent)("rendererready",m);return e.dispatchEvent(C),e.promises.push(g.load({options:t.dash,id:o})),m}};m.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".mpd")?"application/dash+xml":null}),d.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],20:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.PluginDetector=void 0;var a="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},r=e(3),s=o(r),l=e(2),c=o(l),d=e(7),u=o(d),m=e(5),p=o(m),f=e(8),g=e(27),h=e(25),v=e(28),y=n.PluginDetector={plugins:[],hasPluginVersion:function(e,t){var n=y.plugins[e];return t[1]=t[1]||0,t[2]=t[2]||0,n[0]>t[0]||n[0]===t[0]&&n[1]>t[1]||n[0]===t[0]&&n[1]===t[1]&&n[2]>=t[2]},addPlugin:function(e,t,n,i,o){y.plugins[e]=y.detectPlugin(t,n,i,o)},detectPlugin:function(e,t,n,i){var o=[0,0,0],r=void 0,l=void 0;if(null!==h.NAV.plugins&&void 0!==h.NAV.plugins&&"object"===a(h.NAV.plugins[e])){if((r=h.NAV.plugins[e].description)&&(void 0===h.NAV.mimeTypes||!h.NAV.mimeTypes[t]||h.NAV.mimeTypes[t].enabledPlugin)){o=r.replace(e,"").replace(/^\s+/,"").replace(/\sr/gi,".").split(".");for(var c=0,d=o.length;c<d;c++)o[c]=parseInt(o[c].match(/\d+/),10)}}else if(void 0!==s.default.ActiveXObject)try{l=new ActiveXObject(n),l&&(o=i(l))}catch(e){}return o}};y.addPlugin("flash","Shockwave Flash","application/x-shockwave-flash","ShockwaveFlash.ShockwaveFlash",function(e){var t=[],n=e.GetVariable("$version");return n&&(n=n.split(" ")[1].split(","),t=[parseInt(n[0],10),parseInt(n[1],10),parseInt(n[2],10)]),t});var I={create:function(e,t,n){var i={},o=!1;i.options=t,i.id=e.id+"_"+i.options.prefix,i.mediaElement=e,i.flashState={},i.flashApi=null,i.flashApiStack=[];for(var a=u.default.html5media.properties,r=0,l=a.length;r<l;r++)!function(e){i.flashState[e]=null;var t=""+e.substring(0,1).toUpperCase()+e.substring(1);i["get"+t]=function(){if(null!==i.flashApi){if("function"==typeof i.flashApi["get_"+e]){var t=i.flashApi["get_"+e]();return"buffered"===e?{start:function(){return 0},end:function(){return t},length:1}:t}return null}return null},i["set"+t]=function(t){if("src"===e&&(t=(0,v.absolutizeUrl)(t)),null!==i.flashApi&&void 0!==i.flashApi["set_"+e])try{i.flashApi["set_"+e](t)}catch(e){}else i.flashApiStack.push({type:"set",propName:e,value:t})}}(a[r]);var d=u.default.html5media.methods;d.push("stop");for(var m=0,y=d.length;m<y;m++)!function(e){i[e]=function(){if(o)if(null!==i.flashApi){if(i.flashApi["fire_"+e])try{i.flashApi["fire_"+e]()}catch(e){}}else i.flashApiStack.push({type:"call",methodName:e})}}(d[m]);for(var I=["rendererready"],b=0,x=I.length;b<x;b++){var k=(0,g.createEvent)(I[b],i);e.dispatchEvent(k)}s.default["__ready__"+i.id]=function(){if(i.flashReady=!0,i.flashApi=c.default.getElementById("__"+i.id),i.flashApiStack.length)for(var e=0,t=i.flashApiStack.length;e<t;e++){var n=i.flashApiStack[e];if("set"===n.type){var o=n.propName,a=""+o.substring(0,1).toUpperCase()+o.substring(1);i["set"+a](n.value)}else"call"===n.type&&i[n.methodName]()}},s.default["__event__"+i.id]=function(e,t){var n=(0,g.createEvent)(e,i);if(t)try{n.data=JSON.parse(t),n.details.data=JSON.parse(t)}catch(e){n.message=t}i.mediaElement.dispatchEvent(n)},i.flashWrapper=c.default.createElement("div"),-1===["always","sameDomain"].indexOf(i.options.shimScriptAccess)&&(i.options.shimScriptAccess="sameDomain");var j=e.originalNode.autoplay,C=["uid="+i.id,"autoplay="+j,"allowScriptAccess="+i.options.shimScriptAccess,"preload="+(e.originalNode.getAttribute("preload")||"")],S=null!==e.originalNode&&"video"===e.originalNode.tagName.toLowerCase(),M=S?e.originalNode.height:1,w=S?e.originalNode.width:1;e.originalNode.getAttribute("src")&&C.push("src="+e.originalNode.getAttribute("src")),!0===i.options.enablePseudoStreaming&&(C.push("pseudostreamstart="+i.options.pseudoStreamingStartQueryParam),C.push("pseudostreamtype="+i.options.pseudoStreamingType)),i.options.streamDelimiter&&C.push("streamdelimiter="+encodeURIComponent(i.options.streamDelimiter)),i.options.proxyType&&C.push("proxytype="+i.options.proxyType),e.appendChild(i.flashWrapper),e.originalNode.style.display="none";var T=[];if(h.IS_IE||h.IS_EDGE){var N=c.default.createElement("div");i.flashWrapper.appendChild(N),T=h.IS_EDGE?['type="application/x-shockwave-flash"','data="'+i.options.pluginPath+i.options.filename+'"','id="__'+i.id+'"','width="'+w+'"','height="'+M+"'\""]:['classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"','codebase="//download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab"','id="__'+i.id+'"','width="'+w+'"','height="'+M+'"'],S||T.push('style="clip: rect(0 0 0 0); position: absolute;"'),N.outerHTML="<object "+T.join(" ")+'><param name="movie" value="'+i.options.pluginPath+i.options.filename+"?x="+new Date+'" /><param name="flashvars" value="'+C.join("&amp;")+'" /><param name="quality" value="high" /><param name="bgcolor" value="#000000" /><param name="wmode" value="transparent" /><param name="allowScriptAccess" value="'+i.options.shimScriptAccess+'" /><param name="allowFullScreen" value="true" /><div>'+p.default.t("mejs.install-flash")+"</div></object>"}else T=['id="__'+i.id+'"','name="__'+i.id+'"','play="true"','loop="false"','quality="high"','bgcolor="#000000"','wmode="transparent"','allowScriptAccess="'+i.options.shimScriptAccess+'"','allowFullScreen="true"','type="application/x-shockwave-flash"','pluginspage="//www.macromedia.com/go/getflashplayer"','src="'+i.options.pluginPath+i.options.filename+'"','flashvars="'+C.join("&")+'"'],S?(T.push('width="'+w+'"'),T.push('height="'+M+'"')):T.push('style="position: fixed; left: -9999em; top: -9999em;"'),i.flashWrapper.innerHTML="<embed "+T.join(" ")+">";if(i.flashNode=i.flashWrapper.lastChild,i.hide=function(){o=!1,S&&(i.flashNode.style.display="none")},i.show=function(){o=!0,S&&(i.flashNode.style.display="")},i.setSize=function(e,t){i.flashNode.style.width=e+"px",i.flashNode.style.height=t+"px",null!==i.flashApi&&"function"==typeof i.flashApi.fire_setSize&&i.flashApi.fire_setSize(e,t)},i.destroy=function(){i.flashNode.remove()},n&&n.length>0)for(var E=0,_=n.length;E<_;E++)if(f.renderer.renderers[t.prefix].canPlayType(n[E].type)){i.setSrc(n[E].src);break}return i}};if(y.hasPluginVersion("flash",[10,0,0])){v.typeChecks.push(function(e){var t=e.toLowerCase().lastIndexOf("."),n=e.substring(t+1,e.length),i=n.split("?")[0];return e.startsWith("rtmp")?"mp3"===i?"audio/rtmp":"video/rtmp":/\.og(a|g)/i.test(e)?"audio/ogg":"m3u8"===i?"application/x-mpegURL":"mpd"===i?"application/dash+xml":"flv"===i?"video/flv":null});var b={name:"flash_video",options:{prefix:"flash_video",filename:"mediaelement-flash-video.swf",enablePseudoStreaming:!1,pseudoStreamingStartQueryParam:"start",pseudoStreamingType:"byte",proxyType:"",streamDelimiter:""},canPlayType:function(e){return~["video/mp4","video/rtmp","audio/rtmp","rtmp/mp4","audio/mp4","video/flv","video/x-flv"].indexOf(e.toLowerCase())},create:I.create};f.renderer.add(b);var x={name:"flash_hls",options:{prefix:"flash_hls",filename:"mediaelement-flash-video-hls.swf"},canPlayType:function(e){return~["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())},create:I.create};f.renderer.add(x);var k={name:"flash_dash",options:{prefix:"flash_dash",filename:"mediaelement-flash-video-mdash.swf"},canPlayType:function(e){return~["application/dash+xml"].indexOf(e.toLowerCase())},create:I.create};f.renderer.add(k);var j={name:"flash_audio",options:{prefix:"flash_audio",filename:"mediaelement-flash-audio.swf"},canPlayType:function(e){return~["audio/mp3"].indexOf(e.toLowerCase())},create:I.create};f.renderer.add(j);var C={name:"flash_audio_ogg",options:{prefix:"flash_audio_ogg",filename:"mediaelement-flash-audio-ogg.swf"},canPlayType:function(e){return~["audio/ogg","audio/oga","audio/ogv"].indexOf(e.toLowerCase())},create:I.create};f.renderer.add(C)}},{2:2,25:25,27:27,28:28,3:3,5:5,7:7,8:8}],21:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}var a="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},r=e(3),s=o(r),l=e(7),c=o(l),d=e(8),u=e(27),m=e(25),p=e(28),f=e(26),g={promise:null,load:function(e){return console.log("当前路径",document.currentScript.src),"undefined"!=typeof flvjs?g.promise=new Promise(function(e){e()}).then(function(){g._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:window.currentScriptSrc+"/content/flv.js",g.promise=g.promise||(0,f.loadScript)(e.options.path),g.promise.then(function(){g._createPlayer(e)})),g.promise},_createPlayer:function(e){flvjs.LoggingControl.enableDebug=e.options.debug,flvjs.LoggingControl.enableVerbose=e.options.debug;var t=flvjs.createPlayer(e.options,e.configs);return s.default["__ready__"+e.id](t),t}},h={name:"native_flv",options:{prefix:"native_flv",flv:{path:window.currentScriptSrc+"/content/flv.js",cors:!0,debug:!1}},canPlayType:function(e){return m.HAS_MSE&&["video/x-flv","video/flv"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,o=e.id+"_"+t.prefix,r=null,l=null;r=i.cloneNode(!0),t=Object.assign(t,e.options);for(var m=c.default.html5media.properties,p=c.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),f=function(t){var n=(0,u.createEvent)(t.type,e);e.dispatchEvent(n)},h=0,v=m.length;h<v;h++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);r["get"+n]=function(){return null!==l?r[e]:null},r["set"+n]=function(n){if(-1===c.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(r[e]="object"===(void 0===n?"undefined":a(n))&&n.src?n.src:n,null!==l){var i={};i.type="flv",i.url=n,i.cors=t.flv.cors,i.debug=t.flv.debug,i.path=t.flv.path;var s=t.flv.configs;l.destroy();for(var d=0,u=p.length;d<u;d++)r.removeEventListener(p[d],f);l=g._createPlayer({options:i,configs:s,id:o}),l.attachMediaElement(r),l.load()}}else r[e]=n}}(m[h]);if(s.default["__ready__"+o]=function(t){e.flvPlayer=l=t;for(var n=flvjs.Events,i=0,o=p.length;i<o;i++)!function(e){"loadedmetadata"===e&&(l.unload(),l.detachMediaElement(),l.attachMediaElement(r),l.load()),r.addEventListener(e,f)}(p[i]);var a=function(t,n){if("error"===t){var i=n[0]+": "+n[1]+" "+n[2].msg;e.generateError(i,r.src)}else{var o=(0,u.createEvent)(t,e);o.data=n,e.dispatchEvent(o)}};for(var s in n)!function(e){n.hasOwnProperty(e)&&l.on(n[e],function(){for(var t=arguments.length,i=Array(t),o=0;o<t;o++)i[o]=arguments[o];return a(n[e],i)})}(s)},n&&n.length>0)for(var y=0,I=n.length;y<I;y++)if(d.renderer.renderers[t.prefix].canPlayType(n[y].type)){r.setAttribute("src",n[y].src);break}r.setAttribute("id",o),i.parentNode.insertBefore(r,i),i.autoplay=!1,i.style.display="none";var b={};b.type="flv",b.url=r.src,b.cors=t.flv.cors,b.debug=t.flv.debug,b.path=t.flv.path;var x=t.flv.configs;r.setSize=function(e,t){return r.style.width=e+"px",r.style.height=t+"px",r},r.hide=function(){return null!==l&&l.pause(),r.style.display="none",r},r.show=function(){return r.style.display="",r},r.destroy=function(){null!==l&&l.destroy()};var k=(0,u.createEvent)("rendererready",r);return e.dispatchEvent(k),e.promises.push(g.load({options:b,configs:x,id:o})),r}};p.typeChecks.push(function(e){var t=e.toLowerCase().lastIndexOf(".");return"flv"===e.substring(t+1,e.length).split("?")[0]?"video/flv":null}),d.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],22:[function(e,t,n){function o(e){return e&&e.__esModule?e:{default:e}}var a="function"==typeof Symbol&&"symbol"===i(Symbol.iterator)?function(e){return void 0===e?"undefined":i(e)}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":void 0===e?"undefined":i(e)},r=e(3),s=o(r),l=e(7),c=o(l),d=e(8),u=e(27),m=e(25),p=e(28),f=e(26),g={promise:null,load:function(e){return"undefined"!=typeof Hls?g.promise=new Promise(function(e){e()}).then(function(){g._createPlayer(e)}):(e.options.path="string"==typeof e.options.path?e.options.path:window.currentScriptSrc+"/content/hls.js",g.promise=g.promise||(0,f.loadScript)(e.options.path),g.promise.then(function(){g._createPlayer(e)})),g.promise},_createPlayer:function(e){var t=new Hls(e.options);return s.default["__ready__"+e.id](t),t}},h={name:"native_hls",options:{prefix:"native_hls",hls:{path:window.currentScriptSrc+"/content/hls.js",autoStartLoad:!1,debug:!1}},canPlayType:function(e){return m.HAS_MSE&&["application/x-mpegurl","application/vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())>-1},create:function(e,t,n){var i=e.originalNode,o=e.id+"_"+t.prefix,r=i.getAttribute("preload"),l=i.autoplay,m=null,p=null,f=0,h=n.length;p=i.cloneNode(!0),t=Object.assign(t,e.options),t.hls.autoStartLoad=r&&"none"!==r||l;for(var v=c.default.html5media.properties,y=c.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),I=function(t){var n=(0,u.createEvent)(t.type,e);e.dispatchEvent(n)},b=0,x=v.length;b<x;b++)!function(e){var n=""+e.substring(0,1).toUpperCase()+e.substring(1);p["get"+n]=function(){return null!==m?p[e]:null},p["set"+n]=function(n){if(-1===c.default.html5media.readOnlyProperties.indexOf(e))if("src"===e){if(p[e]="object"===(void 0===n?"undefined":a(n))&&n.src?n.src:n,null!==m){m.destroy();for(var i=0,r=y.length;i<r;i++)p.removeEventListener(y[i],I);m=g._createPlayer({options:t.hls,id:o}),m.loadSource(n),m.attachMedia(p)}}else p[e]=n}}(v[b]);if(s.default["__ready__"+o]=function(t){e.hlsPlayer=m=t;for(var i=Hls.Events,o=0,a=y.length;o<a;o++)!function(t){if("loadedmetadata"===t){var n=e.originalNode.src;m.detachMedia(),m.loadSource(n),m.attachMedia(p)}p.addEventListener(t,I)}(y[o]);var r=void 0,s=void 0,l=function(t,i){if("hlsError"===t){if(console.warn(i),i=i[1],i.fatal)switch(i.type){case"mediaError":var o=(new Date).getTime();if(!r||o-r>3e3)r=(new Date).getTime(),m.recoverMediaError();else if(!s||o-s>3e3)s=(new Date).getTime(),console.warn("Attempting to swap Audio Codec and recover from media error"),m.swapAudioCodec(),m.recoverMediaError();else{var a="Cannot recover, last media error recovery failed";e.generateError(a,p.src),console.error(a)}break;case"networkError":if("manifestLoadError"===i.details)if(f<h&&void 0!==n[f+1])p.setSrc(n[f++].src),p.load(),p.play();else{e.generateError("Network error",n),console.error("Network error")}else{e.generateError("Network error",n),console.error("Network error")}break;default:m.destroy()}}else{var l=(0,u.createEvent)(t,e);l.data=i,e.dispatchEvent(l)}};for(var c in i)!function(e){i.hasOwnProperty(e)&&m.on(i[e],function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return l(i[e],n)})}(c)},h>0)for(;f<h;f++)if(d.renderer.renderers[t.prefix].canPlayType(n[f].type)){p.setAttribute("src",n[f].src);break}"auto"===r||l||(p.addEventListener("play",function(){null!==m&&m.startLoad()}),p.addEventListener("pause",function(){null!==m&&m.stopLoad()})),p.setAttribute("id",o),i.parentNode.insertBefore(p,i),i.autoplay=!1,i.style.display="none",p.setSize=function(e,t){return p.style.width=e+"px",p.style.height=t+"px",p},p.hide=function(){return p.pause(),p.style.display="none",p},p.show=function(){return p.style.display="",p},p.destroy=function(){null!==m&&(m.stopLoad(),m.destroy())};var k=(0,u.createEvent)("rendererready",p);return e.dispatchEvent(k),e.promises.push(g.load({options:t.hls,id:o})),p}};p.typeChecks.push(function(e){return~e.toLowerCase().indexOf(".m3u8")?"application/x-mpegURL":null}),d.renderer.add(h)},{25:25,26:26,27:27,28:28,3:3,7:7,8:8}],23:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(3),a=i(o),r=e(2),s=i(r),l=e(7),c=i(l),d=e(8),u=e(27),m=e(25),p={name:"html5",options:{prefix:"html5"},canPlayType:function(e){var t=s.default.createElement("video");return m.IS_ANDROID&&/\/mp(3|4)$/i.test(e)||~["application/x-mpegurl","vnd.apple.mpegurl","audio/mpegurl","audio/hls","video/hls"].indexOf(e.toLowerCase())&&m.SUPPORTS_NATIVE_HLS?"yes":t.canPlayType?t.canPlayType(e.toLowerCase()).replace(/no/,""):""},create:function(e,t,n){var i=e.id+"_"+t.prefix,o=!1,a=null;void 0===e.originalNode||null===e.originalNode?(a=s.default.createElement("audio"),e.appendChild(a)):a=e.originalNode,a.setAttribute("id",i);for(var r=c.default.html5media.properties,l=0,m=r.length;l<m;l++)!function(e){var t=""+e.substring(0,1).toUpperCase()+e.substring(1);a["get"+t]=function(){return a[e]},a["set"+t]=function(t){-1===c.default.html5media.readOnlyProperties.indexOf(e)&&(a[e]=t)}}(r[l]);for(var p=c.default.html5media.events.concat(["click","mouseover","mouseout"]).filter(function(e){return"error"!==e}),f=0,g=p.length;f<g;f++)!function(t){a.addEventListener(t,function(t){if(o){var n=(0,u.createEvent)(t.type,t.target);e.dispatchEvent(n)}})}(p[f]);a.setSize=function(e,t){return a.style.width=e+"px",a.style.height=t+"px",a},a.hide=function(){return o=!1,a.style.display="none",a},a.show=function(){return o=!0,a.style.display="",a};var h=0,v=n.length;if(v>0)for(;h<v;h++)if(d.renderer.renderers[t.prefix].canPlayType(n[h].type)){a.setAttribute("src",n[h].src);break}a.addEventListener("error",function(t){4===t.target.error.code&&o&&(h<v&&void 0!==n[h+1]?(a.src=n[h++].src,a.load(),a.play()):e.generateError("Media error: Format(s) not supported or source(s) not found",n))});var y=(0,u.createEvent)("rendererready",a);return e.dispatchEvent(y),a}};a.default.HtmlMediaElement=c.default.HtmlMediaElement=p,d.renderer.add(p)},{2:2,25:25,27:27,3:3,7:7,8:8}],24:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(3),a=i(o),r=e(2),s=i(r),l=e(7),c=i(l),d=e(8),u=e(27),m=e(28),p=e(26),f={isIframeStarted:!1,isIframeLoaded:!1,iframeQueue:[],enqueueIframe:function(e){f.isLoaded="undefined"!=typeof YT&&YT.loaded,f.isLoaded?f.createIframe(e):(f.loadIframeApi(),f.iframeQueue.push(e))},loadIframeApi:function(){f.isIframeStarted||((0,p.loadScript)("https://www.youtube.com/player_api"),f.isIframeStarted=!0)},iFrameReady:function(){for(f.isLoaded=!0,f.isIframeLoaded=!0;f.iframeQueue.length>0;){var e=f.iframeQueue.pop();f.createIframe(e)}},createIframe:function(e){return new YT.Player(e.containerId,e)},getYouTubeId:function(e){var t="";return e.indexOf("?")>0?""===(t=f.getYouTubeIdFromParam(e))&&(t=f.getYouTubeIdFromUrl(e)):t=f.getYouTubeIdFromUrl(e),t=t.substring(t.lastIndexOf("/")+1).split("?"),t[0]},getYouTubeIdFromParam:function(e){if(void 0===e||null===e||!e.trim().length)return null;for(var t=e.split("?"),n=t[1].split("&"),i="",o=0,a=n.length;o<a;o++){var r=n[o].split("=");if("v"===r[0]){i=r[1];break}}return i},getYouTubeIdFromUrl:function(e){return void 0!==e&&null!==e&&e.trim().length?(e=e.split("?")[0],e.substring(e.lastIndexOf("/")+1)):null},getYouTubeNoCookieUrl:function(e){if(void 0===e||null===e||!e.trim().length||-1===e.indexOf("//www.youtube"))return e;var t=e.split("/");return t[2]=t[2].replace(".com","-nocookie.com"),t.join("/")}},g={name:"youtube_iframe",options:{prefix:"youtube_iframe",youtube:{autoplay:0,controls:0,disablekb:1,end:0,loop:0,modestbranding:0,playsinline:0,rel:0,showinfo:0,start:0,iv_load_policy:3,nocookie:!1,imageQuality:null}},canPlayType:function(e){return~["video/youtube","video/x-youtube"].indexOf(e.toLowerCase())},create:function(e,t,n){var i={},o=[],r=null,l=!0,d=!1,m=null,p=1;i.options=t,i.id=e.id+"_"+t.prefix,i.mediaElement=e;for(var g=c.default.html5media.properties,h=0,v=g.length;h<v;h++)!function(t){var n=""+t.substring(0,1).toUpperCase()+t.substring(1);i["get"+n]=function(){if(null!==r){switch(t){case"currentTime":return r.getCurrentTime();case"duration":return r.getDuration();case"volume":return p=r.getVolume()/100;case"paused":return l;case"ended":return d;case"muted":return r.isMuted();case"buffered":var e=r.getVideoLoadedFraction(),n=r.getDuration();return{start:function(){return 0},end:function(){return e*n},length:1};case"src":return r.getVideoUrl();case"readyState":return 4}return null}return null},i["set"+n]=function(n){if(null!==r)switch(t){case"src":var a="string"==typeof n?n:n[0].src,s=f.getYouTubeId(a);e.originalNode.autoplay?r.loadVideoById(s):r.cueVideoById(s);break;case"currentTime":r.seekTo(n);break;case"muted":n?r.mute():r.unMute(),setTimeout(function(){var t=(0,u.createEvent)("volumechange",i);e.dispatchEvent(t)},50);break;case"volume":p=n,r.setVolume(100*n),setTimeout(function(){var t=(0,u.createEvent)("volumechange",i);e.dispatchEvent(t)},50);break;case"readyState":var l=(0,u.createEvent)("canplay",i);e.dispatchEvent(l)}else o.push({type:"set",propName:t,value:n})}}(g[h]);for(var y=c.default.html5media.methods,I=0,b=y.length;I<b;I++)!function(e){i[e]=function(){if(null!==r)switch(e){case"play":return l=!1,r.playVideo();case"pause":return l=!0,r.pauseVideo();case"load":return null}else o.push({type:"call",methodName:e})}}(y[I]);var x=function(t){var i="";switch(t.data){case 2:i="The request contains an invalid parameter value. Verify that video ID has 11 characters and that contains no invalid characters, such as exclamation points or asterisks.";break;case 5:i="The requested content cannot be played in an HTML5 player or another error related to the HTML5 player has occurred.";break;case 100:i="The video requested was not found. Either video has been removed or has been marked as private.";break;case 101:case 105:i="The owner of the requested video does not allow it to be played in embedded players.";break;default:i="Unknown error."}e.generateError("Code "+t.data+": "+i,n)},k=s.default.createElement("div");k.id=i.id,i.options.youtube.nocookie&&(e.originalNode.src=f.getYouTubeNoCookieUrl(n[0].src)),e.originalNode.parentNode.insertBefore(k,e.originalNode),e.originalNode.style.display="none";var j="audio"===e.originalNode.tagName.toLowerCase(),C=j?"1":e.originalNode.height,S=j?"1":e.originalNode.width,M=f.getYouTubeId(n[0].src),w={id:i.id,containerId:k.id,videoId:M,height:C,width:S,playerVars:Object.assign({controls:0,rel:0,disablekb:1,showinfo:0,modestbranding:0,html5:1,iv_load_policy:3},i.options.youtube),origin:a.default.location.host,events:{onReady:function(t){if(e.youTubeApi=r=t.target,e.youTubeState={paused:!0,ended:!1},o.length)for(var n=0,a=o.length;n<a;n++){var s=o[n];if("set"===s.type){var l=s.propName,c=""+l.substring(0,1).toUpperCase()+l.substring(1);i["set"+c](s.value)}else"call"===s.type&&i[s.methodName]()}m=r.getIframe(),e.originalNode.muted&&r.mute();for(var d=["mouseover","mouseout"],p=function(t){var n=(0,u.createEvent)(t.type,i);e.dispatchEvent(n)},f=0,g=d.length;f<g;f++)m.addEventListener(d[f],p,!1);for(var h=["rendererready","loadedmetadata","loadeddata","canplay"],v=0,y=h.length;v<y;v++){var I=(0,u.createEvent)(h[v],i);e.dispatchEvent(I)}},onStateChange:function(t){var n=[];switch(t.data){case-1:n=["loadedmetadata"],l=!0,d=!1;break;case 0:n=["ended"],l=!1,d=!i.options.youtube.loop,i.options.youtube.loop||i.stopInterval();break;case 1:n=["play","playing"],l=!1,d=!1,i.startInterval();break;case 2:n=["pause"],l=!0,d=!1,i.stopInterval();break;case 3:n=["progress"],d=!1;break;case 5:n=["loadeddata","loadedmetadata","canplay"],l=!0,d=!1}for(var o=0,a=n.length;o<a;o++){var r=(0,u.createEvent)(n[o],i);e.dispatchEvent(r)}},onError:function(e){return x(e)}}};return(j||e.originalNode.hasAttribute("playsinline"))&&(w.playerVars.playsinline=1),e.originalNode.controls&&(w.playerVars.controls=1),e.originalNode.autoplay&&(w.playerVars.autoplay=1),e.originalNode.loop&&(w.playerVars.loop=1),(w.playerVars.loop&&1===parseInt(w.playerVars.loop,10)||e.originalNode.src.indexOf("loop=")>-1)&&!w.playerVars.playlist&&-1===e.originalNode.src.indexOf("playlist=")&&(w.playerVars.playlist=f.getYouTubeId(e.originalNode.src)),f.enqueueIframe(w),i.onEvent=function(t,n,i){null!==i&&void 0!==i&&(e.youTubeState=i)},i.setSize=function(e,t){null!==r&&r.setSize(e,t)},i.hide=function(){i.stopInterval(),i.pause(),m&&(m.style.display="none")},i.show=function(){m&&(m.style.display="")},i.destroy=function(){r.destroy()},i.interval=null,i.startInterval=function(){i.interval=setInterval(function(){var t=(0,u.createEvent)("timeupdate",i);e.dispatchEvent(t)},250)},i.stopInterval=function(){i.interval&&clearInterval(i.interval)},i.getPosterUrl=function(){var n=t.youtube.imageQuality,i=["default","hqdefault","mqdefault","sddefault","maxresdefault"],o=f.getYouTubeId(e.originalNode.src);return n&&i.indexOf(n)>-1&&o?"https://img.youtube.com/vi/"+o+"/"+n+".jpg":""},i}};a.default.onYouTubePlayerAPIReady=function(){f.iFrameReady()},m.typeChecks.push(function(e){return/\/\/(www\.youtube|youtu\.?be)/i.test(e)?"video/x-youtube":null}),d.renderer.add(g)},{2:2,26:26,27:27,28:28,3:3,7:7,8:8}],25:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(n,"__esModule",{value:!0}),n.cancelFullScreen=n.requestFullScreen=n.isFullScreen=n.FULLSCREEN_EVENT_NAME=n.HAS_NATIVE_FULLSCREEN_ENABLED=n.HAS_TRUE_NATIVE_FULLSCREEN=n.HAS_IOS_FULLSCREEN=n.HAS_MS_NATIVE_FULLSCREEN=n.HAS_MOZ_NATIVE_FULLSCREEN=n.HAS_WEBKIT_NATIVE_FULLSCREEN=n.HAS_NATIVE_FULLSCREEN=n.SUPPORTS_NATIVE_HLS=n.SUPPORT_PASSIVE_EVENT=n.SUPPORT_POINTER_EVENTS=n.HAS_MSE=n.IS_STOCK_ANDROID=n.IS_SAFARI=n.IS_FIREFOX=n.IS_CHROME=n.IS_EDGE=n.IS_IE=n.IS_ANDROID=n.IS_IOS=n.IS_IPOD=n.IS_IPHONE=n.IS_IPAD=n.UA=n.NAV=void 0;for(var o=e(3),a=i(o),r=e(2),s=i(r),l=e(7),c=i(l),d=n.NAV=a.default.navigator,u=n.UA=d.userAgent.toLowerCase(),m=n.IS_IPAD=/ipad/i.test(u)&&!a.default.MSStream,p=n.IS_IPHONE=/iphone/i.test(u)&&!a.default.MSStream,f=n.IS_IPOD=/ipod/i.test(u)&&!a.default.MSStream,g=(n.IS_IOS=/ipad|iphone|ipod/i.test(u)&&!a.default.MSStream,n.IS_ANDROID=/android/i.test(u)),h=n.IS_IE=/(trident|microsoft)/i.test(d.appName),v=(n.IS_EDGE="msLaunchUri"in d&&!("documentMode"in s.default)),y=n.IS_CHROME=/chrome/i.test(u),I=n.IS_FIREFOX=/firefox/i.test(u),b=n.IS_SAFARI=/safari/i.test(u)&&!y,x=n.IS_STOCK_ANDROID=/^mozilla\/\d+\.\d+\s\(linux;\su;/i.test(u),k=(n.HAS_MSE="MediaSource"in a.default),j=(n.SUPPORT_POINTER_EVENTS=function(){var e=s.default.createElement("x"),t=s.default.documentElement,n=a.default.getComputedStyle;if(!("pointerEvents"in e.style))return!1;e.style.pointerEvents="auto",e.style.pointerEvents="x",t.appendChild(e);var i=n&&"auto"===n(e,"").pointerEvents;return e.remove(),!!i}()),C=n.SUPPORT_PASSIVE_EVENT=function(){var e=!1;try{var t=Object.defineProperty({},"passive",{get:function(){e=!0}});a.default.addEventListener("test",null,t)}catch(e){}return e}(),S=["source","track","audio","video"],M=void 0,w=0,T=S.length;w<T;w++)M=s.default.createElement(S[w]);var N=n.SUPPORTS_NATIVE_HLS=b||g&&(y||x)||h&&/edge/i.test(u),E=void 0!==M.webkitEnterFullscreen,_=void 0!==M.requestFullscreen;E&&/mac os x 10_5/i.test(u)&&(_=!1,E=!1);var P=void 0!==M.webkitRequestFullScreen,A=void 0!==M.mozRequestFullScreen,D=void 0!==M.msRequestFullscreen,L=P||A||D,z=L,Z="",R=void 0,G=void 0,F=void 0;A?z=s.default.mozFullScreenEnabled:D&&(z=s.default.msFullscreenEnabled),y&&(E=!1),L&&(P?Z="webkitfullscreenchange":A?Z="mozfullscreenchange":D&&(Z="MSFullscreenChange"),n.isFullScreen=R=function(){return A?s.default.mozFullScreen:P?s.default.webkitIsFullScreen:D?null!==s.default.msFullscreenElement:void 0},n.requestFullScreen=G=function(e){P?e.webkitRequestFullScreen():A?e.mozRequestFullScreen():D&&e.msRequestFullscreen()},n.cancelFullScreen=F=function(){P?s.default.webkitCancelFullScreen():A?s.default.mozCancelFullScreen():D&&s.default.msExitFullscreen()});var B=n.HAS_NATIVE_FULLSCREEN=_,O=n.HAS_WEBKIT_NATIVE_FULLSCREEN=P,Q=n.HAS_MOZ_NATIVE_FULLSCREEN=A,J=n.HAS_MS_NATIVE_FULLSCREEN=D,W=n.HAS_IOS_FULLSCREEN=E,Y=n.HAS_TRUE_NATIVE_FULLSCREEN=L,H=n.HAS_NATIVE_FULLSCREEN_ENABLED=z,U=n.FULLSCREEN_EVENT_NAME=Z;n.isFullScreen=R,n.requestFullScreen=G,n.cancelFullScreen=F,c.default.Features=c.default.Features||{},c.default.Features.isiPad=m,c.default.Features.isiPod=f,c.default.Features.isiPhone=p,c.default.Features.isiOS=c.default.Features.isiPhone||c.default.Features.isiPad,c.default.Features.isAndroid=g,c.default.Features.isIE=h,c.default.Features.isEdge=v,c.default.Features.isChrome=y,c.default.Features.isFirefox=I,c.default.Features.isSafari=b,c.default.Features.isStockAndroid=x,c.default.Features.hasMSE=k,c.default.Features.supportsNativeHLS=N,c.default.Features.supportsPointerEvents=j,c.default.Features.supportsPassiveEvent=C,c.default.Features.hasiOSFullScreen=W,c.default.Features.hasNativeFullscreen=B,c.default.Features.hasWebkitNativeFullScreen=O,c.default.Features.hasMozNativeFullScreen=Q,c.default.Features.hasMsNativeFullScreen=J,c.default.Features.hasTrueNativeFullScreen=Y,c.default.Features.nativeFullScreenEnabled=H,c.default.Features.fullScreenEventName=U,c.default.Features.isFullScreen=R,c.default.Features.requestFullScreen=G,c.default.Features.cancelFullScreen=F},{2:2,3:3,7:7}],26:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}function o(e){return new Promise(function(t,n){var i=g.default.createElement("script");i.src=e,i.async=!0,i.onload=function(){i.remove(),t()},i.onerror=function(){i.remove(),n()},g.default.head.appendChild(i)})}function a(e){var t=e.getBoundingClientRect(),n=p.default.pageXOffset||g.default.documentElement.scrollLeft,i=p.default.pageYOffset||g.default.documentElement.scrollTop;return{top:t.top+i,left:t.left+n}}function r(e,t){x(e,t)?j(e,t):k(e,t)}function s(e){
var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=1);var i=null;p.default.requestAnimationFrame(function o(a){i=i||a;var r=a-i,s=parseFloat(1-r/t,2);e.style.opacity=s<0?0:s,r>t?n&&"function"==typeof n&&n():p.default.requestAnimationFrame(o)})}function l(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:400,n=arguments[2];e.style.opacity||(e.style.opacity=0);var i=null;p.default.requestAnimationFrame(function o(a){i=i||a;var r=a-i,s=parseFloat(r/t,2);e.style.opacity=s>1?1:s,r>t?n&&"function"==typeof n&&n():p.default.requestAnimationFrame(o)})}function c(e,t){var n=[];e=e.parentNode.firstChild;do{t&&!t(e)||n.push(e)}while(e=e.nextSibling);return n}function d(e){return void 0!==e.getClientRects&&"function"===e.getClientRects?!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length):!(!e.offsetWidth&&!e.offsetHeight)}function u(e,t,n,i){var o=p.default.XMLHttpRequest?new XMLHttpRequest:new ActiveXObject("Microsoft.XMLHTTP"),a="application/x-www-form-urlencoded; charset=UTF-8",r=!1,s="*/".concat("*");switch(t){case"text":a="text/plain";break;case"json":a="application/json, text/javascript";break;case"html":a="text/html";break;case"xml":a="application/xml, text/xml"}"application/x-www-form-urlencoded"!==a&&(s=a+", */*; q=0.01"),o&&(o.open("GET",e,!0),o.setRequestHeader("Accept",s),o.onreadystatechange=function(){if(!r&&4===o.readyState)if(200===o.status){r=!0;var e=void 0;switch(t){case"json":e=JSON.parse(o.responseText);break;case"xml":e=o.responseXML;break;default:e=o.responseText}n(e)}else"function"==typeof i&&i(o.status)},o.send())}Object.defineProperty(n,"__esModule",{value:!0}),n.removeClass=n.addClass=n.hasClass=void 0,n.loadScript=o,n.offset=a,n.toggleClass=r,n.fadeOut=s,n.fadeIn=l,n.siblings=c,n.visible=d,n.ajax=u;var m=e(3),p=i(m),f=e(2),g=i(f),h=e(7),v=i(h),y=void 0,I=void 0,b=void 0;"classList"in g.default.documentElement?(y=function(e,t){return void 0!==e.classList&&e.classList.contains(t)},I=function(e,t){return e.classList.add(t)},b=function(e,t){return e.classList.remove(t)}):(y=function(e,t){return new RegExp("\\b"+t+"\\b").test(e.className)},I=function(e,t){x(e,t)||(e.className+=" "+t)},b=function(e,t){e.className=e.className.replace(new RegExp("\\b"+t+"\\b","g"),"")});var x=n.hasClass=y,k=n.addClass=I,j=n.removeClass=b;v.default.Utils=v.default.Utils||{},v.default.Utils.offset=a,v.default.Utils.hasClass=x,v.default.Utils.addClass=k,v.default.Utils.removeClass=j,v.default.Utils.toggleClass=r,v.default.Utils.fadeIn=l,v.default.Utils.fadeOut=s,v.default.Utils.siblings=c,v.default.Utils.visible=d,v.default.Utils.ajax=u,v.default.Utils.loadScript=o},{2:2,3:3,7:7}],27:[function(e,t,n){function i(e){if("string"!=typeof e)throw new Error("Argument passed must be a string");var t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};return e.replace(/[&<>"]/g,function(e){return t[e]})}function o(e,t){var n=this,i=arguments,o=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("function"!=typeof e)throw new Error("First argument must be a function");if("number"!=typeof t)throw new Error("Second argument must be a numeric value");var a=void 0;return function(){var r=n,s=i,l=function(){a=null,o||e.apply(r,s)},c=o&&!a;clearTimeout(a),a=setTimeout(l,t),c&&e.apply(r,s)}}function a(e){return Object.getOwnPropertyNames(e).length<=0}function r(e,t){var n=/^((after|before)print|(before)?unload|hashchange|message|o(ff|n)line|page(hide|show)|popstate|resize|storage)\b/,i={d:[],w:[]};return(e||"").split(" ").forEach(function(e){var o=e+(t?"."+t:"");o.startsWith(".")?(i.d.push(o),i.w.push(o)):i[n.test(e)?"w":"d"].push(o)}),i.d=i.d.join(" "),i.w=i.w.join(" "),i}function s(e,t){if("string"!=typeof e)throw new Error("Event name must be a string");var n=e.match(/([a-z]+\.([a-z]+))/i),i={target:t};return null!==n&&(e=n[1],i.namespace=n[2]),new window.CustomEvent(e,{detail:i})}function l(e,t){return!!(e&&t&&2&e.compareDocumentPosition(t))}function c(e){return"string"==typeof e}Object.defineProperty(n,"__esModule",{value:!0}),n.escapeHTML=i,n.debounce=o,n.isObjectEmpty=a,n.splitEvents=r,n.createEvent=s,n.isNodeAfter=l,n.isString=c;var d=e(7),u=function(e){return e&&e.__esModule?e:{default:e}}(d);u.default.Utils=u.default.Utils||{},u.default.Utils.escapeHTML=i,u.default.Utils.debounce=o,u.default.Utils.isObjectEmpty=a,u.default.Utils.splitEvents=r,u.default.Utils.createEvent=s,u.default.Utils.isNodeAfter=l,u.default.Utils.isString=c},{7:7}],28:[function(e,t,n){function i(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=document.createElement("div");return t.innerHTML='<a href="'+(0,u.escapeHTML)(e)+'">x</a>',t.firstChild.href}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e&&!t?r(e):t}function a(e){if("string"!=typeof e)throw new Error("`type` argument must be a string");return e&&e.indexOf(";")>-1?e.substr(0,e.indexOf(";")):e}function r(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");for(var t=0,n=m.length;t<n;t++){var i=m[t](e);if(i)return i}var o=s(e),a=l(o),r="video/mp4";return a&&(~["mp4","m4v","ogg","ogv","webm","flv","mpeg","mov"].indexOf(a)?r="video/"+a:~["mp3","oga","wav","mid","midi"].indexOf(a)&&(r="audio/"+a)),r}function s(e){if("string"!=typeof e)throw new Error("`url` argument must be a string");var t=e.split("?")[0],n=t.split("\\").pop().split("/").pop();return~n.indexOf(".")?n.substring(n.lastIndexOf(".")+1):""}function l(e){if("string"!=typeof e)throw new Error("`extension` argument must be a string");switch(e){case"mp4":case"m4v":return"mp4";case"webm":case"webma":case"webmv":return"webm";case"ogg":case"oga":case"ogv":return"ogg";default:return e}}Object.defineProperty(n,"__esModule",{value:!0}),n.typeChecks=void 0,n.absolutizeUrl=i,n.formatType=o,n.getMimeFromType=a,n.getTypeFromFile=r,n.getExtension=s,n.normalizeExtension=l;var c=e(7),d=function(e){return e&&e.__esModule?e:{default:e}}(c),u=e(27),m=n.typeChecks=[];d.default.Utils=d.default.Utils||{},d.default.Utils.typeChecks=m,d.default.Utils.absolutizeUrl=i,d.default.Utils.formatType=o,d.default.Utils.getMimeFromType=a,d.default.Utils.getTypeFromFile=r,d.default.Utils.getExtension=s,d.default.Utils.normalizeExtension=l},{27:27,7:7}],29:[function(e,t,n){function i(e){return e&&e.__esModule?e:{default:e}}var o=e(2),a=i(o),r=e(4),s=i(r);if(function(e){e.forEach(function(e){e.hasOwnProperty("remove")||Object.defineProperty(e,"remove",{configurable:!0,enumerable:!0,writable:!0,value:function(){this.parentNode.removeChild(this)}})})}([Element.prototype,CharacterData.prototype,DocumentType.prototype]),function(){function e(e,t){t=t||{bubbles:!1,cancelable:!1,detail:void 0};var n=a.default.createEvent("CustomEvent");return n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail),n}if("function"==typeof window.CustomEvent)return!1;e.prototype=window.Event.prototype,window.CustomEvent=e}(),"function"!=typeof Object.assign&&(Object.assign=function(e){if(null===e||void 0===e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1,i=arguments.length;n<i;n++){var o=arguments[n];if(null!==o)for(var a in o)Object.prototype.hasOwnProperty.call(o,a)&&(t[a]=o[a])}return t}),String.prototype.startsWith||(String.prototype.startsWith=function(e,t){return t=t||0,this.substr(t,e.length)===e}),Element.prototype.matches||(Element.prototype.matches=Element.prototype.matchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector||Element.prototype.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=t.length-1;--n>=0&&t.item(n)!==this;);return n>-1}),window.Element&&!Element.prototype.closest&&(Element.prototype.closest=function(e){var t=(this.document||this.ownerDocument).querySelectorAll(e),n=void 0,i=this;do{for(n=t.length;--n>=0&&t.item(n)!==i;);}while(n<0&&(i=i.parentElement));return i}),function(){for(var e=0,t=["ms","moz","webkit","o"],n=0;n<t.length&&!window.requestAnimationFrame;++n)window.requestAnimationFrame=window[t[n]+"RequestAnimationFrame"],window.cancelAnimationFrame=window[t[n]+"CancelAnimationFrame"]||window[t[n]+"CancelRequestAnimationFrame"];window.requestAnimationFrame||(window.requestAnimationFrame=function(t){var n=(new Date).getTime(),i=Math.max(0,16-(n-e)),o=window.setTimeout(function(){t(n+i)},i);return e=n+i,o}),window.cancelAnimationFrame||(window.cancelAnimationFrame=function(e){clearTimeout(e)})}(),/firefox/i.test(navigator.userAgent)){var l=window.getComputedStyle;window.getComputedStyle=function(e,t){var n=l(e,t);return null===n?{getPropertyValue:function(){}}:n}}window.Promise||(window.Promise=s.default),function(e){e&&e.prototype&&null===e.prototype.children&&Object.defineProperty(e.prototype,"children",{get:function(){for(var e=0,t=void 0,n=this.childNodes,i=[];t=n[e++];)1===t.nodeType&&i.push(t);return i}})}(window.Node||window.Element)},{2:2,4:4}],30:[function(e,t,n){function i(){return!((arguments.length>0&&void 0!==arguments[0]?arguments[0]:25)%1==0)}function o(e,t){e<0&&(e=0);var n=t.framesPerSecond;return"audio"===t.playType?TimeCodeConvert.SecondToTimeString_audio(e):TimeCodeConvert.l100Ns2Tc$1(c(e),n,TimeCodeConvert.getRateDropFrame(n))}function a(e,t){return"audio"===t.playType?TimeCodeConvert.SecondToTimeString_audio(e):TimeCodeConvert.frame2Tc(TimeCodeConvert.second2Frame(e,t.framesPerSecond),t.framesPerSecond)}function r(e,t){var n=TimeCodeConvert.timeCode2Frame(e,t.framesPerSecond);return TimeCodeConvert.frame2Second(n,t.framesPerSecond)}function s(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:25;e=!e||"number"!=typeof e||e<0?0:e;for(var i=Math.floor(e/3600)%24,o=Math.floor(e/60)%60,a=Math.floor(e%60),r=Math.floor((e%1*n).toFixed(3)),s=[[r,"f"],[a,"s"],[o,"m"],[i,"h"]],l=t.timeFormat,c=l[1]===l[0],d=c?2:1,u=l.length<d?l[d]:":",m=l[0],p=!1,f=0,g=s.length;f<g;f++)if(~l.indexOf(s[f][1]))p=!0;else if(p){for(var h=!1,v=f;v<g;v++)if(s[v][0]>0){h=!0;break}if(!h)break;c||(l=m+l),l=s[f][1]+u+l,c&&(l=s[f][1]+l),m=s[f][1]}t.timeFormat=l}function l(e){if("string"!=typeof e)throw new TypeError("Argument must be a string value");e=e.replace(",",".");var t=~e.indexOf(".")?e.split(".")[1].length:0,n=0,i=1;e=e.split(":").reverse();for(var o=0,a=e.length;o<a;o++)i=1,o>0&&(i=Math.pow(60,o)),n+=Number(e[o])*i;return Number(n.toFixed(t))}function c(e){return parseInt(e.toFixed(7).replace(".",""),10)}Object.defineProperty(n,"__esModule",{value:!0}),n.isDropFrame=i,n.secondsToTimeCode=o,n.secondsToTimeCode$=a,n.timeCodeToSeconds=r,n.calculateTimeFormat=s,n.convertSMPTEtoSeconds=l;var d=e(7),u=function(e){return e&&e.__esModule?e:{default:e}}(d);u.default.Utils=u.default.Utils||{},u.default.Utils.secondsToTimeCode=o,u.default.Utils.secondsToTimeCode$=a,u.default.Utils.timeCodeToSeconds=r,u.default.Utils.calculateTimeFormat=s,u.default.Utils.convertSMPTEtoSeconds=l},{7:7}]},{},[29,6,5,15,23,20,19,21,22,24,16,18,17,9,10,11,12,13,14])}).call(t,n(2),n(10).setImmediate)},function(e,t,n){"use strict";(function(e){function i(e,t){this._id=e,this._clearFn=t}var o=void 0!==e&&e||"undefined"!=typeof self&&self||window,a=Function.prototype.apply;t.setTimeout=function(){return new i(a.call(setTimeout,o,arguments),clearTimeout)},t.setInterval=function(){return new i(a.call(setInterval,o,arguments),clearInterval)},t.clearTimeout=t.clearInterval=function(e){e&&e.close()},i.prototype.unref=i.prototype.ref=function(){},i.prototype.close=function(){this._clearFn.call(o,this._id)},t.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},t.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},t._unrefActive=t.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},n(11),t.setImmediate="undefined"!=typeof self&&self.setImmediate||void 0!==e&&e.setImmediate||void 0,t.clearImmediate="undefined"!=typeof self&&self.clearImmediate||void 0!==e&&e.clearImmediate||void 0}).call(t,n(2))},function(e,t,n){"use strict";(function(e,t){!function(e,n){function i(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var i={callback:e,args:t};return c[l]=i,s(l),l++}function o(e){delete c[e]}function a(e){var t=e.callback,i=e.args;switch(i.length){case 0:t();break;case 1:t(i[0]);break;case 2:t(i[0],i[1]);break;case 3:t(i[0],i[1],i[2]);break;default:t.apply(n,i)}}function r(e){if(d)setTimeout(r,0,e);else{var t=c[e];if(t){d=!0;try{a(t)}finally{o(e),d=!1}}}}if(!e.setImmediate){var s,l=1,c={},d=!1,u=e.document,m=Object.getPrototypeOf&&Object.getPrototypeOf(e);m=m&&m.setTimeout?m:e,"[object process]"==={}.toString.call(e.process)?function(){s=function(e){t.nextTick(function(){r(e)})}}():function(){if(e.postMessage&&!e.importScripts){var t=!0,n=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=n,t}}()?function(){var t="setImmediate$"+Math.random()+"$",n=function(n){n.source===e&&"string"==typeof n.data&&0===n.data.indexOf(t)&&r(+n.data.slice(t.length))};e.addEventListener?e.addEventListener("message",n,!1):e.attachEvent("onmessage",n),s=function(n){e.postMessage(t+n,"*")}}():e.MessageChannel?function(){var e=new MessageChannel;e.port1.onmessage=function(e){r(e.data)},s=function(t){e.port2.postMessage(t)}}():u&&"onreadystatechange"in u.createElement("script")?function(){var e=u.documentElement;s=function(t){var n=u.createElement("script");n.onreadystatechange=function(){r(t),n.onreadystatechange=null,e.removeChild(n),n=null},e.appendChild(n)}}():function(){s=function(e){setTimeout(r,0,e)}}(),m.setImmediate=i,m.clearImmediate=o}}("undefined"==typeof self?void 0===e?void 0:e:self)}).call(t,n(2),n(12))},function(e,t,n){"use strict";function i(){throw new Error("setTimeout has not been defined")}function o(){throw new Error("clearTimeout has not been defined")}function a(e){if(u===setTimeout)return setTimeout(e,0);if((u===i||!u)&&setTimeout)return u=setTimeout,setTimeout(e,0);try{return u(e,0)}catch(t){try{return u.call(null,e,0)}catch(t){return u.call(this,e,0)}}}function r(e){if(m===clearTimeout)return clearTimeout(e);if((m===o||!m)&&clearTimeout)return m=clearTimeout,clearTimeout(e);try{return m(e)}catch(t){try{return m.call(null,e)}catch(t){return m.call(this,e)}}}function s(){h&&f&&(h=!1,f.length?g=f.concat(g):v=-1,g.length&&l())}function l(){if(!h){var e=a(s);h=!0;for(var t=g.length;t;){for(f=g,g=[];++v<t;)f&&f[v].run();v=-1,t=g.length}f=null,h=!1,r(e)}}function c(e,t){this.fun=e,this.array=t}function d(){}var u,m,p=e.exports={};!function(){try{u="function"==typeof setTimeout?setTimeout:i}catch(e){u=i}try{m="function"==typeof clearTimeout?clearTimeout:o}catch(e){m=o}}();var f,g=[],h=!1,v=-1;p.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];g.push(new c(e,t)),1!==g.length||h||a(l)},c.prototype.run=function(){this.fun.apply(null,this.array)},p.title="browser",p.browser=!0,p.env={},p.argv=[],p.version="",p.versions={},p.on=d,p.addListener=d,p.once=d,p.off=d,p.removeListener=d,p.removeAllListeners=d,p.emit=d,p.prependListener=d,p.prependOnceListener=d,p.listeners=function(e){return[]},p.binding=function(e){throw new Error("process.binding is not supported")},p.cwd=function(){return"/"},p.chdir=function(e){throw new Error("process.chdir is not supported")},p.umask=function(){return 0}},function(e,t,n){"use strict";Object.assign(MediaElementPlayer.prototype,{play:function(){this.playInLockRange(),this.proxy.play(),$(this.controls).parent().find(".mejs__mediaelement").css("z-index","2")},pause:function(){this.proxy.pause(),$(this.controls).parent().find(".mejs__mediaelement").css("z-index","0")},stop:function(){this.pause()},addButton:function(e,t,n,i){var o=this,a=$('<div class="'+o.options.classPrefix+"button "+o.options.classPrefix+e+'-button" title="'+o.options[e+"Text"]+'"><button></button></div>');a.on("click",t),o["$"+e+"Button"]=a;var r="url("+o.options.pluginPath+"content/btns.png)";if(i&&!n){r="url("+o.options.pluginPath+"content/"+i+".png)";var s="url("+o.options.pluginPath+"content/"+i+"hover.png)";a.find("button").mouseenter(function(){a.find("button").css("background-image",s)}),a.find("button").mouseleave(function(){a.find("button").css("background-image",r)})}return n||(a.find("button").css("background-image",r),a.find("button").css("background-repeat","no-repeat")),o.addControlElement(a[0],e),a}})},function(e,t,n){"use strict";Object.assign(MediaElementPlayer.prototype,{isLockPlayRange:!1,startPlayInLockRange:function(){!0!==this.options.disableRangeLock&&this.isLockPlayRange&&void 0!==this.trimin&&(this.media.currentTime<this.trimin||this.media.currentTime>=this.trimout)&&(this.media.currentTime=this.trimin)},playInLockRange:function(){!0!==this.options.disableRangeLock&&this.isLockPlayRange&&void 0!==this.trimin&&void 0!==this.trimout&&this.media.currentTime>=this.trimout&&(this.media.currentTime=this.trimout,this.pause())},playInLockRangeForEnd:function(){!0!==this.options.disableRangeLock&&this.isLockPlayRange&&void 0!==this.trimin&&void 0!==this.trimout&&(this.media.currentTime=this.trimout)},getUpdateTimeForRange:function(e){return this.isLockPlayRange&&void 0!==this.trimin&&void 0!==this.trimout&&(e>this.trimout?e=this.trimout:e<this.trimin&&(e=this.trimin)),e},lockInPlayRange:function(){void 0!==this.trimin&&void 0!==this.trimout&&(this.isLockPlayRange=!0)},unlockInPlayRange:function(){this.isLockPlayRange=!1}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{tostartText:"到头".l("player.jump2Head"),toendText:"到尾".l("player.jump2Tail")}),Object.assign(MediaElementPlayer.prototype,{buildtostart:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());i.setCurrentTime(a.getUpdateTimeForRange(0))}}var a=this;a.addButton("tostart",o,void 0,"tostart"),a.options.keyActions.push({keys:[33],action:o})},buildtoend:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=Math.max(i.duration-1/e.options.framesPerSecond,0);i.setCurrentTime(a.getUpdateTimeForRange(t))}}var a=this;a.addButton("toend",o,void 0,"toend"),a.options.keyActions.push({keys:[34],action:o})}})},function(e,t,n){"use strict";mejs.i18n.en["mejs.fullscreen-off"]="Turn off Fullscreen",mejs.i18n.en["mejs.fullscreen-on"]="Go Fullscreen",mejs.i18n.en["mejs.download-video"]="Download Video",Object.assign(mejs.MepDefaults,{contextMenuItems:[{render:function(e){return void 0===e.enterFullScreen?null:e.isFullScreen?mejs.i18n.t("mejs.fullscreen-off"):mejs.i18n.t("mejs.fullscreen-on")},click:function(e){e.isFullScreen?e.exitFullScreen():e.enterFullScreen()}},{render:function(e){return e.media.muted?mejs.i18n.t("mejs.unmute"):mejs.i18n.t("mejs.mute")},click:function(e){e.media.muted?e.setMuted(!1):e.setMuted(!0)}},{isSeparator:!0},{render:function(){return mejs.i18n.t("mejs.download-video")},click:function(e){window.location.href=e.media.currentSrc}}]}),Object.assign(MediaElementPlayer.prototype,{isContextMenuEnabled:!0,contextMenuTimeout:null,buildcontextmenu:function(e){e.isVideo&&(document.querySelector("."+e.options.classPrefix+"contextmenu")||(e.contextMenu=document.createElement("div"),e.contextMenu.className=e.options.classPrefix+"contextmenu",e.contextMenu.style.display="none",document.body.appendChild(e.contextMenu)),e.container.addEventListener("contextmenu",function(t){!e.isContextMenuEnabled||3!==t.keyCode&&3!==t.which||(t.preventDefault(),t.stopPropagation())}),e.container.addEventListener("click",function(){e.contextMenu&&(e.contextMenu.style.display="none")}),e.contextMenu.addEventListener("mouseleave",function(){e.startContextMenuTimer()}))},cleancontextmenu:function(e){e.contextMenu.remove()},enableContextMenu:function(){this.isContextMenuEnabled=!0},disableContextMenu:function(){this.isContextMenuEnabled=!1},startContextMenuTimer:function(){var e=this;e.killContextMenuTimer(),e.contextMenuTimer=setTimeout(function(){e.hideContextMenu(),e.killContextMenuTimer()},750)},killContextMenuTimer:function(){var e=this.contextMenuTimer;null!==e&&void 0!==e&&(clearTimeout(e),e=null)},hideContextMenu:function(){this.contextMenu.style.display="none"},renderContextMenu:function(e){for(var t=this,n="",i=t.options.contextMenuItems,o=0,a=i.length;o<a;o++){var r=i[o];if(r.isSeparator)n+='<div class="'+t.options.classPrefix+'contextmenu-separator"></div>';else{var s=r.render(t);null!==s&&void 0!==s&&(n+='<div class="'+t.options.classPrefix+'contextmenu-item" data-itemindex="'+o+'" id="element-'+1e6*Math.random()+'">'+s+"</div>")}}t.contextMenu.innerHTML=n;var l=t.contextMenu.offsetWidth,c=t.contextMenu.offsetHeight,d=e.pageX,u=e.pageY,m=document.documentElement,p=(window.pageXOffset||m.scrollLeft)-(m.clientLeft||0),f=(window.pageYOffset||m.scrollTop)-(m.clientTop||0),g=d+l>window.innerWidth+p?d-l:d,h=u+c>window.innerHeight+f?u-c:u;t.contextMenu.style.display="",t.contextMenu.style.left=g+"px",t.contextMenu.style.top=h+"px";for(var v=t.contextMenu.querySelectorAll("."+t.options.classPrefix+"contextmenu-item"),y=0,I=v.length;y<I;y++)!function(e,n){var i=v[e],o=parseInt(i.getAttribute("data-itemindex"),10),a=t.options.contextMenuItems[o];void 0!==a.show&&a.show(i,t),i.addEventListener("click",function(){void 0!==a.click&&a.click(t),t.contextMenu.style.display="none"})}(y);setTimeout(function(){t.killControlsTimer()},100)}})},function(e,t,n){"use strict";mejs.i18n.en["mejs.speed-rate"]="播放速度".l("player.playSpeed"),Object.assign(mejs.MepDefaults,{speeds:["2.00","1.50","1.25","1.00","0.75"],defaultSpeed:"1.00",speedChar:"x",speedText:null}),Object.assign(MediaElementPlayer.prototype,{buildspeed:function(e,t,n,i){var o=this;if(null!==o.media.rendererName&&/(native|html5)/i.test(o.media.rendererName)){for(var a=[],r=mejs.Utils.isString(o.options.speedText)?o.options.speedText:mejs.i18n.t("mejs.speed-rate"),s=function(e){for(var t=0,n=a.length;t<n;t++)if(a[t].value===e)return a[t].name},l=void 0,c=!1,d=0,u=o.options.speeds.length;d<u;d++){var m=o.options.speeds[d];"string"==typeof m?(a.push({name:""+m+o.options.speedChar,value:m}),m===o.options.defaultSpeed&&(c=!0)):(a.push(m),m.value===o.options.defaultSpeed&&(c=!0))}c||a.push({name:o.options.defaultSpeed+o.options.speedChar,value:o.options.defaultSpeed}),a.sort(function(e,t){return parseFloat(t.value)-parseFloat(e.value)}),o.cleanspeed(e),e.speedButton=document.createElement("div"),e.speedButton.className=o.options.classPrefix+"button "+o.options.classPrefix+"speed-button",e.speedButton.innerHTML='<button type="button" aria-controls="'+o.id+'" title="'+r+'" aria-label="'+r+'" tabindex="0">'+s(o.options.defaultSpeed)+'</button><div class="'+o.options.classPrefix+"speed-selector "+o.options.classPrefix+'offscreen"><ul class="'+o.options.classPrefix+'speed-selector-list"></ul></div>',o.addControlElement(e.speedButton,"speed");for(var p=0,f=a.length;p<f;p++){var g=o.id+"-speed-"+a[p].value;e.speedButton.querySelector("ul").innerHTML+='<li class="'+o.options.classPrefix+'speed-selector-list-item"><input class="'+o.options.classPrefix+'speed-selector-input" type="radio" name="'+o.id+'_speed"disabled="disabled" value="'+a[p].value+'" id="'+g+'"  '+(a[p].value===o.options.defaultSpeed?' checked="checked"':"")+'/><label for="'+g+'" class="'+o.options.classPrefix+"speed-selector-label"+(a[p].value===o.options.defaultSpeed?" "+o.options.classPrefix+"speed-selected":"")+'">'+a[p].name+"</label></li>"}l=o.options.defaultSpeed,e.speedSelector=e.speedButton.querySelector("."+o.options.classPrefix+"speed-selector");for(var h=["mouseenter","focusin"],v=["mouseleave","focusout"],y=e.speedButton.querySelectorAll('input[type="radio"]'),I=e.speedButton.querySelectorAll("."+o.options.classPrefix+"speed-selector-label"),b=0,x=h.length;b<x;b++)e.speedButton.addEventListener(h[b],function(){mejs.Utils.removeClass(e.speedSelector,o.options.classPrefix+"offscreen"),e.speedSelector.style.height=e.speedSelector.querySelector("ul").offsetHeight,e.speedSelector.style.top=-1*parseFloat(e.speedSelector.offsetHeight)+"px"});for(var k=0,j=v.length;k<j;k++)e.speedSelector.addEventListener(v[k],function(){});for(var C=0,S=y.length;C<S;C++){var M=y[C];M.disabled=!1,M.addEventListener("click",function(){var t=this,n=t.value;l=n,i.playbackRate=parseFloat(n),e.speedButton.querySelector("button").innerHTML=s(n);for(var a=e.speedButton.querySelectorAll("."+o.options.classPrefix+"speed-selected"),r=0,c=a.length;r<c;r++)mejs.Utils.removeClass(a[r],o.options.classPrefix+"speed-selected");t.checked=!0;for(var d=mejs.Utils.siblings(t,function(e){return mejs.Utils.hasClass(e,o.options.classPrefix+"speed-selector-label")}),u=0,m=d.length;u<m;u++)mejs.Utils.addClass(d[u],o.options.classPrefix+"speed-selected")})}for(var w=0,T=I.length;w<T;w++)I[w].addEventListener("click",function(){var e=mejs.Utils.siblings(this,function(e){return"INPUT"===e.tagName})[0],t=mejs.Utils.createEvent("click",e);e.dispatchEvent(t)});e.speedSelector.addEventListener("keydown",function(e){e.stopPropagation()}),i.addEventListener("loadedmetadata",function(){l&&(i.playbackRate=parseFloat(l))})}},cleanspeed:function(e){e&&(e.speedButton&&e.speedButton.parentNode.removeChild(e.speedButton),e.speedSelector&&e.speedSelector.parentNode.removeChild(e.speedSelector))}})},function(e,t,n){"use strict";function i(e){var t=$(e.container).find(".mejs__inner"),n=t.find(".mejs__time-container");return 0==n.length&&(n=$('<div class="mejs__time-container"></div>'),t.append(n)),n}Object.assign(MediaElementPlayer.prototype,{getFramesFromTc:function(e){var t,n,i=this;if("FTC"===i.selTc)return e;if("LTC"===i.selTc?n=i.ltcList:"VITC"===i.selTc?n=i.vitcList:"RECTC"===i.selTc&&(n=i.rectcList),0!==n.length){for(var o,a,r=0,s=n.length;r<s;r++)if(a=void 0,o=n[r],r<s-1&&(a=n[r+1]),o.ntc>=0&&e>=o.ntc&&a&&a.ntc>=0&&e<a.ntc){t=o.offset+(e-i.currTcRange.ntc);break}if(!t){t=n[n.length-1].offset+(e-i.currTcRange.ntc)}return t}},buildcurrent:function(e,t,n,o){var a=this;a.$currentTime=$('<div class="'+a.options.classPrefix+'currenttime"><input type="text" value=""/></div>'),a.$currentTimeInput=a.$currentTime.find("input"),a.$currentTimeInput.val(mejs.Utils.secondsToTimeCode(0,a.options)),a.$currentTimeInput.on("keydown",function(e){if(13==e.keyCode){var t=a.$currentTimeInput.val();try{var n=TimeCodeConvert.timeCode2Frame(t,a.options.framesPerSecond);a.selTc&&(n=a.getFramesFromTc(n));var i=TimeCodeConvert.frame2Second(n,a.options.framesPerSecond);i+=5e-5,o.setCurrentTime(i)}catch(e){console.error(e)}}}),i(a).append(a.$currentTime),a.updateCurrent(),a.updateTimeCallback=function(){a.controlsAreVisible&&a.updateCurrent()},o.addEventListener("timeupdate",a.updateTimeCallback)},cleancurrent:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateTimeCallback)},buildduration:function(e,t,n,o){var a=this;a.$durationTime=$('<div class="'+a.options.classPrefix+'duration">'+mejs.Utils.secondsToTimeCode(a.options.duration,a.options)+"</div>"),i(a).append(a.$durationTime),a.updateDurationCallback=function(){a.controlsAreVisible&&a.updateDuration()},o.addEventListener("timeupdate",a.updateDurationCallback)},cleanduration:function(e,t,n,i){i.removeEventListener("timeupdate",e.updateDurationCallback)},getCurrTcRange:function(){var e=this,t=e.getCurrentTime();isNaN(t)&&(t=0);var n,i=TimeCodeConvert.second2Frame(t,e.options.framesPerSecond);if("LTC"===e.selTc?n=e.ltcList:"VITC"===e.selTc?n=e.vitcList:"RECTC"===e.selTc&&(n=e.rectcList),n&&0!==n.length){for(var o,a,r=void 0,s=0,l=n.length;s<l;s++)if(a=void 0,o=n[s],s<l-1&&(a=n[s+1]),i>=o.offset&&a&&i<a.offset){r={offset:o.offset,ntc:o.ntc,nextOffset:a.offset};break}if(!r){var c=n[n.length-1],d=e.getDuration();(isNaN(d)||d===1/0||d<0)&&(e.media.duration=e.options.duration=d=0),e.options.duration>0&&(d=e.options.duration),d<0&&(d=0),r={offset:c.offset,ntc:c.ntc,nextOffset:d}}e.currTcRange=r}},buildtc:function(e,t,n,o){var a=this,r=a.options.otcinfo;if(r&&(a.ltcList=_.orderBy(_.filter(r,{type:"1"}),["offset"],["asc"]),a.vitcList=_.orderBy(_.filter(r,{type:"2"}),["offset"],["asc"]),a.rectcList=_.orderBy(_.filter(r,{type:"3"}),["offset"],["asc"]),0!==a.ltcList.length||0!==a.vitcList.length||0!==a.rectcList.length)){a.$tc=$('<div class="'+a.options.classPrefix+'tc"><span>TC</span></div>');var s=$('<div class="dp-list"></div>');s.append('<div class="dp-item" data-type="FTC">FTC</div>'),a.ltcList.length>0&&s.append('<div class="dp-item" data-type="LTC">LTC</div>'),a.vitcList.length>0&&s.append('<div class="dp-item" data-type="VITC">VITC</div>'),a.rectcList.length>0&&s.append('<div class="dp-item" data-type="RECTC">RECTC</div>');var l=localStorage.getItem("default_timecode_cache");a.selTc=l||"LTC",a.$tc.children("span").html(a.selTc),a.$tc.append(s),a.getCurrTcRange(),s.on("click",function(e){e.target&&$(e.target).hasClass("dp-item")&&(a.selTc=$(e.target).data("type"),localStorage.setItem("default_timecode_cache",a.selTc),a.$tc.children("span").html(a.selTc),a.getCurrTcRange(),a.updateCurrent(),a.updateDuration())}),i(a).append(a.$tc)}},getCurrTcTc:function(e){var t,n=this;return n.currTcRange.ntc>=0?(e=n.currTcRange.ntc+(e-n.currTcRange.offset),t=TimeCodeConvert.frame2Tc(e,n.options.framesPerSecond,TimeCodeConvert.getRateDropFrame(n.options.framesPerSecond))):t="--:--:--:--",t},getCurrTcTimecode:function(e){var t,n=this;return"FTC"===n.selTc?t=TimeCodeConvert.frame2Tc(e,n.options.framesPerSecond,TimeCodeConvert.getRateDropFrame(n.options.framesPerSecond)):n.currTcRange&&e>=n.currTcRange.offset&&e<n.currTcRange.nextOffset?t=n.getCurrTcTc(e):n.currTcRange&&(e<n.currTcRange.offset||e>=n.currTcRange.nextOffset)?(n.getCurrTcRange(),t=n.getCurrTcTc(e)):t=TimeCodeConvert.frame2Tc(e,n.options.framesPerSecond,TimeCodeConvert.getRateDropFrame(n.options.framesPerSecond)),t},updateCurrent:function(){var e=this,t=e.getCurrentTime();isNaN(t)&&(t=0);var n=TimeCodeConvert.second2Frame(t,e.options.framesPerSecond),i=e.getCurrTcTimecode(n);e.$currentTimeInput.val(i),e.node.player.clearAllLocatePlayerArea()},updateDuration:function(){var e=this,t=e.getDuration();(isNaN(t)||t===1/0||t<0)&&(e.media.duration=e.options.duration=t=0),e.options.duration>0&&(t=e.options.duration),t<0&&(t=0);var n,i=e.options.framesPerSecond,o=TimeCodeConvert.l100Ns2Frame(parseInt(t.toFixed(7).replace(".",""),10),i);n="audio"===e.options.playType?TimeCodeConvert.SecondToTimeString_audio(t):TimeCodeConvert.frame2Tc(o,i,TimeCodeConvert.getRateDropFrame(i)),e.$durationTime.html(n)}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{getgifText:"Gif截图".l("player.gifScreenshot")}),Object.assign(MediaElementPlayer.prototype,{createGif:function(e){var t=this,n=t.trimin,i=t.trimout,o={el:document.querySelector("video"),parentEl:document.querySelector(".mejs__overlay-play"),workerScript:"libs/gif.worker.js",width:300,height:300,maxTime:10,fps:6,quality:5,shotAnimation:!1,endTime:i,beginTime:n,onStartShot:function(){},onGifProcess:function(){},onGifFinished:function(){}},a=Object.assign(o,e);new CoreVideoToGif(a).shot({start:a.beginTime,end:a.endTime})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{getKeyframeText:"打标记点".l("player.getKeyframe")});Object.assign(MediaElementPlayer.prototype,{getCurrentKeyframe:function(){for(var e=this.node,t="";!this.media.seeking;){try{var n=document.createElement("canvas");n.width=1*e.videoWidth,n.height=1*e.videoHeight,e.crossOrigin="anonymous",e.crossOrigin="*",n.crossOrigin="anonymous",n.crossOrigin="*";n.getContext("2d").drawImage(e,0,0,e.videoWidth,e.videoHeight),t=n.toDataURL("image/png").replace("image/png","image/octet-stream")}catch(e){console.error(e)}break}return t},getKeyframe:function(){var e=this.getCurrentKeyframe(),t=this.currentTime;null!=this.options.getKeyframe&&this.options.getKeyframe(this,t,e)},
getPartKeyframs:function(e,t,n,i,o,a){var r=a,s=this.currentTime;null!=this.options.getKeyframe&&this.options.getKeyframe(this,s,r,e,t,n,i,o)},getPartBase64:function(e,t,n,i,o){var a=document.createElement("canvas");a.width=n,a.height=i;var r=a.getContext("2d"),s=this.getCurrentKeyframe(),l=new Image;l.crossOrigin="Anonymous",l.src=s,l.onload=function(){r.drawImage(l,e,t,n,i,0,0,n,i),o&&o(a.toDataURL("image/png").replace("image/png","image/octet-stream"))}},locatePlayerArea:function(e,t,n,i,o){var a=this.currentTime;this.clearLocatePlayerArea(e,t,n,i);var r=$(this.media);this.pause(),r.removeClass("is-focus-locate-player-area");var s=this.playerNaturalWidth/this.playerNaturalHeight,l=r.height()*s,c=$('<div class="locate-player-area" data-x="'+e+'" data-y="'+t+'" data-width="'+n+'" data-height="'+i+'" data-currenttime="'+a+'"></div>');c.css({left:(e*(l/this.playerNaturalWidth)+(r.width()-l)/2)/r.width()*100+"%",top:t*(r.height()/this.playerNaturalHeight)/r.height()*100+"%",width:n*(l/this.playerNaturalWidth)/r.width()*100+"%",height:i*(r.height()/this.playerNaturalHeight)/r.height()*100+"%"}),o&&c.append('<div class="title">'+o+"</div>"),r.append(c)},focusLocatePlayerArea:function(e,t,n,i){var o=$(this.media);o.children(".locate-player-area").each(function(a,r){$(r).hasClass("locate-player-area")&&$(r).data("x")===e&&$(r).data("y")===t&&$(r).data("width")===n&&$(r).data("height")===i?$(r).hasClass("active")?($(r).removeClass("active"),o.removeClass("is-focus-locate-player-area")):($(r).addClass("active"),o.addClass("is-focus-locate-player-area")):$(r).removeClass("active")})},clearLocatePlayerArea:function(e,t,n,i){$(this.media).children(".locate-player-area").each(function(o,a){$(a).hasClass("locate-player-area")&&$(a).data("x")===e&&$(a).data("y")===t&&$(a).data("width")===n&&$(a).data("height")===i&&$(a).remove()})},clearAllLocatePlayerArea:function(e){var t=this.currentTime;$(this.media).children(".locate-player-area").each(function(n,i){(e||$(i).data("currenttime")!==t)&&$(i).hasClass("locate-player-area")&&$(i).remove()})},buildgetKeyframe:function(e,t,n,i){function o(){a()}function a(){i.seeking||(r.getKeyframe(),r.isFullScreen&&r.exitFullScreen())}var r=this;r.addButton("getKeyframe",o),r.options.keyActions.push({keys:[77],action:o})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{backframeInterval:1,backframeText:"退1帧".l("player.prevFrame"),prevframeInterval:1,prevframeText:"进1帧".l("player.nextFrame")}),Object.assign(MediaElementPlayer.prototype,{buildbackframe:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=Math.max(e.currentTime-e.options.defaultSeekBackwardInterval(e),0),n=a.getUpdateTimeForRange(t);e.setCurrentTime(n+5e-5)}}var a=this,r=e.options.backframeInterval;e.options.backframeInterval=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackFrame?nxt.config.entity.playerFastBackFrame:r;var s=e.options.backframeText;e.options.backframeText=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackFrame?"退".l("player.retreat")+nxt.config.entity.playerFastBackFrame+"帧".l("player.frame"):s,a.addButton("backframe",o),a.options.keyActions.push({keys:[38],action:o}),a.options.keyActions.push({keys:[33],action:function(){if(!isNaN(i.duration)&&i.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());i.setCurrentTime(0),i.dispatchEvent(new CustomEvent("accurateTimeUpdate",{detail:0}))}}})},buildprevframe:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=Math.min(e.currentTime+e.options.defaultSeekForwardInterval(e),e.duration);t>=e.duration&&(t=e.duration-.01);var n=a.getUpdateTimeForRange(t);e.setCurrentTime(n+5e-5)}}var a=this,r=e.options.prevframeInterval;e.options.prevframeInterval=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackFrame?nxt.config.entity.playerFastBackFrame:r;var s=e.options.prevframeText;e.options.prevframeText=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackFrame?"进".l("player.advance")+nxt.config.entity.playerFastBackFrame+"帧".l("player.frame"):s,a.addButton("prevframe",o),a.options.keyActions.push({keys:[40],action:o}),a.options.keyActions.push({keys:[34],action:function(){if(!isNaN(i.duration)&&i.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=Math.max(i.duration-1/e.options.framesPerSecond,0);i.setCurrentTime(t),i.dispatchEvent(new CustomEvent("accurateTimeUpdate",{detail:t}))}}})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{skipBackInterval:5,skipbackText:"退5秒".l("player.back5Sec"),skipForwardInterval:5,skipforwardText:"进5秒".l("player.go5Sec")}),Object.assign(MediaElementPlayer.prototype,{buildskipback:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackTime?nxt.config.entity.playerFastBackTime:a.options.skipBackInterval,n=Math.min(e.currentTime-t,e.duration),i=a.getUpdateTimeForRange(n);e.setCurrentTime(i)}}var a=this,r=e.options.skipbackText;e.options.skipbackText=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackTime?"退".l("player.retreat")+nxt.config.entity.playerFastBackTime+"秒".l("player.second"):r,a.addButton("skipback",o),a.options.keyActions.push({keys:[37],action:o})},buildskipforward:function(e,t,n,i){function o(){if(!isNaN(e.duration)&&e.duration>0){e.isVideo&&(e.showControls(),e.startControlsTimer());var t=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackTime?nxt.config.entity.playerFastBackTime:a.options.skipBackInterval,n=Math.min(e.currentTime+t,e.duration);n>=e.duration&&(n=e.duration-.01);var i=a.getUpdateTimeForRange(n);e.setCurrentTime(i)}}var a=this,r=e.options.skipforwardText;e.options.skipforwardText=nxt&&nxt.config&&nxt.config.entity&&nxt.config.entity.playerFastBackTime?"进".l("player.advance")+nxt.config.entity.playerFastBackTime+"秒".l("player.second"):r,a.addButton("skipforward",o),a.options.keyActions.push({keys:[39],action:o})}})},function(e,t,n){"use strict";function i(e){e.$trimInInput.on("keydown",function(t){var t=t||arguments[0];e.setTrim(t,e)}),e.$trimInInput.on("blur",function(t){var t=t||arguments[0];e.setTrim(t,e)}),e.$trimOutInput.on("keydown",function(t){var t=t||arguments[0];e.setTrim(t,e)}),e.$trimOutInput.on("blur",function(t){var t=t||arguments[0];e.setTrim(t,e)})}function o(e,t){var t=t||arguments[0];e.dragstartPosition=t.clientX,e.dragstartWidth=parseFloat(e.$trimRangeBox.css("width")),e.trimRangeBoxLeft=parseFloat(e.$trimRangeBox.css("left"))}function a(e,t){var t=t||arguments[0],n=e.trimRangeBoxLeft-(e.dragstartPosition-t.clientX),i=e.dragstartWidth+(e.dragstartPosition-t.clientX);if(n<0&&(n=0),n>e.trimRangeBoxLeft+e.dragstartWidth&&(n=e.trimRangeBoxLeft+e.dragstartWidth),i>e.trimRangeBoxLeft+e.dragstartWidth&&(i=e.trimRangeBoxLeft+e.dragstartWidth),0===i)return void e.cleanTrim();var o=$(e.controls).find(".mejs__time-total").width(),a=n/o*e.media.duration;e.trimin=a,e.$trimInInput.val(mejs.Utils.secondsToTimeCode(e.trimin,e.options)),e.$trimRangeBox.css("left",n/o*100+"%"),e.$trimRangeBox.css("width",i/o*100+"%"),i+n<e.$trimRangeText.width()&&"0px"!==e.$trimRangeText.css("left")&&(e.$trimRangeText.css("left",0),e.$trimRangeText.css("right","auto")),e.$trimRangeContainer.width()-n<e.$trimRangeText.width()&&"0px"===e.$trimRangeText.css("left")&&(e.$trimRangeText.css("left","auto"),e.$trimRangeText.css("right",0)),e.$trimRangeContainer.width()-n>e.$trimRangeText.width()&&(e.$trimRangeText.css("left",0),e.$trimRangeText.css("right","auto"))}function r(e,t){t.clientX>0&&(e.dragendPosition=t.clientX);var t=t||arguments[0],n=e.dragstartWidth+(e.dragendPosition-e.dragstartPosition),i=$(e.controls).find(".mejs__time-total").width();if(n<0&&(n=0),n+e.trimRangeBoxLeft>i&&(n=i-e.trimRangeBoxLeft),0===n)return void e.cleanTrim();var o=(n+e.trimRangeBoxLeft)/i*e.media.duration;e.trimout=o,e.$trimOutInput.val(mejs.Utils.secondsToTimeCode(e.trimout,e.options)),e.$trimRangeBox.css("width",n/i*100+"%"),e.trimRangeBoxLeft+n<e.$trimRangeText.width()&&"0px"!==e.$trimRangeText.css("left")&&(e.$trimRangeText.css("left",0),e.$trimRangeText.css("right","auto"))}function s(e){e.$trimRangeLeft.css("cursor","e-resize"),e.$trimRangeRight.css("cursor","e-resize"),e.$trimRangeLeft.on("dragstart",function(t){o(e,t)}),e.$trimRangeLeft.on("drag",function(t){a(e,t)}),e.$trimRangeLeft.on("dragend",function(t){a(e,t),e.triminKeyframe=e.getCurrentKeyframe(),e.isShowModifyBtn&&e.addModifyBtn()}),e.$trimRangeRight.on("dragstart",function(t){o(e,t)}),e.$trimRangeRight.on("drag",function(t){r(e,t)}),e.$trimRangeRight.on("dragend",function(t){r(e,t),e.isShowModifyBtn&&e.addModifyBtn()})}Object.assign(mejs.MepDefaults,{triminText:"设置入点".l("player.setInpoint"),trimoutText:"设置出点".l("player.setOutpoint"),cleantrimText:"清除打点信息".l("player.cleanInoutpoint")}),Object.assign(MediaElementPlayer.prototype,{trimin:0,trimout:0,istrimed:!1,createTrimRange:function(){var e=this,t=$(this.controls).find(".mejs__time-rail"),n=t.find(".trim-range-container");0==n.length&&(n=$('<div class="trim-range-container"></div>'),e.$trimRangeBox=$('<div class="trim-range-box"><div class="trim-range-handle trim-range-left"></div><div class="trim-range"></div><div class="trim-range-text"><input class="trim-in-input" type="text"/> - <input class="trim-out-input" type="text"/></div><div class="trim-range-handle trim-range-right"></div></div>'),n.append(e.$trimRangeBox),t.append(n)),this.$trimRangeContainer=n,this.$trimRangeText=n.find(".trim-range-text"),this.$trimInInput=n.find(".trim-in-input"),this.$trimOutInput=n.find(".trim-out-input"),this.$trimRangeLeft=n.find(".trim-range-left"),this.$trimRangeRight=n.find(".trim-range-right"),this.$trimRangeLeft.attr("draggable",!0),this.$trimRangeRight.attr("draggable",!0),i(e),s(e)},hideModifyBtn:function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(this.isShowModifyBtn=!1),$(".modify-trim").hide(),$(".reset-trim").hide()},showModifyBtn:function(){this.isShowModifyBtn=!0},addModifyBtn:function(){var e=this;0===$(".trim-range-text").find(".modify-trim").length?($(".trim-range-text").append($('<span class="modify-trim">修改</span><span class="reset-trim">取消</span>')),$(".modify-trim").bind("click",function(){var t=e.getCurrentKeyframe();e.triminKeyframe&&(t=e.triminKeyframe),e.options.modifySection&&e.options.modifySection(e,t,e.trimin,e.trimout)}),$(".reset-trim").bind("click",function(){e.options.resetSection&&e.options.resetSection(function(t,n){e.hideModifyBtn(!0),e.setTrimin(t),e.setTrimout(n),e.showModifyBtn()})})):($(".reset-trim").show(),$(".modify-trim").show())},setTrim:function(e,t){var n=e.target||e.srcElement,i=/^[0-9]+\:[0-9]+\:[0-9]+\:[0-9]+$/,o=$(n).val();if("keydown"===e.type&&13===e.keyCode||"blur"===e.type){if(null===o.match(i))return;var a=mejs.Utils.timeCodeToSeconds(o,t.options);switch(a>t.duration&&(a=t.duration),n.className){case"trim-in-input":t.setTrimin(a);break;case"trim-out-input":t.setTrimout(a)}}},showTrimRange:function(){if(this.trimout>0&&(void 0===this.media.duration||isNaN(this.media.duration))){var e=this;setTimeout(function(){e.showTrimRange()},1e3)}else this.isShowModifyBtn&&this.addModifyBtn();var t=$(this.controls).find(".mejs__time-total").width(),n=t/this.media.duration,i=this.trimin*n,o=i/t*100+"%",a=this.trimout*n;this.$trimRangeBox.css("left",o),this.$trimRangeBox.css("width",(a-i)/t*100+"%"),this.$trimInInput.val(mejs.Utils.secondsToTimeCode$(this.trimin,this.options)),this.$trimOutInput.val(mejs.Utils.secondsToTimeCode$(this.trimout,this.options));var r=this.$trimRangeContainer.css("width"),s=i,l=this.$trimRangeText.css("width");r.substring(0,r.length-2)-s<=l.substring(0,l.length-2)?(this.$trimRangeText.css("left","auto"),this.$trimRangeText.css("right",0)):(this.$trimRangeText.css("left",0),this.$trimRangeText.css("right","auto")),this.$trimRangeText.fadeIn(),this.lockInPlayRange()},setTrimin:function(e){this.trimin=e,this.trimout<=this.trimin&&(this.trimout=this.duration),this.istrimed=!0,this.showTrimRange(),this.triminKeyframe=this.getCurrentKeyframe(),this.options.setTrimin&&this.options.setTrimin(this,this.trimin,this.trimout)},setTrimout:function(e){this.trimout=e,this.istrimed=!0,this.trimout<=this.trimin&&(this.trimin=0),this.showTrimRange(),this.options.setTrimout&&this.options.setTrimout(this,this.trimin,this.trimout)},cleanTrim:function(){this.unlockInPlayRange(),this.istrimed=!1,this.trimin=0,this.trimout=0,this.$trimRangeBox.css("left",0),this.$trimRangeBox.css("width",0),this.$trimRangeText.fadeOut(),this.options.cleanTrim&&this.options.cleanTrim(this)},buildtrim:function(e,t,n,i){var o=this;o.createTrimRange(),o.cleanTrim()},buildtrimin:function(e,t,n,i){function o(){i.seeking||a.setTrimin(Math.max(i.currentTime))}var a=this;a.createTrimRange(),a.cleanTrim(),this.isShowModifyBtn=!1,a.addButton("trimin",o),a.options.keyActions.push({keys:[219],action:o})},buildtrimout:function(e,t,n,i){function o(){i.seeking||a.setTrimout(Math.max(i.currentTime))}var a=this;a.addButton("trimout",o),a.options.keyActions.push({keys:[221],action:o})},buildcleantrim:function(e,t,n,i){var o=this;o.addButton("cleantrim",function(){i.seeking||o.cleanTrim()})},cleanbackframe:function(e){}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{toggleKeyPointText:"显示/隐藏关键点".l("player.showHideKeypoint")}),Object.assign(MediaElementPlayer.prototype,{addKeyPoint:function(e){var t=this;e=_.isArray(e)?e:[e],_.isArray(t.keyPoints)||(t.keyPoints=[]);for(var n=0;n<e.length;n++)t.createKeyPoint(e[n]),t.keyPoints.push(e[n])},createKeyPoint:function(e){var t=this;if(!(timecodeconvert.l100Ns2Second(e.frameNo)>t.duration)){var n=t.timefloat,i=$(this.controls).find(".mejs__time-total"),o=$('<div class="mejs__keypoint" data-key="'+e.frameNo+'"></div>');o.css("left",timecodeconvert.l100Ns2Second(e.frameNo)/t.duration*100+"%"),o.on("mouseover",function(t){t.stopPropagation(),i.find(".mejs__time-float").remove();var n=$('<div class="mejs__keypoint-info">'+e.content+"</div>"),o=$('<div class="mejs__keypoint-info-triangle"></div>');0!==$(this).find(".mejs__keypoint-info").length?($(this).find(".mejs__keypoint-info").show(),$(this).find(".mejs__keypoint-info-triangle").show()):($(this).append(n),$(this).append(o));var a=$(this).find(".mejs__keypoint-info").width(),r="",s=$(this).find(".mejs__keypoint-info-triangle").css("border-left-width");r=parseInt($(this).css("left"))<a/2-4?"-2px":parseInt($(this).css("left"))>i.width()-a/2+4?-a-$(this).width()/2+4+"px":-a/2-$(this).width()/2+"px",$(this).find(".mejs__keypoint-info").css("left",r),$(this).find(".mejs__keypoint-info-triangle").css("left",$(this).width()/2-parseInt(s)+"px")}),o.on("mouseout",function(e){$(this).find(".mejs__keypoint-info").hide(),$(this).find(".mejs__keypoint-info-triangle").hide(),i.append(n)}),o.on("click",function(n){n.stopPropagation(),t.setCurrentTime(timecodeconvert.l100Ns2Second(e.frameNo))}),i.append(o)}},removeKeyPoint:function(e){for(var t=$(this.controls).find(".mejs__time-total"),n=t.find(".mejs__keypoint"),i=0;i<n.length;i++)n.eq(i).attr("data-key").toString()===e.toString()&&n.eq(i).remove()},cleanKeyPoint:function(){var e=this;_.forEach(e.keyPoints,function(t){e.removeKeyPoint(t.frameNo)})},toggleKeyPoint:function(){var e=$(this.controls).find(".mejs__time-total"),t=e.find(".mejs__keypoint"),n=$(this.controls).find(".mejs__toggleKeyPoint-button");"block"===t.css("display")?(t.hide(),n.addClass("mejs__toggleon"),n.removeClass("mejs__toggleoff")):(t.show(),n.addClass("mejs__toggleoff"),n.removeClass("mejs__toggleon"))},buildtogglekeypoint:function(e,t,n,i){var o=this;o.addButton("toggleKeyPoint",function(){i.seeking||o.toggleKeyPoint()})}})},function(e,n,i){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=function(){function e(t,n){o(this,e),this.analyser,this.audio=t,this.container=n,this.dataArray,this.createAnalyser(),this.createCanvas(),this.zoomPage(),this.draw()}return a(e,[{key:"createAnalyser",value:function(){var e=AudioContext||webkitAudioContext,t=new e,n=t.createMediaElementSource(this.audio);this.analyser=t.createAnalyser(),n.connect(this.analyser),this.analyser.connect(t.destination),this.analyser.fftSize=4096,this.dataArray=new Uint8Array(this.analyser.fftSize)}},{key:"createCanvas",value:function(){this.canvas=document.createElement("canvas"),this.canvas.setAttribute("width","1366"),this.canvas.setAttribute("height","720"),this.canvas.setAttribute("style","position:absolute;z-index:99;"),this.p=this.canvas.getContext("2d"),this.container.appendChild(this.canvas)}},{key:"zoomPage",value:function(){if(this.mediaHeight=this.container.offsetHeight,this.mediaWidth=this.container.offsetWidth,this.mediaWidth!=this.canvas.width||this.mediaHeight!=this.canvas.height){var e=(this.mediaWidth/this.canvas.width).toPrecision(5),t=(this.mediaHeight/this.canvas.height).toPrecision(5),n=document.createElement("style");document.head.appendChild(n);n.sheet.insertRule("canvas{transform-origin:0% 0%;transform:scale("+e+","+t+");}",0),console.log("执行了zoom操作:",e,t)}}},{key:"draw",value:function(){this.p.globalAlpha=.7,requestAnimationFrame(this.draw.bind(this)),this.analyser.getByteFrequencyData(this.dataArray),this.p.clearRect(0,0,this.canvas.width,this.canvas.height);this.p.beginPath(),this.p.moveTo(0,this.canvas.height-300);for(var e=0,t=1;t<1e3;t++){var n=this.dataArray[t]/512*this.canvas.height/2;t<500?this.p.lineTo(e,this.canvas.height-this.dataArray[t]/512*this.canvas.height/2-300):t>700?this.p.lineTo(e-13,this.canvas.height-300):this.p.lineTo(e,this.canvas.height-n-300),e+=4.5}this.p.fillStyle="#1f8be9",this.p.fill(),this.p.closePath(),this.p.fillStyle="transparent",this.p.fillRect(0,this.canvas.height-300,1366,201),this.p.beginPath(),this.p.moveTo(0,this.canvas.height-300);for(var e=0,t=1;t<1e3;t++){var n=this.dataArray[t]/512*this.canvas.height/2;t<400&&this.p.lineTo(e,this.dataArray[t]/512*this.canvas.height/5+420),e+=4.5}this.p.lineTo(e-12,this.canvas.height-299),this.p.fillStyle="#175899",this.p.fill(),this.p.closePath(),this.p.shadowBlur=0}},{key:"clean",value:function(){}}]),e}();Object.assign(MediaElementPlayer.prototype,{buildaudioVisualizer:function(e,t,n,i){var o=this,a=$(i).find("audio")[0],s=a.parentElement.parentElement;setTimeout(function(){o.audioVisualizer=new r(a,s)},2e3)},cleanaudioVisualizer:function(e,n,i,o){t.audioVisualizer.clean()}})},function(e,n,i){"use strict";function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),r=i(4).default,s=i(27).default;window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.msAudioContext;var l=["0","2","4","6","8","10","12","15","20","24","30","30"],c=function(){function e(){o(this,e)}return a(e,[{key:"init",value:function(e,t){var n=this;this.isDoubleChannel=!0,this.mediaElementPlayer=t,this.player=t.media.childNodes[0],this.initDom(e),this.TICKS_COLOR="#86888a",this.AXIS_LINEWIDTH=.3,this.AXIS_COLOR="#86888a",this.drawAxes(),this.player.onplay=function(){n.initPlayer(),n.meter&&n.meter.shutdown(),n.shutdownAllofAudioMeter(),!n.shutupFrontAudioMeter()&&n.meter&&n.meter.shutup()},this.player.onpause=function(){n.meter&&n.meter.shutdown(),n.shutdownAllofAudioMeter()},this.player.onload=function(){console.log("var1, var2")},this.player.onended=function(){n.clean()},$(t.eightTrack).on("onChangeAudio",function(e,i){var o=t.eightTrack.audio_players[i];o.audioMeter||(o.audioMeter=u.createAudioMeter(o.audioContext)),o.channelMerger.connect(o.audioMeter),n.meter&&n.meter.shutdown(),n.shutdownAllofAudioMeter(),n.shutupFrontAudioMeter()})}},{key:"getFirstActiveAudio",value:function(){if(!this.mediaElementPlayer.eightTrack)return{left:void 0,right:void 0};var e,t,n=this,i=Object.keys(this.mediaElementPlayer.eightTrack.audio_players).sort();return i.forEach(function(i){if(n.mediaElementPlayer.eightTrack.audio_players[i].active){var o=n.mediaElementPlayer.eightTrack.audio_players[i].index;void 0===e&&(o+1)%2>0?e=n.mediaElementPlayer.eightTrack.audio_players[i]:void 0===t&&(o+1)%2==0&&(t=n.mediaElementPlayer.eightTrack.audio_players[i])}}),{left:e,right:t}}},{key:"shutupFrontAudioMeter",value:function(){var e=this.getFirstActiveAudio();return!(!e.left&&!e.right)&&(e.left&&e.left.audioMeter.shutup(),e.right&&e.right.audioMeter.shutup(),!0)}},{key:"shutdownAllofAudioMeter",value:function(){this.mediaElementPlayer.eightTrack&&_.forEach(this.mediaElementPlayer.eightTrack.audio_players,function(e){e.audioMeter&&e.audioMeter.shutdown()})}},{key:"initDom",value:function(e){$(e).children(".mejs__volumn-track")&&$(e).children(".mejs__volumn-track").remove();var t=document.createElement("DIV");t.className="mejs__volumn-track",e.appendChild(t);var n=document.createElement("div");n.className="mejs__volumn-track_top",n.style.width=t.clientWidth+"px",n.innerHTML="<span>L</span><span>R</span>",t.appendChild(n),this.canvas=document.createElement("CANVAS"),this.canvas.width=t.clientWidth,this.canvas.height=t.offsetHeight-n.offsetHeight,t.appendChild(this.canvas),this.ctx=this.canvas.getContext("2d")}},{key:"initPlayer",value:function(){if(!this.context){this.context=new window.AudioContext;var e=this.context.createMediaElementSource(this.player),t=this.context.createChannelSplitter(32),n=this.context.createChannelMerger(32);e.connect(t),t.connect(n,0,0),t.connect(n,1,1),this.meter=u.createAudioMeter(this.context),n.connect(this.context.destination),n.connect(this.meter)}}},{key:"clean",value:function(){this.ctx.clearRect(2,0,5,this.canvas.height),this.ctx.clearRect(this.canvas.width-7,0,5,this.canvas.height)}},{key:"render",value:function(e,t){this.ctx.strokeStyle="#00d0ff",this.ctx.lineWidth=2,e>0&&this.ctx.clearRect(2,0,5,this.canvas.height),t>0&&this.ctx.clearRect(this.canvas.width-7,0,5,this.canvas.height);var n=this.ctx.createLinearGradient(0,this.canvas.height,0,0);n.addColorStop(0,"#00EC00"),n.addColorStop(.7,"#FFFF37"),n.addColorStop(1,"#FF0000");var i=this.ctx.createLinearGradient(0,this.canvas.height,0,0);if(n.addColorStop(0,"#00EC00"),i.addColorStop(.7,"#FFFF37"),i.addColorStop(1,"#FF0000"),e>0){var o=20*Math.log10(e);o=o<-100?-100:o,o=e*this.canvas.height,this.ctx.fillStyle=n,this.ctx.fillRect(2,this.canvas.height,5,1-o)}if(t>0){var a=20*Math.log10(t);a=a<-100?-100:a,a=t*this.canvas.height,this.ctx.fillStyle=n,this.ctx.fillRect(this.canvas.width-7,this.canvas.height,5,1-a)}}},{key:"drawAxes",value:function(){this.ctx.save(),this.ctx.strokeStyle=this.AXIS_COLOR,this.ctx.lineWidth=this.AXIS_LINEWIDTH,this.ctx.strokeStyle=this.TICKS_COLOR,this.drawVerticalAxisTicks(),this.ctx.restore()}},{key:"drawVerticalAxisTicks",value:function(){var e=this;l.forEach(function(t,n,i){e.ctx.beginPath(),e.ctx.fillStyle=e.AXIS_COLOR,e.ctx.textAlign="center","0"===t?(e.ctx.textBaseline="top",e.ctx.fillText(t,e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height+4,e.canvas.width)):n===i.length-1?(e.ctx.textBaseline="bottom",e.ctx.fillText("-"+(n+1===i.length?"∞":t),e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height,e.canvas.width)):(e.ctx.textBaseline="middle",e.ctx.fillText("-"+(n+1===i.length?"∞":t),e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height,e.canvas.width)),e.ctx.stroke()})}},{key:"getVolume",value:function(e){e=Number(e);var t=Math.pow(10,-e/20);return 1-(t<=1e-6?0:t)}}]),e}(),d=new c,u=new r(d),m=new s;Object.assign(MediaElementPlayer.prototype,{buildvolumnTrack:function(e,t,n,i){var o=this;i.childNodes[0].addEventListener("loadedmetadata",function(){var e=o,t=$(i).parent().parent().parent();if(i.childNodes[0].audioTracks&&i.childNodes[0].audioTracks.length>1){m.init(t[0],e);var n=$('<a class="switch-btn"><i class="fa fa-angle-double-left"></i></a>');n.attr("title","切换成8音轨预览模式".l("player.switchToEightTrackViewMode")),n.click(function(){n.children(".fa").hasClass("fa-angle-double-left")?($(t).children(".mejs__multi-volumn-track").addClass("show-others"),n.children(".fa").removeClass("fa-angle-double-left"),n.children(".fa").addClass("fa-angle-double-right"),n.attr("title","切换成1、2音轨预览模式".l("player.switchToOneAndTwoTrackViewMode"))):($(t).children(".mejs__multi-volumn-track").removeClass("show-others"),n.children(".fa").removeClass("fa-angle-double-right"),n.children(".fa").addClass("fa-angle-double-left"),n.attr("title","切换成8音轨预览模式".l("player.switchToEightTrackViewMode")))}),t.append(n)}else d.init(t[0],e),e.volumnTrack=d,$(window).resize(function(){d.init(t[0],e)})})},cleanvolumnTrack:function(e,n,i,o){t.volumnTrack.clean()}})},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=n(4).default,r=function(){function e(){i(this,e)}return o(e,[{key:"init",value:function(e,t){this.mediaElementPlayer=t,this.initDom(e)}},{key:"initDom",value:function(e){$(e).children(".mejs__multi-volumn-track")&&$(e).children(".mejs__multi-volumn-track").remove();var t=document.createElement("DIV");t.className="mejs__multi-volumn-track",e.appendChild(t);var n=document.createElement("DIV");n.className="mejs__multi-volumn-track-inner",t.appendChild(n);for(var i=void 0,o=0;o<8;o++)o+1!==1&&o+1!==5||(i=new c([o,o+1]),i.init(n,this.mediaElementPlayer));for(var a=0;a<8;a++)a+1!==3&&a+1!==7||(i=new c([a,a+1]),i.init(n,this.mediaElementPlayer))}}]),e}();t.default=r;var s=["0","2","4","6","8","10","12","15","20","24"],l=void 0,c=function(){function e(t){i(this,e),this.index=t,this.isDoubleChannel=!0}return o(e,[{key:"init",value:function(e,t){var n=this;this.mediaElementPlayer=t,this.player=t.media.childNodes[0],this.initDom(e),this.TICKS_COLOR="#86888a",this.AXIS_LINEWIDTH=.3,this.AXIS_COLOR="#86888a",this.drawAxes(),this.player.onplay=function(){n.meters&&n.meters.length>0&&_.forEach(n.meters,function(e){e.shutup()})},this.player.onpause=function(){n.meters&&n.meters.length>0&&_.forEach(n.meters,function(e){e.shutdown()})},this.player.onload=function(){console.log("var1, var2")},this.player.onended=function(){n.clean()},$(t.eightTrack).on("onChangeAudio",function(e,i){var o=t.eightTrack.audio_players[i];_.findIndex(n.index,function(e){return e===o.index})>-1&&n.initPlayer()})}},{key:"initDom",value:function(e){var t=document.createElement("DIV");t.className="mejs__sub-volumn-track",e.appendChild(t);var n=document.createElement("div");n.className="mejs__sub-volumn-track_top",n.style.width=t.clientWidth+"px",n.innerHTML="<span>"+(this.index[0]+1)+"</span><span>"+(this.index[1]+1)+"</span>",t.appendChild(n),this.canvas=document.createElement("CANVAS"),this.canvas.width=t.clientWidth,l=l||t.offsetHeight,this.canvas.height=l-n.offsetHeight,t.appendChild(this.canvas),this.ctx=this.canvas.getContext("2d")}},{key:"initPlayer",value:function(){var e=this;if(!e.meters||2!==e.meters.length){var t=this.getCurrentAudioPlayers();t&&t.length>0&&_.forEach(t,function(t){var n=new a(e);t.audioMeter=n.createAudioMeter(t.audioContext);var i=t.audioMeter;t.channelMerger.connect(i),i.shutup(),e.meters||(e.meters=[]),e.meters.push(i)})}}},{key:"getCurrentAudioPlayers",value:function(){if(this.mediaElementPlayer.eightTrack){var e=this,t=Object.keys(this.mediaElementPlayer.eightTrack.audio_players).sort(),n=[];return t.forEach(function(t){_.findIndex(e.index,function(n){return n===e.mediaElementPlayer.eightTrack.audio_players[t].index})>-1&&n.push(e.mediaElementPlayer.eightTrack.audio_players[t])}),n}}},{key:"drawAxes",value:function(){this.ctx.save(),this.ctx.strokeStyle=this.AXIS_COLOR,this.ctx.lineWidth=this.AXIS_LINEWIDTH,this.ctx.strokeStyle=this.TICKS_COLOR,this.drawVerticalAxisTicks(),this.ctx.restore()}},{key:"drawVerticalAxisTicks",value:function(){var e=this;s.forEach(function(t,n,i){e.ctx.beginPath(),e.ctx.fillStyle=e.AXIS_COLOR,e.ctx.textAlign="center","0"===t?(e.ctx.textBaseline="top",e.ctx.fillText(t,e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height+4,e.canvas.width)):n===i.length-1?(e.ctx.textBaseline="bottom",e.ctx.fillText("-"+(n+1===i.length?"∞":t),e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height,e.canvas.width)):(e.ctx.textBaseline="middle",e.ctx.fillText("-"+(n+1===i.length?"∞":t),e.canvas.width/2,(n+1!==i.length?e.getVolume(t):1)*e.canvas.height,e.canvas.width)),e.ctx.stroke()})}},{key:"getVolume",value:function(e){e=Number(e);var t=Math.pow(10,-e/20);return 1-(t<=1e-6?0:t)}},{key:"render",value:function(e,t){this.ctx.strokeStyle="#00d0ff",this.ctx.lineWidth=2,e>0&&this.ctx.clearRect(2,0,5,this.canvas.height),t>0&&this.ctx.clearRect(this.canvas.width-7,0,5,this.canvas.height);var n=this.ctx.createLinearGradient(0,this.canvas.height,0,0);n.addColorStop(0,"#00EC00"),n.addColorStop(.7,"#FFFF37"),n.addColorStop(1,"#FF0000");var i=this.ctx.createLinearGradient(0,this.canvas.height,0,0);if(n.addColorStop(0,"#00EC00"),i.addColorStop(.7,"#FFFF37"),i.addColorStop(1,"#FF0000"),e>0){var o=20*Math.log10(e);o=o<-100?-100:o,o=e*this.canvas.height,this.ctx.fillStyle=n,this.ctx.fillRect(2,this.canvas.height,5,1-o)}if(t>0){var a=20*Math.log10(t);a=a<-100?-100:a,a=t*this.canvas.height,this.ctx.fillStyle=n,this.ctx.fillRect(this.canvas.width-7,this.canvas.height,5,1-a)}}}]),e}()},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{setSectionText:"创建片段".l("player.createSection")}),Object.assign(MediaElementPlayer.prototype,{setSection:function(){if(0===this.trimout)return!1;this.trimin>0&&(this.currentTime=this.trimin);var e=this.getCurrentKeyframe();this.triminKeyframe&&(e=this.triminKeyframe),this.options.setSection&&this.options.setSection(this,e,this.trimin,this.trimout),this.unlockInPlayRange()},buildsetSection:function(e,t,n,i){var o=this;o.addButton("setSection",function(){i.seeking||(o.setSection(),o.isFullScreen&&o.exitFullScreen())})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{exportSectionText:"片段导出".l("player.exportSection")}),Object.assign(MediaElementPlayer.prototype,{exportSection:function(){if(0===this.trimout)return!1;this.trimin>0&&(this.currentTime=this.trimin);var e=this.getCurrentKeyframe();this.triminKeyframe&&(e=this.triminKeyframe),this.options.exportSection&&this.options.exportSection(this,e,this.trimin,this.trimout,this.duration)},buildexportSection:function(e,t,n,i){var o=this;o.addButton("exportSection",function(){i.seeking||(o.exportSection(),o.isFullScreen&&o.exitFullScreen())})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{saveSectionText:"片段另存为".l("player.saveSection")}),Object.assign(MediaElementPlayer.prototype,{saveSection:function(){if(0===this.trimout)return!1;this.trimin>0&&(this.currentTime=this.trimin);var e=this.getCurrentKeyframe();this.triminKeyframe&&(e=this.triminKeyframe),this.options.saveSection&&this.options.saveSection(this,e,this.trimin,this.trimout)},buildsaveSection:function(e,t,n,i){var o=this;o.addButton("saveSection",function(){i.seeking||(o.saveSection(),o.isFullScreen&&o.exitFullScreen())})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{setCatalogText:"创建编目".l("player.createCatalog")}),
Object.assign(MediaElementPlayer.prototype,{setCatalog:function(){if(0===this.trimout)return!1;this.trimin>0&&(this.currentTime=this.trimin);var e=this.getCurrentKeyframe();this.triminKeyframe&&(e=this.triminKeyframe),this.options.setCatalog&&this.options.setCatalog(this,e,this.trimin,this.trimout)},buildsetCatalog:function(e,t,n,i){var o=this;o.addButton("setCatalog",function(){i.seeking||(o.setCatalog(),o.isFullScreen&&o.exitFullScreen())})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{selectScreenText:"选取画面".l("player.selectScreen")});var i,o,a={x:0,y:0},r=!1;Object.assign(MediaElementPlayer.prototype,{getSelectScreenState:function(){return null!=i&&"none"!=i.css("display")},selectScreen:function(e){var t=$.extend({},{callback:null},e),n=this;n.pause();var s=$(n.container);null==i&&(i=$('<div id="draw-box"></div>'),s.find(".mejs__mediaelement").append(i),i.on("mousedown",function(e){null!=o&&o.remove(),a.x=e.pageX-i.offset().left,a.y=e.pageY-i.offset().top,o=$('<div class="rect"></div>'),o.css({left:a.x,top:a.y}),i.append(o),r=!0}),i.on("mousemove",function(e){if(r&&null!=o){var t=e.pageX-i.offset().left,n=e.pageY-i.offset().top;t>=a.x?o.css({width:t-a.x}):o.css({left:t,width:a.x-t}),n>=a.y?o.css({height:n-a.y}):o.css({top:n,height:a.y-n})}}),i.on("mouseup",function(){if(r=!1,null!=o&&o.width()>0&&o.height()>0){var e=n.node,i=document.createElement("canvas");i.width=e.clientWidth,i.height=e.clientHeight,i.crossOrigin=e.crossOrigin="anonymous",i.crossOrigin=e.crossOrigin="*";i.getContext("2d").drawImage(e,0,0,e.videoWidth,e.videoHeight,0,0,i.width,i.height);var a=new Image;a.src=i.toDataURL("image/png"),a.onload=function(){var e=document.createElement("canvas");e.width=o.width(),e.height=o.height(),e.getContext("2d").drawImage(a,parseInt(o.css("left")),parseInt(o.css("top")),e.width,e.height,0,0,e.width,e.height),null!=t.callback&&t.callback({image:e,base64:e.toDataURL("image/png"),rect:o,params:t})}}})),setTimeout(function(){s.find(".mejs__overlay-play").hide(),s.find(".mejs__mediaelement").css("z-index",2)},10),i.show()},exitSelectScreen:function(){var e=this;null!=i&&(i.hide(),null!=o&&(o.remove(),i=null),$(e.container).find(".mejs__overlay-play").show(),e.pause())},buildselectScreen:function(e,t,n,o){var a=this;a.addButton("selectScreen",function(){i&&"none"!=i.css("display")?a.exitSelectScreen():a.selectScreen()})}})},function(e,t,n){"use strict";Object.assign(mejs.MepDefaults,{downloadText:"下载".l("player.download")}),Object.assign(MediaElementPlayer.prototype,{download:function(){var e=this.getCurrentKeyframe();this.triminKeyframe&&(e=this.triminKeyframe),this.options.download&&this.options.download(this,e)},builddownload:function(e,t,n,i){var o=this;o.addButton("download",function(){o.download(),o.isFullScreen&&o.exitFullScreen()})}})},function(e,t,n){var i=n(35);"string"==typeof i&&(i=[[e.i,i,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(7)(i,o);i.locals&&(e.exports=i.locals)},function(e,t,n){var i=n(5);t=e.exports=n(6)(!1),t.push([e.i,"/* Accessibility: hide screen reader texts (and prefer \"top\" for RTL languages).\r\nReference: http://blog.rrwd.nl/2015/04/04/the-screen-reader-text-class-why-and-how/ */\n.mejs__offscreen {\n  border: 0;\n  clip: rect(1px, 1px, 1px, 1px);\n  -webkit-clip-path: inset(50%);\n  clip-path: inset(50%);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n  word-wrap: normal; }\n\n.mejs__container {\n  background: #000;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  font-family: 'Helvetica', Arial, serif;\n  position: relative;\n  text-align: left;\n  text-indent: 0;\n  vertical-align: top; }\n\n.mejs__container * {\n  -moz-box-sizing: border-box;\n  box-sizing: border-box; }\n\n/* Hide native play button and control bar from iOS to favor plugin button */\n.mejs__container video::-webkit-media-controls,\n.mejs__container video::-webkit-media-controls-panel,\n.mejs__container video::-webkit-media-controls-panel-container,\n.mejs__container video::-webkit-media-controls-start-playback-button {\n  -webkit-appearance: none;\n  display: none !important; }\n\n.mejs__fill-container,\n.mejs__fill-container .mejs__container {\n  height: 100%;\n  width: 100%; }\n\n.mejs__fill-container {\n  background: transparent;\n  margin: 0 auto;\n  overflow: hidden;\n  position: relative; }\n\n.mejs__container:focus {\n  outline: none; }\n\n.mejs__iframe-overlay {\n  height: 100%;\n  position: absolute;\n  width: 100%; }\n\n.mejs__embed,\n.mejs__embed body {\n  background: #000;\n  height: 100%;\n  margin: 0;\n  overflow: hidden;\n  padding: 0;\n  width: 100%; }\n\n.mejs__fullscreen {\n  overflow: hidden !important; }\n\n.mejs__container-fullscreen {\n  bottom: 0;\n  left: 0;\n  overflow: hidden;\n  position: fixed;\n  right: 0;\n  top: 0;\n  z-index: 1000; }\n\n.mejs__container-fullscreen .mejs__mediaelement,\n.mejs__container-fullscreen video {\n  height: 100% !important;\n  width: 100% !important; }\n\n/* Start: LAYERS */\n.mejs__background {\n  left: 0;\n  position: absolute;\n  top: 0; }\n\n.mejs__mediaelement {\n  height: 100%;\n  left: 0;\n  position: absolute;\n  top: 0;\n  width: 100%;\n  z-index: 0; }\n\n.mejs__poster {\n  background-position: 50% 50%;\n  background-repeat: no-repeat;\n  background-size: cover;\n  left: 0;\n  position: absolute;\n  top: 0;\n  z-index: 1; }\n\n:root .mejs__poster-img {\n  display: none; }\n\n.mejs__poster-img {\n  border: 0;\n  padding: 0; }\n\n.mejs__overlay {\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  left: 0;\n  position: absolute;\n  top: 0; }\n\n.mejs__layer {\n  z-index: 1; }\n\n.mejs__overlay-play {\n  cursor: pointer; }\n\n.mejs__overlay-button {\n  background: url("+i(n(3))+") no-repeat;\n  background-position: 0 -39px;\n  height: 80px;\n  width: 80px;\n  opacity: 0.2;\n  -webkit-transition: opacity 200ms;\n  transition: opacity 200ms; }\n\n.mejs__overlay:hover > .mejs__overlay-button {\n  /*background-position: -80px -39px;*/\n  opacity: 0.8; }\n\n.mejs__overlay-loading {\n  height: 80px;\n  width: 80px; }\n\n.mejs__overlay-loading-bg-img {\n  -webkit-animation: mejs__loading-spinner 1s linear infinite;\n  animation: mejs__loading-spinner 1s linear infinite;\n  background: transparent url("+i(n(3))+") -160px -40px no-repeat;\n  display: block;\n  height: 80px;\n  width: 80px;\n  z-index: 1; }\n\n@-webkit-keyframes mejs__loading-spinner {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg); } }\n\n@keyframes mejs__loading-spinner {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg); } }\n\n/* End: LAYERS */\n/* Start: CONTROL BAR */\n.mejs__controls {\n  bottom: 0;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  height: 40px;\n  left: 0;\n  list-style-type: none;\n  margin: 0;\n  padding: 0 10px;\n  position: absolute;\n  width: 100%;\n  z-index: 3; }\n\n.mejs__controls:not([style*='display: none']) {\n  background: rgba(255, 0, 0, 0.7);\n  background: -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0.35));\n  background: -webkit-gradient(linear, left top, left bottom, from(transparent), to(rgba(0, 0, 0, 0.35)));\n  background: linear-gradient(transparent, rgba(0, 0, 0, 0.35)); }\n\n.mejs__button,\n.mejs__time,\n.mejs__time-rail {\n  font-size: 10px;\n  height: 40px;\n  line-height: 10px;\n  margin: 0;\n  width: 32px; }\n\n.mejs__button > button {\n  background: transparent url("+i(n(3))+");\n  border: 0;\n  cursor: pointer;\n  display: block;\n  font-size: 0;\n  height: 20px;\n  line-height: 0;\n  margin: 10px 6px;\n  overflow: hidden;\n  padding: 0;\n  text-decoration: none;\n  width: 20px; }\n\n/* :focus for accessibility */\n.mejs__button > button:focus {\n  outline: dotted 1px #999; }\n\n.mejs__container-keyboard-inactive a,\n.mejs__container-keyboard-inactive a:focus,\n.mejs__container-keyboard-inactive button,\n.mejs__container-keyboard-inactive button:focus,\n.mejs__container-keyboard-inactive [role=slider],\n.mejs__container-keyboard-inactive [role=slider]:focus {\n  outline: 0; }\n\n/* End: CONTROL BAR */\n/* Start: Time (Current / Duration) */\n.mejs__time {\n  -moz-box-sizing: content-box;\n  box-sizing: content-box;\n  color: #fff;\n  font-size: 11px;\n  font-weight: bold;\n  height: 24px;\n  overflow: hidden;\n  padding: 16px 6px 0;\n  text-align: center;\n  width: auto; }\n\n/* End: Time (Current / Duration) */\n/* Start: Play/Pause/Stop */\n.mejs__play > button {\n  background-position: 0 0; }\n\n.mejs__pause > button {\n  background-position: -20px 0; }\n\n.mejs__replay > button {\n  background-position: -160px 0; }\n\n/* End: Play/Pause/Stop */\n/* Start: Progress Bar */\n.mejs__time-rail {\n  direction: ltr;\n  -webkit-box-flex: 1;\n  -webkit-flex-grow: 1;\n  -ms-flex-positive: 1;\n  flex-grow: 1;\n  height: 40px;\n  margin: 0 10px;\n  padding-top: 10px;\n  position: relative; }\n\n.mejs__time-total,\n.mejs__time-buffering,\n.mejs__time-loaded,\n.mejs__time-current,\n.mejs__time-float,\n.mejs__time-hovered,\n.mejs__time-float-current,\n.mejs__time-float-corner,\n.mejs__time-marker {\n  border-radius: 2px;\n  cursor: pointer;\n  display: block;\n  height: 10px;\n  position: absolute; }\n\n.mejs__time-total {\n  background: rgba(255, 255, 255, 0.3);\n  margin: 5px 0 0;\n  width: 100%; }\n\n.mejs__time-buffering {\n  -webkit-animation: buffering-stripes 2s linear infinite;\n  animation: buffering-stripes 2s linear infinite;\n  background: -webkit-linear-gradient(135deg, rgba(255, 255, 255, 0.4) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.4) 75%, transparent 75%, transparent);\n  background: linear-gradient(-45deg, rgba(255, 255, 255, 0.4) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.4) 75%, transparent 75%, transparent);\n  background-size: 15px 15px;\n  width: 100%; }\n\n@-webkit-keyframes buffering-stripes {\n  from {\n    background-position: 0 0; }\n  to {\n    background-position: 30px 0; } }\n\n@keyframes buffering-stripes {\n  from {\n    background-position: 0 0; }\n  to {\n    background-position: 30px 0; } }\n\n.mejs__time-loaded {\n  background: rgba(255, 255, 255, 0.3); }\n\n.mejs__time-current,\n.mejs__time-handle-content {\n  background: rgba(255, 255, 255, 0.9); }\n\n.mejs__time-hovered {\n  background: rgba(255, 255, 255, 0.5);\n  z-index: 10; }\n\n.mejs__time-hovered.negative {\n  background: rgba(0, 0, 0, 0.2); }\n\n.mejs__time-current,\n.mejs__time-buffering,\n.mejs__time-loaded,\n.mejs__time-hovered {\n  left: 0;\n  -webkit-transform: scaleX(0);\n  transform: scaleX(0);\n  -webkit-transform-origin: 0 0;\n  transform-origin: 0 0;\n  -webkit-transition: 0.15s ease-in all;\n  transition: 0.15s ease-in all;\n  width: 100%; }\n\n.mejs__time-buffering {\n  -webkit-transform: scaleX(1);\n  transform: scaleX(1); }\n\n.mejs__time-hovered {\n  -webkit-transition: height 0.1s cubic-bezier(0.44, 0, 1, 1);\n  transition: height 0.1s cubic-bezier(0.44, 0, 1, 1); }\n\n.mejs__time-hovered.no-hover {\n  -webkit-transform: scaleX(0) !important;\n  transform: scaleX(0) !important; }\n\n.mejs__time-handle,\n.mejs__time-handle-content {\n  border: 4px solid transparent;\n  cursor: pointer;\n  left: 0;\n  position: absolute;\n  -webkit-transform: translateX(0);\n  transform: translateX(0);\n  z-index: 11; }\n\n.mejs__time-handle-content {\n  border: 4px solid rgba(255, 255, 255, 0.9);\n  border-radius: 50%;\n  height: 10px;\n  left: -7px;\n  top: -4px;\n  -webkit-transform: scale(0);\n  transform: scale(0);\n  width: 10px; }\n\n.mejs__time-rail:hover .mejs__time-handle-content,\n.mejs__time-rail .mejs__time-handle-content:focus,\n.mejs__time-rail .mejs__time-handle-content:active {\n  -webkit-transform: scale(1);\n  transform: scale(1); }\n\n.mejs__time-float {\n  background: #eee;\n  border: solid 1px #333;\n  bottom: 100%;\n  color: #111;\n  display: none;\n  height: 17px;\n  margin-bottom: 9px;\n  position: absolute;\n  text-align: center;\n  -webkit-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 36px; }\n\n.mejs__time-float-current {\n  display: block;\n  left: 0;\n  margin: 2px;\n  text-align: center;\n  width: 30px; }\n\n.mejs__time-float-corner {\n  border: solid 5px #eee;\n  border-color: #eee transparent transparent;\n  border-radius: 0;\n  display: block;\n  height: 0;\n  left: 50%;\n  line-height: 0;\n  position: absolute;\n  top: 100%;\n  -webkit-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 0; }\n\n.mejs__long-video .mejs__time-float {\n  margin-left: -23px;\n  width: 64px; }\n\n.mejs__long-video .mejs__time-float-current {\n  width: 60px; }\n\n.mejs__broadcast {\n  color: #fff;\n  height: 10px;\n  position: absolute;\n  top: 15px;\n  width: 100%; }\n\n/* End: Progress Bar */\n/* Start: Fullscreen */\n.mejs__fullscreen-button > button {\n  background-position: -80px 0; }\n\n.mejs__unfullscreen > button {\n  background-position: -100px 0; }\n\n/* End: Fullscreen */\n/* Start: Mute/Volume */\n.mejs__mute > button {\n  background-position: -60px 0; }\n\n.mejs__unmute > button {\n  background-position: -40px 0; }\n\n.mejs__volume-button {\n  position: relative; }\n\n.mejs__volume-button > .mejs__volume-slider {\n  -webkit-backface-visibility: hidden;\n  background: rgba(50, 50, 50, 0.7);\n  border-radius: 0;\n  bottom: 100%;\n  display: none;\n  height: 115px;\n  left: 50%;\n  margin: 0;\n  position: absolute;\n  -webkit-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 25px;\n  z-index: 1; }\n\n.mejs__volume-button:hover {\n  border-radius: 0 0 4px 4px; }\n\n.mejs__volume-total {\n  background: rgba(255, 255, 255, 0.5);\n  height: 100px;\n  left: 50%;\n  margin: 0;\n  position: absolute;\n  top: 8px;\n  -webkit-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 2px; }\n\n.mejs__volume-current {\n  background: rgba(255, 255, 255, 0.9);\n  left: 0;\n  margin: 0;\n  position: absolute;\n  width: 100%; }\n\n.mejs__volume-handle {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 1px;\n  cursor: ns-resize;\n  height: 6px;\n  left: 50%;\n  position: absolute;\n  -webkit-transform: translateX(-50%);\n  transform: translateX(-50%);\n  width: 16px; }\n\n.mejs__horizontal-volume-slider {\n  display: block;\n  height: 36px;\n  position: relative;\n  vertical-align: middle;\n  width: 56px; }\n\n.mejs__horizontal-volume-total {\n  background: rgba(50, 50, 50, 0.8);\n  border-radius: 2px;\n  font-size: 1px;\n  height: 8px;\n  left: 0;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  top: 16px;\n  width: 50px; }\n\n.mejs__horizontal-volume-current {\n  background: rgba(255, 255, 255, 0.8);\n  border-radius: 2px;\n  font-size: 1px;\n  height: 100%;\n  left: 0;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  top: 0;\n  width: 100%; }\n\n.mejs__horizontal-volume-handle {\n  display: none; }\n\n/* End: Mute/Volume */\n/* Start: Track (Captions and Chapters) */\n.mejs__captions-button,\n.mejs__chapters-button {\n  position: relative; }\n\n.mejs__captions-button > button {\n  background-position: -140px 0; }\n\n.mejs__chapters-button > button {\n  background-position: -180px 0; }\n\n.mejs__captions-button > .mejs__captions-selector,\n.mejs__chapters-button > .mejs__chapters-selector {\n  background: rgba(50, 50, 50, 0.7);\n  border: solid 1px transparent;\n  border-radius: 0;\n  bottom: 100%;\n  margin-right: -43px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  right: 50%;\n  visibility: visible;\n  width: 86px; }\n\n.mejs__chapters-button > .mejs__chapters-selector {\n  margin-right: -55px;\n  width: 110px; }\n\n.mejs__captions-selector-list,\n.mejs__chapters-selector-list {\n  list-style-type: none !important;\n  margin: 0;\n  overflow: hidden;\n  padding: 0; }\n\n.mejs__captions-selector-list-item,\n.mejs__chapters-selector-list-item {\n  color: #fff;\n  cursor: pointer;\n  display: block;\n  list-style-type: none !important;\n  margin: 0 0 6px;\n  overflow: hidden;\n  padding: 0; }\n\n.mejs__captions-selector-list-item:hover,\n.mejs__chapters-selector-list-item:hover {\n  background-color: #c8c8c8 !important;\n  background-color: rgba(255, 255, 255, 0.4) !important; }\n\n.mejs__captions-selector-input,\n.mejs__chapters-selector-input {\n  clear: both;\n  float: left;\n  left: -1000px;\n  margin: 3px 3px 0 5px;\n  position: absolute; }\n\n.mejs__captions-selector-label,\n.mejs__chapters-selector-label {\n  cursor: pointer;\n  float: left;\n  font-size: 10px;\n  line-height: 15px;\n  padding: 4px 10px 0;\n  width: 100%; }\n\n.mejs__captions-selected,\n.mejs__chapters-selected {\n  color: #21f8f8; }\n\n.mejs__captions-translations {\n  font-size: 10px;\n  margin: 0 0 5px; }\n\n.mejs__captions-layer {\n  bottom: 0;\n  color: #fff;\n  font-size: 16px;\n  left: 0;\n  line-height: 20px;\n  position: absolute;\n  text-align: center; }\n\n.mejs__captions-layer a {\n  color: #fff;\n  text-decoration: underline; }\n\n.mejs__captions-layer[lang=ar] {\n  font-size: 20px;\n  font-weight: normal; }\n\n.mejs__captions-position {\n  bottom: 15px;\n  left: 0;\n  position: absolute;\n  width: 100%; }\n\n.mejs__captions-position-hover {\n  bottom: 35px; }\n\n.mejs__captions-text,\n.mejs__captions-text * {\n  background: rgba(20, 20, 20, 0.5);\n  box-shadow: 5px 0 0 rgba(20, 20, 20, 0.5), -5px 0 0 rgba(20, 20, 20, 0.5);\n  padding: 0;\n  white-space: pre-wrap; }\n\n.mejs__container.mejs__hide-cues video::-webkit-media-text-track-container {\n  display: none; }\n\n/* End: Track (Captions and Chapters) */\n/* Start: Error */\n.mejs__overlay-error {\n  position: relative; }\n\n.mejs__overlay-error > img {\n  left: 0;\n  max-width: 100%;\n  position: absolute;\n  top: 0;\n  z-index: -1; }\n\n.mejs__cannotplay,\n.mejs__cannotplay a {\n  color: #fff;\n  font-size: 0.8em; }\n\n.mejs__cannotplay {\n  position: relative; }\n\n.mejs__cannotplay p,\n.mejs__cannotplay a {\n  display: inline-block;\n  padding: 0 15px;\n  width: 100%; }\n\n/* End: Error */\n",""])},function(e,t,n){"use strict";e.exports=function(e){var t="undefined"!=typeof window&&window.location;if(!t)throw new Error("fixUrls requires window.location");if(!e||"string"!=typeof e)return e;var n=t.protocol+"//"+t.host,i=n+t.pathname.replace(/\/[^\/]*$/,"/");return e.replace(/url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,function(e,t){var o=t.trim().replace(/^"(.*)"$/,function(e,t){return t}).replace(/^'(.*)'$/,function(e,t){return t});if(/^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(o))return e;var a;return a=0===o.indexOf("//")?o:0===o.indexOf("/")?n+o:i+o.replace(/^\.\//,""),"url("+JSON.stringify(a)+")"})}},function(e,t,n){var i=n(38);"string"==typeof i&&(i=[[e.i,i,""]]);var o={hmr:!0};o.transform=void 0,o.insertInto=void 0;n(7)(i,o);i.locals&&(e.exports=i.locals)},function(e,t,n){var i=n(5);t=e.exports=n(6)(!1),
t.push([e.i,".mejs__container {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: row;\n  -ms-flex-direction: row;\n  flex-direction: row;\n  height: initial !important;\n  width: unset !important;\n  min-width: unset !important;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1;\n  -ms-flex: 1;\n  flex: 1; }\n\n.mejs__overlay-error a, .mejs__overlay-error p {\n  color: #000; }\n\n.mejs__inner {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n  -ms-flex: 1 0 auto;\n  flex: 1 0 auto;\n  width: 1%;\n  position: relative;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n  -webkit-flex-direction: column;\n  -ms-flex-direction: column;\n  flex-direction: column; }\n\n.mejs__volumn-track {\n  width: 38px;\n  height: 100%;\n  border-left: 1px solid #3c3e40; }\n  .mejs__volumn-track canvas {\n    -webkit-transform: none;\n    transform: none;\n    background: #000; }\n  .mejs__volumn-track .mejs__volumn-track_top {\n    background: #000;\n    color: #86888a;\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-pack: justify;\n    -webkit-justify-content: space-between;\n    -ms-flex-pack: justify;\n    justify-content: space-between;\n    padding: 0 3px;\n    border-bottom: 1px solid #3c3e40;\n    font-size: 12px; }\n\n.mejs__multi-volumn-track {\n  width: 38px;\n  height: 100%;\n  overflow: hidden; }\n  .mejs__multi-volumn-track.show-others {\n    width: 76px; }\n  .mejs__multi-volumn-track .mejs__multi-volumn-track-inner {\n    width: 76px;\n    height: 100%; }\n  .mejs__multi-volumn-track:hover ~ .switch-btn {\n    right: 2px;\n    opacity: 1; }\n  .mejs__multi-volumn-track .mejs__sub-volumn-track {\n    width: 38px;\n    height: 50%;\n    border-left: 1px solid #3c3e40;\n    float: left; }\n    .mejs__multi-volumn-track .mejs__sub-volumn-track canvas {\n      -webkit-transform: none;\n      transform: none;\n      background: #000; }\n    .mejs__multi-volumn-track .mejs__sub-volumn-track .mejs__sub-volumn-track_top {\n      background: #000;\n      color: #86888a;\n      display: -webkit-box;\n      display: -webkit-flex;\n      display: -ms-flexbox;\n      display: flex;\n      -webkit-box-pack: justify;\n      -webkit-justify-content: space-between;\n      -ms-flex-pack: justify;\n      justify-content: space-between;\n      padding: 0 3px;\n      border-bottom: 1px solid #3c3e40;\n      border-top: 1px solid #3c3e40;\n      font-size: 12px; }\n      .mejs__multi-volumn-track .mejs__sub-volumn-track .mejs__sub-volumn-track_top .track-num {\n        color: #00A8FF; }\n\n.switch-btn {\n  position: absolute;\n  right: -13px;\n  bottom: 2px;\n  z-index: 3;\n  -webkit-transition: all 200ms;\n  transition: all 200ms;\n  cursor: pointer;\n  opacity: 0; }\n  .switch-btn:hover {\n    right: 2px;\n    opacity: 1; }\n  .switch-btn .fa {\n    color: #FFFFFF;\n    font-size: 24px; }\n\n.mejs__container-fullscreen {\n  height: 100vh !important; }\n  .mejs__container-fullscreen .mejs__mediaelement {\n    height: -webkit-calc(100vh - 58px) !important;\n    height: calc(100vh - 58px) !important; }\n  .mejs__container-fullscreen .mejs__inner .mejs__controls {\n    height: 10px !important;\n    min-height: 10px !important; }\n    .mejs__container-fullscreen .mejs__inner .mejs__controls:hover {\n      height: 58px !important;\n      min-height: 58px !important; }\n      .mejs__container-fullscreen .mejs__inner .mejs__controls:hover div {\n        opacity: 1; }\n    .mejs__container-fullscreen .mejs__inner .mejs__controls div {\n      opacity: 0; }\n\n.mejs__mediaelement {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  position: relative;\n  left: unset;\n  top: unset;\n  -webkit-box-flex: 1;\n  -webkit-flex: 1 0 auto;\n  -ms-flex: 1 0 auto;\n  flex: 1 0 auto;\n  width: unset;\n  height: 1%; }\n  .mejs__mediaelement mediaelementwrapper {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-flex: 1;\n    -webkit-flex: 1 0 auto;\n    -ms-flex: 1 0 auto;\n    flex: 1 0 auto;\n    width: 1%;\n    -webkit-box-align: center;\n    -webkit-align-items: center;\n    -ms-flex-align: center;\n    align-items: center;\n    -webkit-box-pack: center;\n    -webkit-justify-content: center;\n    -ms-flex-pack: center;\n    justify-content: center;\n    position: relative; }\n    .mejs__mediaelement mediaelementwrapper video {\n      width: 100% !important;\n      height: 100% !important;\n      max-width: 100%;\n      max-height: 100%;\n      position: absolute;\n      left: 50%;\n      top: 50%;\n      -webkit-transform: translateX(-50%) translateY(-50%);\n      transform: translateX(-50%) translateY(-50%); }\n  .mejs__mediaelement #draw-box {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    display: none;\n    cursor: crosshair; }\n    .mejs__mediaelement #draw-box .rect {\n      position: absolute;\n      border: 1px solid #1292fa;\n      background: rgba(255, 255, 255, 0.3); }\n\n@-webkit-keyframes locate-player-area-ani {\n  0% {\n    -webkit-transform: scale(3);\n    transform: scale(3); }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1); } }\n\n@keyframes locate-player-area-ani {\n  0% {\n    -webkit-transform: scale(3);\n    transform: scale(3); }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1); } }\n  .mejs__mediaelement .locate-player-area {\n    position: absolute;\n    border: 1px solid #1890FF;\n    background: rgba(24, 144, 255, 0.1);\n    -webkit-animation: locate-player-area-ani 500ms;\n    animation: locate-player-area-ani 500ms;\n    -webkit-transition: border 400ms,background 400ms;\n    transition: border 400ms,background 400ms; }\n\n@-webkit-keyframes locate-player-area-active-ani {\n  0% {\n    -webkit-transform: scale(1);\n    transform: scale(1); }\n  50% {\n    -webkit-transform: scale(1.18);\n    transform: scale(1.18); }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1); } }\n\n@keyframes locate-player-area-active-ani {\n  0% {\n    -webkit-transform: scale(1);\n    transform: scale(1); }\n  50% {\n    -webkit-transform: scale(1.18);\n    transform: scale(1.18); }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1); } }\n    .mejs__mediaelement .locate-player-area.active {\n      border: 2px solid #1890FF !important;\n      background: rgba(24, 144, 255, 0.4) !important;\n      -webkit-animation: locate-player-area-active-ani 300ms !important;\n      animation: locate-player-area-active-ani 300ms !important;\n      opacity: 1 !important; }\n    .mejs__mediaelement .locate-player-area .title {\n      position: absolute;\n      left: -1px;\n      top: -19px;\n      font-size: 12px;\n      color: #d2d2d2;\n      background: rgba(0, 0, 0, 0.3);\n      white-space: nowrap; }\n  .mejs__mediaelement mediaelementwrapper {\n    overflow: hidden; }\n    .mejs__mediaelement mediaelementwrapper.is-focus-locate-player-area .locate-player-area {\n      border: 1px solid #a4a4a4;\n      background: rgba(180, 180, 180, 0.2);\n      -webkit-animation: none;\n      animation: none;\n      opacity: 0; }\n\n.mejs__layers .mejs__poster {\n  width: 100% !important;\n  height: 100% !important; }\n\n.mejs__time-container {\n  position: absolute;\n  top: 10px;\n  right: 10px;\n  color: #fff;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  height: 22px;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  z-index: 3;\n  font-size: 14px; }\n  .mejs__time-container .mejs__currenttime input {\n    background: transparent;\n    color: #fff;\n    border: none;\n    width: 76px;\n    text-shadow: 1px 1px black, -1px -1px black, 1px -1px black, -1px 1px black; }\n  .mejs__time-container .mejs__duration {\n    border-left: 1px solid #c3c3c3;\n    margin-left: 10px;\n    padding-left: 10px;\n    text-shadow: 1px 1px black, -1px -1px black, 1px -1px black, -1px 1px black; }\n  .mejs__time-container .mejs__tc {\n    border: 1px solid #fff;\n    color: #fff;\n    font-size: 12px;\n    border-radius: 3px;\n    margin-left: 10px;\n    padding: 2px 5px 0;\n    cursor: pointer;\n    position: relative;\n    width: 53px;\n    text-align: center; }\n    .mejs__time-container .mejs__tc:hover .dp-list {\n      -webkit-transform: scaleY(1);\n      transform: scaleY(1); }\n    .mejs__time-container .mejs__tc .dp-list {\n      -webkit-transform: scaleY(0);\n      transform: scaleY(0);\n      -webkit-transform-origin: top;\n      transform-origin: top;\n      -webkit-transition: -webkit-transform 200ms;\n      transition: -webkit-transform 200ms;\n      transition: transform 200ms;\n      transition: transform 200ms, -webkit-transform 200ms;\n      position: absolute;\n      left: -1px;\n      top: 21px;\n      background: rgba(50, 50, 50, 0.3); }\n      .mejs__time-container .mejs__tc .dp-list .dp-item {\n        width: 54px;\n        height: 24px;\n        line-height: 24px;\n        text-align: center;\n        color: #fff; }\n        .mejs__time-container .mejs__tc .dp-list .dp-item:hover {\n          background: rgba(255, 255, 255, 0.7); }\n        .mejs__time-container .mejs__tc .dp-list .dp-item.active {\n          color: #21f8f8; }\n\n.mejs__controls {\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  position: unset;\n  height: 58px;\n  background: #000 !important;\n  padding-top: 18px;\n  min-height: 58px; }\n  .mejs__controls .mejs__time-rail {\n    position: absolute;\n    left: 0;\n    bottom: 38px;\n    padding-top: 0;\n    height: 14px;\n    line-height: inherit;\n    width: -webkit-calc(100% - 20px);\n    width: calc(100% - 20px); }\n    .mejs__controls .mejs__time-rail .mejs__time-total {\n      margin: 0;\n      outline: none;\n      top: 4px; }\n    .mejs__controls .mejs__time-rail .mejs__time-total,\n    .mejs__controls .mejs__time-rail .mejs__time-current,\n    .mejs__controls .mejs__time-rail .mejs__time-loaded,\n    .mejs__controls .mejs__time-rail .mejs__time-buffering,\n    .mejs__controls .mejs__time-rail .mejs__time-hovered {\n      height: 8px; }\n    .mejs__controls .mejs__time-rail .mejs__time-handle-content {\n      width: 8px;\n      height: 8px; }\n    .mejs__controls .mejs__time-rail .mejs__time-float {\n      width: 70px;\n      line-height: 12px;\n      z-index: 9; }\n    .mejs__controls .mejs__time-rail .mejs__keypoint {\n      position: absolute;\n      top: 0;\n      z-index: 9999;\n      width: 6px;\n      height: 8px;\n      border-radius: 40%;\n      border: 1px solid white;\n      background-color: #3c5252; }\n    .mejs__controls .mejs__time-rail .mejs__keypoint-info {\n      position: absolute;\n      bottom: 16px;\n      z-index: 9999;\n      width: auto;\n      height: auto;\n      padding: 5px;\n      color: white;\n      background-color: #4b4b4b;\n      border-radius: 2px; }\n    .mejs__controls .mejs__time-rail .mejs__keypoint-info-triangle {\n      position: absolute;\n      bottom: 0;\n      z-index: 9999;\n      width: 0;\n      height: 0;\n      border-width: 8px 5px;\n      border-style: solid;\n      border-color: #4b4b4b transparent transparent; }\n  .mejs__controls .trim-range-container {\n    width: 100%;\n    position: relative;\n    height: 8px;\n    top: -4px;\n    margin-bottom: 2px; }\n    .mejs__controls .trim-range-container .trim-range-box {\n      position: relative;\n      width: 100%;\n      height: 6px;\n      -webkit-transition: all 0.2s;\n      transition: all 0.2s; }\n    .mejs__controls .trim-range-container .trim-range {\n      width: 100%;\n      background-color: #FDBC20;\n      height: 100%;\n      border-radius: 2px; }\n    .mejs__controls .trim-range-container .trim-range-handle {\n      position: absolute;\n      width: 4px;\n      height: 100%;\n      top: 0;\n      left: 0; }\n    .mejs__controls .trim-range-container .trim-range-right {\n      left: unset;\n      right: 0; }\n    .mejs__controls .trim-range-container .trim-range-text {\n      position: absolute;\n      left: 0;\n      top: -24px;\n      padding: 2px;\n      color: #fff;\n      border-radius: 4px;\n      width: 210px;\n      text-align: center;\n      background: rgba(0, 0, 0, 0.4); }\n      .mejs__controls .trim-range-container .trim-range-text .trim-in-input,\n      .mejs__controls .trim-range-container .trim-range-text .trim-out-input {\n        text-align: center;\n        display: inline-block;\n        width: 66px;\n        height: 100%;\n        background-color: transparent;\n        border: none; }\n      .mejs__controls .trim-range-container .trim-range-text span {\n        display: inline-block;\n        margin-left: 5px;\n        cursor: pointer; }\n      .mejs__controls .trim-range-container .trim-range-text .modify-trim {\n        color: #FDBC20; }\n      .mejs__controls .trim-range-container .trim-range-text .reset-trim {\n        color: #FDBC20; }\n  .mejs__controls .mejs__button {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex;\n    -webkit-box-align: center;\n    -webkit-align-items: center;\n    -ms-flex-align: center;\n    align-items: center;\n    -webkit-box-pack: center;\n    -webkit-justify-content: center;\n    -ms-flex-pack: center;\n    justify-content: center; }\n    .mejs__controls .mejs__button button {\n      margin: 0; }\n    .mejs__controls .mejs__button:hover {\n      cursor: pointer; }\n  .mejs__controls .mejs__skipback-button {\n    -webkit-box-ordinal-group: -9;\n    -webkit-order: -10;\n    -ms-flex-order: -10;\n    order: -10; }\n    .mejs__controls .mejs__skipback-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -198px 0; }\n    .mejs__controls .mejs__skipback-button:hover button {\n      background-position: -216px 0; }\n  .mejs__controls .mejs__backframe-button {\n    -webkit-box-ordinal-group: -8;\n    -webkit-order: -9;\n    -ms-flex-order: -9;\n    order: -9; }\n    .mejs__controls .mejs__backframe-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -414px 0; }\n    .mejs__controls .mejs__backframe-button:hover button {\n      background-position: -270px 0; }\n  .mejs__controls .mejs__playpause-button {\n    -webkit-box-ordinal-group: -7;\n    -webkit-order: -8;\n    -ms-flex-order: -8;\n    order: -8; }\n  .mejs__controls .mejs__play button {\n    background: transparent url("+i(n(0))+");\n    background-size: 250px;\n    background-position: -1px 0px; }\n  .mejs__controls .mejs__play:hover button {\n    background: transparent url("+i(n(1))+");\n    background-size: 250px;\n    background-position: -1px 0px; }\n  .mejs__controls .mejs__pause button {\n    background: transparent url("+i(n(0))+");\n    background-size: 250px;\n    background-position: -1px -19px; }\n  .mejs__controls .mejs__pause:hover button {\n    background: transparent url("+i(n(1))+");\n    background-size: 250px;\n    background-position: -1px -19px; }\n  .mejs__controls .mejs__prevframe-button {\n    -webkit-box-ordinal-group: -6;\n    -webkit-order: -7;\n    -ms-flex-order: -7;\n    order: -7; }\n    .mejs__controls .mejs__prevframe-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -54px 0; }\n    .mejs__controls .mejs__prevframe-button:hover button {\n      background-position: 0 0; }\n  .mejs__controls .mejs__skipforward-button {\n    -webkit-box-ordinal-group: -5;\n    -webkit-order: -6;\n    -ms-flex-order: -6;\n    order: -6; }\n    .mejs__controls .mejs__skipforward-button svg {\n      -webkit-transform: rotateY(180deg);\n      transform: rotateY(180deg); }\n    .mejs__controls .mejs__skipforward-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -234px 0; }\n    .mejs__controls .mejs__skipforward-button:hover button {\n      background-position: -180px 0; }\n  .mejs__controls .mejs__trimin-button {\n    -webkit-box-ordinal-group: -4;\n    -webkit-order: -5;\n    -ms-flex-order: -5;\n    order: -5; }\n    .mejs__controls .mejs__trimin-button svg {\n      -webkit-transform: rotateY(180deg);\n      transform: rotateY(180deg); }\n    .mejs__controls .mejs__trimin-button button {\n      height: 18px;\n      width: 22px;\n      background: transparent no-repeat;\n      background-position: -584px 0; }\n    .mejs__controls .mejs__trimin-button:hover button {\n      background-position: -496px 0; }\n  .mejs__controls .mejs__trimout-button {\n    -webkit-box-ordinal-group: -3;\n    -webkit-order: -4;\n    -ms-flex-order: -4;\n    order: -4; }\n    .mejs__controls .mejs__trimout-button button {\n      height: 18px;\n      width: 22px;\n      background: transparent no-repeat;\n      background-position: -452px 0; }\n    .mejs__controls .mejs__trimout-button:hover button {\n      background-position: -474px 0; }\n  .mejs__controls .mejs__cleantrim-button {\n    -webkit-box-ordinal-group: -2;\n    -webkit-order: -3;\n    -ms-flex-order: -3;\n    order: -3; }\n    .mejs__controls .mejs__cleantrim-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -252px 0; }\n    .mejs__controls .mejs__cleantrim-button:hover button {\n      background-position: -306px 0; }\n  .mejs__controls .mejs__getKeyframe-button {\n    position: relative; }\n    .mejs__controls .mejs__getKeyframe-button button {\n      height: 18px;\n      width: 22px;\n      background: transparent no-repeat;\n      background-position: -540px 0; }\n    .mejs__controls .mejs__getKeyframe-button:hover button {\n      background-position: -518px 0; }\n    .mejs__controls .mejs__getKeyframe-button .setCatalog-dropdown {\n      display: none;\n      position: absolute;\n      bottom: 40px;\n      background-color: #fff;\n      border: 1px solid #bdbdbd;\n      left: -40px; }\n      .mejs__controls .mejs__getKeyframe-button .setCatalog-dropdown a {\n        display: block;\n        text-align: center;\n        height: 30px;\n        line-height: 30px;\n        width: 104px;\n        -webkit-transition: all .2s ease 0s;\n        transition: all .2s ease 0s;\n        color: #868686; }\n        .mejs__controls .mejs__getKeyframe-button .setCatalog-dropdown a.active {\n          background: #F29402;\n          color: #fff; }\n        .mejs__controls .mejs__getKeyframe-button .setCatalog-dropdown a:hover {\n          background-color: #F29402;\n          color: #fff; }\n      .mejs__controls .mejs__getKeyframe-button .setCatalog-dropdown:after {\n        content: '';\n        position: absolute;\n        left: 50%;\n        margin-left: -3.5px;\n        bottom: -7px;\n        border-top: #fff solid 7px;\n        border-left: transparent solid 7px;\n        border-right: transparent solid 7px; }\n  .mejs__controls .mejs__setSection-button button {\n    height: 18px;\n    width: 22px;\n    background: transparent no-repeat;\n    background-position: -562px 0; }\n  .mejs__controls .mejs__setSection-button:hover button {\n    background-position: -606px 0; }\n  .mejs__controls .mejs__exportSection-button button {\n    height: 18px;\n    width: 23px;\n    background: transparent no-repeat;\n    background-position: -628px 0; }\n  .mejs__controls .mejs__exportSection-button:hover button {\n    background-position: -651px 0; }\n  .mejs__controls .mejs__saveSection-button button {\n    height: 18px;\n    width: 18px;\n    background: transparent no-repeat;\n    background-position: -18px 0; }\n  .mejs__controls .mejs__saveSection-button:hover button {\n    background-position: -36px 0; }\n  .mejs__controls .mejs__setCatalog-button button {\n    height: 18px;\n    width: 18px;\n    background: transparent no-repeat;\n    background-position: -72px -0px; }\n  .mejs__controls .mejs__setCatalog-button:hover button {\n    background-position: -90px -0px; }\n  .mejs__controls .mejs__download-button {\n    right: 122px;\n    position: absolute;\n    bottom: 0; }\n    .mejs__controls .mejs__download-button button {\n      height: 18px;\n      width: 18px;\n      background: url("+i(n(39))+") !important;\n      background-position: 0 0; }\n    .mejs__controls .mejs__download-button:hover button {\n      background: url("+i(n(40))+") !important;\n      background-position: 0 0; }\n  .mejs__controls .mejs__toggleKeyPoint-button button {\n    background: transparent no-repeat url("+i(n(0))+") !important;\n    background-size: 250px !important;\n    background-position: -185px -19px !important; }\n  .mejs__controls .mejs__toggleKeyPoint-button:hover button {\n    background: transparent center url("+i(n(1))+") !important;\n    background-size: 250px !important;\n    background-position: -185px -19px !important; }\n  .mejs__controls .mejs__toggleon button {\n    background: transparent no-repeat url("+i(n(0))+") !important;\n    background-size: 250px !important;\n    background-position: -185px 0px !important; }\n  .mejs__controls .mejs__toggleon:hover button {\n    background: transparent center url("+i(n(1))+") !important;\n    background-size: 250px !important;\n    background-position: -185px 0px !important; }\n  .mejs__controls .mejs__toggleoff button {\n    background: transparent no-repeat url("+i(n(0))+") !important;\n    background-size: 250px !important;\n    background-position: -185px -19px !important; }\n  .mejs__controls .mejs__toggleoff:hover button {\n    background: transparent center url("+i(n(1))+") !important;\n    background-size: 250px !important;\n    background-position: -185px -19px !important; }\n  .mejs__controls .mejs__fullscreen-button {\n    position: absolute;\n    right: 10px;\n    bottom: 0; }\n    .mejs__controls .mejs__fullscreen-button button {\n      height: 18px;\n      width: 18px;\n      background: transparent no-repeat;\n      background-position: -360px 0; }\n    .mejs__controls .mejs__fullscreen-button:hover button {\n      background-position: -396px 0; }\n  .mejs__controls .mejs__fullscreen button {\n    height: 18px;\n    width: 18px;\n    background: transparent no-repeat;\n    background-position: -360px 0; }\n  .mejs__controls .mejs__fullscreen:hover button {\n    background-position: -396px 0; }\n  .mejs__controls .mejs__unfullscreen button {\n    background: transparent no-repeat url("+i(n(0))+") !important;\n    background-size: 250px !important;\n    background-position: -42px -20px !important; }\n  .mejs__controls .mejs__unfullscreen:hover button {\n    background: transparent center url("+i(n(1))+") !important;\n    background-size: 250px !important;\n    background-position: -42px -20px !important; }\n  .mejs__controls .mejs__volume-button {\n    position: absolute;\n    right: 40px;\n    bottom: 0; }\n  .mejs__controls .mejs__mute button {\n    background: transparent url("+i(n(0))+");\n    background-size: 250px;\n    background-position: -21px -20px; }\n  .mejs__controls .mejs__mute:hover button {\n    background: transparent center url("+i(n(1))+");\n    background-size: 250px;\n    background-position: -21px -20px; }\n  .mejs__controls .mejs__unmute button {\n    background: transparent url("+i(n(0))+");\n    background-size: 250px;\n    background-position: -21px 0px; }\n  .mejs__controls .mejs__unmute:hover button {\n    background: transparent center url("+i(n(1))+");\n    background-size: 250px;\n    background-position: -21px 0px; }\n  .mejs__controls .mejs__speed-button {\n    position: absolute;\n    right: 75px;\n    bottom: 0;\n    width: 38px; }\n    .mejs__controls .mejs__speed-button:hover button {\n      color: #00a8ff; }\n  .mejs__controls .mejs__selectScreen-button button {\n    height: 18px;\n    width: 18px;\n    background: transparent no-repeat;\n    background-position: -73px 0; }\n  .mejs__controls .mejs__selectScreen-button:hover button {\n    background-position: -90px 0; }\n\n.mejs__audio {\n  position: relative; }\n  .mejs__audio .mejs__controls {\n    background: black; }\n  .mejs__audio .mejs__speed-button {\n    right: 110px; }\n  .mejs__audio .mejs__volume-button {\n    right: 70px; }\n  .mejs__audio .mejs__horizontal-volume-slider {\n    position: absolute;\n    right: 10px; }\n  .mejs__audio .mejs__horizontal-volume-total {\n    top: 14px; }\n\n.mejs__speed-button,\n.mejs-speed-button {\n  position: relative; }\n  .mejs__speed-button > button,\n  .mejs-speed-button > button {\n    background: transparent;\n    color: #fff;\n    font-size: 11px;\n    line-height: normal;\n    margin: 11px 0 0;\n    width: 36px; }\n\n.mejs__speed-selector,\n.mejs-speed-selector {\n  background: rgba(50, 50, 50, 0.7);\n  border: solid 1px transparent;\n  border-radius: 0;\n  height: 150px;\n  left: -10px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  top: -100px;\n  visibility: hidden;\n  width: 60px; }\n  .mejs__speed-selector-list,\n  .mejs-speed-selector-list {\n    display: block;\n    list-style-type: none !important;\n    margin: 0;\n    overflow: hidden;\n    padding: 0; }\n  .mejs__speed-selector-list-item,\n  .mejs-speed-selector-list-item {\n    color: #fff;\n    display: block;\n    list-style-type: none !important;\n    margin: 0 0 6px;\n    overflow: hidden;\n    padding: 0 10px; }\n  .mejs__speed-selector-list-item:hover,\n  .mejs-speed-selector-list-item:hover {\n    background-color: #c8c8c8 !important;\n    background-color: rgba(255, 255, 255, 0.4) !important; }\n  .mejs__speed-selector-input,\n  .mejs-speed-selector-input {\n    clear: both;\n    float: left;\n    left: -1000px;\n    margin: 3px 3px 0 5px;\n    position: absolute; }\n  .mejs__speed-selector-label,\n  .mejs-speed-selector-label {\n    color: white;\n    cursor: pointer;\n    float: left;\n    font-size: 11px;\n    line-height: 15px;\n    margin-left: 5px;\n    padding: 4px 0 0;\n    width: 60px; }\n  .mejs__speed-selector .mejs__speed-selected,\n  .mejs__speed-selector .mejs-speed-selected,\n  .mejs-speed-selector .mejs__speed-selected,\n  .mejs-speed-selector .mejs-speed-selected {\n    color: #21f8f8; }\n\n.mejs__speed-button:hover .mejs__speed-selector,\n.mejs-speed-button:hover .mejs-speed-selector {\n  visibility: visible; }\n\n.mejs__contextmenu,\n.mejs-contextmenu {\n  background: #fff;\n  border: solid 1px #999;\n  border-radius: 4px;\n  left: 0;\n  padding: 10px;\n  position: absolute;\n  top: 0;\n  width: 150px;\n  z-index: 9999999999; }\n  .mejs__contextmenu-separator,\n  .mejs-contextmenu-separator {\n    background: #ccc;\n    font-size: 0;\n    height: 1px;\n    margin: 5px 6px; }\n  .mejs__contextmenu-item,\n  .mejs-contextmenu-item {\n    color: #333;\n    cursor: pointer;\n    font-size: 12px;\n    padding: 4px 6px; }\n  .mejs__contextmenu-item:hover,\n  .mejs-contextmenu-item:hover {\n    background: #2c7c91;\n    color: #fff; }\n\n.mejs__overlay-button {\n  outline: none;\n  top: -29px;\n  position: relative; }\n\n.layer-overlay-watermark {\n  z-index: 3;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  left: 0;\n  position: absolute;\n  top: 55px;\n  background: transparent;\n  width: 148px !important;\n  height: 32px !important;\n  opacity: 0.6;\n  cursor: pointer;\n  -webkit-transform: rotate(-40deg);\n  transform: rotate(-40deg); }\n  .layer-overlay-watermark .container {\n    width: 100%;\n    height: 100%;\n    color: #fff;\n    font-size: 25px;\n    padding: 0;\n    white-space: nowrap; }\n\n.mejs__mulit-track {\n  position: absolute;\n  right: 10px;\n  top: 24px;\n  z-index: 3; }\n  .mejs__mulit-track a {\n    cursor: pointer; }\n  .mejs__mulit-track .sel {\n    display: none;\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    background: rgba(255, 255, 255, 0.4);\n    position: absolute;\n    width: auto; }\n  .mejs__mulit-track:hover .sel {\n    display: block; }\n\n.mejs__mulit-track {\n  position: absolute;\n  z-index: 3;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  top: 0;\n  right: 0;\n  height: 100%;\n  width: 36px; }\n  .mejs__mulit-track a {\n    cursor: pointer;\n    color: #fff;\n    display: block;\n    width: 100%;\n    text-align: center; }\n    .mejs__mulit-track a:hover {\n      color: #008aff; }\n    .mejs__mulit-track a .icon-tiaozheng {\n      font-size: 18px; }\n  .mejs__mulit-track .sel {\n    display: none;\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    background: rgba(50, 50, 50, 0.7);\n    position: absolute;\n    width: auto;\n    min-width: 50px;\n    left: 50%;\n    -webkit-transform: translateX(-50%);\n    transform: translateX(-50%);\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap; }\n    .mejs__mulit-track .sel > .mulit-track-item {\n      width: 50px;\n      padding: 2px 10px;\n      cursor: pointer;\n      -webkit-transition: all .2s ease 0s;\n      transition: all .2s ease 0s;\n      border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n      line-height: 19px;\n      color: #fff;\n      text-align: center; }\n      .mejs__mulit-track .sel > .mulit-track-item:hover {\n        background-color: #008aff; }\n      .mejs__mulit-track .sel > .mulit-track-item.play {\n        background-color: #008aff; }\n  .mejs__mulit-track:hover .sel {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex; }\n\n.mejs__mulit-track-button {\n  position: absolute;\n  right: 130px; }\n  .mejs__mulit-track-button button {\n    background: transparent !important; }\n\n.mejs__selectFile-button > button {\n  display: none; }\n\n.mejs__selectFile-button-dom {\n  position: relative;\n  margin-left: 5px; }\n  .mejs__selectFile-button-dom > span {\n    display: block;\n    line-height: 40px; }\n    .mejs__selectFile-button-dom > span:hover {\n      color: #008aff; }\n  .mejs__selectFile-button-dom:hover > span {\n    color: #008aff; }\n  .mejs__selectFile-button-dom > ul {\n    display: none;\n    position: absolute;\n    list-style: none;\n    margin: 0;\n    padding: 0;\n    bottom: 40px;\n    left: 50%;\n    -webkit-transform: translateX(-50%);\n    transform: translateX(-50%);\n    width: 200px;\n    background: rgba(21, 21, 21, 0.9);\n    max-height: 300px;\n    overflow-y: auto; }\n    .mejs__selectFile-button-dom > ul > li {\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: 100%;\n      line-height: 30px;\n      padding: 0 10px; }\n      .mejs__selectFile-button-dom > ul > li:hover {\n        background: rgba(255, 255, 255, 0.1); }\n      .mejs__selectFile-button-dom > ul > li.active {\n        color: #008aff; }\n  .mejs__selectFile-button-dom:hover > ul {\n    display: block; }\n\n.mejs__eight-track-button {\n  position: absolute;\n  right: 133px;\n  bottom: -1px; }\n  .mejs__eight-track-button button {\n    background: transparent !important; }\n  .mejs__eight-track-button > button {\n    display: none; }\n\n.mejs__eight-track {\n  z-index: 3;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-align: center;\n  -webkit-align-items: center;\n  -ms-flex-align: center;\n  align-items: center;\n  height: 100%;\n  width: 36px; }\n  .mejs__eight-track a {\n    cursor: pointer;\n    color: #fff;\n    display: block;\n    width: 100%;\n    text-align: center; }\n    .mejs__eight-track a:hover {\n      color: #008aff; }\n    .mejs__eight-track a .icon-tiaozheng {\n      font-size: 18px; }\n  .mejs__eight-track .sel {\n    display: none;\n    list-style: none;\n    padding: 0;\n    margin: 0;\n    background: rgba(50, 50, 50, 0.7);\n    position: absolute;\n    width: auto;\n    min-width: 50px;\n    left: 50%;\n    -webkit-transform: translateX(-50%);\n    transform: translateX(-50%);\n    -webkit-flex-wrap: wrap;\n    -ms-flex-wrap: wrap;\n    flex-wrap: wrap; }\n    .mejs__eight-track .sel > .eight-track-item {\n      width: 70px;\n      padding: 2px 10px;\n      cursor: pointer;\n      -webkit-transition: all .2s ease 0s;\n      transition: all .2s ease 0s;\n      border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n      line-height: 19px;\n      color: #fff;\n      text-align: center;\n      -webkit-transition: background-color 200ms;\n      transition: background-color 200ms; }\n      .mejs__eight-track .sel > .eight-track-item:hover {\n        background-color: rgba(0, 138, 255, 0.4); }\n      .mejs__eight-track .sel > .eight-track-item.play {\n        background-color: #008aff; }\n  .mejs__eight-track:hover .sel {\n    display: -webkit-box;\n    display: -webkit-flex;\n    display: -ms-flexbox;\n    display: flex; }\n\n.mejs__button, .mejs__time, .mejs__time-rail {\n  width: 24px; }\n  .mejs__button button, .mejs__time button, .mejs__time-rail button {\n    -webkit-transform: scale(0.9);\n    transform: scale(0.9); }\n",""])
},function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABCklEQVQ4T+2UsUoDQRRFz20sFLs0fkAaFQ0hktJv0CoWdhai32InpLW1ieAfWFkoooU2aVKksBREQRGuDOyG8NhksxJSOeXMu2fe3Ms8MeelOfOYCrS9AuyES+8kfUxqpAy4DTwGcUPS0z9w5MBiPLR9DHSAlHIrBHAPpJQvJXVjOIUd2l7O0q1PSLMPpLQ/ZwKmItsbwAOwFETfQFPSc9FlZR6eAudBeFL01LxmBLR9AAwk3Y4DbPeAvWzvStJ+OG8CW5Iu0v448Bq4kXQWBKvAC/CTCd/D+RFwKGl3JmDmZxv4khS/YfK6OnDaRCoDrgPDiiNtDXgtevImUKsIy8vfcjsWO2D/0u0vXhl1FazM0bsAAAAASUVORK5CYII="},function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAABLElEQVQ4T+2UvUoDQRSFv2NQ0Ghn4wPYaIghKJY+gYgWcVeIlYXos9gJtmLhbhpFfQIrC0VioY2NhYWloAn4g1dWWMMO65KEkMop75zzzf1hrujxUY95ZAMPLM8gc4lHP7hkXY2/EskGhjYD1B1zCU83/8DfDvSph4FtAh6QR8wmBmBcAQ1EDU977nDSMzyzEV6pIyZTp2ncM0qJRTXbA0aqQ5tmgGtgyDG980WZNd2mPZbdw8C2EbuOcSut1FjTAtbMRzxQ0UUCENgRYvknZhzjayVxH1oZo4iv/SjeAoZ2ijhnVTsJw4mN0eQO8ckwRZb04gA3gCqeFtoDRqrA5snxRkXuN4TQugBmraRMIExhPHa00sQE8JRWcgFjvCNYLM7xHLejzwu2i3S/Ac+saBUOz9EOAAAAAElFTkSuQmCC"},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();mejs&&Object.assign(mejs.MepDefaults,{"mulit-trackText":"Task"});var a=function(){function e(t){var n=this;i(this,e),this.audio_players={},this.player=t.player,this.volume=this.player.volume,this.audioList=[],this.audioStatus={};var o=t.streamAudioMediaInfos;o&&o.length>0&&(this.audioList=o.map(function(e){return{name:e.track,url:e.filePath}}),this.played=!1,this.player.addEventListener("play",function(){n.played=!0}),this.player.addEventListener("pause",function(){n.played=!1}))}return o(e,[{key:"mulittrackAction",value:function(){}},{key:"initDom",value:function(e){var t=this,n=document.createElement("div");n.className="mejs__mulit-track";var i='<a><i class="iconfont icon-tiaozheng"></i></a>\n                <ul class="sel">\n                    '+this.buildList(this.audioList)+"\n                </ul>\n                <style>\n                    .mejs__mulit-track .sel{\n                        width:"+50*(parseInt(this.audioList.length/8)+1)+"px\n                    }\n                </style>\n        ";n.innerHTML=i,$(".mejs__mulit-track-button").append(n),$(".sel").css({bottom:$(".mejs__mulit-track").height()+"px"}),$(".mulit-track-item").click(function(){t.setAudio($(this).index())})}},{key:"buildList",value:function(e){var t="";return e.forEach(function(e){t+='<li class="mulit-track-item" title="'+e.name+'">'+e.name+"</li>"}),t}},{key:"setAudio",value:function(e){var t=this;if(this.played){var n="audio_player_track_"+e;if(this.audio_players[n]){var i=Object.keys(this.audio_players);this.audio_players[n].paused?(this.audio_players[n].currentTime=this.player.currentTime,this.audio_players[n].play(),$(".mulit-track-item").eq(e).addClass("play"),this.audioStatus[n]=!0,this.player.volume=0):(this.audio_players[n].pause(),this.audioStatus[n]=!1,$(".mulit-track-item").eq(e).removeClass("play"),i.every(function(e){return t.audio_players[e].paused})&&(this.player.volume=this.volume))}else{this.player.volume=0;var o=document.createElement("audio");o.id=n,o.src=this.audioList[e].url,o.currentTime=this.player.currentTime,this.audio_players[n]=o,this.audioStatus[n]=!1,o.addEventListener("canplaythrough",function(){t.player.paused||(o.play(),t.audioStatus[n]=!0,$(".mulit-track-item").eq(e).addClass("play"))},!1),this.player.addEventListener("ended",function(){o.currentTime=t.player.currentTime,o.pause()},!1),this.player.addEventListener("pause",function(){o.pause()},!1),this.player.addEventListener("play",function(){t.audioStatus[n]&&(o.currentTime=t.player.currentTime,o.play())},!1),this.player.addEventListener("seeked",function(e){t.audio_players[n].paused||(o.currentTime=t.player.currentTime)},!1)}}}}]),e}();Object.assign(MediaElementPlayer.prototype,{buildmulitTrack:function(e,t,n,i){var o=this,r=$(i).parent().parent();o.mulitTrack=new a({player:i.childNodes[0],outer:r[0],streamAudioMediaInfos:_.get(e.options,"streamAudioMediaInfos",[])}),o.mulitTrack.audioList&&o.mulitTrack.audioList.length>0&&(o.addButton("mulit-track",o.mulitTrack.mulittrackAction),o.mulitTrack.initDom())}})},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();Object.assign(mejs.MepDefaults,{selectFileText:"选文件"});var a=function(){function e(t){i(this,e),this.fileList=t.fileList,this.currentSrc=t.currentSrc,this.changeFile=t.changeFile}return o(e,[{key:"initSelectFileElement",value:function(){var e=this;$(".mejs__selectFile-button>button").remove(),$(".mejs__selectFile-button").css({width:"auto",color:"#fff"});var t=$('<div class="mejs__selectFile-button-dom"></div>'),n="";this.fileList.forEach(function(t){n+='<li class="'+(e.currentSrc===t.file_path?"active":"")+'" title="'+t.file_path+'">'+t.quality+"</li>"}),n="<ul>"+n+"</ul>",t.append("<span>选文件</span>"),t.append(n),$(".mejs__selectFile-button").html(t);var i=this;t.find("li").click(function(e){e.preventDefault(),i.changeFile($(this).index())})}}]),e}();Object.assign(MediaElementPlayer.prototype,{buildselectFile:function(e,t,n,i){var o=this;this.selectFile=new a({fileList:_.get(e,"options.fileList"),currentSrc:_.get(e,"options.currentSrc"),changeFile:_.get(e,"options.changeFile")}),this.selectFile.fileList&&this.selectFile.fileList.length>0&&(this.addButton("selectFile",function(){0===$(".mejs__selectFile-button").find(".mejs__selectFile-button-dom").length&&o.selectFile.initSelectFileElement()}),setTimeout(function(){$(".mejs__selectFile-button").click()},100))}})},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}();mejs&&Object.assign(mejs.MepDefaults,{"eight-trackText":"Track"});var a=function(){function e(t){i(this,e),this.player=t.player,this.audioTracks=t.audioTracks,this.p=t.p,this.initDom()}return o(e,[{key:"initDom",value:function(){var e=this,t=document.createElement("div");t.className="mejs__eight-track";var n='<a><i class="iconfont icon-tiaozheng"></i></a>\n                <ul class="sel">\n                    '+this.buildList(this.audioTracks)+"\n                </ul>\n                <style>\n                    .mejs__eight-track .sel{\n                        width:"+70*(parseInt(this.audioTracks.length/8)+1)+"px\n                    }\n                </style>\n        ";t.innerHTML=n,$(".mejs__eight-track-button").append(t),$(".sel").css({bottom:$(".mejs__eight-track").height()+"px"}),$(".eight-track-item").click(function(){e.setAudio($(this).index())})}},{key:"buildList",value:function(e){for(var t="",n=0;n<e.length;n++)t+=0===n?'<li class="eight-track-item play" title="Track'+(n+1)+'">Track '+(n+1)+"</li>":'<li class="eight-track-item" title="Track '+(n+1)+'">Track '+(n+1)+"</li>";return t}},{key:"setAudio",value:function(e){for(var t=0;t<this.player.audioTracks.length;t++)this.player.currentTime=this.p.currentTime,t==e?(this.player.audioTracks[t].enabled=!0,$(".eight-track-item").eq(t).addClass("play")):(this.player.audioTracks[t].enabled=!1,$(".eight-track-item").eq(t).removeClass("play"))}},{key:"eightTrackkAction",value:function(){}}]),e}();Object.assign(MediaElementPlayer.prototype,{buildeightTrack:function(e,t,n,i){var o=this,r=$(i).parent().parent();i.childNodes[0].addEventListener("loadedmetadata",function(){i.childNodes[0].audioTracks?console.log(i.childNodes[0].audioTracks):console.error("没有可用的音轨。"),i.childNodes[0].audioTracks&&i.childNodes[0].audioTracks.length>1&&(o.eightTrack=new a({player:i.childNodes[0],outer:r[0],audioTracks:i.childNodes[0].audioTracks,p:e}),o.addButton("eight-track",o.eightTrack.eightTrackkAction),o.eightTrack.initDom())})}})},function(e,t,n){"use strict";function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=function(){function e(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(t,n,i){return n&&e(t.prototype,n),i&&e(t,i),t}}(),a=function(){function e(t){var n=this;i(this,e),this.player=t.player,this.isFirstSetAudio=!0,this.audio_players={},this.player.addEventListener("play",function(){n.isFirstSetAudio&&(n.setAudio(0),n.setAudio(1));for(var e in n.audio_players)n.audio_players[e].active&&n.playAudio(n.audio_players[e].audio)}),this.player.addEventListener("pause",function(){for(var e in n.audio_players)n.audio_players[e].active&&n.pauseAudio(n.audio_players[e].audio)}),this.player.addEventListener("ended",function(){for(var e in n.audio_players)n.audio_players[e].active&&n.pauseAudio(n.audio_players[e].audio)},!1),this.player.addEventListener("seeked",function(e){n.current_play_audio&&!n.current_play_audio.paused&&(n.current_play_audio.currentTime=n.player.currentTime)},!1)}return o(e,[{key:"initDom",value:function(){var e=this,t=document.createElement("div");t.className="mejs__eight-track";var n='<a><i class="iconfont icon-tiaozheng"></i></a>\n                <ul class="sel">\n                    '+this.buildList()+"\n                </ul>\n                <style>\n                    .mejs__eight-track .sel{\n                        width:"+70*(parseInt(this.player.audioTracks.length/8)+1)+"px\n                    }\n                </style>\n        ";t.innerHTML=n,$(".mejs__eight-track-button").append(t),$(".sel").css({bottom:$(".mejs__eight-track").height()+"px"}),$(".eight-track-item").click(function(){e.setAudio($(this).index())})}},{key:"buildList",value:function(){var e="";if(this.player.audioTracks.length>0)for(var t=0;t<this.player.audioTracks.length;t++)e+='<li class="eight-track-item" title="Track'+(t+1)+'">Track '+(t+1)+"</li>";return e}},{key:"setAudio",value:function(e){var t=this;this.isFirstSetAudio=!1,this.player.muted=!0;var n="audio_player_track_"+e;if(this.audio_players[n])this.setAudioAfter(e,n),$(this).trigger("onChangeAudio",[n]);else{var i=document.createElement("audio");i.id=n,i.src=this.player.src,i.currentTime=this.player.currentTime,i.muted=!1,this.audio_players[n]={index:e,active:!1,audio:i},i.addEventListener("loadedmetadata",function(){t.enableSpecialStreamOnly(i,e),t.bringAudioToChannel(t.audio_players[n],(e+1)%2==0?1:0),t.setAudioAfter(e,n),$(t).trigger("onChangeAudio",[n])})}}},{key:"setAudioAfter",value:function(e,t){if(this.audio_players[t].active){var n=!1;for(var i in this.audio_players)i!==t&&this.audio_players[i].active&&(n=!0);if(!n)return;this.audio_players[t].active=!1,$(".eight-track-item").eq(e).removeClass("play"),this.pauseAudio(this.audio_players[t].audio)}else this.audio_players[t].active=!0,$(".eight-track-item").eq(e).addClass("play"),this.player.paused||this.playAudio(this.audio_players[t].audio)}},{key:"enableSpecialStreamOnly",value:function(e,t){for(var n=0;n<e.audioTracks.length;n++)e.audioTracks[n].enabled=n==t}},{key:"bringAudioToChannel",value:function(e,t){var n=new(window.AudioContext||window.webkitAudioContext),i=n.createChannelMerger(2);n.createMediaElementSource(e.audio).connect(i,0,t),i.connect(n.destination),e.audioContext=n,e.channelMerger=i}},{key:"playAudio",value:function(e){e.currentTime=this.player.currentTime,e.play()}},{key:"pauseAudio",value:function(e){e.pause()}}]),e}();Object.assign(MediaElementPlayer.prototype,{buildeightTrackPro:function(e,t,n,i){var o=this;i.childNodes[0].addEventListener("loadedmetadata",function(){i.childNodes[0].audioTracks&&i.childNodes[0].audioTracks.length>1&&(o.eightTrack=new a({player:i.childNodes[0]}),o.addButton("eight-track",function(){}),o.eightTrack.initDom())})}})}]);