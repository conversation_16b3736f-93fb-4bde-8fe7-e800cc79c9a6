// db.ts
import <PERSON>ie, { Table } from 'dexie';

/** 资源缓存 */
export type ResultModelType = {
  id?: number;
  /** 文本内容*/
  before?: Blob;
  /** 结果 */
  after?: Blob;
  /** 区分页面 */
  key: string;
  /** 显示的名称 */
  title: string;
};

//! 若数据结构改变应当修改版本号
class Result extends Dexie {
  ResultModel!: Table<ResultModelType>;
  constructor() {
    super('Result');
    this.version(1).stores({
      // Primary key and indexed props 索引 不要索引文件
      ResultModel: '++id,key',
    });
  }
}
export const { ResultModel } = new Result();
