.history-chat-style {
    width: 100%;
    height: 100%;
    background-image: url(~@/images/chatTools/history_background.png);
    background-size: 100% 100%;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center center;
    
    .header {
        width: 100%;
        height: 70px;
        font-size: 17px;
        color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        font-weight: bold;
        padding: 0 20px;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .line {
        width: 100%;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        position: relative;
        top: 60px;
    }

    .content {
      width: 100%;
      height: 100%;
      padding: 0 20px;
      position: relative;
      overflow: auto;

      .none-tip {
        height: 40%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .text-info {
      display: flex;
      flex-direction: column;
      width: calc(100% - 44px);
      height: 100%;
      justify-content: center;
      padding-left: 10px;
    }

    .menu-title {
      font-size: 14px;
      font-weight: bold;
      // margin-bottom: 10px;
      height: 30px;
      line-height: 30px;
    }

    .menu-name {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      height: 25px;
      line-height: 25px;
    }

    .menu-item-wrp {

        height: 85px;
        font-size: 14px;
        color: #000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 16px;
        padding: 0 10px;
        cursor: pointer;
        position: relative;
        background: rgba(255, 255, 255, 0.5);
        margin: 15px 0;
    
        .chat-row {
          width: 100%;
          display: flex;
          align-items: center;
          height: 80px;
          column-gap: 5px;
        }

        .menu-title, .menu-name {
          width: 95%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
    
        .anticon {
          display: none;
    
          &:hover {
            color: #ff4d4f;
          }
        }

        .delete-icon {
            opacity: 0;
            position: absolute;
            right: 17px;
        }
    
        &:hover,
        &.active {
          // background: #E9E9F9;
          background: #FFFFFF;
          border-radius: 16px;
          border: 1px solid var(--primary-color);
          box-shadow: 0 0 5px var(--primary-color);

          .delete-icon {
            opacity: 1;
        }
        }
    
        &:hover {
          .anticon {
            display: block;
          }
        }
      }
}