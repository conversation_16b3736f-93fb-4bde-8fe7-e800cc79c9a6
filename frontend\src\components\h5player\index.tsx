import React, { FC, useEffect, useState } from 'react';
import { asyncLoadScript } from '@/utils';
import jQuery from 'jquery';

export interface Player {
  setTrack: (trackId: any, keyboard: any) => void;
  addKeyPoint: (points: any) => void;
  addButton: any;
  selectedTrack: any;
  tracks: any; //字幕音轨
  captionsButton: any;//字幕按钮
  play: () => void;
  stop: () => void;
  /**
   * 设置播放器的当前时间
   * @param opt 位置
   */
  setCurrentTime: (opt: number) => void;
  /**
   * 获取播放器的当前时间
   * @param opt 位置
   */
  getCurrentTime: () => void;
  /***
   * 设置片段的入点
   * @param frame 帧
   */
  setTrimin: (frame: number) => void;
  /**
   * 设置片段的出点
   * @param frame 帧
   */
  setTrimout: (frame: number) => void;
  /**
   * 获取片段的入点
   */
  getTrimin: () => void;
  /**
   * 获取片段的出点
   */
  getTrimout: () => void;
  /**
   * 清除片段
   */
  cleantrim: () => void;
  /**
   * 获取关键帧
   */
  getCurrentKeyframe: () => string;
  /**
   * 隐藏修改按钮
   */
  hideModifyBtn: (state?:boolean) => void;
  /**
   * 显示修改按钮
   */
  showModifyBtn: () => void;
}

export interface H5PlayerProps {
  src: string;
  id?: string,
  contrast?: number,
  brightness?: number,
  saturate?:number,
  config?: any;
  frameRate?: number;
  errorCode?: number;
  shareFlag?: boolean;
  translateFlag?: boolean;
  onSuccess?: (player: Player) => void;
  onError?: (e: any) => void;
  setSection?: (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => void;
  getKeyframe?: (
    media: any,
    currentTime: any,
    img: any,
  ) => void;
  modifySection?: (
    media: any,
    img: string,
    trimin: number,
    trimout: number,
  ) => void;
  resetSection?: (callback: any) => void;
  onPlayChange?: (point: number) => void;
  isEditPermission?: boolean;
  contendId?: any
}

export const defaultFeatures = [
  'playpause',
  'current',
  'progress',
  'duration',
  'tc',
  'tracks',
  'volume',
  'fullscreen',
  'contextmenu',
  'speed',
  'backframe',
  'prevframe',
  'skipback',
  'skipforward',
  'trimin',
  'trimout',
  'cleantrim',
  'seeking',
  //  'toggleKeyPoint',//视频进度条标记
  // 'getKeyframe',
  // 'setSection', //片段导出
  // 'exportSection',
  // 'saveSection',
  // 'togglekeypoint',
  // 'setCatalog',
  'tostart',
  'toend',
  // 'selectScreen',
  // 'ocr',
  // 'getgifdetails',
  // 'selectFile',
  // 'eightTrackPro',
  'volumnTrack',
  'audioVisualizer',
];

const defaultConfig = {
  playType: 'video',
  frameRate: 25,
  pluginPath: '/rman/libs/mam-h5player/dist/',
  features: defaultFeatures,
  disableRangeLock: true,
  /**
   * set 标记
   * @param videoElement
   */
  setTrimin: function (videoElement: any) {
    videoElement.stop();
  },
};

const H5Player: FC<H5PlayerProps> = ({
  src,
  id,
  config = {},
  frameRate,
  onSuccess,
  setSection,
  shareFlag,
  translateFlag,
  onPlayChange,
  modifySection,
  getKeyframe,
  resetSection,
  onError,
  isEditPermission = false,
  contendId,
  contrast,
  brightness,
  saturate
}) => {
  let player: any;
  const mobileFeatures = [
    'playpause',
    'current',
    'progress',
    'duration',
    // 'volume',
    'fullscreen',
    'contextmenu',
    // 'speed',
    'backframe',
    'prevframe',
    'skipback',
    'skipforward',
    'trim',
    // 'trimin',
    // 'trimout',
    // 'cleantrim',
    // 'seeking',
    'tostart',
    'toend',
    // 'audioVisualizer',
  ]
  const init = async () => {
    if (!player) {
      if(isEditPermission){
        !defaultFeatures.includes('setSection') && defaultFeatures.push('setSection');
        if(window.localStorage.getItem('upform_platform')==='standard'){
          !defaultFeatures.includes('getKeyframe') && defaultFeatures.push('getKeyframe');
        }
      }
      player = ($(`#${id}`) as any).h5mediaplayer({
        ...defaultConfig,
        frameRate: frameRate,
        features: defaultFeatures,
        success: (media: any, node: any, player: Player) => {
          if (onSuccess && typeof onSuccess === 'function') {
            onSuccess(player);
          }
        },
        error: (e: any) => {
          if (onError && typeof onError === 'function') {
            onError(e);
          }
        },
        ...config,
        setSection,
        getKeyframe,
        modifySection,
        resetSection,
        // markerColor:'#ff0000',
        // markers:['1','2','3'],
        // markerWidth:1,
        // markerCallback:(media:any, time:any)=>{
        //   console.log(time)
        // }
      });
      (window as any).document
        .getElementById(id)
        .addEventListener('accurateTimeUpdate', function () {
          onPlayChange && onPlayChange(player.getCurrentTime() * 10000000);
        });
      window.onresize = function (e) {
        if (!checkFull()) {
          //要执行的动作
          player.exitFullScreen()
        }
      }
    }
  };
  const loadScripts = async () => {
    await asyncLoadScript('/rman/libs/mam-base/dist/mam-base.js');
    await asyncLoadScript('/rman/libs/mam-h5player/dist/mam-h5player.js');
    return true;
  };
  /**
   * esc退出全屏bug（底部按钮消失问题）
   */
  //判断浏览器是否全屏（需要考虑兼容问题）
  const checkFull = () => {
    const doc = (document as any)
    let isFull = doc.mozFullScreen ||
      doc.fullScreen ||
      //谷歌浏览器及Webkit内核浏览器
      doc.webkitIsFullScreen ||
      doc.webkitRequestFullScreen ||
      doc.mozRequestFullScreen ||
      doc.msFullscreenEnabled
    if (isFull === undefined) {
      isFull = false
    }
    // console.log(isFull, 'isFull')
    return isFull;
  }
  useEffect(() => {
    (window as any).$ = (window as any).jQuery = jQuery;
    (window as any).nxt = {};

    loadScripts().finally(() => {
      init();
    });
  }, []);
  useEffect(() => {
    let video = document.getElementById(id as string) as HTMLElement
    video.style.filter = `saturate(${saturate}%) contrast(${contrast}%) brightness(${brightness}%)`
  },[saturate,contrast,brightness])
  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <video
        id={id}
        style={{ width: '100%', height: '100%'}}
        preload="metadata"
        crossOrigin="anonymous"
        src={src}
      >
        {/* <track src={`/api/search/webvtt?contentId=${contendId}&voice=true`} kind="subtitles" srcLang="zh" label="简体中文" default /> */}
        <track src={`/rman/v1/search/webvtt?contentId=${contendId}&voice=true${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="zh" label="简体中文" default />
        {
          translateFlag && 
          // <track src={`/api/search/webvtt?contentId=${contendId}&voice=true&lang=en${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="en" label="英文" default />
          <track src={`/rman/v1/search/webvtt?contentId=${contendId}&voice=true&lang=en${shareFlag?'&isSysAuth=true':''}`} kind="subtitles" srcLang="en" label="英文" default />
        }
      </video>
    </div>
  );
};
export default H5Player;
