import { useIntl } from 'umi';
// @ts-ignore
import Zh from "@/locales/zh-CN.json"

interface Name {
  [property: string]: string;
}

export default function useLocale() {
  const intl = useIntl();
  const t = (id: string, name?: string | Name) => {
    const isOject = name instanceof Object;
    const obj = isOject ? name : name ? { name } : undefined;
    let text = id
    if (Zh[id]) {
      text = intl.formatMessage({ id, defaultMessage: id }, obj)
    }
    
    return text
    
  };

  return { t };
}
