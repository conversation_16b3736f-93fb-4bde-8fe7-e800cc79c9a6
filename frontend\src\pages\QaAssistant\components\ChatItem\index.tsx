import React, { FC } from 'react';
import './index.less';
import { Avatar, message } from 'antd';
import IconFont from '@/components/iconFont/iconFont';
import { deepCopy } from '../../utils';
import AssistantAvatar from '@/assets/img/qa-assistant.png';
import { IGlobal, useSelector } from 'umi';
import defaultAvatar from '@/assets/img/defualt-avatar.png';
import { copy } from '@/utils/utils';

interface ChatItemProps {
  tag?: string;
  data?: any;
  isMine?: boolean;
  content?: any;
  typing?: boolean;
  children?: any;
  extend?: any;
  avatar: string;
}

const LinkItem: FC<{ data: any; }> = ({ data }) => {
  return <div className='course-item-wrp' onClick={() => window.open(data.link)}>
    <img src={data.cover} alt="" />
    <div className='name-wrp' title={data.name}>
      {data.name.map((item: any, index: number) => <span key={index} className={`${item.isLight && 'high'}`}>{item.text}</span>)}
    </div>
  </div>;
};

const ChatItem: FC<ChatItemProps> = ({
  tag,
  data,
  isMine,
  avatar,
  content,
  typing,
  children,
  extend
}) => {
  const { userInfo } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  const handleCopy = () => {
    const data = children ?? content;
    // debugger;
    const text = deepCopy(data);
    copy(text);
  };
  return (
    <div
      className={`chat-item-wrp ${isMine ? 'mine' : ''} ${tag ? tag : ''
        }`}>
      <div className="avatar">
        <Avatar
          size={34}
          src={isMine ? userInfo.avatar : avatar}
          icon={<img src={AssistantAvatar}></img>}
          draggable={false}
        />
      </div>
      <div className="chat-message-box">
        <div className="chat-message-content">
          <div>{children ?? content}</div>
          {extend?.list?.length > 0 && <div>
            <p>平台关于”{extend.preMsg}“有以下课程：</p>
            <div className='course-list-wrp'>
              {extend.list.map((item: any) => <LinkItem data={item} key={item.link} />)}
              {extend.isMore && <div className='more'><a href={`/learn/global/search?keyword=${extend.preMsg}`} target='_blank'>更多{`>`}</a></div>}
            </div>
            
          </div>}
        </div>
        <div className="chat-message-date">
          <div className="copy-btn" onClick={handleCopy}>
            <IconFont type="iconcopy" />
            复制
          </div>
          {!typing && (
            <div className="date">{data?.create_time}</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatItem;
