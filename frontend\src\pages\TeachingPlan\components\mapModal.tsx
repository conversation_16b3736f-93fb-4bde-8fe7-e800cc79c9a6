import React, { FC, useState } from 'react';
import { Modal, Tabs, message } from 'antd'
import {
  ActionType,
  ProColumns,
  ProTable,
  ProTableProps,
} from '@ant-design/pro-components';
import { reqMapApi } from '@/api/teachingPlan'
import dayjs from 'dayjs'
interface MapModalProps {
  modalVisible: boolean;
  onCloseModal: (val?: any) => void;
  emitSelectMap: (val: any) => void;
}
const MapModal: FC<MapModalProps> = ({
  modalVisible,
  onCloseModal,
  emitSelectMap
}) => {
  const [tabKey, setTabKey] = useState<string>('1');
  const onChange = (key: string) => {
    setTabKey(key)
  };
  const [total, setTotal] = useState<number>(0);
  const [pagination, setPagination] = useState<any>({
    current: 1,
    pageSize: 10,
  })
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);

  const columns: ProColumns<any>[] = [
    {
      title: '名称',
      fieldProps: {
        placeholder: `请输入地图名称`,
      },
      formItemProps: {
        label: null,
      },
      dataIndex: 'mapName',
      colSize: 1.5,
    },
    {
      title: '适用课程',
      dataIndex: 'courseName',
      hideInSearch: true,
    },
    {
      title: '学科专业',
      dataIndex: 'subjectName',
      hideInSearch: true,
    },
    {
      title: '创建人',
      dataIndex: 'teacherName',
      hideInSearch: true,
    },
    {
      title: "创建时间",
      dataIndex: 'createDate',
      key: 'createDate',
      render: (text: any) => dayjs(text).format('YYYY-MM-DD HH:mm:ss') || '-'
    },
  ]
  const tableConfig: ProTableProps<any, any> = {
    pagination: {
      total: total,
      current: pagination.current,
      pageSize: pagination.pageSize,
      pageSizeOptions: ['50', '100', '500', '1000', '2000'],
      showSizeChanger: true,
      showQuickJumper: true,
      hideOnSinglePage: false,
      // showTotal: (total: number) => `共 ${total} 条`,
      defaultPageSize: pagination.pageSize,
      position: ['bottomCenter'],
      size: 'small',
    },
    options: false,
    rowKey: 'id',
    search: {
      span: 4,
      showHiddenNum: true,
      labelWidth: 'auto',
      className: 'in-pro-table-search'
    },
    className: 'in-pro-table',
    columns,
    params: {
      tabKey
    },
    async request(
      // 第一个参数 params 查询表单和 params 参数的结合
      // 第一个参数中一定会有 pageSize 和  current ，这两个参数是 antd 的规范
      // T & {} https://procomponents.ant.design/components/table#request
      params: any,
      sort,
      filter,
    ) {
      try {
        const {
          current,
          pageSize,
          ...rest
        } = params;
        const { data: { results, total } } = await reqMapApi({
          page: current,
          size: pageSize,
          field: params.tabKey == 1 ? 0 : 3,
          sort: true,
          isShow: params.tabKey == 2 ? 2 : null,
          name: params.mapName
        });
        setTotal(total)
        return {
          data: results,
          // success 请返回 true，
          // 不然 table 会停止解析数据，即使有数据
          success: true,
          // 不传会使用 data 的长度，如果是分页一定要传
          total,
        };
      } catch (error) {
        return {
          data: [],
          success: false,
        };
      }
    },
    scroll: {
      x: true,
    },
    onChange: (
      pagination: any,
      filters: any,
      sorter: any,
    ) => {
      setPagination({
        ...pagination,
      });
    },
    tableAlertRender: false,
    rowSelection: {
      type: 'radio', // 单选模式
      columnWidth: 80,
      selectedRowKeys,
      onChange(keys, rows) {
        setSelectedRowKeys(keys);
        setSelectedRows(rows);
      },
    },
  };
  return (
    <Modal
      width={'80%'}
      wrapClassName='map-modal-wrapper'
      title="选择课程地图"
      open={modalVisible}
      onCancel={() => {
        onCloseModal()
        setSelectedRowKeys([])
        setSelectedRows([])
      }}
      onOk={() => {
        if (selectedRowKeys.length > 0) {
          const row = selectedRows[0]
          emitSelectMap({
            mapId: row.id,
            mapName: row.mapName
          })
          onCloseModal()
          setSelectedRowKeys([])
          setSelectedRows([])
        } else {
          message.error('请选择图谱!')
          return
        }
      }}
    >
      <Tabs activeKey={tabKey} onChange={onChange}>
        <Tabs.TabPane tab="我的地图" key="1">
          <ProTable {...tableConfig} />
        </Tabs.TabPane>
        <Tabs.TabPane tab="已发布地图" key="2">
          <ProTable {...tableConfig} />
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
};

export default MapModal;