/* Ephox Accessibility Checker Plugin
 *
 * Copyright 2010-2017 Ephox Corporation.  All rights reserved.
 *
 * Version: 2.3.1-127
 */

!function(f){"use strict";var e,t,n,r,o,i,m=function(e){var t=e,n=function(){return t};return{get:n,set:function(e){t=e},clone:function(){return m(n())}}},a=function(){},g=function(e){return function(){return e}},u=function(e){return e},c=g(!1),h=g(!0),s=function(){return l},l=(e=function(e){return e.isNone()},r={fold:function(e,t){return e()},is:c,isSome:c,isNone:h,getOr:n=function(e){return e},getOrThunk:t=function(e){return e()},getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:g(null),getOrUndefined:g(void 0),or:n,orThunk:t,map:s,each:a,bind:s,exists:c,forall:h,filter:s,equals:e,equals_:e,toArray:function(){return[]},toString:g("none()")},Object.freeze&&Object.freeze(r),r),d=function(n){var e=g(n),t=function(){return o},r=function(e){return e(n)},o={fold:function(e,t){return t(n)},is:function(e){return n===e},isSome:h,isNone:c,getOr:e,getOrThunk:e,getOrDie:e,getOrNull:e,getOrUndefined:e,or:t,orThunk:t,map:function(e){return d(e(n))},each:function(e){e(n)},bind:r,exists:r,forall:r,filter:function(e){return e(n)?o:l},toArray:function(){return[n]},toString:function(){return"some("+n+")"},equals:function(e){return e.is(n)},equals_:function(e,t){return e.fold(c,function(e){return t(n,e)})}};return o},w={some:d,none:s,from:function(e){return null==e?l:d(e)}},p=function(e){return parseInt(e,10)},v=function(e,t){var n=e-t;return 0===n?0:0<n?1:-1},y=function(e,t,n){return{major:e,minor:t,patch:n}},b=function(e){var t=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e);return t?y(p(t[1]),p(t[2]),p(t[3])):y(0,0,0)},x=function(e,t){return!!e&&-1===function(e,t){var n=v(e.major,t.major);if(0!==n)return n;var r=v(e.minor,t.minor);if(0!==r)return r;var o=v(e.patch,t.patch);return 0!==o?o:0}(b([(n=e).majorVersion,n.minorVersion].join(".").split(".").slice(0,3).join(".")),b(t));var n},k=(o=f.window,i="error",function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=o.console;n&&i in n&&n[i].apply(n,e)}),D={requestAnimationFrame:function(e,t){for(var n=f.window.requestAnimationFrame,r=["ms","moz","webkit"],o=0;o<r.length&&!n;o++)n=f.window[r[o]+"RequestAnimationFrame"];n||(n=function(e){f.window.setTimeout(e,0)}),n(e,t)},setEditorTimeout:function(e,t,n){f.window.setTimeout(function(){e.removed||t()},n||0)}},T=function(t){return function(e){return function(e){if(null===e)return"null";var t=typeof e;return"object"===t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"===t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t}(e)===t}},N=T("string"),A=T("array"),S=T("boolean"),R=T("undefined"),I=T("function"),C=T("number"),O=Array.prototype.slice,E=Array.prototype.indexOf,_=Array.prototype.push,M=function(e,t){return n=e,r=t,-1<E.call(n,r);var n,r},H=function(e,t){for(var n=0,r=e.length;n<r;n++){if(t(e[n],n))return!0}return!1},U=function(e,t){for(var n=e.length,r=new Array(n),o=0;o<n;o++){var i=e[o];r[o]=t(i,o)}return r},P=function(e,t){for(var n=0,r=e.length;n<r;n++){t(e[n],n)}},G=function(e,t){for(var n=[],r=0,o=e.length;r<o;r++){var i=e[r];t(i,r)&&n.push(i)}return n},L=function(e,t,n){return P(e,function(e){n=t(n,e)}),n},W=function(e,t){for(var n=0,r=e.length;n<r;n++){var o=e[n];if(t(o,n))return w.some(o)}return w.none()},q=function(e){for(var t=[],n=0,r=e.length;n<r;++n){if(!A(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);_.apply(t,e[n])}return t},B=function(e,t){return q(U(e,t))},V=function(e,t){for(var n=0,r=e.length;n<r;++n){if(!0!==t(e[n],n))return!1}return!0},j=(I(Array.from)&&Array.from,function(e,t,n){return 0!=(e.compareDocumentPosition(t)&n)}),F=function(e,t){return j(e,t,f.Node.DOCUMENT_POSITION_PRECEDING)},z=function(e,t){return j(e,t,f.Node.DOCUMENT_POSITION_CONTAINED_BY)},Y=function(e){if(null==e)throw new Error("Node cannot be null or undefined");return{dom:g(e)}},X={fromHtml:function(e,t){var n=(t||f.document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||1<n.childNodes.length)throw f.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return Y(n.childNodes[0])},fromTag:function(e,t){var n=(t||f.document).createElement(e);return Y(n)},fromText:function(e,t){var n=(t||f.document).createTextNode(e);return Y(n)},fromDom:Y,fromPoint:function(e,t,n){var r=e.dom();return w.from(r.elementFromPoint(t,n)).map(Y)}},K=(f.Node.ATTRIBUTE_NODE,f.Node.CDATA_SECTION_NODE,f.Node.COMMENT_NODE,f.Node.DOCUMENT_NODE),$=(f.Node.DOCUMENT_TYPE_NODE,f.Node.DOCUMENT_FRAGMENT_NODE,f.Node.ELEMENT_NODE),J=f.Node.TEXT_NODE,Q=(f.Node.PROCESSING_INSTRUCTION_NODE,f.Node.ENTITY_REFERENCE_NODE,f.Node.ENTITY_NODE,f.Node.NOTATION_NODE,$),Z=K,ee=function(e,t){var n=e.dom();if(n.nodeType!==Q)return!1;var r=n;if(void 0!==r.matches)return r.matches(t);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(t);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(t);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")},te=function(e){return e.nodeType!==Q&&e.nodeType!==Z||0===e.childElementCount},ne=function(e,t){var n=void 0===t?f.document:t.dom();return te(n)?w.none():w.from(n.querySelector(e)).map(X.fromDom)},re=function(t,e,n){return B(e,function(e){return"I1"===e.id?e.check(t,n)?[{rule:e,element:t}]:[]:e.check(t)?[{rule:e,element:t}]:[]})},oe=function(e,t){for(var n=[],r=function(e,t){for(var n=[],r=[],o=0,i=e.length;o<i;o++){var a=e[o];(t(a,o)?n:r).push(a)}return{pass:n,fail:r}}(t.rules,function(e){return e.walkable}),o=R(t.ignoreSelectors)?[]:t.ignoreSelectors,i=r.pass,a=r.fail,u=f.document.createTreeWalker(e,f.NodeFilter.SHOW_ELEMENT,null,!1);u.nextNode();)if(!H(o,function(e){return ee(X.fromDom(u.currentNode),e)})){var c=re(u.currentNode,i,t.allowDecorativeImages);n=n.concat(c)}var s,l=(s=e,B(a,function(t){return U(t.check(s),function(e){return{rule:t,element:e}})}));return(n=n.concat(l)).sort(function(e,t){return n=e.element,r=t.element,n===r?0:F(n,r)?1:-1;var n,r}),n};var ie,ae={html4:g("html4"),html5:g("html5")},ue={a:g("a"),aa:g("aa"),aaa:g("aaa")},ce={error:g("error"),warning:g("warning"),info:g("info")},se=function(e,t){return e.matches?e.matches(t):e.webkitMatchesSelector?e.webkitMatchesSelector(t):e.mozMatchesSelector?e.mozMatchesSelector(t):!!e.msMatchesSelector&&e.msMatchesSelector(t)},le={id:"D1",wcag:"1.3.1",severity:ce.warning(),desckey:"a11y.d1.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return!!se(e,"p")&&V(e.childNodes,function(e){return/^(b|strong)$/i.test(e.nodeName)})},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},fe={id:"D2",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.d2.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!1,check:function(e){var n,t=e.querySelectorAll("h1, h2, h3, h4, h5, h6"),r=[];return P(t,function(e){var t=parseInt(e.tagName.slice(1),10);n&&1<t-n&&r.push(e),n=t}),r},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},de={id:"D3",wcag:"2.4.4",severity:ce.error(),desckey:"a11y.d3.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/navigation-mechanisms-refs.html",walkable:!1,check:function(e){return G(e.querySelectorAll("a + a"),function(e){return e.href===e.previousSibling.href})},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},pe=function(e){var t=e.nodeName,n=function(e){for(var t=e.nextSibling;null!==t&&("#text"===t.nodeName||"BR"===t.nodeName);)t=t.nextSibling;return t}(e);return null!==n&&n.nodeName===t&&n},me=function(e,t){for(var n=pe(e),r=t(e)?[e]:[];!1!==n&&t(n);)r.push(n),n=pe(n);return r},he=function(e,t){for(var n=-1,r=0;r<e.length;r++)if(e[r]===t){n=r;break}return n},ve=function(e,t){for(var n=e.querySelectorAll("p,h1,h2,h3,h4,h5,h6"),r=[],o=0;o<n.length;){var i=n[o],a=me(i,t);if(1<a.length){r.push(a[0]);var u=a[a.length-1],c=he(n,u);o=-1===c?o+1:c}else o++}return r},ye=function(e){return e.textContent.match(/^\s{0,}([a-z1-9]|i+)\s{0,}[.)-]/gim)},ge={id:"D4o",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.d4.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!1,check:function(e){return ve(e,ye)},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},we=function(e){return e.textContent.match(/^\s{0,}(\*|-)\s{0,}/gim)},be={id:"D4u",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.d4.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!1,check:function(e){return ve(e,we)},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},xe=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(t.length!==n.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+n.length+" arguments");var r={};return P(t,function(e,t){r[e]=g(n[t])}),r}},ke=Object.keys,De=Object.hasOwnProperty,Te=function(e,t){for(var n=ke(e),r=0,o=n.length;r<o;r++){var i=n[r];t(e[i],i)}},Ne=function(n){return function(e,t){n[t]=e}},Ae=function(e,t){var n,r,o,i,a={},u={};return n=e,r=t,o=Ne(a),i=Ne(u),Te(n,function(e,t){(r(e,t)?o:i)(e,t)}),{t:a,f:u}},Se=function(e){return n=function(e){return e},r=[],Te(e,function(e,t){r.push(n(e,t))}),r;var n,r},Re=function(e,t){return De.call(e,t)},Ie=function(e){return e.slice(0).sort()},Ce=function(t,e){if(!A(e))throw new Error("The "+t+" fields must be an array. Was: "+e+".");P(e,function(e){if(!N(e))throw new Error("The value "+e+" in the "+t+" fields was not a string.")})},Oe=function(o,i){var n,a=o.concat(i);if(0===a.length)throw new Error("You must specify at least one required or optional field.");return Ce("required",o),Ce("optional",i),n=Ie(a),W(n,function(e,t){return t<n.length-1&&e===n[t+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+n.join(", ")+"].")}),function(t){var n=ke(t);V(o,function(e){return M(n,e)})||function(e,t){throw new Error("All required keys ("+Ie(e).join(", ")+") were not specified. Specified keys were: "+Ie(t).join(", ")+".")}(o,n);var e=G(n,function(e){return!M(a,e)});0<e.length&&function(e){throw new Error("Unsupported keys for object: "+Ie(e).join(", "))}(e);var r={};return P(o,function(e){r[e]=g(t[e])}),P(i,function(e){r[e]=g(Object.prototype.hasOwnProperty.call(t,e)?w.some(t[e]):w.none())}),r}},Ee=xe("red","green","blue","alpha"),_e={rgbaColour:Ee,white:function(){return Ee(255,255,255,1)}},Me=function(e,t){return(Math.max(e,t)+.05)/(Math.min(e,t)+.05)},He=function(e){var t=function(e){return e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4)},n=e.red()/255,r=e.green()/255,o=e.blue()/255;return.2126*t(n)+.7152*t(r)+.0722*t(o)},Ue=function(e,t){var n=e.alpha()+t.alpha()*(1-e.alpha()),r=(e.red()*e.alpha()+t.red()*t.alpha()*(1-e.alpha()))/n,o=(e.green()*e.alpha()+t.green()*t.alpha()*(1-e.alpha()))/n,i=(e.blue()*e.alpha()+t.blue()*t.alpha()*(1-e.alpha()))/n;return _e.rgbaColour(Math.ceil(r),Math.ceil(o),Math.ceil(i),n)},Pe=function(e){var t=function(e,t,n,r){var o=parseInt(e,10),i=parseInt(t,10),a=parseInt(n,10),u=parseFloat(r);return w.some(_e.rgbaColour(o,i,a,u))};if("transparent"===e)return t(0,0,0,0);var n=e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/);if(null!==n)return t(n[1],n[2],n[3],1);var r=e.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/);return null!==r?t(r[1],r[2],r[3],r[4]):w.none()},Ge=void 0!==f.window?f.window:Function("return this;")(),Le=function(e,t){return function(e,t){for(var n=null!=t?t:Ge,r=0;r<e.length&&null!=n;++r)n=n[e[r]];return n}(e.split("."),t)},We=function(e,t){var n=Le(e,t);if(null==n)throw new Error(e+" not available on this browser");return n},qe=function(e){var t,n=Le("ownerDocument.defaultView",e);return(t=n,We("HTMLElement",t)).prototype.isPrototypeOf(e)},Be=function(e){return qe(e.dom())},Ve=(ie=J,function(e){return e.dom().nodeType===ie}),je=function(e){var t=e.dom().body;if(null==t)throw new Error("Body is not available yet");return X.fromDom(t)},Fe=function(){return(Fe=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};var ze,Ye=function(){return Xe(0,0)},Xe=function(e,t){return{major:e,minor:t}},Ke={nu:Xe,detect:function(e,t){var n=String(t).toLowerCase();return 0===e.length?Ye():function(e,t){var n=function(e,t){for(var n=0;n<e.length;n++){var r=e[n];if(r.test(t))return r}}(e,t);if(!n)return{major:0,minor:0};var r=function(e){return Number(t.replace(n,"$"+e))};return Xe(r(1),r(2))}(e,n)},unknown:Ye},$e="Firefox",Je=function(e,t){return function(){return t===e}},Qe=function(e){var t=e.current;return{current:t,version:e.version,isEdge:Je("Edge",t),isChrome:Je("Chrome",t),isIE:Je("IE",t),isOpera:Je("Opera",t),isFirefox:Je($e,t),isSafari:Je("Safari",t)}},Ze={unknown:function(){return Qe({current:void 0,version:Ke.unknown()})},nu:Qe,edge:g("Edge"),chrome:g("Chrome"),ie:g("IE"),opera:g("Opera"),firefox:g($e),safari:g("Safari")},et="Windows",tt="Android",nt="Solaris",rt="FreeBSD",ot="ChromeOS",it=function(e,t){return function(){return t===e}},at=function(e){var t=e.current;return{current:t,version:e.version,isWindows:it(et,t),isiOS:it("iOS",t),isAndroid:it(tt,t),isOSX:it("OSX",t),isLinux:it("Linux",t),isSolaris:it(nt,t),isFreeBSD:it(rt,t),isChromeOS:it(ot,t)}},ut={unknown:function(){return at({current:void 0,version:Ke.unknown()})},nu:at,windows:g(et),ios:g("iOS"),android:g(tt),linux:g("Linux"),osx:g("OSX"),solaris:g(nt),freebsd:g(rt),chromeos:g(ot)},ct=function(e,t){var n=String(t).toLowerCase();return W(e,function(e){return e.search(n)})},st=function(e,n){return ct(e,n).map(function(e){var t=Ke.detect(e.versionRegexes,n);return{current:e.name,version:t}})},lt=function(e,n){return ct(e,n).map(function(e){var t=Ke.detect(e.versionRegexes,n);return{current:e.name,version:t}})},ft=function(e,t){return-1!==e.indexOf(t)},dt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,pt=function(t){return function(e){return ft(e,t)}},mt=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return ft(e,"edge/")&&ft(e,"chrome")&&ft(e,"safari")&&ft(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,dt],search:function(e){return ft(e,"chrome")&&!ft(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return ft(e,"msie")||ft(e,"trident")}},{name:"Opera",versionRegexes:[dt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:pt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:pt("firefox")},{name:"Safari",versionRegexes:[dt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(ft(e,"safari")||ft(e,"mobile/"))&&ft(e,"applewebkit")}}],ht=[{name:"Windows",search:pt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return ft(e,"iphone")||ft(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:pt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:pt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:pt("linux"),versionRegexes:[]},{name:"Solaris",search:pt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:pt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:pt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],vt={browsers:g(mt),oses:g(ht)},yt=m(function(e,t){var n,r,o,i,a,u,c,s,l,f,d,p,m=vt.browsers(),h=vt.oses(),v=st(m,e).fold(Ze.unknown,Ze.nu),y=lt(h,e).fold(ut.unknown,ut.nu);return{browser:v,os:y,deviceType:(r=v,o=e,i=t,a=(n=y).isiOS()&&!0===/ipad/i.test(o),u=n.isiOS()&&!a,c=n.isiOS()||n.isAndroid(),s=c||i("(pointer:coarse)"),l=a||!u&&c&&i("(min-device-width:768px)"),f=u||c&&!l,d=r.isSafari()&&n.isiOS()&&!1===/safari/i.test(o),p=!f&&!l&&!d,{isiPad:g(a),isiPhone:g(u),isTablet:g(l),isPhone:g(f),isTouch:g(s),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:g(d),isDesktop:g(p)})}}(f.navigator.userAgent,function(e){return f.window.matchMedia(e).matches})),gt=function(e,t){return e.dom()===t.dom()},wt=(yt.get().browser.isIE(),function(e,t,n){!function(e,t,n){if(!(N(n)||S(n)||C(n)))throw f.console.error("Invalid call to Attr.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")}(e.dom(),t,n)}),bt=function(e,t){var n=e.dom().getAttribute(t);return null===n?void 0:n},xt=function(e,t){var n=e.dom();return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},kt=function(e,t){e.dom().removeAttribute(t)},Dt=function(e,t){var n,r,o=e.dom(),i=f.window.getComputedStyle(o).getPropertyValue(t),a=""!==i||null!=(r=Ve(n=e)?n.dom().parentNode:n.dom())&&r.ownerDocument.body.contains(r)?i:Tt(o,t);return null===a?void 0:a},Tt=function(e,t){return void 0!==(n=e).style&&I(n.style.getPropertyValue)?e.style.getPropertyValue(t):"";var n},Nt=function(e){return X.fromDom(e.dom().ownerDocument)},At=function(e){return w.from(e.dom().parentNode).map(X.fromDom)},St=function(e){return U(e.dom().childNodes,X.fromDom)},Rt=function(e){return t=0,n=e.dom().childNodes,w.from(n[t]).map(X.fromDom);var t,n},It=(xe("element","offset"),function(e,t){var n=parseFloat(Dt(e,"opacity"));return n<1?_e.rgbaColour(t.red(),t.green(),t.blue(),t.alpha()*n):t}),Ct=function(e){var t=Nt(e),n=je(t),r=[e].concat(function(e,t){for(var n=I(t)?t:c,r=e.dom(),o=[];null!==r.parentNode&&void 0!==r.parentNode;){var i=r.parentNode,a=X.fromDom(i);if(o.push(a),!0===n(a))break;r=i}return o}(e,function(e){return gt(e,n)})),o=L(r,function(n,r){if(1===n.alpha())return n;var e=Dt(r,"background-color");return Pe(e).bind(function(e){var t=It(r,e);return 0<t.alpha()?w.some(Ue(n,t)):w.none()}).getOr(n)},_e.rgbaColour(0,0,0,0));return o.alpha()<1?Ue(o,_e.white()):o},Ot=function(r,o){var e=Dt(r,"color");return Pe(e).bind(function(e){var t=It(r,e),n=t.alpha()<1?Ue(t,o):t;return w.some(n)})},Et=function(e){var r=Ct(e);return Ot(e,r).map(function(e){var t=He(r),n=He(e);return Me(n,t)})},_t=function(e,t){return Et(e).filter(function(e){return e<t}).isSome()},Mt=function(e){var t=St(e);return 0<G(t,function(e){return Ve(e)&&0<(t=e,t.dom().nodeValue).trim().length;var t}).length},Ht={id:"D5c",wcag:"1.4.3",severity:ce.error(),desckey:"a11y.d5c.description",url:"https://www.w3.org/TR/UNDERSTANDING-WCAG20/visual-audio-contrast-contrast.html",walkable:!0,check:function(e){var t=X.fromDom(e);return Mt(t)&&_t(t,7)},levels:[ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Ut=function(e){var t=Dt(e,"font-size"),n=new RegExp(".*px").test(t),r=t.match(/(\d*\.?\d*)(.*)/);return n&&null!==r?w.some(Number(r[1])):w.none()},Pt=function(e){var t,n,r=!("bold"!==(n=Dt(e,"font-weight"))&&"bolder"!==n)||!!/^[0-9]*$/.test(t=n)&&700<=parseInt(t,10),o=Nt(e),i=je(o),a=Ut(i);return Ut(e).fold(c,function(t){return a.fold(function(){return 24<=t||r&&19<=t},function(e){return 1.5*e<=t||r&&1.2*e<=t})})},Gt={id:"D5a",wcag:"1.4.3",severity:ce.error(),desckey:"a11y.d5a.description",url:"https://www.w3.org/TR/UNDERSTANDING-WCAG20/visual-audio-contrast-contrast.html",walkable:!0,check:function(e){var t=X.fromDom(e);return Mt(t)&&Pt(t)&&_t(t,3)},levels:[ue.aa()],htmlversions:[ae.html4(),ae.html5()]},Lt={id:"D5b",wcag:"1.4.3",severity:ce.error(),desckey:"a11y.d5b.description",url:"https://www.w3.org/TR/UNDERSTANDING-WCAG20/visual-audio-contrast-contrast.html",walkable:!0,check:function(e){var t=X.fromDom(e);return Mt(t)&&!Pt(t)&&_t(t,4.5)},levels:[ue.aa()],htmlversions:[ae.html4(),ae.html5()]},Wt=function(e,t){return n=function(e){return ee(e,t)},G(St(e),n);var n},qt=function(e,t){return n=t,o=void 0===(r=e)?f.document:r.dom(),te(o)?[]:U(o.querySelectorAll(n),X.fromDom);var n,r,o},Bt={id:"H93",wcag:"4.1.1",severity:ce.error(),desckey:"a11y.h93.description",url:"https://www.w3.org/TR/UNDERSTANDING-WCAG20/ensure-compat-parses.html",walkable:!1,check:function(e){var t=X.fromDom(e),n=qt(t,"*[id]"),r=L(n,function(e,t){var n,r,o=bt(t,"id");if(void 0===o)return e;if(Re(e.problems,o))return e;if(Re(e.ids,o)){var i=e.ids[o];return Fe(Fe({},e),{problems:Fe(Fe({},e.problems),(n={},n[o]=i,n))})}return Fe(Fe({},e),{ids:Fe(Fe({},e.ids),(r={},r[o]=t.dom(),r))})},{ids:{},problems:{}});return Se(r.problems)},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Vt={id:"I1",wcag:"1.1.1",severity:ce.error(),desckey:"a11y.i1.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/text-equiv-all.html",walkable:!0,check:function(e,t){var n=e.hasAttribute("alt"),r=n&&""===e.getAttribute("alt").trim(),o=e.hasAttribute("role")&&"presentation"===e.getAttribute("role");return se(e,"img")&&(!n||(t?n&&r&&!o||n&&!r&&o:r||o))},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},jt={id:"I2",wcag:"1.1.1",severity:ce.error(),desckey:"a11y.i2.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/text-equiv-all.html",walkable:!0,check:function(e){return se(e,"img[alt][src]")&&e.src.split("/").pop()===e.alt.split("/").pop()},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Ft={id:"T1",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.t1.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){if(se(e,"table")){var t=e.querySelector("caption");return!t||se(t,":empty")}return!1},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},zt={id:"T2",wcag:"1.3.1",severity:ce.warning(),desckey:"a11y.t2.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return se(e,"table")&&(e.querySelector("[rowspan]")||e.querySelector("[colspan]"))&&!e.hasAttribute("summary")},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4()]},Yt={id:"T3",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.t3.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return se(e,"table")&&e.hasAttribute("summary")&&0<e.querySelectorAll("caption").length&&e.getAttribute("summary")===e.querySelector("caption").innerHTML},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Xt={id:"T4b",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.t4b.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return"table"===e.nodeName.toLowerCase()&&0===e.querySelectorAll("th").length},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Kt={id:"T4c",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.t4c.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return"th"===e.nodeName.toLowerCase()&&(n=!1===(t=e).hasAttribute("scope"),r=!1===/row|col|rowgroup|colgroup/i.test(t.getAttribute("scope")),n||r);var t,n,r},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},$t=function(e,t){return 0===e.querySelectorAll(t).length},Jt={id:"T4a",wcag:"1.3.1",severity:ce.error(),desckey:"a11y.t4a.description",url:"http://www.w3.org/TR/UNDERSTANDING-WCAG20/content-structure-separation-programmatic.html",walkable:!0,check:function(e){return"table"===e.nodeName.toLowerCase()&&($t(t=e,"tr")||$t(t,"td"));var t},levels:[ue.a(),ue.aa(),ue.aaa()],htmlversions:[ae.html4(),ae.html5()]},Qt={all:g([le,fe,de,ge,be,Gt,Lt,Ht,Bt,Vt,jt,Ft,zt,Yt,Kt,Jt,Xt])},Zt=function(){return Qt.all().slice()},en=function(e){var t=e.getParam("a11y_advanced_options",!1,"boolean");return e.getParam("a11ychecker_allow_decorative_images",t,"boolean")},tn=[ue.a(),ue.aa(),ue.aaa()],nn=[ae.html4(),ae.html5()],rn=function(e,t){var n=5===t?"":"-"+t+"x";return e.getParam("a11ychecker_issue_url_callback",function(e){return"https://www.tiny.cloud/docs"+n+"/plugins/a11ychecker/#"+e},"function")},on={"a11y.d1.description":"This paragraph looks like a heading. If it is a heading, please select a heading level.","a11y.d1.repair.block.h1":"Heading 1","a11y.d1.repair.block.h2":"Heading 2","a11y.d1.repair.block.h3":"Heading 3","a11y.d1.repair.block.h4":"Heading 4","a11y.d1.repair.block.h5":"Heading 5","a11y.d1.repair.block.h6":"Heading 6","a11y.d1.repair.info":"Select a heading level:","a11y.d2.description":"Headings must be applied in sequential order. For example: Heading 1 should be followed by Heading 2, not Heading 3.","a11y.d3.description":"Adjacent links with the same URL should be merged into one link","a11y.d4.description":"The selected text appears to be a list. Lists should be formatted using a list tag.","a11y.d5a.description":"Large text must have a contrast ratio of at least 3:1","a11y.d5b.description":"Text must have a contrast ratio of at least 4.5:1","a11y.d5c.description":"Text must have a contrast ratio of at least 7:1","a11y.h93.description":"ID attribute must be unique","a11y.h93.repair.info":"Make ID unique","a11y.h93.repair.deduplicate.others":"Keep this ID and remove all others","a11y.h93.repair.deduplicate.this":"Remove this ID","a11y.h93.repair.deduplicate.all":"Remove all IDs","a11y.i1.description.decorativeAllowed":"Images must be marked as decorative or have an alternative text description","a11y.i1.description.decorativeNotAllowed":"Images must have an alternative text description. Decorative images are not allowed.","a11y.i1.repair.errors.duplicate":"Alternative text cannot be the same as the filename","a11y.i1.repair.errors.empty":"Alternative text cannot be empty","a11y.i1.repair.info.decorativeAllowed":"Or provide alternative text:","a11y.i1.repair.info.decorativeNotAllowed":"Provide alternative text:","a11y.i1.repair.checkboxlabel":"Make image decorative:","a11y.i1.repair.checkboxtext":"Image is decorative","a11y.i2.description":"Alternative text must not be the same as the image filename","a11y.i2.repair.info":"Provide alternative text:","a11y.t1.description":"Tables must have captions","a11y.t1.repair.errors.duplicate":"Table caption cannot be the same as the table summary","a11y.t1.repair.errors.empty":"Caption cannot be empty","a11y.t1.repair.info":"Provide caption:","a11y.t2.description":"Complex tables should have summaries","a11y.t2.repair.errors.duplicate":"Table summary cannot be the same as the table caption","a11y.t2.repair.errors.empty":"Summary cannot be empty","a11y.t2.repair.info":"Provide table summary:","a11y.t3.description":"Table caption and summary cannot have the same value","a11y.t4a.description":"Table elements must contain TR and TD tags","a11y.t4b.description":"Tables must have at least one header cell","a11y.t4b.repair.info":"Choose table header:","a11y.t4c.description":"Table headers must be applied to a row or a column","a11y.t4c.repair.header.col":"Header Column","a11y.t4c.repair.header.row":"Header Row","a11y.t4c.repair.info":"Select header scope:","a11y.t4c.repair.scope.col":"Column","a11y.t4c.repair.scope.colgroup":"Column Group","a11y.t4c.repair.scope.row":"Row","a11y.t4c.repair.scope.rowgroup":"Row Group","a11y.widget.counter":"Issue {0} of {1}","a11y.widget.ignore":"Ignore","a11y.widget.issue.none":"No accessibility issues detected","a11y.widget.next":"Next Issue","a11y.widget.previous":"Previous Issue","a11y.widget.legacyrepair":"Repair Issue","a11y.widget.repair":"Repair","a11y.widget.title":"Accessibility Checker","a11y.widget.help.title":"Click for more info",error:"Error",warning:"Warning",info:"Info",success:"Success"},an=function(e){return on[e]},un=function(e){return Array.isArray(e)?tinymce.translate([an(e[0])].concat(e.slice(1))):tinymce.translate(an(e))},cn=function(e,t,n){var r=bt(e,t).concat(" ",n);sn(e,t,r)},sn=function(e,t,n){wt(e,t,n)},ln=function(e,t){return xt(e,t)?bt(e,t).split(" "):[]},fn=function(e,t,n){(xt(e,t)?cn:sn)(e,t,n)},dn=function(t,n){At(t).each(function(e){e.dom().insertBefore(n.dom(),t.dom())})},pn=function(e,t){var n;(n=e,w.from(n.dom().nextSibling).map(X.fromDom)).fold(function(){At(e).each(function(e){mn(e,t)})},function(e){dn(e,t)})},mn=function(e,t){e.dom().appendChild(t.dom())},hn=function(t,e){P(e,function(e){mn(t,e)})},vn=function(e){var t=e.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},yn=function(e){var t,n=St(e);0<n.length&&(t=e,P(n,function(e){dn(t,e)})),vn(e)},gn=function(e){return ne(e)},wn=function(e,t){return n=function(e){return ee(e,t)},W(e.dom().childNodes,function(e){return n(X.fromDom(e))}).map(X.fromDom);var n},bn=function(e,t){return ne(t,e)},xn=function(e){return gn(e).isSome()},kn=(ze="ephox-foam".replace(/\./g,"-"),{resolve:function(e){return ze+"-"+e}}),Dn=kn.resolve("styles"),Tn="#"+Dn,Nn={resolve:kn.resolve,injectStyles:function(e){if(!xn(Tn)){var t=X.fromTag("link");wt(t,"id",Dn),wt(t,"rel","stylesheet"),wt(t,"href",e);var n=gn("head").getOrDie("Head element could not be found.");mn(n,t)}},removeStyles:function(){if(xn(Tn)){var e=gn("head").getOrDie("Head element could not be found."),t=bn(e,Tn).getOrDie("The style element could not be removed");vn(t)}}},An=function(e,t,n){wt(e,t,n)},Sn=function(e,n,t,r,o,i){P(e,function(t){var e=Rn(t.severity,r,o,i).toArray().concat([n]);P(e,function(e){wt(t.element,e,1)})}),w.from(e[0]).each(function(e){An(e.element,t,e.severity)})},Rn=function(e,t,n,r){switch(e){case"info":return w.some(t);case"warning":return w.some(n);case"error":return w.some(r);default:return w.none()}},In=function(e,t,n,r){var o=qt(e,"["+t+"]");P(o,function(e){kt(e,t)}),An(n,t,r)},Cn=function(e,n,r,o,i,a){var t=qt(e,"["+n+"]");P(t,function(t){P([n,r,o,i,a],function(e){kt(t,e)}),On(t,n,r)})},On=function(e,t,n){kt(e,t),kt(e,n)},En=function(r){var o=r.editor();return{start:function(e){Sn(e,r.violation(),r.current(),r.info(),r.warn(),r.error())},moveTo:function(e,t){var n=X.fromDom(o.getBody());In(n,r.current(),e,t)},stop:function(){var e=X.fromDom(o.getBody());Cn(e,r.violation(),r.current(),r.info(),r.warn(),r.error())},exclude:function(e){On(e,r.violation(),r.current())}}},_n="data-"+Nn.resolve("a11y-current-violation"),Mn="data-"+Nn.resolve("a11y-violation"),Hn="data-"+Nn.resolve("a11y-severity-info"),Un="data-"+Nn.resolve("a11y-severity-warn"),Pn="data-"+Nn.resolve("a11y-severity-error"),Gn="data-"+Nn.resolve("accessibility-ignore"),Ln=Oe(["editor","violation","current","info","warn","error"],[]),Wn={configure:function(t){t.on("init",function(e){P([Gn,Mn,Hn,Un,Pn,_n],function(e){t.serializer.addTempAttr(e)})})},ignoreAttr:g(Gn),manager:function(e){return En(Ln({editor:e,violation:Mn,current:_n,info:Hn,warn:Un,error:Pn}))}},qn=function(e){var t,n,r,o=(t=e.getParam("a11ychecker_ignored_rules","","string"),B(tinymce.explode(t),function(e){return 0<e.length?[e.toUpperCase()]:[]})),i=(n=e.getParam("a11ychecker_level","","string"),M(tn,n)?n:ue.aa()),a=(r=e.getParam("a11ychecker_html_version","","string"),M(nn,r)?r:ae.html4()),u=Zt();return{rules:G(u,function(e){return!M(o,e.id.toUpperCase())&&M(e.levels,i)&&M(e.htmlversions,a)}),allowDecorativeImages:en(e),ignoreSelectors:["img[data-mce-placeholder]","[data-mce-bogus=all] *","[data-mce-bogus]"]}},Bn=function(e){return{id:e.id.toUpperCase(),severity:e.severity,url:e.url,description:un(e.description),element:e.element.dom()}},Vn=function(o){var e=qn(o);Wn.configure(o);var t,n=m(!1),r=(t=e,{audit:function(e){return oe(e,t)}}),i=Wn.manager(o),a=Wn.ignoreAttr(),u=[],c=0,s=function(){var n,e=r.audit(o.getBody()),t=(n=o,U(e,function(e){var t=e.rule;return{id:t.id.toUpperCase(),description:"I1"!==t.id?t.desckey:en(n)?t.desckey+".decorativeAllowed":t.desckey+".decorativeNotAllowed",element:X.fromDom(e.element),url:t.url,severity:t.severity}}));return G(t,function(e){var t=e.element,n=ln(t,a),r=o.getParam("a11ychecker_filter_issue",h,"function");return!M(n,e.id)&&r(Bn(e))})},l=function(e){return c=(c+=e)<0?c+u.length:c%u.length,i.moveTo(u[c].element,u[c].severity),o.selection.scrollIntoView(u[c].element.dom()),{summaryLabel:un(["a11y.widget.counter",c+1,u.length]),issue:u[c],index:c+1}},f=function(){return 0<u.length?w.some(l(0)):w.none()},d=function(){n.set(!0),u=s(),c=0},p=function(){i.stop(),n.set(!1),c=0,u=[]};return{scan:function(){return d(),i.start(u),f()},stepIssue:l,getIssues:function(){return u.slice(0)},getCurrent:f,getReport:function(){var e=n.get();try{return e||d(),U(u,Bn)}finally{e||p()}},resolveCurrent:function(e){return e&&fn(u[c].element,a,u[c].id),u=s(),f()},clear:p,isAuditing:function(){return n.get()}}},jn={getInitialHtml:function(){return'<div role="presentation"></div><div style="clip: rect(1px, 1px, 1px, 1px);height: 1px;overflow: hidden;position: absolute;width: 1px;" aria-live="assertive" aria-relevant="additions" aria-atomic="true"></div>'},buildHtml:function(e,t){return'<div style="white-space:normal;">'+e+(t?'<button aria-label="Show specification details" role="button" style="display:inline;" tabindex="-1"><i class="mce-ico mce-i-help"></i><button>':"")+"</div>"},setSeverity:function(e,t){e.classes.remove("error"),e.classes.remove("warning"),e.classes.remove("success"),e.classes.add(t)}};function Fn(u){var c,t={},o=m(null),s=function(e,t,n){return Math.min(Math.max(e,t),n)},e=function(){var e,t,n,r,o=u.getBody();if(o&&c&&!0===c.state.get("visible")){var i=u.inline?u.getElement():u.getContentAreaContainer(),a=("BODY"===(r=o).nodeName&&(r=r.parentNode),r.scrollHeight>r.clientHeight?-25:-10);c.moveRel(i,"tr-tr"),c.moveBy(a,10),t=10,n=(e=c).layoutRect(),e.moveTo(s(n.x,0,f.window.innerWidth-n.w),s(n.y,t,f.window.innerHeight-n.h-t))}};return{actions:t,open:function(){(c=tinymce.ui.Factory.create("window",{title:un("a11y.widget.title"),spacing:5,padding:10,minWidth:300,layout:"flex",direction:"column",align:"stretch",items:[{type:"label",name:"index",text:"Index",padding:"0 0 5 0"},{type:"widget",classes:"widget infobox has-help",name:"description",onpostrender:function(e){e.control.getEl().innerHTML=jn.getInitialHtml()},onclick:function(e){if(o.get()){e.stopPropagation(),e.preventDefault();var t=o.get();t&&f.window.open(t)}}},{type:"container",layout:"flex",direction:"column",align:"stretch",name:"repairer",padding:"5 0 5 0",items:[]},{type:"label",name:"validation",hidden:!0,classes:"error"},{type:"container",name:"footer",layout:"flex",direction:"row",spacing:3,items:[{type:"button",name:"repair",text:un("a11y.widget.legacyrepair"),onclick:"repair"},{type:"button",text:un("a11y.widget.ignore"),onclick:"ignore"},{type:"spacer",flex:1},{type:"button",text:un("a11y.widget.previous"),onclick:"prev"},{type:"button",text:un("a11y.widget.next"),autofocus:!0,subtype:"primary",onclick:"next"}]},{type:"container",name:"closer",layout:"flex",direction:"row",hidden:!0,items:[{type:"spacer",flex:1},{type:"button",subtype:"primary",name:"close",text:"Close",onclick:function(){c.close()}}]}]})).on("submit",function(e){e.preventDefault(),t.repair()}),c.modal=!1,c.on("open",function(){e()}),c.on("close",function(){t.close()}),c.on("execute",function(e){t[e.action]()}),u.on("remove",function(){c&&c.close()}),c.renderTo().reflow(),c.moveTo(0,0)},close:function(){c&&(c.close(),c=null)},reposition:e,find:function(e){return c.find(e)},setIndexText:function(e){c.find("#index").show().text(e)},hideIndex:function(){c.find("#index").hide()},setDescriptionText:function(e,t,n){var r=c.find("#description")[0].show();jn.setSeverity(r,t),o.set(n),r.getEl().firstChild.innerHTML=jn.buildHtml(e,n),r.updateLayoutRect(),r.reflow(),D.setEditorTimeout(u,function(){r.getEl().lastChild.textContent=e})},setRepairerChildren:function(e){var t;(t=c.find("#repairer")[0]).items().remove(),t.append(e),t.renderNew()},hideFooter:function(){c.find("#footer").hide()},showCloser:function(){var e=c.find("#closer")[0];e.show(),e.focus()},hideRepairer:function(){c.find("#repairer").hide()},showRepairer:function(){c.find("#repairer").show()},hideRepairButton:function(){c.find("#repair").hide()},showRepairButton:function(){c.find("#repair").show()},disableUi:function(e){c.find(e).disabled(!0)},enableUi:function(e){c.find(e).disabled(!1)},setValidationInformation:function(e){c.find("#validation").show().text(e)},hideValidation:function(){c.find("#validation").hide()},retrieveInputValue:function(e){return c.find("#"+e).value()}}}var zn=function(n){return{is:function(e){return n===e},isValue:h,isError:c,getOr:g(n),getOrThunk:g(n),getOrDie:g(n),or:function(e){return zn(n)},orThunk:function(e){return zn(n)},fold:function(e,t){return t(n)},map:function(e){return zn(e(n))},mapError:function(e){return zn(n)},each:function(e){e(n)},bind:function(e){return e(n)},exists:function(e){return e(n)},forall:function(e){return e(n)},toOption:function(){return w.some(n)}}},Yn=function(n){return{is:c,isValue:c,isError:h,getOr:u,getOrThunk:function(e){return e()},getOrDie:function(){return e=String(n),function(){throw new Error(e)}();var e},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,t){return e(n)},map:function(e){return Yn(n)},mapError:function(e){return Yn(e(n))},each:a,bind:function(e){return Yn(n)},exists:c,forall:h,toOption:w.none}},Xn={value:zn,error:Yn,fromOption:function(e,t){return e.fold(function(){return Yn(t)},zn)}},Kn=function(e){return t=e,n=!0,X.fromDom(t.dom().cloneNode(n));var t,n},$n=function(e,t,n){var r=Nt(e),o=X.fromTag(n[t],r.dom()),i=St(e),a=U(i,Kn);hn(o,a);var u=qt(o,"b,strong");return P(u,yn),dn(e,o),vn(e),Xn.value(o)},Jn=function(e){var t=X.fromDom(e.dom().previousSibling);if(t){var n=St(e);hn(t,n),vn(e)}return Xn.value(t)},Qn=function(t,e,n,r){var o=n[e],i="all"===o||"this"===o;if("all"===o||"others"===o){var a=bt(t,"id"),u=r.getBody(),c=X.fromDom(u),s=qt(c,'*[id="'+a+'"]'),l=G(s,function(e){return!gt(e,t)});P(l,function(e){kt(e,"id")})}return i&&kt(t,"id"),Xn.value(t)},Zn=function(e,t){return Xn.error(t(e))},er=function(e,t,n,r,o){return t===n?Zn(r,o):Xn.value(e)},tr=function(e,t,n,r,o,i){return 0===t.length?Zn(r,i):er(e,t,n,o,i)},nr=function(e){var t=e.split("/");return 0<t.length?t.pop():""},rr=function(e,t,n,r,o,i){var a,u,c,s=(a=e,u="src",w.from(bt(a,u))).map(nr).getOr(""),l=n[t+"_textinput"];return n[t+"_checkbox"]?(wt(c=e,"alt",""),wt(c,"role","presentation"),Xn.value(c)):tr(e,l,s,r,o,i).map(function(e){return wt(e,"alt",l),"presentation"===bt(e,"role")&&kt(e,"role"),e})},or=function(e){return e.dom().textContent},ir=function(a,u,c,e,t,n){var r=bt(a,"summary");return tr(a,c[u],r,e,t,n).map(function(e){var t,n,r,o,i=wn(a,"caption").fold(function(){return X.fromTag("caption")},function(e){var t;return(t=e).dom().textContent="",P(St(t),function(e){vn(e)}),e});return t=i,n=c[u],t.dom().textContent=n,o=i,Rt(r=e).fold(function(){mn(r,o)},function(e){r.dom().insertBefore(o.dom(),e.dom())}),e})},ar=function(t,n,r,e,o,i){var a=bn(t,"caption").filter(Be).map(or).getOr("");return tr(t,r[n],a,e,o,i).map(function(e){return wt(t,"summary",r[n]),e})},ur=function(e,t,n){var r=[];if("row"===n[t]){var o=X.fromDom(e.dom().rows[0]);r=Wt(o,"td")}else"col"===n[t]&&P(e.dom().rows,function(e){wn(X.fromDom(e),"td").each(function(e){r.push(e)})});return P(r,function(t){var n=X.fromTag("th");P(t.dom().attributes,function(e){wt(n,e.name,bt(t,e.name))}),n.dom().innerHTML=t.dom().innerHTML,pn(t,n),vn(t)}),Xn.value(e)},cr=function(e,t,n){return wt(e,"scope",n[t]),Xn.value(e)},sr=function(e,t){return{type:"label",forName:e,text:t}},lr=function(e,t,n){return{type:"listbox",name:e,values:U(t,function(e){return{text:n(e.text),value:e.value}})}},fr=function(e,t){return{type:"textbox",name:e,value:t}},dr=function(e,t){return{type:"checkbox",name:e,text:t}},pr=Oe(["ui","repair","handler"],[]),mr=function(e,t){var n=e.id,r="#"+n+"_textinput",o=t.find(r)[0],i="#"+n+"_checkbox",a=t.find(i)[0],u=function(){!0===a.value()?(t.disableUi(r),t.enableUi("#repair")):(t.enableUi(r),0<o.value().length?(t.enableUi("#repair"),t.enableUi(i)):(t.enableUi(i),t.disableUi("#repair")))};o.on("input",u),a.on("change",u),u()},hr=function(e,t,n,r){var o,i=bt(r,"alt"),a=[sr(t+"_textinput_label",n((o="a11y.i1.repair.info",e?o+".decorativeAllowed":o+".decorativeNotAllowed"))),fr(t+"_textinput",i)];return e?q([[sr(t+"_checkbox_label",n("a11y.i1.repair.checkboxlabel")),dr(t+"_checkbox",n("a11y.i1.repair.checkboxtext"))],a]):a},vr=function(e,t,n,r,o){var i,a,u=((i={})[r+"_textinput"]=t.retrieveInputValue(r+"_textinput"),i),c=e?((a={})[r+"_checkbox"]=t.retrieveInputValue(r+"_checkbox"),a):{},s=Fe(Fe({},u),c);return rr(n,r,s,"a11y.i1.repair.errors.empty","a11y.i1.repair.errors.duplicate",o)};function yr(o,i){var a,u,c,s=rn(o,4),l=(c=en(u=o),{H93:w.some(pr({ui:w.some(function(e,t){return[sr("H93_selectbox_label",e("a11y.h93.repair.info")),lr("H93_selectbox",[{text:"a11y.h93.repair.deduplicate.others",value:"others"},{text:"a11y.h93.repair.deduplicate.this",value:"this"},{text:"a11y.h93.repair.deduplicate.all",value:"all"}],e)]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("H93_selectbox");return Qn(e,t,{H93:o},u)},handler:w.none()})),I1:w.some(pr({ui:w.some(function(e,t){return hr(c,"I1",e,t)}),repair:function(e,t,n,r){return vr(c,n,e,t,r)},handler:c?w.some(mr):w.none()})),I2:w.some(pr({ui:w.some(function(e,t){return hr(c,"I2",e,t)}),repair:function(e,t,n,r){return vr(c,n,e,t,r)},handler:c?w.some(mr):w.none()})),T1:w.some(pr({ui:w.some(function(e,t){return[sr("table-caption",e("a11y.t1.repair.info")),fr("table-caption","")]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("table-caption");return ir(e,t,{T1:o},"a11y.t1.repair.errors.empty","a11y.t1.repair.errors.duplicate",r)},handler:w.none()})),T2:w.some(pr({ui:w.some(function(e,t){return[sr("table-summary",e("a11y.t2.repair.info")),fr("table-summary","")]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("table-summary");return ar(e,t,{T2:o},"a11y.t2.repair.errors.empty","a11y.t2.repair.errors.duplicate",r)},handler:w.none()})),T3:w.some(pr({ui:w.some(function(e,t){var n=wn(t,"caption").fold(function(){return""},function(e){return e.dom().innerHTML});return[sr("table-caption",e("a11y.t1.repair.info")),fr("table-caption",n)]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("table-caption");return ir(e,t,{T3:o},"a11y.t1.repair.errors.empty","a11y.t1.repair.errors.duplicate",r)},handler:w.none()})),T4A:w.none(),T4B:w.some(pr({ui:w.some(function(e,t){return[sr("table-headers",e("a11y.t4b.repair.info")),lr("table-headers",[{text:"a11y.t4c.repair.header.row",value:"row"},{text:"a11y.t4c.repair.header.col",value:"col"}],e)]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("table-headers");return ur(e,t,{T4B:o})},handler:w.none()})),T4C:w.some(pr({ui:w.some(function(e,t){return[sr("scope-type",e("a11y.t4c.repair.info")),lr("scope-type",[{text:"a11y.t4c.repair.scope.row",value:"row"},{text:"a11y.t4c.repair.scope.col",value:"col"},{text:"a11y.t4c.repair.scope.rowgroup",value:"rowgroup"},{text:"a11y.t4c.repair.scope.colgroup",value:"colgroup"}],e)]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("scope-type");return cr(e,t,{T4C:o})},handler:w.none()})),D1:w.some(pr({ui:w.some(function(e,t){return[sr("heading-type",e("a11y.d1.repair.info")),lr("heading-type",[{text:"a11y.d1.repair.block.h1",value:"h1"},{text:"a11y.d1.repair.block.h2",value:"h2"},{text:"a11y.d1.repair.block.h3",value:"h3"},{text:"a11y.d1.repair.block.h4",value:"h4"},{text:"a11y.d1.repair.block.h5",value:"h5"},{text:"a11y.d1.repair.block.h6",value:"h6"}],e)]}),repair:function(e,t,n,r){var o=n.retrieveInputValue("heading-type");return $n(e,t,{D1:o})},handler:w.none()})),D2:w.none(),D3:w.some(pr({ui:w.none(),repair:function(e,t,n,r){return Jn(e)},handler:w.none()}))}),e=function(){n(),i.scan().fold(function(){return p()},function(e){return r(e.issue,e.index)})},t=function(){a&&a.close()},n=function(){(a=Fn(o)).actions.close=function(){i.clear()},a.actions.ignore=function(){d(!0)},a.actions.prev=v,a.actions.next=y,a.open()},f=function(){a&&a.reposition()},r=function(t,e){a.hideValidation();var n,r=s(t.id);a.setIndexText(un(["a11y.widget.counter",e,i.getIssues().length])),a.setDescriptionText(un(t.description),t.severity,r),(n=t.id,void 0!==l[n]?l[n]:w.none()).fold(m,function(e){return h(e,t)}),f(),o.selection.scrollIntoView(t.element.dom())},d=function(e){i.resolveCurrent(e).fold(function(){return p()},function(e){r(e.issue,e.index)}),f()},p=function(){a.hideIndex(),a.hideRepairer(),a.hideValidation(),a.hideFooter(),a.showCloser(),a.setDescriptionText(un("a11y.widget.issue.none"),"success")},m=function(){a.hideRepairer(),a.hideRepairButton()},h=function(e,r){e.ui().fold(m,function(e,t,n){a.setRepairerChildren(e(un,r.element)),a.showRepairer()}),a.showRepairButton(),e.handler().each(function(e){return e(r,a)}),a.actions.repair=function(){o.undoManager.transact(function(){e.repair()(r.element,r.id,a,un).fold(function(e){a.setValidationInformation(e),f()},function(){return d(!1)})})}},v=function(){var e=i.stepIssue(-1);r(e.issue,e.index)},y=function(){var e=i.stepIssue(1);r(e.issue,e.index)};return{toggleAuditing:function(){i.isAuditing()?t():e()},startAuditing:e,stopAuditing:t,positionDialog:f}}var gr,wr,br=function(e,t){var n=t.ui.registry.getAll().icons;return w.from(n[e]).orThunk(function(){return w.from(n["temporary-placeholder"])}).getOr("!not found!")},xr=function(e,t,n,r,o){var i,a,u,c=un(n),s=un(t);return'<div class="accessibility-issue__description"><div><div><div class="tox-icon">'+br("info"===n?"info":"warning",e)+"</div><h2>"+c+"</h2></div><p>"+s+"</p></div><div>"+(i=r,a=e,u='<a href="'+o+'" title="'+un("a11y.widget.help.title")+'" class="tox-button tox-button--naked tox-button--icon" target="_blank" data-alloy-tabstop="true" tabindex="-1"><div class="tox-icon">'+br(i,a)+"</div></a>","help"===i?u:'<div class="tox-icon">'+br(i,a)+"</div>")+"</div></div>"},kr=function(e,t,n,r){return{type:"selectbox",label:t,name:e,items:U(n,function(e){return{text:r(e.text),value:e.value}})}},Dr=function(e,t){return{type:"input",name:e,label:t}},Tr=function(e,t,n){return{type:"label",label:t,items:[{type:"checkbox",name:e,label:n}]}},Nr=function(e){return{type:"htmlpanel",html:'<div class="tox-form__group--error">'+e+"</div>"}},Ar=function(e,t){return{title:un("a11y.widget.title"),body:{type:"panel",items:[{type:"panel",classes:["tox-accessibility-issue","accessibility-issue--success"],items:[{type:"htmlpanel",html:xr(e,"a11y.widget.issue.none","success","checkmark"),presets:"presentation"}]}]},buttons:[{type:"submit",name:"ok",text:"OK",primary:!0}],onSubmit:function(e){return e.close()},onClose:t}},Sr=function(e,t,n){e.redial(t),e.focus(n)};(wr=gr||(gr={})).Next="next",wr.Prev="prev",wr.Ignore="ignore",wr.Ok="ok",wr.Repair="repair";var Rr=function(e,c,s,i,l,f){var d=e.issue,p=e.summaryLabel,u=rn(l,5),m=function(e){return Rr(e,c,s,w.none(),l,f)},h=function(u){return function(t,e){var n,r,o,i=e.name;e.value;if(i===gr.Next||i===gr.Prev){var a=c.stepIssue(i===gr.Next?1:-1);Sr(t,m(a),i)}else i===gr.Ignore?c.resolveCurrent(!0).fold(function(){return Sr(t,Ar(l,f),gr.Ok)},function(e){return Sr(t,m(e),gr.Ignore)}):i===gr.Repair&&(n=t,r=function(e){Sr(n,Rr({issue:d,summaryLabel:p},c,s,w.some(e),l,f),gr.Repair)},o=function(){return c.resolveCurrent(!1).fold(function(){return c.scan().fold(function(){return Sr(n,Ar(l,f),gr.Ok)},function(e){return Sr(n,m(e),gr.Ignore)})},function(e){return Sr(n,m(e),gr.Ignore)})},u.each(function(e){e.repair(d.element,d.id,Ae(n.getData(),function(e,t){return n=t,r=d.id,o=n,a=0,""===(i=r)||!(o.length<i.length)&&o.substr(a,a+i.length)===i;var n,r,o,i,a}).t,function(e){return e.fold(r,o)})}))}},a=function(e,t,n){var r,o,i="warning"===(r=d.severity)?"warn":r,a=t.map(function(){return{type:"panel",classes:["accessibility-issue__repair"],items:t.map(function(e){return e.items}).getOr([])}}).toArray();return{title:un("a11y.widget.title"),body:{type:"panel",classes:["tox-accessibility-issue","accessibility-issue--"+i],items:function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var r=Array(e),o=0;for(t=0;t<n;t++)for(var i=arguments[t],a=0,u=i.length;a<u;a++,o++)r[o]=i[a];return r}([{type:"panel",classes:["accessibility-issue__header"],items:[{type:"htmlpanel",html:"<h1>"+p+"</h1>",presets:"presentation"},{type:"button",name:gr.Prev,text:un("a11y.widget.previous"),icon:"action-prev",borderless:!0},{type:"button",name:gr.Next,text:un("a11y.widget.next"),icon:"action-next",borderless:!0}]},{type:"htmlpanel",html:xr(l,d.description,d.severity,"help",u(d.id)),presets:"presentation"}],a)},buttons:[{type:"custom",name:gr.Ignore,text:un("a11y.widget.ignore")},{type:"custom",name:gr.Repair,text:un("a11y.widget.repair"),disabled:n,primary:!0}],initialData:Fe({},t.map(function(e){return e.initialData}).getOr({})),onAction:h(e),onChange:(o=e,function(t){return o.each(function(e){e.onChange.each(function(e){return e(t,t.getData(),d)}),e.shouldDisableRepair.each(function(e){e(t.getData(),d)?t.disable(gr.Repair):t.enable(gr.Repair)})})}),onClose:f}};return s(d.id).fold(function(){return a(w.none(),w.none(),!0)},function(e){var t,r,n=(t=e,r=d,i.fold(function(){return t.ui.map(function(e){return e(r.id,r.element)})},function(n){return t.ui.map(function(e){var t=e(r.id,r.element);return{items:t.items.concat([Nr(n)]),initialData:t.initialData}})})),o=e.shouldDisableRepair.bind(function(t){return n.map(function(e){return t(e.initialData,d)})}).getOr(!1);return a(w.some(e),n,o)})},Ir=function(e){return e+"_checkbox"},Cr=function(e){return e+"_textinput"},Or=function(e,t){var n=t.id,r=!0===e[Ir(n)],o=0<e[Cr(n)].length;return!r&&!o},Er=function(e,t,n){var r=n.id,o=Ir(r),i=Cr(r);!0===t[o]?(e.disable(i),e.enable(gr.Repair)):(e.enable(i),0<t[i].length?(e.enable(gr.Repair),e.enable(o)):(e.enable(o),e.disable(gr.Repair)))},_r=function(e,t){var n,r=Dr(Cr(t),un((n="a11y.i1.repair.info",e?n+".decorativeAllowed":n+".decorativeNotAllowed")));return e?[Tr(Ir(t),un("a11y.i1.repair.checkboxlabel"),un("a11y.i1.repair.checkboxtext")),r]:[r]},Mr=function(e,t,n){var r,o,i=bt(e,"alt"),a=t?((r={})[Ir(n)]=!1,r):{},u=((o={})[Cr(n)]=i||"",o);return Fe(Fe({},u),a)},Hr=function(s,l){var f=m(w.none()),d=function(){f.set(w.none()),l.clear()},e=function(){var t,n,r,o,i,a,u,c,e=(t=function(e){return s.windowManager.open(e,{inline:"toolbar",ariaAttrs:!0})},n=l,u=en(a=s),c={H93:w.some({ui:w.some(function(e,t){var n;return{items:[kr(e,un("a11y.h93.repair.info"),[{text:"a11y.h93.repair.deduplicate.others",value:"others"},{text:"a11y.h93.repair.deduplicate.this",value:"this"},{text:"a11y.h93.repair.deduplicate.all",value:"all"}],un)],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(Qn(e,t,n,a))})},shouldDisableRepair:w.none(),onChange:w.none()}),I1:w.some({ui:w.some(function(e,t){return{items:_r(u,e),initialData:Mr(t,u,e)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(rr(e,t,n,"a11y.i1.repair.errors.empty","a11y.i1.repair.errors.duplicate",un))})},shouldDisableRepair:u?w.some(Or):w.none(),onChange:u?w.some(Er):w.none()}),I2:w.some({ui:w.some(function(e,t){return{items:_r(u,e),initialData:Mr(t,u,e)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(rr(e,t,n,"a11y.i1.repair.errors.empty","a11y.i1.repair.errors.duplicate",un))})},shouldDisableRepair:u?w.some(Or):w.none(),onChange:u?w.some(Er):w.none()}),T1:w.some({ui:w.some(function(e,t){var n;return{items:[Dr(e,un("a11y.t1.repair.info"))],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(ir(e,t,n,"a11y.t1.repair.errors.empty","a11y.t1.repair.errors.duplicate",un))})},shouldDisableRepair:w.none(),onChange:w.none()}),T2:w.some({ui:w.some(function(e,t){var n;return{items:[Dr(e,un("a11y.t2.repair.info"))],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(ar(e,t,n,"a11y.t2.repair.errors.empty","a11y.t2.repair.errors.duplicate",un))})},shouldDisableRepair:w.none(),onChange:w.none()}),T3:w.some({ui:w.some(function(e,t){var n,r=wn(t,"caption").fold(function(){return""},function(e){return e.dom().innerHTML});return{items:[Dr(e,un("a11y.t1.repair.info"))],initialData:(n={},n[e]=r,n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(ir(e,t,n,"a11y.t1.repair.errors.empty","a11y.t1.repair.errors.duplicate",un))})},shouldDisableRepair:w.none(),onChange:w.none()}),T4A:w.none(),T4B:w.some({ui:w.some(function(e,t){var n;return{items:[kr(e,un("a11y.t4b.repair.info"),[{text:"a11y.t4c.repair.header.row",value:"row"},{text:"a11y.t4c.repair.header.col",value:"col"}],un)],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(ur(e,t,n))})},shouldDisableRepair:w.none(),onChange:w.none()}),T4C:w.some({ui:w.some(function(e,t){var n;return{items:[kr(e,un("a11y.t4c.repair.info"),[{text:"a11y.t4c.repair.scope.row",value:"row"},{text:"a11y.t4c.repair.scope.col",value:"col"},{text:"a11y.t4c.repair.scope.rowgroup",value:"rowgroup"},{text:"a11y.t4c.repair.scope.colgroup",value:"colgroup"}],un)],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r(cr(e,t,n))})},shouldDisableRepair:w.none(),onChange:w.none()}),D1:w.some({ui:w.some(function(e,t){var n;return{items:[kr(e,un("a11y.d1.repair.info"),[{text:"a11y.d1.repair.block.h1",value:"h1"},{text:"a11y.d1.repair.block.h2",value:"h2"},{text:"a11y.d1.repair.block.h3",value:"h3"},{text:"a11y.d1.repair.block.h4",value:"h4"},{text:"a11y.d1.repair.block.h5",value:"h5"},{text:"a11y.d1.repair.block.h6",value:"h6"}],un)],initialData:(n={},n[e]="",n)}}),repair:function(e,t,n,r){a.undoManager.transact(function(){r($n(e,t,n))})},shouldDisableRepair:w.none(),onChange:w.none()}),D2:w.none(),D3:w.some({ui:w.none(),repair:function(e,t,n,r){a.undoManager.transact(function(){r(Jn(e))})},shouldDisableRepair:w.none(),onChange:w.none()})},r=function(e){return c.hasOwnProperty(e)?c[e]:w.none()},o=s,i=d,n.scan().fold(function(){return t(Ar(o,i))},function(e){return t(Rr(e,n,r,w.none(),o,i))}));f.set(w.some(e))},t=function(e){return e.close()};return s.ui.registry.addButton("a11ycheck",{tooltip:un("a11y.widget.title"),icon:"accessibility-check",onAction:e}),s.ui.registry.addMenuItem("a11ycheck",{icon:"accessibility-check",text:un("a11y.widget.title"),onAction:e}),function(){return f.get().fold(e,t)}},Ur=function(t,e){if(x(tinymce,"4.3.13"))return k('The "a11ychecker" plugin requires at least 4.3.13 version of TinyMCE.'),function(){};var n,r,o,i=Vn(t),a=t.a11ychecker_css_url,u=a||e+"/css/annotations.css";return x(tinymce,"5.0.0")?(o=yr(r=t,i),r.addButton("a11ycheck",{tooltip:un("a11y.widget.title"),icon:"a11y",onclick:o.toggleAuditing}),r.addMenuItem("a11ycheck",{context:"tools",icon:"a11y",text:un("a11y.widget.title"),onclick:o.toggleAuditing}),r.on("ResizeWindow",function(){D.requestAnimationFrame(function(){o.positionDialog()})}),r.on("ScrollWindow",function(){o.positionDialog()}),r.on("ResizeEditor",function(){o.positionDialog()}),r.on("click",function(){o.stopAuditing()}),r.on("keydown",function(){o.stopAuditing()}),n=o.toggleAuditing):n=Hr(t,i),t.on("init",function(e){t.dom.loadCSS(u)}),t.on("remove",function(e){1===tinymce.editors.length&&Nn.removeStyles()}),{getReport:i.getReport,toggleaudit:n}};tinymce.PluginManager.requireLangPack("a11ychecker","ar,bg_BG,ca,cs,da,de,el,es,eu,fa,fi,fr_FR,he_IL,hr,hu_HU,id,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_PT,pt_BR,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_TW,zh_CN"),tinymce.PluginManager.add("a11ychecker",Ur)}(window);