/*
 * @Author: 冉志诚
 * @Date: 2023-07-11 14:00:29
 * @LastEditTime: 2023-07-11 14:09:27
 * @FilePath: \frontend\src\constant\Request.ts
 * @Description:
 */
/**
 * @description 接口相关常量
 */
export namespace Request {
  /**
   * @description 接口前缀
   */
  export enum PREFIX {
    RMAN = '/rman',
    UP_FORM = '/unifiedplatform',
    LIVE_MANAGE = '/livemanage',
    SUPERVISION = '/supervision',
    IPINGESTMAN = '/ipingestman',
    LEARN = '/learn',
    CHATDOC = '/chatdoc',
    TRANSLATION = '/translation',
  }
  export enum CODE {
    SUCCESS_LIVE_MANAGE = 'livemanage.0000.0000',
  }
  export enum LIVE_TYPE {
    ACTIVITY = '1',
    COURSE = '2',
  }
}
