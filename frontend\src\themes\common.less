body {

  .ant-input,
  .ant-input-affix-wrapper,
  .ant-btn:not(.ant-btn-round, .ant-btn-circle),
  .ant-select:not(.ant-select-customize-input) .ant-select-selector {
    border-radius: 6px;
  }

  .ant-pagination-item,
  .ant-pagination-options-quick-jumper input {
    border-radius: 5px;
  }

  .ant-btn-link:not(:disabled),
  .ant-btn-text:not(:disabled) {
    color: #525252;

    &:hover {
      color: var(--primary-color);
      background: transparent;
    }
  }

  // .ant-menu-root {
  //   padding: 20px 13px;
  // }
  .ant-menu-sub.ant-menu-inline {
    background: transparent;
  }

  .ant-menu-item::after {
    display: none;
  }

}
