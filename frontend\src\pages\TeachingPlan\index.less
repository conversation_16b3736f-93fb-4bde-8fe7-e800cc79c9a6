.teaching-plan {
  height: 100%;
  .content {
    width: 100%;
    display: flex;
    height: 100%;
    padding: 30px;
    background: url(../../assets/img/teachingPlan/content_bg.png)
      no-repeat;
    background-position: center;
    .left {
      width: 452px;
      height: 100%;
      border-radius: 20px;
      box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
      background: url(../../assets/img/teachingPlan/left_bg.png)
        no-repeat;
      background-size: 100% 100%;
      padding: 15px;
      margin-right: 30px;
      .left-title {
        width: 100%;
        padding: 15px;
        display: flex;
        align-items: center;
        img {
          &:nth-child(1) {
            margin-right: 17px;
          }
        }
      }
      .teaching-plan-mode {
        padding: 15px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        .mode-item {
          width: 48%;
          padding: 15px;
          height: 50px;
          display: flex;
          align-items: center;
          background: #ffffff;
          box-shadow: 2px 2px 11px 0px
            rgba(192, 192, 192, 0.5);
          border-radius: 25px;
          font-weight: 400;
          font-size: 16px;
          color: #2e2e2e;
          cursor: pointer;
          img {
            margin-right: 5px;
          }
        }
        .active-mode-item {
          background: linear-gradient(
            319deg,
            #7a43ff 0%,
            #7c91fc 100%
          );
          color: #fff;
        }
      }
      .set-teaching-plan-content {
        height: calc(100% - 300px);
        overflow-y: auto;
        margin-bottom: 20px;
        padding: 0 15px 15px 15px;
        .template-content {
          margin-top: 15px;
          .template-title {
            display: flex;
            align-items: center;
            font-weight: 600;
            font-size: 18px;
            color: #2e2e2e;
            margin-bottom: 10px;
            img {
              margin-right: 5px;
            }
          }
          .template-list {
            background: #ffffff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            padding: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            .template-item {
              display: flex;
              flex-direction: column;
              width: 31.3%;
              height: 126px;
              padding: 5px;
              border-radius: 6px;
              border: 1px solid #e2e2e2;
              margin-bottom: 15px;
              .ant-image,
              .ant-image-mask {
                width: 100%;
                height: 80px;
                border-radius: 5px;
              }
            }
          }
        }
        .require-set-content {
          .add-box {
            min-height: 150px;
          }
          .add-box,
          .add-content {
            // min-height: 150px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            font-size: 14px;
            color: #2e2e2e;
            margin-bottom: 15px;
            .file-list {
              .file-item {
                display: flex;
                align-items: center;
                height: 37px;
                border-radius: 6px;
                border: 1px dashed #d4ceff;
                padding-right: 10px;
                margin: 10px 0;
                .file-name {
                  width: 100%;
                  white-space: nowrap; /* 不换行 */
                  overflow: hidden; /* 超出隐藏 */
                  text-overflow: ellipsis; /* 超出部分显示省略号 */
                }
                .anticon-close-circle {
                  margin-left: auto;
                }
              }
            }
            .add-btn {
              display: flex;
              align-items: center;
              color: #7a53fe;
              margin-top: 20px;
              cursor: pointer;
              .anticon {
                margin-right: 5px;
              }
            }
          }
          .add-box {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 16px;
            cursor: pointer;
            &:hover {
              color: #7a43ff;
            }
            .anticon {
              margin-right: 8px;
              font-size: 18px;
              color: #5c43ff;
              font-weight: bold;
            }
          }
          .text-input-aera {
            padding: 20px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            border-radius: 15px;
            font-size: 14px;
            color: #2e2e2e;
            margin-bottom: 20px;
            .header {
              display: flex;
              align-items: center;
              margin-bottom: 5px;
              span {
                color: #868686;
              }
            }
            .ant-input {
              padding-left: 0;
            }
          }
          .cailiao-upload {
            height: 150px;
            padding: 15px;
            border-radius: 15px;
            background-color: #fff;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            color: #2e2e2e;
            margin-bottom: 15px;
            .ant-upload-drag {
              height: 60px;
              margin-bottom: 20px;
              border-radius: 5px;
              .ant-upload-btn {
                display: flex;
                justify-content: center;
                align-items: center;
              }
              .ant-upload-drag-container {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
              }
              .ant-upload-drag-icon {
                font-size: 16px;
                display: flex;
                align-items: center;
                margin-bottom: 0px;
                .anticon {
                  margin-right: 8px;
                  font-size: 18px;
                  color: #5c43ff;
                  font-weight: bold;
                }
              }
            }
            .ant-upload-text {
              font-size: 14px;
              color: #868686;
              margin: 2px 0;
              a {
                color: #7a43ff;
                text-decoration: underline;
              }
            }
          }
        }
      }
      .ant-btn {
        width: 100%;
        height: 50px;
        background: linear-gradient(
          319deg,
          #7a43ff 0%,
          #7c91fc 100%
        );
        border-radius: 25px;
        color: #fff;
        font-weight: 600;
        font-size: 18px;
      }
    }
    .right {
      width: calc(100% - 482px);
      height: 100%;
      border-radius: 20px;
      box-shadow: 0px 0px 40px 0px rgba(106, 117, 133, 0.15);
      background: url(../../assets/img/teachingPlan/right_bg.png)
        no-repeat;
      background-size: 100% 100%;
      padding: 15px;
      .plan-content {
        height: calc(100% - 30px);
        .right-header {
          display: flex;
          align-items: center;
          font-weight: 600;
          font-size: 18px;
          color: #2e2e2e;
          margin: 15px;
          img {
            margin-right: 10px;
          }
          .ant-btn {
            margin-left: auto;
            background: linear-gradient(
              319deg,
              #5c43ff 0%,
              #7c91fc 100%
            );
            border-radius: 25px;
            color: #fff;
            width: 139px;
            height: 50px;
          }
        }
        .topic-list {
          padding: 15px;
          height: calc(100% - 50px);
          overflow-y: auto;
          background-color: #fff;
          .topic-item {
            padding: 5px;
            border-radius: 10px;
            box-shadow: 2px 2px 11px 0px
              rgba(192, 192, 192, 0.5);
            margin-bottom: 20px;
            .topic-header {
              height: 72px;
              background: #eceffe;
              border-radius: 10px;
              display: flex;
              align-items: center;
              padding: 20px;
              .topic-type {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #ffffff;
                margin-right: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #5c43ff;
              }
              .topic-title {
                font-weight: 500;
                font-size: 18px;
                color: #2e2e2e;
              }
              .ant-space {
                margin-left: auto;
                .ant-btn {
                  width: 110px;
                  height: 40px;
                  background: #ffffff;
                  border-radius: 20px;
                }
                .btn-primary {
                  background: linear-gradient(
                    319deg,
                    #7a43ff 0%,
                    #7c91fc 100%
                  );
                  color: #fff;
                }
              }
            }
            .topic-content {
              padding: 20px;
            }
          }
        }
      }
      .empty {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: -34px;
        p {
          font-weight: 600;
          font-size: 24px;
          color: #2e2e2e;
          margin-top: -50px;
        }
      }
      .loading-page {
        padding: 40px;
        .loading-header {
          display: flex;
          align-items: center;
          margin-bottom: 30px;
          font-weight: 600;
          font-size: 18px;
          color: #2e2e2e;
          img {
            margin-right: 6px;
          }
        }
        .loading-content {
          height: 320px;
          background: url(../../assets//img/teachingPlan/tikuang_bg.png)
            no-repeat;
          background-size: 100% 100%;
          display: flex;
          align-items: center;
          font-weight: 600;
          font-size: 24px;
          color: #2e2e2e;
          .animation-content {
            width: 352px;
            height: 270px;
          }
        }
      }
    }
  }
}
.map-modal-wrapper {
  .ant-modal-body {
    padding-top: 0;
    .ant-tabs-nav {
      margin: 0;
    }
  }
}
.chapter-modal-wrapper {
  .chapter-content {
    .ant-pagination {
      display: flex;
      justify-content: center;
    }
    .chapter-list {
      margin-top: 20px;
      margin-bottom: 20px;
      .chapter-card-checked {
        background-color: #ded9ff;
        border-color: #7a43ff;
      }
      .chapter-card-checked::after {
        opacity: 1;
        border: 10px solid #7a43ff;
        border-block-end: 10px solid transparent;
        border-inline-start: 10px solid transparent;
        border-start-end-radius: 6px;
      }
      .chapter-card {
        display: flex;
        padding: 10px;
        border: 1px dashed #e0e0e0;
        border-radius: 6px;
        cursor: pointer;
        height: 140px;
        &::after {
          position: absolute;
          inset-block-start: 4px;
          inset-inline-end: 12px;
          width: 0;
          height: 0;
          opacity: 1;
          transition: all 0.3s
            cubic-bezier(0.645, 0.045, 0.355, 1);
          border-block-end: 10px solid transparent;
          border-inline-start: 10px solid transparent;
          border-start-end-radius: 6px;
          content: '';
        }
        img {
          width: 150px;
          height: 110px;
          margin-right: 5px;
        }
        .card-content {
          width: calc(100% - 150px);
          font-size: 14px;
          color: #868686;
          p {
            margin: 0;
          }
          .title {
            color: #2e2e2e;
            font-size: 16px;
            font-weight: 500;
            display: -webkit-box; /* 创建伸缩盒子模型 */
            -webkit-box-orient: vertical; /* 垂直排列子元素 */
            -webkit-line-clamp: 2; /* 显示两行 */
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 省略号 */
            /* 兼容性更好可以加上下面两行 */
            word-break: break-word;
            white-space: normal;
          }
          .semester,
          .teacher,
          .school {
            width: 100%;
            white-space: nowrap; /* 不换行 */
            overflow: hidden; /* 超出隐藏 */
            text-overflow: ellipsis; /* 超出部分显示省略号 */
          }
        }
      }
    }
  }
}
.chapter-modal-tree-wrapper {
  .course-name {
    font-size: 16px;
    margin-bottom: 10px;
  }
}
