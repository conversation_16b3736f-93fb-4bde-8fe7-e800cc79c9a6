/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:29:57
 * @LastEditTime: 2023-09-14 10:30:05
 * @FilePath: \frontend\src\pages\Translate\index.tsx
 * @Description:
 */
import Header, {
  CUSTOMER_NPU,
} from '@/components/header/header';
import Left from './components/Left';
import React, { useEffect, useState } from 'react';
import style from './index.less';
import {
  Affix,
  Button,
  Empty,
  Input,
  message,
  Upload,
} from 'antd';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
import FilePreview from '@/components/FilePreview';
import './reset.less';
import {
  ClassNames,
  blobToFile,
  changeBlobType,
  downloadBlob,
  fileToBlob,
  getBlobTypeToSuffix,
  getWords,
  isFileFormat,
  isFileSize,
  openFileDialog,
  sleep,
  stringToBlob,
  suffixToBlobType,
  txtToString,
} from '@/utils/utils';
import { DraggerProps } from 'antd/lib/upload/Dragger';
import LoadingIcon from '@/images/loading.png';
import { TranslateService } from '@/api/translate';

import { InfoModelType } from '@/db/models/infoModel';
import { Info } from '@/db/controllers/info';
import ChatContent from './components/ChatContent';
import { Chatdoc } from '@/api/chatdoc';
import { AI } from '@/constant/ai';
import { useSelector } from 'umi';
const Dragger = Upload.Dragger;

const Retrieval: React.FC<TranslateProps> = ({
  children,
}) => {
  const [isEmpty, setIsEmpty] = useState(false);
  const [text, setText] = useState('');
  const [selectedKey, setSelectedKey] = useImmer<
    React.Key | undefined
  >(undefined);
  const [documentData, setDocumentData] = useImmer<
    Partial<InfoModelType>
  >({
    after: undefined,
  });

  // 应该查询数据库
  useEffect(() => {
    setDocumentData({
      before: undefined,
      after: undefined,
    });
  }, [selectedKey]);
  const [loading, setLoading] = useImmer({
    submit: false,
    render: false,
  });

  async function getData(id: number) {
    const result = await Info.getResultById(id);
    if (result) {
      setDocumentData(result);
      if (!result.contentId) {
        setLoading((draft) => {
          draft.submit = true;
        });
        try {
          const { doc_id } = await Chatdoc.uploadFile(
            blobToFile(result.before!, result.title),
          );
          if (!doc_id) {
            throw new Error('上传失败');
          }
          //! 写入contentID
          setDocumentData((draft) => {
            draft.contentId = doc_id;
          });
          await Info.updateResult({
            ...result,
            contentId: doc_id,
          });
        } catch (error) {
          //@ts-ignore
          message.error(error.message);
        }
        setLoading((draft) => {
          draft.submit = false;
        });
      }
    }
  }
  useEffect(() => {
    (async () => {
      setLoading((draft) => {
        draft.render = true;
      });
      if (selectedKey) {
        await getData(selectedKey as number);
      }
      setLoading((draft) => {
        draft.render = false;
      });
    })();
    setText('');
  }, [selectedKey]);
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  const showOther =
    parameterConfig?.target_customer === CUSTOMER_NPU;
  return (
    <div
      id="translate"
      className={ClassNames(
        showOther ? 'other-tr-header' : '',
      )}>
      <Header showNav={true} customTitle="文档信息检索" />
      <div className={style.content}>
        <Left
          pageKey={AI.RETRIEVAL}
          createTitle="本地上传"
          setIsEmpty={setIsEmpty}
          selectedKey={selectedKey}
          setSelectedKey={setSelectedKey}
        />
        <main>
          {isEmpty || !selectedKey ? (
            <div className={style.empty}>
              <Empty
                description={
                  <div className={style.desc_empty}>
                    <p>请在左侧选择或新增上传</p>
                    <p>
                      上传文档，分析完成后您将可以针对文档内容进行提问，帮助您高效获取文档信息。
                    </p>
                    <p>上传要求：PDF文档，20M以下</p>
                  </div>
                }
              />
            </div>
          ) : (
            <div  className={style.renderContent}>
              <div
                className={style.before}
                id="translate-before">
                <div className={style.showArea}>
                  <Access
                    accessible={!!documentData.before}>
                    <Access accessible={!loading.submit}>
                      <FilePreview
                        blob={documentData.before}
                      />
                    </Access>
                    <Access accessible={loading.submit}>
                      <div className={style.loading}>
                        <img src={LoadingIcon} />
                        <p> 上传中... </p>
                        <p>请耐心等待</p>
                      </div>
                    </Access>
                  </Access>
                </div>
              </div>
              <div
                className={style.after}
                id="translate-after">
                <Affix
                  offsetTop={0}
                  target={() => {
                    return document.querySelector(
                      '#translate-after',
                    ) as HTMLElement;
                  }}>
                  <h2 className={style.title}>
                    <p>{documentData.title}</p>
                  </h2>
                </Affix>
                <div
                  className={style.showArea}
                  style={{ padding: 0 }}>
                  <ChatContent
                    historyId={documentData.id}
                    emptyDesc="无学习资料无法开始问答"
                    sessionId={documentData.contentId}
                    defaultMessage="欢迎使用文档信息检索!"
                  />
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

interface TranslateProps {
  children: React.ReactNode;
}
export default Retrieval;
Retrieval.displayName = 'Translate';
