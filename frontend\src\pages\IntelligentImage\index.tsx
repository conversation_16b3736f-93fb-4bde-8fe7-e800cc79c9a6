/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:29:57
 * @LastEditTime: 2023-09-14 10:30:05
 * @FilePath: \frontend\src\pages\IntelligentImage\index.tsx
 * @Description:
 */
import Header from '@/components/header/header';
import React, { useEffect, useState } from 'react';
import style from './index.less';
import {
  Affix,
  Button,
  Empty,
  Input,
  message,
  Upload,
} from 'antd';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
// import './reset.less';
import {
  blobToFile,
  changeBlobType,
  fileToBlob,
  getBlobTypeToSuffix,
  getWords,
  stringToBlob,
  suffixToBlobType,
  txtToString,
} from '@/utils/utils';
import { CloudUploadOutlined, UploadOutlined } from '@ant-design/icons';
import { DraggerProps } from 'antd/lib/upload/Dragger';
import LoadingIcon from '@/images/IntelligentImage.png';
import downloadIcon from '@/images/download.png';
import copyIcon from '@/images/copy.png';
import regerateIcon from '@/images/regerate.png';

import { IntelligentImageService } from '@/api/IntelligentImage';
import {
  getResultById,
} from '@/db/controllers/Result';
const Dragger = Upload.Dragger;

const IntelligentImage: React.FC = () => {
  const [isEmpty, setIsEmpty] = useState(false);
  const [text, setText] = useState('');
  const [selectedKey, setSelectedKey] = useImmer<
    React.Key | undefined
  >(undefined);
  const [renderBlob, setRenderBlob] = useImmer<{
    before?: Blob;
    after?: Blob;
  }>({
    before: undefined,
    after: undefined,
  });
  // 应该查询数据库
  useEffect(() => {
    setRenderBlob({
      before: undefined,
      after: undefined,
    });
  }, [selectedKey]);
  const [loading, setLoading] = useImmer({
    submit: false,
    render: false,
  });
  const [result, setResult] = useImmer<boolean>(false);
  const [file, setFile] = useImmer<any>(null);
  const [imgUrl, setImgUrl] = useImmer<string>('')
  async function submit(blob: Blob, file?: any) {
    setLoading((draft) => { draft.submit = true; });
    // 更新数据库
    try {
      // const suffix = getBlobTypeToSuffix(blob);
      let afterBlob: Blob | undefined;
      let title: string | undefined;
  
      title = file.name;
      const {
        data,
        message,
      } = await IntelligentImageService.uploadFile(
        blobToFile(blob, file.name),
      );
      setImgUrl(data)
      IntelligentImageService.ocrTask({
        taskUid: file.uid,
        imagePath: window.location.origin + data
      }).then((res) => {
        const timer = setInterval(async () => {
          const res = await IntelligentImageService.getOcrTask({ taskUid: file.uid })
          console.log(res, 'res')
          if (res?.data.extend_message[0].task_status === 3) {
            clearInterval(timer);
            let str = ''
            res?.data.extend_message[0].result.forEach((item: { words: string; }) => {
              str += item.words
            })
            setText(str)
            setResult(true)
            // const temp = res?.data.data.map((item: any, index: number) => {
            //   return {
            //     ...item,
            //     selectFlag: false, // 选中标记
            //   };
            // });
            // setProduct(temp);
            // setRelationVideoVisible(true);
            setLoading((draft) => { draft.submit = false; });
          }
        }, 500)
      })
      console.log(data, 'data')
    } catch (error) {
      setRenderBlob((draft) => {
        draft.before = undefined;
      });
      //@ts-ignore
      message.error(error.message);
    }

  }
  const draggerConfig = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.jpg,.jpeg,.png',
    onChange(info) {
      setRenderBlob((draft) => { draft.before = info.file.originFileObj });
      setFile(info.file)
      submit(
        fileToBlob(info.file.originFileObj),
        info.file,
      );
    },
    beforeUpload: (file) => {
      // 大于10mb
      if (file.size > 2 * 1024 * 1024) {
        message.error('文件超过2MB');
        return false;
      }
      return true;
    },
    className: style.dragger,
    // ...uploadProps,
  };
  const uploadProps = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.jpg,.jpeg,.png',
    onChange(info) {
      setResult(false)
      setRenderBlob((draft) => { draft.before = info.file.originFileObj });
      setFile(info.file)
      submit(
        fileToBlob(info.file.originFileObj!),
        info.file,
      );
    },
    beforeUpload: (file) => {
      // 大于10mb
      if (file.size > 10 * 1024 * 1024) {
        message.error('文件超过10MB');
        return false;
      }
      return true;
    },
  };
  async function getData(id: number) {
    const result = await getResultById(id);
    if (result) {
      setRenderBlob({
        before: result.before,
        after: result.after,
      });
    }
  }
  const uploadDefault = () => {
    return true
  }
  const copyText = () => {
    const input = document.createElement('input');
    input.setAttribute('readonly', 'readonly');
    input.setAttribute('value', 123);
    document.body.appendChild(input);
    input.select();
    if (document.execCommand('copy')) {
      document.execCommand('copy');
      message.success(' 复制成功')
    } 
    document.body.removeChild(input);
  }
  const downLoadText = () => {
    const blob = new Blob([text], {
      type: "text/plain;charset=utf-8"
    })
    const objectURL = URL.createObjectURL(blob)
    const aTag = document.createElement('a')
    aTag.href = objectURL
    aTag.download = "文本文件.txt"
    aTag.click()
    URL.revokeObjectURL(objectURL)
  }
  const regenerate = () => {
    setResult(false)
    submit(renderBlob.before, file)
  }
  return (
    <div className={style.retrieval}>
      <Affix offsetTop={0}>
        <Header showNav={true} customTitle="智能识图工具" />
      </Affix>
      <div className={style.content}>
        <main>
          <div className={style.renderContent}>
            <div className={style.showArea}>
              <Access accessible={!loading.submit && !result}>
                <div className={style.new}>
                <div className={style.topTip}>
                      <p className={style.title}>智能识图工具</p>
                      <p style={{ margin: '15px 0' }}>上传图片，帮助您高效获取图片中的文字信息。</p>
                      <p>
                        上传要求：请上传jpg、png格式图片。
                      </p>
                    </div>
                  <Dragger {...draggerConfig} customRequest={uploadDefault}>
                    <p className="ant-upload-drag-icon">
                      <CloudUploadOutlined />
                    </p>
                    <p className={style.uploadText}>
                      选择本地文档
                      <span className={style.strong}>
                        或拖拽至此区域
                      </span>
                    </p>
                  </Dragger>
                </div>
              </Access>
              <Access accessible={loading.submit && !result}>
                <div className={style.loading}>
                  <img src={LoadingIcon} />
                  <p>智能分析中...</p>
                </div>
              </Access>
              <Access accessible={!loading.submit && result}>
                <div className={style.result}>
                  <div className={style['result-left']}>
                    <div className={style["btn-upload"]}>
                    <Upload {...uploadProps} customRequest={uploadDefault}>
                      <Button icon={<UploadOutlined />} type='primary'>重新上传</Button>
                    </Upload>
                    </div>
                    <div className={style["img-preview"]}>
                      <img src={imgUrl} alt="img" />
                    </div>
                  </div>
                  <div className={style['result-right']}>
                    <div className={style["content-text"]}>
                      <div className={style["text-title"]}>以下是在图片中识别到的文字信息:</div>
                      <div className={style["text"]}>{text}</div>
                    </div>
                    <div className={style["btn-list"]}>
                      <div onClick={copyText}>
                        <img src={copyIcon} alt="" />复制文本
                      </div>
                      <div onClick={downLoadText}><img src={downloadIcon} alt="" />下载文本</div>
                      <div onClick={regenerate}><img src={regerateIcon} alt="" />重新生成</div>
                    </div>
                  </div>
                </div>
              </Access>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default IntelligentImage;
IntelligentImage.displayName = 'IntelligentImage';
