import request from "./request";
// 获取内容权限列表
async function getContentTemplate() {
    return request(`/unifiedplatform/v1/content/template/get/list`, {
        method: 'GET'
    })
}
// 获取角色内容权限
async function getRoleContentList(code: string) {
    return request(`/unifiedplatform/v1/content/template/get/role/authorization?role_code=${code}`, {
        method: 'GET'
    })
}
// 获取角色内容权限
async function addRoleContentList(data: any) {
    return request(`/unifiedplatform/v1/content/template/add/role/authorization`, {
        method: 'POST',
        data
    })
}
let permission = {
    getContentTemplate,
    getRoleContentList,
    addRoleContentList
}

export default permission