declare namespace Setting {
  export interface IPage {
    pageGuid: string;
    style: string;
    backgroundPicture: string;
    logo: string;
    title: string;
    subtitle: string;
    themeColor: string;
    defaultJumpAddress: string;
    addressGuid: string;
  }

  export interface IIdentity {
    order: number;
    key: string;
    identityGuid: string;
    name: string;
    code: string;
    jumpAddress: string;
    fieldSettingGuid?: string;
    // fieldSetting: string
  }

  export interface IField {
    fieldGuid: string;
    authenticationMode: number;
    loginUrl: string;
    smsUrl: string;
    imageVerificationCode: boolean;
    needIdentityChoose: boolean;
    addressGuid: string;
    identityInfos: IIdentity[];
  }

  export interface IAddress {
    addressGuid?: string;
    address: string;
    isDefault: boolean;
  }

  export interface IItem extends IAddress {
    pageSetting: IPage;
    fieldSetting: IField;
  }

  export interface ISetting {
    addressInfo: IItem;
  }

  export interface LoginParams {
    captcha?: string;
    captchaKey?: string;
    loginName?: string;
    password?: string;
    phone?: string;
    phoneCaptcha?: string;
    identity?: string;
  }
}
