import React from 'react';
import './index.less';
import icon2 from '@/images/sanhang/icon2.png';
import { Popover } from 'antd';

interface ExtensionInformationProps {
  keyword?: string;
}

const ExtensionInformation: React.FC<ExtensionInformationProps> = ({
  keyword,
}) => {

  const showdetail = (text: string) => {
    return (
      <div className="ExtensionInformation_popover_content">
        <div className="popover_title">{text}</div>
        <div className="popover_text">这是关于{text}的详细描述。</div>
      </div>
    );
  };

  const createSpan = (text: string) => {
    const parts = text.split(new RegExp(`(${keyword})`, 'g'));

    return (
      <span className="subject_value">
        {parts.map((part, index) =>
          part === keyword ? (
            <Popover content={showdetail(keyword)} trigger="hover" key={index}>
              <span style={{ color: '#0546D2', cursor: 'pointer' }}>{part}</span>
            </Popover>
          ) : (
            part
          ),
        )}
      </span>
    );
  };

  return (
    <div className="extension_information">
      <div className="title_section">
        <img className='square_icon' src={icon2} />
        <span className="title_text">扩展信息</span>
      </div>
      <div className="content_section">
        <div className="subject_item">
          <span className="subject_label">所属学科:</span>
          <span className="subject_value">军队指挥学 — 航空宇航科学与技术 — 军用飞机设计</span>
        </div>
        <div className="subject_item">
          <span className="subject_label">名词释义:</span>
          {createSpan(
            '“轰炸指挥飞机”指的是一种在轰炸行动中用于指挥和协调轰炸任务的飞机。这类飞机通常配备了先进的通信、导航和指挥控制系统，以便对参与轰炸的其他飞机进行有效的指挥和引导。它们的作用是确保轰炸行效性和安全性。',
          )}
        </div>
        <div className="subject_item">
          <span className="subject_label">应用场景:</span>
          {createSpan(
            '例如，在大规模的军事轰炸行动中，轰炸指挥飞机可以实时获取各架执行轰炸任务飞机的位置、状态和弹药消耗等信息，并根据战场态势做出调整指令，比如指示某些飞机改变轰炸目标或重新规划轰炸路线。',
          )}
        </div>
        <div className="subject_item">
          <span className="subject_label">相关知识:</span>
          <div className="knowledge_list">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((item, index) => (
              <span className="knowledge_item" key={index}>知识点{item}</span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExtensionInformation;