# mam-timecode-convert ，时码转换

## 注意
因为原来这个js 同时对外透露 了 TimeCodeConvert和timecodeconvert 2个对象， 实际这2个都是同一个东西。 但历史代码中 2个对象都在使用。目前阶段还没有修正这个。


> 其他详情可问 刘总。
## 1-1.秒转帧
### 调用方式
``` js
TimeCodeConvert.second2Frame(dbSec, dFrameRate);
timecodeconvert.second2Frame(dbSec, dFrameRate);
```
### 说明：
* 作用：用于将秒数转换成帧。
* 参数：dbSec--秒数，dFrameRate--帧率。
* 返回值：(number)帧数。

***
## 1-2.秒转帧
### 调用方式
``` js
TimeCodeConvert.second2Frame$1(dbSec, dRate, dScale);
timecodeconvert.second2Frame$1(dbSec, dRate, dScale);
```
### 说明：
* 作用：用于将秒数转换成帧。
* 参数：dbSec--秒数，dRate--帧率，dScale--修正值。
* 返回值：(number)帧数。

***
## 2-1.帧转百纳秒
### 调用方式
``` js
TimeCodeConvert.frame2L100Ns$1(lFrame, dRate, dScale);
timecodeconvert.frame2L100Ns$1(lFrame, dRate, dScale);
```
### 说明：
* 作用：用于将帧转换成百纳秒。
* 参数：lFrame--帧，dRate--帧率，dScale--修正值。
* 返回值：(number)百纳秒。

***
## 2-2.帧转百纳秒
### 调用方式
``` js
TimeCodeConvert.frame2L100Ns(dFrame, dFrameRate);
timecodeconvert.frame2L100Ns(dFrame, dFrameRate);
```
### 说明：
* 作用：用于将帧转换成百纳秒。
* 参数：dFrame--帧，dFrameRate--帧率。
* 返回值：(number)百纳秒。

***
## 3-1.帧转秒
### 调用方式
``` js
TimeCodeConvert.frame2Second$1(lFrame, dRate, dScale);
timecodeconvert.frame2Second$1(lFrame, dRate, dScale);
```
### 说明：
* 作用：用于将帧转换成秒。
* 参数：lFrame--帧，dRate--帧率，dScale--修正值。
* 返回值：(number)秒。

***
## 3-2.帧转秒
### 调用方式
``` js
TimeCodeConvert.frame2Second(dFrame, dFrameRate);
timecodeconvert.frame2Second(dFrame, dFrameRate);
```
### 说明：
* 作用：用于将帧转换成秒。
* 参数：dFrame--帧，dFrameRate--帧率。
* 返回值：(number)秒。

***
## 4-1.帧转时码
### 调用方式
``` js
TimeCodeConvert.frame2Tc$1(dFrame, dRate, dScale, dropFrame);
timecodeconvert.frame2Tc$1(dFrame, dRate, dScale, dropFrame);
```
### 说明：
* 作用：用于将帧转换成时码。
* 参数：dFrame--帧，dRate--帧率，dScale--修正值，是否丢帧(true/false)。
* 返回值：(string)时码字符串。

***
## 4-2.帧转时码
### 调用方式
``` js
TimeCodeConvert.frame2Tc(dFrame, dFrameRate, dropFrame);
timecodeconvert.frame2Tc(dFrame, dFrameRate, dropFrame);
```
### 说明：
* 作用：用于将帧转换成时码。
* 参数：dFrame--帧，dFrameRate--帧率，dropFrame--是否丢帧(true/false)。
* 返回值：(string)时码字符串。

***
## 5-1.时码字符串转帧
### 调用方式
``` js
TimeCodeConvert.timeCode2Frame$1(sTimeCode, frameRate, dRate, dScale, dropFrame);
timecodeconvert.timeCode2Frame$1(sTimeCode, frameRate, dRate, dScale, dropFrame);
```
### 说明：
* 作用：用于将时码转换成帧。
* 参数：sTimeCode--时码，frameRate--帧率，dRate--?，dScale--修正值，dropFrame--是否丢帧(true/false)。
* 返回值：(number)帧。

***
## 5-2.时码字符串转帧
### 调用方式
``` js
TimeCodeConvert.timeCode2Frame(sTimeCode, dFrameRate, dropFrame);
timecodeconvert.timeCode2Frame(sTimeCode, dFrameRate, dropFrame);
```
### 说明：
* 作用：用于将时码转换成帧。
* 参数：sTimeCode--时码，dFrameRate--帧率，dropFrame--是否丢帧(true/false)。
* 返回值：(number)帧。

***
## 6.获取帧率和修正值
### 调用方式
``` js
TimeCodeConvert.frameRate2RateAndScale(dFrameRate, dRate, dScale);
timecodeconvert.frameRate2RateAndScale(dFrameRate, dRate, dScale);
```
### 说明：
* 作用：用于获取帧率和修正值。
* 参数：dFrameRate--输入帧率，dRate--修正帧率，dScale--修正值。
* 返回值：(void)无返回值。

***
## 7-1.百纳秒转帧
### 调用方式
``` js
TimeCodeConvert.l100Ns2Frame$1(l100Ns, dRate, dScale);
timecodeconvert.l100Ns2Frame$1(l100Ns, dRate, dScale);
```
### 说明：
* 作用：用于将百纳秒转换成帧。
* 参数：l100Ns--百纳秒，dRate--帧率，dScale--修正值。
* 返回值：(number)帧。

***
## 7-2.百纳秒转帧
### 调用方式
``` js
TimeCodeConvert.l100Ns2Frame(l100Ns, dFrameRate);
timecodeconvert.l100Ns2Frame(l100Ns, dFrameRate);
```
### 说明：
* 作用：用于将百纳秒转换成帧。
* 参数：l100Ns--百纳秒，dFrameRate--帧率。
* 返回值：(number)帧。

***
## 8.百纳秒转秒
### 调用方式
``` js
TimeCodeConvert.l100Ns2Second(l100Ns);
timecodeconvert.l100Ns2Second(l100Ns);
```
### 说明：
* 作用：用于将百纳秒转换成秒。
* 参数：l100Ns--百纳秒。
* 返回值：(number)秒。

***
## 9-1.百纳秒转时码
### 调用方式
``` js
TimeCodeConvert.l100Ns2Tc(l100Ns, dropFrame);
timecodeconvert.l100Ns2Tc(l100Ns, dropFrame);
```
### 说明：
* 作用：用于将百纳秒转换成时码。
* 参数：l100Ns--百纳秒，dropFrame--是否丢帧(true/false)。
* 返回值：(string)时码字符串。

***
## 9-2.百纳秒转时码
### 调用方式
``` js
TimeCodeConvert.l100Ns2Tc$1(l100Ns, dFrameRate, dropFrame);
timecodeconvert.l100Ns2Tc$1(l100Ns, dFrameRate, dropFrame);
```
### 说明：
* 作用：用于将百纳秒转换成时码。
* 参数：l100Ns--百纳秒，dFrameRate--帧率，dropFrame--是否丢帧(true/false)。
* 返回值：(string)时码字符串。

***
## 10.帧率修正
### 调用方式
``` js
TimeCodeConvert.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);
timecodeconvert.rate2ScaleFrameRateAndFrameScale(dRate, dScale, dFrameRate, dFrameScale);
```
### 说明：
* 作用：用于帧率修正。
* 参数：dRate--帧率，dScale--修正值，dFrameRate--输出帧率，dFrameScale--输出修正值。
* 返回值：(void)无返回值。

***
## 11-1.格式化时码字符串
### 调用方式
``` js
TimeCodeConvert.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);
timecodeconvert.formatTimeCodeString(hours, minutes, seconds, frames, dropFrame);
```
### 说明：
* 作用：用于将时码格式化。
* 参数：hours--小时数，minutes--分钟数，seconds--秒数，frames--帧数，dropFrame--是否丢帧(true/false)。
* 返回值：(string)格式化后的时码字符串。

***
## 11-2.格式化时码字符串
### 调用方式
``` js
TimeCodeConvert.formatTimeCodeString$1(timeCode, dFrameRate, dropFrame);
timecodeconvert.formatTimeCodeString$1(timeCode, dFrameRate, dropFrame);
```
### 说明：
* 作用：用于将时码格式化。
* 参数：timeCode--时码，dFrameRate--帧率，dropFrame--是否丢帧(true/false)。
* 返回值："--:--:--:--"。

***
## 12.递归解决时码丢帧的问题
### 调用方式
``` js
TimeCodeConvert.getFrameByTimeCode(sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame);
timecodeconvert.getFrameByTimeCode(sTimeCode, ftcFrames, isAdded, corrValue, dFrameRate, dropFrame);
```
### 说明：
* 作用：用于解决时码丢帧的问题。
* 参数：sTimeCode--时码，ftcFrames--?， isAdded--是否加修正值(false减，true加），corrValue--修正值，dFrameRate--帧率，dropFrame--是否丢帧(true/false)。
* 返回值：(number)ftcNewFrames。

***
## 13.获取此帧率是否丢帧
### 调用方式
``` js
TimeCodeConvert.getRateDropFrame(rate);
timecodeconvert.getRateDropFrame(rate);
```
### 说明：
* 作用：用于判断是否丢帧。
* 参数：rate--帧率。
* 返回值：(boolean)true/false。

***
## 14.时间字符串转秒(未考虑丢帧的情况)
### 调用方式
``` js
TimeCodeConvert.timeCode2NdfFrame(sTimeCode, dFrameRate);
timecodeconvert.timeCode2NdfFrame(sTimeCode, dFrameRate);
```
### 说明：
* 作用：用于将时码转换成秒。
* 参数：sTimeCode--时码，dFrameRate--帧率。
* 返回值：(number)帧数。

***
## 15.时间字符串格式化
### 调用方式
``` js
TimeCodeConvert.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);
timecodeconvert.timeCode2Format(sTimeCode, lHour, lMinute, lSecond, lFrame, dFrameRate);
```
### 说明：
* 作用：用于将时码格式化。
* 参数：sTimeCode--时码，lHour--时，lMinute--分，lSecond--秒，lFrame--，dFrameRate--帧率。
* 返回值：(void)。

***
## 16.音频时间格式转换
### 调用方式
``` js
TimeCodeConvert.SecondToTimeString_audio(seconds, opts);
timecodeconvert.SecondToTimeString_audio(seconds, opts);
```
### 说明：
* 作用：用于将秒数转换成时间格式。
* 参数：seconds--秒，opts--。
* 返回值："--:--:--:--"。