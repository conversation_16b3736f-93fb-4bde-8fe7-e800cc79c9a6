.card {
  flex: 1;
  margin-right: 26px;
  padding: 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  box-sizing: border-box;
  border: 1px solid transparent;
  max-width: 270px;
  min-width: 270px;
  position: relative;
  margin-bottom: 31px;
  &:hover {
    border: 1px solid rgba(24, 143, 255, 0.425);
  }
  .icon {
    // width: 48px;
    height: 48px;
  }
  .logo {
    position: absolute;
    top: 20px;
    right: 6px;
    height: 24px;
  }
  h3 {
    font-size: 16px;
    font-weight: 700;
    color: #2e2e2e;
    line-height: 22px;
    margin: 10px 0;
  }
  p {
    font-size: 12px;
    font-weight: 400;
    color: #525252;
    line-height: 22px;
  }
}
[class~='custom-mobile'] {
  @gap: 1.5rem;
  .card {
    flex: calc(50% - @gap);
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s;
    box-sizing: border-box;
    border: 0.0625rem solid transparent;
    position: relative;
    max-width: 50%;
    min-width: none;
    margin-right: 0;
    padding: 0.875rem;
    margin-bottom: @gap;
    // 奇数个
    &:nth-child(odd) {
      margin-right: @gap;
    }
    // 偶数个
    &:nth-child(even) {
      margin-right: 0;
    }

    &:hover {
      border: 0.0625rem solid rgba(24, 143, 255, 0.425);
    }
    .icon {
      // width: 3rem;
      height: 3rem;
    }
    .logo {
      position: absolute;
      top: 1.25rem;
      right: 0.375rem;
      height: 1.5rem;
    }
    h3 {
      font-size: 1rem;
      font-weight: 700;
      color: #2e2e2e;
      line-height: 1.375rem;
      margin: 0.625rem 0;
    }
    p {
      font-size: 0.75rem;
      font-weight: 400;
      color: #525252;
      line-height: 1.375rem;
    }
  }
}
