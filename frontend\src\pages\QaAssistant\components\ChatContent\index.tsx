import IconFont from '@/components/iconFont/iconFont';
import React, {
  FC,
  useEffect,
  useRef,
  useState,
} from 'react';
import ChatItem from '../ChatItem';
import {
  DEFAULT_CHAT_TITLE,
  defaultAssistantMessage,
  defaultSchoolMessage,
} from '@/constant/assistant';
import { Button, Input, Spin } from 'antd';
import './index.less';
import {
  getSearch,
  reqChatList,
  sendMessage,
  updateChat,
} from '@/api/assistant';
import { getUuid } from '@/utils/utils';
import { dealCourse, getStorageByKey, updateStorage, verifyObjComplete } from '../../utils';
import { useLocation } from 'umi';

const { TextArea } = Input;

interface ChatProps {
  isOut: boolean;
  chatUser: string;
  sessionId: string;
  titleChange: () => void;
  chatStateChange: (isChat: boolean) => void;
}

const ChatContent: FC<ChatProps> = ({
  isOut,
  chatUser,
  sessionId,
  titleChange,
  chatStateChange,
}) => {
  const [inputValue, setInputValue] = useState<string>('');
  const [list, setList] = useState<any[]>([]);
  const [chatMessage, setChatMessage] = useState<
    string | null
  >(null); // 当前机器正在回答的内容
  const [chatData, setChatData] = useState<any>({});
  const [title, setTitle] = useState<string>('');
  const coRef = useRef(null);
  const [defaultMessage, setDefaultMessage] = useState<
    Array<{ type: string; text: string; }>
  >([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [extendMsgs, setExtendMsgs] = useState<any>({});
  const location: any = useLocation();

  useEffect(() => {
    if (sessionId && chatUser) {
      getList();
      if (chatUser.includes("qa")) {
        const extend = getStorageByKey(chatUser, sessionId);
        setExtendMsgs(extend);
      }
    }
  }, [sessionId, chatUser]);
  useEffect(() => {
    if (chatUser) {
      const isSchool = chatUser.includes('school');
      setDefaultMessage(
        isSchool
          ? defaultSchoolMessage
          : defaultAssistantMessage,
      );
    }
  }, [chatUser]);

  const getList = () => {
    const param = {
      user: chatUser,
      session_id: sessionId,
    };
    return reqChatList(param).then((res: any) => {
      const temp =
        res?.messages?.map((message: any) => ({
          ...message,
          contents: `${message.contents
            .map((item: any) => item.sentence)
            .join('')}${message.author.role === "assistant" && message.contents.length>0 ? `\n（来自于${message.contents[0]?.source}）` : ""}`,
        })) ?? [];
      setList(temp);
      setChatData(res ?? {});
      setTitle(res?.title ?? DEFAULT_CHAT_TITLE);
      setTimeout(() => {
        handleToBottom();
      });
    });
  };
  const handleInputChange = (e: any) => {
    setInputValue(e.target.value);
  };

  const handleToBottom = () => {
    const co: any = coRef.current;
    if (co) {
      co.scrollTop = co.scrollHeight;
    }
  };
  const handleSubmit = (t?: string) => {
    if (!inputValue && !t || loading) {
      return;
    }
    let input = t ?? inputValue;
    const pMsgId =
      list.length > 0
        ? list[list.length - 1].message_id
        : '';
    const msgId = getUuid();
    let data:any = {
      session_id: sessionId,
      message_id: msgId,
      parent_message_id: pMsgId,
      author: { name: chatUser, role: 'user' },
      message_type: 'query',
      contents: [
        {
          content_type: 'text',
          sentence: input
        },
      ],      
    };
    
    if(location.pathname == '/schoolAssistant'){
      data.runtime_env = {
        type_id:305
      }
    }

    setList([
      ...list,
      {
        ...data,
        contents: input
      },
    ]);
    if (!chatData.title) {
      updateChat({
        user: chatUser,
        session_id: sessionId,
        title: input,
        domain_id: chatUser.includes('school') ? location.query.domain ?? 211 : null,
      }).then(() => {
        titleChange();
      });
    }
    if (!t) {
      setInputValue('');
    }
    setLoading(true);
    chatStateChange(true);
    setChatMessage('');
    setTimeout(() => {
      handleToBottom();
    });
    sendMessage(data, handleChatMessage(input));
  };
  const handleChatMessage = (value: string) => {
    return (message: string) => {
      const isObject = verifyObjComplete(message);
      if (!isObject) return;
      const data = JSON.parse(message);
      let text = ''
      if(data.contents[0].classification){
        text = data.contents[0].sentence || '';
      }
      setTimeout(() => {
        setChatMessage(
          (pre: string | null) => (pre ?? '') + text,
        );
        if (data.message_end) {
          getList().then(() => {
            setChatMessage(null);
          });
          if (chatUser.includes("qa")) {
            getCourses(value, data.message_id);
            requestAnimationFrame(handleToBottom);
          }
          setLoading(false);
          chatStateChange(false);
        }
      }, 100);
      requestAnimationFrame(handleToBottom);
    };
  };
  const handleEnter = (event: any) => {
    if (event.keyCode == 13) {
      if (!event.shiftKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
  };
  function getCourses(value: string, id: string) {
    getSearch(value).then((res: any) => {
      if (res.success) {
        const list = dealCourse(res.data.results);
        const data = {
          key: id,
          list,
          preMsg: value,
          isMore: res.data?.total > 3
        };
        updateStorage(chatUser, sessionId, {
          ...extendMsgs,
          [id]: data
        });
        setExtendMsgs({
          ...extendMsgs,
          [id]: data
        });
      }
    });
  }

  return (
    <div className={`right-wrp ${isOut ? 'out' : ''}`}>
      <div className="right-header-wrp">
        <div className="name">{title}</div>
        {/* <div className="right-btn-group">
          <IconFont type="iconremove" />
          <CloudDownloadOutlined />
        </div> */}
      </div>
      <div className="chat-wrp" ref={coRef}>
        <ChatItem
          key={`${chatUser.includes('school')
            ? 'default-school-msg'
            : 'default-msg'
            }`}>
          {defaultMessage.map((item) =>
            item.type === 'text' ? (
              <p>{item.text}</p>
            ) : (
              <p>
                <a onClick={() => handleSubmit(item.text)}>
                  {item.text}
                </a>
              </p>
            ),
          )}
        </ChatItem>
        {list.map((item) => (
          <ChatItem
            key={item.message_id + item.create_time}
            data={item}
            content={<pre>{item.contents}</pre>}
            isMine={item?.author?.role == 'user'}
            extend={extendMsgs[item.message_id]}
          />
        ))}
        {chatMessage !== null && (
          <ChatItem
            key={`${chatUser.includes('school')
              ? 'assistant-school-msg'
              : 'assistant-msg'
              }`}
            tag="assistant-msg"
            content={
              <pre>
                {chatMessage === ''
                  ? '正在输入...'
                  : chatMessage}
              </pre>
            }
          />
        )}
        {inputValue !== '' && inputValue != null && (
          <ChatItem
            key="user-msg"
            typing
            isMine
            content={<pre>{inputValue}</pre>}
          />
        )}
      </div>

      <div className="input-wrp">
        <TextArea
          rows={3}
          placeholder="请输入问题，按enter键发送"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleEnter}
        />
        <Button
          type="primary"
          loading={loading}
          className={`sub-btn ${inputValue === '' ? 'ant-btn-loading' : ''
            }`}
          onClick={() => handleSubmit()}>
          <IconFont type="iconfabushu" />
          发送
        </Button>
      </div>
    </div>
  );
};

export default ChatContent;
