/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 14:03:18
 * @LastEditTime: 2023-09-14 14:03:25
 * @FilePath: \frontend\src\pages\Translate\components\Left\index.tsx
 * @Description:
 */

import { Affix, Input, message } from 'antd';
import React, {
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import style from './index.less';
import {
  PlusCircleOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { useImmer } from 'use-immer';
import { useLiveQuery } from 'dexie-react-hooks';
import { Info } from '@/db/controllers/info';

import { AI } from '@/constant/ai';
import {
  ClassNames,
  fileToBlob,
  isFileFormat,
  isFileSize,
  openFileDialog,
} from '@/utils/utils';
import { Chatdoc } from '@/api/chatdoc';

const Left: React.FC<LeftProps> = ({
  actionRef: propsActionRef,
  createTitle,
  setIsEmpty,
  selected<PERSON>ey,
  setSelected<PERSON>ey,
  pageKey,
}) => {
  const items = useLiveQuery(async () => {
    try {
      const result = await Info.getAllResult(pageKey);
      return result;
    } catch (error) {
      console.log(
        '%c [ error ]-28',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error,
      );
      return [];
    }
  });
  const renderCountRef = useRef(0);

  useEffect(() => {
    // 默认选中第一个
    if (
      renderCountRef.current === 0 &&
      (items?.length ?? 0) > 0
    ) {
      setSelectedKey?.(items?.[0]?.id);
      renderCountRef.current++;
    }
    if (items) {
      setIsEmpty?.(items.length === 0);
    }
  }, [items]);
  useImperativeHandle(
    propsActionRef,
    (): ActionRefType => ({}),
    [],
  );

  return (
    <div id="left" className={style.left}>
      <div
        className={style.new}
        onClick={async () => {
          try {
            const [file] = await openFileDialog('.pdf');
            if (!isFileSize(file, 20 * 1024 * 1024)) {
              throw new Error('文件大小不能超过20M');
            }
            if (!isFileFormat(file, ['.pdf'])) {
              throw new Error('只能上传pdf格式的文件');
            }
            const {
              deleteId,
              createId,
            } = await Info.storeResult({
              key: pageKey,
              title: file.name,
              // contentId: Math.random()
              //   .toString(36)
              //   .substr(2),
              before: fileToBlob(file),
            });

            if (createId && (createId as number) > -1) {
              setSelectedKey?.(createId as number);
            }
          } catch (error) {
            //@ts-ignore
            message.error(error.message);
          }
        }}>
        <PlusCircleOutlined size={36} />
        <p>{createTitle}</p>
      </div>
      <div className={style.tabs}>
        {items?.map((item) => {
          return (
            <div
              className={ClassNames(
                style.tab,
                selectedKey === item.id
                  ? style.selected
                  : '',
              )}
              onClick={() => {
                setSelectedKey?.(item.id);
              }}
              key={item.id}>
              {item.title}
              <DeleteOutlined
                onClick={async (e) => {
                  e.stopPropagation();
                  if (selectedKey === item.id) {
                    setSelectedKey?.(undefined);
                  }
                  if (item.id) {
                    await Info.deleteResult(item.id);
                  }
                }}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

export interface LeftProps {
  actionRef?: React.MutableRefObject<any>;
  createTitle: string;
  setIsEmpty?: (isEmpty: boolean) => void;
  selectedKey?: React.Key;
  setSelectedKey?: (key?: React.Key) => void;
  pageKey: AI;
}

export default Left;
Left.displayName = 'Left';
export type ActionRefType = {};
