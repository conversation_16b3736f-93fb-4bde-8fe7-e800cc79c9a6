/**
 * OOS 阿里云云存储SDK
 * 2022-10-15 zhangzhonghai
 * api文档：https://help.aliyun.com/document_detail/64041.html
 */
let aclient: any;
const OOSDK: any = {
  //初始化配置
  init:(params: any) => {
    const s3Config = {
      // yourRegion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
      region: params.endpoint.split('.')[0],
      // region: params.endpoint,
      // 从STS服务获取的临时访问密钥（AccessKey ID和AccessKey Secret）。
      accessKeyId: params.access_key_id,
      accessKeySecret: params.secret_access_key,
      // 从STS服务获取的安全令牌（SecurityToken）。
      stsToken: params.session_token,
      // 填写Bucket名称，例如examplebucket。
      bucket: params.bucket,
    };
    aclient = new (window as any).OSS(s3Config);
    console.log('aclient', aclient);
    return aclient
  },
  /**
   * Put 操作用来向指定 bucket 中添加一个对象，要求发送请求者对该 bucket 有写权限，用户必须添加完整的对象。
   * params:{
   *  Bucket 用户 bucket 名称 
      Body File 内容 
      Key 文件名
   * }
   */
  putObject: async (params: any) => {
    //第一个参数是文件名；
    try{
      const result = await aclient.put(params.key, params.file.source);
      return result
    }catch(e){
      console.log(e)
      return e
    }
  },
  /**
   * 通常在文件大于100 MB的情况下，建议采用分片上传的方法，通过断点续传和重试，提高上传成功率。
   * 如果在文件小于100 MB的情况下使用分片上传，且partSize设置不合理的情况下，可能会出现无法完整显示上传进度的情况。
   * 对于小于100 MB的文件，建议使用简单上传的方式。
   * params:{
     Bucket 用户 bucket 名称 
     Key 文件名 
   * }
   */
  createMultipartUpload: async (params: any) => {
    console.log('aliyunclient', aclient)
    // 分片上传。
    const options = {
      // 获取分片上传进度、断点和返回值。
      progress: (p: any, cpt: any, res: any) => {
        console.log('获取分片上传进度、断点和返回值',p);
        return {
          process:p,
          checkpoint:cpt,
          result:res
        }
        //abortCheckpoint = cpt
      },
      // 设置并发上传的分片数量。
      parallel: 4,
      // 设置分片大小。默认值为1 MB，最小值为100 KB。
      partSize: 5 * 1024 * 1024,
      // headers,
      // 自定义元数据，通过HeadObject接口可以获取Object的元数据。
      meta: { year: 2020, people: "test" },
      mime: "text/plain",
    };
    const res = await aclient.multipartUpload(params.file.name, params.file.source, {
      ...options,
      // 设置上传回调。
      // 如果不涉及回调服务器，请删除callback相关设置。
      // callback: {
      //   // 设置回调请求的服务器地址。
      //   url: "http://examplebucket.aliyuncs.com:23450",
      //   // 设置回调请求消息头中Host的值，即您的服务器配置Host的值。
      //   host: "yourHost",
      //   /* eslint no-template-curly-in-string: [0] */
      //   // 设置发起回调时请求body的值。
      //   body: "bucket=${bucket}&object=${object}&var1=${x:var1}",
      //   // 设置发起回调请求的Content-Type。
      //   contentType: "application/x-www-form-urlencoded",
      //   customValue: {
      //     // 设置发起回调请求的自定义参数。
      //     var1: "value1",
      //     var2: "value2",
      //   },
      // },
    });
    console.log('multipartUpload',res)
  },
  //暂停上传  aclient.cancel();
  /**
   * 文件分片
   * }
   */
  splitFile: (file: any, chunkSize?: number) => {
    let realChunkSize = Math.max(chunkSize || 5 * 1024 * 1024, 5 * 1024 * 1024); //每片不能低于5M
    const size: number = file.size / 1024 / 1024;
    //动态算分片数 每片不能低于5M 最佳区间 
    // while(file.size/realChunkSize>100){
    //   realChunkSize+=base;
    // }
    if (size < 100) {
      realChunkSize = 5 * 1024 * 1024;
    } else if (size >= 100 && size < 200) {
      realChunkSize = 10 * 1024 * 1024;
    } else if (size >= 200 && size < 500) {
      realChunkSize = 15 * 1024 * 1024;
    } else if (size >= 500 && size < 1000) {
      realChunkSize = 20 * 1024 * 1024;
    } else {
      realChunkSize = file.size / 100;
    }
    console.log('当前分片数', (file.size / realChunkSize).toFixed(0))
    let fileChunkList: any = [];
    let curChunkIndex = 0;
    while (curChunkIndex <= file.size) {
      const chunk = file.slice(curChunkIndex, curChunkIndex + realChunkSize);
      fileChunkList.push({ chunk: chunk, })
      curChunkIndex += realChunkSize;
    }
    return fileChunkList;
  },

  /**
   * 断点续传
   * }
   */
  resumeUpload: async (params: any) => {
    // 设置重试次数为五次。
    try {
      const result = await aclient.multipartUpload(params.name, params.file, {
        checkpoint: params.abortCheckpoint,
        progress: (p: any, cpt: any, res: any) => {
          // 为了实现断点上传，您可以在上传过程中保存断点信息（checkpoint）。发生上传错误后，
          //将已保存的checkpoint作为参数传递给multipartUpload，此时将从上次上传失败的地方继续上传。
          // 获取上传进度。
          console.log(p);
        },
      });
      console.log(result);
    } catch (e) {
      console.log(e);
    }
  },
  /* Abort Multipart Upload
   * 您可以调用client.abortMultipartUpload方法来取消分片上传事件。当一个分片上传事件被取消后，
     无法再使用该uploadId进行任何操作，已上传的分片数据会被删除。
   */
  //abortCheckpoint 分片上传的回传参数
  abortMultipartUpload: (abortCheckpoint: any) => {
    // 中断分片上传。
    aclient.abortMultipartUpload(abortCheckpoint.name, abortCheckpoint.uploadId);
  },
};
export default OOSDK;