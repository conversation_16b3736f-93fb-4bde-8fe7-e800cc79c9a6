.extension_information {
  padding: 20px;
  padding-bottom: 5px;
  background-color: #fff;
  margin-top: 20px;
  width: calc(100% - 40px);
  border-radius: 5px;
  margin-left: 20px;

  .title_section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .square_icon {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }

    .title_text {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }

  .content_section {
    .subject_item {
      display: flex;
      align-items: flex-start;
      font-size: 14px;
      color: #555;
      margin-bottom: 20px;
      line-height: 24px;

      .subject_label {
        font-weight: bold;
        margin-right: 10px;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          left: 0;
          bottom: -3px;
          width: 100%;
          height: 2px;
          background: linear-gradient(to right, #007bff, transparent);
        }
      }

      .subject_value {
        color: #666;
        width: calc(100% - 100px);
        height: auto;        
      }

      .knowledge_list{
        width: calc(100% - 100px);
        height: auto;  
        display: flex;
        flex-wrap: wrap;

        .knowledge_item{
            margin-right: 10px;
            padding: 5px 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient( 135deg, #F4F5FC 0%, #FAFBFE 100%);
            border-radius: 4px;
            font-weight: 400;
            font-size: 14px;
            color: var(--primary-color);
            line-height: 20px;
            margin-bottom: 10px;
            cursor: pointer;
        }
      }
    }
  }
}

.ExtensionInformation_popover_content{
  width: 100%;

  
  .popover_title{
    width: 100%;
    height: 30px;
    display: flex;
    align-items: flex-start;
    border-bottom: 1px solid #EBEBEB;
  }
}