.treasure-box-page {
    width: 100%;
    height: 100%;
    background-image: url(~@/images/chatTools/box_background.png);
    background-size: 100% 100%;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center center;
    
    .header {
        display: flex;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.8);
        font-size: 17px;
        align-items: center;
        height: 70px;
        padding: 0 20px;
        font-weight: bold;
        // position: fixed;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .header-left, .header-right {
        display: flex;
        align-items: center;
        column-gap: 5px;
        .header-img{
            width: 24px;
            height: 24px;
        }

        .header-ai-img {
            width: 32px;
            height: 27px;
            margin-right: 30px;
        }

        .ai-img {
            width: 14px;
            height: 14px;
            position: absolute;
        }
    }

    .line {
        width: 100%;
        padding-top: 60px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .content {
        width: 100%;
        height: calc(100% - 70px);
        padding: 0 40px;
        overflow: auto;
        
        .user {
            font-size: 14px;
            padding: 15px 0 5px;
            white-space: nowrap;
            font-weight: bold;
            color: rgba(0, 0, 0, 0.7);
        }
    }
}