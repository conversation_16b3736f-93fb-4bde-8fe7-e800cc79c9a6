import { Pagination, Progress, Spin } from 'antd';
import {
  getDocument,
  GlobalWorkerOptions,
  PDFDocumentProxy,
} from 'pdfjs-dist';
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useImmer } from 'use-immer';
import style from './index.less';
import {
  blobToArrayBuffer,
  getPathname,
} from '@/utils/utils';
import Access from '../Access';
import './reset.less';
import 'pdfjs-dist/build/pdf.worker.min.mjs';

const PDF: React.FC<Props> = ({ blob, setErr }) => {
  // const url =
  //   'http://higher-scsmartedu.oos-cn.ctyunapi.cn/24365/common/8e2c1add-ca9b-4174-9fb2-b186d522d71a/CSS%E6%9D%83%E5%A8%81%E6%8C%87%E5%8D%97-%E7%AC%AC%E4%B8%89%E7%89%88[4].pdf';
  const [loading, setLoading] = useState(true);
  const ref = useRef<HTMLDivElement>(null);
  const [
    pdfDoc,
    setPdfDoc,
  ] = useImmer<PDFDocumentProxy | null>(null);
  const [pageNum, setPageNum] = useState(1);
  const getData = useCallback(async () => {
    setLoading(true);
    try {
      console.log(
        '%c [ blob ]-33',
        'font-size:13px; background:pink; color:#bf2c9f;',
        blob,
      );
      if (blob) {
        const arrayBuffer = await blobToArrayBuffer(blob);
        setPdfDoc(
          await getDocument({
            data: arrayBuffer,
            cMapUrl: `${getPathname()}cmaps/`,
            cMapPacked: true,
          }).promise,
        );

        // 获取当前模块的URL
        // '../../node_modules/pdfjs-dist/cmaps/',
        console.log(`${getPathname()}cmaps/`);
      }
    } catch (error) {
      console.log(error);
      setErr(true);
    }
    setLoading(false);
  }, [blob]);
  useEffect(() => {
    getData();
  }, [getData]);
  const renderPage = useCallback(async () => {
    if (pdfDoc) {
      try {
        const page = await pdfDoc?.getPage(pageNum);
        const scale = 1.5;
        const viewport = page?.getViewport({ scale });
        const canvas = document.createElement('canvas');
        const context = canvas?.getContext('2d');
        if (!context) {
          return setErr(true);
        }
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        canvas.height = viewport?.height || 0;
        canvas.width = viewport?.width || 0;
        await page?.render(renderContext).promise;
        // 替换canvas
        if (ref.current) {
          ref.current.innerHTML = '';
          ref.current.appendChild(canvas);
        }
      } catch (error) {
        console.log(error, '渲染失败');
        setErr(true);
      }
    }
  }, [pdfDoc, pageNum]);
  const renderAll = useCallback(async () => {
    // 一次性渲染所有页面
    if (pdfDoc) {
      setLoading(true);

      try {
        const numPages = pdfDoc?.numPages;
        for (let i = 1; i <= numPages; i++) {
          const page = await pdfDoc?.getPage(i);
          const scale = 1.5;
          const viewport = page?.getViewport({ scale });
          const canvas = document.createElement('canvas');
          const context = canvas?.getContext('2d');
          if (!context) {
            return setErr(true);
          }

          canvas.height = viewport?.height || 0;
          canvas.width = viewport?.width || 0;
          await page?.render({
            canvasContext: context,
            viewport: viewport,
          }).promise;
          // 替换canvas
          if (ref.current) {
            ref.current.appendChild(canvas);
          }
        }
      } catch (error) {
        console.log(error, '渲染失败');
        setErr(true);
      }
      setLoading(false);
    }
  }, [pdfDoc]);

  useEffect(() => {
    renderAll();
  }, [renderAll]);

  return (
    <div
      style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
      }}
      id="pdf-render"
      className="pdf">
      <Access accessible={loading}>
        <Spin />
      </Access>
      <div ref={ref} className="pdf-container"></div>
    </div>
  );
};

interface Props {
  blob?: Blob;
  setErr: (err: boolean) => void;
}
export default PDF;
PDF.displayName = 'PDF';
