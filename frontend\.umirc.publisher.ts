import { defineConfig } from 'umi';

export default defineConfig({
  proxy: {
    '/api': {
      target: 'http://*************',
      // target: 'http://*************:5005',
      // target: 'http://localhost:8080',
      changeOrigin: true,
      pathRewrite: { '^/api': '/' },
    },
    '/cvod': {
      target: 'http://*************',
      changeOrigin: true,
    },
    '/cvodweb': {
      target: 'http://*************',
      changeOrigin: true,
    },
    '/rman': {
      target: 'http://*************',
      // target: 'http://localhost:8001',
      changeOrigin: true,
    },
    '/bucket-z': {
      target: 'http://*************',
      changeOrigin: true,
    },
    '/edudata': {
      target: 'http://*************:8002',
      // target: 'http://*************',
      changeOrigin: true,
    },
    '/unifiedlogin': {
      target: 'http://*************',
      changeOrigin: true,
    },
  },
});
