import request from './request';

async function recordingTask() {
  return request(`/ipingestman/schedule/record/monitoring`, {
    method: 'get',
  });
}

async function classroomlist(abnormal:string) {
  return request(`/ipingestman/schedule/record/areamonitoring?date=${abnormal}`, {
    method: 'get',
  });
}
async function teachingtree() {
  return request(`/ipingestman/schedule/record/areas`, {
    method: 'get',
  });
}

// /ipingestman/schedule/record/jsareas?date=2022-03-02&area_id=1731
async function teachingtreelist(data:string,id:Number) {
  return request(`/ipingestman/schedule/record/jsareas?date=${data}&area_id=${id}`, {
    method: 'get',
  });
}

//查询某个教室某天的任务列表
async function teachingtasklist(id:Number,data:string) {
  return request(`/ipingestman/schedule/record/tasks?area_id=${id}&date=${data}`, {
    method: 'get',
  });
}

async function flowmanagerlist(name:string) {
  return request(`/flowmanager/v1/flow/define/${name}`, {
    method: 'get',
  });
}


// http://**************/flowmanager/v1/flow/instance/f7980d06-a04c-11ec-9f97-00155d0a3c0e_20220310163457/Send2Acotr(key)
async function flowmanager(id:string,name:string) {
  return request(`/flowmanager/v1/flow/instance/${id}/${name}`, {
    method: 'get',
  });
}
//http://**************/flowmanager/v1/flow/instance/f7980d06-a04c-11ec-9f97-00155d0a3c0e_20220310163457/Send2Acotr(key)
async function flowmanageroutput(id:string,name:string) {
  return request(`/flowmanager/v1/flow/instance/${id}/${name}`, {
    method: 'POST',
  });
}
// http://localhost:8001/flowmanager/v1/flow/instance/3887bb8c-a8c9-11ec-9f97-00155d0a3c0e_20220321114433
// http://localhost:8000/flowmanager/v1/flow/instance/1b99abbd-a54a-11ec-9f97-00155d0a3c0e_20220317005705
async function flowmanageroutputs(id:string) {
  return request(`/flowmanager/v1/flow/instance/${id}}`, {
    method: 'POST',
  });
}
let api = {
  recordingTask,
  classroomlist,
  teachingtree,
  teachingtreelist,
  teachingtasklist,
  flowmanagerlist,
  flowmanager,
  flowmanageroutput,
  flowmanageroutputs
}

export default api;