import type { IConfigFromPlugins } from "@/.umi/core/pluginConfig";

/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:36:28
 * @LastEditTime: 2023-10-27 16:30:57
 * @FilePath: \frontend\config\routes\index.ts
 * @Description: 
 */
const initRoutes:RouteType[] =[
  {
    path:'/translate',
    name:'翻译',
    component:'./Translate',
  },
  {
    path: "/qaAssistant",
    name: "QaAssistant",
    component: "@/pages/QaAssistant"
  },
  {
    path: "/outQaAssistant",
    name: "OutQaAssistant",
    component: "@/pages/QaAssistant"
  },
  {
    path: "/schoolAssistant",
    name: "QaAssistant",
    component: "@/pages/QaAssistant"
  },
  {
    path: "/outChat",
    name: "OutChat",
    component: "@/pages/QaAssistant/QaAndSchool.tsx"
  },
  {
    path: "/historyChat",
    name: "HistoryChat",
    component: "@/pages/QaAssistant/historyChat/index.tsx"
  },
  {
    path: "/treasureBox",
    name: "TreasureBox",
    component: "@/pages/TreasureBox/index.tsx"
  },
  {
    path: "/retrieval",
    name: "文档信息检索",
    component: "./Retrieval"
  },{
    path: "/summarize",
    name: "文档内容总结",
    component: "./Summarize"
  },
  {
    path:'/intelligentImage',
    name:'智能识图工具',
    component:'./IntelligentImage',
  },
  {
    path:'/toGif',
    name:'视频转GIF工具',
    component:'./Togif',
  },
  {
    path:'/clip',
    name:'智能剪辑工具',
    component:'./Clip',
  },
  {
    path: '/overview',
    name: 'AI工具集',
    component: './Overview',
  },
  {
    path: '/teachingPlan',
    name: '智能教案',
    component: './TeachingPlan',
  },
]

export const routes:RouteType[] = [
  {
    path: '/sanhang',
    component: '@/pages/Layouts/SanHangLayout',
    routes:[
      {
        path: '/sanhang',
        exact: true,
        redirect: '/sanhang/translate',
      },
      {
        path: '/sanhang/translate',
        name: 'AI工具集',
        component: '@/pages/SanHangTranslate',
      },
      {
        path: '/sanhang/touches',
        name: 'AI工具集',
        component: '@/pages/Touches',
      }
    ]
  },
  {
    path: '/',
    component: './Layouts',
    routes:[
      {
        path: '/',
        exact: true,
        redirect: '/overview',
      },
      ...initRoutes
    ]
  },
  
  {
    path: '*',
    name: '404 not found',
    component: './404',
  }
 
]

export type RouteType = ArrayElement<Required<IConfigFromPlugins>['routes']>

export type ArrayElement<
  ArrayType extends readonly unknown[],
> = ArrayType extends readonly (infer ElementType)[]
  ? ElementType
  : never;