import React,{ useEffect,useState } from "react";
import './index.less';
import { Table, Tree } from "antd";
import {
    GetUserFolders,
    GetUserCourses,
    GetFolderFolders,
    GetFoldersFiles,
    GetCoursesFoldersChild
  } from '@/api/canvasapi';
  import dayjs from "dayjs";

interface DataNode {
    id: string;
    title: string;
    level: number;
    name: string;
    key: string;
    isLeaf?: boolean;
    children?: DataNode[];
  }

const CanvasResources = ({onSelect}:any) =>{
    const [treeData, setTreeData] = useState<any>([]);
    const [dataSource, setDataSource] = useState<any>([]);
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
        init();
    }, []);

    const init = () =>{
        GetUserFolders().then((res:any) => {
            let newarr = []
            if(res?.data.length > 0){
                const rootnode = res.data.filter((item:any)=>item.parent_folder_id == undefined);
                newarr.push({                
                    id: 'rootfile_'+rootnode[0].id,
                    name:'我的文件',
                    level:0,
                    children: []
                })
            }
            GetUserCourses().then((res2:any) => {
                if(res2?.data.length > 0){   
                    res2.data.forEach((element:any) => {  
                        newarr.push({   
                            id: 'course_'+element.id,
                            name:element.name,
                            level:0,
                            children: []
                        })                                              
                    });
                    setTreeData(newarr)
                    
                }
            })
        })
        
    }    
   

    const getfile = (key:any,info:any) =>{        
        let ids = key[0].split('_');
        setLoading(true);
        if(ids[0] == 'course'){            
            GetCoursesFoldersChild(ids[1]).then((res:any)=>{
                if(res?.data.length > 0){
                    GetFoldersFiles(res.data[0].id).then((res:any) => {
                        if(res?.data){
                            setDataSource(res.data)
                        }else{
                            setDataSource([])
                        }
                    }).finally(()=>{
                        setLoading(false);
                    });
                }
            })            
        }else{
            GetFoldersFiles(ids[1]).then((res:any) => {
                if(res?.data){
                    setDataSource(res.data)
                }else{
                    setDataSource([])
                }
            }).finally(()=>{
                setLoading(false);
            });
        }

    }

    const columns:any = [
        {
          title: '名称',
          dataIndex: 'display_name',
          key: 'display_name',
          render:(text:any,record:any) =>{                
                if(record.thumbnail_url){
                    return <div className="name_render">
                        <img src={record.thumbnail_url} />
                        <span>{text}</span>
                    </div>
                }else{
                    return text
                }
         }
        },
        {
          title: '创建日期',
          dataIndex: 'created_at',
          key: 'created_at',
          render:(text:any) => (dayjs(text).format('YYYY-MM-DD HH:mm:ss'))
        },
        {
          title: '修改日期',
          dataIndex: 'updated_at',
          key: 'updated_at',
          render:(text:any) => (dayjs(text).format('YYYY-MM-DD HH:mm:ss'))
        },        
        {
            title: '修改者',
            dataIndex: 'display_name',
            key: 'display_name',
            render:(text:any,record:any) =>record.user.display_name
        },
        {
            title: '大小',
            dataIndex: 'size',
            key: 'size',
            render:(text:any,record:any) =>{                
                if(text > (1024 * 1024)){
                    return (text/1024/1024).toFixed(2) + 'MB'
                }else{
                    return (text/1024).toFixed(0) + 'KB'
                }
            }
        },
      ];

    const onSelectChange = (newSelectedRowKeys:any,newSelectedRow:any, selected:any) => {
        onSelect(newSelectedRow)
        setSelectedRowKeys(newSelectedRowKeys);
    };

    const rowSelection = {
        selectedRowKeys,
        onChange: onSelectChange,
    };

    const updateTreeData = (list: DataNode[], key: React.Key, children: any[]): any[] =>{
        return list.map(node => {
            if (node.id === key) {
                if(node.id.includes('course')){
                    return {
                        ...node,
                        folder_id:children[0].parent_folder_id,
                        children,
                      };
                }else{
                    return {
                      ...node,
                      children,
                    };
                }
            }
            if (node.children) {
              return {
                ...node,
                children: updateTreeData(node.children, key, children),
              };
            }
            return node;
          });
    }
        
    
       
      

    const loadData = (treeNode:any) => {    
       let ids = treeNode.key.split('_');
       return new Promise<void>(resolve => {
            if(ids[0] == 'course' && treeNode.level == 0){
                GetCoursesFoldersChild(ids[1]).then((res:any)=>{
                    if(res?.data.length > 0){
                        let folder_id = res.data[0].id;
                        GetFolderFolders(folder_id).then((res2:any) => {
                            if(res2?.data.length > 0){
                                const newdata = res2.data.map((item:any) => ({
                                    ...item,
                                    id: 'folders_'+item.id,
                                    key: 'folders_'+item.id,
                                    level:treeNode.level+1
                                }))
                                setTreeData((predata:any) =>
                                    updateTreeData(predata, treeNode.key, newdata)
                                  );
                                resolve();
                            }else{
                                resolve();
                            }
                        })
                    }else{
                        resolve();
                    }
                })
            }else if(ids[0] == 'course' || ids[0] == 'rootfile' || ids[0] == 'folders'){
                GetFolderFolders(ids[1]).then((res:any) => {
                    if(res?.data.length > 0){
                        const newdata = res.data.map((item:any) => ({
                            ...item,
                            id: 'folders_'+item.id,
                            key: 'folders_'+item.id,
                            level:treeNode.level+1
                        }))
                        setTreeData((predata:any) =>
                            updateTreeData(predata, treeNode.key, newdata)
                          );
                        resolve();
                    }else{
                        resolve();
                    }
                })
            }
       });
    }

    return (
        <div className="CanvasResources_view">
            <div className="left_tree">
                <Tree onSelect={getfile}
                loadData={loadData}
                 fieldNames={{
                    title: 'name',
                    key: 'id',
                    children: 'children'
                }} treeData={treeData} />
            </div>
            <div className="right_view">
                <Table loading={loading} rowKey='id' rowSelection={rowSelection} dataSource={dataSource} columns={columns} pagination={false} />
            </div>
        </div>
    )
}

export default CanvasResources;