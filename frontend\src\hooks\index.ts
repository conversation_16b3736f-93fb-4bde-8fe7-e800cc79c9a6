import { isNil } from '@/utils/utils';
import { PaginationProps } from 'antd';
import {
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useHistory, useLocation } from 'react-router';
import { IGlobal, useModel, useSelector } from 'umi';
import { useImmer } from 'use-immer';

/**
 * @description 自适应rem
 * @param standard 基准比率  以1920为基准分辨率,计算出的rem根值为16px
 */
export const useSelfAdaptation = (
  standard = 120,
  backupKey = 'data-backup-fontsize',
) => {
  // ...
  useEffect(() => {
    const setRem = () => {
      // 判断是否为移动端设备（屏幕宽度小于768px）
      const isMobile = window.innerWidth < 768;
      
      // 只在移动端设备上设置fontSize
      if (isMobile) {
        const backupFontsize = document.documentElement.getAttribute(
          backupKey,
        );
        if (isNil(backupFontsize)) {
          document.documentElement.setAttribute(
            backupKey,
            document.documentElement.style.fontSize,
          );
        }
        const htmlWidth =
          document.documentElement.clientWidth ||
          document.body.clientWidth;
        document.documentElement.style.fontSize = `${htmlWidth /
          standard}px`;
      }
    };
    setRem();
    window.addEventListener('resize', setRem);
    return () => {
      document.documentElement.style.fontSize =
        document.documentElement.getAttribute(backupKey) ??
        '';
      window.removeEventListener('resize', setRem);
    };
  }, []);
};

/**
 * @description 上拉加载
 */
export const usePullUp = (
  observer: any,
  callback: Function,
) => {
  const [isFetching, setIsFetching] = useState(false);

  useEffect(() => {
    observer.current = new MutationObserver((entries) => {
      const target = entries[0].target as HTMLElement;
      if (
        target.scrollTop + target.clientHeight >=
        target.scrollHeight
      ) {
        setIsFetching(true);
        callback().then(() => setIsFetching(false));
      }
    });

    observer.current.observe(document.documentElement, {
      childList: true,
      subtree: true,
    });

    return () => observer.current.disconnect();
  }, [callback]);

  return [isFetching];
};

/**
 * @description 修改query的形式导航
 */
export function useQuery<T extends Record<string, any>>() {
  const history = useHistory();
  const location = history.location;

  // 获取查询参数
  const queryParams = useMemo(() => {
    return new URLSearchParams(location.search);
  }, [location.search]);
  const query: T = useMemo(() => {
    const params: any = {};
    for (let param of queryParams.keys()) {
      let value = queryParams.get(param) ?? '';
      try {
        value = JSON.parse(value);
      } catch (error) {}
      params[param as keyof typeof params] = value;
    }
    return params;
  }, [queryParams]);

  // 设置查询参数
  const setQuery = (value: T) => {
    // 解析value设置所有query
    for (let key in value) {
      queryParams.set(
        key,
        JSON.stringify(value[key as keyof typeof value]),
      );
    }

    history.replace({
      search: queryParams.toString(),
    });
  };

  return { query, setQuery };
}

/**
 * @description 用于分页的hook
 * @param rest 需要放置在query中的数据
 */
export const useQueryPagination = <
  T extends Record<string, any>
>(
  defaultPagination?: PaginationProps,
  rest?: T,
) => {
  const { query, setQuery } = useQuery<
    T & {
      page?: number;
      pageSize?: number;
    }
  >();

  const pagination = useMemo<PaginationProps>(() => {
    return {
      className: 'hook-pagination',
      pageSize: Number(query.pageSize ?? 10),
      current: Number(query.page ?? 1),
      showTotal: (total) => `共${total}条`,
      // hideOnSinglePage: true,
      onChange(page, pageSize) {
        setQuery({
          ...query,
          page,
          pageSize,
          ...rest,
        });
      },
      ...defaultPagination,
    };
  }, [query, rest]);

  return {
    pagination,
    query,
    setQuery,
  };
};

/** voltanode版本控制 判断当前用户是否是管理员 */
export const useIsAdmin = () => {
  const { userInfo } = useSelector<any, IGlobal>(
    (state) => state.global,
  );

  const managerCode = ['r_sys_manager', 'admin_S1'];
  return userInfo?.roles?.some((item: any) => {
    return managerCode.includes(item.roleCode);
  });
};
export const useContainerDimensions = (
  myRef: React.RefObject<HTMLDivElement>,
) => {
  const [dimensions, setDimensions] = useState({
    width: 0,
    height: 0,
  });
  useEffect(() => {
    const getDimensions = () => {
      if (!myRef.current) {
        return {
          width: 0,
          height: 0,
        };
      }
      console.log(
        '%c [ myRef.current ]-150',
        'font-size:13px; background:pink; color:#bf2c9f;',
        myRef.current,
      );

      return {
        width: myRef.current.offsetWidth,
        height: myRef.current.offsetHeight,
      };
    };
    const handleResize = () => {
      setDimensions(getDimensions());
    };

    if (myRef.current) {
      setDimensions(getDimensions());
    }
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [myRef]);

  return dimensions;
};

/**
 * @description 媒体查询hook
 * @param condition
 * @param changeCallback
 * @param resetCallback
 */
export const useMediaScreen = (
  /** 条件 */
  condition: (width: number) => boolean,
  changeCallback?: () => void,
  resetCallback?: () => void,
) => {
  const observer = useRef<ResizeObserver>();
  useEffect(() => {
    observer.current = new ResizeObserver(() => {
      const target = document.documentElement;
      const width =
        target.clientWidth === 0
          ? Infinity
          : target.clientWidth;

      if (condition(width)) {
        // 执行相应的操作，例如改变样式或触发事件。
        changeCallback?.();
      } else {
        resetCallback?.();
      }
    });
    observer.current.observe(document.documentElement);

    return () => {
      observer.current?.disconnect();
      resetCallback?.();
    };
  }, []);
};
/**
 * @description 默认移动端媒体查询
 */
export const useMobileMediaScreen = () => {
  const { isMobile, setIsMobile } = useModel('mobile');
  useMediaScreen(
    (width) => width < 768,
    () => {
      document.documentElement.classList.add(
        'custom-mobile',
      );
      setIsMobile(true);
    },
    () => {
      document.documentElement.classList.remove(
        'custom-mobile',
      );
      setIsMobile(false);
    },
  );
  return isMobile;
};
