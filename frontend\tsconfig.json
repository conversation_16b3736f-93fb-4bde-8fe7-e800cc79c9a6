{"compilerOptions": {"experimentalDecorators": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "importHelpers": true, "jsx": "react", "esModuleInterop": true, "sourceMap": true, "baseUrl": "./", "strict": true, "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "allowSyntheticDefaultImports": true, "resolveJsonModule": true}, "include": ["mock/**/*", "src/**/*", "config/**/*", ".umirc.ts", "typings.d.ts"]}