import { request } from 'umi';
import { Request } from '@/constant/Request';
import {
    generatContentType,
    objToFormData,
} from '@/utils/utils';
import { IResponse, RmanResponse } from '.';

export namespace togif {
    export const uploadFile = (file: File) => {
        return request(`/v1/upload/save/file`, {
            method: 'post',
            data: objToFormData({
                file,
            }),
            prefix: Request.PREFIX.RMAN,
        });
    };
    export const downloadFile = (filename: string) => {
        return request<Blob>(
            `/v1/translation/download/${filename}`,
            {
                method: 'get',
                prefix: Request.PREFIX.LEARN,
                responseType: 'blob',
            },
        );
    };

    export const chineseAndEnglish = (data: {
        content: string;
    }) => {
        return request<RmanResponse<string>>(
            `/v1/translation/between/chinese/and/english`,
            {
                method: 'post',
                data,
                prefix: Request.PREFIX.LEARN,
            },
        );
    };
    export const ocrTask = (data: any) => {
        return request(`/v1/intelligent/ocr/task`, {
            method: 'post',
            data,
            prefix: Request.PREFIX.RMAN,
        });
    };
    export const getOcrTask = (params: any) => {
        return request(`/v1/intelligent/ocr/text`, {
            method: 'get',
            params,
            prefix: Request.PREFIX.RMAN,
        });
    };
    /**
   * OOS配置
   * @param data
   */
    export const storageConfig = async (data: any) => {
        return request(`/v1/upload/v4/path`, {
            method: 'POST',
            data,
            prefix: Request.PREFIX.RMAN,
        });
    };
    export const uploadImport = (
        folderPath: string,
        filePath: string,
        uploadMetas: IFormItem[],
        fileLength?: number,
        extraParam?: any[]
    ) => {
        return request<boolean>('/v1/upload/import', {
            method: 'POST',
            data: {
                folderPath,
                filePath,
                uploadMetas,
                fileLength,
                extraParam
            },
            prefix: Request.PREFIX.RMAN,
        });
    };

    export const filemerge = (guid: string, fileName: string, fileGuid: string) => {
        return request<string>('/v1/upload/filemerge', {
            method: 'POST',
            data: {
                guid,
                fileName,
                fileGuid
            },
            prefix: Request.PREFIX.RMAN,
        });
    };
    export const filesave = (data: any) => {
        return request<string>('/v1/upload/filesave', {
            method: 'POST',
            headers: {
                "Content-Type": "multipart/form-data",
            },
            data,
            prefix: Request.PREFIX.RMAN,
        });
    };

    /**
     * 查询合并进度
     * @param fileGuid
     */
    export const fetchMergeStatus = (fileGuid: string) =>
        request<{
            state: number;
            errorMsg: string;
            finalFilePath: string;
        }>(`/v1/upload/get/composite/task/details/${fileGuid}`, { prefix: Request.PREFIX.RMAN, });
    export const getAllFieldsByType = (type: string) => {
        return request(
            `/v1/metadata/config/fields/upload?EntityType=${type}&Type=basic&ResourceType=model_sobey_object_entity`, { prefix: Request.PREFIX.RMAN });
    };
    export const importGif = (data: any) => {
        return request(`/v1/upload/video/to/gif`, {
            method: 'POST',
            data,
            prefix: Request.PREFIX.RMAN,
        });
    };
    export const gettreebylevel = (gettreebylevel: number = 2) => {
        return request(`/v1/folder/all/tree?level=${gettreebylevel}`, {
          method: 'GET',
          prefix: Request.PREFIX.RMAN,
        });
    };
    export const loadChild = (path: string, isOwner?: any) => {
        return request(
          `/v1/folder/children?folderPath=${encodeURIComponent(path)}%2F&isChildCount=true${
            isOwner ? '&isOwner=true' : ''
          }`,
          {
            method: 'GET',
            prefix: Request.PREFIX.RMAN,
          },
        );
      };
}

