/*
 * @Description: 课程模块
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-04-26 10:24:07
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-08-20 14:02:43
 */
import { Reducer } from 'redux';
import { Effect } from 'dva';
import { querymapinfo} from '@/api/canvasapi';

export interface ICourseMapModelState {
  mapinfo: any;
}

export interface CourseMapType {
  namespace: 'coursemap';
  state: ICourseMapModelState;
  effects: {
    fetchMapInfo: Effect;
  };

  reducers: {
    changemapinfo: Reducer<any>;
  };
}

const CourseMapType: CourseMapType = {
  namespace: 'coursemap',

  state: {
    mapinfo: {},
  },

  effects: {
    *fetchMapInfo({ payload }, { call, put }) {
      const { params } = payload;      
      const res = yield call(querymapinfo, params);
      yield put({
        type: 'changemapinfo',
        payload: { mapinfo: { ...res.data } },
      });
    },
  },

  reducers: {
    changemapinfo: (state: ICourseMapModelState, { payload }) => {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default CourseMapType;
