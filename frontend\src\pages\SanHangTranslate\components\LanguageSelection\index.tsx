import React, { useState } from 'react';
import { Select, Button, Radio } from 'antd';
import { SwapOutlined } from '@ant-design/icons';
import './index.less';
import { languageOptions } from '../../sanhangutil';

interface LanguageSelectionProps {
  LanguageChange: (ourceLanguage:string,targetLanguage:string) => void;
  modelChange: (model:any) => void;
}

// 翻译类型选项
const optionsWithDisabled = [
  { label: '词语翻译', value: 'words' },
  { label: '段落翻译', value: 'paragraphs' },
];

const LanguageSelection: React.FC<LanguageSelectionProps> = ({
  LanguageChange,modelChange
}) => {
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [targetLanguage, setTargetLanguage] = useState('cn');
  const [model, setModel] = useState('words');

  return (
    <div className="language-selection">
      <div className='select_language'>
        <Select value={sourceLanguage} onChange={(e)=>setSourceLanguage(e)} options={languageOptions} style={{width:'130px'}} bordered={false} />
        <SwapOutlined onClick={()=>{
          setSourceLanguage(targetLanguage)
          setTargetLanguage(sourceLanguage)
          LanguageChange && LanguageChange(sourceLanguage,targetLanguage)
        }} />
        <Select value={targetLanguage} onChange={(e)=>setTargetLanguage(e)} options={languageOptions} style={{width:'130px'}} bordered={false} />
      </div>
      <div className='rideo_model'>
        {optionsWithDisabled.map((option) => (
          <div 
            key={option.value}
            className={`rideo_model_select ${model === option.value ? 'active' : ''}`}
            onClick={() => {
              modelChange && modelChange(option.value);
              setModel(option.value);
            }}
          >
            <span>{option.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default LanguageSelection;
