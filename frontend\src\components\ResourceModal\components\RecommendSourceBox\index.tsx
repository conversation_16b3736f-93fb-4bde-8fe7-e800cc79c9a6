import { IconFont } from '@/components/iconFont/iconFont_2';
import { Checkbox, Divider, Empty, List } from 'antd';
import React from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

import './index.less';

export interface IRecommendSourceProp {
  /** 资源列表 */
  recommendList: any[];
  /** 是否显示加载改变 */
  hasMoreRecommend: boolean;
  /** 搜索的知识点 */
  pointName: string;
  /** 已经选择的列表 */
  checkedList: any[];
  /** 标题 */
  title?: string;
  /** 改变出发的方法 */
  checkChange: (value: any) => void;
  /** 加载更多的方法 */
  loadMore: () => void;
}

const RecommendSourceBox: React.FC<IRecommendSourceProp> = props => {
  const {
    recommendList,
    hasMoreRecommend,
    pointName,
    checkedList,
    title = '推荐资源',
    checkChange,
    loadMore,
  } = props;

  return (
    <div className="recommend-container-f">
      <div style={{ color: 'var(--primary-color)', fontSize: '15px' }}>
        <IconFont type="iconzhinengtuijian" style={{ marginRight: '10px' }} />
        {title}
      </div>
      <div className="recommend-title">
        以下为
        <span className="recommend-course">{pointName}</span>
        课程相关备课资源推荐
      </div>
      <div
        className="recommend-scroll-list"
        style={{ overflow: 'hidden' }}
        id="scrollableResource"
      >
        {recommendList?.length > 0 ? (
          <>
            <Checkbox.Group
              name="rec_check"
              value={checkedList}
              onChange={checkChange}
              style={{ width: '100%' }}
            >
              <InfiniteScroll
                dataLength={recommendList.length}
                height="calc(65vh - 44px)"
                scrollThreshold={0.7}
                next={loadMore}
                hasMore={hasMoreRecommend}
                loader={
                  <h4 style={{ width: '100%', textAlign: 'center' }}>
                    加载中...
                  </h4>
                }
                endMessage={
                  <Divider plain style={{ width: '100%', textAlign: 'center' }}>
                    {recommendList.length > 0
                      ? '已加载完毕所有的资源！'
                      : ''}
                  </Divider>
                }
                scrollableTarget="scrollableResource"
              >
                <List
                  grid={{ gutter: 3, column: 1 }}
                  dataSource={recommendList}
                  style={{ width: '99.5%' }}
                  renderItem={item => {
                    const keyWrods = item?.highlight_keywords || [];
                    return (
                      <List.Item>
                        <div
                          key={item.contentId_}
                          className="recommend-item-container"
                        >
                          {item.keyframe_ && item.keyframe_ != '' ? (
                            <img src={item.keyframe_} onError={(e: any) => {
                              let img = e.target
                              img.src = '/rman/static/images/video.png'
                              img.onError = null
                            }} className="course-img" />
                          ) : (
                            <img
                              src={'/rman/static/images/video.png'}
                              className="course-img"
                            />
                          )}
                          <div className="course-info">
                            <span className="course-name">
                              <Checkbox value={item}></Checkbox>
                              <span
                                style={{
                                  width: '100%',
                                  overflow: 'hidden',
                                  whiteSpace: 'nowrap',
                                  textOverflow: 'ellipsis',
                                  cursor: 'default',
                                  paddingLeft: '5px'
                                }}
                                title={item.name}
                              >
                                {item.name}
                              </span>
                            </span>
                            <div className="course-key-word">
                              <span className="reco-keywords">
                                {keyWrods?.[0]}
                              </span>
                            </div>
                          </div>
                        </div>
                      </List.Item>
                    );
                  }}
                />
              </InfiniteScroll>
            </Checkbox.Group>
          </>
        ) : (
          <Empty description="暂无相关资源推荐" />
        )}
      </div>
    </div>
  );
};

export default RecommendSourceBox;
