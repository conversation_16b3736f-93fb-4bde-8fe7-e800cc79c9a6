/*
 * @Author: 李武林
 * @Date: 2022-05-25 15:57:05
 * @LastEditors: 李武林
 * @LastEditTime: 2022-05-27 18:00:56
 * @FilePath: \frontend\src\api\synchronization.ts
 * @Description: 
 * 
 * Copyright (c) 2022 by <PERSON>武林/索贝数码, All Rights Reserved. 
 */
import request from "./request";

//同步情况概览查询
async function getAllTaskTypes() {
    return request(`/edudataimport/cycletask/TaskTypes/`, {
        method: 'get'
    })
}

// 设置同步周期
async function setTaskCycle(data:any) {
    return request(`/edudataimport/cycletask/TaskCycle/`, {
        method: 'POST',
        data
    })
}

// 手动执行同步任务
async function manualSync(data:any) {
    return request(`/edudataimport/cycletask/ManualTask/`, {
        method: 'POST',
        data
    })
}

// 查询任务详情
async function getTaskRecords(params:any) {
    return request(`/edudataimport/cycletask/TaskRecords/`, {
        method: 'get',
        params
    })
}

// 查询任务下面的步骤
async function getStepRecords(params:any) {
    return request(`/edudataimport/cycletask/StepRecords/`, {
        method: 'get',
        params
    })
}

// 根据步骤查询日志
async function getStepLogs(params:any) {
    return request(`/edudataimport/cycletask/StepRecordsLog/`, {
        method: 'get',
        params
    })
}

// 导出日志
async function exportLogs(params:any) {
    return request(`/edudataimport/cycletask/StepRecordsDownloadLog/`, {
        method: 'get',
        params,
        responseType: 'blob'
    })
}


let synchronization = {
    getAllTaskTypes,
    setTaskCycle,
    manualSync,
    getTaskRecords,
    getStepRecords,
    getStepLogs,
    exportLogs
}

export default synchronization;
