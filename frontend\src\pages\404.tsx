/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> ran
 * @Date: 2022-12-16 14:15:06
 * @LastEditTime: 2022-12-16 14:15:06
 * @Description:
 */

import { Result } from 'antd';
import React from 'react';

const Page404: React.FC = () => {
  return (
    <div
      style={{
        width: '100%',
        height: '100vh',
        backgroundColor: '#fff',
      }}
    >
      <Result
        status="404"
        title="404"
        subTitle="非常抱歉,当前页面不存在"
        //       extra={
        //         <div>
        //  {callbackText ?? '返回首页'}
        //         </div>
        //       }
      />
    </div>
  );
};

export default Page404;
