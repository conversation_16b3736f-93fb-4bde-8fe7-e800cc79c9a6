declare namespace AppModule {
  interface IItem {
    guid?: string;
    id?: number;
    code: string;
    name: string;
    checked?: boolean;
  }

  type CreateModule = IItem & { appId: number };
  type CreateFeature = IItem & { moduleId: number };

  type AppDetail = IItem & {
    applicationModules: (IItem & {
      applicationModuleFeatureses: IItem[];
    })[];
  };

  type RoleFeature = {
    applicationInfo: IItem;
    applicationModule: IItem;
    applicationModuleFeatures: IItem;
  };
  /*** v2 start****/
  type ModuleItem = {
    parentId: number;
    guid?: string;
    id: number;
    code: string;
    name: string;
    disable: boolean;
    isSystem?:boolean
  };

  type ItemV2 = {
    id: number;
    code: string;
    name: string;
    moduleID: number;
  };

  type RoleFeatureV2 = {
    roleCode: string;
    isSystem: false;
    appModule: {
      id: string;
      parentId: string;
      guid: string;
      code: string;
      name: string;
      disable: boolean;
      isSystem: boolean;
    };
    appModuleFeatures: {
      id: string;
      code: string;
      name: string;
      isSystem: boolean;
      moduleID: string;
    };
  };

  type ParameterItem = {
    id: number;
    name: string;
    code: string;
    parameterType: number;
    valJson: string;
    value: string;
    moduleID: number;
  };
}
