import setting from '@/api/setting';
import { getLightColor, loadFavicon } from '@/utils';
import config from '@/utils/config';

interface ITheme {
  title: string;
  themeColor: string;
  logoUrl: string;
  isShow: number;
  faviconUrl: string;
  browserTabAbbreviation: string;
}

const themeStr = localStorage.getItem(config.themeStorage);
const themeConfig: ITheme = themeStr
  ? JSON.parse(themeStr)
  : {
      title: '',
      themeColor: '',
      logoUrl: '',
      isShow: 1,
      faviconUrl: '',
      browserTabAbbreviation:''
    };

function modifyTheme(themeColor: string) {
  console.time('theme');
  // 切换主题颜色（antd）
  return (window as any).less
    .modifyVars({
      '@primary-color': themeColor,
      '@hover-color': getLightColor(themeColor, 0.2),
    })
    .then(() => {
      console.timeEnd('theme');
      console.log(`${themeColor} 主题切换成功`);
    })
    .catch(() => console.error(`${themeColor} 主题切换失败`));
}

export default {
  namespace: 'themes',
  subscriptions: {
    setup: ({ dispatch }: any) => {
      dispatch({
        type: 'initTheme',
        payload: themeConfig,
      });
      if (themeConfig.themeColor) {
        dispatch({
          type: 'generateTheme',
          payload: themeConfig,
        });
      }
    },
  },
  state: themeConfig,
  effects: {
    *initTheme({ payload }: { payload: ITheme }, { call, put }: any) {
      const {
        errorCode,
        extendMessage,
      }: API.IResponse<API.ISetting> = yield call(
        setting.getSettingUnauthorized,
      );
      if (errorCode === 'success') {
        localStorage.setItem(
          config.themeStorage,
          JSON.stringify(extendMessage),
        );
        if (extendMessage.themeColor !== payload.themeColor) {
          yield put({
            type: 'setTheme',
            payload: extendMessage,
          });
          yield put({
            type: 'generateTheme',
            payload: extendMessage,
          });
        }
      }
    },
    *generateTheme(
      { payload: { faviconUrl, themeColor } }: { payload: ITheme },
      { call, put }: any,
    ) {
      // const config = themeCfg[theme];
      // 下载icon
      loadFavicon(faviconUrl || '/learn/workbench/favicon_scu.ico');
      yield put({
        type: 'global/updateShowLoading',
        payload: true,
      });
      try {
        yield call(modifyTheme, themeColor);
      } catch (e) {
        yield put({
          type: 'global/updateShowLoading',
          payload: false,
        });
      }
      yield put({
        type: 'global/updateShowLoading',
        payload: false,
      });
    },
  },
  reducers: {
    setTheme(state: any, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
