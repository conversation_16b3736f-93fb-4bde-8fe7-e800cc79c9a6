import React, { FC, useState, useImperativeHandle, useEffect } from 'react';
import { Input, Button, Space, message } from 'antd';
import { reqExportMessage } from '@/api/teachingPlan'
import { download } from '@/utils'
interface PlanContentProps {
  planContentRef: any;
  list: any;
  emitReGenerate?: (item: any) => void;
  allFinished: boolean;
  templateName: string;
  isScrollBottom: boolean;
  emitDeleteTopic?: (newList: any) => void;
}
const PlanContent: FC<PlanContentProps> = ({
  planContentRef,
  list,
  emitReGenerate,
  allFinished,
  templateName,
  isScrollBottom,
  emitDeleteTopic
}) => {
  useImperativeHandle(planContentRef, () => {
    return {
      open
    }
  });
  const [topicList, setTopicList] = useState<any>([])
  const [value, setValue] = useState('');
  const containerRef = React.useRef(null)

  useEffect(() => {
    setTopicList([...list])
  }, [list]);

  const deleteTopic = (item: any) => {
    const newList = topicList.filter((i: any) => i.id !== item.id)
    setTopicList(newList)
    emitDeleteTopic?.(newList)
  }
  const editTopic = (item: any, type: number) => {
    // 将数组当前项readonly设置为true
    const newList = topicList.map((i: any) => {
      if (i.key === item.key) {
        return { ...i, readOnly: type == 1 ? false : true }
      }
      return i
    })
    setTopicList(newList)
  }
  const reGenerate = (item: any) => {
    emitReGenerate?.(item)
  }

  useEffect(() => {
    if (isScrollBottom) {
      const container: any = containerRef.current;
      if (!container) return;

      const observer = new MutationObserver(() => {
        container.scrollTop = container.scrollHeight;
      });

      observer.observe(container, { childList: true, subtree: true });
      return () => observer.disconnect();
    }
  }, [isScrollBottom]);

  const downloadPlan = async () => {
    if (!allFinished) {
      message.info('正在生成教案中，请稍后下载')
      return
    }
    try {
      const params = topicList?.map((item: any) => {
        return {
          name: item.name,
          key: item.key,
          value: item.content
        }
      })
      const res = await reqExportMessage({
        templateName,
        params
      })
      await download(res, `${templateName}.docx`);
    } catch (error) {
      message.error('导出失败');
    }
  }
  return (
    <div className='plan-content'>
      <div className='right-header'>
        <img src={require('@/assets/img/teachingPlan/ok.png')} />
        教案已生成
        <Button onClick={downloadPlan}>下载教案</Button>
      </div>
      <div className='topic-list' ref={containerRef}>
        {topicList.map((item: any) => {
          return <div key={item.id} className='topic-item'>
            <div className='topic-header'>
              {/* <div className='topic-type'>数学</div> */}
              <div className='topic-title'>{item.name}</div>
              <Space>
                {item.readOnly ? <Button onClick={() => {
                  editTopic(item, 1)
                }}>编辑</Button> : <Button onClick={() => {
                  editTopic(item, 2)
                }}>保存</Button>}
                <Button className='btn-primary' onClick={() => {
                  reGenerate(item)
                }}>重新生成</Button>
                <Button onClick={() => {
                  deleteTopic(item)
                }}>删除</Button>
              </Space>
            </div>
            <div className='topic-content'>
              <Input.TextArea
                value={item.content}
                bordered={false}
                readOnly={item.readOnly}
                onChange={(e) => {
                  setValue(e.target.value)
                  const newList = topicList.map((i: any) => {
                    if (i.key === item.key) {
                      return { ...i, content: e.target.value }
                    } else {
                      return i
                    }
                  })
                  setTopicList(newList)
                }}
                placeholder="请输入"
                autoSize={{ minRows: 5 }}
              />
            </div>
          </div>
        })}
      </div>
    </div>
  );
};

export default PlanContent;