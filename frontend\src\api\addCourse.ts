import { message } from 'antd';
import HTTP from './request';

export const queryResourceLabel = (ids: string[]) =>
  HTTP.post(`/learn/v1/homepage/resource/info`, ids)


export function folderChildren(folderId: string) {
  // return HTTP.get(`/rman/v1/folder/children?folderId=${folderId}`)
  return HTTP(`/rman/v1/folder/children?folderPath=${encodeURIComponent(folderId)}%2F&isChildCount=true`,{
    method:'get'
  })
}

export function searchResList(id:any,path: any, current: number, type: string[] | undefined, pageSize?:number,keyword?:string, condition?: any) {
  if (type) {
    condition = [
      ...condition,
      {
        field: 'type_',
        searchRelation: 0,
        value: type,
      },
    ];
  }
  return HTTP('/rman/v1/search/folder',{
    method:'post',
    data:{
      folderId: id,
      folderPath:path+'/',
      // path: encodeURIComponent(id),
      keyword: [keyword],
      conditions: condition,
      sortFields: [
        {
          field: 'createDate_',
          isDesc: true,
        },
      ],
      pageIndex: current,
      pageSize: pageSize || 9,
      anonymous: true
    }
  })
}

export function searchResAll(
  id: any,
  type: string,
  path: string,
  keyword: string,
  starttime: string,
  endtime: string,
  current: number,
  isconvert?: boolean,
  condition?: any,
) {

  let conditions:any = []
  if(type){
    conditions.push({
      field: 'type_',
      searchRelation: 0,
      value: [type],
    })
  }
  if(isconvert){
    conditions.push({
      field:"fileext",
      searchRelation:11,
      value:["PDF"]
    })
  }
  conditions = conditions.concat(condition ?? []);
  return HTTP('/rman/v1/search/all',{
    method:'post',
    data:{
      folderId: id,
      keyword: [keyword],
      folderPath: path,
      conditions: conditions,
      sortFields: [
        {
          field: 'createDate_',
          isDesc: true,
        },
      ],
      pageIndex: current,
      pageSize: 9,
    }
  })
}

export function getTreebylevel(level: number = 2) {
  return HTTP(`/rman/v1/folder/all/tree?level=${level}`,{
    method:'get'
  })
}

//查询我的收藏
export const getmycollectionlist = (data: any) => {
  return HTTP(`/rman/v1/metadata/resource/search/collection`, {
      method: 'POST',
      data: data
  })
}
//我的录播
export const getmyvideolist = (data: any) => {
  return HTTP(`/rman/v1/search/my/video`, {
      method: 'POST',
      data: data
  })
}
//查询分享给自己的
export const shareMyself = (data: any) => {
  return HTTP(`/rman/v1/share/search`, {
      method: 'POST',
      data
  })
}


//根据名称查询所有符合要求的资源知识点片段
export const getAllpoint = (params: any) => {
  return HTTP("/rman/v1/search/knowledge/point", {
    method: "POST",
    params,
  })
}

//查询知识点推荐资源
export const queryRecommendResource = ( params: any) => {
  return HTTP("/rman/v1/search/knowledge/point/recommend/resource", {
    method: "GET",
    params
  })
}
//查询推荐知识点
//
export const queryRecommendPoint = (data: any, params: any) => {
  return HTTP("/rman/v1/search/knowledge/point", {
    method: "POST",
    data,
    params
  })
}

export const getAmazonConfig = (url: string, options: any) => {
  return HTTP(url, options);
};
//查询资源
export function resourceDetail(id: string, pathType?: number) {
  return HTTP.get(`/rman/v1/entity/base/${id}`, {
    params: {
      pathType,
      isSysAuth: true
    },
  })
}