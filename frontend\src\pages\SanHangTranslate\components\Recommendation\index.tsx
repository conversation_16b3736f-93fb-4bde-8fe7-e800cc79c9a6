import React from 'react';
import './index.less';
import icon2 from '@/images/sanhang/icon2.png';
// 假设有其他图标，这里先用占位符
import docIcon from '@/images/sanhang/docment.png'; // 假设文档图标
import bookIcon from '@/images/sanhang/book.png'; // 假设图书图标
import mp4Icon from '@/images/sanhang/mp4.png'; // 假设MP4图标
import atlasIcon from '@/images/sanhang/map.png'; // 假设图谱图标
import { Col, Row } from 'antd';


const resources = [
  {
    type: 'document',
    icon: docIcon,
    iconText: '文献期刊',
    title: '火力与指挥控制杂志',
    image: 'https://via.placeholder.com/180x120/E0E0E0/808080?text=Document_Image', // 占位图片
    description: '《火力与指挥控制》（月刊）创刊于1976年，由中华人民共和国信息产业部主管，中国国防工业火力与指挥控制研究会和中国兵器工业集团北方自动控制技术研究所主办。',
  },
  {
    type: 'book',
    icon: bookIcon,
    iconText: '图书',
    title: '火力与指挥控制杂志',
    image: 'https://via.placeholder.com/180x120/E0E0E0/808080?text=Book_Image', // 占位图片
    description: '《火力与指挥控制》（月刊）创刊于1976年，由中华人民共和国信息产业部主管，中国国防工业火力与指挥控制研究会和中国兵器工业集团北方自动控制技术研究所主办。',
  },
  {
    type: 'mp4',
    icon: mp4Icon,
    iconText: 'MP4',
    title: '轰炸指挥飞机应用讲解',
    image: 'https://via.placeholder.com/260x160/B0E0E6/000000?text=MP4_Image', // 占位图片
  },
  {
    type: 'mp4',
    icon: mp4Icon,
    iconText: 'MP4',
    title: '轰炸指挥飞机应用讲解',
    image: 'https://via.placeholder.com/260x160/B0E0E6/000000?text=MP4_Image', // 占位图片
  },
  {
    type: 'mp4',
    icon: mp4Icon,
    iconText: 'MP4',
    title: '轰炸指挥飞机应用讲解',
    image: 'https://via.placeholder.com/260x160/B0E0E6/000000?text=MP4_Image', // 占位图片
  },
  {
    type: 'document',
    icon: docIcon,
    iconText: '文献期刊',
    title: '火力与指挥控制杂志',
    image: 'https://via.placeholder.com/180x120/E0E0E0/808080?text=Document_Image', // 占位图片
    description: '《火力与指挥控制》（月刊）创刊于1976年，由中华人民共和国信息产业部主管，中国国防工业火力与指挥控制研究会和中国兵器工业集团北方自动控制技术研究所主办。',
  },
  {
    type: 'map',
    icon: atlasIcon,
    iconText: '图谱',
    title: '轰炸指挥飞机应用讲解',
    image: 'https://via.placeholder.com/260x160/DDA0DD/000000?text=Atlas_Image', // 占位图片
  },
  {
    type: 'map',
    icon: atlasIcon,
    iconText: '图谱',
    title: '轰炸指挥飞机应用讲解',
    image: 'https://via.placeholder.com/260x160/DDA0DD/000000?text=Atlas_Image', // 占位图片
  },
];


const Recommendation: React.FC = () => {
  return (
    <div className="recommendation">
      <div className="title_section">
        <img className='square_icon' src={icon2} />
        <span className="title_text">推荐资源</span>
      </div>
      <div className="content_section">
        <Row gutter={[16, 16]}>
        {
            resources.map((resource, index) => {
                return ( 
                    <Col key={index} xxl={6} xl={8}>
                        <div className={`resource_card ${resource.type}_card`}>
                            <div className="card_header">
                                <img src={resource.icon} className="card_icon" />
                                <span className="card_icon_text">{resource.iconText}</span>
                            </div>
                            <div className='card_body'>
                                {['book','document'].includes(resource.type) && <div className="book_content">
                                    <img src={resource.image} className='cover' />
                                    <div className='title_box'>
                                        <span className='title'>{resource.title}</span>
                                        <span className='description'>{resource.description}</span>
                                    </div>
                                </div>}
                                {['mp4','map'].includes(resource.type) && <div className="video_content">
                                    <img src={resource.image} className='cover' />
                                    <div className='title_box'>
                                        <span className='title'>{resource.title}</span>                                       
                                    </div>
                                </div>}
                            </div>
                        </div>
                    </Col>
                );
            })
        }
        </Row>        
      </div>
    </div>
  );
};

export default Recommendation;
