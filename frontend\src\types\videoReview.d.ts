declare namespace VideoReview {
  export interface IPosition {
    campus: string; // 校区
    academicBuilding: string; // 教学楼
    classroomNumber: string; // 教室
  }

  export interface ITime {
    week: number;
    section: string;
  }

  export interface ITeacher {
    userName: string;
    userCode: string;
    avatar: string;
  }

  export interface IItem {
    courseId: string;
    courseName: string;
    courseNumber: string;
    serialNumber: string;
    coursePositions: IPosition[];
    colleges: string;
    majors: string;
    teachers: ITeacher[];
    courseTimes: ITime[];
    collectionsTotal: number;
    whetherCollection: boolean;
    latestFestival: string;
    collegeNames: any;
    teacherNames: any;
  }

  export interface IBuilding {
    [propName: string]: {
      academicBuildingName: string;
      classrooms: string[];
    }[];
  }

  export interface ICondition {
    courseName?: string;
    teacherName?: string;
    campus?: string;
    academicBuilding?: string;
    classroom?: string;
    orderBy?: string;
    isDesc?: string;
  }

  export interface IQuery extends ICondition {
    page: number;
    size: number;
    isDesc?: boolean;
    orderBy?: string;
  }

  export interface IVideoInfo {
    scheduleId: string;
    weekDay: string;
    section: string;
    videoId: string;
  }

  export interface IVideoRecord {
    week: number;
    weekStartTime: string;
    videoInfos: IVideoInfo[];
  }

  export interface ICourseDetail {
    courseId: string;
    courseName: string;
    collectionsTotal: number;
    whetherCollection: boolean;
    colleges?: any;
    teachers: {
      userName: string;
      userCode: string;
      avatar: string;
    }[];
    recordingVideoInfoShows: IVideoRecord[];
  }

  export interface ICollect {
    courseId: string;
    courseName: string;
    teachers?: ITeacher[];
    collegeName?: any;
    courseStatus?: -1 | 0 | 1, // -1,已删除；0,已下架；1,正常
    courseTime?: string;
    courseLocation?: string;
    visitors?: number; //浏览人数
    collectionsTotal?: number; // 收藏人数
    courseType: string;
    courseCover?: string; //课程封面
  }

  export interface CollectQuery {
    courseName?: string;
    courseType: string;
    page: number;
    size: number;
  }
}
