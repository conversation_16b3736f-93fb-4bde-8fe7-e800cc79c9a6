/* Tiny Export plugin
 *
 * Copyright 2020 Tiny Technologies LLC. All rights reserved.
 *
 * Version: 0.1.0-8
 */

!function(){"use strict";function t(){}function w(t){return function(){return t}}function a(r){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var A=n.concat(t);return r.apply(null,A)}}var o=w(!1),i=w(!0),xt="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t,e,A){return t(A={path:e,exports:{},require:function(t,e){return rt(null==e&&A.path)}},A.exports),A.exports}function rt(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}e(function(t,e){t.exports=function(){function r(t){var e=typeof t;return t!==null&&(e==="object"||e==="function")}function a(t){return typeof t==="function"}var t=void 0;if(Array.isArray){t=Array.isArray}else{t=function(t){return Object.prototype.toString.call(t)==="[object Array]"}}var A=t,n=0,e=void 0,i=void 0,o=function t(e,A){v[n]=e;v[n+1]=A;n+=2;if(n===2){if(i){i(y)}else{Q()}}};function s(t){i=t}function c(t){o=t}var u=typeof window!=="undefined"?window:undefined,l=u||{},h=l.MutationObserver||l.WebKitMutationObserver,f=typeof self==="undefined"&&typeof process!=="undefined"&&{}.toString.call(process)==="[object process]",d=typeof Uint8ClampedArray!=="undefined"&&typeof importScripts!=="undefined"&&typeof MessageChannel!=="undefined";function p(){return function(){return process.nextTick(y)}}function g(){if(typeof e!=="undefined"){return function(){e(y)}}return m()}function B(){var t=0;var e=new h(y);var A=document.createTextNode("");e.observe(A,{characterData:true});return function(){A.data=t=++t%2}}function w(){var t=new MessageChannel;t.port1.onmessage=y;return function(){return t.port2.postMessage(0)}}function m(){var t=setTimeout;return function(){return t(y,1)}}var v=new Array(1e3);function y(){for(var t=0;t<n;t+=2){var e=v[t];var A=v[t+1];e(A);v[t]=undefined;v[t+1]=undefined}n=0}function C(){try{var t=Function("return this")().require("vertx");e=t.runOnLoop||t.runOnContext;return g()}catch(t){return m()}}var Q=void 0;if(f){Q=p()}else if(h){Q=B()}else if(d){Q=w()}else if(u===undefined&&typeof rt==="function"){Q=C()}else{Q=m()}function F(t,e){var A=this;var r=new this.constructor(E);if(r[U]===undefined){X(r)}var n=A._state;if(n){var i=arguments[n-1];o(function(){return z(n,r,i,A._result)})}else{D(A,r,t,e)}return r}function b(t){var e=this;if(t&&typeof t==="object"&&t.constructor===e){return t}var A=new e(E);R(A,t);return A}var U=Math.random().toString(36).substring(2);function E(){}var N=void 0,L=1,H=2;function S(){return new TypeError("You cannot resolve a promise with itself")}function x(){return new TypeError("A promises callback cannot return that same promise.")}function _(t,e,A,r){try{t.call(e,A,r)}catch(t){return t}}function I(t,r,n){o(function(e){var A=false;var t=_(n,r,function(t){if(A){return}A=true;if(r!==t){R(e,t)}else{M(e,t)}},function(t){if(A){return}A=true;K(e,t)},"Settle: "+(e._label||" unknown promise"));if(!A&&t){A=true;K(e,t)}},t)}function T(e,t){if(t._state===L){M(e,t._result)}else if(t._state===H){K(e,t._result)}else{D(t,undefined,function(t){return R(e,t)},function(t){return K(e,t)})}}function O(t,e,A){if(e.constructor===t.constructor&&A===F&&e.constructor.resolve===b){T(t,e)}else{if(A===undefined){M(t,e)}else if(a(A)){I(t,e,A)}else{M(t,e)}}}function R(e,t){if(e===t){K(e,S())}else if(r(t)){var A=void 0;try{A=t.then}catch(t){K(e,t);return}O(e,t,A)}else{M(e,t)}}function P(t){if(t._onerror){t._onerror(t._result)}k(t)}function M(t,e){if(t._state!==N){return}t._result=e;t._state=L;if(t._subscribers.length!==0){o(k,t)}}function K(t,e){if(t._state!==N){return}t._state=H;t._result=e;o(P,t)}function D(t,e,A,r){var n=t._subscribers;var i=n.length;t._onerror=null;n[i]=e;n[i+L]=A;n[i+H]=r;if(i===0&&t._state){o(k,t)}}function k(t){var e=t._subscribers;var A=t._state;if(e.length===0){return}var r=void 0,n=void 0,i=t._result;for(var o=0;o<e.length;o+=3){r=e[o];n=e[o+A];if(r){z(A,r,n,i)}else{n(i)}}t._subscribers.length=0}function z(t,e,A,r){var n=a(A),i=void 0,o=void 0,s=true;if(n){try{i=A(r)}catch(t){s=false;o=t}if(e===i){K(e,x());return}}else{i=r}if(e._state!==N);else if(n&&s){R(e,i)}else if(s===false){K(e,o)}else if(t===L){M(e,i)}else if(t===H){K(e,i)}}function j(A,t){try{t(function t(e){R(A,e)},function t(e){K(A,e)})}catch(t){K(A,t)}}var q=0;function V(){return q++}function X(t){t[U]=q++;t._state=undefined;t._result=undefined;t._subscribers=[]}function G(){return new Error("Array Methods must be provided an Array")}var J=function(){function t(t,e){this._instanceConstructor=t;this.promise=new t(E);if(!this.promise[U]){X(this.promise)}if(A(e)){this.length=e.length;this._remaining=e.length;this._result=new Array(this.length);if(this.length===0){M(this.promise,this._result)}else{this.length=this.length||0;this._enumerate(e);if(this._remaining===0){M(this.promise,this._result)}}}else{K(this.promise,G())}}t.prototype._enumerate=function t(e){for(var A=0;this._state===N&&A<e.length;A++){this._eachEntry(e[A],A)}};t.prototype._eachEntry=function t(e,A){var r=this._instanceConstructor;var n=r.resolve;if(n===b){var i=void 0;var o=void 0;var s=false;try{i=e.then}catch(t){s=true;o=t}if(i===F&&e._state!==N){this._settledAt(e._state,A,e._result)}else if(typeof i!=="function"){this._remaining--;this._result[A]=e}else if(r===et){var a=new r(E);if(s){K(a,o)}else{O(a,e,i)}this._willSettleAt(a,A)}else{this._willSettleAt(new r(function(t){return t(e)}),A)}}else{this._willSettleAt(n(e),A)}};t.prototype._settledAt=function t(e,A,r){var n=this.promise;if(n._state===N){this._remaining--;if(e===H){K(n,r)}else{this._result[A]=r}}if(this._remaining===0){M(n,this._result)}};t.prototype._willSettleAt=function t(e,A){var r=this;D(e,undefined,function(t){return r._settledAt(L,A,t)},function(t){return r._settledAt(H,A,t)})};return t}();function W(t){return new J(this,t).promise}function Y(n){var i=this;if(A(n))return new i(function(t,e){for(var A=n.length,r=0;r<A;r++)i.resolve(n[r]).then(t,e)});else return new i(function(t,e){return e(new TypeError("You must pass an array to race."))})}function Z(t){var e=new this(E);return K(e,t),e}function $(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function tt(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}var et=function(){function e(t){this[U]=V();this._result=this._state=undefined;this._subscribers=[];if(E!==t){typeof t!=="function"&&$();this instanceof e?j(this,t):tt()}}e.prototype.catch=function t(e){return this.then(null,e)};e.prototype.finally=function t(e){var A=this;var r=A.constructor;if(a(e)){return A.then(function(t){return r.resolve(e()).then(function(){return t})},function(t){return r.resolve(e()).then(function(){throw t})})}return A.then(e,e)};return e}();function At(){var t=void 0;if(void 0!==xt)t=xt;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var A=null;try{A=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===A&&!e.cast)return}t.Promise=et}return et.prototype.then=F,et.all=function(t){return new J(this,t).promise},et.race=function(n){var i=this;return A(n)?new i(function(t,e){for(var A=n.length,r=0;r<A;r++)i.resolve(n[r]).then(t,e)}):new i(function(t,e){return e(new TypeError("You must pass an array to race."))})},et.resolve=b,et.reject=function(t){var e=new this(E);return K(e,t),e},et._setScheduler=function(t){i=t},et._setAsap=function(t){o=t},et._asap=o,et.polyfill=function(){var t=void 0;if(void 0!==xt)t=xt;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var A=null;try{A=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===A&&!e.cast)return}t.Promise=et},(et.Promise=et).polyfill(),et}()});function A(){return c}var s=function(){return(s=Object.assign||function(t){for(var e,A=1,r=arguments.length;A<r;A++)for(var n in e=arguments[A])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)},c={fold:function(t,e){return t()},is:o,isSome:o,isNone:i,getOr:u,getOrThunk:n,getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:w(null),getOrUndefined:w(void 0),or:u,orThunk:n,map:A,each:t,bind:A,exists:o,forall:i,filter:A,equals:r,equals_:r,toArray:function(){return[]},toString:w("none()")};function r(t){return t.isNone()}function n(t){return t()}function u(t){return t}function l(e){return function(t){return t.dom.nodeType===e}}function h(e){return function(t){return typeof t===e}}function f(t){return!(null==t)}function d(t,e){for(var A=0,r=t.length;A<r;A++){e(t[A],A)}}function p(t,e){return function(t,e,A){for(var r=0,n=t.length;r<n;r++){var i=t[r];if(e(i,r))return I.some(i);if(A(i,r))break}return I.none()}(t,e,o)}function g(A){var r,n=!1;return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return n||(n=!0,r=A.apply(null,t)),r}}function B(t,e){var A=function(t,e){for(var A=0;A<t.length;A++){var r=t[A];if(r.test(e))return r}}(t,e);if(!A)return{major:0,minor:0};function r(t){return Number(e.replace(A,"$"+t))}return X(r(1),r(2))}function m(t,e){var A=String(e).toLowerCase();return p(t,function(t){return t.search(A)})}function v(t,e){return-1!==t.indexOf(e)}function y(e){return function(t){return v(t,e)}}function C(t){return window.matchMedia(t).matches}function Q(t,e){return A=t.dom,r=e.dom,n=A,i=r,o=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(n.compareDocumentPosition(i)&o);var A,r,n,i,o}function F(t,e){return ft().browser.isIE()?Q(t,e):(A=e,r=t.dom,n=A.dom,r!==n&&r.contains(n));var A,r,n}function b(t){return O.fromDom(t.dom.ownerDocument)}function U(t){return function(t,e){for(var A=t.length,r=new Array(A),n=0;n<A;n++){var i=t[n];r[n]=e(i,n)}return r}(t.dom.childNodes,O.fromDom)}function E(t){var e=gt(t);return K(e)?I.some(e):I.none()}function N(t){return O.fromDom(t.dom.host)}function L(t){if(pt()&&f(t.target)){var e=O.fromDom(t.target);if(R(e)&&Bt(e)&&t.composed&&t.composedPath){var A=t.composedPath();if(A)return 0===(r=A).length?I.none():I.some(r[0])}}var r;return I.from(t.target)}function H(){return mt(O.fromDom(document))}var S,x,_=function(A){function t(){return n}function e(t){return t(A)}var r=w(A),n={fold:function(t,e){return e(A)},is:function(t){return A===t},isSome:i,isNone:o,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:t,orThunk:t,map:function(t){return _(t(A))},each:function(t){t(A)},bind:e,exists:e,forall:e,filter:function(t){return t(A)?n:c},toArray:function(){return[A]},toString:function(){return"some("+A+")"},equals:function(t){return t.is(A)},equals_:function(t,e){return t.fold(o,function(t){return e(A,t)})}};return n},I={some:_,none:A,from:function(t){return null==t?c:_(t)}},T=function(t){if(null==t)throw new Error("Node cannot be null or undefined");return{dom:t}},O={fromHtml:function(t,e){var A=(e||document).createElement("div");if(A.innerHTML=t,!A.hasChildNodes()||1<A.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return T(A.childNodes[0])},fromTag:function(t,e){var A=(e||document).createElement(t);return T(A)},fromText:function(t,e){var A=(e||document).createTextNode(t);return T(A)},fromDom:T,fromPoint:function(t,e,A){return I.from(t.dom.elementFromPoint(e,A)).map(T)}},R=("undefined"!=typeof window||Function("return this;")(),l(1)),P=l(3),M=l(9),K=l(11),D=(S="string",function(t){return A=typeof(e=t),(null===e?"null":"object"==A&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==A&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":A)===S;var e,A}),k=h("boolean"),z=(x=void 0,function(t){return x===t}),j=h("function"),q=h("number"),V=function(){return X(0,0)},X=function(t,e){return{major:t,minor:e}},G={nu:X,detect:function(t,e){var A=String(e).toLowerCase();return 0===t.length?V():B(t,A)},unknown:V},J=function(t,A){return m(t,A).map(function(t){var e=G.detect(t.versionRegexes,A);return{current:t.name,version:e}})},W=function(t,A){return m(t,A).map(function(t){var e=G.detect(t.versionRegexes,A);return{current:t.name,version:e}})},Y=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Z=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return v(t,"edge/")&&v(t,"chrome")&&v(t,"safari")&&v(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Y],search:function(t){return v(t,"chrome")&&!v(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return v(t,"msie")||v(t,"trident")}},{name:"Opera",versionRegexes:[Y,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:y("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:y("firefox")},{name:"Safari",versionRegexes:[Y,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(v(t,"safari")||v(t,"mobile/"))&&v(t,"applewebkit")}}],$=[{name:"Windows",search:y("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return v(t,"iphone")||v(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:y("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:y("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:y("linux"),versionRegexes:[]},{name:"Solaris",search:y("sunos"),versionRegexes:[]},{name:"FreeBSD",search:y("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:y("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],tt={browsers:w(Z),oses:w($)},et="Firefox",At=function(t){function e(t){return function(){return A===t}}var A=t.current,r=t.version;return{current:A,version:r,isEdge:e("Edge"),isChrome:e("Chrome"),isIE:e("IE"),isOpera:e("Opera"),isFirefox:e(et),isSafari:e("Safari")}},nt={unknown:function(){return At({current:void 0,version:G.unknown()})},nu:At,edge:w("Edge"),chrome:w("Chrome"),ie:w("IE"),opera:w("Opera"),firefox:w(et),safari:w("Safari")},it="Windows",ot="Android",st="Solaris",at="FreeBSD",ct="ChromeOS",ut=function(t){function e(t){return function(){return A===t}}var A=t.current,r=t.version;return{current:A,version:r,isWindows:e(it),isiOS:e("iOS"),isAndroid:e(ot),isOSX:e("OSX"),isLinux:e("Linux"),isSolaris:e(st),isFreeBSD:e(at),isChromeOS:e(ct)}},lt={unknown:function(){return ut({current:void 0,version:G.unknown()})},nu:ut,windows:w(it),ios:w("iOS"),android:w(ot),linux:w("Linux"),osx:w("OSX"),solaris:w(st),freebsd:w(at),chromeos:w(ct)},ht=function(t,e){var A,r,n,i,o,s,a,c,u,l,h,f,d=tt.browsers(),p=tt.oses(),g=J(d,t).fold(nt.unknown,nt.nu),B=W(p,t).fold(lt.unknown,lt.nu);return{browser:g,os:B,deviceType:(r=g,n=t,i=e,o=(A=B).isiOS()&&!0===/ipad/i.test(n),s=A.isiOS()&&!o,a=A.isiOS()||A.isAndroid(),c=a||i("(pointer:coarse)"),u=o||!s&&a&&i("(min-device-width:768px)"),l=s||a&&!u,h=r.isSafari()&&A.isiOS()&&!1===/safari/i.test(n),f=!l&&!u&&!h,{isiPad:w(o),isiPhone:w(s),isTablet:w(u),isPhone:w(l),isTouch:w(c),isAndroid:A.isAndroid,isiOS:A.isiOS,isWebView:w(h),isDesktop:w(f)})}},ft=g(function(){return ht(navigator.userAgent,C)}),dt=j(Element.prototype.attachShadow)&&j(Node.prototype.getRootNode),pt=w(dt),gt=dt?function(t){return O.fromDom(t.dom.getRootNode())}:function(t){return M(t)?t:b(t)},Bt=function(t){return f(t.dom.shadowRoot)},wt=function(t){var e=P(t)?t.dom.parentNode:t.dom;if(null==e||null===e.ownerDocument)return!1;var A,r,n=e.ownerDocument;return E(O.fromDom(e)).fold(function(){return n.body.contains(e)},(A=wt,r=N,function(t){return A(r(t))}))},mt=function(t){var e=t.dom.body;if(null==e)throw new Error("Body is not available yet");return O.fromDom(e)},vt=e(function(Ht,St){!function(){function se(t){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}!function(t){if("object"!==se(t.console)){t.console={};for(var e,A,r=t.console,n=function(){},i=["memory"],o="assert,clear,count,debug,dir,dirxml,error,exception,group,groupCollapsed,groupEnd,info,log,markTimeline,profile,profiles,profileEnd,show,table,time,timeEnd,timeline,timelineEnd,timeStamp,trace,warn".split(",");e=i.pop();)r[e]||(r[e]={});for(;A=o.pop();)r[A]||(r[A]=n)}var s,a,c,u,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";void 0===t.btoa&&(t.btoa=function(t){var e,A,r,n,i,o=0,s=0,a="",c=[];if(!t)return t;for(;e=(i=t.charCodeAt(o++)<<16|t.charCodeAt(o++)<<8|t.charCodeAt(o++))>>18&63,A=i>>12&63,r=i>>6&63,n=63&i,c[s++]=l.charAt(e)+l.charAt(A)+l.charAt(r)+l.charAt(n),o<t.length;);a=c.join("");var u=t.length%3;return(u?a.slice(0,u-3):a)+"===".slice(u||3)}),void 0===t.atob&&(t.atob=function(t){var e,A,r,n,i,o,s=0,a=0,c=[];if(!t)return t;for(t+="";e=(o=l.indexOf(t.charAt(s++))<<18|l.indexOf(t.charAt(s++))<<12|(n=l.indexOf(t.charAt(s++)))<<6|(i=l.indexOf(t.charAt(s++))))>>16&255,A=o>>8&255,r=255&o,c[a++]=64==n?String.fromCharCode(e):64==i?String.fromCharCode(e,A):String.fromCharCode(e,A,r),s<t.length;);return c.join("")}),Array.prototype.map||(Array.prototype.map=function(t){if(null==this||"function"!=typeof t)throw new TypeError;for(var e=Object(this),A=e.length>>>0,r=new Array(A),n=1<arguments.length?arguments[1]:void 0,i=0;i<A;i++)i in e&&(r[i]=t.call(n,e[i],i,e));return r}),Array.isArray||(Array.isArray=function(t){return"[object Array]"===Object.prototype.toString.call(t)}),Array.prototype.forEach||(Array.prototype.forEach=function(t,e){if(null==this||"function"!=typeof t)throw new TypeError;for(var A=Object(this),r=A.length>>>0,n=0;n<r;n++)n in A&&t.call(e,A[n],n,A)}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),A=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var r=arguments[1],n=0;n<A;){var i=e[n];if(t.call(r,i,n,e))return i;n++}},configurable:!0,writable:!0}),Object.keys||(Object.keys=(s=Object.prototype.hasOwnProperty,a=!{toString:null}.propertyIsEnumerable("toString"),u=(c=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"]).length,function(t){if("object"!==se(t)&&("function"!=typeof t||null===t))throw new TypeError;var e,A,r=[];for(e in t)s.call(t,e)&&r.push(e);if(a)for(A=0;A<u;A++)s.call(t,c[A])&&r.push(c[A]);return r})),"function"!=typeof Object.assign&&(Object.assign=function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");t=Object(t);for(var e=1;e<arguments.length;e++){var A=arguments[e];if(null!=A)for(var r in A)Object.prototype.hasOwnProperty.call(A,r)&&(t[r]=A[r])}return t}),String.prototype.trim||(String.prototype.trim=function(){return this.replace(/^\s+|\s+$/g,"")}),String.prototype.trimLeft||(String.prototype.trimLeft=function(){return this.replace(/^\s+/g,"")}),String.prototype.trimRight||(String.prototype.trimRight=function(){return this.replace(/\s+$/g,"")}),Number.isInteger=Number.isInteger||function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")());var t,e,A,U,a,E,N,f,d,L,o,i,s,c,u,l,r,n,h,p,g,B,w,m,v,y,H,S,x,C,Q,F,b,_,I,T,O,R,P,M,K,D,k,z,j,q,V,X,G,J,W,Y,Z,$,tt,et,At,rt,nt,it,ot,st,at=function(ne){function ie(i){if("object"!==se(i))throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var o={};this.subscribe=function(t,e,A){if(A=A||!1,"string"!=typeof t||"function"!=typeof e||"boolean"!=typeof A)throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");o.hasOwnProperty(t)||(o[t]={});var r=Math.random().toString(35);return o[t][r]=[e,!!A],r},this.unsubscribe=function(t){for(var e in o)if(o[e][t])return delete o[e][t],0===Object.keys(o[e]).length&&delete o[e],!0;return!1},this.publish=function(t){if(o.hasOwnProperty(t)){var e=Array.prototype.slice.call(arguments,1),A=[];for(var r in o[t]){var n=o[t][r];try{n[0].apply(i,e)}catch(t){ne.console&&console.error("jsPDF PubSub Error",t.message,t)}n[1]&&A.push(r)}A.length&&A.forEach(this.unsubscribe)}},this.getTopics=function(){return o}}function oe(t,e,n,A){var r={},i=[],o=1;"object"===se(t)&&(t=(r=t).orientation,e=r.unit||e,n=r.format||n,A=r.compress||r.compressPdf||A,i=r.filters||(!0===A?["FlateEncode"]:i),o="number"==typeof r.userUnit?Math.abs(r.userUnit):1),e=e||"mm",t=(""+(t||"P")).toLowerCase();var s=r.putOnlyUsedFonts||!0,W={},a={internal:{},__private__:{}};a.__private__.PubSub=ie;var c="1.3",u=a.__private__.getPdfVersion=function(){return c},l=(a.__private__.setPdfVersion=function(t){c=t},{a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]}),h=(a.__private__.getPageFormats=function(){return l},a.__private__.getPageFormat=function(t){return l[t]});"string"==typeof n&&(n=h(n)),n=n||h("a4");var f,Y=a.f2=a.__private__.f2=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return t.toFixed(2)},Z=a.__private__.f3=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f3");return t.toFixed(3)},d="00000000000000000000000000000000",p=a.__private__.getFileId=function(){return d},g=a.__private__.setFileId=function(t){return t=t||"12345678901234567890123456789012".split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),d=t};a.setFileId=function(t){return g(t),this},a.getFileId=function(){return p()};var B=a.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),A=e<0?"+":"-",r=Math.floor(Math.abs(e/60)),n=Math.abs(e%60),i=[A,N(r),"'",N(n),"'"].join("");return["D:",t.getFullYear(),N(t.getMonth()+1),N(t.getDate()),N(t.getHours()),N(t.getMinutes()),N(t.getSeconds()),i].join("")},w=a.__private__.convertPDFDateToDate=function(t){var e=parseInt(t.substr(2,4),10),A=parseInt(t.substr(6,2),10)-1,r=parseInt(t.substr(8,2),10),n=parseInt(t.substr(10,2),10),i=parseInt(t.substr(12,2),10),o=parseInt(t.substr(14,2),10);parseInt(t.substr(16,2),10),parseInt(t.substr(20,2),10);return new Date(e,A,r,n,i,o,0)},m=a.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),"object"===se(t)&&"[object Date]"===Object.prototype.toString.call(t))e=B(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|\-0[0-9]|\-1[0-1])\'(0[0-9]|[1-5][0-9])\'?$/.test(t))throw new Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return f=e},v=a.__private__.getCreationDate=function(t){var e=f;return"jsDate"===t&&(e=w(f)),e};a.setCreationDate=function(t){return m(t),this},a.getCreationDate=function(t){return v(t)};var y,C,Q,F,b,$,U,E,N=a.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},L=!1,H=[],S=[],x=0,tt=(a.__private__.setCustomOutputDestination=function(t){C=t},a.__private__.resetCustomOutputDestination=function(t){C=void 0},a.__private__.out=function(t){var e;return t="string"==typeof t?t:t.toString(),(e=void 0===C?L?H[y]:S:C).push(t),L||(x+=t.length+1),e}),_=a.__private__.write=function(t){return tt(1===arguments.length?t.toString():Array.prototype.join.call(arguments," "))},I=a.__private__.getArrayBuffer=function(t){for(var e=t.length,A=new ArrayBuffer(e),r=new Uint8Array(A);e--;)r[e]=t.charCodeAt(e);return A},T=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]],et=(a.__private__.getStandardFonts=function(t){return T},r.fontSize||16),O=(a.__private__.setFontSize=a.setFontSize=function(t){return et=t,this},a.__private__.getFontSize=a.getFontSize=function(){return et}),At=r.R2L||!1,R=(a.__private__.setR2L=a.setR2L=function(t){return At=t,this},a.__private__.getR2L=a.getR2L=function(t){return At},a.__private__.setZoomMode=function(t){var e=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^\d*\.?\d*\%$/.test(t))Q=t;else if(isNaN(t)){if(-1===e.indexOf(t))throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');Q=t}else Q=parseInt(t,10)}),P=(a.__private__.getZoomMode=function(){return Q},a.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');F=t}),M=(a.__private__.getPageMode=function(){return F},a.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');b=t}),K=(a.__private__.getLayoutMode=function(){return b},a.__private__.setDisplayMode=a.setDisplayMode=function(t,e,A){return R(t),M(e),P(A),this},{title:"",subject:"",author:"",keywords:"",creator:""}),D=(a.__private__.getDocumentProperty=function(t){if(-1===Object.keys(K).indexOf(t))throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return K[t]},a.__private__.getDocumentProperties=function(t){return K},a.__private__.setDocumentProperties=a.setProperties=a.setDocumentProperties=function(t){for(var e in K)K.hasOwnProperty(e)&&t[e]&&(K[e]=t[e]);return this},a.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(K).indexOf(t))throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return K[t]=e},0),k=[],rt={},z={},j=0,q=[],V=[],nt=new ie(a),X=r.hotfixes||[],G=a.__private__.newObject=function(){var t=J();return it(t,!0),t},J=a.__private__.newObjectDeferred=function(){return k[++D]=function(){return x},D},it=function(t,e){return e="boolean"==typeof e&&e,k[t]=x,e&&tt(t+" 0 obj"),t},ot=a.__private__.newAdditionalObject=function(){var t={objId:J(),content:""};return V.push(t),t},st=J(),at=J(),ct=a.__private__.decodeColorString=function(t){var e=t.split(" ");if(2===e.length&&("g"===e[1]||"G"===e[1])){var A=parseFloat(e[0]);e=[A,A,A,"r"]}for(var r="#",n=0;n<3;n++)r+=("0"+Math.floor(255*parseFloat(e[n])).toString(16)).slice(-2);return r},ut=a.__private__.encodeColorString=function(t){var e;"string"==typeof t&&(t={ch1:t});var A=t.ch1,r=t.ch2,n=t.ch3,i=t.ch4,o=(t.precision,"draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"]);if("string"==typeof A&&"#"!==A.charAt(0)){var s=new RGBColor(A);if(s.ok)A=s.toHex();else if(!/^\d*\.?\d*$/.test(A))throw new Error('Invalid color "'+A+'" passed to jsPDF.encodeColorString.')}if("string"==typeof A&&/^#[0-9A-Fa-f]{3}$/.test(A)&&(A="#"+A[1]+A[1]+A[2]+A[2]+A[3]+A[3]),"string"==typeof A&&/^#[0-9A-Fa-f]{6}$/.test(A)){var a=parseInt(A.substr(1),16);A=a>>16&255,r=a>>8&255,n=255&a}if(void 0===r||void 0===i&&A===r&&r===n)if("string"==typeof A)e=A+" "+o[0];else switch(t.precision){case 2:e=Y(A/255)+" "+o[0];break;case 3:default:e=Z(A/255)+" "+o[0]}else if(void 0===i||"object"===se(i)){if(i&&!isNaN(i.a)&&0===i.a)return e=["1.000","1.000","1.000",o[1]].join(" ");if("string"==typeof A)e=[A,r,n,o[1]].join(" ");else switch(t.precision){case 2:e=[Y(A/255),Y(r/255),Y(n/255),o[1]].join(" ");break;default:case 3:e=[Z(A/255),Z(r/255),Z(n/255),o[1]].join(" ")}}else if("string"==typeof A)e=[A,r,n,i,o[2]].join(" ");else switch(t.precision){case 2:e=[Y(A/255),Y(r/255),Y(n/255),Y(i/255),o[2]].join(" ");break;case 3:default:e=[Z(A/255),Z(r/255),Z(n/255),Z(i/255),o[2]].join(" ")}return e},lt=a.__private__.getFilters=function(){return i},ht=a.__private__.putStream=function(t){var e=(t=t||{}).data||"",A=t.filters||lt(),r=t.alreadyAppliedFilters||[],n=t.addLength1||!1,i=e.length,o={};!0===A&&(A=["FlateEncode"]);var s=t.additionalKeyValues||[],a=(o=void 0!==oe.API.processDataByFilters?oe.API.processDataByFilters(e,A):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(r)?r.join(" "):r.toString());0!==o.data.length&&(s.push({key:"Length",value:o.data.length}),!0===n&&s.push({key:"Length1",value:i})),0!=a.length&&(a.split("/").length-1==1?s.push({key:"Filter",value:a}):s.push({key:"Filter",value:"["+a+"]"})),tt("<<");for(var c=0;c<s.length;c++)tt("/"+s[c].key+" "+s[c].value);tt(">>"),0!==o.data.length&&(tt("stream"),tt(o.data),tt("endstream"))},ft=a.__private__.putPage=function(t){t.mediaBox;var e=t.number,A=t.data,r=t.objId,n=t.contentsObjId;it(r,!0);q[y].mediaBox.topRightX,q[y].mediaBox.bottomLeftX,q[y].mediaBox.topRightY,q[y].mediaBox.bottomLeftY;tt("<</Type /Page"),tt("/Parent "+t.rootDictionaryObjId+" 0 R"),tt("/Resources "+t.resourceDictionaryObjId+" 0 R"),tt("/MediaBox ["+parseFloat(Y(t.mediaBox.bottomLeftX))+" "+parseFloat(Y(t.mediaBox.bottomLeftY))+" "+Y(t.mediaBox.topRightX)+" "+Y(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&tt("/CropBox ["+Y(t.cropBox.bottomLeftX)+" "+Y(t.cropBox.bottomLeftY)+" "+Y(t.cropBox.topRightX)+" "+Y(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&tt("/BleedBox ["+Y(t.bleedBox.bottomLeftX)+" "+Y(t.bleedBox.bottomLeftY)+" "+Y(t.bleedBox.topRightX)+" "+Y(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&tt("/TrimBox ["+Y(t.trimBox.bottomLeftX)+" "+Y(t.trimBox.bottomLeftY)+" "+Y(t.trimBox.topRightX)+" "+Y(t.trimBox.topRightY)+"]"),null!==t.artBox&&tt("/ArtBox ["+Y(t.artBox.bottomLeftX)+" "+Y(t.artBox.bottomLeftY)+" "+Y(t.artBox.topRightX)+" "+Y(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&tt("/UserUnit "+t.userUnit),nt.publish("putPage",{objId:r,pageContext:q[e],pageNumber:e,page:A}),tt("/Contents "+n+" 0 R"),tt(">>"),tt("endobj");var i=A.join("\n");return it(n,!0),ht({data:i,filters:lt()}),tt("endobj"),r},dt=a.__private__.putPages=function(){var t,e,A=[];for(t=1;t<=j;t++)q[t].objId=J(),q[t].contentsObjId=J();for(t=1;t<=j;t++)A.push(ft({number:t,data:H[t],objId:q[t].objId,contentsObjId:q[t].contentsObjId,mediaBox:q[t].mediaBox,cropBox:q[t].cropBox,bleedBox:q[t].bleedBox,trimBox:q[t].trimBox,artBox:q[t].artBox,userUnit:q[t].userUnit,rootDictionaryObjId:st,resourceDictionaryObjId:at}));it(st,!0),tt("<</Type /Pages");var r="/Kids [";for(e=0;e<j;e++)r+=A[e]+" 0 R ";tt(r+"]"),tt("/Count "+j),tt(">>"),tt("endobj"),nt.publish("postPutPages")},pt=function(){!function(){for(var t in rt)rt.hasOwnProperty(t)&&(!1===s||!0===s&&W.hasOwnProperty(t))&&(e=rt[t],nt.publish("putFont",{font:e,out:tt,newObject:G,putStream:ht}),!0!==e.isAlreadyPutted&&(e.objectNumber=G(),tt("<<"),tt("/Type /Font"),tt("/BaseFont /"+e.postScriptName),tt("/Subtype /Type1"),"string"==typeof e.encoding&&tt("/Encoding /"+e.encoding),tt("/FirstChar 32"),tt("/LastChar 255"),tt(">>"),tt("endobj")));var e}(),nt.publish("putResources"),it(at,!0),tt("<<"),function(){for(var t in tt("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),tt("/Font <<"),rt)rt.hasOwnProperty(t)&&(!1===s||!0===s&&W.hasOwnProperty(t))&&tt("/"+t+" "+rt[t].objectNumber+" 0 R");tt(">>"),tt("/XObject <<"),nt.publish("putXobjectDict"),tt(">>")}(),tt(">>"),tt("endobj"),nt.publish("postPutResources")},gt=function(t,e,A){z.hasOwnProperty(e)||(z[e]={}),z[e][A]=t},Bt=function(t,e,A,r,n){n=n||!1;var i="F"+(Object.keys(rt).length+1).toString(10),o={id:i,postScriptName:t,fontName:e,fontStyle:A,encoding:r,isStandardFont:n,metadata:{}};return nt.publish("addFont",{font:o,instance:this}),void 0!==i&&(rt[i]=o,gt(i,e,A)),i},wt=a.__private__.pdfEscape=a.pdfEscape=function(t,e){return function(t,e){var A,r,n,i,o,s,a,c,u;if(n=(e=e||{}).sourceEncoding||"Unicode",o=e.outputEncoding,(e.autoencode||o)&&rt[$].metadata&&rt[$].metadata[n]&&rt[$].metadata[n].encoding&&(i=rt[$].metadata[n].encoding,!o&&rt[$].encoding&&(o=rt[$].encoding),!o&&i.codePages&&(o=i.codePages[0]),"string"==typeof o&&(o=i[o]),o)){for(a=!1,s=[],A=0,r=t.length;A<r;A++)(c=o[t.charCodeAt(A)])?s.push(String.fromCharCode(c)):s.push(t[A]),s[A].charCodeAt(0)>>8&&(a=!0);t=s.join("")}for(A=t.length;void 0===a&&0!==A;)t.charCodeAt(A-1)>>8&&(a=!0),A--;if(!a)return t;for(s=e.noBOM?[]:[254,255],A=0,r=t.length;A<r;A++){if((u=(c=t.charCodeAt(A))>>8)>>8)throw new Error("Character at position "+A+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");s.push(u),s.push(c-(u<<8))}return String.fromCharCode.apply(void 0,s)}(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},mt=a.__private__.beginPage=function(t,e){var A,r="string"==typeof e&&e.toLowerCase();if("string"==typeof t&&(A=h(t.toLowerCase()))&&(t=A[0],e=A[1]),Array.isArray(t)&&(e=t[1],t=t[0]),(isNaN(t)||isNaN(e))&&(t=n[0],e=n[1]),r){switch(r.substr(0,1)){case"l":t<e&&(r="s");break;case"p":e<t&&(r="s")}"s"===r&&(A=t,t=e,e=A)}(14400<t||14400<e)&&(console.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),t=Math.min(14400,t),e=Math.min(14400,e)),n=[t,e],L=!0,H[++j]=[],q[j]={objId:0,contentsObjId:0,userUnit:Number(o),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t),topRightY:Number(e)}},yt(j)},vt=function(){mt.apply(this,arguments),Kt(Mt),tt(Gt),0!==te&&tt(te+" J"),0!==Ae&&tt(Ae+" j"),nt.publish("addPage",{pageNumber:j})},yt=function(t){0<t&&t<=j&&(y=t)},Ct=a.__private__.getNumberOfPages=a.getNumberOfPages=function(){return H.length-1},Qt=function(t,e,A){var r,n=void 0;return A=A||{},t=void 0!==t?t:rt[$].fontName,e=void 0!==e?e:rt[$].fontStyle,r=t.toLowerCase(),void 0!==z[r]&&void 0!==z[r][e]?n=z[r][e]:void 0!==z[t]&&void 0!==z[t][e]?n=z[t][e]:!1===A.disableWarning&&console.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),n||A.noFallback||null==(n=z.times[e])&&(n=z.times.normal),n},Ft=a.__private__.putInfo=function(){for(var t in G(),tt("<<"),tt("/Producer (jsPDF "+oe.version+")"),K)K.hasOwnProperty(t)&&K[t]&&tt("/"+t.substr(0,1).toUpperCase()+t.substr(1)+" ("+wt(K[t])+")");tt("/CreationDate ("+f+")"),tt(">>"),tt("endobj")},bt=a.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||st;switch(G(),tt("<<"),tt("/Type /Catalog"),tt("/Pages "+e+" 0 R"),Q||(Q="fullwidth"),Q){case"fullwidth":tt("/OpenAction [3 0 R /FitH null]");break;case"fullheight":tt("/OpenAction [3 0 R /FitV null]");break;case"fullpage":tt("/OpenAction [3 0 R /Fit]");break;case"original":tt("/OpenAction [3 0 R /XYZ null null 1]");break;default:var A=""+Q;"%"===A.substr(A.length-1)&&(Q=parseInt(Q)/100),"number"==typeof Q&&tt("/OpenAction [3 0 R /XYZ null null "+Y(Q)+"]")}switch(b||(b="continuous"),b){case"continuous":tt("/PageLayout /OneColumn");break;case"single":tt("/PageLayout /SinglePage");break;case"two":case"twoleft":tt("/PageLayout /TwoColumnLeft");break;case"tworight":tt("/PageLayout /TwoColumnRight")}F&&tt("/PageMode /"+F),nt.publish("putCatalog"),tt(">>"),tt("endobj")},Ut=a.__private__.putTrailer=function(){tt("trailer"),tt("<<"),tt("/Size "+(D+1)),tt("/Root "+D+" 0 R"),tt("/Info "+(D-1)+" 0 R"),tt("/ID [ <"+d+"> <"+d+"> ]"),tt(">>")},Et=a.__private__.putHeader=function(){tt("%PDF-"+c),tt("%\xba\xdf\xac\xe0")},Nt=a.__private__.putXRef=function(){var t=1,e="0000000000";for(tt("xref"),tt("0 "+(D+1)),tt("0000000000 65535 f "),t=1;t<=D;t++){"function"==typeof k[t]?tt((e+k[t]()).slice(-10)+" 00000 n "):void 0!==k[t]?tt((e+k[t]).slice(-10)+" 00000 n "):tt("0000000000 00000 n ")}},Lt=a.__private__.buildDocument=function(){L=!1,x=D=0,S=[],k=[],V=[],st=J(),at=J(),nt.publish("buildDocument"),Et(),dt(),function(){nt.publish("putAdditionalObjects");for(var t=0;t<V.length;t++){var e=V[t];it(e.objId,!0),tt(e.content),tt("endobj")}nt.publish("postPutAdditionalObjects")}(),pt(),Ft(),bt();var t=x;return Nt(),Ut(),tt("startxref"),tt(""+t),tt("%%EOF"),L=!0,S.join("\n")},Ht=a.__private__.getBlob=function(t){return new Blob([I(t)],{type:"application/pdf"})},St=a.output=a.__private__.output=((E=function(t,e){e=e||{};var A=Lt();switch("string"==typeof e?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return A;case"save":a.save(e.filename);break;case"arraybuffer":return I(A);case"blob":return Ht(A);case"bloburi":case"bloburl":if(void 0!==ne.URL&&"function"==typeof ne.URL.createObjectURL)return ne.URL&&ne.URL.createObjectURL(Ht(A))||void 0;console.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":return"data:application/pdf;filename="+e.filename+";base64,"+btoa(A);case"dataurlnewwindow":var r='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring")+'"></iframe></body></html>',n=ne.open();if(null!==n&&n.document.write(r),n||"undefined"==typeof safari)return n;case"datauri":case"dataurl":return ne.document.location.href="data:application/pdf;filename="+e.filename+";base64,"+btoa(A);default:return null}}).foo=function(){try{return E.apply(this,arguments)}catch(t){var e=t.stack||"";~e.indexOf(" at ")&&(e=e.split(" at ")[1]);var A="Error in function "+e.split("\n")[0].split("<")[0]+": "+t.message;if(!ne.console)throw new Error(A);ne.console.error(A,t),ne.alert&&alert(A)}},(E.foo.bar=E).foo),xt=function(t){return!0===Array.isArray(X)&&-1<X.indexOf(t)};switch(e){case"pt":U=1;break;case"mm":U=72/25.4;break;case"cm":U=72/2.54;break;case"in":U=72;break;case"px":U=1==xt("px_scaling")?.75:96/72;break;case"pc":case"em":U=12;break;case"ex":U=6;break;default:throw new Error("Invalid unit: "+e)}m(),g();var _t=a.__private__.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:q[t].objId,pageNumber:t,pageContext:q[t]}},It=a.__private__.getPageInfoByObjId=function(t){for(var e in q)if(q[e].objId===t)break;if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");return _t(e)},Tt=a.__private__.getCurrentPageInfo=function(){return{objId:q[y].objId,pageNumber:y,pageContext:q[y]}};a.addPage=function(){return vt.apply(this,arguments),this},a.setPage=function(){return yt.apply(this,arguments),this},a.insertPage=function(t){return this.addPage(),this.movePage(y,t),this},a.movePage=function(t,e){if(e<t){for(var A=H[t],r=q[t],n=t;e<n;n--)H[n]=H[n-1],q[n]=q[n-1];H[e]=A,q[e]=r,this.setPage(e)}else if(t<e){for(A=H[t],r=q[t],n=t;n<e;n++)H[n]=H[n+1],q[n]=q[n+1];H[e]=A,q[e]=r,this.setPage(e)}return this},a.deletePage=function(){return function(t){0<t&&t<=j&&(H.splice(t,1),--j<y&&(y=j),this.setPage(y))}.apply(this,arguments),this};a.__private__.text=a.text=function(t,e,A,n){var r;"number"!=typeof t||"number"!=typeof e||"string"!=typeof A&&!Array.isArray(A)||(r=A,A=e,e=t,t=r);var i=arguments[3],o=arguments[4],s=arguments[5];if("object"===se(i)&&null!==i||("string"==typeof o&&(s=o,o=null),"string"==typeof i&&(s=i,i=null),"number"==typeof i&&(o=i,i=null),n={flags:i,angle:o,align:s}),(i=i||{}).noBOM=i.noBOM||!0,i.autoencode=i.autoencode||!0,isNaN(e)||isNaN(A)||null==t)throw new Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return l;var a,c="",u="number"==typeof n.lineHeightFactor?n.lineHeightFactor:Pt,l=n.scope||this;function h(t){for(var e,A=t.concat(),r=[],n=A.length;n--;)"string"==typeof(e=A.shift())?r.push(e):Array.isArray(t)&&1===e.length?r.push(e[0]):r.push([e[0],e[1],e[2]]);return r}function f(t,e){var A;if("string"==typeof t)A=e(t)[0];else if(Array.isArray(t)){for(var r,n,i=t.concat(),o=[],s=i.length;s--;)"string"==typeof(r=i.shift())?o.push(e(r)[0]):Array.isArray(r)&&"string"===r[0]&&(n=e(r[0],r[1],r[2]),o.push([n[0],n[1],n[2]]));A=o}return A}var d=!1,p=!0;if("string"==typeof t)d=!0;else if(Array.isArray(t)){for(var g,B=t.concat(),w=[],m=B.length;m--;)("string"!=typeof(g=B.shift())||Array.isArray(g)&&"string"!=typeof g[0])&&(p=!1);d=p}if(!1===d)throw new Error('Type of text must be string or Array. "'+t+'" is not recognized.');var v=rt[$].encoding;"WinAnsiEncoding"!==v&&"StandardEncoding"!==v||(t=f(t,function(t,e,A){return[(r=t,r=r.split("\t").join(Array(n.TabLen||9).join(" ")),wt(r,i)),e,A];var r})),"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var y=et/l.internal.scaleFactor,C=y*(Pt-1);switch(n.baseline){case"bottom":A-=C;break;case"top":A+=y-C;break;case"hanging":A+=y-2*C;break;case"middle":A+=y/2-C}0<(O=n.maxWidth||0)&&("string"==typeof t?t=l.splitTextToSize(t,O):"[object Array]"===Object.prototype.toString.call(t)&&(t=l.splitTextToSize(t.join(" "),O)));var Q={text:t,x:e,y:A,options:n,mutex:{pdfEscape:wt,activeFontKey:$,fonts:rt,activeFontSize:et}};nt.publish("preProcessText",Q),t=Q.text;o=(n=Q.options).angle;var F=l.internal.scaleFactor,b=[];if(o){o*=Math.PI/180;var U=Math.cos(o),E=Math.sin(o);b=[Y(U),Y(E),Y(-1*E),Y(U)]}void 0!==(T=n.charSpace)&&(c+=Z(T*F)+" Tc\n");n.lang;var N=-1,L=void 0!==n.renderingMode?n.renderingMode:n.stroke,H=l.internal.getCurrentPageInfo().pageContext;switch(L){case 0:case!1:case"fill":N=0;break;case 1:case!0:case"stroke":N=1;break;case 2:case"fillThenStroke":N=2;break;case 3:case"invisible":N=3;break;case 4:case"fillAndAddForClipping":N=4;break;case 5:case"strokeAndAddPathForClipping":N=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":N=6;break;case 7:case"addToPathForClipping":N=7}var S=void 0!==H.usedRenderingMode?H.usedRenderingMode:-1;-1!==N?c+=N+" Tr\n":-1!==S&&(c+="0 Tr\n"),-1!==N&&(H.usedRenderingMode=N);s=n.align||"left";var x=et*u,_=l.internal.pageSize.getWidth(),I=(F=l.internal.scaleFactor,rt[$]),T=n.charSpace||Zt,O=n.maxWidth||0,R=(i={},[]);if("[object Array]"===Object.prototype.toString.call(t)){var P,M;w=h(t);"left"!==s&&(M=w.map(function(t){return l.getStringUnitWidth(t,{font:I,charSpace:T,fontSize:et})*et/F}));var K,D=Math.max.apply(Math,M),k=0;if("right"===s){e-=M[0],t=[];var z=0;for(m=w.length;z<m;z++)D-M[z],P=0===z?(K=jt(e),qt(A)):(K=(k-M[z])*F,-x),t.push([w[z],K,P]),k=M[z]}else if("center"===s){e-=M[0]/2,t=[];for(z=0,m=w.length;z<m;z++)(D-M[z])/2,P=0===z?(K=jt(e),qt(A)):(K=(k-M[z])/2*F,-x),t.push([w[z],K,P]),k=M[z]}else if("left"===s){t=[];for(z=0,m=w.length;z<m;z++)P=0===z?qt(A):-x,K=0===z?jt(e):0,t.push(w[z])}else{if("justify"!==s)throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');t=[];for(O=0!==O?O:_,z=0,m=w.length;z<m;z++)P=0===z?qt(A):-x,K=0===z?jt(e):0,z<m-1&&R.push(((O-M[z])/(w[z].split(" ").length-1)*F).toFixed(2)),t.push([w[z],K,P])}}!0===("boolean"==typeof n.R2L?n.R2L:At)&&(t=f(t,function(t,e,A){return[t.split("").reverse().join(""),e,A]}));Q={text:t,x:e,y:A,options:n,mutex:{pdfEscape:wt,activeFontKey:$,fonts:rt,activeFontSize:et}};nt.publish("postProcessText",Q),t=Q.text,a=Q.mutex.isHex;w=h(t);t=[];var j,q,V,X=0,G=(m=w.length,"");for(z=0;z<m;z++)G="",Array.isArray(w[z])?(j=parseFloat(w[z][1]),q=parseFloat(w[z][2]),V=(a?"<":"(")+w[z][0]+(a?">":")"),X=1):(j=jt(e),q=qt(A),V=(a?"<":"(")+w[z]+(a?">":")")),void 0!==R&&void 0!==R[z]&&(G=R[z]+" Tw\n"),0!==b.length&&0===z?t.push(G+b.join(" ")+" "+j.toFixed(2)+" "+q.toFixed(2)+" Tm\n"+V):1===X||0===X&&0===z?t.push(G+j.toFixed(2)+" "+q.toFixed(2)+" Td\n"+V):t.push(G+V);t=0===X?t.join(" Tj\nT* "):t.join(" Tj\n"),t+=" Tj\n";var J="BT\n/"+$+" "+et+" Tf\n"+(et*u).toFixed(2)+" TL\n"+Wt+"\n";return J+=c,J+=t,tt(J+="ET"),W[$]=!0,l},a.__private__.lstext=a.lstext=function(t,e,A,r){return console.warn("jsPDF.lstext is deprecated"),this.text(t,e,A,{charSpace:r})},a.__private__.clip=a.clip=function(t){tt("evenodd"===t?"W*":"W"),tt("n")},a.__private__.clip_fixed=a.clip_fixed=function(t){console.log("clip_fixed is deprecated"),a.clip(t)};var Ot=a.__private__.isValidStyle=function(t){var e=!1;return-1!==[void 0,null,"S","F","DF","FD","f","f*","B","B*"].indexOf(t)&&(e=!0),e},Rt=a.__private__.getStyle=function(t){var e="S";return"F"===t?e="f":"FD"===t||"DF"===t?e="B":"f"!==t&&"f*"!==t&&"B"!==t&&"B*"!==t||(e=t),e};a.__private__.line=a.line=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw new Error("Invalid arguments passed to jsPDF.line");return this.lines([[A-t,r-e]],t,e)},a.__private__.lines=a.lines=function(t,e,A,r,n,i){var o,s,a,c,u,l,h,f,d,p,g,B;if("number"==typeof t&&(B=A,A=e,e=t,t=B),r=r||[1,1],i=i||!1,isNaN(e)||isNaN(A)||!Array.isArray(t)||!Array.isArray(r)||!Ot(n)||"boolean"!=typeof i)throw new Error("Invalid arguments passed to jsPDF.lines");for(tt(Z(jt(e))+" "+Z(qt(A))+" m "),o=r[0],s=r[1],c=t.length,p=e,g=A,a=0;a<c;a++)2===(u=t[a]).length?(p=u[0]*o+p,g=u[1]*s+g,tt(Z(jt(p))+" "+Z(qt(g))+" l")):(l=u[0]*o+p,h=u[1]*s+g,f=u[2]*o+p,d=u[3]*s+g,p=u[4]*o+p,g=u[5]*s+g,tt(Z(jt(l))+" "+Z(qt(h))+" "+Z(jt(f))+" "+Z(qt(d))+" "+Z(jt(p))+" "+Z(qt(g))+" c"));return i&&tt(" h"),null!==n&&tt(Rt(n)),this},a.__private__.rect=a.rect=function(t,e,A,r,n){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||!Ot(n))throw new Error("Invalid arguments passed to jsPDF.rect");return tt([Y(jt(t)),Y(qt(e)),Y(A*U),Y(-r*U),"re"].join(" ")),null!==n&&tt(Rt(n)),this},a.__private__.triangle=a.triangle=function(t,e,A,r,n,i,o){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||isNaN(n)||isNaN(i)||!Ot(o))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[A-t,r-e],[n-A,i-r],[t-n,e-i]],t,e,[1,1],o,!0),this},a.__private__.roundedRect=a.roundedRect=function(t,e,A,r,n,i,o){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||isNaN(n)||isNaN(i)||!Ot(o))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var s=4/3*(Math.SQRT2-1);return this.lines([[A-2*n,0],[n*s,0,n,i-i*s,n,i],[0,r-2*i],[0,i*s,-n*s,i,-n,i],[2*n-A,0],[-n*s,0,-n,-i*s,-n,-i],[0,2*i-r],[0,-i*s,n*s,-i,n,-i]],t+n,e,[1,1],o),this},a.__private__.ellipse=a.ellipse=function(t,e,A,r,n){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||!Ot(n))throw new Error("Invalid arguments passed to jsPDF.ellipse");var i=4/3*(Math.SQRT2-1)*A,o=4/3*(Math.SQRT2-1)*r;return tt([Y(jt(t+A)),Y(qt(e)),"m",Y(jt(t+A)),Y(qt(e-o)),Y(jt(t+i)),Y(qt(e-r)),Y(jt(t)),Y(qt(e-r)),"c"].join(" ")),tt([Y(jt(t-i)),Y(qt(e-r)),Y(jt(t-A)),Y(qt(e-o)),Y(jt(t-A)),Y(qt(e)),"c"].join(" ")),tt([Y(jt(t-A)),Y(qt(e+o)),Y(jt(t-i)),Y(qt(e+r)),Y(jt(t)),Y(qt(e+r)),"c"].join(" ")),tt([Y(jt(t+i)),Y(qt(e+r)),Y(jt(t+A)),Y(qt(e+o)),Y(jt(t+A)),Y(qt(e)),"c"].join(" ")),null!==n&&tt(Rt(n)),this},a.__private__.circle=a.circle=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||!Ot(r))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,A,A,r)};a.setFont=function(t,e){return $=Qt(t,e,{disableWarning:!1}),this},a.setFontStyle=a.setFontType=function(t){return $=Qt(void 0,t),this};a.__private__.getFontList=a.getFontList=function(){var t,e,A,r={};for(t in z)if(z.hasOwnProperty(t))for(e in r[t]=A=[],z[t])z[t].hasOwnProperty(e)&&A.push(e);return r};a.addFont=function(t,e,A,r){Bt.call(this,t,e,A,r=r||"Identity-H")};var Pt,Mt=r.lineWidth||.200025,Kt=a.__private__.setLineWidth=a.setLineWidth=function(t){return tt((t*U).toFixed(2)+" w"),this},Dt=(a.__private__.setLineDash=oe.API.setLineDash=function(t,e){if(t=t||[],e=e||0,isNaN(e)||!Array.isArray(t))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return t=t.map(function(t){return(t*U).toFixed(3)}).join(" "),e=parseFloat((e*U).toFixed(3)),tt("["+t+"] "+e+" d"),this},a.__private__.getLineHeight=a.getLineHeight=function(){return et*Pt}),kt=(Dt=a.__private__.getLineHeight=a.getLineHeight=function(){return et*Pt},a.__private__.setLineHeightFactor=a.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(Pt=t),this}),zt=a.__private__.getLineHeightFactor=a.getLineHeightFactor=function(){return Pt};kt(r.lineHeight);var jt=a.__private__.getHorizontalCoordinate=function(t){return t*U},qt=a.__private__.getVerticalCoordinate=function(t){return q[y].mediaBox.topRightY-q[y].mediaBox.bottomLeftY-t*U},Vt=a.__private__.getHorizontalCoordinateString=function(t){return Y(t*U)},Xt=a.__private__.getVerticalCoordinateString=function(t){return Y(q[y].mediaBox.topRightY-q[y].mediaBox.bottomLeftY-t*U)},Gt=r.strokeColor||"0 G",Jt=(a.__private__.getStrokeColor=a.getDrawColor=function(){return ct(Gt)},a.__private__.setStrokeColor=a.setDrawColor=function(t,e,A,r){return Gt=ut({ch1:t,ch2:e,ch3:A,ch4:r,pdfColorType:"draw",precision:2}),tt(Gt),this},r.fillColor||"0 g"),Wt=(a.__private__.getFillColor=a.getFillColor=function(){return ct(Jt)},a.__private__.setFillColor=a.setFillColor=function(t,e,A,r){return Jt=ut({ch1:t,ch2:e,ch3:A,ch4:r,pdfColorType:"fill",precision:2}),tt(Jt),this},r.textColor||"0 g"),Yt=a.__private__.getTextColor=a.getTextColor=function(){return ct(Wt)},Zt=(a.__private__.setTextColor=a.setTextColor=function(t,e,A,r){return Wt=ut({ch1:t,ch2:e,ch3:A,ch4:r,pdfColorType:"text",precision:3}),this},r.charSpace||0),$t=a.__private__.getCharSpace=a.getCharSpace=function(){return Zt},te=(a.__private__.setCharSpace=a.setCharSpace=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Zt=t,this},0);a.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2};a.__private__.setLineCap=a.setLineCap=function(t){var e=a.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return tt((te=e)+" J"),this};var ee,Ae=0;a.__private__.setLineJoin=a.setLineJoin=function(t){var e=a.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return tt((Ae=e)+" j"),this},a.__private__.setMiterLimit=a.setMiterLimit=function(t){if(t=t||0,isNaN(t))throw new Error("Invalid argument passed to jsPDF.setMiterLimit");return ee=parseFloat(Y(t*U)),tt(ee+" M"),this};for(var re in a.save=function(r,t){if(r=r||"generated.pdf",(t=t||{}).returnPromise=t.returnPromise||!1,!1!==t.returnPromise)return new Promise(function(t,e){try{var A=ae(Ht(Lt()),r);"function"==typeof ae.unload&&ne.setTimeout&&setTimeout(ae.unload,911),t(A)}catch(t){e(t.message)}});ae(Ht(Lt()),r),"function"==typeof ae.unload&&ne.setTimeout&&setTimeout(ae.unload,911)},oe.API)oe.API.hasOwnProperty(re)&&("events"===re&&oe.API.events.length?function(t,e){var A,r,n;for(n=e.length-1;-1!==n;n--)A=e[n][0],r=e[n][1],t.subscribe.apply(t,[A].concat("function"==typeof r?[r]:r))}(nt,oe.API.events):a[re]=oe.API[re]);return a.internal={pdfEscape:wt,getStyle:Rt,getFont:function(){return rt[Qt.apply(a,arguments)]},getFontSize:O,getCharSpace:$t,getTextColor:Yt,getLineHeight:Dt,getLineHeightFactor:zt,write:_,getHorizontalCoordinate:jt,getVerticalCoordinate:qt,getCoordinateString:Vt,getVerticalCoordinateString:Xt,collections:{},newObject:G,newAdditionalObject:ot,newObjectDeferred:J,newObjectDeferredBegin:it,getFilters:lt,putStream:ht,events:nt,scaleFactor:U,pageSize:{getWidth:function(){return(q[y].mediaBox.topRightX-q[y].mediaBox.bottomLeftX)/U},setWidth:function(t){q[y].mediaBox.topRightX=t*U+q[y].mediaBox.bottomLeftX},getHeight:function(){return(q[y].mediaBox.topRightY-q[y].mediaBox.bottomLeftY)/U},setHeight:function(t){q[y].mediaBox.topRightY=t*U+q[y].mediaBox.bottomLeftY}},output:St,getNumberOfPages:Ct,pages:H,out:tt,f2:Y,f3:Z,getPageInfo:_t,getPageInfoByObjId:It,getCurrentPageInfo:Tt,getPDFVersion:u,hasHotfix:xt},Object.defineProperty(a.internal.pageSize,"width",{get:function(){return(q[y].mediaBox.topRightX-q[y].mediaBox.bottomLeftX)/U},set:function(t){q[y].mediaBox.topRightX=t*U+q[y].mediaBox.bottomLeftX},enumerable:!0,configurable:!0}),Object.defineProperty(a.internal.pageSize,"height",{get:function(){return(q[y].mediaBox.topRightY-q[y].mediaBox.bottomLeftY)/U},set:function(t){q[y].mediaBox.topRightY=t*U+q[y].mediaBox.bottomLeftY},enumerable:!0,configurable:!0}),function(t){for(var e=0,A=T.length;e<A;e++){var r=Bt(t[e][0],t[e][1],t[e][2],T[e][3],!0);W[r]=!0;var n=t[e][0].split("-");gt(r,n[0],n[1]||"")}nt.publish("addFonts",{fonts:rt,dictionary:z})}(T),$="F1",vt(n,t),nt.publish("initialized"),a}return oe.API={events:[]},oe.version="1.5.3",Ht.exports?(Ht.exports=oe,Ht.exports.jsPDF=oe):ne.jsPDF=oe,oe}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")());(function(t,e){var F,A=1,b=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},B=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},U=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return t.toFixed(2)},s=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return t.toFixed(5)};t.__acroform__={};var r=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},w=function(t){return t*A},m=function(t){return t/A},a=function(t){var e=new _,A=X.internal.getHeight(t)||0,r=X.internal.getWidth(t)||0;return e.BBox=[0,0,Number(U(r)),Number(U(A))],e},n=t.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|=1<<e},i=t.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&=~(1<<e)},o=t.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return 0==(t&1<<e)?0:1},v=t.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return o(t,e-1)},y=t.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return n(t,e-1)},C=t.__acroform__.clearBitForPdf=function(t,e,A){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return i(t,e-1)},l=t.__acroform__.calculateCoordinates=function(t){var e=this.internal.getHorizontalCoordinate,A=this.internal.getVerticalCoordinate,r=t[0],n=t[1],i=t[2],o=t[3],s={};return s.lowerLeft_X=e(r)||0,s.lowerLeft_Y=A(n+o)||0,s.upperRight_X=e(r+i)||0,s.upperRight_Y=A(n)||0,[Number(U(s.lowerLeft_X)),Number(U(s.lowerLeft_Y)),Number(U(s.upperRight_X)),Number(U(s.upperRight_Y))]},h=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],A=t.V||t.DV,r=c(t,A),n=F.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(F.__private__.encodeColorString(t.color)),e.push("/"+n+" "+U(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var i=new a(t);return i.stream=e.join("\n"),i}},c=function(n,t){var e=n.maxFontSize||12,A=(n.fontName,{text:"",fontSize:""}),i=(t=")"==(t="("==t.substr(0,1)?t.substr(1):t).substr(t.length-1)?t.substr(0,t.length-1):t).split(" "),r=(F.__private__.encodeColorString(n.color),e),o=X.internal.getHeight(n)||0;o=o<0?-o:o;var s=X.internal.getWidth(n)||0;s=s<0?-s:s;var a=function(t,e,A){if(t+1<i.length){var r=e+" "+i[t+1];return E(r,n,A).width<=s-4}return!1};r++;t:for(;;){t="";var c=E("3",n,--r).height,u=n.multiline?o-r:(o-c)/2,l=-2,h=u+=2,f=0,d=0,p=0;if(r<=0){t="(...) Tj\n",t+="% Width of Text: "+E(t,n,r=12).width+", FieldWidth:"+s+"\n";break}p=E(i[0]+" ",n,r).width;var g="",B=0;for(var w in i)if(i.hasOwnProperty(w)){g=" "==(g+=i[w]+" ").substr(g.length-1)?g.substr(0,g.length-1):g;var m=parseInt(w);p=E(g+" ",n,r).width;var v=a(m,g,r),y=w>=i.length-1;if(v&&!y){g+=" ";continue}if(v||y){if(y)d=m;else if(n.multiline&&o<(c+2)*(B+2)+2)continue t}else{if(!n.multiline)continue t;if(o<(c+2)*(B+2)+2)continue t;d=m}for(var C="",Q=f;Q<=d;Q++)C+=i[Q]+" ";switch(C=" "==C.substr(C.length-1)?C.substr(0,C.length-1):C,p=E(C,n,r).width,n.textAlign){case"right":l=s-p-2;break;case"center":l=(s-p)/2;break;case"left":default:l=2}t+=U(l)+" "+U(h)+" Td\n",t+="("+b(C)+") Tj\n",t+=-U(l)+" 0 Td\n",h=-(r+2),p=0,f=d+1,B++,g=""}break}return A.text=t,A.fontSize=r,A},E=function(t,e,A){var r=F.internal.getFont(e.fontName,e.fontStyle),n=F.getStringUnitWidth(t,{font:r,fontSize:parseFloat(A),charSpace:0})*parseFloat(A);return{height:F.getStringUnitWidth("3",{font:r,fontSize:parseFloat(A),charSpace:0})*parseFloat(A)*1.5,width:n}},u={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},f=function(){F.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var t=F.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var e in t)if(t.hasOwnProperty(e)){var A=t[e];A.objId=void 0,A.hasAnnotation&&d.call(F,A)}},d=function(t){var e={type:"reference",object:t};void 0===F.internal.getPageInfo(t.page).pageContext.annotations.find(function(t){return t.type===e.type&&t.object===e.object})&&F.internal.getPageInfo(t.page).pageContext.annotations.push(e)},p=function(){if(void 0===F.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("putCatalogCallback: Root missing.");F.internal.write("/AcroForm "+F.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")},g=function(){F.internal.events.unsubscribe(F.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete F.internal.acroformPlugin.acroFormDictionaryRoot._eventID,F.internal.acroformPlugin.printedOut=!0},Q=function(t){var e=!t;t||(F.internal.newObjectDeferredBegin(F.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),F.internal.acroformPlugin.acroFormDictionaryRoot.putStream());t=t||F.internal.acroformPlugin.acroFormDictionaryRoot.Kids;for(var A in t)if(t.hasOwnProperty(A)){var r=t[A],n=[],i=r.Rect;if(r.Rect&&(r.Rect=l.call(this,r.Rect)),F.internal.newObjectDeferredBegin(r.objId,!0),r.DA=X.createDefaultAppearanceStream(r),"object"===se(r)&&"function"==typeof r.getKeyValueListForStream&&(n=r.getKeyValueListForStream()),r.Rect=i,r.hasAppearanceStream&&!r.appearanceStreamContent){var o=h.call(this,r);n.push({key:"AP",value:"<</N "+o+">>"}),F.internal.acroformPlugin.xForms.push(o)}if(r.appearanceStreamContent){var s="";for(var a in r.appearanceStreamContent)if(r.appearanceStreamContent.hasOwnProperty(a)){var c=r.appearanceStreamContent[a];if(s+="/"+a+" ",s+="<<",1<=Object.keys(c).length||Array.isArray(c))for(var A in c){var u;if(c.hasOwnProperty(A))"function"==typeof(u=c[A])&&(u=u.call(this,r)),s+="/"+A+" "+u+" ",0<=F.internal.acroformPlugin.xForms.indexOf(u)||F.internal.acroformPlugin.xForms.push(u)}else"function"==typeof(u=c)&&(u=u.call(this,r)),s+="/"+A+" "+u,0<=F.internal.acroformPlugin.xForms.indexOf(u)||F.internal.acroformPlugin.xForms.push(u);s+=">>"}n.push({key:"AP",value:"<<\n"+s+">>"})}F.internal.putStream({additionalKeyValues:n}),F.internal.out("endobj")}e&&N.call(this,F.internal.acroformPlugin.xForms)},N=function(t){for(var e in t)if(t.hasOwnProperty(e)){var A=e,r=t[e];F.internal.newObjectDeferredBegin(r&&r.objId,!0),"object"===se(r)&&"function"==typeof r.putStream&&r.putStream(),delete t[A]}},L=function(){if(void 0!==this.internal&&(void 0===this.internal.acroformPlugin||!1===this.internal.acroformPlugin.isInitialized)){if(F=this,T.FieldNum=0,this.internal.acroformPlugin=JSON.parse(JSON.stringify(u)),this.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");A=F.internal.scaleFactor,F.internal.acroformPlugin.acroFormDictionaryRoot=new I,F.internal.acroformPlugin.acroFormDictionaryRoot._eventID=F.internal.events.subscribe("postPutResources",g),F.internal.events.subscribe("buildDocument",f),F.internal.events.subscribe("putCatalog",p),F.internal.events.subscribe("postPutPages",Q),F.internal.acroformPlugin.isInitialized=!0}},H=t.__acroform__.arrayToPdfArray=function(t){if(Array.isArray(t)){for(var e="[",A=0;A<t.length;A++)switch(0!==A&&(e+=" "),se(t[A])){case"boolean":case"number":case"object":e+=t[A].toString();break;case"string":"/"!==t[A].substr(0,1)?e+="("+b(t[A].toString())+")":e+=t[A].toString()}return e+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")};var S=function(t){return(t=t||"").toString(),t="("+b(t)+")"},x=function(){var e;Object.defineProperty(this,"objId",{configurable:!0,get:function(){if(e||(e=F.internal.newObjectDeferred()),!e)throw new Error("AcroFormPDFObject: Couldn't create Object ID");return e},set:function(t){e=t}})};x.prototype.toString=function(){return this.objId+" 0 R"},x.prototype.putStream=function(){var t=this.getKeyValueListForStream();F.internal.putStream({data:this.stream,additionalKeyValues:t}),F.internal.out("endobj")},x.prototype.getKeyValueListForStream=function(){return function(t){var e=[],A=Object.getOwnPropertyNames(t).filter(function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"_"!=t.substring(0,1)});for(var r in A)if(!1===Object.getOwnPropertyDescriptor(t,A[r]).configurable){var n=A[r],i=t[n];i&&(Array.isArray(i)?e.push({key:n,value:H(i)}):i instanceof x?e.push({key:n,value:i.objId+" 0 R"}):"function"!=typeof i&&e.push({key:n,value:i}))}return e}(this)};var _=function(){x.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writeable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writeable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writeable:!0});var e,A=[];Object.defineProperty(this,"BBox",{configurable:!1,writeable:!0,get:function(){return A},set:function(t){A=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writeable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(t){e=t.trim()},get:function(){return e||null}})};r(_,x);var I=function(){x.call(this);var e,t=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return 0<t.length?t:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return t}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(e)return"("+e+")"},set:function(t){e=t}})};r(I,x);var T=function t(){x.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(e,3))},set:function(t){!0===Boolean(t)?this.F=y(e,3):this.F=C(e,3)}});var A=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return A},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute Ff supplied.');A=t}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==r.length)return r},set:function(t){r=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:m(r[0])},set:function(t){r[0]=w(t)}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:m(r[1])},set:function(t){r[1]=w(t)}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:m(r[2])},set:function(t){r[2]=w(t)}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:m(r[3])},set:function(t){r[3]=w(t)}});var n="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return n},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":n=t;break;default:throw new Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var i=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!i||i.length<1){if(this instanceof z)return;i="FieldObject"+t.FieldNum++}return"("+b(i)+")"},set:function(t){i=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return i},set:function(t){i=t}});var o="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return o},set:function(t){o=t}});var s="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var a=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return m(a)},set:function(t){a=w(t)}});var c=50;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return m(c)},set:function(t){c=w(t)}});var u="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return u},set:function(t){u=t}});var l="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!l||this instanceof z||this instanceof q))return S(l)},set:function(t){t=t.toString(),l=t}});var h=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(h)return this instanceof K==!1?S(h):h},set:function(t){t=t.toString(),h=this instanceof K==!1?"("===t.substr(0,1)?B(t.substr(1,t.length-2)):B(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof K==!0?B(h.substr(1,h.length-1)):h},set:function(t){t=t.toString(),h=this instanceof K==!0?"/"+t:t}});var f=null;Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof K==!1?S(f):f},set:function(t){t=t.toString(),f=this instanceof K==!1?"("===t.substr(0,1)?B(t.substr(1,t.length-2)):B(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof K==!0?B(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof K==!0?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var d,p=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,writeable:!0,get:function(){return p},set:function(t){t=Boolean(t),p=t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,writeable:!0,get:function(){if(d)return d},set:function(t){d=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,1))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,1):this.Ff=C(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,2))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,2):this.Ff=C(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,3))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,3):this.Ff=C(this.Ff,3)}});var g=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==g)return g},set:function(t){if(-1===[0,1,2].indexOf(t))throw new Error('Invalid value "'+t+'" for attribute Q supplied.');g=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t="left";switch(g){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:g=2;break;case"center":case 1:g=1;break;case"left":case 0:default:g=0}}})};r(T,x);var O=function(){T.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var e=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return e},set:function(t){e=t}});var r=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return H(r)},set:function(t){var e,A;A=[],"string"==typeof(e=t)&&(A=function(t,e,A){A||(A=1);for(var r,n=[];r=e.exec(t);)n.push(r[A]);return n}(e,/\((.*?)\)/g)),r=A}}),this.getOptions=function(){return r},this.setOptions=function(t){r=t,this.sort&&r.sort()},this.addOption=function(t){t=(t=t||"").toString(),r.push(t),this.sort&&r.sort()},this.removeOption=function(t,e){for(e=e||!1,t=(t=t||"").toString();-1!==r.indexOf(t)&&(r.splice(r.indexOf(t),1),!1!==e););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,18))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,18):this.Ff=C(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,19))},set:function(t){!0===this.combo&&(!0===Boolean(t)?this.Ff=y(this.Ff,19):this.Ff=C(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,20))},set:function(t){!0===Boolean(t)?(this.Ff=y(this.Ff,20),r.sort()):this.Ff=C(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,22))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,22):this.Ff=C(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,23):this.Ff=C(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,27))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,27):this.Ff=C(this.Ff,27)}}),this.hasAppearanceStream=!1};r(O,T);var R=function(){O.call(this),this.fontName="helvetica",this.combo=!1};r(R,O);var P=function(){R.call(this),this.combo=!0};r(P,R);var M=function(){P.call(this),this.edit=!0};r(M,P);var K=function(){T.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,15))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,15):this.Ff=C(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,16))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,16):this.Ff=C(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,17))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,17):this.Ff=C(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,26):this.Ff=C(this.Ff,26)}});var e,A={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){if(0!==Object.keys(A).length){var t,e=[];for(t in e.push("<<"),A)e.push("/"+t+" ("+A[t]+")");return e.push(">>"),e.join("\n")}},set:function(t){"object"===se(t)&&(A=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return A.CA||""},set:function(t){"string"==typeof t&&(A.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return e.substr(1,e.length-1)},set:function(t){e="/"+t}})};r(K,T);var D=function(){K.call(this),this.pushButton=!0};r(D,K);var k=function(){K.call(this),this.radio=!0,this.pushButton=!1;var e=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=void 0!==t?t:[]}})};r(k,K);var z=function(){var e,A;T.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return A},set:function(t){A=t}});var r,n={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t,e=[];for(t in e.push("<<"),n)e.push("/"+t+" ("+n[t]+")");return e.push(">>"),e.join("\n")},set:function(t){"object"===se(t)&&(n=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return n.CA||""},set:function(t){"string"==typeof t&&(n.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(t){r=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(t){r="/"+t}}),this.optionName=name,this.caption="l",this.appearanceState="Off",this._AppearanceType=X.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(name)};r(z,T),k.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t&&"getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var A=this.Kids[e];A.appearanceStreamContent=t.createAppearanceStream(A.optionName),A.caption=t.getCA()}},k.prototype.createOption=function(t){this.Kids.length;var e=new z;return e.Parent=this,e.optionName=t,this.Kids.push(e),G.call(this,e),e};var j=function(){K.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=X.CheckBox.createAppearanceStream()};r(j,K);var q=function(){T.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,13))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,13):this.Ff=C(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,21))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,21):this.Ff=C(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,23):this.Ff=C(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,24))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,24):this.Ff=C(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,25))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,25):this.Ff=C(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,26):this.Ff=C(this.Ff,26)}});var e=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return e},set:function(t){Number.isInteger(t)&&(e=t)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};r(q,T);var V=function(){q.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return Boolean(v(this.Ff,14))},set:function(t){!0===Boolean(t)?this.Ff=y(this.Ff,14):this.Ff=C(this.Ff,14)}}),this.password=!0};r(V,q);var X={CheckBox:{createAppearanceStream:function(){return{N:{On:X.CheckBox.YesNormal},D:{On:X.CheckBox.YesPushDown,Off:X.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=a(t),A=[],r=F.internal.getFont(t.fontName,t.fontStyle).id,n=F.__private__.encodeColorString(t.color),i=c(t,t.caption);return A.push("0.749023 g"),A.push("0 0 "+U(X.internal.getWidth(t))+" "+U(X.internal.getHeight(t))+" re"),A.push("f"),A.push("BMC"),A.push("q"),A.push("0 0 1 rg"),A.push("/"+r+" "+U(i.fontSize)+" Tf "+n),A.push("BT"),A.push(i.text),A.push("ET"),A.push("Q"),A.push("EMC"),e.stream=A.join("\n"),e},YesNormal:function(t){var e=a(t),A=F.internal.getFont(t.fontName,t.fontStyle).id,r=F.__private__.encodeColorString(t.color),n=[],i=X.internal.getHeight(t),o=X.internal.getWidth(t),s=c(t,t.caption);return n.push("1 g"),n.push("0 0 "+U(o)+" "+U(i)+" re"),n.push("f"),n.push("q"),n.push("0 0 1 rg"),n.push("0 0 "+U(o-1)+" "+U(i-1)+" re"),n.push("W"),n.push("n"),n.push("0 g"),n.push("BT"),n.push("/"+A+" "+U(s.fontSize)+" Tf "+r),n.push(s.text),n.push("ET"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=a(t),A=[];return A.push("0.749023 g"),A.push("0 0 "+U(X.internal.getWidth(t))+" "+U(X.internal.getHeight(t))+" re"),A.push("f"),e.stream=A.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:X.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=X.RadioButton.Circle.YesNormal,e.D[t]=X.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=a(t),A=[],r=X.internal.getWidth(t)<=X.internal.getHeight(t)?X.internal.getWidth(t)/4:X.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var n=X.internal.Bezier_C,i=Number((r*n).toFixed(5));return A.push("q"),A.push("1 0 0 1 "+s(X.internal.getWidth(t)/2)+" "+s(X.internal.getHeight(t)/2)+" cm"),A.push(r+" 0 m"),A.push(r+" "+i+" "+i+" "+r+" 0 "+r+" c"),A.push("-"+i+" "+r+" -"+r+" "+i+" -"+r+" 0 c"),A.push("-"+r+" -"+i+" -"+i+" -"+r+" 0 -"+r+" c"),A.push(i+" -"+r+" "+r+" -"+i+" "+r+" 0 c"),A.push("f"),A.push("Q"),e.stream=A.join("\n"),e},YesPushDown:function(t){var e=a(t),A=[],r=X.internal.getWidth(t)<=X.internal.getHeight(t)?X.internal.getWidth(t)/4:X.internal.getHeight(t)/4,n=(r=Number((.9*r).toFixed(5)),Number((2*r).toFixed(5))),i=Number((n*X.internal.Bezier_C).toFixed(5)),o=Number((r*X.internal.Bezier_C).toFixed(5));return A.push("0.749023 g"),A.push("q"),A.push("1 0 0 1 "+s(X.internal.getWidth(t)/2)+" "+s(X.internal.getHeight(t)/2)+" cm"),A.push(n+" 0 m"),A.push(n+" "+i+" "+i+" "+n+" 0 "+n+" c"),A.push("-"+i+" "+n+" -"+n+" "+i+" -"+n+" 0 c"),A.push("-"+n+" -"+i+" -"+i+" -"+n+" 0 -"+n+" c"),A.push(i+" -"+n+" "+n+" -"+i+" "+n+" 0 c"),A.push("f"),A.push("Q"),A.push("0 g"),A.push("q"),A.push("1 0 0 1 "+s(X.internal.getWidth(t)/2)+" "+s(X.internal.getHeight(t)/2)+" cm"),A.push(r+" 0 m"),A.push(r+" "+o+" "+o+" "+r+" 0 "+r+" c"),A.push("-"+o+" "+r+" -"+r+" "+o+" -"+r+" 0 c"),A.push("-"+r+" -"+o+" -"+o+" -"+r+" 0 -"+r+" c"),A.push(o+" -"+r+" "+r+" -"+o+" "+r+" 0 c"),A.push("f"),A.push("Q"),e.stream=A.join("\n"),e},OffPushDown:function(t){var e=a(t),A=[],r=X.internal.getWidth(t)<=X.internal.getHeight(t)?X.internal.getWidth(t)/4:X.internal.getHeight(t)/4,n=(r=Number((.9*r).toFixed(5)),Number((2*r).toFixed(5))),i=Number((n*X.internal.Bezier_C).toFixed(5));return A.push("0.749023 g"),A.push("q"),A.push("1 0 0 1 "+s(X.internal.getWidth(t)/2)+" "+s(X.internal.getHeight(t)/2)+" cm"),A.push(n+" 0 m"),A.push(n+" "+i+" "+i+" "+n+" 0 "+n+" c"),A.push("-"+i+" "+n+" -"+n+" "+i+" -"+n+" 0 c"),A.push("-"+n+" -"+i+" -"+i+" -"+n+" 0 -"+n+" c"),A.push(i+" -"+n+" "+n+" -"+i+" "+n+" 0 c"),A.push("f"),A.push("Q"),e.stream=A.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:X.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=X.RadioButton.Cross.YesNormal,e.D[t]=X.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=a(t),A=[],r=X.internal.calculateCross(t);return A.push("q"),A.push("1 1 "+U(X.internal.getWidth(t)-2)+" "+U(X.internal.getHeight(t)-2)+" re"),A.push("W"),A.push("n"),A.push(U(r.x1.x)+" "+U(r.x1.y)+" m"),A.push(U(r.x2.x)+" "+U(r.x2.y)+" l"),A.push(U(r.x4.x)+" "+U(r.x4.y)+" m"),A.push(U(r.x3.x)+" "+U(r.x3.y)+" l"),A.push("s"),A.push("Q"),e.stream=A.join("\n"),e},YesPushDown:function(t){var e=a(t),A=X.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+U(X.internal.getWidth(t))+" "+U(X.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+U(X.internal.getWidth(t)-2)+" "+U(X.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(U(A.x1.x)+" "+U(A.x1.y)+" m"),r.push(U(A.x2.x)+" "+U(A.x2.y)+" l"),r.push(U(A.x4.x)+" "+U(A.x4.y)+" m"),r.push(U(A.x3.x)+" "+U(A.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=a(t),A=[];return A.push("0.749023 g"),A.push("0 0 "+U(X.internal.getWidth(t))+" "+U(X.internal.getHeight(t))+" re"),A.push("f"),e.stream=A.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=F.internal.getFont(t.fontName,t.fontStyle).id,A=F.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+A}};X.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=X.internal.getWidth(t),A=X.internal.getHeight(t),r=Math.min(e,A);return{x1:{x:(e-r)/2,y:(A-r)/2+r},x2:{x:(e-r)/2+r,y:(A-r)/2},x3:{x:(e-r)/2,y:(A-r)/2},x4:{x:(e-r)/2+r,y:(A-r)/2+r}}}},X.internal.getWidth=function(t){var e=0;return"object"===se(t)&&(e=w(t.Rect[2])),e},X.internal.getHeight=function(t){var e=0;return"object"===se(t)&&(e=w(t.Rect[3])),e};var G=t.addField=function(t){if(L.call(this),!(t instanceof T))throw new Error("Invalid argument passed to jsPDF.addField.");return function(t){F.internal.acroformPlugin.printedOut&&(F.internal.acroformPlugin.printedOut=!1,F.internal.acroformPlugin.acroFormDictionaryRoot=null),F.internal.acroformPlugin.acroFormDictionaryRoot||L.call(F),F.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(t)}.call(this,t),t.page=F.internal.getCurrentPageInfo().pageNumber,this};t.addButton=function(t){if(t instanceof K==!1)throw new Error("Invalid argument passed to jsPDF.addButton.");return G.call(this,t)},t.addTextField=function(t){if(t instanceof q==!1)throw new Error("Invalid argument passed to jsPDF.addTextField.");return G.call(this,t)},t.addChoiceField=function(t){if(t instanceof O==!1)throw new Error("Invalid argument passed to jsPDF.addChoiceField.");return G.call(this,t)};"object"==se(e)&&void 0===e.ChoiceField&&void 0===e.ListBox&&void 0===e.ComboBox&&void 0===e.EditBox&&void 0===e.Button&&void 0===e.PushButton&&void 0===e.RadioButton&&void 0===e.CheckBox&&void 0===e.TextField&&void 0===e.PasswordField?(e.ChoiceField=O,e.ListBox=R,e.ComboBox=P,e.EditBox=M,e.Button=K,e.PushButton=D,e.RadioButton=k,e.CheckBox=j,e.TextField=q,e.PasswordField=V,e.AcroForm={Appearance:X}):console.warn("AcroForm-Classes are not populated into global-namespace, because the class-Names exist already."),t.AcroFormChoiceField=O,t.AcroFormListBox=R,t.AcroFormComboBox=P,t.AcroFormEditBox=M,t.AcroFormButton=K,t.AcroFormPushButton=D,t.AcroFormRadioButton=k,t.AcroFormCheckBox=j,t.AcroFormTextField=q,t.AcroFormPasswordField=V,t.AcroFormAppearance=X,t.AcroForm={ChoiceField:O,ListBox:R,ComboBox:P,EditBox:M,Button:K,PushButton:D,RadioButton:k,CheckBox:j,TextField:q,PasswordField:V,Appearance:X}})((window.tmp=at).API,"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt),function(y){var C="addImage_",a={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},c=y.getImageFileTypeByImageData=function(t,e){var A,r;e=e||"UNKNOWN";var n,i,o,s="UNKNOWN";for(o in y.isArrayBufferView(t)&&(t=y.arrayBufferToBinaryString(t)),a)for(n=a[o],A=0;A<n.length;A+=1){for(i=!0,r=0;r<n[A].length;r+=1)if(void 0!==n[A][r]&&n[A][r]!==t.charCodeAt(r)){i=!1;break}if(!0===i){s=o;break}}return"UNKNOWN"===s&&"UNKNOWN"!==e&&(console.warn('FileType of Image not recognized. Processing image as "'+e+'".'),s=e),s},A=function t(e){for(var A=this.internal.newObject(),r=this.internal.write,n=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.n=A;var o=[];if(o.push({key:"Type",value:"/XObject"}),o.push({key:"Subtype",value:"/Image"}),o.push({key:"Width",value:e.w}),o.push({key:"Height",value:e.h}),e.cs===this.color_spaces.INDEXED?o.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.pal.length/3-1)+" "+("smask"in e?A+2:A+1)+" 0 R]"}):(o.push({key:"ColorSpace",value:"/"+e.cs}),e.cs===this.color_spaces.DEVICE_CMYK&&o.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),o.push({key:"BitsPerComponent",value:e.bpc}),"dp"in e&&o.push({key:"DecodeParms",value:"<<"+e.dp+">>"}),"trns"in e&&e.trns.constructor==Array){for(var s="",a=0,c=e.trns.length;a<c;a++)s+=e.trns[a]+" "+e.trns[a]+" ";o.push({key:"Mask",value:"["+s+"]"})}"smask"in e&&o.push({key:"SMask",value:A+1+" 0 R"});var u=void 0!==e.f?["/"+e.f]:void 0;if(n({data:e.data,additionalKeyValues:o,alreadyAppliedFilters:u}),r("endobj"),"smask"in e){var l="/Predictor "+e.p+" /Colors 1 /BitsPerComponent "+e.bpc+" /Columns "+e.w,h={w:e.w,h:e.h,cs:"DeviceGray",bpc:e.bpc,dp:l,data:e.smask};"f"in e&&(h.f=e.f),t.call(this,h)}e.cs===this.color_spaces.INDEXED&&(this.internal.newObject(),n({data:this.arrayBufferToBinaryString(new Uint8Array(e.pal))}),r("endobj"))},Q=function(){var t=this.internal.collections[C+"images"];for(var e in t)A.call(this,t[e])},F=function(){var t,e=this.internal.collections[C+"images"],A=this.internal.write;for(var r in e)A("/I"+(t=e[r]).i,t.n,"0","R")},b=function(t){return"function"==typeof y["process"+t.toUpperCase()]},U=function(t){return"object"===se(t)&&1===t.nodeType},E=function(t,e){if("IMG"===t.nodeName&&t.hasAttribute("src")){var A=""+t.getAttribute("src");if(0===A.indexOf("data:image/"))return unescape(A);var r=y.loadFile(A);if(void 0!==r)return btoa(r)}if("CANVAS"===t.nodeName){var n=t;return t.toDataURL("image/jpeg",1)}(n=document.createElement("canvas")).width=t.clientWidth||t.width,n.height=t.clientHeight||t.height;var i=n.getContext("2d");if(!i)throw"addImage requires canvas to be supported by browser.";return i.drawImage(t,0,0,n.width,n.height),n.toDataURL("png"==(""+e).toLowerCase()?"image/png":"image/jpeg")},N=function(t,e){var A;if(e)for(var r in e)if(t===e[r].alias){A=e[r];break}return A};y.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"},y.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"},y.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},y.sHashCode=function(t){var e,A=0;if(0===(t=t||"").length)return A;for(e=0;e<t.length;e++)A=(A<<5)-A+t.charCodeAt(e),A|=0;return A},y.isString=function(t){return"string"==typeof t},y.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+\/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9\/][A-Za-z0-9+\/]|[A-Za-z0-9+\/]=|==$/.test(t.substr(-2))&&(e=!1),e},y.extractInfoFromBase64DataURI=function(t){return/^data:([\w]+?\/([\w]+?));\S*;*base64,(.+)$/g.exec(t)},y.extractImageFromDataUrl=function(t){var e=(t=t||"").split("base64,"),A=null;if(2===e.length){var r=/^data:(\w*\/\w*);*(charset=[\w=-]*)*;*$/.exec(e[0]);Array.isArray(r)&&(A={mimeType:r[1],charset:r[2],data:e[1]})}return A},y.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array},y.isArrayBuffer=function(t){return!!this.supportsArrayBuffer()&&t instanceof ArrayBuffer},y.isArrayBufferView=function(t){return!!this.supportsArrayBuffer()&&("undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array))},y.binaryStringToUint8Array=function(t){for(var e=t.length,A=new Uint8Array(e),r=0;r<e;r++)A[r]=t.charCodeAt(r);return A},y.arrayBufferToBinaryString=function(t){if("function"==typeof atob)return atob(this.arrayBufferToBase64(t))},y.arrayBufferToBase64=function(t){for(var e,A="",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=new Uint8Array(t),i=n.byteLength,o=i%3,s=i-o,a=0;a<s;a+=3)A+=r[(16515072&(e=n[a]<<16|n[a+1]<<8|n[a+2]))>>18]+r[(258048&e)>>12]+r[(4032&e)>>6]+r[63&e];return 1==o?A+=r[(252&(e=n[s]))>>2]+r[(3&e)<<4]+"==":2==o&&(A+=r[(64512&(e=n[s]<<8|n[s+1]))>>10]+r[(1008&e)>>4]+r[(15&e)<<2]+"="),A},y.createImageInfo=function(t,e,A,r,n,i,o,s,a,c,u,l,h){var f={alias:s,w:e,h:A,cs:r,bpc:n,i:o,data:t};return i&&(f.f=i),a&&(f.dp=a),c&&(f.trns=c),u&&(f.pal=u),l&&(f.smask=l),h&&(f.p=h),f},y.addImage=function(t,e,A,r,n,i,o,s,a){var c="";if("string"!=typeof e){var u=i;i=n,n=r,r=A,A=e,e=u}if("object"===se(t)&&!U(t)&&"imageData"in t){var l=t;t=l.imageData,e=l.format||e||"UNKNOWN",A=l.x||A||0,r=l.y||r||0,n=l.w||n,i=l.h||i,o=l.alias||o,s=l.compression||s,a=l.rotation||l.angle||a}var h=this.internal.getFilters();if(void 0===s&&-1!==h.indexOf("FlateEncode")&&(s="SLOW"),"string"==typeof t&&(t=unescape(t)),isNaN(A)||isNaN(r))throw console.error("jsPDF.addImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addImage");var f,d,p,g,B,w,m,v=function(){var t=this.internal.collections[C+"images"];return t||(this.internal.collections[C+"images"]=t={},this.internal.events.subscribe("putResources",Q),this.internal.events.subscribe("putXobjectDict",F)),t}.call(this);if(!((f=N(t,v))||(U(t)&&(t=E(t,e)),(null==(m=o)||0===m.length)&&(o="string"==typeof(w=t)?y.sHashCode(w):y.isArrayBufferView(w)?y.sHashCode(y.arrayBufferToBinaryString(w)):null),f=N(o,v)))){if(this.isString(t)&&(""!==(c=this.convertStringToImageData(t))?t=c:void 0!==(c=y.loadFile(t))&&(t=c)),e=this.getImageFileTypeByImageData(t,e),!b(e))throw new Error("addImage does not support files of type '"+e+"', please ensure that a plugin for '"+e+"' support is added.");if(this.supportsArrayBuffer()&&(t instanceof Uint8Array||(d=t,t=this.binaryStringToUint8Array(t))),!(f=this["process"+e.toUpperCase()](t,(B=0,(g=v)&&(B=Object.keys?Object.keys(g).length:function(t){var e=0;for(var A in t)t.hasOwnProperty(A)&&e++;return e}(g)),B),o,((p=s)&&"string"==typeof p&&(p=p.toUpperCase()),p in y.image_compression?p:y.image_compression.NONE),d)))throw new Error("An unknown error occurred whilst processing the image")}return function(t,e,A,r,n,i,o,s){var a=function(t,e,A){return t||e||(e=t=-96),t<0&&(t=-1*A.w*72/t/this.internal.scaleFactor),e<0&&(e=-1*A.h*72/e/this.internal.scaleFactor),0===t&&(t=e*A.w/A.h),0===e&&(e=t*A.h/A.w),[t,e]}.call(this,A,r,n),c=this.internal.getCoordinateString,u=this.internal.getVerticalCoordinateString;if(A=a[0],r=a[1],o[i]=n,s){s*=Math.PI/180;var l=Math.cos(s),h=Math.sin(s),f=function(t){return t.toFixed(4)},d=[f(l),f(h),f(-1*h),f(l),0,0,"cm"]}this.internal.write("q"),s?(this.internal.write([1,"0","0",1,c(t),u(e+r),"cm"].join(" ")),this.internal.write(d.join(" ")),this.internal.write([c(A),"0","0",c(r),"0","0","cm"].join(" "))):this.internal.write([c(A),"0","0",c(r),c(t),u(e+r),"cm"].join(" ")),this.internal.write("/I"+n.i+" Do"),this.internal.write("Q")}.call(this,A,r,n,i,f,f.i,v,a),this},y.convertStringToImageData=function(t){var e,A="";if(this.isString(t)){var r;e=null!==(r=this.extractImageFromDataUrl(t))?r.data:t;try{A=atob(e)}catch(t){throw y.validateStringAsBase64(e)?new Error("atob-Error in jsPDF.convertStringToImageData "+t.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertStringToImageData ")}}return A};var u=function(t,e){return t.subarray(e,e+5)};y.processJPEG=function(t,e,A,r,n,i){var o,s=this.decode.DCT_DECODE;if(!this.isString(t)&&!this.isArrayBuffer(t)&&!this.isArrayBufferView(t))return null;if(this.isString(t)&&(o=function(t){var e;if("JPEG"!==c(t))throw new Error("getJpegSize requires a binary string jpeg file");for(var A=256*t.charCodeAt(4)+t.charCodeAt(5),r=4,n=t.length;r<n;){if(r+=A,255!==t.charCodeAt(r))throw new Error("getJpegSize could not find the size of the image");if(192===t.charCodeAt(r+1)||193===t.charCodeAt(r+1)||194===t.charCodeAt(r+1)||195===t.charCodeAt(r+1)||196===t.charCodeAt(r+1)||197===t.charCodeAt(r+1)||198===t.charCodeAt(r+1)||199===t.charCodeAt(r+1))return e=256*t.charCodeAt(r+5)+t.charCodeAt(r+6),[256*t.charCodeAt(r+7)+t.charCodeAt(r+8),e,t.charCodeAt(r+9)];r+=2,A=256*t.charCodeAt(r)+t.charCodeAt(r+1)}}(t)),this.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.isArrayBufferView(t)&&(o=function(t){if(65496!=(t[0]<<8|t[1]))throw new Error("Supplied data is not a JPEG");for(var e,A=t.length,r=(t[4]<<8)+t[5],n=4;n<A;){if(r=((e=u(t,n+=r))[2]<<8)+e[3],(192===e[1]||194===e[1])&&255===e[0]&&7<r)return{width:((e=u(t,n+5))[2]<<8)+e[3],height:(e[0]<<8)+e[1],numcomponents:e[4]};n+=2}throw new Error("getJpegSizeFromBytes could not find the size of the image")}(t),t=n||this.arrayBufferToBinaryString(t)),void 0===i)switch(o.numcomponents){case 1:i=this.color_spaces.DEVICE_GRAY;break;case 4:i=this.color_spaces.DEVICE_CMYK;break;default:case 3:i=this.color_spaces.DEVICE_RGB}return this.createImageInfo(t,o.width,o.height,i,8,s,e,A)},y.processJPG=function(){return this.processJPEG.apply(this,arguments)},y.getImageProperties=function(t){var e,A,r="";if(U(t)&&(t=E(t)),this.isString(t)&&(""!==(r=this.convertStringToImageData(t))?t=r:void 0!==(r=y.loadFile(t))&&(t=r)),A=this.getImageFileTypeByImageData(t),!b(A))throw new Error("addImage does not support files of type '"+A+"', please ensure that a plugin for '"+A+"' support is added.");if(this.supportsArrayBuffer()&&(t instanceof Uint8Array||(t=this.binaryStringToUint8Array(t))),!(e=this["process"+A.toUpperCase()](t)))throw new Error("An unknown error occurred whilst processing the image");return{fileType:A,width:e.w,height:e.h,colorSpace:e.cs,compressionMode:e.f,bitsPerComponent:e.bpc}}}(at.API),t=at.API,at.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(t){for(var e=this.internal.getPageInfoByObjId(t.objId),A=t.pageContext.annotations,r=function(t){if(void 0!==t&&""!=t)return!0},n=!1,i=0;i<A.length&&!n;i++)switch((a=A[i]).type){case"link":if(r(a.options.url)||r(a.options.pageNumber)){n=!0;break}case"reference":case"text":case"freetext":n=!0}if(0!=n){this.internal.write("/Annots ["),this.internal.pageSize.height;var o=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString;for(i=0;i<A.length;i++){var a;switch((a=A[i]).type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var c=this.internal.newAdditionalObject(),u=this.internal.newAdditionalObject(),l=a.title||"Note";g="<</Type /Annot /Subtype /Text "+(f="/Rect ["+o(a.bounds.x)+" "+s(a.bounds.y+a.bounds.h)+" "+o(a.bounds.x+a.bounds.w)+" "+s(a.bounds.y)+"] ")+"/Contents ("+a.contents+")",g+=" /Popup "+u.objId+" 0 R",g+=" /P "+e.objId+" 0 R",g+=" /T ("+l+") >>",c.content=g;var h=c.objId+" 0 R";g="<</Type /Annot /Subtype /Popup "+(f="/Rect ["+o(a.bounds.x+30)+" "+s(a.bounds.y+a.bounds.h)+" "+o(a.bounds.x+a.bounds.w+30)+" "+s(a.bounds.y)+"] ")+" /Parent "+h,a.open&&(g+=" /Open true"),g+=" >>",u.content=g,this.internal.write(c.objId,"0 R",u.objId,"0 R");break;case"freetext":var f="/Rect ["+o(a.bounds.x)+" "+s(a.bounds.y)+" "+o(a.bounds.x+a.bounds.w)+" "+s(a.bounds.y+a.bounds.h)+"] ",d=a.color||"#000000";g="<</Type /Annot /Subtype /FreeText "+f+"/Contents ("+a.contents+")",g+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+d+")",g+=" /Border [0 0 0]",g+=" >>",this.internal.write(g);break;case"link":if(a.options.name){var p=this.annotations._nameMap[a.options.name];a.options.pageNumber=p.page,a.options.top=p.y}else a.options.top||(a.options.top=0);f="/Rect ["+o(a.x)+" "+s(a.y)+" "+o(a.x+a.w)+" "+s(a.y+a.h)+"] ";var g="";if(a.options.url)g="<</Type /Annot /Subtype /Link "+f+"/Border [0 0 0] /A <</S /URI /URI ("+a.options.url+") >>";else if(a.options.pageNumber)switch(g="<</Type /Annot /Subtype /Link "+f+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":g+=" /Fit]";break;case"FitH":g+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,g+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var B=s(a.options.top);a.options.left=a.options.left||0,void 0===a.options.zoom&&(a.options.zoom=0),g+=" /XYZ "+a.options.left+" "+B+" "+a.options.zoom+"]"}""!=g&&(g+=" >>",this.internal.write(g))}}this.internal.write("]")}}]),t.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},t.link=function(t,e,A,r,n){this.internal.getCurrentPageInfo().pageContext.annotations.push({x:t,y:e,w:A,h:r,options:n,type:"link"})},t.textWithLink=function(t,e,A,r){var n=this.getTextWidth(t),i=this.internal.getLineHeight()/this.internal.scaleFactor;return this.text(t,e,A),A+=.2*i,this.link(e,A-i,n,i,r),n},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor},function(t){var c={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},o={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},e={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},A=[1570,1571,1573,1575];t.__arabicParser__={};var r=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==c[t.charCodeAt(0)]},u=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},n=t.__arabicParser__.isArabicEndLetter=function(t){return u(t)&&r(t)&&c[t.charCodeAt(0)].length<=2},i=t.__arabicParser__.isArabicAlfLetter=function(t){return u(t)&&0<=A.indexOf(t.charCodeAt(0))},s=(t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return u(t)&&r(t)&&1<=c[t.charCodeAt(0)].length},t.__arabicParser__.arabicLetterHasFinalForm=function(t){return u(t)&&r(t)&&2<=c[t.charCodeAt(0)].length}),a=(t.__arabicParser__.arabicLetterHasInitialForm=function(t){return u(t)&&r(t)&&3<=c[t.charCodeAt(0)].length},t.__arabicParser__.arabicLetterHasMedialForm=function(t){return u(t)&&r(t)&&4==c[t.charCodeAt(0)].length}),l=t.__arabicParser__.resolveLigatures=function(t){var e=0,A=o,r=0,n="",i=0;for(e=0;e<t.length;e+=1)void 0!==A[t.charCodeAt(e)]?(i++,"number"==typeof(A=A[t.charCodeAt(e)])&&(r=-1!==(r=h(t.charAt(e),t.charAt(e-i),t.charAt(e+1)))?r:0,n+=String.fromCharCode(A),A=o,i=0),e===t.length-1&&(A=o,n+=t.charAt(e-(i-1)),e-=i-1,i=0)):(A=o,n+=t.charAt(e-i),e-=i,i=0);return n},h=(t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==e[t.charCodeAt(0)]},t.__arabicParser__.getCorrectForm=function(t,e,A){return u(t)?!1===r(t)?-1:!s(t)||!u(e)&&!u(A)||!u(A)&&n(e)||n(t)&&!u(e)||n(t)&&i(e)||n(t)&&n(e)?0:a(t)&&u(e)&&!n(e)&&u(A)&&s(A)?3:n(t)||!u(A)?1:2:-1}),f=t.__arabicParser__.processArabic=t.processArabic=function(t){var e=0,A=0,r=0,n="",i="",o="",s=(t=t||"").split("\\s+"),a=[];for(e=0;e<s.length;e+=1){for(a.push(""),A=0;A<s[e].length;A+=1)n=s[e][A],i=s[e][A-1],o=s[e][A+1],u(n)?(r=h(n,i,o),a[e]+=-1!==r?String.fromCharCode(c[n.charCodeAt(0)][r]):n):a[e]+=n;a[e]=l(a[e])}return a.join(" ")};t.events.push(["preProcessText",function(t){var e=t.text,A=(t.x,t.y,t.options||{}),r=(t.mutex,A.lang,[]);if("[object Array]"===Object.prototype.toString.call(e)){var n=0;for(r=[],n=0;n<e.length;n+=1)"[object Array]"===Object.prototype.toString.call(e[n])?r.push([f(e[n][0]),e[n][1],e[n][2]]):r.push([f(e[n])]);t.text=r}else t.text=f(e)}])}(at.API),at.API.autoPrint=function(t){var e;switch((t=t||{}).variant=t.variant||"non-conform",t.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},e=at.API,(A=function(){var e=void 0;Object.defineProperty(this,"pdf",{get:function(){return e},set:function(t){e=t}});var A=150;Object.defineProperty(this,"width",{get:function(){return A},set:function(t){A=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=A+1)}});var r=300;Object.defineProperty(this,"height",{get:function(){return r},set:function(t){r=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=r+1)}});var n=[];Object.defineProperty(this,"childNodes",{get:function(){return n},set:function(t){n=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{get:function(){return!1}})}).prototype.getContext=function(t,e){var A;if("2d"!==(t=t||"2d"))return null;for(A in e)this.pdf.context2d.hasOwnProperty(A)&&(this.pdf.context2d[A]=e[A]);return(this.pdf.context2d._canvas=this).pdf.context2d},A.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},e.events.push(["initialized",function(){this.canvas=new A,this.canvas.pdf=this}]),U=at.API,E={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},N=1,f=function(t,e,A,r,n){E={x:t,y:e,w:A,h:r,ln:n}},d=function(){return E},L={left:0,top:0,bottom:0},U.setHeaderFunction=function(t){a=t},U.getTextDimensions=function(t,e){var A=this.table_font_size||this.internal.getFontSize(),r=(this.internal.getFont().fontStyle,(e=e||{}).scaleFactor||this.internal.scaleFactor),n=0,i=0,o=0;if("string"==typeof t)0!=(n=this.getStringUnitWidth(t)*A)&&(i=1);else{if("[object Array]"!==Object.prototype.toString.call(t))throw new Error("getTextDimensions expects text-parameter to be of type String or an Array of Strings.");for(var s=0;s<t.length;s++)n<(o=this.getStringUnitWidth(t[s])*A)&&(n=o);0!==n&&(i=t.length)}return{w:n/=r,h:Math.max((i*A*this.getLineHeightFactor()-A*(this.getLineHeightFactor()-1))/r,0)}},U.cellAddPage=function(){var t=this.margins||L;this.addPage(),f(t.left,t.top,void 0,void 0),N+=1},U.cellInitialize=function(){E={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},N=1},U.cell=function(t,e,A,r,n,i,o){var s=d(),a=!1;if(void 0!==s.ln)if(s.ln===i)t=s.x+s.w,e=s.y;else{var c=this.margins||L;s.y+s.h+r+13>=this.internal.pageSize.getHeight()-c.bottom&&(this.cellAddPage(),a=!0,this.printHeaders&&this.tableHeaderRow&&this.printHeaderRow(i,!0)),e=d().y+d().h,a&&(e=23)}if(void 0!==n[0])if(this.printingHeaderRow?this.rect(t,e,A,r,"FD"):this.rect(t,e,A,r),"right"===o){n instanceof Array||(n=[n]);for(var u=0;u<n.length;u++){var l=n[u],h=this.getStringUnitWidth(l)*this.internal.getFontSize()/this.internal.scaleFactor;this.text(l,t+A-h-3,e+this.internal.getLineHeight()*(u+1))}}else this.text(n,t+3,e+this.internal.getLineHeight());return f(t,e,A,r,i),this},U.arrayMax=function(t,e){var A,r,n,i=t[0];for(A=0,r=t.length;A<r;A+=1)n=t[A],e?-1===e(i,n)&&(i=n):i<n&&(i=n);return i},U.table=function(t,e,A,r,n){if(!A)throw"No data for PDF table";var i,o,s,a,c,u,l,h,f,d,p=[],g=[],B={},w={},m=[],v=[],y=!1,C=!0,Q=12,F=L;if(F.width=this.internal.pageSize.getWidth(),n&&(!0===n.autoSize&&(y=!0),!1===n.printHeaders&&(C=!1),n.fontSize&&(Q=n.fontSize),n.css&&void 0!==n.css["font-size"]&&(Q=16*n.css["font-size"]),n.margins&&(F=n.margins)),this.lnMod=0,E={x:void 0,y:void 0,w:void 0,h:void 0,ln:void 0},N=1,this.printHeaders=C,this.margins=F,this.setFontSize(Q),this.table_font_size=Q,null==r)p=Object.keys(A[0]);else if(r[0]&&"string"!=typeof r[0])for(o=0,s=r.length;o<s;o+=1)i=r[o],p.push(i.name),g.push(i.prompt),w[i.name]=i.width*(19.049976/25.4);else p=r;if(y)for(d=function(t){return t[i]},o=0,s=p.length;o<s;o+=1){for(B[i=p[o]]=A.map(d),m.push(this.getTextDimensions(g[o]||i,{scaleFactor:1}).w),l=0,a=(u=B[i]).length;l<a;l+=1)c=u[l],m.push(this.getTextDimensions(c,{scaleFactor:1}).w);w[i]=U.arrayMax(m),m=[]}if(C){var b=this.calculateLineHeight(p,w,g.length?g:p);for(o=0,s=p.length;o<s;o+=1)i=p[o],v.push([t,e,w[i],b,String(g.length?g[o]:i)]);this.setTableHeaderRow(v),this.printHeaderRow(1,!1)}for(o=0,s=A.length;o<s;o+=1)for(h=A[o],b=this.calculateLineHeight(p,w,h),l=0,f=p.length;l<f;l+=1)i=p[l],this.cell(t,e,w[i],b,h[i],o+2,i.align);return this.lastCellPos=E,this.table_x=t,this.table_y=e,this},U.calculateLineHeight=function(t,e,A){for(var r,n=0,i=0;i<t.length;i++){A[r=t[i]]=this.splitTextToSize(String(A[r]),e[r]-3);var o=this.internal.getLineHeight()*A[r].length+3;n<o&&(n=o)}return n},U.setTableHeaderRow=function(t){this.tableHeaderRow=t},U.printHeaderRow=function(t,e){if(!this.tableHeaderRow)throw"Property tableHeaderRow does not exist.";var A,r,n,i;if(this.printingHeaderRow=!0,void 0!==a){var o=a(this,N);f(o[0],o[1],o[2],o[3],-1)}this.setFontStyle("bold");var s=[];for(n=0,i=this.tableHeaderRow.length;n<i;n+=1)this.setFillColor(200,200,200),A=this.tableHeaderRow[n],e&&(this.margins.top=13,A[1]=this.margins&&this.margins.top||0,s.push(A)),r=[].concat(A),this.cell.apply(this,r.concat(t));0<s.length&&this.setTableHeaderRow(s),this.setFontStyle("normal"),this.printingHeaderRow=!1},function(t,e){var a,n,i,c,u,l=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new T,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new _,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new _,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new A(this),a=this.internal.f2,this.internal.f3,n=this.internal.getCoordinateString,i=this.internal.getVerticalCoordinateString,c=this.internal.getHorizontalCoordinate,u=this.internal.getVerticalCoordinate}]);var A=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}}),Object.defineProperty(this,"pdf",{get:function(){return t}});var e=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return e},set:function(t){e=Boolean(t)}});var A=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return A},set:function(t){A=Boolean(t)}});var r=0;Object.defineProperty(this,"posX",{get:function(){return r},set:function(t){isNaN(t)||(r=t)}});var n=0;Object.defineProperty(this,"posY",{get:function(){return n},set:function(t){isNaN(t)||(n=t)}});var i=!1;Object.defineProperty(this,"autoPaging",{get:function(){return i},set:function(t){i=Boolean(t)}});var o=0;Object.defineProperty(this,"lastBreak",{get:function(){return o},set:function(t){o=t}});var s=[];Object.defineProperty(this,"pageBreaks",{get:function(){return s},set:function(t){s=t}});var a=new l;Object.defineProperty(this,"ctx",{get:function(){return a},set:function(t){t instanceof l&&(a=t)}}),Object.defineProperty(this,"path",{get:function(){return a.path},set:function(t){a.path=t}});var c=[];Object.defineProperty(this,"ctxStack",{get:function(){return c},set:function(t){c=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=h(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=h(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var A=e[1],r=(e[2],e[3]),n=e[4],i=e[5],o=e[6];n="px"===i?Math.floor(parseFloat(n)):"em"===i?Math.floor(parseFloat(n)*this.pdf.getFontSize()):Math.floor(parseFloat(n)),this.pdf.setFontSize(n);var s="";("bold"===r||700<=parseInt(r,10)||"bold"===A)&&(s="bold"),"italic"===A&&(s+="italic"),0===s.length&&(s="normal");for(var a="",c=o.toLowerCase().replace(/"|'/g,"").split(/\s*,\s*/),u={arial:"Helvetica",verdana:"Helvetica",helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",courier:"Courier",times:"Times",cursive:"Times",fantasy:"Times",serif:"Times"},l=0;l<c.length;l++){if(void 0!==this.pdf.internal.getFont(c[l],s,{noFallback:!0,disableWarning:!0})){a=c[l];break}if("bolditalic"===s&&void 0!==this.pdf.internal.getFont(c[l],"bold",{noFallback:!0,disableWarning:!0}))a=c[l],s="bold";else if(void 0!==this.pdf.internal.getFont(c[l],"normal",{noFallback:!0,disableWarning:!0})){a=c[l],s="normal";break}}if(""===a)for(l=0;l<c.length;l++)if(u[c[l]]){a=u[c[l]];break}a=""===a?"Times":a,this.pdf.setFont(a,s)}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=Boolean(t)}})};A.prototype.fill=function(){r.call(this,"fill",!1)},A.prototype.stroke=function(){r.call(this,"stroke",!1)},A.prototype.beginPath=function(){this.path=[{type:"begin"}]},A.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var A=this.ctx.transform.applyToPoint(new _(t,e));this.path.push({type:"mt",x:A.x,y:A.y}),this.ctx.lastPoint=new _(t,e)},A.prototype.closePath=function(){var t=new _(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===se(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new _(this.path[e+1].x,this.path[e+1].y),this.path.push({type:"lt",x:t.x,y:t.y});break}"object"===se(this.path[e+2])&&"number"==typeof this.path[e+2].x&&this.path.push(JSON.parse(JSON.stringify(this.path[e+2]))),this.path.push({type:"close"}),this.ctx.lastPoint=new _(t.x,t.y)},A.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var A=this.ctx.transform.applyToPoint(new _(t,e));this.path.push({type:"lt",x:A.x,y:A.y}),this.ctx.lastPoint=new _(A.x,A.y)},A.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),r.call(this,null,!0)},A.prototype.quadraticCurveTo=function(t,e,A,r){if(isNaN(A)||isNaN(r)||isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var n=this.ctx.transform.applyToPoint(new _(A,r)),i=this.ctx.transform.applyToPoint(new _(t,e));this.path.push({type:"qct",x1:i.x,y1:i.y,x:n.x,y:n.y}),this.ctx.lastPoint=new _(n.x,n.y)},A.prototype.bezierCurveTo=function(t,e,A,r,n,i){if(isNaN(n)||isNaN(i)||isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw console.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var o=this.ctx.transform.applyToPoint(new _(n,i)),s=this.ctx.transform.applyToPoint(new _(t,e)),a=this.ctx.transform.applyToPoint(new _(A,r));this.path.push({type:"bct",x1:s.x,y1:s.y,x2:a.x,y2:a.y,x:o.x,y:o.y}),this.ctx.lastPoint=new _(o.x,o.y)},A.prototype.arc=function(t,e,A,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||isNaN(n))throw console.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(i=Boolean(i),!this.ctx.transform.isIdentity){var o=this.ctx.transform.applyToPoint(new _(t,e));t=o.x,e=o.y;var s=this.ctx.transform.applyToPoint(new _(0,A)),a=this.ctx.transform.applyToPoint(new _(0,0));A=Math.sqrt(Math.pow(s.x-a.x,2)+Math.pow(s.y-a.y,2))}Math.abs(n-r)>=2*Math.PI&&(r=0,n=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:A,startAngle:r,endAngle:n,counterclockwise:i})},A.prototype.arcTo=function(t,e,A,r,n){throw new Error("arcTo not implemented.")},A.prototype.rect=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw console.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+A,e),this.lineTo(t+A,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.lineTo(t+A,e),this.lineTo(t,e)},A.prototype.fillRect=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw console.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!C.call(this)){var n={};"butt"!==this.lineCap&&(n.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(n.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,A,r),this.fill(),n.hasOwnProperty("lineCap")&&(this.lineCap=n.lineCap),n.hasOwnProperty("lineJoin")&&(this.lineJoin=n.lineJoin)}},A.prototype.strokeRect=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw console.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");Q.call(this)||(this.beginPath(),this.rect(t,e,A,r),this.stroke())},A.prototype.clearRect=function(t,e,A,r){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r))throw console.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,A,r))},A.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,A=0;A<this.pdf.internal.getNumberOfPages();A++)this.pdf.setPage(A+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var r=new l(this.ctx);this.ctxStack.push(this.ctx),this.ctx=r}},A.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,A=0;A<this.pdf.internal.getNumberOfPages();A++)this.pdf.setPage(A+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin)},A.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var h=function(t){var e,A,r,n;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))n=r=A=e=0;else{var i=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==i)e=parseInt(i[1]),A=parseInt(i[2]),r=parseInt(i[3]),n=1;else if(null!==(i=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d\.]+)\s*\)/.exec(t)))e=parseInt(i[1]),A=parseInt(i[2]),r=parseInt(i[3]),n=parseFloat(i[4]);else{if(n=1,"string"==typeof t&&"#"!==t.charAt(0)){var o=new RGBColor(t);t=o.ok?o.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,A=t.substring(2,3),A+=A,r=t.substring(3,4),r+=r):(e=t.substring(1,3),A=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),A=parseInt(A,16),r=parseInt(r,16)}}return{r:e,g:A,b:r,a:n,style:t}},C=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},Q=function(){return Boolean(this.ctx.isStrokeTransparent||0==this.globalAlpha)};A.prototype.fillText=function(t,e,A,r){if(isNaN(e)||isNaN(A)||"string"!=typeof t)throw console.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(r=isNaN(r)?void 0:r,!C.call(this)){A=o.call(this,A);var n=x(this.ctx.transform.rotation),i=this.ctx.transform.scaleX;s.call(this,{text:t,x:e,y:A,scale:i,angle:n,align:this.textAlign,maxWidth:r})}},A.prototype.strokeText=function(t,e,A,r){if(isNaN(e)||isNaN(A)||"string"!=typeof t)throw console.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!Q.call(this)){r=isNaN(r)?void 0:r,A=o.call(this,A);var n=x(this.ctx.transform.rotation),i=this.ctx.transform.scaleX;s.call(this,{text:t,x:e,y:A,scale:i,renderingMode:"stroke",angle:n,align:this.textAlign,maxWidth:r})}},A.prototype.measureText=function(t){if("string"!=typeof t)throw console.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,A=this.pdf.internal.scaleFactor,r=e.internal.getFontSize(),n=e.getStringUnitWidth(t)*r/e.internal.scaleFactor;return new function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this}({width:n*=Math.round(96*A/72*1e4)/1e4})},A.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var A=new T(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(A)},A.prototype.rotate=function(t){if(isNaN(t))throw console.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new T(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},A.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw console.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var A=new T(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(A)},A.prototype.transform=function(t,e,A,r,n,i){if(isNaN(t)||isNaN(e)||isNaN(A)||isNaN(r)||isNaN(n)||isNaN(i))throw console.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var o=new T(t,e,A,r,n,i);this.ctx.transform=this.ctx.transform.multiply(o)},A.prototype.setTransform=function(t,e,A,r,n,i){t=isNaN(t)?1:t,e=isNaN(e)?0:e,A=isNaN(A)?0:A,r=isNaN(r)?1:r,n=isNaN(n)?0:n,i=isNaN(i)?0:i,this.ctx.transform=new T(t,e,A,r,n,i)},A.prototype.drawImage=function(t,e,A,r,n,i,o,s,a){var c=this.pdf.getImageProperties(t),u=1,l=1,h=1,f=1;void 0!==r&&void 0!==s&&(h=s/r,f=a/n,u=c.width/r*s/r,l=c.height/n*a/n),void 0===i&&(i=e,o=A,A=e=0),void 0!==r&&void 0===s&&(s=r,a=n),void 0===r&&void 0===s&&(s=c.width,a=c.height);var d=this.ctx.transform.decompose(),p=x(d.rotate.shx);d.scale.sx,d.scale.sy;for(var g,B=new T,w=((B=(B=(B=B.multiply(d.translate)).multiply(d.skew)).multiply(d.scale)).applyToPoint(new _(s,a)),B.applyToRectangle(new I(i-e*h,o-A*f,r*u,n*l))),m=E.call(this,w),v=[],y=0;y<m.length;y+=1)-1===v.indexOf(m[y])&&v.push(m[y]);if(v.sort(),this.autoPaging)for(var C=v[0],Q=v[v.length-1],F=C;F<Q+1;F++){if(this.pdf.setPage(F),0!==this.ctx.clip_path.length){var b=this.path;g=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(g,this.posX,-1*this.pdf.internal.pageSize.height*(F-1)+this.posY),L.call(this,"fill",!0),this.path=b}var U=JSON.parse(JSON.stringify(w));U=N([U],this.posX,-1*this.pdf.internal.pageSize.height*(F-1)+this.posY)[0],this.pdf.addImage(t,"jpg",U.x,U.y,U.w,U.h,null,null,p)}else this.pdf.addImage(t,"jpg",w.x,w.y,w.w,w.h,null,null,p)};var E=function(t,e,A){var r=[];switch(e=e||this.pdf.internal.pageSize.width,A=A||this.pdf.internal.pageSize.height,t.type){default:case"mt":case"lt":r.push(Math.floor((t.y+this.posY)/A)+1);break;case"arc":r.push(Math.floor((t.y+this.posY-t.radius)/A)+1),r.push(Math.floor((t.y+this.posY+t.radius)/A)+1);break;case"qct":var n=m(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);r.push(Math.floor(n.y/A)+1),r.push(Math.floor((n.y+n.h)/A)+1);break;case"bct":var i=v(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);r.push(Math.floor(i.y/A)+1),r.push(Math.floor((i.y+i.h)/A)+1);break;case"rect":r.push(Math.floor((t.y+this.posY)/A)+1),r.push(Math.floor((t.y+t.h+this.posY)/A)+1)}for(var o=0;o<r.length;o+=1)for(;this.pdf.internal.getNumberOfPages()<r[o];)w.call(this);return r},w=function(){var t=this.fillStyle,e=this.strokeStyle,A=this.font,r=this.lineCap,n=this.lineWidth,i=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=A,this.lineCap=r,this.lineWidth=n,this.lineJoin=i},N=function(t,e,A){for(var r=0;r<t.length;r++)switch(t[r].type){case"bct":t[r].x2+=e,t[r].y2+=A;case"qct":t[r].x1+=e,t[r].y1+=A;case"mt":case"lt":case"arc":default:t[r].x+=e,t[r].y+=A}return t},r=function(t,e){for(var A,r,n=this.fillStyle,i=this.strokeStyle,o=(this.font,this.lineCap),s=this.lineWidth,a=this.lineJoin,c=JSON.parse(JSON.stringify(this.path)),u=JSON.parse(JSON.stringify(this.path)),l=[],h=0;h<u.length;h++)if(void 0!==u[h].x)for(var f=E.call(this,u[h]),d=0;d<f.length;d+=1)-1===l.indexOf(f[d])&&l.push(f[d]);for(h=0;h<l.length;h++)for(;this.pdf.internal.getNumberOfPages()<l[h];)w.call(this);if(l.sort(),this.autoPaging){var p=l[0],g=l[l.length-1];for(h=p;h<g+1;h++){if(this.pdf.setPage(h),this.fillStyle=n,this.strokeStyle=i,this.lineCap=o,this.lineWidth=s,this.lineJoin=a,0!==this.ctx.clip_path.length){var B=this.path;A=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(A,this.posX,-1*this.pdf.internal.pageSize.height*(h-1)+this.posY),L.call(this,t,!0),this.path=B}r=JSON.parse(JSON.stringify(c)),this.path=N(r,this.posX,-1*this.pdf.internal.pageSize.height*(h-1)+this.posY),!1!==e&&0!==h||L.call(this,t,e)}}else L.call(this,t,e);this.path=c},L=function(t,e){if(("stroke"!==t||e||!Q.call(this))&&("stroke"===t||e||!C.call(this))){var A=[];this.ctx.globalAlpha;this.ctx.fillOpacity<1&&this.ctx.fillOpacity;for(var r,n=this.path,i=0;i<n.length;i++){var o=n[i];switch(o.type){case"begin":A.push({begin:!0});break;case"close":A.push({close:!0});break;case"mt":A.push({start:o,deltas:[],abs:[]});break;case"lt":var s=A.length;if(!isNaN(n[i-1].x)){var a=[o.x-n[i-1].x,o.y-n[i-1].y];if(0<s)for(;0<=s;s--)if(!0!==A[s-1].close&&!0!==A[s-1].begin){A[s-1].deltas.push(a),A[s-1].abs.push(o);break}}break;case"bct":a=[o.x1-n[i-1].x,o.y1-n[i-1].y,o.x2-n[i-1].x,o.y2-n[i-1].y,o.x-n[i-1].x,o.y-n[i-1].y];A[A.length-1].deltas.push(a);break;case"qct":var c=n[i-1].x+2/3*(o.x1-n[i-1].x),u=n[i-1].y+2/3*(o.y1-n[i-1].y),l=o.x+2/3*(o.x1-o.x),h=o.y+2/3*(o.y1-o.y),f=o.x,d=o.y;a=[c-n[i-1].x,u-n[i-1].y,l-n[i-1].x,h-n[i-1].y,f-n[i-1].x,d-n[i-1].y];A[A.length-1].deltas.push(a);break;case"arc":A.push({deltas:[],abs:[],arc:!0}),Array.isArray(A[A.length-1].abs)&&A[A.length-1].abs.push(o)}}r=e?null:"stroke"===t?"stroke":"fill";for(i=0;i<A.length;i++){if(A[i].arc)for(var p=A[i].abs,g=0;g<p.length;g++){var B=p[g];if(void 0!==B.startAngle){var w=x(B.startAngle),m=x(B.endAngle),v=B.x,y=B.y;F.call(this,v,y,B.radius,w,m,B.counterclockwise,r,e)}else H.call(this,B.x,B.y)}if(!A[i].arc&&!0!==A[i].close&&!0!==A[i].begin){v=A[i].start.x,y=A[i].start.y;S.call(this,A[i].deltas,v,y,null,null)}}r&&b.call(this,r),e&&U.call(this)}},o=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,A=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-A;case"top":return t+e-A;case"hanging":return t+e-2*A;case"middle":return t+e/2-A;case"ideographic":return t;case"alphabetic":default:return t}};A.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},A.prototype.createPattern=function(){return this.createLinearGradient()},A.prototype.createRadialGradient=function(){return this.createLinearGradient()};var F=function(t,e,A,r,n,i,o,s){this.pdf.internal.scaleFactor;for(var a=B(r),c=B(n),u=p.call(this,A,a,c,i),l=0;l<u.length;l++){var h=u[l];0===l&&f.call(this,h.x1+t,h.y1+e),d.call(this,t,e,h.x2,h.y2,h.x3,h.y3,h.x4,h.y4)}s?U.call(this):b.call(this,o)},b=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},U=function(){this.pdf.clip()},f=function(t,e){this.pdf.internal.out(n(t)+" "+i(e)+" m")},s=function(t){var e;switch(t.align){case"right":case"end":e="right";break;case"center":e="center";break;case"left":case"start":default:e="left"}var A=this.ctx.transform.applyToPoint(new _(t.x,t.y)),r=this.ctx.transform.decompose(),n=new T;n=(n=(n=n.multiply(r.translate)).multiply(r.skew)).multiply(r.scale);for(var i,o=this.pdf.getTextDimensions(t.text),s=this.ctx.transform.applyToRectangle(new I(t.x,t.y,o.w,o.h)),a=n.applyToRectangle(new I(t.x,t.y-o.h,o.w,o.h)),c=E.call(this,a),u=[],l=0;l<c.length;l+=1)-1===u.indexOf(c[l])&&u.push(c[l]);if(u.sort(),!0===this.autoPaging)for(var h=u[0],f=u[u.length-1],d=h;d<f+1;d++){if(this.pdf.setPage(d),0!==this.ctx.clip_path.length){var p=this.path;i=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=N(i,this.posX,-1*this.pdf.internal.pageSize.height*(d-1)+this.posY),L.call(this,"fill",!0),this.path=p}var g=JSON.parse(JSON.stringify(s));if(g=N([g],this.posX,-1*this.pdf.internal.pageSize.height*(d-1)+this.posY)[0],.01<=t.scale){var B=this.pdf.internal.getFontSize();this.pdf.setFontSize(B*t.scale)}this.pdf.text(t.text,g.x,g.y,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),.01<=t.scale&&this.pdf.setFontSize(B)}else{if(.01<=t.scale){B=this.pdf.internal.getFontSize();this.pdf.setFontSize(B*t.scale)}this.pdf.text(t.text,A.x+this.posX,A.y+this.posY,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),.01<=t.scale&&this.pdf.setFontSize(B)}},H=function(t,e,A,r){A=A||0,r=r||0,this.pdf.internal.out(n(t+A)+" "+i(e+r)+" l")},S=function(t,e,A){return this.pdf.lines(t,e,A,null,null)},d=function(t,e,A,r,n,i,o,s){this.pdf.internal.out([a(c(A+t)),a(u(r+e)),a(c(n+t)),a(u(i+e)),a(c(o+t)),a(u(s+e)),"c"].join(" "))},p=function(t,e,A,r){var n=2*Math.PI,i=e;(i<n||n<i)&&(i%=n);var o=A;(o<n||n<o)&&(o%=n);for(var s=[],a=Math.PI/2,c=r?-1:1,u=e,l=Math.min(n,Math.abs(o-i));1e-5<l;){var h=u+c*Math.min(l,a);s.push(g.call(this,t,u,h)),l-=Math.abs(h-u),u=h}return s},g=function(t,e,A){var r=(A-e)/2,n=t*Math.cos(r),i=t*Math.sin(r),o=n,s=-i,a=o*o+s*s,c=a+o*n+s*i,u=4/3*(Math.sqrt(2*a*c)-c)/(o*i-s*n),l=o-u*s,h=s+u*o,f=l,d=-h,p=r+e,g=Math.cos(p),B=Math.sin(p);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:l*g-h*B,y2:l*B+h*g,x3:f*g-d*B,y3:f*B+d*g,x4:t*Math.cos(A),y4:t*Math.sin(A)}},x=function(t){return 180*t/Math.PI},B=function(t){return t*Math.PI/180},m=function(t,e,A,r,n,i){var o=t+.5*(A-t),s=e+.5*(r-e),a=n+.5*(A-n),c=i+.5*(r-i),u=Math.min(t,n,o,a),l=Math.max(t,n,o,a),h=Math.min(e,i,s,c),f=Math.max(e,i,s,c);return new I(u,h,l-u,f-h)},v=function(t,e,A,r,n,i,o,s){for(var a,c,u,l,h,f,d,p,g,B,w,m,v,y=A-t,C=r-e,Q=n-A,F=i-r,b=o-n,U=s-i,E=0;E<41;E++)p=(f=(c=t+(a=E/40)*y)+a*((l=A+a*Q)-c))+a*(l+a*(n+a*b-l)-f),g=(d=(u=e+a*C)+a*((h=r+a*F)-u))+a*(h+a*(i+a*U-h)-d),v=0==E?(m=B=p,w=g):(B=Math.min(B,p),w=Math.min(w,g),m=Math.max(m,p),Math.max(v,g));return new I(Math.round(B),Math.round(w),Math.round(m-B),Math.round(v-w))},_=function(t,e){var A=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return A},set:function(t){isNaN(t)||(A=parseFloat(t))}});var r=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var n="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return n},set:function(t){n=t.toString()}}),this},I=function(t,e,A,r){_.call(this,t,e),this.type="rect";var n=A||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var i=r||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}}),this},T=function(t,e,A,r,n,i){var o=[];return Object.defineProperty(this,"sx",{get:function(){return o[0]},set:function(t){o[0]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"shy",{get:function(){return o[1]},set:function(t){o[1]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"shx",{get:function(){return o[2]},set:function(t){o[2]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"sy",{get:function(){return o[3]},set:function(t){o[3]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"tx",{get:function(){return o[4]},set:function(t){o[4]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"ty",{get:function(){return o[5]},set:function(t){o[5]=Math.round(1e5*t)/1e5}}),Object.defineProperty(this,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(this,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(this,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(this,"isIdentity",{get:function(){return 1===this.sx&&(0===this.shy&&(0===this.shx&&(1===this.sy&&(0===this.tx&&0===this.ty))))}}),this.sx=isNaN(t)?1:t,this.shy=isNaN(e)?0:e,this.shx=isNaN(A)?0:A,this.sy=isNaN(r)?1:r,this.tx=isNaN(n)?0:n,this.ty=isNaN(i)?0:i,this};T.prototype.multiply=function(t){var e=t.sx*this.sx+t.shy*this.shx,A=t.sx*this.shy+t.shy*this.sy,r=t.shx*this.sx+t.sy*this.shx,n=t.shx*this.shy+t.sy*this.sy,i=t.tx*this.sx+t.ty*this.shx+this.tx,o=t.tx*this.shy+t.ty*this.sy+this.ty;return new T(e,A,r,n,i,o)},T.prototype.decompose=function(){var t=this.sx,e=this.shy,A=this.shx,r=this.sy,n=this.tx,i=this.ty,o=Math.sqrt(t*t+e*e),s=(t/=o)*A+(e/=o)*r;A-=t*s,r-=e*s;var a=Math.sqrt(A*A+r*r);return s/=a,t*(r/=a)<e*(A/=a)&&(t=-t,e=-e,s=-s,o=-o),{scale:new T(o,0,0,a,0,0),translate:new T(1,0,0,1,n,i),rotate:new T(t,e,-e,t,0,0),skew:new T(1,0,s,1,0,0)}},T.prototype.applyToPoint=function(t){var e=t.x*this.sx+t.y*this.shx+this.tx,A=t.x*this.shy+t.y*this.sy+this.ty;return new _(e,A)},T.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),A=this.applyToPoint(new _(t.x+t.w,t.y+t.h));return new I(e.x,e.y,A.x-e.x,A.y-e.y)},T.prototype.clone=function(){var t=this.sx,e=this.shy,A=this.shx,r=this.sy,n=this.tx,i=this.ty;return new T(t,e,A,r,n,i)}}(at.API,"undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")()),o=at.API,i=function(t){var r,e,A,n,i,o,s,a,c,u;for(/[^\x00-\xFF]/.test(t),e=[],A=0,n=(t+=r="\0\0\0\0".slice(t.length%4||4)).length;A<n;A+=4)0!==(i=(t.charCodeAt(A)<<24)+(t.charCodeAt(A+1)<<16)+(t.charCodeAt(A+2)<<8)+t.charCodeAt(A+3))?(o=(i=((i=((i=((i=(i-(u=i%85))/85)-(c=i%85))/85)-(a=i%85))/85)-(s=i%85))/85)%85,e.push(o+33,s+33,a+33,c+33,u+33)):e.push(122);return function(t,e){for(var A=r.length;0<A;A--)t.pop()}(e),String.fromCharCode.apply(String,e)+"~>"},s=function(t){var r,e,A,n,i,o=String,s="length",a="charCodeAt",c="slice",u="replace";for(t[c](-2),t=t[c](0,-2)[u](/\s/g,"")[u]("z","!!!!!"),A=[],n=0,i=(t+=r="uuuuu"[c](t[s]%5||5))[s];n<i;n+=5)e=52200625*(t[a](n)-33)+614125*(t[a](n+1)-33)+7225*(t[a](n+2)-33)+85*(t[a](n+3)-33)+(t[a](n+4)-33),A.push(255&e>>24,255&e>>16,255&e>>8,255&e);return function(t,e){for(var A=r[s];0<A;A--)t.pop()}(A),o.fromCharCode.apply(o,A)},c=function(t){for(var e="",A=0;A<t.length;A+=1)e+=("0"+t.charCodeAt(A).toString(16)).slice(-2);return e+=">"},u=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var A="",r=0;r<t.length;r+=2)A+=String.fromCharCode("0x"+(t[r]+t[r+1]));return A},l=function(t,e){e=Object.assign({predictor:1,colors:1,bitsPerComponent:8,columns:1},e);for(var A,r,n=[],i=t.length;i--;)n[i]=t.charCodeAt(i);return A=o.adler32cs.from(t),(r=new Deflater(6)).append(new Uint8Array(n)),t=r.flush(),(n=new Uint8Array(t.length+6)).set(new Uint8Array([120,156])),n.set(t,2),n.set(new Uint8Array([255&A,A>>8&255,A>>16&255,A>>24&255]),t.length+2),t=String.fromCharCode.apply(null,n)},o.processDataByFilters=function(t,e){var A=0,r=t||"",n=[];for("string"==typeof(e=e||[])&&(e=[e]),A=0;A<e.length;A+=1)switch(e[A]){case"ASCII85Decode":case"/ASCII85Decode":r=s(r),n.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":r=i(r),n.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":r=u(r),n.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":r=c(r),n.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":r=l(r),n.push("/FlateDecode");break;default:throw'The filter: "'+e[A]+'" is not implemented'}return{data:r,reverseChain:n.reverse().join(" ")}},(r=at.API).loadFile=function(t,e,A){var r;e=e||!0,A=A||function(){};try{r=function(t,e,A){var r=new XMLHttpRequest,n=[],i=0,o=function(t){var e=t.length,A=String.fromCharCode;for(i=0;i<e;i+=1)n.push(A(255&t.charCodeAt(i)));return n.join("")};if(r.open("GET",t,!e),r.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(r.onload=function(){return o(this.responseText)}),r.send(null),200===r.status)return e?o(r.responseText):void 0;console.warn('Unable to load file "'+t+'"')}(t,e)}catch(t){r=void 0}return r},r.loadImageFile=r.loadFile,n=at.API,h="undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt,p=function(t){var e=se(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},g=function(t,e){var A=document.createElement(t);if(e.className&&(A.className=e.className),e.innerHTML){A.innerHTML=e.innerHTML;for(var r=A.getElementsByTagName("script"),n=r.length;0<n--;null)r[n].parentNode.removeChild(r[n])}for(var i in e.style)A.style[i]=e.style[i];return A},(((B=function t(e){var A=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(Promise.resolve(),A);return r=(r=r.setProgress(1,t,1,[t])).set(e)}).prototype=Object.create(Promise.prototype)).constructor=B).convert=function(t,e){return t.__proto__=e||B.prototype,t},B.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{}}},B.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(p(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.set({src:g("div",{innerHTML:t})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}})},B.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},B.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t={position:"relative",display:"inline-block",width:Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:"white"},e=function t(e,A){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),n=e.firstChild;n;n=n.nextSibling)!0!==A&&1===n.nodeType&&"SCRIPT"===n.nodeName||r.appendChild(t(n,A));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft},!0)),r}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=g("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=g("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(g("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},B.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(function(){var t=Object.assign({},this.opt.html2canvas);if(delete t.onrendered,this.isHtml2CanvasLoaded())return html2canvas(this.prop.container,t)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},B.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(function(){var t=this.opt.jsPDF,e=Object.assign({async:!0,allowTaint:!0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete e.onrendered,t.context2d.autoPaging=!0,t.context2d.posX=this.opt.x,t.context2d.posY=this.opt.y,e.windowHeight=e.windowHeight||0,e.windowHeight=0==e.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):e.windowHeight,this.isHtml2CanvasLoaded())return html2canvas(this.prop.container,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)})},B.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},B.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},B.prototype.output=function(t,e,A){return"img"===(A=A||"pdf").toLowerCase()||"image"===A.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},B.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},B.prototype.outputImg=function(t,e){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},B.prototype.isHtml2CanvasLoaded=function(){var t=void 0!==h.html2canvas;return t||console.error("html2canvas not loaded."),t},B.prototype.save=function(t){if(this.isHtml2CanvasLoaded())return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},B.prototype.doCallback=function(t){if(this.isHtml2CanvasLoaded())return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},B.prototype.set=function(e){if("object"!==p(e))return this;var t=Object.keys(e||{}).map(function(t){if(t in B.template.prop)return function(){this.prop[t]=e[t]};switch(t){case"margin":return this.setMargin.bind(this,e.margin);case"jsPDF":return function(){return this.opt.jsPDF=e.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,e.pageSize);default:return function(){this.opt[t]=e[t]}}},this);return this.then(function(){return this.thenList(t)})},B.prototype.get=function(e,A){return this.then(function(){var t=e in B.template.prop?this.prop[e]:this.opt[e];return A?A(t):t})},B.prototype.setMargin=function(t){return this.then(function(){switch(p(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},B.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then(function(){(t=t||at.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},B.prototype.setProgress=function(t,e,A,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=A&&(this.progress.n=A),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},B.prototype.updateProgress=function(t,e,A,r){return this.setProgress(t?this.progress.val+t:null,e||null,A?this.progress.n+A:null,r?this.progress.stack.concat(r):null)},B.prototype.then=function(t,e){var A=this;return this.thenCore(t,e,function(e,t){return A.updateProgress(null,null,1,[e]),Promise.prototype.then.call(this,function(t){return A.updateProgress(null,e),t}).then(e,t).then(function(t){return A.updateProgress(1),t})})},B.prototype.thenCore=function(t,e,A){A=A||Promise.prototype.then;var r=this;t&&(t=t.bind(r)),e&&(e=e.bind(r));var n=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?r:B.convert(Object.assign({},r),Promise.prototype),i=A.call(n,t,e);return B.convert(i,r.__proto__)},B.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},B.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},B.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return B.convert(e,this)},B.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},B.prototype.error=function(t){return this.then(function(){throw new Error(t)})},B.prototype.using=B.prototype.set,B.prototype.saveAs=B.prototype.save,B.prototype.export=B.prototype.output,B.prototype.run=B.prototype.then,at.getPageSize=function(t,e,A){if("object"===se(t)){var r=t;t=r.orientation,e=r.unit||e,A=r.format||A}e=e||"mm",A=A||"a4",t=(""+(t||"P")).toLowerCase();var n=(""+A).toLowerCase(),i={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":var o=1;break;case"mm":o=72/25.4;break;case"cm":o=72/2.54;break;case"in":o=72;break;case"px":o=.75;break;case"pc":case"em":o=12;break;case"ex":o=6;break;default:throw"Invalid unit: "+e}if(i.hasOwnProperty(n))var s=i[n][1]/o,a=i[n][0]/o;else try{s=A[1],a=A[0]}catch(t){throw new Error("Invalid format: "+A)}if("p"===t||"portrait"===t){if(t="p",s<a){var c=a;a=s,s=c}}else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",a<s&&(c=a,a=s,s=c)}return{width:a,height:s,unit:e,k:o}},n.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.jsPDF;var A=new B(e);return e.worker?A:A.from(t).doCallback()},at.API.addJS=function(t){return v=t,this.internal.events.subscribe("postPutResources",function(t){w=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(w+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),m=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+v+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){void 0!==w&&void 0!==m&&this.internal.out("/Names <</JavaScript "+w+" 0 R>>")}),this},(y=at.API).events.push(["postPutResources",function(){var t=this,e=/^(\d+) 0 obj$/;if(0<this.outline.root.children.length)for(var A=t.outline.render().split(/\r\n/),r=0;r<A.length;r++){var n=A[r],i=e.exec(n);if(null!=i){var o=i[1];t.internal.newObjectDeferredBegin(o,!1)}t.internal.write(n)}if(this.outline.createNamedDestinations){var s=this.internal.pages.length,a=[];for(r=0;r<s;r++){var c=t.internal.newObject();a.push(c);var u=t.internal.getPageInfo(r+1);t.internal.write("<< /D["+u.objId+" 0 R /XYZ null null null]>> endobj")}var l=t.internal.newObject();for(t.internal.write("<< /Names [ "),r=0;r<a.length;r++)t.internal.write("(page_"+(r+1)+")"+a[r]+" 0 R");t.internal.write(" ] >>","endobj"),t.internal.newObject(),t.internal.write("<< /Dests "+l+" 0 R"),t.internal.write(">>","endobj")}}]),y.events.push(["putCatalog",function(){0<this.outline.root.children.length&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+namesOid+" 0 R"))}]),y.events.push(["initialized",function(){var o=this;o.outline={createNamedDestinations:!1,root:{children:[]}},o.outline.add=function(t,e,A){var r={title:e,options:A,children:[]};return null==t&&(t=this.root),t.children.push(r),r},o.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=o,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},o.outline.genIds_r=function(t){t.id=o.internal.newObjectDeferred();for(var e=0;e<t.children.length;e++)this.genIds_r(t.children[e])},o.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),0<t.children.length&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},o.outline.renderItems=function(t){this.ctx.pdf.internal.getCoordinateString;for(var e=this.ctx.pdf.internal.getVerticalCoordinateString,A=0;A<t.children.length;A++){var r=t.children[A];this.objStart(r),this.line("/Title "+this.makeString(r.title)),this.line("/Parent "+this.makeRef(t)),0<A&&this.line("/Prev "+this.makeRef(t.children[A-1])),A<t.children.length-1&&this.line("/Next "+this.makeRef(t.children[A+1])),0<r.children.length&&(this.line("/First "+this.makeRef(r.children[0])),this.line("/Last "+this.makeRef(r.children[r.children.length-1])));var n=this.count=this.count_r({count:0},r);if(0<n&&this.line("/Count "+n),r.options&&r.options.pageNumber){var i=o.internal.getPageInfo(r.options.pageNumber);this.line("/Dest ["+i.objId+" 0 R /XYZ 0 "+e(0)+" 0]")}this.objEnd()}for(A=0;A<t.children.length;A++)r=t.children[A],this.renderItems(r)},o.outline.line=function(t){this.ctx.val+=t+"\r\n"},o.outline.makeRef=function(t){return t.id+" 0 R"},o.outline.makeString=function(t){return"("+o.internal.pdfEscape(t)+")"},o.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},o.outline.objEnd=function(t){this.ctx.val+=">> \r\nendobj\r\n"},o.outline.count_r=function(t,e){for(var A=0;A<e.children.length;A++)t.count++,this.count_r(t,e.children[A]);return t.count}}]),H=at.API,S=function(){var t="function"==typeof Deflater;if(!t)throw new Error("requires deflate.js for compression");return t},x=function(t,e,A,r){var n=5,i=I;switch(r){case H.image_compression.FAST:n=3,i=_;break;case H.image_compression.MEDIUM:n=6,i=T;break;case H.image_compression.SLOW:n=9,i=O}t=F(t,e,A,i);var o=new Uint8Array(C(n)),s=Q(t),a=new Deflater(n),c=a.append(t),u=a.flush(),l=o.length+c.length+u.length,h=new Uint8Array(l+4);return h.set(o),h.set(c,o.length),h.set(u,o.length+c.length),h[l++]=s>>>24&255,h[l++]=s>>>16&255,h[l++]=s>>>8&255,h[l++]=255&s,H.arrayBufferToBinaryString(h)},C=function(t,e){var A=Math.LOG2E*Math.log(32768)-8<<4|8,r=A<<8;return r|=Math.min(3,(e-1&255)>>1)<<6,r|=0,[A,255&(r+=31-r%31)]},Q=function(t,e){for(var A,r=1,n=0,i=t.length,o=0;0<i;){for(i-=A=e<i?e:i;n+=r+=t[o++],--A;);r%=65521,n%=65521}return(n<<16|r)>>>0},F=function(t,e,A,r){for(var n,i,o,s=t.length/e,a=new Uint8Array(t.length+s),c=P(),u=0;u<s;u++){if(o=u*e,n=t.subarray(o,o+e),r)a.set(r(n,A,i),o+u);else{for(var l=0,h=c.length,f=[];l<h;l++)f[l]=c[l](n,A,i);var d=M(f.concat());a.set(f[d],o+u)}i=n}return a},b=function(t,e,A){var r=Array.apply([],t);return r.unshift(0),r},_=function(t,e,A){var r,n=[],i=0,o=t.length;for(n[0]=1;i<o;i++)r=t[i-e]||0,n[i+1]=t[i]-r+256&255;return n},I=function(t,e,A){var r,n=[],i=0,o=t.length;for(n[0]=2;i<o;i++)r=A&&A[i]||0,n[i+1]=t[i]-r+256&255;return n},T=function(t,e,A){var r,n,i=[],o=0,s=t.length;for(i[0]=3;o<s;o++)r=t[o-e]||0,n=A&&A[o]||0,i[o+1]=t[o]+256-(r+n>>>1)&255;return i},O=function(t,e,A){var r,n,i,o,s=[],a=0,c=t.length;for(s[0]=4;a<c;a++)r=t[a-e]||0,n=A&&A[a]||0,i=A&&A[a-e]||0,o=R(r,n,i),s[a+1]=t[a]-o+256&255;return s},R=function(t,e,A){var r=t+e-A,n=Math.abs(r-t),i=Math.abs(r-e),o=Math.abs(r-A);return n<=i&&n<=o?t:i<=o?e:A},P=function(){return[b,_,I,T,O]},M=function(t){for(var e,A,r,n=0,i=t.length;n<i;)((e=K(t[n].slice(1)))<A||!A)&&(A=e,r=n),n++;return r},K=function(t){for(var e=0,A=t.length,r=0;e<A;)r+=Math.abs(t[e++]);return r},H.processPNG=function(t,e,A,r,n){var i,o,s,a,c,u,l=this.color_spaces.DEVICE_RGB,h=this.decode.FLATE_DECODE,f=8;if(this.isArrayBuffer(t)&&(t=new Uint8Array(t)),this.isArrayBufferView(t)){if("function"!=typeof PNG||"function"!=typeof Lt)throw new Error("PNG support requires png.js and zlib.js");if(t=(i=new PNG(t)).imgData,f=i.bits,l=i.colorSpace,a=i.colors,-1!==[4,6].indexOf(i.colorType)){if(8===i.bits)for(var d,p=(U=32==i.pixelBitlength?new Uint32Array(i.decodePixels().buffer):16==i.pixelBitlength?new Uint16Array(i.decodePixels().buffer):new Uint8Array(i.decodePixels().buffer)).length,g=new Uint8Array(p*i.colors),B=new Uint8Array(p),w=i.pixelBitlength-i.bits,m=0,v=0;m<p;m++){for(y=U[m],d=0;d<w;)g[v++]=y>>>d&255,d+=i.bits;B[m]=y>>>d&255}if(16===i.bits){p=(U=new Uint32Array(i.decodePixels().buffer)).length,g=new Uint8Array(p*(32/i.pixelBitlength)*i.colors),B=new Uint8Array(p*(32/i.pixelBitlength));for(var y,C=1<i.colors,Q=v=m=0;m<p;)y=U[m++],g[v++]=y>>>0&255,C&&(g[v++]=y>>>16&255,y=U[m++],g[v++]=y>>>0&255),B[Q++]=y>>>16&255;f=8}r!==H.image_compression.NONE&&S()?(t=x(g,i.width*i.colors,i.colors,r),u=x(B,i.width,1,r)):(t=g,u=B,h=null)}if(3===i.colorType&&(l=this.color_spaces.INDEXED,c=i.palette,i.transparency.indexed)){var F=i.transparency.indexed,b=0;for(m=0,p=F.length;m<p;++m)b+=F[m];if((b/=255)==p-1&&-1!==F.indexOf(0))s=[F.indexOf(0)];else if(b!==p){var U=i.decodePixels();for(B=new Uint8Array(U.length),m=0,p=U.length;m<p;m++)B[m]=F[U[m]];u=x(B,i.width,1)}}var E=function(t){var e;switch(t){case H.image_compression.FAST:e=11;break;case H.image_compression.MEDIUM:e=13;break;case H.image_compression.SLOW:e=14;break;default:e=12}return e}(r);return o=h===this.decode.FLATE_DECODE?"/Predictor "+E+" /Colors "+a+" /BitsPerComponent "+f+" /Columns "+i.width:"/Colors "+a+" /BitsPerComponent "+f+" /Columns "+i.width,(this.isArrayBuffer(t)||this.isArrayBufferView(t))&&(t=this.arrayBufferToBinaryString(t)),(u&&this.isArrayBuffer(u)||this.isArrayBufferView(u))&&(u=this.arrayBufferToBinaryString(u)),this.createImageInfo(t,i.width,i.height,l,f,h,e,A,o,s,c,u,E)}throw new Error("Unsupported PNG image data, try using JPEG instead.")},(D=at.API).processGIF89A=function(t,e,A,r,n){var i=new Ft(t),o=i.width,s=i.height,a=[];i.decodeAndBlitFrameRGBA(0,a);var c={data:a,width:o,height:s},u=new Ut(100).encode(c,100);return D.processJPEG.call(this,u,e,A,r)},D.processGIF87A=D.processGIF89A,(k=at.API).processBMP=function(t,e,A,r,n){var i=new Et(t,!1),o=i.width,s=i.height,a={data:i.getData(),width:o,height:s},c=new Ut(100).encode(a,100);return k.processJPEG.call(this,c,e,A,r)},at.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!=={af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"FYRO Macedonian",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},z=at.API,j=z.getCharWidthsArray=function(t,e){var A,r,n,i=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),s=e.charSpace||this.internal.getCharSpace(),a=e.widths?e.widths:i.metadata.Unicode.widths,c=a.fof?a.fof:1,u=e.kerning?e.kerning:i.metadata.Unicode.kerning,l=u.fof?u.fof:1,h=0,f=a[0]||c,d=[];for(A=0,r=t.length;A<r;A++)n=t.charCodeAt(A),"function"==typeof i.metadata.widthOfString?d.push((i.metadata.widthOfGlyph(i.metadata.characterToGlyph(n))+s*(1e3/o)||0)/1e3):d.push((a[n]||f)/c+(u[n]&&u[n][h]||0)/l),h=n;return d},q=z.getArraySum=function(t){for(var e=t.length,A=0;e;)A+=t[--e];return A},V=z.getStringUnitWidth=function(t,e){var A=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),n=e.charSpace||this.internal.getCharSpace();return"function"==typeof r.metadata.widthOfString?r.metadata.widthOfString(t,A,n)/A:q(j.apply(this,arguments))},X=function(t,e,A,r){for(var n=[],i=0,o=t.length,s=0;i!==o&&s+e[i]<A;)s+=e[i],i++;n.push(t.slice(0,i));var a=i;for(s=0;i!==o;)s+e[i]>r&&(n.push(t.slice(a,i)),s=0,a=i),s+=e[i],i++;return a!==i&&n.push(t.slice(a,i)),n},G=function(t,e,A){A||(A={});var r,n,i,o,s,a,c=[],u=[c],l=A.textIndent||0,h=0,f=0,d=t.split(" "),p=j.apply(this,[" ",A])[0];if(a=-1===A.lineIndent?d[0].length+2:A.lineIndent||0){var g=Array(a).join(" "),B=[];d.map(function(t){1<(t=t.split(/\s*\n/)).length?B=B.concat(t.map(function(t,e){return(e&&t.length?"\n":"")+t})):B.push(t[0])}),d=B,a=V.apply(this,[g,A])}for(i=0,o=d.length;i<o;i++){var w=0;if(r=d[i],a&&"\n"==r[0]&&(r=r.substr(1),w=1),n=j.apply(this,[r,A]),e<l+h+(f=q(n))||w){if(e<f){for(s=X.apply(this,[r,n,e-(l+h),e]),c.push(s.shift()),c=[s.pop()];s.length;)u.push([s.shift()]);f=q(n.slice(r.length-(c[0]?c[0].length:0)))}else c=[r];u.push(c),l=f+a,h=p}else c.push(r),l+=h+f,h=p}if(a)var m=function(t,e){return(e?g:"")+t.join(" ")};else m=function(t){return t.join(" ")};return u.map(m)},z.splitTextToSize=function(t,e,A){var r,n=(A=A||{}).fontSize||this.internal.getFontSize(),i=function(t){var e={0:1},A={};if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var r=this.internal.getFont(t.fontName,t.fontStyle),n="Unicode";return r.metadata[n]?{widths:r.metadata[n].widths||e,kerning:r.metadata[n].kerning||A}:{font:r.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}.call(this,A);r=Array.isArray(t)?t:t.split(/\r?\n/);var o=1*this.internal.scaleFactor*e/n;i.textIndent=A.textIndent?1*A.textIndent*this.internal.scaleFactor/n:0,i.lineIndent=A.lineIndent;var s,a,c=[];for(s=0,a=r.length;s<a;s++)c=c.concat(G.apply(this,[r[s],o,i]));return c},J=at.API,Y={codePages:["WinAnsiEncoding"],WinAnsiEncoding:(W=function(t){for(var e="klmnopqrstuvwxyz",A={},r=0;r<e.length;r++)A[e[r]]="0123456789abcdef"[r];var n,i,o,s,a,c={},u=1,l=c,h=[],f="",d="",p=t.length-1;for(r=1;r!=p;)a=t[r],r+=1,"'"==a?i=i?(s=i.join(""),n):[]:i?i.push(a):"{"==a?(h.push([l,s]),l={},s=n):"}"==a?((o=h.pop())[0][o[1]]=l,s=n,l=o[0]):"-"==a?u=-1:s===n?A.hasOwnProperty(a)?(f+=A[a],s=parseInt(f,16)*u,u=1,f=""):f+=a:A.hasOwnProperty(a)?(d+=A[a],l[s]=parseInt(d,16)*u,u=1,s=n,d=""):d+=a;return c})("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},Z={Unicode:{Courier:Y,"Courier-Bold":Y,"Courier-BoldOblique":Y,"Courier-Oblique":Y,Helvetica:Y,"Helvetica-Bold":Y,"Helvetica-BoldOblique":Y,"Helvetica-Oblique":Y,"Times-Roman":Y,"Times-Bold":Y,"Times-BoldItalic":Y,"Times-Italic":Y}},$={Unicode:{"Courier-Oblique":W("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":W("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":W("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:W("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":W("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":W("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:W("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:W("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":W("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:W("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":W("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":W("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":W("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":W("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}},J.events.push(["addFont",function(t){var e,A,r,n=t.font,i="Unicode";(e=$[i][n.postScriptName])&&((A=n.metadata[i]?n.metadata[i]:n.metadata[i]={}).widths=e.widths,A.kerning=e.kerning),(r=Z[i][n.postScriptName])&&((A=n.metadata[i]?n.metadata[i]:n.metadata[i]={}).encoding=r).codePages&&r.codePages.length&&(n.encoding=r.codePages[0])}]),tt=at,"undefined"!=typeof self&&self||"undefined"!=typeof xt&&xt||"undefined"!=typeof window&&window||Function("return this")(),tt.API.events.push(["addFont",function(t){var e=t.font,A=t.instance;if(void 0!==A&&A.existsFileInVFS(e.postScriptName)){var r=A.getFileFromVFS(e.postScriptName);if("string"!=typeof r)throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+e.postScriptName+"').");e.metadata=tt.API.TTFFont.open(e.postScriptName,e.fontName,r,e.encoding),e.metadata.Unicode=e.metadata.Unicode||{encoding:{},kerning:{},widths:[]},e.metadata.glyIdsUsed=[0]}else if(!1===e.isStandardFont)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+e.postScriptName+"').")}]),(et=at.API).addSvg=function(t,e,A,r,n){if(void 0===e||void 0===A)throw new Error("addSVG needs values for 'x' and 'y'");function i(t){for(var e=parseFloat(t[1]),A=parseFloat(t[2]),r=[],n=3,i=t.length;n<i;)"c"===t[n]?(r.push([parseFloat(t[n+1]),parseFloat(t[n+2]),parseFloat(t[n+3]),parseFloat(t[n+4]),parseFloat(t[n+5]),parseFloat(t[n+6])]),n+=7):"l"===t[n]?(r.push([parseFloat(t[n+1]),parseFloat(t[n+2])]),n+=3):n+=1;return[e,A,r]}var o,s,a,c,u,l,h,f,d=(c=document,f=c.createElement("iframe"),u=".jsPDF_sillysvg_iframe {display:none;position:absolute;}",(h=(l=c).createElement("style")).type="text/css",h.styleSheet?h.styleSheet.cssText=u:h.appendChild(l.createTextNode(u)),l.getElementsByTagName("head")[0].appendChild(h),f.name="childframe",f.setAttribute("width",0),f.setAttribute("height",0),f.setAttribute("frameborder","0"),f.setAttribute("scrolling","no"),f.setAttribute("seamless","seamless"),f.setAttribute("class","jsPDF_sillysvg_iframe"),c.body.appendChild(f),f),p=(o=t,(a=((s=d).contentWindow||s.contentDocument).document).write(o),a.close(),a.getElementsByTagName("svg")[0]),g=[1,1],B=parseFloat(p.getAttribute("width")),w=parseFloat(p.getAttribute("height"));B&&w&&(r&&n?g=[r/B,n/w]:r?g=[r/B,r/B]:n&&(g=[n/w,n/w]));var m,v,y,C,Q=p.childNodes;for(m=0,v=Q.length;m<v;m++)(y=Q[m]).tagName&&"PATH"===y.tagName.toUpperCase()&&((C=i(y.getAttribute("d").split(" ")))[0]=C[0]*g[0]+e,C[1]=C[1]*g[1]+A,this.lines.call(this,C[2],C[0],C[1],g));return this},et.addSVG=et.addSvg,et.addSvgAsImage=function(t,e,A,r,n,i,o,s){if(isNaN(e)||isNaN(A))throw console.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(r)||isNaN(n))throw console.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var a=document.createElement("canvas");a.width=r,a.height=n;var c=a.getContext("2d");return c.fillStyle="#fff",c.fillRect(0,0,a.width,a.height),canvg(a,t,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0}),this.addImage(a.toDataURL("image/jpeg",1),e,A,r,n,o,s),this},at.API.putTotalPages=function(t){var e,A=0;A=parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var n=0;n<this.internal.pages[r].length;n++)this.internal.pages[r][n]=this.internal.pages[r][n].replace(e,A);return this},at.API.viewerPreferences=function(t,e){var A;t=t||{},e=e||!1;var r,n,i={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},o=Object.keys(i),s=[],a=0,c=0,u=0,l=!0;function h(t,e){var A,r=!1;for(A=0;A<t.length;A+=1)t[A]===e&&(r=!0);return r}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(i)),this.internal.viewerpreferences.isSubscribed=!1),A=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var f=o.length;for(u=0;u<f;u+=1)A[o[u]].value=A[o[u]].defaultValue,A[o[u]].explicitSet=!1}if("object"===se(t))for(r in t)if(n=t[r],h(o,r)&&void 0!==n){if("boolean"===A[r].type&&"boolean"==typeof n)A[r].value=n;else if("name"===A[r].type&&h(A[r].valueSet,n))A[r].value=n;else if("integer"===A[r].type&&Number.isInteger(n))A[r].value=n;else if("array"===A[r].type){for(a=0;a<n.length;a+=1)if(l=!0,1===n[a].length&&"number"==typeof n[a][0])s.push(String(n[a]-1));else if(1<n[a].length){for(c=0;c<n[a].length;c+=1)"number"!=typeof n[a][c]&&(l=!1);!0===l&&s.push([n[a][0]-1,n[a][1]-1].join(" "))}A[r].value="["+s.join(" ")+"]"}else A[r].value=A[r].defaultValue;A[r].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",function(){var t,e=[];for(t in A)!0===A[t].explicitSet&&("name"===A[t].type?e.push("/"+t+" /"+A[t].value):e.push("/"+t+" "+A[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=A,this},At=at.API,it=nt=rt="",At.addMetadata=function(t,e){return nt=e||"http://jspdf.default.namespaceuri/",rt=t,this.internal.events.subscribe("postPutResources",function(){if(rt){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+nt+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),A=unescape(encodeURIComponent(t)),r=unescape(encodeURIComponent(rt)),n=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),i=unescape(encodeURIComponent("</x:xmpmeta>")),o=A.length+r.length+n.length+e.length+i.length;it=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+o+" >>"),this.internal.write("stream"),this.internal.write(e+A+r+n+i),this.internal.write("endstream"),this.internal.write("endobj")}else it=""}),this.internal.events.subscribe("putCatalog",function(){it&&this.internal.write("/Metadata "+it+" 0 R")}),this},function(h,t){var e=h.API;var g=e.pdfEscape16=function(t,e){for(var A,r=e.metadata.Unicode.widths,n=["","0","00","000","0000"],i=[""],o=0,s=t.length;o<s;++o){if(A=e.metadata.characterToGlyph(t.charCodeAt(o)),e.metadata.glyIdsUsed.push(A),e.metadata.toUnicode[A]=t.charCodeAt(o),-1==r.indexOf(A)&&(r.push(A),r.push([parseInt(e.metadata.widthOfGlyph(A),10)])),"0"==A)return i.join("");A=A.toString(16),i.push(n[4-A.length],A)}return i.join("")},f=function(t){var e,A,r,n,i,o,s;for(i="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",r=[],o=0,s=(A=Object.keys(t).sort(function(t,e){return t-e})).length;o<s;o++)e=A[o],100<=r.length&&(i+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),n=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),r.push("<"+e+"><"+n+">");return r.length&&(i+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),i+="endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(t){!function(t,e,A,r){if(t.metadata instanceof h.API.TTFFont&&"Identity-H"===t.encoding){for(var n=t.metadata.Unicode.widths,i=t.metadata.subset.encode(t.metadata.glyIdsUsed,1),o="",s=0;s<i.length;s++)o+=String.fromCharCode(i[s]);var a=A();r({data:o,addLength1:!0}),e("endobj");var c=A();r({data:f(t.metadata.toUnicode),addLength1:!0}),e("endobj");var u=A();e("<<"),e("/Type /FontDescriptor"),e("/FontName /"+t.fontName),e("/FontFile2 "+a+" 0 R"),e("/FontBBox "+h.API.PDFObject.convert(t.metadata.bbox)),e("/Flags "+t.metadata.flags),e("/StemV "+t.metadata.stemV),e("/ItalicAngle "+t.metadata.italicAngle),e("/Ascent "+t.metadata.ascender),e("/Descent "+t.metadata.decender),e("/CapHeight "+t.metadata.capHeight),e(">>"),e("endobj");var l=A();e("<<"),e("/Type /Font"),e("/BaseFont /"+t.fontName),e("/FontDescriptor "+u+" 0 R"),e("/W "+h.API.PDFObject.convert(n)),e("/CIDToGIDMap /Identity"),e("/DW 1000"),e("/Subtype /CIDFontType2"),e("/CIDSystemInfo"),e("<<"),e("/Supplement 0"),e("/Registry (Adobe)"),e("/Ordering ("+t.encoding+")"),e(">>"),e(">>"),e("endobj"),t.objectNumber=A(),e("<<"),e("/Type /Font"),e("/Subtype /Type0"),e("/ToUnicode "+c+" 0 R"),e("/BaseFont /"+t.fontName),e("/Encoding /"+t.encoding),e("/DescendantFonts ["+l+" 0 R]"),e(">>"),e("endobj"),t.isAlreadyPutted=!0}}(t.font,t.out,t.newObject,t.putStream)}]);e.events.push(["putFont",function(t){!function(t,e,A,r){if(t.metadata instanceof h.API.TTFFont&&"WinAnsiEncoding"===t.encoding){t.metadata.Unicode.widths;for(var n=t.metadata.rawData,i="",o=0;o<n.length;o++)i+=String.fromCharCode(n[o]);var s=A();r({data:i,addLength1:!0}),e("endobj");var a=A();r({data:f(t.metadata.toUnicode),addLength1:!0}),e("endobj");var c=A();for(e("<<"),e("/Descent "+t.metadata.decender),e("/CapHeight "+t.metadata.capHeight),e("/StemV "+t.metadata.stemV),e("/Type /FontDescriptor"),e("/FontFile2 "+s+" 0 R"),e("/Flags 96"),e("/FontBBox "+h.API.PDFObject.convert(t.metadata.bbox)),e("/FontName /"+t.fontName),e("/ItalicAngle "+t.metadata.italicAngle),e("/Ascent "+t.metadata.ascender),e(">>"),e("endobj"),t.objectNumber=A(),o=0;o<t.metadata.hmtx.widths.length;o++)t.metadata.hmtx.widths[o]=parseInt(t.metadata.hmtx.widths[o]*(1e3/t.metadata.head.unitsPerEm));e("<</Subtype/TrueType/Type/Font/ToUnicode "+a+" 0 R/BaseFont/"+t.fontName+"/FontDescriptor "+c+" 0 R/Encoding/"+t.encoding+" /FirstChar 29 /LastChar 255 /Widths "+h.API.PDFObject.convert(t.metadata.hmtx.widths)+">>"),e("endobj"),t.isAlreadyPutted=!0}}(t.font,t.out,t.newObject,t.putStream)}]);var c=function(t){var e,A,r=t.text||"",n=t.x,i=t.y,o=t.options||{},s=t.mutex||{},a=s.pdfEscape,c=s.activeFontKey,u=s.fonts,l=(s.activeFontSize,""),h=0,f="",d=u[A=c].encoding;if("Identity-H"!==u[A].encoding)return{text:r,x:n,y:i,options:o,mutex:s};for(f=r,A=c,"[object Array]"===Object.prototype.toString.call(r)&&(f=r[0]),h=0;h<f.length;h+=1)u[A].metadata.hasOwnProperty("cmap")&&(e=u[A].metadata.cmap.unicode.codeMap[f[h].charCodeAt(0)]),e?l+=f[h]:f[h].charCodeAt(0)<256&&u[A].metadata.hasOwnProperty("Unicode")?l+=f[h]:l+="";var p="";return parseInt(A.slice(1))<14||"WinAnsiEncoding"===d?p=function(t){for(var e="",A=0;A<t.length;A++)e+=""+t.charCodeAt(A).toString(16);return e}(a(l,A)):"Identity-H"===d&&(p=g(l,u[A])),s.isHex=!0,{text:p,x:n,y:i,options:o,mutex:s}};e.events.push(["postProcessText",function(t){var e=t.text||"",A=t.x,r=t.y,n=t.options,i=t.mutex,o=(n.lang,[]),s={text:e,x:A,y:r,options:n,mutex:i};if("[object Array]"===Object.prototype.toString.call(e)){var a=0;for(a=0;a<e.length;a+=1)"[object Array]"===Object.prototype.toString.call(e[a])&&3===e[a].length?o.push([c(Object.assign({},s,{text:e[a][0]})).text,e[a][1],e[a][2]]):o.push(c(Object.assign({},s,{text:e[a]})).text);t.text=o}else t.text=c(Object.assign({},s,{text:e})).text}])}(at,"undefined"!=typeof self&&self||"undefined"!=typeof xt&&xt||"undefined"!=typeof window&&window||Function("return this")()),ot=at.API,st=function(t){return void 0!==t&&(void 0===t.vFS&&(t.vFS={}),!0)},ot.existsFileInVFS=function(t){return!!st(this.internal)&&void 0!==this.internal.vFS[t]},ot.addFileToVFS=function(t,e){return st(this.internal),this.internal.vFS[t]=e,this},ot.getFileFromVFS=function(t){return st(this.internal),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null},at.API.addHTML=function(t,d,p,s,g){if("undefined"==typeof html2canvas&&"undefined"==typeof rasterizeHTML)throw new Error("You need either https://github.com/niklasvh/html2canvas or https://github.com/cburgmer/rasterizeHTML.js");"number"!=typeof d&&(s=d,g=p),"function"==typeof s&&(g=s,s=null),"function"!=typeof g&&(g=function(){});var e=this.internal,B=e.scaleFactor,w=e.pageSize.getWidth(),m=e.pageSize.getHeight();if((s=s||{}).onrendered=function(a){d=parseInt(d)||0,p=parseInt(p)||0;var t=s.dim||{},c=Object.assign({top:0,right:0,bottom:0,left:0,useFor:"content"},s.margin),e=t.h||Math.min(m,a.height/B),u=t.w||Math.min(w,a.width/B)-d,l=s.format||"JPEG",h=s.imageCompression||"SLOW";if(a.height>m-c.top-c.bottom&&s.pagesplit){var f=function(t,e,A,r,n){var i=document.createElement("canvas");i.height=n,i.width=r;var o=i.getContext("2d");return o.mozImageSmoothingEnabled=!1,o.webkitImageSmoothingEnabled=!1,o.msImageSmoothingEnabled=!1,o.imageSmoothingEnabled=!1,o.fillStyle=s.backgroundColor||"#ffffff",o.fillRect(0,0,r,n),o.drawImage(t,e,A,r,n,0,0,r,n),i},A=function(){for(var t,e,A=0,r=0,n={},i=!1;;){var o;if(r=0,n.top=0!==A?c.top:p,n.left=0!==A?c.left:d,i=(w-c.left-c.right)*B<a.width,"content"===c.useFor?0===A?(t=Math.min((w-c.left)*B,a.width),e=Math.min((m-c.top)*B,a.height-A)):(t=Math.min(w*B,a.width),e=Math.min(m*B,a.height-A),n.top=0):(t=Math.min((w-c.left-c.right)*B,a.width),e=Math.min((m-c.bottom-c.top)*B,a.height-A)),i)for(;;){"content"===c.useFor&&(0===r?t=Math.min((w-c.left)*B,a.width):(t=Math.min(w*B,a.width-r),n.left=0));var s=[o=f(a,r,A,t,e),n.left,n.top,o.width/B,o.height/B,l,null,h];if(this.addImage.apply(this,s),(r+=t)>=a.width)break;this.addPage()}else s=[o=f(a,0,A,t,e),n.left,n.top,o.width/B,o.height/B,l,null,h],this.addImage.apply(this,s);if((A+=e)>=a.height)break;this.addPage()}g(u,A,null,s)}.bind(this);if("CANVAS"===a.nodeName){var r=new Image;r.onload=A,r.src=a.toDataURL("image/png"),a=r}else A()}else{var n=Math.random().toString(35),i=[a,d,p,u,e,l,n,h];this.addImage.apply(this,i),g(u,e,n,i)}}.bind(this),"undefined"!=typeof html2canvas&&!s.rstz)return html2canvas(t,s);if("undefined"==typeof rasterizeHTML)return null;var A="drawDocument";return"string"==typeof t&&(A=/^http/.test(t)?"drawURL":"drawHTML"),s.width=s.width||w*B,rasterizeHTML[A](t,void 0,s).then(function(t){s.onrendered(t.image)},function(t){g(null,t)})},function(t){var N,L,n,o,s,a,c,u,H,m,h,l,f,A,S,x,d,p,g,_;N=function(){return function(t){return e.prototype=t,new e};function e(){}}(),m=function(t){var e,A,r,n,i,o,s;for(A=0,r=t.length,e=void 0,o=n=!1;!n&&A!==r;)(e=t[A]=t[A].trimLeft())&&(n=!0),A++;for(A=r-1;r&&!o&&-1!==A;)(e=t[A]=t[A].trimRight())&&(o=!0),A--;for(i=/\s+$/g,s=!0,A=0;A!==r;)"\u2028"!=t[A]&&(e=t[A].replace(/\s+/g," "),s&&(e=e.trimLeft()),e&&(s=i.test(e)),t[A]=e),A++;return t},l=function(t){var e,A,r;for(e=void 0,A=(r=t.split(",")).shift();!e&&A;)e=n[A.trim().toLowerCase()],A=r.shift();return e},f=function(t){var e;return-1<(t="auto"===t?"0px":t).indexOf("em")&&!isNaN(Number(t.replace("em","")))&&(t=18.719*Number(t.replace("em",""))+"px"),-1<t.indexOf("pt")&&!isNaN(Number(t.replace("pt","")))&&(t=1.333*Number(t.replace("pt",""))+"px"),(e=A[t])?e:void 0!==(e={"xx-small":9,"x-small":11,small:13,medium:16,large:19,"x-large":23,"xx-large":28,auto:0}[t])?A[t]=e/16:(e=parseFloat(t))?A[t]=e/16:(e=t.match(/([\d\.]+)(px)/),Array.isArray(e)&&3===e.length?A[t]=parseFloat(e[1])/16:A[t]=1)},H=function(t){var e,A,r,n,i;return i=t,n=document.defaultView&&document.defaultView.getComputedStyle?document.defaultView.getComputedStyle(i,null):i.currentStyle?i.currentStyle:i.style,A=void 0,(e={})["font-family"]=l((r=function(t){return t=t.replace(/-\D/g,function(t){return t.charAt(1).toUpperCase()}),n[t]})("font-family"))||"times",e["font-style"]=o[r("font-style")]||"normal",e["text-align"]=s[r("text-align")]||"left","bold"===(A=a[r("font-weight")]||"normal")&&("normal"===e["font-style"]?e["font-style"]=A:e["font-style"]=A+e["font-style"]),e["font-size"]=f(r("font-size"))||1,e["line-height"]=f(r("line-height"))||1,e.display="inline"===r("display")?"inline":"block",A="block"===e.display,e["margin-top"]=A&&f(r("margin-top"))||0,e["margin-bottom"]=A&&f(r("margin-bottom"))||0,e["padding-top"]=A&&f(r("padding-top"))||0,e["padding-bottom"]=A&&f(r("padding-bottom"))||0,e["margin-left"]=A&&f(r("margin-left"))||0,e["margin-right"]=A&&f(r("margin-right"))||0,e["padding-left"]=A&&f(r("padding-left"))||0,e["padding-right"]=A&&f(r("padding-right"))||0,e["page-break-before"]=r("page-break-before")||"auto",e.float=c[r("cssFloat")]||"none",e.clear=u[r("clear")]||"none",e.color=r("color"),e},S=function(t,e,A){var r,n,i,o,s;if(i=!1,o=n=void 0,r=A["#"+t.id])if("function"==typeof r)i=r(t,e);else for(n=0,o=r.length;!i&&n!==o;)i=r[n](t,e),n++;if(r=A[t.nodeName],!i&&r)if("function"==typeof r)i=r(t,e);else for(n=0,o=r.length;!i&&n!==o;)i=r[n](t,e),n++;for(s="string"==typeof t.className?t.className.split(" "):[],n=0;n<s.length;n++)if(r=A["."+s[n]],!i&&r)if("function"==typeof r)i=r(t,e);else for(n=0,o=r.length;!i&&n!==o;)i=r[n](t,e),n++;return i},_=function(t,e){var A,r,n,i,o,s,a,c,u;for(A=[],r=[],n=0,u=t.rows[0].cells.length,a=t.clientWidth;n<u;)c=t.rows[0].cells[n],r[n]={name:c.textContent.toLowerCase().replace(/\s+/g,""),prompt:c.textContent.replace(/\r?\n/g,""),width:c.clientWidth/a*e.pdf.internal.pageSize.getWidth()},n++;for(n=1;n<t.rows.length;){for(s=t.rows[n],o={},i=0;i<s.cells.length;)o[r[i].name]=s.cells[i].textContent.replace(/\r?\n/g,""),i++;A.push(o),n++}return{rows:A,headers:r}};var I={SCRIPT:1,STYLE:1,NOSCRIPT:1,OBJECT:1,EMBED:1,SELECT:1},T=1;L=function(t,n,e){var A,r,i,o,s,a,c,u;for(r=t.childNodes,A=void 0,(s="block"===(i=H(t)).display)&&(n.setBlockBoundary(),n.setBlockStyle(i)),o=0,a=r.length;o<a;){if("object"===se(A=r[o])){if(n.executeWatchFunctions(A),1===A.nodeType&&"HEADER"===A.nodeName){var l=A,h=n.pdf.margins_doc.top;n.pdf.internal.events.subscribe("addPage",function(t){n.y=h,L(l,n,e),n.pdf.margins_doc.top=n.y+10,n.y+=10},!1)}if(8===A.nodeType&&"#comment"===A.nodeName)~A.textContent.indexOf("ADD_PAGE")&&(n.pdf.addPage(),n.y=n.pdf.margins_doc.top);else if(1!==A.nodeType||I[A.nodeName])if(3===A.nodeType){var f=A.nodeValue;if(A.nodeValue&&"LI"===A.parentNode.nodeName)if("OL"===A.parentNode.parentNode.nodeName)f=T+++". "+f;else{var d=i["font-size"],p=(3-.75*d)*n.pdf.internal.scaleFactor,g=.75*d*n.pdf.internal.scaleFactor,B=1.74*d/n.pdf.internal.scaleFactor;u=function(t,e){this.pdf.circle(t+p,e+g,B,"FD")}}16&A.ownerDocument.body.compareDocumentPosition(A)&&n.addText(f,i)}else"string"==typeof A&&n.addText(A,i);else{var w;if("IMG"===A.nodeName){var m=A.getAttribute("src");w=x[n.pdf.sHashCode(m)||m]}if(w){n.pdf.internal.pageSize.getHeight()-n.pdf.margins_doc.bottom<n.y+A.height&&n.y>n.pdf.margins_doc.top&&(n.pdf.addPage(),n.y=n.pdf.margins_doc.top,n.executeWatchFunctions(A));var v=H(A),y=n.x,C=12/n.pdf.internal.scaleFactor,Q=(v["margin-left"]+v["padding-left"])*C,F=(v["margin-right"]+v["padding-right"])*C,b=(v["margin-top"]+v["padding-top"])*C,U=(v["margin-bottom"]+v["padding-bottom"])*C;void 0!==v.float&&"right"===v.float?y+=n.settings.width-A.width-F:y+=Q,n.pdf.addImage(w,y,n.y+b,A.width,A.height),w=void 0,"right"===v.float||"left"===v.float?(n.watchFunctions.push(function(t,e,A,r){return n.y>=e?(n.x+=t,n.settings.width+=A,!0):!!(r&&1===r.nodeType&&!I[r.nodeName]&&n.x+r.width>n.pdf.margins_doc.left+n.pdf.margins_doc.width)&&(n.x+=t,n.y=e,n.settings.width+=A,!0)}.bind(this,"left"===v.float?-A.width-Q-F:0,n.y+A.height+b+U,A.width)),n.watchFunctions.push(function(t,e,A){return!(n.y<t&&e===n.pdf.internal.getNumberOfPages())||1===A.nodeType&&"both"===H(A).clear&&(n.y=t,!0)}.bind(this,n.y+A.height,n.pdf.internal.getNumberOfPages())),n.settings.width-=A.width+Q+F,"left"===v.float&&(n.x+=A.width+Q+F)):n.y+=A.height+b+U}else if("TABLE"===A.nodeName)c=_(A,n),n.y+=10,n.pdf.table(n.x,n.y,c.rows,c.headers,{autoSize:!1,printHeaders:e.printHeaders,margins:n.pdf.margins_doc,css:H(A)}),n.y=n.pdf.lastCellPos.y+n.pdf.lastCellPos.h+20;else if("OL"===A.nodeName||"UL"===A.nodeName)T=1,S(A,n,e)||L(A,n,e),n.y+=10;else if("LI"===A.nodeName){var E=n.x;n.x+=20/n.pdf.internal.scaleFactor,n.y+=3,S(A,n,e)||L(A,n,e),n.x=E}else"BR"===A.nodeName?(n.y+=i["font-size"]*n.pdf.internal.scaleFactor,n.addText("\u2028",N(i))):S(A,n,e)||L(A,n,e)}}o++}if(e.outY=n.y,s)return n.setBlockBoundary(u)},x={},d=function(t,i,e,A){var o,r=t.getElementsByTagName("img"),n=r.length,s=0;function a(){i.pdf.internal.events.publish("imagesLoaded"),A(o)}function c(e,A,r){if(e){var n=new Image;o=++s,n.crossOrigin="",n.onerror=n.onload=function(){if(n.complete&&(0===n.src.indexOf("data:image/")&&(n.width=A||n.width||0,n.height=r||n.height||0),n.width+n.height)){var t=i.pdf.sHashCode(e)||e;x[t]=x[t]||n}--s||a()},n.src=e}}for(;n--;)c(r[n].getAttribute("src"),r[n].width,r[n].height);return s||a()},p=function(t,i,o){var s=t.getElementsByTagName("footer");if(0<s.length){s=s[0];var e=i.pdf.internal.write,A=i.y;i.pdf.internal.write=function(){},L(s,i,o);var a=Math.ceil(i.y-A)+5;i.y=A,i.pdf.internal.write=e,i.pdf.margins_doc.bottom+=a;for(var r=function(t){var e=void 0!==t?t.pageNumber:1,A=i.y;i.y=i.pdf.internal.pageSize.getHeight()-i.pdf.margins_doc.bottom,i.pdf.margins_doc.bottom-=a;for(var r=s.getElementsByTagName("span"),n=0;n<r.length;++n)-1<(" "+r[n].className+" ").replace(/[\n\t]/g," ").indexOf(" pageCounter ")&&(r[n].innerHTML=e),-1<(" "+r[n].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")&&(r[n].innerHTML="###jsPDFVarTotalPages###");L(s,i,o),i.pdf.margins_doc.bottom+=a,i.y=A},n=s.getElementsByTagName("span"),c=0;c<n.length;++c)-1<(" "+n[c].className+" ").replace(/[\n\t]/g," ").indexOf(" totalPages ")&&i.pdf.internal.events.subscribe("htmlRenderingFinished",i.pdf.putTotalPages.bind(i.pdf,"###jsPDFVarTotalPages###"),!0);i.pdf.internal.events.subscribe("addPage",r,!1),r(),I.FOOTER=1}},g=function(t,e,A,r,n,i){if(!e)return!1;var o,s,a,c;"string"==typeof e||e.parentNode||(e=""+e.innerHTML),"string"==typeof e&&(o=e.replace(/<\/?script[^>]*?>/gi,""),c="jsPDFhtmlText"+Date.now().toString()+(1e3*Math.random()).toFixed(0),(a=document.createElement("div")).style.cssText="position: absolute !important;clip: rect(1px 1px 1px 1px); /* IE6, IE7 */clip: rect(1px, 1px, 1px, 1px);padding:0 !important;border:0 !important;height: 1px !important;width: 1px !important; top:auto;left:-100px;overflow: hidden;",a.innerHTML='<iframe style="height:1px;width:1px" name="'+c+'" />',document.body.appendChild(a),(s=window.frames[c]).document.open(),s.document.writeln(o),s.document.close(),e=s.document.body);var u,l=new h(t,A,r,n);return d.call(this,e,l,n.elementHandlers,function(t){p(e,l,n.elementHandlers),L(e,l,n.elementHandlers),l.pdf.internal.events.publish("htmlRenderingFinished"),u=l.dispose(),"function"==typeof i?i(u):t&&console.error("jsPDF Warning: rendering issues? provide a callback to fromHTML!")}),u||{x:l.x,y:l.y}},(h=function(t,e,A,r){return this.pdf=t,this.x=e,this.y=A,this.settings=r,this.watchFunctions=[],this.init(),this}).prototype.init=function(){return this.paragraph={text:[],style:[]},this.pdf.internal.write("q")},h.prototype.dispose=function(){return this.pdf.internal.write("Q"),{x:this.x,y:this.y,ready:!0}},h.prototype.executeWatchFunctions=function(t){var e=!1,A=[];if(0<this.watchFunctions.length){for(var r=0;r<this.watchFunctions.length;++r)!0===this.watchFunctions[r](t)?e=!0:A.push(this.watchFunctions[r]);this.watchFunctions=A}return e},h.prototype.splitFragmentsIntoLines=function(t,e){var A,r,n,i,o,s,a,c,u,l,h,f,d,p;for(l=this.pdf.internal.scaleFactor,i={},s=a=c=p=o=n=u=r=void 0,f=[h=[]],A=0,d=this.settings.width;t.length;)if(o=t.shift(),p=e.shift(),o)if((n=i[(r=p["font-family"])+(u=p["font-style"])])||(n=this.pdf.internal.getFont(r,u).metadata.Unicode,i[r+u]=n),c={widths:n.widths,kerning:n.kerning,fontSize:12*p["font-size"],textIndent:A},a=this.pdf.getStringUnitWidth(o,c)*c.fontSize/l,"\u2028"==o)h=[],f.push(h);else if(d<A+a){for(s=this.pdf.splitTextToSize(o,d,c),h.push([s.shift(),p]);s.length;)h=[[s.shift(),p]],f.push(h);A=this.pdf.getStringUnitWidth(h[0][0],c)*c.fontSize/l}else h.push([o,p]),A+=a;if(void 0!==p["text-align"]&&("center"===p["text-align"]||"right"===p["text-align"]||"justify"===p["text-align"]))for(var g=0;g<f.length;++g){var B=this.pdf.getStringUnitWidth(f[g][0][0],c)*c.fontSize/l;0<g&&(f[g][0][1]=N(f[g][0][1]));var w=d-B;if("right"===p["text-align"])f[g][0][1]["margin-left"]=w;else if("center"===p["text-align"])f[g][0][1]["margin-left"]=w/2;else if("justify"===p["text-align"]){var m=f[g][0][0].split(" ").length-1;f[g][0][1]["word-spacing"]=w/m,g===f.length-1&&(f[g][0][1]["word-spacing"]=0)}}return f},h.prototype.RenderTextFragment=function(t,e){var A,r;r=0,this.pdf.internal.pageSize.getHeight()-this.pdf.margins_doc.bottom<this.y+this.pdf.internal.getFontSize()&&(this.pdf.internal.write("ET","Q"),this.pdf.addPage(),this.y=this.pdf.margins_doc.top,this.pdf.internal.write("q","BT",this.getPdfColor(e.color),this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td"),r=Math.max(r,e["line-height"],e["font-size"]),this.pdf.internal.write(0,(-12*r).toFixed(2),"Td")),A=this.pdf.internal.getFont(e["font-family"],e["font-style"]);var n=this.getPdfColor(e.color);n!==this.lastTextColor&&(this.pdf.internal.write(n),this.lastTextColor=n),void 0!==e["word-spacing"]&&0<e["word-spacing"]&&this.pdf.internal.write(e["word-spacing"].toFixed(2),"Tw"),this.pdf.internal.write("/"+A.id,(12*e["font-size"]).toFixed(2),"Tf","("+this.pdf.internal.pdfEscape(t)+") Tj"),void 0!==e["word-spacing"]&&this.pdf.internal.write(0,"Tw")},h.prototype.getPdfColor=function(t){var e,A,r,n=/rgb\s*\(\s*(\d+),\s*(\d+),\s*(\d+\s*)\)/.exec(t);if(null!=n)e=parseInt(n[1]),A=parseInt(n[2]),r=parseInt(n[3]);else{if("string"==typeof t&&"#"!=t.charAt(0)){var i=new RGBColor(t);t=i.ok?i.toHex():"#000000"}e=t.substring(1,3),e=parseInt(e,16),A=t.substring(3,5),A=parseInt(A,16),r=t.substring(5,7),r=parseInt(r,16)}if("string"==typeof e&&/^#[0-9A-Fa-f]{6}$/.test(e)){var o=parseInt(e.substr(1),16);e=o>>16&255,A=o>>8&255,r=255&o}var s=this.f3;return 0===e&&0===A&&0===r||void 0===A?s(e/255)+" g":[s(e/255),s(A/255),s(r/255),"rg"].join(" ")},h.prototype.f3=function(t){return t.toFixed(3)},h.prototype.renderParagraph=function(t){var e,A,r,n,i,o,s,a,c,u,l,h,f;if(r=m(this.paragraph.text),h=this.paragraph.style,e=this.paragraph.blockstyle,this.paragraph.priorblockstyle||{},this.paragraph={text:[],style:[],blockstyle:{},priorblockstyle:e},r.join("").trim()){s=this.splitFragmentsIntoLines(r,h),a=o=void 0,A=12/this.pdf.internal.scaleFactor,this.priorMarginBottom=this.priorMarginBottom||0,l=(Math.max((e["margin-top"]||0)-this.priorMarginBottom,0)+(e["padding-top"]||0))*A,u=((e["margin-bottom"]||0)+(e["padding-bottom"]||0))*A,this.priorMarginBottom=e["margin-bottom"]||0,"always"===e["page-break-before"]&&(this.pdf.addPage(),this.y=0,l=((e["margin-top"]||0)+(e["padding-top"]||0))*A),c=this.pdf.internal.write,i=n=void 0,this.y+=l,c("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td");for(var d=0;s.length;){for(n=a=0,i=(o=s.shift()).length;n!==i;)o[n][0].trim()&&(a=Math.max(a,o[n][1]["line-height"],o[n][1]["font-size"]),f=7*o[n][1]["font-size"]),n++;var p=0,g=0;for(void 0!==o[0][1]["margin-left"]&&0<o[0][1]["margin-left"]&&(p=(g=this.pdf.internal.getCoordinateString(o[0][1]["margin-left"]))-d,d=g),c(p+Math.max(e["margin-left"]||0,0)*A,(-12*a).toFixed(2),"Td"),n=0,i=o.length;n!==i;)o[n][0]&&this.RenderTextFragment(o[n][0],o[n][1]),n++;if(this.y+=a*A,this.executeWatchFunctions(o[0][1])&&0<s.length){var B=[],w=[];s.forEach(function(t){for(var e=0,A=t.length;e!==A;)t[e][0]&&(B.push(t[e][0]+" "),w.push(t[e][1])),++e}),s=this.splitFragmentsIntoLines(m(B),w),c("ET","Q"),c("q","BT 0 g",this.pdf.internal.getCoordinateString(this.x),this.pdf.internal.getVerticalCoordinateString(this.y),"Td")}}return t&&"function"==typeof t&&t.call(this,this.x-9,this.y-f/2),c("ET","Q"),this.y+=u}},h.prototype.setBlockBoundary=function(t){return this.renderParagraph(t)},h.prototype.setBlockStyle=function(t){return this.paragraph.blockstyle=t},h.prototype.addText=function(t,e){return this.paragraph.text.push(t),this.paragraph.style.push(e)},n={helvetica:"helvetica","sans-serif":"helvetica","times new roman":"times",serif:"times",times:"times",monospace:"courier",courier:"courier"},a={100:"normal",200:"normal",300:"normal",400:"normal",500:"bold",600:"bold",700:"bold",800:"bold",900:"bold",normal:"normal",bold:"bold",bolder:"bold",lighter:"normal"},o={normal:"normal",italic:"italic",oblique:"italic"},s={left:"left",right:"right",center:"center",justify:"justify"},c={none:"none",right:"right",left:"left"},u={none:"none",both:"both"},A={normal:1},t.fromHTML=function(t,e,A,r,n,i){return this.margins_doc=i||{top:0,bottom:0},r||(r={}),r.elementHandlers||(r.elementHandlers={}),g(this,t,isNaN(e)?4:e,isNaN(A)?4:A,r,n)}}(at.API),("undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt).html2pdf=function(t,o,e){var A=o.canvas;if(A){var r,n;if((A.pdf=o).annotations={_nameMap:[],createAnnotation:function(t,e){var A,r=o.context2d._wrapX(e.left),n=o.context2d._wrapY(e.top),i=(o.context2d._page(e.top),t.indexOf("#"));A=0<=i?{name:t.substring(i+1)}:{url:t},o.link(r,n,e.right-e.left,e.bottom-e.top,A)},setName:function(t,e){var A=o.context2d._wrapX(e.left),r=o.context2d._wrapY(e.top),n=o.context2d._page(e.top);this._nameMap[t]={page:n,x:A,y:r}}},A.annotations=o.annotations,o.context2d._pageBreakAt=function(t){this.pageBreaks.push(t)},o.context2d._gotoPage=function(t){for(;o.internal.getNumberOfPages()<t;)o.addPage();o.setPage(t)},"string"==typeof t){t=t.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"");var i,s,a=document.createElement("iframe");document.body.appendChild(a),null!=(i=a.contentDocument)&&null!=i||(i=a.contentWindow.document),i.open(),i.write(t),i.close(),r=i.body,s=i.body||{},t=i.documentElement||{},n=Math.max(s.scrollHeight,s.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight)}else s=(r=t).body||{},n=Math.max(s.scrollHeight,s.offsetHeight,t.clientHeight,t.scrollHeight,t.offsetHeight);var c={async:!0,allowTaint:!0,backgroundColor:"#ffffff",canvas:A,imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1,windowHeight:n=o.internal.pageSize.getHeight(),scrollY:n};o.context2d.pageWrapYEnabled=!0,o.context2d.pageWrapY=o.internal.pageSize.getHeight(),html2canvas(r,c).then(function(t){e&&(a&&a.parentElement.removeChild(a),e(o))})}else alert("jsPDF canvas plugin not installed")},window.tmp=html2pdf,function(h){var r=h.BlobBuilder||h.WebKitBlobBuilder||h.MSBlobBuilder||h.MozBlobBuilder;h.URL=h.URL||h.webkitURL||function(t,e){return(e=document.createElement("a")).href=t,e};var A=h.Blob,f=URL.createObjectURL,d=URL.revokeObjectURL,i=h.Symbol&&h.Symbol.toStringTag,t=!1,e=!1,p=!!h.ArrayBuffer,n=r&&r.prototype.append&&r.prototype.getBlob;try{t=2===new Blob(["\xe4"]).size,e=2===new Blob([new Uint8Array([1,2])]).size}catch(t){}function o(t){return t.map(function(t){if(t.buffer instanceof ArrayBuffer){var e=t.buffer;if(t.byteLength!==e.byteLength){var A=new Uint8Array(t.byteLength);A.set(new Uint8Array(e,t.byteOffset,t.byteLength)),e=A.buffer}return e}return t})}function s(t,e){e=e||{};var A=new r;return o(t).forEach(function(t){A.append(t)}),e.type?A.getBlob(e.type):A.getBlob()}function a(t,e){return new A(o(t),e||{})}if(h.Blob&&(s.prototype=Blob.prototype,a.prototype=Blob.prototype),i)try{File.prototype[i]="File",Blob.prototype[i]="Blob",FileReader.prototype[i]="FileReader"}catch(t){}function c(){var t=!!h.ActiveXObject||"-ms-scroll-limit"in document.documentElement.style&&"-ms-ime-align"in document.documentElement.style,e=h.XMLHttpRequest&&h.XMLHttpRequest.prototype.send;t&&e&&(XMLHttpRequest.prototype.send=function(t){t instanceof Blob&&this.setRequestHeader("Content-Type",t.type),e.call(this,t)});try{new File([],"")}catch(t){try{var A=new Function('class File extends Blob {constructor(chunks, name, opts) {opts = opts || {};super(chunks, opts || {});this.name = name;this.lastModifiedDate = opts.lastModified ? new Date(opts.lastModified) : new Date;this.lastModified = +this.lastModifiedDate;}};return new File([], ""), File')();h.File=A}catch(t){A=function(t,e,A){var r=new Blob(t,A),n=A&&void 0!==A.lastModified?new Date(A.lastModified):new Date;return r.name=e,r.lastModifiedDate=n,r.lastModified=+n,r.toString=function(){return"[object File]"},i&&(r[i]="File"),r};h.File=A}}}t?(c(),h.Blob=e?h.Blob:a):n?(c(),h.Blob=s):function(){function o(t){for(var e=[],A=0;A<t.length;A++){var r=t.charCodeAt(A);r<128?e.push(r):r<2048?e.push(192|r>>6,128|63&r):r<55296||57344<=r?e.push(224|r>>12,128|r>>6&63,128|63&r):(A++,r=65536+((1023&r)<<10|1023&t.charCodeAt(A)),e.push(240|r>>18,128|r>>12&63,128|r>>6&63,128|63&r))}return e}function e(t){var e,A,r,n,i,o;for(e="",r=t.length,A=0;A<r;)switch((n=t[A++])>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:e+=String.fromCharCode(n);break;case 12:case 13:i=t[A++],e+=String.fromCharCode((31&n)<<6|63&i);break;case 14:i=t[A++],o=t[A++],e+=String.fromCharCode((15&n)<<12|(63&i)<<6|(63&o)<<0)}return e}function s(t){for(var e=new Array(t.byteLength),A=new Uint8Array(t),r=e.length;r--;)e[r]=A[r];return e}function A(t){for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",A=[],r=0;r<t.length;r+=3){var n=t[r],i=r+1<t.length,o=i?t[r+1]:0,s=r+2<t.length,a=s?t[r+2]:0,c=n>>2,u=(3&n)<<4|o>>4,l=(15&o)<<2|a>>6,h=63&a;s||(h=64,i||(l=64)),A.push(e[c],e[u],e[l],e[h])}return A.join("")}var t=Object.create||function(t){function e(){}return e.prototype=t,new e};if(p)var r=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],a=ArrayBuffer.isView||function(t){return t&&-1<r.indexOf(Object.prototype.toString.call(t))};function c(t,e){for(var A=0,r=(t=t||[]).length;A<r;A++){var n=t[A];n instanceof c?t[A]=n._buffer:"string"==typeof n?t[A]=o(n):p&&(ArrayBuffer.prototype.isPrototypeOf(n)||a(n))?t[A]=s(n):p&&(i=n)&&DataView.prototype.isPrototypeOf(i)?t[A]=s(n.buffer):t[A]=o(String(n))}var i;this._buffer=[].concat.apply([],t),this.size=this._buffer.length,this.type=e&&e.type||""}function n(t,e,A){var r=c.call(this,t,A=A||{})||this;return r.name=e,r.lastModifiedDate=A.lastModified?new Date(A.lastModified):new Date,r.lastModified=+r.lastModifiedDate,r}if(c.prototype.slice=function(t,e,A){return new c([this._buffer.slice(t||0,e||this._buffer.length)],{type:A})},c.prototype.toString=function(){return"[object Blob]"},(n.prototype=t(c.prototype)).constructor=n,Object.setPrototypeOf)Object.setPrototypeOf(n,c);else try{n.__proto__=c}catch(t){}function i(){if(!(this instanceof i))throw new TypeError("Failed to construct 'FileReader': Please use the 'new' operator, this DOM object constructor cannot be called as a function.");var A=document.createDocumentFragment();this.addEventListener=A.addEventListener,this.dispatchEvent=function(t){var e=this["on"+t.type];"function"==typeof e&&e(t),A.dispatchEvent(t)},this.removeEventListener=A.removeEventListener}function u(t,e,A){if(!(e instanceof c))throw new TypeError("Failed to execute '"+A+"' on 'FileReader': parameter 1 is not of type 'Blob'.");t.result="",setTimeout(function(){this.readyState=i.LOADING,t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend"))})}n.prototype.toString=function(){return"[object File]"},i.EMPTY=0,i.LOADING=1,i.DONE=2,i.prototype.error=null,i.prototype.onabort=null,i.prototype.onerror=null,i.prototype.onload=null,i.prototype.onloadend=null,i.prototype.onloadstart=null,i.prototype.onprogress=null,i.prototype.readAsDataURL=function(t){u(this,t,"readAsDataURL"),this.result="data:"+t.type+";base64,"+A(t._buffer)},i.prototype.readAsText=function(t){u(this,t,"readAsText"),this.result=e(t._buffer)},i.prototype.readAsArrayBuffer=function(t){u(this,t,"readAsText"),this.result=t._buffer.slice()},i.prototype.abort=function(){},URL.createObjectURL=function(t){return t instanceof c?"data:"+t.type+";base64,"+A(t._buffer):f.call(URL,t)},URL.revokeObjectURL=function(t){d&&d.call(URL,t)};var l=h.XMLHttpRequest&&h.XMLHttpRequest.prototype.send;l&&(XMLHttpRequest.prototype.send=function(t){t instanceof c?(this.setRequestHeader("Content-Type",t.type),l.call(this,e(t._buffer))):l.call(this,t)}),h.FileReader=i,h.File=n,h.Blob=c}()}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")());var ct,ut,lt,ht,ft,dt,pt,gt,Bt,wt,mt,vt,yt,Ct,Qt,ae=ae||function(s){if(!(void 0===s||"undefined"!=typeof navigator&&/MSIE [1-9]\./.test(navigator.userAgent))){var t=s.document,a=function(){return s.URL||s.webkitURL||s},c=t.createElementNS("http://www.w3.org/1999/xhtml","a"),u="download"in c,l=/constructor/i.test(s.HTMLElement)||s.safari,h=/CriOS\/[\d]+/.test(navigator.userAgent),f=s.setImmediate||s.setTimeout,d=function(t){f(function(){throw t},0)},p=function(t){setTimeout(function(){"string"==typeof t?a().revokeObjectURL(t):t.remove()},4e4)},g=function(t){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t},r=function(t,A,e){e||(t=g(t));var r,n=this,i="application/octet-stream"===t.type,o=function(){!function(t,e,A){for(var r=(e=[].concat(e)).length;r--;){var n=t["on"+e[r]];if("function"==typeof n)try{n.call(t,A||t)}catch(t){d(t)}}}(n,"writestart progress write writeend".split(" "))};if(n.readyState=n.INIT,u)return r=a().createObjectURL(t),void f(function(){var t,e;c.href=r,c.download=A,t=c,e=new MouseEvent("click"),t.dispatchEvent(e),o(),p(r),n.readyState=n.DONE},0);!function(){if((h||i&&l)&&s.FileReader){var e=new FileReader;return e.onloadend=function(){var t=h?e.result:e.result.replace(/^data:[^;]*;/,"data:attachment/file;");s.open(t,"_blank")||(s.location.href=t),t=void 0,n.readyState=n.DONE,o()},e.readAsDataURL(t),n.readyState=n.INIT}r||(r=a().createObjectURL(t)),i?s.location.href=r:s.open(r,"_blank")||(s.location.href=r);n.readyState=n.DONE,o(),p(r)}()},e=r.prototype;return"undefined"!=typeof navigator&&navigator.msSaveOrOpenBlob?function(t,e,A){return e=e||t.name||"download",A||(t=g(t)),navigator.msSaveOrOpenBlob(t,e)}:(e.abort=function(){},e.readyState=e.INIT=0,e.WRITING=1,e.DONE=2,e.error=e.onwritestart=e.onprogress=e.onwrite=e.onabort=e.onerror=e.onwriteend=null,function(t,e,A){return new r(t,e||t.name||"download",A)})}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||void 0);function Ft(y){var t=0;if(71!==y[t++]||73!==y[t++]||70!==y[t++]||56!==y[t++]||56!=(y[t++]+1&253)||97!==y[t++])throw"Invalid GIF 87a/89a header.";var C=y[t++]|y[t++]<<8,e=y[t++]|y[t++]<<8,A=y[t++],r=A>>7,n=1<<(7&A)+1;y[t++];y[t++];var i=null;r&&(i=t,t+=3*n);var o=!0,s=[],a=0,c=null,u=0,l=null;for(this.width=C,this.height=e;o&&t<y.length;)switch(y[t++]){case 33:switch(y[t++]){case 255:if(11!==y[t]||78==y[t+1]&&69==y[t+2]&&84==y[t+3]&&83==y[t+4]&&67==y[t+5]&&65==y[t+6]&&80==y[t+7]&&69==y[t+8]&&50==y[t+9]&&46==y[t+10]&&48==y[t+11]&&3==y[t+12]&&1==y[t+13]&&0==y[t+16])t+=14,l=y[t++]|y[t++]<<8,t++;else for(t+=12;;){if(0===(F=y[t++]))break;t+=F}break;case 249:if(4!==y[t++]||0!==y[t+4])throw"Invalid graphics extension block.";var h=y[t++];a=y[t++]|y[t++]<<8,c=y[t++],0==(1&h)&&(c=null),u=h>>2&7,t++;break;case 254:for(;;){if(0===(F=y[t++]))break;t+=F}break;default:throw"Unknown graphic control label: 0x"+y[t-1].toString(16)}break;case 44:var f=y[t++]|y[t++]<<8,d=y[t++]|y[t++]<<8,p=y[t++]|y[t++]<<8,g=y[t++]|y[t++]<<8,B=y[t++],w=B>>6&1,m=i,v=!1;if(B>>7){v=!0;m=t,t+=3*(1<<(7&B)+1)}var Q=t;for(t++;;){var F;if(0===(F=y[t++]))break;t+=F}s.push({x:f,y:d,width:p,height:g,has_local_palette:v,palette_offset:m,data_offset:Q,data_length:t-Q,transparent_index:c,interlaced:!!w,delay:a,disposal:u});break;case 59:o=!1;break;default:throw"Unknown gif block: 0x"+y[t-1].toString(16)}this.numFrames=function(){return s.length},this.loopCount=function(){return l},this.frameInfo=function(t){if(t<0||t>=s.length)throw"Frame index out of range.";return s[t]},this.decodeAndBlitFrameBGRA=function(t,e){var A=this.frameInfo(t),r=A.width*A.height,n=new Uint8Array(r);bt(y,A.data_offset,n,r);var i=A.palette_offset,o=A.transparent_index;null===o&&(o=256);var s=A.width,a=C-s,c=s,u=4*(A.y*C+A.x),l=4*((A.y+A.height)*C+A.x),h=u,f=4*a;!0===A.interlaced&&(f+=4*(s+a)*7);for(var d=8,p=0,g=n.length;p<g;++p){var B=n[p];if(0===c&&(c=s,l<=(h+=f)&&(f=a+4*(s+a)*(d-1),h=u+(s+a)*(d<<1),d>>=1)),B===o)h+=4;else{var w=y[i+3*B],m=y[i+3*B+1],v=y[i+3*B+2];e[h++]=v,e[h++]=m,e[h++]=w,e[h++]=255}--c}},this.decodeAndBlitFrameRGBA=function(t,e){var A=this.frameInfo(t),r=A.width*A.height,n=new Uint8Array(r);bt(y,A.data_offset,n,r);var i=A.palette_offset,o=A.transparent_index;null===o&&(o=256);var s=A.width,a=C-s,c=s,u=4*(A.y*C+A.x),l=4*((A.y+A.height)*C+A.x),h=u,f=4*a;!0===A.interlaced&&(f+=4*(s+a)*7);for(var d=8,p=0,g=n.length;p<g;++p){var B=n[p];if(0===c&&(c=s,l<=(h+=f)&&(f=a+4*(s+a)*(d-1),h=u+(s+a)*(d<<1),d>>=1)),B===o)h+=4;else{var w=y[i+3*B],m=y[i+3*B+1],v=y[i+3*B+2];e[h++]=w,e[h++]=m,e[h++]=v,e[h++]=255}--c}}}function bt(t,e,A,r){for(var n=t[e++],i=1<<n,o=i+1,s=o+1,a=n+1,c=(1<<a)-1,u=0,l=0,h=0,f=t[e++],d=new Int32Array(4096),p=null;;){for(;u<16&&0!==f;)l|=t[e++]<<u,u+=8,1===f?f=t[e++]:--f;if(u<a)break;var g=l&c;if(l>>=a,u-=a,g!==i){if(g===o)break;for(var B=g<s?g:p,w=0,m=B;i<m;)m=d[m]>>8,++w;var v=m;if(r<h+w+(B!==g?1:0))return void console.log("Warning, gif stream longer than expected.");A[h++]=v;var y=h+=w;for(B!==g&&(A[h++]=v),m=B;w--;)m=d[m],A[--y]=255&m,m>>=8;null!==p&&s<4096&&(d[s++]=p<<8|v,c+1<=s&&a<12&&(++a,c=c<<1|1)),p=g}else s=o+1,c=(1<<(a=n+1))-1,p=null}return h!==r&&console.log("Warning, gif stream shorter than expected."),A}try{St.GifWriter=function(B,t,e,A){var w=0,r=void 0===(A=void 0===A?{}:A).loop?null:A.loop,m=void 0===A.palette?null:A.palette;if(t<=0||e<=0||65535<t||65535<e)throw"Width/Height invalid.";function v(t){var e=t.length;if(e<2||256<e||e&e-1)throw"Invalid code/color length, must be power of 2 and 2 .. 256.";return e}B[w++]=71,B[w++]=73,B[w++]=70,B[w++]=56,B[w++]=57,B[w++]=97;var n=0,i=0;if(null!==m){for(var o=v(m);o>>=1;)++n;if(o=1<<n,--n,void 0!==A.background){if(o<=(i=A.background))throw"Background index out of range.";if(0===i)throw"Background index explicitly passed as 0."}}if(B[w++]=255&t,B[w++]=t>>8&255,B[w++]=255&e,B[w++]=e>>8&255,B[w++]=(null!==m?128:0)|n,B[w++]=i,B[w++]=0,null!==m)for(var s=0,a=m.length;s<a;++s){var c=m[s];B[w++]=c>>16&255,B[w++]=c>>8&255,B[w++]=255&c}if(null!==r){if(r<0||65535<r)throw"Loop count invalid.";B[w++]=33,B[w++]=255,B[w++]=11,B[w++]=78,B[w++]=69,B[w++]=84,B[w++]=83,B[w++]=67,B[w++]=65,B[w++]=80,B[w++]=69,B[w++]=50,B[w++]=46,B[w++]=48,B[w++]=3,B[w++]=1,B[w++]=255&r,B[w++]=r>>8&255,B[w++]=0}var y=!1;this.addFrame=function(t,e,A,r,n,i){if(!0===y&&(--w,y=!1),i=void 0===i?{}:i,t<0||e<0||65535<t||65535<e)throw"x/y invalid.";if(A<=0||r<=0||65535<A||65535<r)throw"Width/Height invalid.";if(n.length<A*r)throw"Not enough pixels for the frame size.";var o=!0,s=i.palette;if(null==s&&(o=!1,s=m),null==s)throw"Must supply either a local or global palette.";for(var a=v(s),c=0;a>>=1;)++c;a=1<<c;var u=void 0===i.delay?0:i.delay,l=void 0===i.disposal?0:i.disposal;if(l<0||3<l)throw"Disposal out of range.";var h=!1,f=0;if(void 0!==i.transparent&&null!==i.transparent&&(h=!0,(f=i.transparent)<0||a<=f))throw"Transparent color index.";if((0!==l||h||0!==u)&&(B[w++]=33,B[w++]=249,B[w++]=4,B[w++]=l<<2|(!0===h?1:0),B[w++]=255&u,B[w++]=u>>8&255,B[w++]=f,B[w++]=0),B[w++]=44,B[w++]=255&t,B[w++]=t>>8&255,B[w++]=255&e,B[w++]=e>>8&255,B[w++]=255&A,B[w++]=A>>8&255,B[w++]=255&r,B[w++]=r>>8&255,B[w++]=!0===o?128|c-1:0,!0===o)for(var d=0,p=s.length;d<p;++d){var g=s[d];B[w++]=g>>16&255,B[w++]=g>>8&255,B[w++]=255&g}w=function(e,A,t,r){e[A++]=t;var n=A++,i=1<<t,o=i-1,s=i+1,a=s+1,c=t+1,u=0,l=0;function h(t){for(;t<=u;)e[A++]=255&l,l>>=8,u-=8,A===n+256&&(e[n]=255,n=A++)}function f(t){l|=t<<u,u+=c,h(8)}var d=r[0]&o,p={};f(i);for(var g=1,B=r.length;g<B;++g){var w=r[g]&o,m=d<<8|w,v=p[m];if(void 0===v){for(l|=d<<u,u+=c;8<=u;)e[A++]=255&l,l>>=8,u-=8,A===n+256&&(e[n]=255,n=A++);4096===a?(f(i),a=s+1,c=t+1,p={}):(1<<c<=a&&++c,p[m]=a++),d=w}else d=v}return f(d),f(s),h(1),n+1===A?e[n]=0:(e[n]=A-n-1,e[A++]=0),A}(B,w,c<2?2:c,n)},this.end=function(){return!1===y&&(B[w++]=59,y=!0),w}},St.GifReader=Ft}catch(t){}function Ut(t){var C,Q,F,b,e,l=Math.floor,U=new Array(64),E=new Array(64),N=new Array(64),L=new Array(64),B=new Array(65535),w=new Array(65535),Y=new Array(64),m=new Array(64),H=[],S=0,x=7,_=new Array(64),I=new Array(64),T=new Array(64),A=new Array(256),O=new Array(2048),v=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],R=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],P=[0,1,2,3,4,5,6,7,8,9,10,11],M=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],K=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],D=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],k=[0,1,2,3,4,5,6,7,8,9,10,11],z=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],j=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function r(t,e){for(var A=0,r=0,n=new Array,i=1;i<=16;i++){for(var o=1;o<=t[i];o++)n[e[r]]=[],n[e[r]][0]=A,n[e[r]][1]=i,r++,A++;A*=2}return n}function q(t){for(var e=t[0],A=t[1]-1;0<=A;)e&1<<A&&(S|=1<<x),A--,--x<0&&(255==S?(V(255),V(0)):V(S),x=7,S=0)}function V(t){H.push(t)}function X(t){V(t>>8&255),V(255&t)}function G(t,e,A,r,n){for(var i,o=n[0],s=n[240],a=function(t,e){var A,r,n,i,o,s,a,c,u,l,h=0;for(u=0;u<8;++u){A=t[h],r=t[h+1],n=t[h+2],i=t[h+3],o=t[h+4],s=t[h+5],a=t[h+6];var f=A+(c=t[h+7]),d=A-c,p=r+a,g=r-a,B=n+s,w=n-s,m=i+o,v=i-o,y=f+m,C=f-m,Q=p+B,F=p-B;t[h]=y+Q,t[h+4]=y-Q;var b=.707106781*(F+C);t[h+2]=C+b,t[h+6]=C-b;var U=.382683433*((y=v+w)-(F=g+d)),E=.5411961*y+U,N=1.306562965*F+U,L=.707106781*(Q=w+g),H=d+L,S=d-L;t[h+5]=S+E,t[h+3]=S-E,t[h+1]=H+N,t[h+7]=H-N,h+=8}for(u=h=0;u<8;++u){A=t[h],r=t[h+8],n=t[h+16],i=t[h+24],o=t[h+32],s=t[h+40],a=t[h+48];var x=A+(c=t[h+56]),_=A-c,I=r+a,T=r-a,O=n+s,R=n-s,P=i+o,M=i-o,K=x+P,D=x-P,k=I+O,z=I-O;t[h]=K+k,t[h+32]=K-k;var j=.707106781*(z+D);t[h+16]=D+j,t[h+48]=D-j;var q=.382683433*((K=M+R)-(z=T+_)),V=.5411961*K+q,X=1.306562965*z+q,G=.707106781*(k=R+T),J=_+G,W=_-G;t[h+40]=W+V,t[h+24]=W-V,t[h+8]=J+X,t[h+56]=J-X,h++}for(u=0;u<64;++u)l=t[u]*e[u],Y[u]=0<l?l+.5|0:l-.5|0;return Y}(t,e),c=0;c<64;++c)m[v[c]]=a[c];var u=m[0]-A;A=m[0],0==u?q(r[0]):(q(r[w[i=32767+u]]),q(B[i]));for(var l=63;0<l&&0==m[l];l--);if(0==l)return q(o),A;for(var h,f=1;f<=l;){for(var d=f;0==m[f]&&f<=l;++f);var p=f-d;if(16<=p){h=p>>4;for(var g=1;g<=h;++g)q(s);p&=15}i=32767+m[f],q(n[(p<<4)+w[i]]),q(B[i]),f++}return 63!=l&&q(o),A}function J(t){if(t<=0&&(t=1),100<t&&(t=100),e!=t){(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],A=0;A<64;A++){var r=l((e[A]*t+50)/100);r<1?r=1:255<r&&(r=255),U[v[A]]=r}for(var n=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],i=0;i<64;i++){var o=l((n[i]*t+50)/100);o<1?o=1:255<o&&(o=255),E[v[i]]=o}for(var s=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],a=0,c=0;c<8;c++)for(var u=0;u<8;u++)N[a]=1/(U[v[a]]*s[c]*s[u]*8),L[a]=1/(E[v[a]]*s[c]*s[u]*8),a++})(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),e=t}}this.encode=function(t,e){var A,r;(new Date).getTime();e&&J(e),H=new Array,S=0,x=7,X(65496),X(65504),X(16),V(74),V(70),V(73),V(70),V(0),V(1),V(1),V(0),X(1),X(1),V(0),V(0),function(){X(65499),X(132),V(0);for(var t=0;t<64;t++)V(U[t]);V(1);for(var e=0;e<64;e++)V(E[e])}(),A=t.width,r=t.height,X(65472),X(17),V(8),X(r),X(A),V(3),V(1),V(17),V(0),V(2),V(17),V(1),V(3),V(17),V(1),function(){X(65476),X(418),V(0);for(var t=0;t<16;t++)V(R[t+1]);for(var e=0;e<=11;e++)V(P[e]);V(16);for(var A=0;A<16;A++)V(M[A+1]);for(var r=0;r<=161;r++)V(K[r]);V(1);for(var n=0;n<16;n++)V(D[n+1]);for(var i=0;i<=11;i++)V(k[i]);V(17);for(var o=0;o<16;o++)V(z[o+1]);for(var s=0;s<=161;s++)V(j[s])}(),X(65498),X(12),V(3),V(1),V(0),V(2),V(17),V(3),V(17),V(0),V(63),V(0);var n=0,i=0,o=0;S=0,x=7,this.encode.displayName="_encode_";for(var s,a,c,u,l,h,f,d,p,g=t.data,B=t.width,w=t.height,m=4*B,v=0;v<w;){for(s=0;s<m;){for(h=l=m*v+s,f=-1,p=d=0;p<64;p++)h=l+(d=p>>3)*m+(f=4*(7&p)),w<=v+d&&(h-=m*(v+1+d-w)),m<=s+f&&(h-=s+f-m+4),a=g[h++],c=g[h++],u=g[h++],_[p]=(O[a]+O[c+256>>0]+O[u+512>>0]>>16)-128,I[p]=(O[a+768>>0]+O[c+1024>>0]+O[u+1280>>0]>>16)-128,T[p]=(O[a+1280>>0]+O[c+1536>>0]+O[u+1792>>0]>>16)-128;n=G(_,N,n,C,F),i=G(I,L,i,Q,b),o=G(T,L,o,Q,b),s+=32}v+=8}if(0<=x){var y=[];y[1]=x+1,y[0]=(1<<x+1)-1,q(y)}return X(65497),new Uint8Array(H)},function(){(new Date).getTime();t||(t=50),function(){for(var t=String.fromCharCode,e=0;e<256;e++)A[e]=t(e)}(),C=r(R,P),Q=r(D,k),F=r(M,K),b=r(z,j),function(){for(var t=1,e=2,A=1;A<=15;A++){for(var r=t;r<e;r++)w[32767+r]=A,B[32767+r]=[],B[32767+r][1]=A,B[32767+r][0]=r;for(var n=-(e-1);n<=-t;n++)w[32767+n]=A,B[32767+n]=[],B[32767+n][1]=A,B[32767+n][0]=e-1+n;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)O[t]=19595*t,O[t+256>>0]=38470*t,O[t+512>>0]=7471*t+32768,O[t+768>>0]=-11059*t,O[t+1024>>0]=-21709*t,O[t+1280>>0]=32768*t+8421375,O[t+1536>>0]=-27439*t,O[t+1792>>0]=-5329*t}(),J(t),(new Date).getTime()}()}function Et(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}window.tmp=Ft,at.API.adler32cs=(dt="function"==typeof ArrayBuffer&&"function"==typeof Uint8Array,pt=null,gt=function(){if(!dt)return function(){return!1};try{var t={};"function"==typeof t.Buffer&&(pt=t.Buffer)}catch(t){}return function(t){return t instanceof ArrayBuffer||null!==pt&&t instanceof pt}}(),Bt=null!==pt?function(t){return new pt(t,"utf8").toString("binary")}:function(t){return unescape(encodeURIComponent(t))},wt=function(t,e){for(var A=65535&t,r=t>>>16,n=0,i=e.length;n<i;n++)A=(A+(255&e.charCodeAt(n)))%65521,r=(r+A)%65521;return(r<<16|A)>>>0},mt=function(t,e){for(var A=65535&t,r=t>>>16,n=0,i=e.length;n<i;n++)A=(A+e[n])%65521,r=(r+A)%65521;return(r<<16|A)>>>0},yt=(vt={}).Adler32=(((ft=(ht=function(t){if(!(this instanceof ht))throw new TypeError("Constructor cannot called be as a function.");if(!isFinite(t=null==t?1:+t))throw new Error("First arguments needs to be a finite number.");this.checksum=t>>>0}).prototype={}).constructor=ht).from=((ct=function(t){if(!(this instanceof ht))throw new TypeError("Constructor cannot called be as a function.");if(null==t)throw new Error("First argument needs to be a string.");this.checksum=wt(1,t.toString())}).prototype=ft,ct),ht.fromUtf8=((ut=function(t){if(!(this instanceof ht))throw new TypeError("Constructor cannot called be as a function.");if(null==t)throw new Error("First argument needs to be a string.");var e=Bt(t.toString());this.checksum=wt(1,e)}).prototype=ft,ut),dt&&(ht.fromBuffer=((lt=function(t){if(!(this instanceof ht))throw new TypeError("Constructor cannot called be as a function.");if(!gt(t))throw new Error("First argument needs to be ArrayBuffer.");var e=new Uint8Array(t);return this.checksum=mt(1,e)}).prototype=ft,lt)),ft.update=function(t){if(null==t)throw new Error("First argument needs to be a string.");return t=t.toString(),this.checksum=wt(this.checksum,t)},ft.updateUtf8=function(t){if(null==t)throw new Error("First argument needs to be a string.");var e=Bt(t.toString());return this.checksum=wt(this.checksum,e)},dt&&(ft.updateBuffer=function(t){if(!gt(t))throw new Error("First argument needs to be ArrayBuffer.");var e=new Uint8Array(t);return this.checksum=mt(this.checksum,e)}),ft.clone=function(){return new yt(this.checksum)},ht),vt.from=function(t){if(null==t)throw new Error("First argument needs to be a string.");return wt(1,t.toString())},vt.fromUtf8=function(t){if(null==t)throw new Error("First argument needs to be a string.");var e=Bt(t.toString());return wt(1,e)},dt&&(vt.fromBuffer=function(t){if(!gt(t))throw new Error("First argument need to be ArrayBuffer.");var e=new Uint8Array(t);return mt(1,e)}),vt),function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var d,p,l,h,n,i,o,s=e,g=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],B=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],w={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},a={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},c=["(",")","(","<",">","<","[","]","[","{","}","{","\xab","\xbb","\xab","\u2039","\u203a","\u2039","\u2045","\u2046","\u2045","\u207d","\u207e","\u207d","\u208d","\u208e","\u208d","\u2264","\u2265","\u2264","\u2329","\u232a","\u2329","\ufe59","\ufe5a","\ufe59","\ufe5b","\ufe5c","\ufe5b","\ufe5d","\ufe5e","\ufe5d","\ufe64","\ufe65","\ufe64"],u=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),m=!1,v=0;this.__bidiEngine__={};var y=function(t){var e=t.charCodeAt(),A=e>>8,r=a[A];return void 0!==r?s[256*r+(255&e)]:252===A||253===A?"AL":u.test(A)?"L":8===A?"R":"N"},f=function(t){for(var e,A=0;A<t.length;A++){if("L"===(e=y(t.charAt(A))))return!1;if("R"===e)return!0}return!1},C=function(t,e,A,r){var n,i,o,s,a=e[r];switch(a){case"L":case"R":m=!1;break;case"N":case"AN":break;case"EN":m&&(a="AN");break;case"AL":m=!0,a="R";break;case"WS":a="N";break;case"CS":r<1||r+1>=e.length||"EN"!==(n=A[r-1])&&"AN"!==n||"EN"!==(i=e[r+1])&&"AN"!==i?a="N":m&&(i="AN"),a=i===n?i:"N";break;case"ES":a="EN"===(n=0<r?A[r-1]:"B")&&r+1<e.length&&"EN"===e[r+1]?"EN":"N";break;case"ET":if(0<r&&"EN"===A[r-1]){a="EN";break}if(m){a="N";break}for(o=r+1,s=e.length;o<s&&"ET"===e[o];)o++;a=o<s&&"EN"===e[o]?"EN":"N";break;case"NSM":if(l&&!h){for(s=e.length,o=r+1;o<s&&"NSM"===e[o];)o++;if(o<s){var c=t[r],u=1425<=c&&c<=2303||64286===c;if(n=e[o],u&&("R"===n||"AL"===n)){a="R";break}}}a=r<1||"B"===(n=e[r-1])?"N":A[r-1];break;case"B":d=!(m=!1),a=v;break;case"S":p=!0,a="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":m=!1;break;case"BN":a="N"}return a},Q=function(t,e,A){var r=t.split("");return A&&F(r,A,{hiLevel:v}),r.reverse(),e&&e.reverse(),r.join("")},F=function(t,e,A){var r,n,i,o,s,a=-1,c=t.length,u=0,l=[],h=v?B:g,f=[];for(p=d=m=!1,n=0;n<c;n++)f[n]=y(t[n]);for(i=0;i<c;i++){if(s=u,l[i]=C(t,f,l,i),r=240&(u=h[s][w[l[i]]]),u&=15,e[i]=o=h[u][5],0<r)if(16===r){for(n=a;n<i;n++)e[n]=1;a=-1}else a=-1;if(h[u][6])-1===a&&(a=i);else if(-1<a){for(n=a;n<i;n++)e[n]=o;a=-1}"B"===f[i]&&(e[i]=0),A.hiLevel|=o}p&&function(t,e,A){for(var r=0;r<A;r++)if("S"===t[r]){e[r]=v;for(var n=r-1;0<=n&&"WS"===t[n];n--)e[n]=v}}(f,e,c)},b=function(t,e,A,r,n){if(!(n.hiLevel<t)){if(1===t&&1===v&&!d)return e.reverse(),void(A&&A.reverse());for(var i,o,s,a,c=e.length,u=0;u<c;){if(r[u]>=t){for(s=u+1;s<c&&r[s]>=t;)s++;for(a=u,o=s-1;a<o;a++,o--)i=e[a],e[a]=e[o],e[o]=i,A&&(i=A[a],A[a]=A[o],A[o]=i);u=s}u++}}},U=function(t,e,A){var r=t.split(""),n={hiLevel:v};return A||(A=[]),F(r,A,n),function(t,e,A){if(0!==A.hiLevel&&o)for(var r,n=0;n<t.length;n++)1===e[n]&&0<=(r=c.indexOf(t[n]))&&(t[n]=c[r+1])}(r,A,n),b(2,r,e,A,n),b(1,r,e,A,n),r.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,A){if(function(t,e){if(e)for(var A=0;A<t.length;A++)e[A]=A;void 0===h&&(h=f(t)),void 0===i&&(i=f(t))}(t,e),l||!n||i)if(l&&n&&h^i)v=h?1:0,t=Q(t,e,A);else if(!l&&n&&i)v=h?1:0,t=U(t,e,A),t=Q(t,e);else if(!l||h||n||i){if(l&&!n&&h^i)t=Q(t,e),t=h?(v=0,U(t,e,A)):(v=1,t=U(t,e,A),Q(t,e));else if(l&&h&&!n&&i)v=1,t=U(t,e,A),t=Q(t,e);else if(!l&&!n&&h^i){var r=o;h?(v=1,t=U(t,e,A),v=0,o=!1,t=U(t,e,A),o=r):(v=0,t=U(t,e,A),t=Q(t,e),o=!(v=1),t=U(t,e,A),o=r,t=Q(t,e))}}else v=0,t=U(t,e,A);else v=h?1:0,t=U(t,e,A);return t},this.__bidiEngine__.setOptions=function(t){t&&(l=t.isInputVisual,n=t.isOutputVisual,h=t.isInputRtl,i=t.isOutputRtl,o=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],i=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text,A=(t.x,t.y,t.options||{}),r=(t.mutex,A.lang,[]);if("[object Array]"===Object.prototype.toString.call(e)){var n=0;for(r=[],n=0;n<e.length;n+=1)"[object Array]"===Object.prototype.toString.call(e[n])?r.push([i.doBidiReorder(e[n][0]),e[n][1],e[n][2]]):r.push([i.doBidiReorder(e[n])]);t.text=r}else t.text=i.doBidiReorder(e)}])}(at),window.tmp=Ut,Et.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var A=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:n,green:r,blue:A,quad:i}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},Et.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(t){console.log("bit decode error:"+t)}},Et.prototype.bit1=function(){var t=Math.ceil(this.width/8),e=t%4,A=0<=this.height?this.height-1:-this.height;for(A=this.height-1;0<=A;A--){for(var r=this.bottom_up?A:this.height-1-A,n=0;n<t;n++)for(var i=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+8*n*4,s=0;s<8&&8*n+s<this.width;s++){var a=this.palette[i>>7-s&1];this.data[o+4*s]=a.blue,this.data[o+4*s+1]=a.green,this.data[o+4*s+2]=a.red,this.data[o+4*s+3]=255}0!=e&&(this.pos+=4-e)}},Et.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,A=this.height-1;0<=A;A--){for(var r=this.bottom_up?A:this.height-1-A,n=0;n<t;n++){var i=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+2*n*4,s=i>>4,a=15&i,c=this.palette[s];if(this.data[o]=c.blue,this.data[o+1]=c.green,this.data[o+2]=c.red,this.data[o+3]=255,2*n+1>=this.width)break;c=this.palette[a],this.data[o+4]=c.blue,this.data[o+4+1]=c.green,this.data[o+4+2]=c.red,this.data[o+4+3]=255}0!=e&&(this.pos+=4-e)}},Et.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;0<=e;e--){for(var A=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var n=this.datav.getUint8(this.pos++,!0),i=A*this.width*4+4*r;if(n<this.palette.length){var o=this.palette[n];this.data[i]=o.red,this.data[i+1]=o.green,this.data[i+2]=o.blue,this.data[i+3]=255}else this.data[i]=255,this.data[i+1]=255,this.data[i+2]=255,this.data[i+3]=255}0!=t&&(this.pos+=4-t)}},Et.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),A=this.height-1;0<=A;A--){for(var r=this.bottom_up?A:this.height-1-A,n=0;n<this.width;n++){var i=this.datav.getUint16(this.pos,!0);this.pos+=2;var o=(i&e)/e*255|0,s=(i>>5&e)/e*255|0,a=(i>>10&e)/e*255|0,c=i>>15?255:0,u=r*this.width*4+4*n;this.data[u]=a,this.data[u+1]=s,this.data[u+2]=o,this.data[u+3]=c}this.pos+=t}},Et.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),A=parseInt("111111",2),r=this.height-1;0<=r;r--){for(var n=this.bottom_up?r:this.height-1-r,i=0;i<this.width;i++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(o&e)/e*255|0,a=(o>>5&A)/A*255|0,c=(o>>11)/e*255|0,u=n*this.width*4+4*i;this.data[u]=c,this.data[u+1]=a,this.data[u+2]=s,this.data[u+3]=255}this.pos+=t}},Et.prototype.bit24=function(){for(var t=this.height-1;0<=t;t--){for(var e=this.bottom_up?t:this.height-1-t,A=0;A<this.width;A++){var r=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=e*this.width*4+4*A;this.data[o]=i,this.data[o+1]=n,this.data[o+2]=r,this.data[o+3]=255}this.pos+=this.width%4}},Et.prototype.bit32=function(){for(var t=this.height-1;0<=t;t--)for(var e=this.bottom_up?t:this.height-1-t,A=0;A<this.width;A++){var r=this.datav.getUint8(this.pos++,!0),n=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*A;this.data[s]=i,this.data[s+1]=n,this.data[s+2]=r,this.data[s+3]=o}},Et.prototype.getData=function(){return this.data},window.tmp=Et,function(t){var d=15,p=573,e=[0,1,2,3,4,4,5,5,6,6,6,6,7,7,7,7,8,8,8,8,8,8,8,8,9,9,9,9,9,9,9,9,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,10,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,11,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,13,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,14,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,15,0,0,16,17,18,18,19,19,20,20,20,20,21,21,21,21,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,28,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29,29];function lt(){var f=this;function a(t,e){for(var A=0;A|=1&t,t>>>=1,A<<=1,0<--e;);return A>>>1}f.build_tree=function(t){var e,A,r,n=f.dyn_tree,i=f.stat_desc.static_tree,o=f.stat_desc.elems,s=-1;for(t.heap_len=0,t.heap_max=p,e=0;e<o;e++)0!==n[2*e]?(t.heap[++t.heap_len]=s=e,t.depth[e]=0):n[2*e+1]=0;for(;t.heap_len<2;)n[2*(r=t.heap[++t.heap_len]=s<2?++s:0)]=1,t.depth[r]=0,t.opt_len--,i&&(t.static_len-=i[2*r+1]);for(f.max_code=s,e=Math.floor(t.heap_len/2);1<=e;e--)t.pqdownheap(n,e);for(r=o;e=t.heap[1],t.heap[1]=t.heap[t.heap_len--],t.pqdownheap(n,1),A=t.heap[1],t.heap[--t.heap_max]=e,t.heap[--t.heap_max]=A,n[2*r]=n[2*e]+n[2*A],t.depth[r]=Math.max(t.depth[e],t.depth[A])+1,n[2*e+1]=n[2*A+1]=r,t.heap[1]=r++,t.pqdownheap(n,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t){var e,A,r,n,i,o,s=f.dyn_tree,a=f.stat_desc.static_tree,c=f.stat_desc.extra_bits,u=f.stat_desc.extra_base,l=f.stat_desc.max_length,h=0;for(n=0;n<=d;n++)t.bl_count[n]=0;for(s[2*t.heap[t.heap_max]+1]=0,e=t.heap_max+1;e<p;e++)l<(n=s[2*s[2*(A=t.heap[e])+1]+1]+1)&&(n=l,h++),s[2*A+1]=n,A>f.max_code||(t.bl_count[n]++,i=0,u<=A&&(i=c[A-u]),o=s[2*A],t.opt_len+=o*(n+i),a&&(t.static_len+=o*(a[2*A+1]+i)));if(0!==h){do{for(n=l-1;0===t.bl_count[n];)n--;t.bl_count[n]--,t.bl_count[n+1]+=2,t.bl_count[l]--,h-=2}while(0<h);for(n=l;0!==n;n--)for(A=t.bl_count[n];0!==A;)(r=t.heap[--e])>f.max_code||(s[2*r+1]!=n&&(t.opt_len+=(n-s[2*r+1])*s[2*r],s[2*r+1]=n),A--)}}(t),function(t,e,A){var r,n,i,o=[],s=0;for(r=1;r<=d;r++)o[r]=s=s+A[r-1]<<1;for(n=0;n<=e;n++)0!==(i=t[2*n+1])&&(t[2*n]=a(o[i]++,i))}(n,f.max_code,t.bl_count)}}function ht(t,e,A,r,n){this.static_tree=t,this.extra_bits=e,this.extra_base=A,this.elems=r,this.max_length=n}lt._length_code=[0,1,2,3,4,5,6,7,8,8,9,9,10,10,11,11,12,12,12,12,13,13,13,13,14,14,14,14,15,15,15,15,16,16,16,16,16,16,16,16,17,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,25,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,26,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,27,28],lt.base_length=[0,1,2,3,4,5,6,7,8,10,12,14,16,20,24,28,32,40,48,56,64,80,96,112,128,160,192,224,0],lt.base_dist=[0,1,2,3,4,6,8,12,16,24,32,48,64,96,128,192,256,384,512,768,1024,1536,2048,3072,4096,6144,8192,12288,16384,24576],lt.d_code=function(t){return t<256?e[t]:e[256+(t>>>7)]},lt.extra_lbits=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],lt.extra_dbits=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],lt.extra_blbits=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],lt.bl_order=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],ht.static_ltree=[12,8,140,8,76,8,204,8,44,8,172,8,108,8,236,8,28,8,156,8,92,8,220,8,60,8,188,8,124,8,252,8,2,8,130,8,66,8,194,8,34,8,162,8,98,8,226,8,18,8,146,8,82,8,210,8,50,8,178,8,114,8,242,8,10,8,138,8,74,8,202,8,42,8,170,8,106,8,234,8,26,8,154,8,90,8,218,8,58,8,186,8,122,8,250,8,6,8,134,8,70,8,198,8,38,8,166,8,102,8,230,8,22,8,150,8,86,8,214,8,54,8,182,8,118,8,246,8,14,8,142,8,78,8,206,8,46,8,174,8,110,8,238,8,30,8,158,8,94,8,222,8,62,8,190,8,126,8,254,8,1,8,129,8,65,8,193,8,33,8,161,8,97,8,225,8,17,8,145,8,81,8,209,8,49,8,177,8,113,8,241,8,9,8,137,8,73,8,201,8,41,8,169,8,105,8,233,8,25,8,153,8,89,8,217,8,57,8,185,8,121,8,249,8,5,8,133,8,69,8,197,8,37,8,165,8,101,8,229,8,21,8,149,8,85,8,213,8,53,8,181,8,117,8,245,8,13,8,141,8,77,8,205,8,45,8,173,8,109,8,237,8,29,8,157,8,93,8,221,8,61,8,189,8,125,8,253,8,19,9,275,9,147,9,403,9,83,9,339,9,211,9,467,9,51,9,307,9,179,9,435,9,115,9,371,9,243,9,499,9,11,9,267,9,139,9,395,9,75,9,331,9,203,9,459,9,43,9,299,9,171,9,427,9,107,9,363,9,235,9,491,9,27,9,283,9,155,9,411,9,91,9,347,9,219,9,475,9,59,9,315,9,187,9,443,9,123,9,379,9,251,9,507,9,7,9,263,9,135,9,391,9,71,9,327,9,199,9,455,9,39,9,295,9,167,9,423,9,103,9,359,9,231,9,487,9,23,9,279,9,151,9,407,9,87,9,343,9,215,9,471,9,55,9,311,9,183,9,439,9,119,9,375,9,247,9,503,9,15,9,271,9,143,9,399,9,79,9,335,9,207,9,463,9,47,9,303,9,175,9,431,9,111,9,367,9,239,9,495,9,31,9,287,9,159,9,415,9,95,9,351,9,223,9,479,9,63,9,319,9,191,9,447,9,127,9,383,9,255,9,511,9,0,7,64,7,32,7,96,7,16,7,80,7,48,7,112,7,8,7,72,7,40,7,104,7,24,7,88,7,56,7,120,7,4,7,68,7,36,7,100,7,20,7,84,7,52,7,116,7,3,8,131,8,67,8,195,8,35,8,163,8,99,8,227,8],ht.static_dtree=[0,5,16,5,8,5,24,5,4,5,20,5,12,5,28,5,2,5,18,5,10,5,26,5,6,5,22,5,14,5,30,5,1,5,17,5,9,5,25,5,5,5,21,5,13,5,29,5,3,5,19,5,11,5,27,5,7,5,23,5],ht.static_l_desc=new ht(ht.static_ltree,lt.extra_lbits,257,286,d),ht.static_d_desc=new ht(ht.static_dtree,lt.extra_dbits,0,30,d),ht.static_bl_desc=new ht(null,lt.extra_blbits,0,19,7);function A(t,e,A,r,n){this.good_length=t,this.max_lazy=e,this.nice_length=A,this.max_chain=r,this.func=n}var ft=[new A(0,0,0,0,0),new A(4,4,8,4,1),new A(4,5,16,8,1),new A(4,6,32,32,1),new A(4,4,16,16,2),new A(8,16,32,32,2),new A(8,16,128,128,2),new A(8,32,128,256,2),new A(32,128,258,1024,2),new A(32,258,258,4096,2)],dt=["need dictionary","stream end","","","stream error","data error","","buffer error","",""];function pt(t,e,A,r){var n=t[2*e],i=t[2*A];return n<i||n==i&&r[e]<=r[A]}function r(){var a,c,u,l,h,f,d,p,n,g,B,w,m,o,v,y,C,Q,F,b,U,E,N,L,H,S,x,_,I,T,s,O,R,P,M,K,D,i,k,z,j,q=this,V=new lt,X=new lt,G=new lt;function J(){var t;for(t=0;t<286;t++)s[2*t]=0;for(t=0;t<30;t++)O[2*t]=0;for(t=0;t<19;t++)R[2*t]=0;s[512]=1,q.opt_len=q.static_len=0,K=i=0}function W(t,e){var A,r,n=-1,i=t[1],o=0,s=7,a=4;for(0===i&&(s=138,a=3),t[2*(e+1)+1]=65535,A=0;A<=e;A++)r=i,i=t[2*(A+1)+1],++o<s&&r==i||(o<a?R[2*r]+=o:0!==r?(r!=n&&R[2*r]++,R[32]++):o<=10?R[34]++:R[36]++,n=r,a=(o=0)===i?(s=138,3):r==i?(s=6,3):(s=7,4))}function Y(t){q.pending_buf[q.pending++]=t}function Z(t){Y(255&t),Y(t>>>8&255)}function $(t,e){var A,r=e;16-r<j?(Z(z|=(A=t)<<j&65535),z=A>>>16-j,j+=r-16):(z|=t<<j&65535,j+=r)}function tt(t,e){var A=2*t;$(65535&e[A],65535&e[A+1])}function et(t,e){var A,r,n=-1,i=t[1],o=0,s=7,a=4;for(0===i&&(s=138,a=3),A=0;A<=e;A++)if(r=i,i=t[2*(A+1)+1],!(++o<s&&r==i)){if(o<a)for(;tt(r,R),0!=--o;);else 0!==r?(r!=n&&(tt(r,R),o--),tt(16,R),$(o-3,2)):o<=10?(tt(17,R),$(o-3,3)):(tt(18,R),$(o-11,7));n=r,a=(o=0)===i?(s=138,3):r==i?(s=6,3):(s=7,4)}}function At(){16==j?(Z(z),j=z=0):8<=j&&(Y(255&z),z>>>=8,j-=8)}function rt(t,e){var A,r,n;if(q.pending_buf[D+2*K]=t>>>8&255,q.pending_buf[D+2*K+1]=255&t,q.pending_buf[P+K]=255&e,K++,0===t?s[2*e]++:(i++,t--,s[2*(lt._length_code[e]+256+1)]++,O[2*lt.d_code(t)]++),0==(8191&K)&&2<x){for(A=8*K,r=U-C,n=0;n<30;n++)A+=O[2*n]*(5+lt.extra_dbits[n]);if(A>>>=3,i<Math.floor(K/2)&&A<Math.floor(r/2))return!0}return K==M-1}function nt(t,e){var A,r,n,i,o=0;if(0!==K)for(;A=q.pending_buf[D+2*o]<<8&65280|255&q.pending_buf[D+2*o+1],r=255&q.pending_buf[P+o],o++,0===A?tt(r,t):(tt((n=lt._length_code[r])+256+1,t),0!==(i=lt.extra_lbits[n])&&$(r-=lt.base_length[n],i),tt(n=lt.d_code(--A),e),0!==(i=lt.extra_dbits[n])&&$(A-=lt.base_dist[n],i)),o<K;);tt(256,t),k=t[513]}function it(){8<j?Z(z):0<j&&Y(255&z),j=z=0}function ot(t,e,A){var r,n,i;$(0+(A?1:0),3),r=t,n=e,i=!0,it(),k=8,i&&(Z(n),Z(~n)),q.pending_buf.set(p.subarray(r,r+n),q.pending),q.pending+=n}function e(t,e,A){var r,n,i=0;0<x?(V.build_tree(q),X.build_tree(q),i=function(){var t;for(W(s,V.max_code),W(O,X.max_code),G.build_tree(q),t=18;3<=t&&0===R[2*lt.bl_order[t]+1];t--);return q.opt_len+=3*(t+1)+5+5+4,t}(),r=q.opt_len+3+7>>>3,(n=q.static_len+3+7>>>3)<=r&&(r=n)):r=n=e+5,e+4<=r&&-1!=t?ot(t,e,A):n==r?($(2+(A?1:0),3),nt(ht.static_ltree,ht.static_dtree)):($(4+(A?1:0),3),function(t,e,A){var r;for($(t-257,5),$(e-1,5),$(A-4,4),r=0;r<A;r++)$(R[2*lt.bl_order[r]+1],3);et(s,t-1),et(O,e-1)}(V.max_code+1,X.max_code+1,i+1),nt(s,O)),J(),A&&it()}function st(t){e(0<=C?C:-1,U-C,t),C=U,a.flush_pending()}function at(){var t,e,A,r;do{if(0===(r=n-N-U)&&0===U&&0===N)r=h;else if(-1==r)r--;else if(h+h-262<=U){for(p.set(p.subarray(h,h+h),0),E-=h,U-=h,C-=h,A=t=m;e=65535&B[--A],B[A]=h<=e?e-h:0,0!=--t;);for(A=t=h;e=65535&g[--A],g[A]=h<=e?e-h:0,0!=--t;);r+=h}if(0===a.avail_in)return;t=a.read_buf(p,U+N,r),3<=(N+=t)&&(w=((w=255&p[U])<<y^255&p[U+1])&v)}while(N<262&&0!==a.avail_in)}function ct(t){var e,A,r=H,n=U,i=L,o=h-262<U?U-(h-262):0,s=T,a=d,c=U+258,u=p[n+i-1],l=p[n+i];I<=L&&(r>>=2),N<s&&(s=N);do{if(p[(e=t)+i]==l&&p[e+i-1]==u&&p[e]==p[n]&&p[++e]==p[n+1]){n+=2,e++;do{}while(p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&p[++n]==p[++e]&&n<c);if(A=258-(c-n),n=c-258,i<A){if(E=t,s<=(i=A))break;u=p[n+i-1],l=p[n+i]}}}while((t=65535&g[t&a])>o&&0!=--r);return i<=N?i:N}function ut(t){return t.total_in=t.total_out=0,t.msg=null,q.pending=0,q.pending_out=0,c=113,l=0,V.dyn_tree=s,V.stat_desc=ht.static_l_desc,X.dyn_tree=O,X.stat_desc=ht.static_d_desc,G.dyn_tree=R,G.stat_desc=ht.static_bl_desc,j=z=0,k=8,J(),function(){var t;for(n=2*h,t=B[m-1]=0;t<m-1;t++)B[t]=0;S=ft[x].max_lazy,I=ft[x].good_length,T=ft[x].nice_length,H=ft[x].max_chain,Q=L=2,w=b=N=C=U=0}(),0}q.depth=[],q.bl_count=[],q.heap=[],s=[],O=[],R=[],q.pqdownheap=function(t,e){for(var A=q.heap,r=A[e],n=e<<1;n<=q.heap_len&&(n<q.heap_len&&pt(t,A[n+1],A[n],q.depth)&&n++,!pt(t,r,A[n],q.depth));)A[e]=A[n],e=n,n<<=1;A[e]=r},q.deflateInit=function(t,e,A,r,n,i){return r||(r=8),n||(n=8),i||(i=0),t.msg=null,-1==e&&(e=6),n<1||9<n||8!=r||A<9||15<A||e<0||9<e||i<0||2<i?-2:(t.dstate=q,d=(h=1<<(f=A))-1,v=(m=1<<(o=n+7))-1,y=Math.floor((o+3-1)/3),p=new Uint8Array(2*h),g=[],B=[],M=1<<n+6,q.pending_buf=new Uint8Array(4*M),u=4*M,D=Math.floor(M/2),P=3*M,x=e,_=i,ut(t))},q.deflateEnd=function(){return 42!=c&&113!=c&&666!=c?-2:(q.pending_buf=null,p=g=B=null,q.dstate=null,113==c?-3:0)},q.deflateParams=function(t,e,A){var r=0;return-1==e&&(e=6),e<0||9<e||A<0||2<A?-2:(ft[x].func!=ft[e].func&&0!==t.total_in&&(r=t.deflate(1)),x!=e&&(S=ft[x=e].max_lazy,I=ft[x].good_length,T=ft[x].nice_length,H=ft[x].max_chain),_=A,r)},q.deflateSetDictionary=function(t,e,A){var r,n=A,i=0;if(!e||42!=c)return-2;if(n<3)return 0;for(h-262<n&&(i=A-(n=h-262)),p.set(e.subarray(i,i+n),0),C=U=n,w=((w=255&p[0])<<y^255&p[1])&v,r=0;r<=n-3;r++)w=(w<<y^255&p[r+2])&v,g[r&d]=B[w],B[w]=r;return 0},q.deflate=function(t,e){var A,r,n,i,o,s;if(4<e||e<0)return-2;if(!t.next_out||!t.next_in&&0!==t.avail_in||666==c&&4!=e)return t.msg=dt[4],-2;if(0===t.avail_out)return t.msg=dt[7],-5;if(a=t,i=l,l=e,42==c&&(r=8+(f-8<<4)<<8,3<(n=(x-1&255)>>1)&&(n=3),r|=n<<6,0!==U&&(r|=32),c=113,Y((s=r+=31-r%31)>>8&255),Y(255&s)),0!==q.pending){if(a.flush_pending(),0===a.avail_out)return l=-1,0}else if(0===a.avail_in&&e<=i&&4!=e)return a.msg=dt[7],-5;if(666==c&&0!==a.avail_in)return t.msg=dt[7],-5;if(0!==a.avail_in||0!==N||0!=e&&666!=c){switch(o=-1,ft[x].func){case 0:o=function(t){var e,A=65535;for(u-5<A&&(A=u-5);;){if(N<=1){if(at(),0===N&&0==t)return 0;if(0===N)break}if(U+=N,e=C+A,((N=0)===U||e<=U)&&(N=U-e,U=e,st(!1),0===a.avail_out))return 0;if(h-262<=U-C&&(st(!1),0===a.avail_out))return 0}return st(4==t),0===a.avail_out?4==t?2:0:4==t?3:1}(e);break;case 1:o=function(t){for(var e,A=0;;){if(N<262){if(at(),N<262&&0==t)return 0;if(0===N)break}if(3<=N&&(w=(w<<y^255&p[U+2])&v,A=65535&B[w],g[U&d]=B[w],B[w]=U),0!==A&&(U-A&65535)<=h-262&&2!=_&&(Q=ct(A)),3<=Q)if(e=rt(U-E,Q-3),N-=Q,Q<=S&&3<=N){for(Q--;w=(w<<y^255&p[++U+2])&v,A=65535&B[w],g[U&d]=B[w],B[w]=U,0!=--Q;);U++}else U+=Q,Q=0,w=((w=255&p[U])<<y^255&p[U+1])&v;else e=rt(0,255&p[U]),N--,U++;if(e&&(st(!1),0===a.avail_out))return 0}return st(4==t),0===a.avail_out?4==t?2:0:4==t?3:1}(e);break;case 2:o=function(t){for(var e,A,r=0;;){if(N<262){if(at(),N<262&&0==t)return 0;if(0===N)break}if(3<=N&&(w=(w<<y^255&p[U+2])&v,r=65535&B[w],g[U&d]=B[w],B[w]=U),L=Q,F=E,Q=2,0!==r&&L<S&&(U-r&65535)<=h-262&&(2!=_&&(Q=ct(r)),Q<=5&&(1==_||3==Q&&4096<U-E)&&(Q=2)),3<=L&&Q<=L){for(A=U+N-3,e=rt(U-1-F,L-3),N-=L-1,L-=2;++U<=A&&(w=(w<<y^255&p[U+2])&v,r=65535&B[w],g[U&d]=B[w],B[w]=U),0!=--L;);if(b=0,Q=2,U++,e&&(st(!1),0===a.avail_out))return 0}else if(0!==b){if((e=rt(0,255&p[U-1]))&&st(!1),U++,N--,0===a.avail_out)return 0}else b=1,U++,N--}return 0!==b&&(e=rt(0,255&p[U-1]),b=0),st(4==t),0===a.avail_out?4==t?2:0:4==t?3:1}(e)}if(2!=o&&3!=o||(c=666),0==o||2==o)return 0===a.avail_out&&(l=-1),0;if(1==o){if(1==e)$(2,3),tt(256,ht.static_ltree),At(),1+k+10-j<9&&($(2,3),tt(256,ht.static_ltree),At()),k=7;else if(ot(0,0,!1),3==e)for(A=0;A<m;A++)B[A]=0;if(a.flush_pending(),0===a.avail_out)return l=-1,0}}return 4!=e?0:1}}function n(){this.next_in_index=0,this.next_out_index=0,this.avail_in=0,this.total_in=0,this.avail_out=0,this.total_out=0}n.prototype={deflateInit:function(t,e){return this.dstate=new r,e||(e=d),this.dstate.deflateInit(this,t,e)},deflate:function(t){return this.dstate?this.dstate.deflate(this,t):-2},deflateEnd:function(){if(!this.dstate)return-2;var t=this.dstate.deflateEnd();return this.dstate=null,t},deflateParams:function(t,e){return this.dstate?this.dstate.deflateParams(this,t,e):-2},deflateSetDictionary:function(t,e){return this.dstate?this.dstate.deflateSetDictionary(this,t,e):-2},read_buf:function(t,e,A){var r=this.avail_in;return A<r&&(r=A),0===r?0:(this.avail_in-=r,t.set(this.next_in.subarray(this.next_in_index,this.next_in_index+r),e),this.next_in_index+=r,this.total_in+=r,r)},flush_pending:function(){var t=this,e=t.dstate.pending;e>t.avail_out&&(e=t.avail_out),0!==e&&(t.next_out.set(t.dstate.pending_buf.subarray(t.dstate.pending_out,t.dstate.pending_out+e),t.next_out_index),t.next_out_index+=e,t.dstate.pending_out+=e,t.total_out+=e,t.avail_out-=e,t.dstate.pending-=e,0===t.dstate.pending&&(t.dstate.pending_out=0))}};var i=t.zip||t;i.Deflater=i._jzlib_Deflater=function(t){var s=new n,a=new Uint8Array(512),e=t?t.level:-1;void 0===e&&(e=-1),s.deflateInit(e),s.next_out=a,this.append=function(t,e){var A,r=[],n=0,i=0,o=0;if(t.length){s.next_in_index=0,s.next_in=t,s.avail_in=t.length;do{if(s.next_out_index=0,s.avail_out=512,0!=s.deflate(0))throw new Error("deflating: "+s.msg);s.next_out_index&&(512==s.next_out_index?r.push(new Uint8Array(a)):r.push(new Uint8Array(a.subarray(0,s.next_out_index)))),o+=s.next_out_index,e&&0<s.next_in_index&&s.next_in_index!=n&&(e(s.next_in_index),n=s.next_in_index)}while(0<s.avail_in||0===s.avail_out);return A=new Uint8Array(o),r.forEach(function(t){A.set(t,i),i+=t.length}),A}},this.flush=function(){var t,e,A=[],r=0,n=0;do{if(s.next_out_index=0,s.avail_out=512,1!=(t=s.deflate(4))&&0!=t)throw new Error("deflating: "+s.msg);0<512-s.avail_out&&A.push(new Uint8Array(a.subarray(0,s.next_out_index))),n+=s.next_out_index}while(0<s.avail_in||0===s.avail_out);return s.deflateEnd(),e=new Uint8Array(n),A.forEach(function(t){e.set(t,r),r+=t.length}),e}}}("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")()),("undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")()).RGBColor=function(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t=(t=t.replace(/ /g,"")).toLowerCase();var A={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};for(var r in A)t==r&&(t=A[r]);for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],i=0;i<n.length;i++){var o=n[i].re,s=n[i].process,a=o.exec(t);a&&(e=s(a),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:255<this.r?255:this.r,this.g=this.g<0||isNaN(this.g)?0:255<this.g?255:this.g,this.b=this.b<0||isNaN(this.b)?0:255<this.b?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),A=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==A.length&&(A="0"+A),"#"+t+e+A}},function(t){var A="+".charCodeAt(0),r="/".charCodeAt(0),n="0".charCodeAt(0),i="a".charCodeAt(0),o="A".charCodeAt(0),s="-".charCodeAt(0),a="_".charCodeAt(0),u=function(t){var e=t.charCodeAt(0);return e===A||e===s?62:e===r||e===a?63:e<n?-1:e<n+10?e-n+26+26:e<o+26?e-o:e<i+26?e-i+26:void 0};t.API.TTFFont=function(){function n(t,e,A){var r;if(this.rawData=t,r=this.contents=new G(t),this.contents.pos=4,"ttcf"===r.readString(4)){if(!e)throw new Error("Must specify a font name for TTC files.");throw new Error("Font "+e+" not found in TTC file.")}r.pos=0,this.parse(),this.subset=new N(this),this.registerTTF()}return n.open=function(t,e,A,r){if("string"!=typeof A)throw new Error("Invalid argument supplied in TTFFont.open");return new n(function(t){var e,A,r,n,i,o;if(0<t.length%4)throw new Error("Invalid string. Length must be a multiple of 4");var s=t.length;i="="===t.charAt(s-2)?2:"="===t.charAt(s-1)?1:0,o=new Uint8Array(3*t.length/4-i),r=0<i?t.length-4:t.length;var a=0;function c(t){o[a++]=t}for(A=e=0;e<r;e+=4,A+=3)c((16711680&(n=u(t.charAt(e))<<18|u(t.charAt(e+1))<<12|u(t.charAt(e+2))<<6|u(t.charAt(e+3))))>>16),c((65280&n)>>8),c(255&n);return 2===i?c(255&(n=u(t.charAt(e))<<2|u(t.charAt(e+1))>>4)):1===i&&(c((n=u(t.charAt(e))<<10|u(t.charAt(e+1))<<4|u(t.charAt(e+2))>>2)>>8&255),c(255&n)),o}(A),e,r)},n.prototype.parse=function(){return this.directory=new e(this.contents),this.head=new f(this),this.name=new v(this),this.cmap=new B(this),this.toUnicode=new Map,this.hhea=new p(this),this.maxp=new y(this),this.hmtx=new C(this),this.post=new w(this),this.os2=new g(this),this.loca=new E(this),this.glyf=new F(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},n.prototype.registerTTF=function(){var n,t,e,A,r;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var t,e,A,r;for(r=[],t=0,e=(A=this.bbox).length;t<e;t++)n=A[t],r.push(Math.round(n*this.scaleFactor));return r}.call(this),this.stemV=0,this.post.exists?(e=255&(A=this.post.italic_angle),!0&(t=A>>16)&&(t=-(1+(65535^t))),this.italicAngle=+(t+"."+e)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(r=this.familyClass)||2===r||3===r||4===r||5===r||7===r,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},n.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},n.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},n.prototype.widthOfString=function(t,e,A){var r,n,i,o,s;for(n=o=i=0,s=(t=""+t).length;0<=s?o<s:s<o;n=0<=s?++o:--o)r=t.charCodeAt(n),i+=this.widthOfGlyph(this.characterToGlyph(r))+A*(1e3/e)||0;return i*(e/1e3)},n.prototype.lineHeight=function(t,e){var A;return null==e&&(e=!1),A=e?this.lineGap:0,(this.ascender+A-this.decender)/1e3*t},n}();var c,G=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return 2147483648<=(t=this.readUInt32())?t-4294967296:t},t.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return 32768<=(t=this.readUInt16())?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,A,r;for(A=[],e=r=0;0<=t?r<t:t<r;e=0<=t?++r:--r)A[e]=String.fromCharCode(this.readByte());return A.join("")},t.prototype.writeString=function(t){var e,A,r,n;for(n=[],e=A=0,r=t.length;0<=r?A<r:r<A;e=0<=r?++A:--A)n.push(this.writeByte(t.charCodeAt(e)));return n},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,A,r,n,i,o,s;return t=this.readByte(),e=this.readByte(),A=this.readByte(),r=this.readByte(),n=this.readByte(),i=this.readByte(),o=this.readByte(),s=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^A)+4294967296*(255^r)+16777216*(255^n)+65536*(255^i)+256*(255^o)+(255^s)+1):72057594037927940*t+281474976710656*e+1099511627776*A+4294967296*r+16777216*n+65536*i+256*o+s},t.prototype.writeLongLong=function(t){var e,A;return e=Math.floor(t/4294967296),A=4294967295&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(A>>24&255),this.writeByte(A>>16&255),this.writeByte(A>>8&255),this.writeByte(255&A)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,A;for(e=[],A=0;0<=t?A<t:t<A;0<=t?++A:--A)e.push(this.readByte());return e},t.prototype.write=function(t){var e,A,r,n;for(n=[],A=0,r=t.length;A<r;A++)e=t[A],n.push(this.writeByte(e));return n},t}(),e=function(){var d;function t(t){var e,A,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},A=0,r=this.tableCount;0<=r?A<r:r<A;0<=r?++A:--A)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return t.prototype.encode=function(t){var e,A,r,n,i,o,s,a,c,u,l,h,f;for(f in l=Object.keys(t).length,o=Math.log(2),c=16*Math.floor(Math.log(l)/o),n=Math.floor(c/o),a=16*l-c,(A=new G).writeInt(this.scalarType),A.writeShort(l),A.writeShort(c),A.writeShort(n),A.writeShort(a),r=16*l,s=A.pos+r,i=null,h=[],t)for(u=t[f],A.writeString(f),A.writeInt(d(u)),A.writeInt(s),A.writeInt(u.length),h=h.concat(u),"head"===f&&(i=s),s+=u.length;s%4;)h.push(0),s++;return A.write(h),e=2981146554-d(A.data),A.pos=i+8,A.writeUInt32(e),A.data},d=function(t){var e,A,r,n;for(t=Q.call(t);t.length%4;)t.push(0);for(A=new G(t),r=e=0,n=t.length;r<n;r+=4)e+=A.readUInt32();return 4294967295&e},t}(),l={}.hasOwnProperty,h=function(t,e){for(var A in e)l.call(e,A)&&(t[A]=e[A]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t};c=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var f=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="head",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},e.prototype.encode=function(t){var e;return(e=new G).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},e}(),d=function(){function t(A,t){var e,r,n,i,o,s,a,c,u,l,h,f,d,p,g,B,w,m;switch(this.platformID=A.readUInt16(),this.encodingID=A.readShort(),this.offset=t+A.readInt(),u=A.pos,A.pos=this.offset,this.format=A.readUInt16(),this.length=A.readUInt16(),this.language=A.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(s=g=0;g<256;s=++g)this.codeMap[s]=A.readByte();break;case 4:for(h=A.readUInt16(),l=h/2,A.pos+=6,n=function(){var t,e;for(e=[],s=t=0;0<=l?t<l:l<t;s=0<=l?++t:--t)e.push(A.readUInt16());return e}(),A.pos+=2,d=function(){var t,e;for(e=[],s=t=0;0<=l?t<l:l<t;s=0<=l?++t:--t)e.push(A.readUInt16());return e}(),a=function(){var t,e;for(e=[],s=t=0;0<=l?t<l:l<t;s=0<=l?++t:--t)e.push(A.readUInt16());return e}(),c=function(){var t,e;for(e=[],s=t=0;0<=l?t<l:l<t;s=0<=l?++t:--t)e.push(A.readUInt16());return e}(),r=(this.length-A.pos+this.offset)/2,o=function(){var t,e;for(e=[],s=t=0;0<=r?t<r:r<t;s=0<=r?++t:--t)e.push(A.readUInt16());return e}(),s=B=0,m=n.length;B<m;s=++B)for(p=n[s],e=w=f=d[s];f<=p?w<=p:p<=w;e=f<=p?++w:--w)0===c[s]?i=e+a[s]:0!==(i=o[c[s]/2+(e-f)-(l-s)]||0)&&(i+=a[s]),this.codeMap[e]=65535&i}A.pos=u}return t.encode=function(t,e){var A,r,n,i,o,s,a,c,u,l,h,f,d,p,g,B,w,m,v,y,C,Q,F,b,U,E,N,L,H,S,x,_,I,T,O,R,P,M,K,D,k,z,j,q,V,X;switch(L=new G,i=Object.keys(t).sort(function(t,e){return t-e}),e){case"macroman":for(d=0,p=function(){var t,e;for(e=[],f=t=0;t<256;f=++t)e.push(0);return e}(),B={0:0},n={},H=0,I=i.length;H<I;H++)null==B[j=t[r=i[H]]]&&(B[j]=++d),n[r]={old:t[r],new:B[t[r]]},p[r]=B[t[r]];return L.writeUInt16(1),L.writeUInt16(0),L.writeUInt32(12),L.writeUInt16(0),L.writeUInt16(262),L.writeUInt16(0),L.write(p),{charMap:n,subtable:L.data,maxGlyphID:d+1};case"unicode":for(E=[],u=[],B={},A={},g=a=null,S=w=0,T=i.length;S<T;S++)null==B[v=t[r=i[S]]]&&(B[v]=++w),A[r]={old:v,new:B[v]},o=B[v]-r,null!=g&&o===a||(g&&u.push(g),E.push(r),a=o),g=r;for(g&&u.push(g),u.push(65535),E.push(65535),b=2*(F=E.length),Q=2*Math.pow(Math.log(F)/Math.LN2,2),l=Math.log(Q/2)/Math.LN2,C=2*F-Q,s=[],y=[],h=[],f=x=0,O=E.length;x<O;f=++x){if(U=E[f],c=u[f],65535===U){s.push(0),y.push(0);break}if(32768<=U-(N=A[U].new))for(s.push(0),y.push(2*(h.length+F-f)),r=_=U;U<=c?_<=c:c<=_;r=U<=c?++_:--_)h.push(A[r].new);else s.push(N-U),y.push(0)}for(L.writeUInt16(3),L.writeUInt16(1),L.writeUInt32(12),L.writeUInt16(4),L.writeUInt16(16+8*F+2*h.length),L.writeUInt16(0),L.writeUInt16(b),L.writeUInt16(Q),L.writeUInt16(l),L.writeUInt16(C),k=0,R=u.length;k<R;k++)r=u[k],L.writeUInt16(r);for(L.writeUInt16(0),z=0,P=E.length;z<P;z++)r=E[z],L.writeUInt16(r);for(q=0,M=s.length;q<M;q++)o=s[q],L.writeUInt16(o);for(V=0,K=y.length;V<K;V++)m=y[V],L.writeUInt16(m);for(X=0,D=h.length;X<D;X++)d=h[X],L.writeUInt16(d);return{charMap:A,subtable:L.data,maxGlyphID:w+1}}},t}(),B=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="cmap",e.prototype.parse=function(t){var e,A,r;for(t.pos=this.offset,this.version=t.readUInt16(),A=t.readUInt16(),this.tables=[],this.unicode=null,r=0;0<=A?r<A:A<r;0<=A?++r:--r)e=new d(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},e.encode=function(t,e){var A,r;return null==e&&(e="macroman"),A=d.encode(t,e),(r=new G).writeUInt16(0),r.writeUInt16(1),A.table=r.data.concat(A.subtable),A},e}(),p=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="hhea",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},e}(),g=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="OS/2",e.prototype.parse=function(A){if(A.pos=this.offset,this.version=A.readUInt16(),this.averageCharWidth=A.readShort(),this.weightClass=A.readUInt16(),this.widthClass=A.readUInt16(),this.type=A.readShort(),this.ySubscriptXSize=A.readShort(),this.ySubscriptYSize=A.readShort(),this.ySubscriptXOffset=A.readShort(),this.ySubscriptYOffset=A.readShort(),this.ySuperscriptXSize=A.readShort(),this.ySuperscriptYSize=A.readShort(),this.ySuperscriptXOffset=A.readShort(),this.ySuperscriptYOffset=A.readShort(),this.yStrikeoutSize=A.readShort(),this.yStrikeoutPosition=A.readShort(),this.familyClass=A.readShort(),this.panose=function(){var t,e;for(e=[],t=0;t<10;++t)e.push(A.readByte());return e}(),this.charRange=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(A.readInt());return e}(),this.vendorID=A.readString(4),this.selection=A.readShort(),this.firstCharIndex=A.readShort(),this.lastCharIndex=A.readShort(),0<this.version&&(this.ascent=A.readShort(),this.descent=A.readShort(),this.lineGap=A.readShort(),this.winAscent=A.readShort(),this.winDescent=A.readShort(),this.codePageRange=function(){var t,e;for(e=[],t=0;t<2;++t)e.push(A.readInt());return e}(),1<this.version))return this.xHeight=A.readShort(),this.capHeight=A.readShort(),this.defaultChar=A.readShort(),this.breakChar=A.readShort(),this.maxContext=A.readShort()},e}(),w=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="post",e.prototype.parse=function(r){var t,e,A,n;switch(r.pos=this.offset,this.format=r.readInt(),this.italicAngle=r.readInt(),this.underlinePosition=r.readShort(),this.underlineThickness=r.readShort(),this.isFixedPitch=r.readInt(),this.minMemType42=r.readInt(),this.maxMemType42=r.readInt(),this.minMemType1=r.readInt(),this.maxMemType1=r.readInt(),this.format){case 65536:break;case 131072:for(e=r.readUInt16(),this.glyphNameIndex=[],A=0;0<=e?A<e:e<A;0<=e?++A:--A)this.glyphNameIndex.push(r.readUInt16());for(this.names=[],n=[];r.pos<this.offset+this.length;)t=r.readByte(),n.push(this.names.push(r.readString(t)));return n;case 151552:return e=r.readUInt16(),this.offsets=r.read(e);case 196608:break;case 262144:return this.map=function(){var t,e,A;for(A=[],t=0,e=this.file.maxp.numGlyphs;0<=e?t<e:e<t;0<=e?++t:--t)A.push(r.readUInt32());return A}.call(this)}},e}(),m=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},v=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="name",e.prototype.parse=function(t){var e,A,r,n,i,o,s,a,c,u,l,h;for(t.pos=this.offset,t.readShort(),e=t.readShort(),o=t.readShort(),A=[],n=c=0;0<=e?c<e:e<c;n=0<=e?++c:--c)A.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+o+t.readShort()});for(s={},n=u=0,l=A.length;u<l;n=++u)r=A[n],t.pos=r.offset,a=t.readString(r.length),i=new m(a,r),null==s[h=r.nameID]&&(s[h]=[]),s[r.nameID].push(i);this.strings=s,this.copyright=s[0],this.fontFamily=s[1],this.fontSubfamily=s[2],this.uniqueSubfamily=s[3],this.fontName=s[4],this.version=s[5];try{this.postscriptName=s[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(t){this.postscriptName=s[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=s[7],this.manufacturer=s[8],this.designer=s[9],this.description=s[10],this.vendorUrl=s[11],this.designerUrl=s[12],this.license=s[13],this.licenseUrl=s[14],this.preferredFamily=s[15],this.preferredSubfamily=s[17],this.compatibleFull=s[18],this.sampleText=s[19]},e}(),y=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="maxp",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},e}(),C=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="hmtx",e.prototype.parse=function(A){var t,r,n,e,i,o,s;for(A.pos=this.offset,this.metrics=[],e=0,o=this.file.hhea.numberOfMetrics;0<=o?e<o:o<e;0<=o?++e:--e)this.metrics.push({advance:A.readUInt16(),lsb:A.readInt16()});for(r=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var t,e;for(e=[],t=0;0<=r?t<r:r<t;0<=r?++t:--t)e.push(A.readInt16());return e}(),this.widths=function(){var t,e,A,r;for(r=[],t=0,e=(A=this.metrics).length;t<e;t++)n=A[t],r.push(n.advance);return r}.call(this),t=this.widths[this.widths.length-1],s=[],i=0;0<=r?i<r:r<i;0<=r?++i:--i)s.push(this.widths.push(t));return s},e.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},e}(),Q=[].slice,F=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="glyf",e.prototype.parse=function(t){return this.cache={}},e.prototype.glyphFor=function(t){var e,A,r,n,i,o,s,a,c,u;return(t=t)in this.cache?this.cache[t]:(n=this.file.loca,e=this.file.contents,A=n.indexOf(t),0===(r=n.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+A,i=(o=new G(e.read(r))).readShort(),a=o.readShort(),u=o.readShort(),s=o.readShort(),c=o.readShort(),this.cache[t]=-1===i?new U(o,a,u,s,c):new b(o,i,a,u,s,c),this.cache[t]))},e.prototype.encode=function(t,e,A){var r,n,i,o,s;for(i=[],n=[],o=0,s=e.length;o<s;o++)r=t[e[o]],n.push(i.length),r&&(i=i.concat(r.encode(A)));return n.push(i.length),{table:i,offsets:n}},e}(),b=function(){function t(t,e,A,r,n,i){this.raw=t,this.numberOfContours=e,this.xMin=A,this.yMin=r,this.xMax=n,this.yMax=i,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),U=function(){function t(t,e,A,r,n){var i,o;for(this.raw=t,this.xMin=e,this.yMin=A,this.xMax=r,this.yMax=n,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],i=this.raw;o=i.readShort(),this.glyphOffsets.push(i.pos),this.glyphIDs.push(i.readShort()),32&o;)i.pos+=1&o?4:2,128&o?i.pos+=8:64&o?i.pos+=4:8&o&&(i.pos+=2)}return t.prototype.encode=function(t){var e,A,r,n,i;for(A=new G(Q.call(this.raw.data)),e=r=0,n=(i=this.glyphIDs).length;r<n;e=++r)i[e],A.pos=this.glyphOffsets[e];return A.data},t}(),E=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return h(e,c),e.prototype.tag="loca",e.prototype.parse=function(r){var t;return r.pos=this.offset,t=this.file.head.indexToLocFormat,this.offsets=0===t?function(){var t,e,A;for(A=[],t=0,e=this.length;t<e;t+=2)A.push(2*r.readUInt16());return A}.call(this):function(){var t,e,A;for(A=[],t=0,e=this.length;t<e;t+=4)A.push(r.readUInt32());return A}.call(this)},e.prototype.indexOf=function(t){return this.offsets[t]},e.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},e.prototype.encode=function(t,e){for(var A=new Uint32Array(this.offsets.length),r=0,n=0,i=0;i<A.length;++i)if(A[i]=r,n<e.length&&e[n]==i){++n,A[i]=r;var o=this.offsets[i],s=this.offsets[i+1]-o;0<s&&(r+=s)}for(var a=new Array(4*A.length),c=0;c<A.length;++c)a[4*c+3]=255&A[c],a[4*c+2]=(65280&A[c])>>8,a[4*c+1]=(16711680&A[c])>>16,a[4*c]=(4278190080&A[c])>>24;return a},e}(),N=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,A,r,n;for(e in r=this.font.cmap.tables[0].codeMap,t={},n=this.subset)A=n[e],t[e]=r[A];return t},t.prototype.glyphsFor=function(t){var e,A,r,n,i,o,s;for(r={},i=0,o=t.length;i<o;i++)r[n=t[i]]=this.font.glyf.glyphFor(n);for(n in e=[],r)(null!=(A=r[n])?A.compound:void 0)&&e.push.apply(e,A.glyphIDs);if(0<e.length)for(n in s=this.glyphsFor(e))A=s[n],r[n]=A;return r},t.prototype.encode=function(t,e){var A,r,n,i,o,s,a,c,u,l,h,f,d,p,g;for(r in A=B.encode(this.generateCmap(),"unicode"),i=this.glyphsFor(t),h={0:0},g=A.charMap)h[(s=g[r]).old]=s.new;for(f in l=A.maxGlyphID,i)f in h||(h[f]=l++);return c=function(t){var e,A;for(e in A={},t)A[t[e]]=e;return A}(h),u=Object.keys(c).sort(function(t,e){return t-e}),d=function(){var t,e,A;for(A=[],t=0,e=u.length;t<e;t++)o=u[t],A.push(c[o]);return A}(),n=this.font.glyf.encode(i,d,h),a=this.font.loca.encode(n.offsets,d),p={cmap:this.font.cmap.raw(),glyf:n.table,loca:a,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(p["OS/2"]=this.font.os2.raw()),this.font.directory.encode(p)},t}();t.API.PDFObject=function(){var i;function o(){}return i=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},o.convert=function(r){var n,t,e,A;if(Array.isArray(r))return"["+function(){var t,e,A;for(A=[],t=0,e=r.length;t<e;t++)n=r[t],A.push(o.convert(n));return A}().join(" ")+"]";if("string"==typeof r)return"/"+r;if(null!=r?r.isString:void 0)return"("+r+")";if(r instanceof Date)return"(D:"+i(r.getUTCFullYear(),4)+i(r.getUTCMonth(),2)+i(r.getUTCDate(),2)+i(r.getUTCHours(),2)+i(r.getUTCMinutes(),2)+i(r.getUTCSeconds(),2)+"Z)";if("[object Object]"!=={}.toString.call(r))return""+r;for(t in e=["<<"],r)A=r[t],e.push("/"+t+" "+o.convert(A));return e.push(">>"),e.join("\n")},o}()}(at),Ct="undefined"!=typeof self&&self||"undefined"!=typeof window&&window||"undefined"!=typeof xt&&xt||Function('return typeof this === "object" && this.content')()||Function("return this")(),Qt=function(){var c,A,r;function n(t){var e,A,r,n,i,o,s,a,c,u,l,h,f,d;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},o=null;;){switch(e=this.readUInt32(),c=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}.call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":o&&this.animation.frames.push(o),this.pos+=4,o={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},i=this.readUInt16(),n=this.readUInt16()||100,o.delay=1e3*i/n,o.disposeOp=this.data[this.pos++],o.blendOp=this.data[this.pos++],o.data=[];break;case"IDAT":case"fdAT":for("fdAT"===c&&(this.pos+=4,e-=4),t=(null!=o?o.data:void 0)||this.imgData,h=0;0<=e?h<e:e<h;0<=e?++h:--h)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(r=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>r)throw new Error("More transparent colors than palette size");if(0<(u=r-this.transparency.indexed.length))for(f=0;0<=u?f<u:u<f;0<=u?++f:--f)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":s=(l=this.read(e)).indexOf(0),a=String.fromCharCode.apply(String,l.slice(0,s)),this.text[a]=String.fromCharCode.apply(String,l.slice(s+1));break;case"IEND":return o&&this.animation.frames.push(o),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(d=this.colorType)||6===d,A=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*A,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}n.load=function(t,e,A){var r;return"function"==typeof e&&(A=e),(r=new XMLHttpRequest).open("GET",t,!0),r.responseType="arraybuffer",r.onload=function(){var t;return t=new n(new Uint8Array(r.response||r.mozResponseArrayBuffer)),"function"==typeof(null!=e?e.getContext:void 0)&&t.render(e),"function"==typeof A?A(t):void 0},r.send(null)},n.prototype.read=function(t){var e,A;for(A=[],e=0;0<=t?e<t:t<e;0<=t?++e:--e)A.push(this.data[this.pos++]);return A},n.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},n.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},n.prototype.decodePixels=function(S){var x=this.pixelBitlength/8,_=new Uint8Array(this.width*this.height*x),I=0,T=this;if(null==S&&(S=this.imgData),0===S.length)return new Uint8Array(0);function t(t,e,A,r){var n,i,o,s,a,c,u,l,h,f,d,p,g,B,w,m,v,y,C,Q,F,b=Math.ceil((T.width-t)/A),U=Math.ceil((T.height-e)/r),E=T.width==b&&T.height==U;for(B=x*b,p=E?_:new Uint8Array(B*U),c=S.length,i=g=0;g<U&&I<c;){switch(S[I++]){case 0:for(s=v=0;v<B;s=v+=1)p[i++]=S[I++];break;case 1:for(s=y=0;y<B;s=y+=1)n=S[I++],a=s<x?0:p[i-x],p[i++]=(n+a)%256;break;case 2:for(s=C=0;C<B;s=C+=1)n=S[I++],o=(s-s%x)/x,w=g&&p[(g-1)*B+o*x+s%x],p[i++]=(w+n)%256;break;case 3:for(s=Q=0;Q<B;s=Q+=1)n=S[I++],o=(s-s%x)/x,a=s<x?0:p[i-x],w=g&&p[(g-1)*B+o*x+s%x],p[i++]=(n+Math.floor((a+w)/2))%256;break;case 4:for(s=F=0;F<B;s=F+=1)n=S[I++],o=(s-s%x)/x,a=s<x?0:p[i-x],0===g?w=m=0:(w=p[(g-1)*B+o*x+s%x],m=o&&p[(g-1)*B+(o-1)*x+s%x]),u=a+w-m,l=Math.abs(u-a),f=Math.abs(u-w),d=Math.abs(u-m),h=l<=f&&l<=d?a:f<=d?w:m,p[i++]=(n+h)%256;break;default:throw new Error("Invalid filter algorithm: "+S[I-1])}if(!E){var N=((e+g*r)*T.width+t)*x,L=g*B;for(s=0;s<b;s+=1){for(var H=0;H<x;H+=1)_[N++]=p[L++];N+=(A-1)*x}}g++}}return S=(S=new Lt(S)).getBytes(),1==T.interlaceMethod?(t(0,0,8,8),t(4,0,8,8),t(0,4,4,8),t(2,0,4,4),t(0,2,2,4),t(1,0,2,2),t(0,1,1,2)):t(0,0,1,1),_},n.prototype.decodePalette=function(){var t,e,A,r,n,i,o,s,a;for(A=this.palette,i=this.transparency.indexed||[],n=new Uint8Array((i.length||0)+A.length),r=0,A.length,e=o=t=0,s=A.length;o<s;e=o+=3)n[r++]=A[e],n[r++]=A[e+1],n[r++]=A[e+2],n[r++]=null!=(a=i[t++])?a:255;return n},n.prototype.copyToImageData=function(t,e){var A,r,n,i,o,s,a,c,u,l,h;if(r=this.colors,u=null,A=this.hasAlphaChannel,this.palette.length&&(u=null!=(h=this._decodedPalette)?h:this._decodedPalette=this.decodePalette(),r=4,A=!0),c=(n=t.data||t).length,o=u||e,i=s=0,1===r)for(;i<c;)a=u?4*e[i/4]:s,l=o[a++],n[i++]=l,n[i++]=l,n[i++]=l,n[i++]=A?o[a++]:255,s=a;else for(;i<c;)a=u?4*e[i/4]:s,n[i++]=o[a++],n[i++]=o[a++],n[i++]=o[a++],n[i++]=A?o[a++]:255,s=a},n.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};try{A=Ct.document.createElement("canvas"),r=A.getContext("2d")}catch(t){return-1}return c=function(t){var e;return r.width=t.width,r.height=t.height,r.clearRect(0,0,t.width,t.height),r.putImageData(t,0,0),(e=new Image).src=A.toDataURL(),e},n.prototype.decodeFrames=function(t){var e,A,r,n,i,o,s,a;if(this.animation){for(a=[],A=i=0,o=(s=this.animation.frames).length;i<o;A=++i)e=s[A],r=t.createImageData(e.width,e.height),n=this.decodePixels(new Uint8Array(e.data)),this.copyToImageData(r,n),e.imageData=r,a.push(e.image=c(r));return a}},n.prototype.renderFrame=function(t,e){var A,r,n;return A=(r=this.animation.frames)[e],n=r[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=n?n.disposeOp:void 0)?t.clearRect(n.xOffset,n.yOffset,n.width,n.height):2===(null!=n?n.disposeOp:void 0)&&t.putImageData(n.imageData,n.xOffset,n.yOffset),0===A.blendOp&&t.clearRect(A.xOffset,A.yOffset,A.width,A.height),t.drawImage(A.image,A.xOffset,A.yOffset)},n.prototype.animate=function(A){var r,n,i,o,s,t,a=this;return n=0,t=this.animation,o=t.numFrames,i=t.frames,s=t.numPlays,(r=function(){var t,e;if(t=n++%o,e=i[t],a.renderFrame(A,t),1<o&&n/o<s)return a.animation._timeout=setTimeout(r,e.delay)})()},n.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},n.prototype.render=function(t){var e,A;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(A=e.createImageData(this.width,this.height),this.copyToImageData(A,this.decodePixels()),e.putImageData(A,0,0))},n}(),Ct.PNG=Qt;var Nt=function(){function t(){this.pos=0,this.bufferLength=0,this.eof=!1,this.buffer=null}return t.prototype={ensureBuffer:function(t){var e=this.buffer,A=e?e.byteLength:0;if(t<A)return e;for(var r=512;r<t;)r<<=1;for(var n=new Uint8Array(r),i=0;i<A;++i)n[i]=e[i];return this.buffer=n},getByte:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return this.buffer[this.pos++]},getBytes:function(t){var e=this.pos;if(t){this.ensureBuffer(e+t);for(var A=e+t;!this.eof&&this.bufferLength<A;)this.readBlock();var r=this.bufferLength;r<A&&(A=r)}else{for(;!this.eof;)this.readBlock();A=this.bufferLength}return this.pos=A,this.buffer.subarray(e,A)},lookChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos])},getChar:function(){for(var t=this.pos;this.bufferLength<=t;){if(this.eof)return null;this.readBlock()}return String.fromCharCode(this.buffer[this.pos++])},makeSubStream:function(t,e,A){for(var r=t+e;this.bufferLength<=r&&!this.eof;)this.readBlock();return new Stream(this.buffer,t,e,A)},skip:function(t){t||(t=1),this.pos+=t},reset:function(){this.pos=0}},t}(),Lt=function(){if("undefined"!=typeof Uint32Array){var L=new Uint32Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),H=new Uint32Array([3,4,5,6,7,8,9,10,65547,65549,65551,65553,131091,131095,131099,131103,196643,196651,196659,196667,262211,262227,262243,262259,327811,327843,327875,327907,258,258,258]),S=new Uint32Array([1,2,3,4,65541,65543,131081,131085,196625,196633,262177,262193,327745,327777,393345,393409,459009,459137,524801,525057,590849,591361,657409,658433,724993,727041,794625,798721,868353,876545]),x=[new Uint32Array([459008,524368,524304,524568,459024,524400,524336,590016,459016,524384,524320,589984,524288,524416,524352,590048,459012,524376,524312,589968,459028,524408,524344,590032,459020,524392,524328,59e4,524296,524424,524360,590064,459010,524372,524308,524572,459026,524404,524340,590024,459018,524388,524324,589992,524292,524420,524356,590056,459014,524380,524316,589976,459030,524412,524348,590040,459022,524396,524332,590008,524300,524428,524364,590072,459009,524370,524306,524570,459025,524402,524338,590020,459017,524386,524322,589988,524290,524418,524354,590052,459013,524378,524314,589972,459029,524410,524346,590036,459021,524394,524330,590004,524298,524426,524362,590068,459011,524374,524310,524574,459027,524406,524342,590028,459019,524390,524326,589996,524294,524422,524358,590060,459015,524382,524318,589980,459031,524414,524350,590044,459023,524398,524334,590012,524302,524430,524366,590076,459008,524369,524305,524569,459024,524401,524337,590018,459016,524385,524321,589986,524289,524417,524353,590050,459012,524377,524313,589970,459028,524409,524345,590034,459020,524393,524329,590002,524297,524425,524361,590066,459010,524373,524309,524573,459026,524405,524341,590026,459018,524389,524325,589994,524293,524421,524357,590058,459014,524381,524317,589978,459030,524413,524349,590042,459022,524397,524333,590010,524301,524429,524365,590074,459009,524371,524307,524571,459025,524403,524339,590022,459017,524387,524323,589990,524291,524419,524355,590054,459013,524379,524315,589974,459029,524411,524347,590038,459021,524395,524331,590006,524299,524427,524363,590070,459011,524375,524311,524575,459027,524407,524343,590030,459019,524391,524327,589998,524295,524423,524359,590062,459015,524383,524319,589982,459031,524415,524351,590046,459023,524399,524335,590014,524303,524431,524367,590078,459008,524368,524304,524568,459024,524400,524336,590017,459016,524384,524320,589985,524288,524416,524352,590049,459012,524376,524312,589969,459028,524408,524344,590033,459020,524392,524328,590001,524296,524424,524360,590065,459010,524372,524308,524572,459026,524404,524340,590025,459018,524388,524324,589993,524292,524420,524356,590057,459014,524380,524316,589977,459030,524412,524348,590041,459022,524396,524332,590009,524300,524428,524364,590073,459009,524370,524306,524570,459025,524402,524338,590021,459017,524386,524322,589989,524290,524418,524354,590053,459013,524378,524314,589973,459029,524410,524346,590037,459021,524394,524330,590005,524298,524426,524362,590069,459011,524374,524310,524574,459027,524406,524342,590029,459019,524390,524326,589997,524294,524422,524358,590061,459015,524382,524318,589981,459031,524414,524350,590045,459023,524398,524334,590013,524302,524430,524366,590077,459008,524369,524305,524569,459024,524401,524337,590019,459016,524385,524321,589987,524289,524417,524353,590051,459012,524377,524313,589971,459028,524409,524345,590035,459020,524393,524329,590003,524297,524425,524361,590067,459010,524373,524309,524573,459026,524405,524341,590027,459018,524389,524325,589995,524293,524421,524357,590059,459014,524381,524317,589979,459030,524413,524349,590043,459022,524397,524333,590011,524301,524429,524365,590075,459009,524371,524307,524571,459025,524403,524339,590023,459017,524387,524323,589991,524291,524419,524355,590055,459013,524379,524315,589975,459029,524411,524347,590039,459021,524395,524331,590007,524299,524427,524363,590071,459011,524375,524311,524575,459027,524407,524343,590031,459019,524391,524327,589999,524295,524423,524359,590063,459015,524383,524319,589983,459031,524415,524351,590047,459023,524399,524335,590015,524303,524431,524367,590079]),9],_=[new Uint32Array([327680,327696,327688,327704,327684,327700,327692,327708,327682,327698,327690,327706,327686,327702,327694,0,327681,327697,327689,327705,327685,327701,327693,327709,327683,327699,327691,327707,327687,327703,327695,0]),5];return(t.prototype=Object.create(Nt.prototype)).getBits=function(t){for(var e,A=this.codeSize,r=this.codeBuf,n=this.bytes,i=this.bytesPos;A<t;)void 0===(e=n[i++])&&I("Bad encoding in flate stream"),r|=e<<A,A+=8;return e=r&(1<<t)-1,this.codeBuf=r>>t,this.codeSize=A-=t,this.bytesPos=i,e},t.prototype.getCode=function(t){for(var e=t[0],A=t[1],r=this.codeSize,n=this.codeBuf,i=this.bytes,o=this.bytesPos;r<A;){var s;void 0===(s=i[o++])&&I("Bad encoding in flate stream"),n|=s<<r,r+=8}var a=e[n&(1<<A)-1],c=a>>16,u=65535&a;return(0==r||r<c||0==c)&&I("Bad encoding in flate stream"),this.codeBuf=n>>c,this.codeSize=r-c,this.bytesPos=o,u},t.prototype.generateHuffmanTable=function(t){for(var e=t.length,A=0,r=0;r<e;++r)t[r]>A&&(A=t[r]);for(var n=1<<A,i=new Uint32Array(n),o=1,s=0,a=2;o<=A;++o,s<<=1,a<<=1)for(var c=0;c<e;++c)if(t[c]==o){var u=0,l=s;for(r=0;r<o;++r)u=u<<1|1&l,l>>=1;for(r=u;r<n;r+=a)i[r]=o<<16|c;++s}return[i,A]},t.prototype.readBlock=function(){function t(t,e,A,r,n){for(var i=t.getBits(A)+r;0<i--;)e[a++]=n}var e=this.getBits(3);if(1&e&&(this.eof=!0),0!=(e>>=1)){var A,r;if(1==e)A=x,r=_;else if(2==e){for(var n=this.getBits(5)+257,i=this.getBits(5)+1,o=this.getBits(4)+4,s=Array(L.length),a=0;a<o;)s[L[a++]]=this.getBits(3);for(var c=this.generateHuffmanTable(s),u=0,l=(a=0,n+i),h=new Array(l);a<l;){var f=this.getCode(c);16==f?t(this,h,2,3,u):17==f?t(this,h,3,3,u=0):18==f?t(this,h,7,11,u=0):h[a++]=u=f}A=this.generateHuffmanTable(h.slice(0,n)),r=this.generateHuffmanTable(h.slice(n,l))}else I("Unknown block type in flate stream");for(var d=(U=this.buffer)?U.length:0,p=this.bufferLength;;){var g=this.getCode(A);if(g<256)d<=p+1&&(d=(U=this.ensureBuffer(p+1)).length),U[p++]=g;else{if(256==g)return void(this.bufferLength=p);var B=(g=H[g-=257])>>16;0<B&&(B=this.getBits(B));u=(65535&g)+B;g=this.getCode(r),0<(B=(g=S[g])>>16)&&(B=this.getBits(B));var w=(65535&g)+B;d<=p+u&&(d=(U=this.ensureBuffer(p+u)).length);for(var m=0;m<u;++m,++p)U[p]=U[p-w]}}}else{var v,y=this.bytes,C=this.bytesPos;void 0===(v=y[C++])&&I("Bad block header in flate stream");var Q=v;void 0===(v=y[C++])&&I("Bad block header in flate stream"),Q|=v<<8,void 0===(v=y[C++])&&I("Bad block header in flate stream");var F=v;void 0===(v=y[C++])&&I("Bad block header in flate stream"),(F|=v<<8)!=(65535&~Q)&&I("Bad uncompressed block length in flate stream"),this.codeBuf=0,this.codeSize=0;var b=this.bufferLength,U=this.ensureBuffer(b+Q),E=b+Q;this.bufferLength=E;for(var N=b;N<E;++N){if(void 0===(v=y[C++])){this.eof=!0;break}U[N]=v}this.bytesPos=C}},t}function I(t){throw new Error(t)}function t(t){var e=0,A=t[e++],r=t[e++];-1!=A&&-1!=r||I("Invalid header in flate stream"),8!=(15&A)&&I("Unknown compression method in flate stream"),((A<<8)+r)%31!=0&&I("Bad FCHECK in flate stream"),32&r&&I("FDICT bit set in flate stream"),this.bytes=t,this.bytesPos=2,this.codeSize=0,this.codeBuf=0,Nt.call(this)}}();window.tmp=Lt}();try{Ht.exports=jsPDF}catch(t){}}),yt=function(t,e){return(yt=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var A in e)e.hasOwnProperty(A)&&(t[A]=e[A])})(t,e)};function Ct(t,e){function A(){this.constructor=t}yt(t,e),t.prototype=null===e?Object.create(e):(A.prototype=e.prototype,new A)}var Qt=function(){return(Qt=Object.assign||function(t){for(var e,A=1,r=arguments.length;A<r;A++)for(var n in e=arguments[A])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}).apply(this,arguments)};function Ft(i,o,s,a){return new(s=s||Promise)(function(t,e){function A(t){try{n(a.next(t))}catch(t){e(t)}}function r(t){try{n(a.throw(t))}catch(t){e(t)}}function n(e){e.done?t(e.value):new s(function(t){t(e.value)}).then(A,r)}n((a=a.apply(i,o||[])).next())})}function bt(A,r){var n,i,o,t,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return t={next:e(0),throw:e(1),return:e(2)},"function"==typeof Symbol&&(t[Symbol.iterator]=function(){return this}),t;function e(e){return function(t){return function(e){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,i&&(o=2&e[0]?i.return:e[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,e[1])).done)return o;switch(i=0,o&&(e=[2&e[0],o.value]),e[0]){case 0:case 1:o=e;break;case 4:return s.label++,{value:e[1],done:!1};case 5:s.label++,i=e[1],e=[0];continue;case 7:e=s.ops.pop(),s.trys.pop();continue;default:if(!(o=0<(o=s.trys).length&&o[o.length-1])&&(6===e[0]||2===e[0])){s=0;continue}if(3===e[0]&&(!o||e[1]>o[0]&&e[1]<o[3])){s.label=e[1];break}if(6===e[0]&&s.label<o[1]){s.label=o[1],o=e;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(e);break}o[2]&&s.ops.pop(),s.trys.pop();continue}e=r.call(A,s)}catch(t){e=[6,t],i=0}finally{n=o=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,t])}}}var Ut=(Et.prototype.add=function(t,e,A,r){return new Et(this.left+t,this.top+e,this.width+A,this.height+r)},Et.fromClientRect=function(t){return new Et(t.left,t.top,t.width,t.height)},Et);function Et(t,e,A,r){this.left=t,this.top=e,this.width=A,this.height=r}for(var Nt=function(t){return Ut.fromClientRect(t.getBoundingClientRect())},Lt=function(t){for(var e=[],A=0,r=t.length;A<r;){var n=t.charCodeAt(A++);if(55296<=n&&n<=56319&&A<r){var i=t.charCodeAt(A++);56320==(64512&i)?e.push(((1023&n)<<10)+(1023&i)+65536):(e.push(n),A--)}else e.push(n)}return e},Ht=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,t);var A=t.length;if(!A)return"";for(var r=[],n=-1,i="";++n<A;){var o=t[n];o<=65535?r.push(o):(o-=65536,r.push(55296+(o>>10),o%1024+56320)),(n+1===A||16384<r.length)&&(i+=String.fromCharCode.apply(String,r),r.length=0)}return i},St="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",_t="undefined"==typeof Uint8Array?[]:new Uint8Array(256),It=0;It<St.length;It++)_t[St.charCodeAt(It)]=It;function Tt(t,e,A){return t.slice?t.slice(e,A):new Uint16Array(Array.prototype.slice.call(t,e,A))}var Ot=(Rt.prototype.get=function(t){var e;if(0<=t){if(t<55296||56319<t&&t<=65535)return e=((e=this.index[t>>5])<<2)+(31&t),this.data[e];if(t<=65535)return e=((e=this.index[2048+(t-55296>>5)])<<2)+(31&t),this.data[e];if(t<this.highStart)return e=2080+(t>>11),e=this.index[e],e+=t>>5&63,e=((e=this.index[e])<<2)+(31&t),this.data[e];if(t<=1114111)return this.data[this.highValueIndex]}return this.errorValue},Rt);function Rt(t,e,A,r,n,i){this.initialValue=t,this.errorValue=e,this.highStart=A,this.highValueIndex=r,this.index=n,this.data=i}function Pt(t,e,A,r){var n=r[A];if(Array.isArray(t)?-1!==t.indexOf(n):t===n)for(var i=A;i<=r.length;){if((a=r[++i])===e)return 1;if(a!==Wt)break}if(n===Wt)for(i=A;0<i;){var o=r[--i];if(Array.isArray(t)?-1!==t.indexOf(o):t===o)for(var s=A;s<=r.length;){var a;if((a=r[++s])===e)return 1;if(a!==Wt)break}if(o!==Wt)break}}function Mt(t,e){for(var A=t;0<=A;){var r=e[A];if(r!==Wt)return r;A--}return 0}function Kt(t,e,A,r,n){if(0===A[r])return ve;var i=r-1;if(Array.isArray(n)&&!0===n[i])return ve;var o=i-1,s=1+i,a=e[i],c=0<=o?e[o]:0,u=e[s];if(2===a&&3===u)return ve;if(-1!==Qe.indexOf(a))return"!";if(-1!==Qe.indexOf(u))return ve;if(-1!==Fe.indexOf(u))return ve;if(8===Mt(i,e))return"\xf7";if(11===ye.get(t[i])&&(u===pe||u===le||u===he))return ve;if(7===a||7===u)return ve;if(9===a)return ve;if(-1===[Wt,Yt,Zt].indexOf(a)&&9===u)return ve;if(-1!==[$t,te,ee,ie,ce].indexOf(u))return ve;if(Mt(i,e)===ne)return ve;if(Pt(23,ne,i,e))return ve;if(Pt([$t,te],re,i,e))return ve;if(Pt(12,12,i,e))return ve;if(a===Wt)return"\xf7";if(23===a||23===u)return ve;if(16===u||16===a)return"\xf7";if(-1!==[Yt,Zt,re].indexOf(u)||14===a)return ve;if(36===c&&-1!==Ne.indexOf(a))return ve;if(a===ce&&36===u)return ve;if(u===Ae&&-1!==Ce.concat(Ae,ee,oe,pe,le,he).indexOf(a))return ve;if(-1!==Ce.indexOf(u)&&a===oe||-1!==Ce.indexOf(a)&&u===oe)return ve;if(a===ae&&-1!==[pe,le,he].indexOf(u)||-1!==[pe,le,he].indexOf(a)&&u===se)return ve;if(-1!==Ce.indexOf(a)&&-1!==be.indexOf(u)||-1!==be.indexOf(a)&&-1!==Ce.indexOf(u))return ve;if(-1!==[ae,se].indexOf(a)&&(u===oe||-1!==[ne,Zt].indexOf(u)&&e[1+s]===oe)||-1!==[ne,Zt].indexOf(a)&&u===oe||a===oe&&-1!==[oe,ce,ie].indexOf(u))return ve;if(-1!==[oe,ce,ie,$t,te].indexOf(u))for(var l=i;0<=l;){if((h=e[l])===oe)return ve;if(-1===[ce,ie].indexOf(h))break;l--}if(-1!==[ae,se].indexOf(u))for(l=-1!==[$t,te].indexOf(a)?o:i;0<=l;){var h;if((h=e[l])===oe)return ve;if(-1===[ce,ie].indexOf(h))break;l--}if(ge===a&&-1!==[ge,Be,fe,de].indexOf(u)||-1!==[Be,fe].indexOf(a)&&-1!==[Be,we].indexOf(u)||-1!==[we,de].indexOf(a)&&u===we)return ve;if(-1!==Ee.indexOf(a)&&-1!==[Ae,se].indexOf(u)||-1!==Ee.indexOf(u)&&a===ae)return ve;if(-1!==Ce.indexOf(a)&&-1!==Ce.indexOf(u))return ve;if(a===ie&&-1!==Ce.indexOf(u))return ve;if(-1!==Ce.concat(oe).indexOf(a)&&u===ne||-1!==Ce.concat(oe).indexOf(u)&&a===te)return ve;if(41===a&&41===u){for(var f=A[i],d=1;0<f&&41===e[--f];)d++;if(d%2!=0)return ve}return a===le&&u===he?ve:"\xf7"}function Dt(A,t){var e=function(t,n){void 0===n&&(n="strict");var i=[],o=[],s=[];return t.forEach(function(t,e){var A=ye.get(t);if(50<A?(s.push(!0),A-=50):s.push(!1),-1!==["normal","auto","loose"].indexOf(n)&&-1!==[8208,8211,12316,12448].indexOf(t))return o.push(e),i.push(16);if(4!==A&&11!==A)return o.push(e),31===A?i.push("strict"===n?re:pe):A===me||29===A?i.push(ue):43===A?131072<=t&&t<=196605||196608<=t&&t<=262141?i.push(pe):i.push(ue):void i.push(A);if(0===e)return o.push(e),i.push(ue);var r=i[e-1];return-1===Ue.indexOf(r)?(o.push(o[e-1]),i.push(r)):(o.push(e),i.push(ue))}),[o,i,s]}(A,(t=t||{lineBreak:"normal",wordBreak:"normal"}).lineBreak),r=e[0],n=e[1],i=e[2];return"break-all"!==t.wordBreak&&"break-word"!==t.wordBreak||(n=n.map(function(t){return-1!==[oe,ue,me].indexOf(t)?pe:t})),[r,n,"keep-all"===t.wordBreak?i.map(function(t,e){return t&&19968<=A[e]&&A[e]<=40959}):void 0]}var kt,zt,jt,qt,Vt,Xt,Gt,Jt,Wt=10,Yt=13,Zt=15,$t=17,te=18,ee=19,Ae=20,re=21,ne=22,ie=24,oe=25,se=26,ae=27,ce=28,ue=30,le=32,he=33,fe=34,de=35,pe=37,ge=38,Be=39,we=40,me=42,ve="\xd7",ye=(qt=function(t){var e,A,r,n,i,o=.75*t.length,s=t.length,a=0;"="===t[t.length-1]&&(o--,"="===t[t.length-2]&&o--);var c=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(o),u=Array.isArray(c)?c:new Uint8Array(c);for(e=0;e<s;e+=4)A=_t[t.charCodeAt(e)],r=_t[t.charCodeAt(e+1)],n=_t[t.charCodeAt(e+2)],i=_t[t.charCodeAt(e+3)],u[a++]=A<<2|r>>4,u[a++]=(15&r)<<4|n>>2,u[a++]=(3&n)<<6|63&i;return c}("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"),Vt=Array.isArray(qt)?function(t){for(var e=t.length,A=[],r=0;r<e;r+=4)A.push(t[r+3]<<24|t[r+2]<<16|t[r+1]<<8|t[r]);return A}(qt):new Uint32Array(qt),Xt=Array.isArray(qt)?function(t){for(var e=t.length,A=[],r=0;r<e;r+=2)A.push(t[r+1]<<8|t[r]);return A}(qt):new Uint16Array(qt),Gt=Tt(Xt,12,Vt[4]/2),Jt=2===Vt[5]?Tt(Xt,(24+Vt[4])/2):(kt=Vt,zt=Math.ceil((24+Vt[4])/4),kt.slice?kt.slice(zt,jt):new Uint32Array(Array.prototype.slice.call(kt,zt,jt))),new Ot(Vt[0],Vt[1],Vt[2],Vt[3],Gt,Jt)),Ce=[ue,36],Qe=[1,2,3,5],Fe=[Wt,8],be=[ae,se],Ue=Qe.concat(Fe),Ee=[ge,Be,we,fe,de],Ne=[Zt,Yt],Le=(He.prototype.slice=function(){return Ht.apply(void 0,this.codePoints.slice(this.start,this.end))},He);function He(t,e,A,r){this.codePoints=t,this.required="!"===e,this.start=A,this.end=r}var Se,xe;(xe=Se=Se||{})[xe.STRING_TOKEN=0]="STRING_TOKEN",xe[xe.BAD_STRING_TOKEN=1]="BAD_STRING_TOKEN",xe[xe.LEFT_PARENTHESIS_TOKEN=2]="LEFT_PARENTHESIS_TOKEN",xe[xe.RIGHT_PARENTHESIS_TOKEN=3]="RIGHT_PARENTHESIS_TOKEN",xe[xe.COMMA_TOKEN=4]="COMMA_TOKEN",xe[xe.HASH_TOKEN=5]="HASH_TOKEN",xe[xe.DELIM_TOKEN=6]="DELIM_TOKEN",xe[xe.AT_KEYWORD_TOKEN=7]="AT_KEYWORD_TOKEN",xe[xe.PREFIX_MATCH_TOKEN=8]="PREFIX_MATCH_TOKEN",xe[xe.DASH_MATCH_TOKEN=9]="DASH_MATCH_TOKEN",xe[xe.INCLUDE_MATCH_TOKEN=10]="INCLUDE_MATCH_TOKEN",xe[xe.LEFT_CURLY_BRACKET_TOKEN=11]="LEFT_CURLY_BRACKET_TOKEN",xe[xe.RIGHT_CURLY_BRACKET_TOKEN=12]="RIGHT_CURLY_BRACKET_TOKEN",xe[xe.SUFFIX_MATCH_TOKEN=13]="SUFFIX_MATCH_TOKEN",xe[xe.SUBSTRING_MATCH_TOKEN=14]="SUBSTRING_MATCH_TOKEN",xe[xe.DIMENSION_TOKEN=15]="DIMENSION_TOKEN",xe[xe.PERCENTAGE_TOKEN=16]="PERCENTAGE_TOKEN",xe[xe.NUMBER_TOKEN=17]="NUMBER_TOKEN",xe[xe.FUNCTION=18]="FUNCTION",xe[xe.FUNCTION_TOKEN=19]="FUNCTION_TOKEN",xe[xe.IDENT_TOKEN=20]="IDENT_TOKEN",xe[xe.COLUMN_TOKEN=21]="COLUMN_TOKEN",xe[xe.URL_TOKEN=22]="URL_TOKEN",xe[xe.BAD_URL_TOKEN=23]="BAD_URL_TOKEN",xe[xe.CDC_TOKEN=24]="CDC_TOKEN",xe[xe.CDO_TOKEN=25]="CDO_TOKEN",xe[xe.COLON_TOKEN=26]="COLON_TOKEN",xe[xe.SEMICOLON_TOKEN=27]="SEMICOLON_TOKEN",xe[xe.LEFT_SQUARE_BRACKET_TOKEN=28]="LEFT_SQUARE_BRACKET_TOKEN",xe[xe.RIGHT_SQUARE_BRACKET_TOKEN=29]="RIGHT_SQUARE_BRACKET_TOKEN",xe[xe.UNICODE_RANGE_TOKEN=30]="UNICODE_RANGE_TOKEN",xe[xe.WHITESPACE_TOKEN=31]="WHITESPACE_TOKEN",xe[xe.EOF_TOKEN=32]="EOF_TOKEN";function _e(t){return 48<=t&&t<=57}function Ie(t){return _e(t)||65<=t&&t<=70||97<=t&&t<=102}function Te(t){return 10===t||9===t||32===t}function Oe(t){return 97<=(r=e=t)&&r<=122||65<=(A=e)&&A<=90||128<=t||95===t;var e,A,r}function Re(t){return Oe(t)||_e(t)||45===t}function Pe(t,e){return 92===t&&10!==e}function Me(t,e,A){return 45===t?Oe(e)||Pe(e,A):!!Oe(t)||!(92!==t||!Pe(t,e))}function Ke(t,e,A){return 43===t||45===t?!!_e(e)||46===e&&_e(A):_e(46===t?e:t)}var De={type:Se.LEFT_PARENTHESIS_TOKEN},ke={type:Se.RIGHT_PARENTHESIS_TOKEN},ze={type:Se.COMMA_TOKEN},je={type:Se.SUFFIX_MATCH_TOKEN},qe={type:Se.PREFIX_MATCH_TOKEN},Ve={type:Se.COLUMN_TOKEN},Xe={type:Se.DASH_MATCH_TOKEN},Ge={type:Se.INCLUDE_MATCH_TOKEN},Je={type:Se.LEFT_CURLY_BRACKET_TOKEN},We={type:Se.RIGHT_CURLY_BRACKET_TOKEN},Ye={type:Se.SUBSTRING_MATCH_TOKEN},Ze={type:Se.BAD_URL_TOKEN},$e={type:Se.BAD_STRING_TOKEN},tA={type:Se.CDO_TOKEN},eA={type:Se.CDC_TOKEN},AA={type:Se.COLON_TOKEN},rA={type:Se.SEMICOLON_TOKEN},nA={type:Se.LEFT_SQUARE_BRACKET_TOKEN},iA={type:Se.RIGHT_SQUARE_BRACKET_TOKEN},oA={type:Se.WHITESPACE_TOKEN},sA={type:Se.EOF_TOKEN},aA=(cA.prototype.write=function(t){this._value=this._value.concat(Lt(t))},cA.prototype.read=function(){for(var t=[],e=this.consumeToken();e!==sA;)t.push(e),e=this.consumeToken();return t},cA.prototype.consumeToken=function(){var t=this.consumeCodePoint();switch(t){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),A=this.peekCodePoint(1),r=this.peekCodePoint(2);if(Re(e)||Pe(A,r)){var n=Me(e,A,r)?2:1,i=this.consumeName();return{type:Se.HASH_TOKEN,value:i,flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),je;break;case 39:return this.consumeStringToken(39);case 40:return De;case 41:return ke;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Ye;break;case 43:if(Ke(t,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(t),this.consumeNumericToken();break;case 44:return ze;case 45:var o=t,s=this.peekCodePoint(0),a=this.peekCodePoint(1);if(Ke(o,s,a))return this.reconsumeCodePoint(t),this.consumeNumericToken();if(Me(o,s,a))return this.reconsumeCodePoint(t),this.consumeIdentLikeToken();if(45===s&&62===a)return this.consumeCodePoint(),this.consumeCodePoint(),eA;break;case 46:if(Ke(t,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(t),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var c=this.consumeCodePoint();if(42===c&&47===(c=this.consumeCodePoint()))return this.consumeToken();if(-1===c)return this.consumeToken()}break;case 58:return AA;case 59:return rA;case 60:if(33===this.peekCodePoint(0)&&45===this.peekCodePoint(1)&&45===this.peekCodePoint(2))return this.consumeCodePoint(),this.consumeCodePoint(),tA;break;case 64:var u=this.peekCodePoint(0),l=this.peekCodePoint(1),h=this.peekCodePoint(2);if(Me(u,l,h))return i=this.consumeName(),{type:Se.AT_KEYWORD_TOKEN,value:i};break;case 91:return nA;case 92:if(Pe(t,this.peekCodePoint(0)))return this.reconsumeCodePoint(t),this.consumeIdentLikeToken();break;case 93:return iA;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),qe;break;case 123:return Je;case 125:return We;case 117:case 85:var f=this.peekCodePoint(0),d=this.peekCodePoint(1);return 43!==f||!Ie(d)&&63!==d||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(t),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Xe;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),Ve;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Ge;break;case-1:return sA}return Te(t)?(this.consumeWhiteSpace(),oA):_e(t)?(this.reconsumeCodePoint(t),this.consumeNumericToken()):Oe(t)?(this.reconsumeCodePoint(t),this.consumeIdentLikeToken()):{type:Se.DELIM_TOKEN,value:Ht(t)}},cA.prototype.consumeCodePoint=function(){var t=this._value.shift();return void 0===t?-1:t},cA.prototype.reconsumeCodePoint=function(t){this._value.unshift(t)},cA.prototype.peekCodePoint=function(t){return t>=this._value.length?-1:this._value[t]},cA.prototype.consumeUnicodeRangeToken=function(){for(var t=[],e=this.consumeCodePoint();Ie(e)&&t.length<6;)t.push(e),e=this.consumeCodePoint();for(var A=!1;63===e&&t.length<6;)t.push(e),e=this.consumeCodePoint(),A=!0;if(A){var r=parseInt(Ht.apply(void 0,t.map(function(t){return 63===t?48:t})),16),n=parseInt(Ht.apply(void 0,t.map(function(t){return 63===t?70:t})),16);return{type:Se.UNICODE_RANGE_TOKEN,start:r,end:n}}var i=parseInt(Ht.apply(void 0,t),16);if(45===this.peekCodePoint(0)&&Ie(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var o=[];Ie(e)&&o.length<6;)o.push(e),e=this.consumeCodePoint();return n=parseInt(Ht.apply(void 0,o),16),{type:Se.UNICODE_RANGE_TOKEN,start:i,end:n}}return{type:Se.UNICODE_RANGE_TOKEN,start:i,end:i}},cA.prototype.consumeIdentLikeToken=function(){var t=this.consumeName();return"url"===t.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:Se.FUNCTION_TOKEN,value:t}):{type:Se.IDENT_TOKEN,value:t}},cA.prototype.consumeUrlToken=function(){var t=[];if(this.consumeWhiteSpace(),-1===this.peekCodePoint(0))return{type:Se.URL_TOKEN,value:""};var e,A=this.peekCodePoint(0);if(39===A||34===A){var r=this.consumeStringToken(this.consumeCodePoint());return r.type===Se.STRING_TOKEN&&(this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:Se.URL_TOKEN,value:r.value}):(this.consumeBadUrlRemnants(),Ze)}for(;;){var n=this.consumeCodePoint();if(-1===n||41===n)return{type:Se.URL_TOKEN,value:Ht.apply(void 0,t)};if(Te(n))return this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:Se.URL_TOKEN,value:Ht.apply(void 0,t)}):(this.consumeBadUrlRemnants(),Ze);if(34===n||39===n||40===n||0<=(e=n)&&e<=8||11===e||14<=e&&e<=31||127===e)return this.consumeBadUrlRemnants(),Ze;if(92===n){if(!Pe(n,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),Ze;t.push(this.consumeEscapedCodePoint())}else t.push(n)}},cA.prototype.consumeWhiteSpace=function(){for(;Te(this.peekCodePoint(0));)this.consumeCodePoint()},cA.prototype.consumeBadUrlRemnants=function(){for(;;){var t=this.consumeCodePoint();if(41===t||-1===t)return;Pe(t,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},cA.prototype.consumeStringSlice=function(t){for(var e="";0<t;){var A=Math.min(6e4,t);e+=Ht.apply(void 0,this._value.splice(0,A)),t-=A}return this._value.shift(),e},cA.prototype.consumeStringToken=function(t){for(var e="",A=0;;){var r=this._value[A];if(-1===r||void 0===r||r===t)return e+=this.consumeStringSlice(A),{type:Se.STRING_TOKEN,value:e};if(10===r)return this._value.splice(0,A),$e;if(92===r){var n=this._value[A+1];-1!==n&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(A),A=-1,this._value.shift()):Pe(r,n)&&(e+=this.consumeStringSlice(A),e+=Ht(this.consumeEscapedCodePoint()),A=-1))}A++}},cA.prototype.consumeNumber=function(){var t=[],e=4,A=this.peekCodePoint(0);for(43!==A&&45!==A||t.push(this.consumeCodePoint());_e(this.peekCodePoint(0));)t.push(this.consumeCodePoint());A=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(46===A&&_e(r))for(t.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;_e(this.peekCodePoint(0));)t.push(this.consumeCodePoint());A=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((69===A||101===A)&&((43===r||45===r)&&_e(n)||_e(r)))for(t.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;_e(this.peekCodePoint(0));)t.push(this.consumeCodePoint());return[function(t){var e=0,A=1;43!==t[e]&&45!==t[e]||(45===t[e]&&(A=-1),e++);for(var r=[];_e(t[e]);)r.push(t[e++]);var n=r.length?parseInt(Ht.apply(void 0,r),10):0;46===t[e]&&e++;for(var i=[];_e(t[e]);)i.push(t[e++]);var o=i.length,s=o?parseInt(Ht.apply(void 0,i),10):0;69!==t[e]&&101!==t[e]||e++;var a=1;43!==t[e]&&45!==t[e]||(45===t[e]&&(a=-1),e++);for(var c=[];_e(t[e]);)c.push(t[e++]);var u=c.length?parseInt(Ht.apply(void 0,c),10):0;return A*(n+s*Math.pow(10,-o))*Math.pow(10,a*u)}(t),e]},cA.prototype.consumeNumericToken=function(){var t=this.consumeNumber(),e=t[0],A=t[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),i=this.peekCodePoint(2);if(Me(r,n,i)){var o=this.consumeName();return{type:Se.DIMENSION_TOKEN,number:e,flags:A,unit:o}}return 37===r?(this.consumeCodePoint(),{type:Se.PERCENTAGE_TOKEN,number:e,flags:A}):{type:Se.NUMBER_TOKEN,number:e,flags:A}},cA.prototype.consumeEscapedCodePoint=function(){var t,e=this.consumeCodePoint();if(Ie(e)){for(var A=Ht(e);Ie(this.peekCodePoint(0))&&A.length<6;)A+=Ht(this.consumeCodePoint());Te(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(A,16);return 0===r||55296<=(t=r)&&t<=57343||1114111<r?65533:r}return-1===e?65533:e},cA.prototype.consumeName=function(){for(var t="";;){var e=this.consumeCodePoint();if(Re(e))t+=Ht(e);else{if(!Pe(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),t;t+=Ht(this.consumeEscapedCodePoint())}}},cA);function cA(){this._value=[]}var uA=(lA.create=function(t){var e=new aA;return e.write(t),new lA(e.read())},lA.parseValue=function(t){return lA.create(t).parseComponentValue()},lA.parseValues=function(t){return lA.create(t).parseComponentValues()},lA.prototype.parseComponentValue=function(){for(var t=this.consumeToken();t.type===Se.WHITESPACE_TOKEN;)t=this.consumeToken();if(t.type===Se.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(t);for(var e=this.consumeComponentValue();(t=this.consumeToken()).type===Se.WHITESPACE_TOKEN;);if(t.type===Se.EOF_TOKEN)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},lA.prototype.parseComponentValues=function(){for(var t=[];;){var e=this.consumeComponentValue();if(e.type===Se.EOF_TOKEN)return t;t.push(e),t.push()}},lA.prototype.consumeComponentValue=function(){var t=this.consumeToken();switch(t.type){case Se.LEFT_CURLY_BRACKET_TOKEN:case Se.LEFT_SQUARE_BRACKET_TOKEN:case Se.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(t.type);case Se.FUNCTION_TOKEN:return this.consumeFunction(t)}return t},lA.prototype.consumeSimpleBlock=function(t){for(var e={type:t,values:[]},A=this.consumeToken();;){if(A.type===Se.EOF_TOKEN||HA(A,t))return e;this.reconsumeToken(A),e.values.push(this.consumeComponentValue()),A=this.consumeToken()}},lA.prototype.consumeFunction=function(t){for(var e={name:t.value,values:[],type:Se.FUNCTION};;){var A=this.consumeToken();if(A.type===Se.EOF_TOKEN||A.type===Se.RIGHT_PARENTHESIS_TOKEN)return e;this.reconsumeToken(A),e.values.push(this.consumeComponentValue())}},lA.prototype.consumeToken=function(){var t=this._tokens.shift();return void 0===t?sA:t},lA.prototype.reconsumeToken=function(t){this._tokens.unshift(t)},lA);function lA(t){this._tokens=t}function hA(t){return t.type===Se.DIMENSION_TOKEN}function fA(t){return t.type===Se.NUMBER_TOKEN}function dA(t){return t.type===Se.IDENT_TOKEN}function pA(t){return t.type===Se.STRING_TOKEN}function gA(t,e){return dA(t)&&t.value===e}function BA(t){return t.type!==Se.WHITESPACE_TOKEN}function wA(t){return t.type!==Se.WHITESPACE_TOKEN&&t.type!==Se.COMMA_TOKEN}function mA(t){var e=[],A=[];return t.forEach(function(t){if(t.type===Se.COMMA_TOKEN){if(0===A.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(A),void(A=[])}t.type!==Se.WHITESPACE_TOKEN&&A.push(t)}),A.length&&e.push(A),e}function vA(t){return t.type===Se.NUMBER_TOKEN||t.type===Se.DIMENSION_TOKEN}function yA(t){return t.type===Se.PERCENTAGE_TOKEN||vA(t)}function CA(t){return 1<t.length?[t[0],t[1]]:[t[0]]}function QA(t,e,A){var r=t[0],n=t[1];return[IA(r,e),IA(void 0!==n?n:r,A)]}function FA(t){return t.type===Se.DIMENSION_TOKEN&&("deg"===t.unit||"grad"===t.unit||"rad"===t.unit||"turn"===t.unit)}function bA(t){switch(t.filter(dA).map(function(t){return t.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[SA,SA];case"to top":case"bottom":return OA(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[SA,_A];case"to right":case"left":return OA(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[_A,_A];case"to bottom":case"top":return OA(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[_A,SA];case"to left":case"right":return OA(270)}return 0}function UA(t){return 0==(255&t)}function EA(t){var e=255&t,A=255&t>>8,r=255&t>>16,n=255&t>>24;return e<255?"rgba("+n+","+r+","+A+","+e/255+")":"rgb("+n+","+r+","+A+")"}function NA(t,e){if(t.type===Se.NUMBER_TOKEN)return t.number;if(t.type!==Se.PERCENTAGE_TOKEN)return 0;var A=3===e?1:255;return 3===e?t.number/100*A:Math.round(t.number/100*A)}function LA(t){var e=t.filter(wA);if(3===e.length){var A=e.map(NA),r=A[0],n=A[1],i=A[2];return PA(r,n,i,1)}if(4!==e.length)return 0;var o=e.map(NA),s=(r=o[0],n=o[1],i=o[2],o[3]);return PA(r,n,i,s)}var HA=function(t,e){return e===Se.LEFT_CURLY_BRACKET_TOKEN&&t.type===Se.RIGHT_CURLY_BRACKET_TOKEN||(e===Se.LEFT_SQUARE_BRACKET_TOKEN&&t.type===Se.RIGHT_SQUARE_BRACKET_TOKEN||e===Se.LEFT_PARENTHESIS_TOKEN&&t.type===Se.RIGHT_PARENTHESIS_TOKEN)},SA={type:Se.NUMBER_TOKEN,number:0,flags:4},xA={type:Se.PERCENTAGE_TOKEN,number:50,flags:4},_A={type:Se.PERCENTAGE_TOKEN,number:100,flags:4},IA=function(t,e){if(t.type===Se.PERCENTAGE_TOKEN)return t.number/100*e;if(hA(t))switch(t.unit){case"rem":case"em":return 16*t.number;case"px":default:return t.number}return t.number},TA=function(t){if(t.type===Se.DIMENSION_TOKEN)switch(t.unit){case"deg":return Math.PI*t.number/180;case"grad":return Math.PI/200*t.number;case"rad":return t.number;case"turn":return 2*Math.PI*t.number}throw new Error("Unsupported angle type")},OA=function(t){return Math.PI*t/180},RA=function(t){if(t.type===Se.FUNCTION){var e=qA[t.name];if(void 0===e)throw new Error('Attempting to parse an unsupported color function "'+t.name+'"');return e(t.values)}if(t.type===Se.HASH_TOKEN){if(3===t.value.length){var A=t.value.substring(0,1),r=t.value.substring(1,2),n=t.value.substring(2,3);return PA(parseInt(A+A,16),parseInt(r+r,16),parseInt(n+n,16),1)}if(4===t.value.length){A=t.value.substring(0,1),r=t.value.substring(1,2),n=t.value.substring(2,3);var i=t.value.substring(3,4);return PA(parseInt(A+A,16),parseInt(r+r,16),parseInt(n+n,16),parseInt(i+i,16)/255)}if(6===t.value.length){A=t.value.substring(0,2),r=t.value.substring(2,4),n=t.value.substring(4,6);return PA(parseInt(A,16),parseInt(r,16),parseInt(n,16),1)}if(8===t.value.length){A=t.value.substring(0,2),r=t.value.substring(2,4),n=t.value.substring(4,6),i=t.value.substring(6,8);return PA(parseInt(A,16),parseInt(r,16),parseInt(n,16),parseInt(i,16)/255)}}if(t.type===Se.IDENT_TOKEN){var o=VA[t.value.toUpperCase()];if(void 0!==o)return o}return VA.TRANSPARENT},PA=function(t,e,A,r){return(t<<24|e<<16|A<<8|Math.round(255*r)<<0)>>>0};function MA(t,e,A){return A<0&&(A+=1),1<=A&&--A,A<1/6?(e-t)*A*6+t:A<.5?e:A<2/3?6*(e-t)*(2/3-A)+t:t}function KA(t){var e=t.filter(wA),A=e[0],r=e[1],n=e[2],i=e[3],o=(A.type===Se.NUMBER_TOKEN?OA(A.number):TA(A))/(2*Math.PI),s=yA(r)?r.number/100:0,a=yA(n)?n.number/100:0,c=void 0!==i&&yA(i)?IA(i,1):1;if(0==s)return PA(255*a,255*a,255*a,1);var u=a<=.5?a*(1+s):a+s-a*s,l=2*a-u,h=MA(l,u,o+1/3),f=MA(l,u,o),d=MA(l,u,o-1/3);return PA(255*h,255*f,255*d,c)}var DA,kA,zA,jA,qA={hsl:KA,hsla:KA,rgb:LA,rgba:LA},VA={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199};(kA=DA=DA||{})[kA.VALUE=0]="VALUE",kA[kA.LIST=1]="LIST",kA[kA.IDENT_VALUE=2]="IDENT_VALUE",kA[kA.TYPE_VALUE=3]="TYPE_VALUE",kA[kA.TOKEN_VALUE=4]="TOKEN_VALUE",(jA=zA=zA||{})[jA.BORDER_BOX=0]="BORDER_BOX",jA[jA.PADDING_BOX=1]="PADDING_BOX";function XA(t){var e=RA(t[0]),A=t[1];return A&&yA(A)?{color:e,stop:A}:{color:e,stop:null}}function GA(t,A){var e=t[0],r=t[t.length-1];null===e.stop&&(e.stop=SA),null===r.stop&&(r.stop=_A);for(var n=[],i=0,o=0;o<t.length;o++){var s=t[o].stop;if(null!==s){var a=IA(s,A);i<a?n.push(a):n.push(i),i=a}else n.push(null)}var c=null;for(o=0;o<n.length;o++){var u=n[o];if(null===u)null===c&&(c=o);else if(null!==c){for(var l=o-c,h=(u-n[c-1])/(1+l),f=1;f<=l;f++)n[c+f-1]=h*f;c=null}}return t.map(function(t,e){return{color:t.color,stop:Math.max(Math.min(1,n[e]/A),0)}})}function JA(t,e,A){var r,n,i,o,s,a,c,u="number"==typeof t?t:(o=(n=e)/2,s=(i=A)/2,a=IA((r=t)[0],n)-o,c=s-IA(r[1],i),(Math.atan2(c,a)+2*Math.PI)%(2*Math.PI)),l=Math.abs(e*Math.sin(u))+Math.abs(A*Math.cos(u)),h=e/2,f=A/2,d=l/2,p=Math.sin(u-Math.PI/2)*d,g=Math.cos(u-Math.PI/2)*d;return[l,h-g,h+g,f-p,f+p]}function WA(t,e){return Math.sqrt(t*t+e*e)}function YA(t,e,i,o,s){return[[0,0],[0,e],[t,0],[t,e]].reduce(function(t,e){var A=e[0],r=e[1],n=WA(i-A,o-r);return(s?n<t.optimumDistance:n>t.optimumDistance)?{optimumCorner:e,optimumDistance:n}:t},{optimumDistance:s?1/0:-1/0,optimumCorner:null}).optimumCorner}function ZA(t){var n=OA(180),i=[];return mA(t).forEach(function(t,e){if(0===e){var A=t[0];if(A.type===Se.IDENT_TOKEN&&-1!==["top","left","right","bottom"].indexOf(A.value))return void(n=bA(t));if(FA(A))return void(n=(TA(A)+OA(270))%OA(360))}var r=XA(t);i.push(r)}),{angle:n,stops:i,type:hr.LINEAR_GRADIENT}}function $A(t){return 0===t[0]&&255===t[1]&&0===t[2]&&255===t[3]}var tr={name:"background-clip",initialValue:"border-box",prefix:!(jA[jA.CONTENT_BOX=2]="CONTENT_BOX"),type:DA.LIST,parse:function(t){return t.map(function(t){if(dA(t))switch(t.value){case"padding-box":return zA.PADDING_BOX;case"content-box":return zA.CONTENT_BOX}return zA.BORDER_BOX})}},er={name:"background-color",initialValue:"transparent",prefix:!1,type:DA.TYPE_VALUE,format:"color"},Ar=function(t,e,A,r,n){var i="http://www.w3.org/2000/svg",o=document.createElementNS(i,"svg"),s=document.createElementNS(i,"foreignObject");return o.setAttributeNS(null,"width",t.toString()),o.setAttributeNS(null,"height",e.toString()),s.setAttributeNS(null,"width","100%"),s.setAttributeNS(null,"height","100%"),s.setAttributeNS(null,"x",A.toString()),s.setAttributeNS(null,"y",r.toString()),s.setAttributeNS(null,"externalResourcesRequired","true"),o.appendChild(s),s.appendChild(n),o},rr=function(r){return new Promise(function(t,e){var A=new Image;A.onload=function(){return t(A)},A.onerror=e,A.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},nr={get SUPPORT_RANGE_BOUNDS(){var t=function(t){if(t.createRange){var e=t.createRange();if(e.getBoundingClientRect){var A=t.createElement("boundtest");A.style.height="123px",A.style.display="block",t.body.appendChild(A),e.selectNode(A);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(t.body.removeChild(A),123===n)return!0}}return!1}(document);return Object.defineProperty(nr,"SUPPORT_RANGE_BOUNDS",{value:t}),t},get SUPPORT_SVG_DRAWING(){var t=function(t){var e=new Image,A=t.createElement("canvas"),r=A.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),A.toDataURL()}catch(t){return!1}return!0}(document);return Object.defineProperty(nr,"SUPPORT_SVG_DRAWING",{value:t}),t},get SUPPORT_FOREIGNOBJECT_DRAWING(){var t="function"==typeof Array.from&&"function"==typeof window.fetch?function(r){var t=r.createElement("canvas");t.width=100,t.height=100;var n=t.getContext("2d");if(!n)return Promise.reject(!1);n.fillStyle="rgb(0, 255, 0)",n.fillRect(0,0,100,100);var e=new Image,i=t.toDataURL();e.src=i;var A=Ar(100,100,0,0,e);return n.fillStyle="red",n.fillRect(0,0,100,100),rr(A).then(function(t){n.drawImage(t,0,0);var e=n.getImageData(0,0,100,100).data;n.fillStyle="red",n.fillRect(0,0,100,100);var A=r.createElement("div");return A.style.backgroundImage="url("+i+")",A.style.height="100px",$A(e)?rr(Ar(100,100,0,0,A)):Promise.reject(!1)}).then(function(t){return n.drawImage(t,0,0),$A(n.getImageData(0,0,100,100).data)}).catch(function(){return!1})}(document):Promise.resolve(!1);return Object.defineProperty(nr,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:t}),t},get SUPPORT_CORS_IMAGES(){var t=void 0!==(new Image).crossOrigin;return Object.defineProperty(nr,"SUPPORT_CORS_IMAGES",{value:t}),t},get SUPPORT_RESPONSE_TYPE(){var t="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(nr,"SUPPORT_RESPONSE_TYPE",{value:t}),t},get SUPPORT_CORS_XHR(){var t="withCredentials"in new XMLHttpRequest;return Object.defineProperty(nr,"SUPPORT_CORS_XHR",{value:t}),t}},ir=(or.prototype.debug=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,[this.id,this.getTime()+"ms"].concat(t)):this.info.apply(this,t))},or.prototype.getTime=function(){return Date.now()-this.start},or.create=function(t){or.instances[t.id]=new or(t)},or.destroy=function(t){delete or.instances[t]},or.getInstance=function(t){var e=or.instances[t];if(void 0===e)throw new Error("No logger instance found with id "+t);return e},or.prototype.info=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,[this.id,this.getTime()+"ms"].concat(t))},or.prototype.error=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,[this.id,this.getTime()+"ms"].concat(t)):this.info.apply(this,t))},or.instances={},or);function or(t){var e=t.id,A=t.enabled;this.id=e,this.enabled=A,this.start=Date.now()}var sr=(ar.create=function(t,e){return ar._caches[t]=new cr(t,e)},ar.destroy=function(t){delete ar._caches[t]},ar.open=function(t){var e=ar._caches[t];if(void 0!==e)return e;throw new Error('Cache with key "'+t+'" not found')},ar.getOrigin=function(t){var e=ar._link;return e?(e.href=t,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},ar.isSameOrigin=function(t){return ar.getOrigin(t)===ar._origin},ar.setContext=function(t){ar._link=t.document.createElement("a"),ar._origin=ar.getOrigin(t.location.href)},ar.getInstance=function(){var t=ar._current;if(null===t)throw new Error("No cache instance attached");return t},ar.attachInstance=function(t){ar._current=t},ar.detachInstance=function(){ar._current=null},ar._caches={},ar._origin="about:blank",ar._current=null,ar);function ar(){}var cr=(ur.prototype.addImage=function(t){var e=Promise.resolve();return this.has(t)||(vr(t)||Br(t))&&(this._cache[t]=this.loadImage(t)),e},ur.prototype.match=function(t){return this._cache[t]},ur.prototype.loadImage=function(o){return Ft(this,void 0,void 0,function(){var e,r,A,n,i=this;return bt(this,function(t){switch(t.label){case 0:return e=sr.isSameOrigin(o),r=!wr(o)&&!0===this._options.useCORS&&nr.SUPPORT_CORS_IMAGES&&!e,A=!wr(o)&&!e&&"string"==typeof this._options.proxy&&nr.SUPPORT_CORS_XHR&&!r,e||!1!==this._options.allowTaint||wr(o)||A||r?(n=o,A?[4,this.proxy(n)]:[3,2]):[2];case 1:n=t.sent(),t.label=2;case 2:return ir.getInstance(this.id).debug("Added image "+o.substring(0,256)),[4,new Promise(function(t,e){var A=new Image;A.onload=function(){return t(A)},A.onerror=e,(mr(n)||r)&&(A.crossOrigin="anonymous"),A.src=n,!0===A.complete&&setTimeout(function(){return t(A)},500),0<i._options.imageTimeout&&setTimeout(function(){return e("Timed out ("+i._options.imageTimeout+"ms) loading image")},i._options.imageTimeout)})];case 3:return[2,t.sent()]}})})},ur.prototype.has=function(t){return void 0!==this._cache[t]},ur.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},ur.prototype.proxy=function(i){var o=this,s=this._options.proxy;if(!s)throw new Error("No proxy defined");var a=i.substring(0,256);return new Promise(function(e,A){var r=nr.SUPPORT_RESPONSE_TYPE?"blob":"text",n=new XMLHttpRequest;if(n.onload=function(){if(200===n.status)if("text"==r)e(n.response);else{var t=new FileReader;t.addEventListener("load",function(){return e(t.result)},!1),t.addEventListener("error",function(t){return A(t)},!1),t.readAsDataURL(n.response)}else A("Failed to proxy resource "+a+" with status code "+n.status)},n.onerror=A,n.open("GET",s+"?url="+encodeURIComponent(i)+"&responseType="+r),"text"!=r&&n instanceof XMLHttpRequest&&(n.responseType=r),o._options.imageTimeout){var t=o._options.imageTimeout;n.timeout=t,n.ontimeout=function(){return A("Timed out ("+t+"ms) proxying "+a)}}n.send()})},ur);function ur(t,e){this.id=t,this._options=e,this._cache={}}function lr(t){var n=Nr.CIRCLE,i=Hr.FARTHEST_CORNER,o=[],s=[];return mA(t).forEach(function(t,e){var A=!0;if(0===e?A=t.reduce(function(t,e){if(dA(e))switch(e.value){case"center":return s.push(xA),!1;case"top":case"left":return s.push(SA),!1;case"right":case"bottom":return s.push(_A),!1}else if(yA(e)||vA(e))return s.push(e),!1;return t},A):1===e&&(A=t.reduce(function(t,e){if(dA(e))switch(e.value){case"circle":return n=Nr.CIRCLE,!1;case Ur:return n=Nr.ELLIPSE,!1;case Er:case Cr:return i=Hr.CLOSEST_SIDE,!1;case Qr:return i=Hr.FARTHEST_SIDE,!1;case Fr:return i=Hr.CLOSEST_CORNER,!1;case"cover":case br:return i=Hr.FARTHEST_CORNER,!1}else if(vA(e)||yA(e))return Array.isArray(i)||(i=[]),i.push(e),!1;return t},A)),A){var r=XA(t);o.push(r)}}),{size:i,shape:n,stops:o,position:s,type:hr.RADIAL_GRADIENT}}var hr,fr,dr=/^data:image\/svg\+xml/i,pr=/^data:image\/.*;base64,/i,gr=/^data:image\/.*/i,Br=function(t){return nr.SUPPORT_SVG_DRAWING||!yr(t)},wr=function(t){return gr.test(t)},mr=function(t){return pr.test(t)},vr=function(t){return"blob"===t.substr(0,4)},yr=function(t){return"svg"===t.substr(-3).toLowerCase()||dr.test(t)},Cr="closest-side",Qr="farthest-side",Fr="closest-corner",br="farthest-corner",Ur="ellipse",Er="contain";(fr=hr=hr||{})[fr.URL=0]="URL",fr[fr.LINEAR_GRADIENT=1]="LINEAR_GRADIENT",fr[fr.RADIAL_GRADIENT=2]="RADIAL_GRADIENT";var Nr,Lr,Hr,Sr;(Lr=Nr=Nr||{})[Lr.CIRCLE=0]="CIRCLE",Lr[Lr.ELLIPSE=1]="ELLIPSE",(Sr=Hr=Hr||{})[Sr.CLOSEST_SIDE=0]="CLOSEST_SIDE",Sr[Sr.FARTHEST_SIDE=1]="FARTHEST_SIDE",Sr[Sr.CLOSEST_CORNER=2]="CLOSEST_CORNER",Sr[Sr.FARTHEST_CORNER=3]="FARTHEST_CORNER";var xr=function(t){if(t.type===Se.URL_TOKEN){var e={url:t.value,type:hr.URL};return sr.getInstance().addImage(t.value),e}if(t.type!==Se.FUNCTION)throw new Error("Unsupported image type");var A=Tr[t.name];if(void 0===A)throw new Error('Attempting to parse an unsupported image function "'+t.name+'"');return A(t.values)};var _r,Ir,Tr={"linear-gradient":function(t){var n=OA(180),i=[];return mA(t).forEach(function(t,e){if(0===e){var A=t[0];if(A.type===Se.IDENT_TOKEN&&"to"===A.value)return void(n=bA(t));if(FA(A))return void(n=TA(A))}var r=XA(t);i.push(r)}),{angle:n,stops:i,type:hr.LINEAR_GRADIENT}},"-moz-linear-gradient":ZA,"-ms-linear-gradient":ZA,"-o-linear-gradient":ZA,"-webkit-linear-gradient":ZA,"radial-gradient":function(t){var i=Nr.CIRCLE,o=Hr.FARTHEST_CORNER,s=[],a=[];return mA(t).forEach(function(t,e){var A=!0;if(0===e){var r=!1;A=t.reduce(function(t,e){if(r)if(dA(e))switch(e.value){case"center":return a.push(xA),t;case"top":case"left":return a.push(SA),t;case"right":case"bottom":return a.push(_A),t}else(yA(e)||vA(e))&&a.push(e);else if(dA(e))switch(e.value){case"circle":return i=Nr.CIRCLE,!1;case Ur:return i=Nr.ELLIPSE,!1;case"at":return!(r=!0);case Cr:return o=Hr.CLOSEST_SIDE,!1;case"cover":case Qr:return o=Hr.FARTHEST_SIDE,!1;case Er:case Fr:return o=Hr.CLOSEST_CORNER,!1;case br:return o=Hr.FARTHEST_CORNER,!1}else if(vA(e)||yA(e))return Array.isArray(o)||(o=[]),o.push(e),!1;return t},A)}if(A){var n=XA(t);s.push(n)}}),{size:o,shape:i,stops:s,position:a,type:hr.RADIAL_GRADIENT}},"-moz-radial-gradient":lr,"-ms-radial-gradient":lr,"-o-radial-gradient":lr,"-webkit-radial-gradient":lr,"-webkit-gradient":function(t){var e=OA(180),o=[],s=hr.LINEAR_GRADIENT,A=Nr.CIRCLE,r=Hr.FARTHEST_CORNER;return mA(t).forEach(function(t,e){var A=t[0];if(0===e){if(dA(A)&&"linear"===A.value)return void(s=hr.LINEAR_GRADIENT);if(dA(A)&&"radial"===A.value)return void(s=hr.RADIAL_GRADIENT)}if(A.type===Se.FUNCTION)if("from"===A.name){var r=RA(A.values[0]);o.push({stop:SA,color:r})}else if("to"===A.name){r=RA(A.values[0]);o.push({stop:_A,color:r})}else if("color-stop"===A.name){var n=A.values.filter(wA);if(2===n.length){r=RA(n[1]);var i=n[0];fA(i)&&o.push({stop:{type:Se.PERCENTAGE_TOKEN,number:100*i.number,flags:i.flags},color:r})}}}),s===hr.LINEAR_GRADIENT?{angle:(e+OA(180))%OA(360),stops:o,type:s}:{size:r,shape:A,stops:o,position:[],type:s}}},Or={name:"background-image",initialValue:"none",type:DA.LIST,prefix:!1,parse:function(t){if(0===t.length)return[];var e=t[0];return e.type===Se.IDENT_TOKEN&&"none"===e.value?[]:t.filter(function(t){return wA(t)&&((e=t).type!==Se.FUNCTION||Tr[e.name]);var e}).map(xr)}},Rr={name:"background-origin",initialValue:"border-box",prefix:!1,type:DA.LIST,parse:function(t){return t.map(function(t){if(dA(t))switch(t.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Pr={name:"background-position",initialValue:"0% 0%",type:DA.LIST,prefix:!1,parse:function(t){return mA(t).map(function(t){return t.filter(yA)}).map(CA)}};(Ir=_r=_r||{})[Ir.REPEAT=0]="REPEAT",Ir[Ir.NO_REPEAT=1]="NO_REPEAT",Ir[Ir.REPEAT_X=2]="REPEAT_X";var Mr,Kr,Dr={name:"background-repeat",initialValue:"repeat",prefix:!(Ir[Ir.REPEAT_Y=3]="REPEAT_Y"),type:DA.LIST,parse:function(t){return mA(t).map(function(t){return t.filter(dA).map(function(t){return t.value}).join(" ")}).map(kr)}},kr=function(t){switch(t){case"no-repeat":return _r.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return _r.REPEAT_X;case"repeat-y":case"no-repeat repeat":return _r.REPEAT_Y;case"repeat":default:return _r.REPEAT}};(Kr=Mr=Mr||{}).AUTO="auto",Kr.CONTAIN="contain";function zr(t){return{name:"border-"+t+"-color",initialValue:"transparent",prefix:!1,type:DA.TYPE_VALUE,format:"color"}}function jr(t){return{name:"border-radius-"+t,initialValue:"0 0",prefix:!1,type:DA.LIST,parse:function(t){return CA(t.filter(yA))}}}var qr,Vr,Xr={name:"background-size",initialValue:"0",prefix:!(Kr.COVER="cover"),type:DA.LIST,parse:function(t){return mA(t).map(function(t){return t.filter(Gr)})}},Gr=function(t){return dA(t)||yA(t)},Jr=zr("top"),Wr=zr("right"),Yr=zr("bottom"),Zr=zr("left"),$r=jr("top-left"),tn=jr("top-right"),en=jr("bottom-right"),An=jr("bottom-left");(Vr=qr=qr||{})[Vr.NONE=0]="NONE",Vr[Vr.SOLID=1]="SOLID";function rn(t){return{name:"border-"+t+"-style",initialValue:"solid",prefix:!1,type:DA.IDENT_VALUE,parse:function(t){switch(t){case"none":return qr.NONE}return qr.SOLID}}}function nn(t){return{name:"border-"+t+"-width",initialValue:"0",type:DA.VALUE,prefix:!1,parse:function(t){return hA(t)?t.number:0}}}var on,sn,an=rn("top"),cn=rn("right"),un=rn("bottom"),ln=rn("left"),hn=nn("top"),fn=nn("right"),dn=nn("bottom"),pn=nn("left"),gn={name:"color",initialValue:"transparent",prefix:!1,type:DA.TYPE_VALUE,format:"color"},Bn={name:"display",initialValue:"inline-block",prefix:!1,type:DA.LIST,parse:function(t){return t.filter(dA).reduce(function(t,e){return t|wn(e.value)},0)}},wn=function(t){switch(t){case"block":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0};(sn=on=on||{})[sn.NONE=0]="NONE",sn[sn.LEFT=1]="LEFT",sn[sn.RIGHT=2]="RIGHT",sn[sn.INLINE_START=3]="INLINE_START";var mn,vn,yn,Cn,Qn={name:"float",initialValue:"none",prefix:!(sn[sn.INLINE_END=4]="INLINE_END"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"left":return on.LEFT;case"right":return on.RIGHT;case"inline-start":return on.INLINE_START;case"inline-end":return on.INLINE_END}return on.NONE}},Fn={name:"letter-spacing",initialValue:"0",prefix:!1,type:DA.VALUE,parse:function(t){return!(t.type===Se.IDENT_TOKEN&&"normal"===t.value||t.type!==Se.NUMBER_TOKEN&&t.type!==Se.DIMENSION_TOKEN)?t.number:0}},bn={name:"line-break",initialValue:(vn=mn=mn||{}).NORMAL="normal",prefix:!(vn.STRICT="strict"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"strict":return mn.STRICT;case"normal":default:return mn.NORMAL}}},Un={name:"line-height",initialValue:"normal",prefix:!1,type:DA.TOKEN_VALUE},En={name:"list-style-image",initialValue:"none",type:DA.VALUE,prefix:!1,parse:function(t){return t.type===Se.IDENT_TOKEN&&"none"===t.value?null:xr(t)}};(Cn=yn=yn||{})[Cn.INSIDE=0]="INSIDE";var Nn,Ln,Hn={name:"list-style-position",initialValue:"outside",prefix:!(Cn[Cn.OUTSIDE=1]="OUTSIDE"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"inside":return yn.INSIDE;case"outside":default:return yn.OUTSIDE}}};(Ln=Nn=Nn||{})[Ln.NONE=-1]="NONE",Ln[Ln.DISC=0]="DISC",Ln[Ln.CIRCLE=1]="CIRCLE",Ln[Ln.SQUARE=2]="SQUARE",Ln[Ln.DECIMAL=3]="DECIMAL",Ln[Ln.CJK_DECIMAL=4]="CJK_DECIMAL",Ln[Ln.DECIMAL_LEADING_ZERO=5]="DECIMAL_LEADING_ZERO",Ln[Ln.LOWER_ROMAN=6]="LOWER_ROMAN",Ln[Ln.UPPER_ROMAN=7]="UPPER_ROMAN",Ln[Ln.LOWER_GREEK=8]="LOWER_GREEK",Ln[Ln.LOWER_ALPHA=9]="LOWER_ALPHA",Ln[Ln.UPPER_ALPHA=10]="UPPER_ALPHA",Ln[Ln.ARABIC_INDIC=11]="ARABIC_INDIC",Ln[Ln.ARMENIAN=12]="ARMENIAN",Ln[Ln.BENGALI=13]="BENGALI",Ln[Ln.CAMBODIAN=14]="CAMBODIAN",Ln[Ln.CJK_EARTHLY_BRANCH=15]="CJK_EARTHLY_BRANCH",Ln[Ln.CJK_HEAVENLY_STEM=16]="CJK_HEAVENLY_STEM",Ln[Ln.CJK_IDEOGRAPHIC=17]="CJK_IDEOGRAPHIC",Ln[Ln.DEVANAGARI=18]="DEVANAGARI",Ln[Ln.ETHIOPIC_NUMERIC=19]="ETHIOPIC_NUMERIC",Ln[Ln.GEORGIAN=20]="GEORGIAN",Ln[Ln.GUJARATI=21]="GUJARATI",Ln[Ln.GURMUKHI=22]="GURMUKHI",Ln[Ln.HEBREW=22]="HEBREW",Ln[Ln.HIRAGANA=23]="HIRAGANA",Ln[Ln.HIRAGANA_IROHA=24]="HIRAGANA_IROHA",Ln[Ln.JAPANESE_FORMAL=25]="JAPANESE_FORMAL",Ln[Ln.JAPANESE_INFORMAL=26]="JAPANESE_INFORMAL",Ln[Ln.KANNADA=27]="KANNADA",Ln[Ln.KATAKANA=28]="KATAKANA",Ln[Ln.KATAKANA_IROHA=29]="KATAKANA_IROHA",Ln[Ln.KHMER=30]="KHMER",Ln[Ln.KOREAN_HANGUL_FORMAL=31]="KOREAN_HANGUL_FORMAL",Ln[Ln.KOREAN_HANJA_FORMAL=32]="KOREAN_HANJA_FORMAL",Ln[Ln.KOREAN_HANJA_INFORMAL=33]="KOREAN_HANJA_INFORMAL",Ln[Ln.LAO=34]="LAO",Ln[Ln.LOWER_ARMENIAN=35]="LOWER_ARMENIAN",Ln[Ln.MALAYALAM=36]="MALAYALAM",Ln[Ln.MONGOLIAN=37]="MONGOLIAN",Ln[Ln.MYANMAR=38]="MYANMAR",Ln[Ln.ORIYA=39]="ORIYA",Ln[Ln.PERSIAN=40]="PERSIAN",Ln[Ln.SIMP_CHINESE_FORMAL=41]="SIMP_CHINESE_FORMAL",Ln[Ln.SIMP_CHINESE_INFORMAL=42]="SIMP_CHINESE_INFORMAL",Ln[Ln.TAMIL=43]="TAMIL",Ln[Ln.TELUGU=44]="TELUGU",Ln[Ln.THAI=45]="THAI",Ln[Ln.TIBETAN=46]="TIBETAN",Ln[Ln.TRAD_CHINESE_FORMAL=47]="TRAD_CHINESE_FORMAL",Ln[Ln.TRAD_CHINESE_INFORMAL=48]="TRAD_CHINESE_INFORMAL",Ln[Ln.UPPER_ARMENIAN=49]="UPPER_ARMENIAN",Ln[Ln.DISCLOSURE_OPEN=50]="DISCLOSURE_OPEN";function Sn(t){return{name:"margin-"+t,initialValue:"0",prefix:!1,type:DA.TOKEN_VALUE}}var xn,_n,In={name:"list-style-type",initialValue:"none",prefix:!(Ln[Ln.DISCLOSURE_CLOSED=51]="DISCLOSURE_CLOSED"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"disc":return Nn.DISC;case"circle":return Nn.CIRCLE;case"square":return Nn.SQUARE;case"decimal":return Nn.DECIMAL;case"cjk-decimal":return Nn.CJK_DECIMAL;case"decimal-leading-zero":return Nn.DECIMAL_LEADING_ZERO;case"lower-roman":return Nn.LOWER_ROMAN;case"upper-roman":return Nn.UPPER_ROMAN;case"lower-greek":return Nn.LOWER_GREEK;case"lower-alpha":return Nn.LOWER_ALPHA;case"upper-alpha":return Nn.UPPER_ALPHA;case"arabic-indic":return Nn.ARABIC_INDIC;case"armenian":return Nn.ARMENIAN;case"bengali":return Nn.BENGALI;case"cambodian":return Nn.CAMBODIAN;case"cjk-earthly-branch":return Nn.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return Nn.CJK_HEAVENLY_STEM;case"cjk-ideographic":return Nn.CJK_IDEOGRAPHIC;case"devanagari":return Nn.DEVANAGARI;case"ethiopic-numeric":return Nn.ETHIOPIC_NUMERIC;case"georgian":return Nn.GEORGIAN;case"gujarati":return Nn.GUJARATI;case"gurmukhi":return Nn.GURMUKHI;case"hebrew":return Nn.HEBREW;case"hiragana":return Nn.HIRAGANA;case"hiragana-iroha":return Nn.HIRAGANA_IROHA;case"japanese-formal":return Nn.JAPANESE_FORMAL;case"japanese-informal":return Nn.JAPANESE_INFORMAL;case"kannada":return Nn.KANNADA;case"katakana":return Nn.KATAKANA;case"katakana-iroha":return Nn.KATAKANA_IROHA;case"khmer":return Nn.KHMER;case"korean-hangul-formal":return Nn.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return Nn.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return Nn.KOREAN_HANJA_INFORMAL;case"lao":return Nn.LAO;case"lower-armenian":return Nn.LOWER_ARMENIAN;case"malayalam":return Nn.MALAYALAM;case"mongolian":return Nn.MONGOLIAN;case"myanmar":return Nn.MYANMAR;case"oriya":return Nn.ORIYA;case"persian":return Nn.PERSIAN;case"simp-chinese-formal":return Nn.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return Nn.SIMP_CHINESE_INFORMAL;case"tamil":return Nn.TAMIL;case"telugu":return Nn.TELUGU;case"thai":return Nn.THAI;case"tibetan":return Nn.TIBETAN;case"trad-chinese-formal":return Nn.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return Nn.TRAD_CHINESE_INFORMAL;case"upper-armenian":return Nn.UPPER_ARMENIAN;case"disclosure-open":return Nn.DISCLOSURE_OPEN;case"disclosure-closed":return Nn.DISCLOSURE_CLOSED;case"none":default:return Nn.NONE}}},Tn=Sn("top"),On=Sn("right"),Rn=Sn("bottom"),Pn=Sn("left");(_n=xn=xn||{})[_n.VISIBLE=0]="VISIBLE",_n[_n.HIDDEN=1]="HIDDEN",_n[_n.SCROLL=2]="SCROLL";function Mn(t){return{name:"padding-"+t,initialValue:"0",prefix:!1,type:DA.TYPE_VALUE,format:"length-percentage"}}var Kn,Dn,kn,zn,jn={name:"overflow",initialValue:"visible",prefix:!(_n[_n.AUTO=3]="AUTO"),type:DA.LIST,parse:function(t){return t.filter(dA).map(function(t){switch(t.value){case"hidden":return xn.HIDDEN;case"scroll":return xn.SCROLL;case"auto":return xn.AUTO;case"visible":default:return xn.VISIBLE}})}},qn={name:"overflow-wrap",initialValue:(Dn=Kn=Kn||{}).NORMAL="normal",prefix:!(Dn.BREAK_WORD="break-word"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"break-word":return Kn.BREAK_WORD;case"normal":default:return Kn.NORMAL}}},Vn=Mn("top"),Xn=Mn("right"),Gn=Mn("bottom"),Jn=Mn("left");(zn=kn=kn||{})[zn.LEFT=0]="LEFT",zn[zn.CENTER=1]="CENTER";var Wn,Yn,Zn={name:"text-align",initialValue:"left",prefix:!(zn[zn.RIGHT=2]="RIGHT"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"right":return kn.RIGHT;case"center":case"justify":return kn.CENTER;case"left":default:return kn.LEFT}}};(Yn=Wn=Wn||{})[Yn.STATIC=0]="STATIC",Yn[Yn.RELATIVE=1]="RELATIVE",Yn[Yn.ABSOLUTE=2]="ABSOLUTE",Yn[Yn.FIXED=3]="FIXED";var $n,ti,ei={name:"position",initialValue:"static",prefix:!(Yn[Yn.STICKY=4]="STICKY"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"relative":return Wn.RELATIVE;case"absolute":return Wn.ABSOLUTE;case"fixed":return Wn.FIXED;case"sticky":return Wn.STICKY}return Wn.STATIC}},Ai={name:"text-shadow",initialValue:"none",type:DA.LIST,prefix:!1,parse:function(t){return 1===t.length&&gA(t[0],"none")?[]:mA(t).map(function(t){for(var e={color:VA.TRANSPARENT,offsetX:SA,offsetY:SA,blur:SA},A=0,r=0;r<t.length;r++){var n=t[r];vA(n)?(0===A?e.offsetX=n:1===A?e.offsetY=n:e.blur=n,A++):e.color=RA(n)}return e})}};(ti=$n=$n||{})[ti.NONE=0]="NONE",ti[ti.LOWERCASE=1]="LOWERCASE",ti[ti.UPPERCASE=2]="UPPERCASE";var ri,ni,ii={name:"text-transform",initialValue:"none",prefix:!(ti[ti.CAPITALIZE=3]="CAPITALIZE"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"uppercase":return $n.UPPERCASE;case"lowercase":return $n.LOWERCASE;case"capitalize":return $n.CAPITALIZE}return $n.NONE}},oi={name:"transform",initialValue:"none",prefix:!0,type:DA.VALUE,parse:function(t){if(t.type===Se.IDENT_TOKEN&&"none"===t.value)return null;if(t.type!==Se.FUNCTION)return null;var e=si[t.name];if(void 0===e)throw new Error('Attempting to parse an unsupported transform function "'+t.name+'"');return e(t.values)}},si={matrix:function(t){var e=t.filter(function(t){return t.type===Se.NUMBER_TOKEN}).map(function(t){return t.number});return 6===e.length?e:null},matrix3d:function(t){var e=t.filter(function(t){return t.type===Se.NUMBER_TOKEN}).map(function(t){return t.number}),A=e[0],r=e[1],n=(e[2],e[3],e[4]),i=e[5],o=(e[6],e[7],e[8],e[9],e[10],e[11],e[12]),s=e[13];e[14],e[15];return 16===e.length?[A,r,n,i,o,s]:null}},ai={type:Se.PERCENTAGE_TOKEN,number:50,flags:4},ci=[ai,ai],ui={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:DA.LIST,parse:function(t){var e=t.filter(yA);return 2!==e.length?ci:[e[0],e[1]]}};(ni=ri=ri||{})[ni.VISIBLE=0]="VISIBLE",ni[ni.HIDDEN=1]="HIDDEN";var li,hi,fi={name:"visible",initialValue:"none",prefix:!(ni[ni.COLLAPSE=2]="COLLAPSE"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"hidden":return ri.HIDDEN;case"collapse":return ri.COLLAPSE;case"visible":default:return ri.VISIBLE}}};(hi=li=li||{}).NORMAL="normal",hi.BREAK_ALL="break-all";var di,pi,gi={name:"word-break",initialValue:"normal",prefix:!(hi.KEEP_ALL="keep-all"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"break-all":return li.BREAK_ALL;case"keep-all":return li.KEEP_ALL;case"normal":default:return li.NORMAL}}},Bi={name:"z-index",initialValue:"auto",prefix:!1,type:DA.VALUE,parse:function(t){if(t.type===Se.IDENT_TOKEN)return{auto:!0,order:0};if(fA(t))return{auto:!1,order:t.number};throw new Error("Invalid z-index number parsed")}},wi={name:"opacity",initialValue:"1",type:DA.VALUE,prefix:!1,parse:function(t){return fA(t)?t.number:1}},mi={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:DA.TYPE_VALUE,format:"color"},vi={name:"text-decoration-line",initialValue:"none",prefix:!1,type:DA.LIST,parse:function(t){return t.filter(dA).map(function(t){switch(t.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(t){return 0!==t})}},yi={name:"font-family",initialValue:"",prefix:!1,type:DA.LIST,parse:function(t){var e=[],A=[];return t.forEach(function(t){switch(t.type){case Se.IDENT_TOKEN:case Se.STRING_TOKEN:e.push(t.value);break;case Se.NUMBER_TOKEN:e.push(t.number.toString());break;case Se.COMMA_TOKEN:A.push(e.join(" ")),e.length=0}}),e.length&&A.push(e.join(" ")),A.map(function(t){return-1===t.indexOf(" ")?t:"'"+t+"'"})}},Ci={name:"font-size",initialValue:"0",prefix:!1,type:DA.TYPE_VALUE,format:"length"},Qi={name:"font-weight",initialValue:"normal",type:DA.VALUE,prefix:!1,parse:function(t){if(fA(t))return t.number;if(dA(t))switch(t.value){case"bold":return 700;case"normal":default:return 400}return 400}},Fi={name:"font-variant",initialValue:"none",type:DA.LIST,prefix:!1,parse:function(t){return t.filter(dA).map(function(t){return t.value})}};(pi=di=di||{}).NORMAL="normal",pi.ITALIC="italic";function bi(t,e){return 0!=(t&e)}function Ui(t,e,A){if(!t)return"";var r=t[Math.min(e,t.length-1)];return r?A?r.open:r.close:""}var Ei={name:"font-style",initialValue:"normal",prefix:!(pi.OBLIQUE="oblique"),type:DA.IDENT_VALUE,parse:function(t){switch(t){case"oblique":return di.OBLIQUE;case"italic":return di.ITALIC;case"normal":default:return di.NORMAL}}},Ni={name:"content",initialValue:"none",type:DA.LIST,prefix:!1,parse:function(t){if(0===t.length)return[];var e=t[0];return e.type===Se.IDENT_TOKEN&&"none"===e.value?[]:t}},Li={name:"counter-increment",initialValue:"none",prefix:!0,type:DA.LIST,parse:function(t){if(0===t.length)return null;var e=t[0];if(e.type===Se.IDENT_TOKEN&&"none"===e.value)return null;for(var A=[],r=t.filter(BA),n=0;n<r.length;n++){var i=r[n],o=r[n+1];if(i.type===Se.IDENT_TOKEN){var s=o&&fA(o)?o.number:1;A.push({counter:i.value,increment:s})}}return A}},Hi={name:"counter-reset",initialValue:"none",prefix:!0,type:DA.LIST,parse:function(t){if(0===t.length)return[];for(var e=[],A=t.filter(BA),r=0;r<A.length;r++){var n=A[r],i=A[r+1];if(dA(n)&&"none"!==n.value){var o=i&&fA(i)?i.number:0;e.push({counter:n.value,reset:o})}}return e}},Si={name:"quotes",initialValue:"none",prefix:!0,type:DA.LIST,parse:function(t){if(0===t.length)return null;var e=t[0];if(e.type===Se.IDENT_TOKEN&&"none"===e.value)return null;var A=[],r=t.filter(pA);if(r.length%2!=0)return null;for(var n=0;n<r.length;n+=2){var i=r[n].value,o=r[n+1].value;A.push({open:i,close:o})}return A}},xi={name:"box-shadow",initialValue:"none",type:DA.LIST,prefix:!1,parse:function(t){return 1===t.length&&gA(t[0],"none")?[]:mA(t).map(function(t){for(var e={color:255,offsetX:SA,offsetY:SA,blur:SA,spread:SA,inset:!1},A=0,r=0;r<t.length;r++){var n=t[r];gA(n,"inset")?e.inset=!0:vA(n)?(0===A?e.offsetX=n:1===A?e.offsetY=n:2===A?e.blur=n:e.spread=n,A++):e.color=RA(n)}return e})}},_i=(Ii.prototype.isVisible=function(){return 0<this.display&&0<this.opacity&&this.visibility===ri.VISIBLE},Ii.prototype.isTransparent=function(){return UA(this.backgroundColor)},Ii.prototype.isTransformed=function(){return null!==this.transform},Ii.prototype.isPositioned=function(){return this.position!==Wn.STATIC},Ii.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},Ii.prototype.isFloating=function(){return this.float!==on.NONE},Ii.prototype.isInlineLevel=function(){return bi(this.display,4)||bi(this.display,33554432)||bi(this.display,268435456)||bi(this.display,536870912)||bi(this.display,67108864)||bi(this.display,134217728)},Ii);function Ii(t){this.backgroundClip=Pi(tr,t.backgroundClip),this.backgroundColor=Pi(er,t.backgroundColor),this.backgroundImage=Pi(Or,t.backgroundImage),this.backgroundOrigin=Pi(Rr,t.backgroundOrigin),this.backgroundPosition=Pi(Pr,t.backgroundPosition),this.backgroundRepeat=Pi(Dr,t.backgroundRepeat),this.backgroundSize=Pi(Xr,t.backgroundSize),this.borderTopColor=Pi(Jr,t.borderTopColor),this.borderRightColor=Pi(Wr,t.borderRightColor),this.borderBottomColor=Pi(Yr,t.borderBottomColor),this.borderLeftColor=Pi(Zr,t.borderLeftColor),this.borderTopLeftRadius=Pi($r,t.borderTopLeftRadius),this.borderTopRightRadius=Pi(tn,t.borderTopRightRadius),this.borderBottomRightRadius=Pi(en,t.borderBottomRightRadius),this.borderBottomLeftRadius=Pi(An,t.borderBottomLeftRadius),this.borderTopStyle=Pi(an,t.borderTopStyle),this.borderRightStyle=Pi(cn,t.borderRightStyle),this.borderBottomStyle=Pi(un,t.borderBottomStyle),this.borderLeftStyle=Pi(ln,t.borderLeftStyle),this.borderTopWidth=Pi(hn,t.borderTopWidth),this.borderRightWidth=Pi(fn,t.borderRightWidth),this.borderBottomWidth=Pi(dn,t.borderBottomWidth),this.borderLeftWidth=Pi(pn,t.borderLeftWidth),this.boxShadow=Pi(xi,t.boxShadow),this.color=Pi(gn,t.color),this.display=Pi(Bn,t.display),this.float=Pi(Qn,t.cssFloat),this.fontFamily=Pi(yi,t.fontFamily),this.fontSize=Pi(Ci,t.fontSize),this.fontStyle=Pi(Ei,t.fontStyle),this.fontVariant=Pi(Fi,t.fontVariant),this.fontWeight=Pi(Qi,t.fontWeight),this.letterSpacing=Pi(Fn,t.letterSpacing),this.lineBreak=Pi(bn,t.lineBreak),this.lineHeight=Pi(Un,t.lineHeight),this.listStyleImage=Pi(En,t.listStyleImage),this.listStylePosition=Pi(Hn,t.listStylePosition),this.listStyleType=Pi(In,t.listStyleType),this.marginTop=Pi(Tn,t.marginTop),this.marginRight=Pi(On,t.marginRight),this.marginBottom=Pi(Rn,t.marginBottom),this.marginLeft=Pi(Pn,t.marginLeft),this.opacity=Pi(wi,t.opacity);var e=Pi(jn,t.overflow);this.overflowX=e[0],this.overflowY=e[1<e.length?1:0],this.overflowWrap=Pi(qn,t.overflowWrap),this.paddingTop=Pi(Vn,t.paddingTop),this.paddingRight=Pi(Xn,t.paddingRight),this.paddingBottom=Pi(Gn,t.paddingBottom),this.paddingLeft=Pi(Jn,t.paddingLeft),this.position=Pi(ei,t.position),this.textAlign=Pi(Zn,t.textAlign),this.textDecorationColor=Pi(mi,t.textDecorationColor||t.color),this.textDecorationLine=Pi(vi,t.textDecorationLine),this.textShadow=Pi(Ai,t.textShadow),this.textTransform=Pi(ii,t.textTransform),this.transform=Pi(oi,t.transform),this.transformOrigin=Pi(ui,t.transformOrigin),this.visibility=Pi(fi,t.visibility),this.wordBreak=Pi(gi,t.wordBreak),this.zIndex=Pi(Bi,t.zIndex)}var Ti,Oi=function(t){this.content=Pi(Ni,t.content),this.quotes=Pi(Si,t.quotes)},Ri=function(t){this.counterIncrement=Pi(Li,t.counterIncrement),this.counterReset=Pi(Hi,t.counterReset)},Pi=function(t,e){var A=new aA,r=null!=e?e.toString():t.initialValue;A.write(r);var n=new uA(A.read());switch(t.type){case DA.IDENT_VALUE:var i=n.parseComponentValue();return t.parse(dA(i)?i.value:t.initialValue);case DA.VALUE:return t.parse(n.parseComponentValue());case DA.LIST:return t.parse(n.parseComponentValues());case DA.TOKEN_VALUE:return n.parseComponentValue();case DA.TYPE_VALUE:switch(t.format){case"angle":return TA(n.parseComponentValue());case"color":return RA(n.parseComponentValue());case"image":return xr(n.parseComponentValue());case"length":var o=n.parseComponentValue();return vA(o)?o:SA;case"length-percentage":var s=n.parseComponentValue();return yA(s)?s:SA}}throw new Error("Attempting to parse unsupported css format type "+t.format)},Mi=function(t){this.styles=new _i(window.getComputedStyle(t,null)),this.textNodes=[],this.elements=[],null!==this.styles.transform&&Po(t)&&(t.style.transform="none"),this.bounds=Nt(t),this.flags=0},Ki=function(t,e){this.text=t,this.bounds=e},Di=function(t){var e=t.ownerDocument;if(e){var A=e.createElement("html2canvaswrapper");A.appendChild(t.cloneNode(!0));var r=t.parentNode;if(r){r.replaceChild(A,t);var n=Nt(A);return A.firstChild&&r.replaceChild(A.firstChild,A),n}}return new Ut(0,0,0,0)},ki=function(t,e,A){var r=t.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(t,e),n.setEnd(t,e+A),Ut.fromClientRect(n.getBoundingClientRect())},zi=function(t,e){return 0!==e.letterSpacing?Lt(t).map(function(t){return Ht(t)}):ji(t,e)},ji=function(t,e){for(var A,r,n,i,o,s,a,c,u,l,h,f=(A=t,r={lineBreak:e.lineBreak,wordBreak:e.overflowWrap===Kn.BREAK_WORD?"break-word":e.wordBreak},n=Lt(A),i=Dt(n,r),o=i[0],s=i[1],a=i[2],c=n.length,l=u=0,{next:function(){if(c<=l)return{done:!0,value:null};for(var t=ve;l<c&&(t=Kt(n,s,o,++l,a))===ve;);if(t===ve&&l!==c)return{done:!0,value:null};var e=new Le(n,t,u,l);return u=l,{value:e,done:!1}}}),d=[];!(h=f.next()).done;)h.value&&d.push(h.value.slice());return d},qi=function(t,e){var A,r,n,i,o,s;this.text=Vi(t.data,e.textTransform),this.textBounds=(A=this.text,n=t,i=zi(A,r=e),o=[],s=0,i.forEach(function(t){if(r.textDecorationLine.length||0<t.trim().length)if(nr.SUPPORT_RANGE_BOUNDS)o.push(new Ki(t,ki(n,s,t.length)));else{var e=n.splitText(t.length);o.push(new Ki(t,Di(n))),n=e}else nr.SUPPORT_RANGE_BOUNDS||(n=n.splitText(t.length));s+=t.length}),o)},Vi=function(t,e){switch(e){case $n.LOWERCASE:return t.toLowerCase();case $n.CAPITALIZE:return t.replace(Xi,Gi);case $n.UPPERCASE:return t.toUpperCase();default:return t}},Xi=/(^|\s|:|-|\(|\))([a-z])/g,Gi=function(t,e,A){return 0<t.length?e+A.toUpperCase():t},Ji=(Ct(Wi,Ti=Mi),Wi);function Wi(t){var e=Ti.call(this,t)||this;return e.src=t.currentSrc||t.src,e.intrinsicWidth=t.naturalWidth,e.intrinsicHeight=t.naturalHeight,sr.getInstance().addImage(e.src),e}var Yi,Zi=(Ct($i,Yi=Mi),$i);function $i(t){var e=Yi.call(this,t)||this;return e.canvas=t,e.intrinsicWidth=t.width,e.intrinsicHeight=t.height,e}var to,eo=(Ct(Ao,to=Mi),Ao);function Ao(t){var e=to.call(this,t)||this,A=new XMLSerializer;return e.svg="data:image/svg+xml,"+encodeURIComponent(A.serializeToString(t)),e.intrinsicWidth=t.width.baseVal.value,e.intrinsicHeight=t.height.baseVal.value,sr.getInstance().addImage(e.svg),e}var ro,no=(Ct(io,ro=Mi),io);function io(t){var e=ro.call(this,t)||this;return e.value=t.value,e}var oo,so=(Ct(ao,oo=Mi),ao);function ao(t){var e=oo.call(this,t)||this;return e.start=t.start,e.reversed="boolean"==typeof t.reversed&&!0===t.reversed,e}var co,uo=[{type:Se.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],lo=[{type:Se.PERCENTAGE_TOKEN,flags:0,number:50}],ho="checkbox",fo="radio",po="password",go=707406591,Bo=(Ct(wo,co=Mi),wo);function wo(t){var e,A,r,n=co.call(this,t)||this;switch(n.type=t.type.toLowerCase(),n.checked=t.checked,n.value=0===(A=(e=t).type===po?new Array(e.value.length+1).join("\u2022"):e.value).length?e.placeholder||"":A,n.type!==ho&&n.type!==fo||(n.styles.backgroundColor=3739148031,n.styles.borderTopColor=n.styles.borderRightColor=n.styles.borderBottomColor=n.styles.borderLeftColor=2779096575,n.styles.borderTopWidth=n.styles.borderRightWidth=n.styles.borderBottomWidth=n.styles.borderLeftWidth=1,n.styles.borderTopStyle=n.styles.borderRightStyle=n.styles.borderBottomStyle=n.styles.borderLeftStyle=qr.SOLID,n.styles.backgroundClip=[zA.BORDER_BOX],n.styles.backgroundOrigin=[0],n.bounds=(r=n.bounds).width>r.height?new Ut(r.left+(r.width-r.height)/2,r.top,r.height,r.height):r.width<r.height?new Ut(r.left,r.top+(r.height-r.width)/2,r.width,r.width):r),n.type){case ho:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=uo;break;case fo:n.styles.borderTopRightRadius=n.styles.borderTopLeftRadius=n.styles.borderBottomRightRadius=n.styles.borderBottomLeftRadius=lo}return n}var mo,vo=(Ct(yo,mo=Mi),yo);function yo(t){var e=mo.call(this,t)||this,A=t.options[t.selectedIndex||0];return e.value=A&&A.text||"",e}var Co,Qo=(Ct(Fo,Co=Mi),Fo);function Fo(t){var e=Co.call(this,t)||this;return e.value=t.value,e}function bo(t){return RA(uA.create(t).parseComponentValue())}var Uo,Eo=(Ct(No,Uo=Mi),No);function No(t){var e=Uo.call(this,t)||this;e.src=t.src,e.width=parseInt(t.width,10)||0,e.height=parseInt(t.height,10)||0,e.backgroundColor=e.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){e.tree=_o(t.contentWindow.document.documentElement);var A=t.contentWindow.document.documentElement?bo(getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):VA.TRANSPARENT,r=t.contentWindow.document.body?bo(getComputedStyle(t.contentWindow.document.body).backgroundColor):VA.TRANSPARENT;e.backgroundColor=UA(A)?UA(r)?e.styles.backgroundColor:r:A}}catch(t){}return e}function Lo(t){return"STYLE"===t.tagName}var Ho=["OL","UL","MENU"],So=function(t,e,A){for(var r=t.firstChild,n=void 0;r;r=n)if(n=r.nextSibling,Oo(r)&&0<r.data.trim().length)e.textNodes.push(new qi(r,e.styles));else if(Ro(r)){var i=xo(r);i.styles.isVisible()&&(Io(r,i,A)?i.flags|=4:To(i.styles)&&(i.flags|=2),-1!==Ho.indexOf(r.tagName)&&(i.flags|=8),e.elements.push(i),Go(r)||zo(r)||Jo(r)||So(r,i,A))}},xo=function(t){return new(Vo(t)?Ji:qo(t)?Zi:zo(t)?eo:Ko(t)?no:Do(t)?so:ko(t)?Bo:Jo(t)?vo:Go(t)?Qo:Xo(t)?Eo:Mi)(t)},_o=function(t){var e=xo(t);return e.flags|=4,So(t,e,e),e},Io=function(t,e,A){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||jo(t)&&A.styles.isTransparent()},To=function(t){return t.isPositioned()||t.isFloating()},Oo=function(t){return t.nodeType===Node.TEXT_NODE},Ro=function(t){return t.nodeType===Node.ELEMENT_NODE},Po=function(t){return Ro(t)&&void 0!==t.style&&!Mo(t)},Mo=function(t){return"object"==typeof t.className},Ko=function(t){return"LI"===t.tagName},Do=function(t){return"OL"===t.tagName},ko=function(t){return"INPUT"===t.tagName},zo=function(t){return"svg"===t.tagName},jo=function(t){return"BODY"===t.tagName},qo=function(t){return"CANVAS"===t.tagName},Vo=function(t){return"IMG"===t.tagName},Xo=function(t){return"IFRAME"===t.tagName},Go=function(t){return"TEXTAREA"===t.tagName},Jo=function(t){return"SELECT"===t.tagName},Wo=(Yo.prototype.getCounterValue=function(t){var e=this.counters[t];return e&&e.length?e[e.length-1]:1},Yo.prototype.getCounterValues=function(t){var e=this.counters[t];return e||[]},Yo.prototype.pop=function(t){var e=this;t.forEach(function(t){return e.counters[t].pop()})},Yo.prototype.parse=function(t){var A=this,e=t.counterIncrement,r=t.counterReset,n=!0;null!==e&&e.forEach(function(t){var e=A.counters[t.counter];e&&0!==t.increment&&(n=!1,e[Math.max(0,e.length-1)]+=t.increment)});var i=[];return n&&r.forEach(function(t){var e=A.counters[t.counter];i.push(t.counter),(e=e||(A.counters[t.counter]=[])).push(t.reset)}),i},Yo);function Yo(){this.counters={}}function Zo(r,t,e,n,A,i){return r<t||e<r?us(r,A,0<i.length):n.integers.reduce(function(t,e,A){for(;e<=r;)r-=e,t+=n.values[A];return t},"")+i}function $o(t,e,A,r){for(var n="";A||t--,n=r(t)+n,e<=(t/=e)*e;);return n}function ts(t,e,A,r,n){var i=A-e+1;return(t<0?"-":"")+($o(Math.abs(t),i,r,function(t){return Ht(Math.floor(t%i)+e)})+n)}function es(t,e,A){void 0===A&&(A=". ");var r=e.length;return $o(Math.abs(t),r,!1,function(t){return e[Math.floor(t%r)]})+A}function As(t,e,A,r,n,i){if(t<-9999||9999<t)return us(t,Nn.CJK_DECIMAL,0<n.length);var o=Math.abs(t),s=n;if(0===o)return e[0]+s;for(var a=0;0<o&&a<=4;a++){var c=o%10;0==c&&bi(i,1)&&""!==s?s=e[c]+s:1<c||1==c&&0===a||1==c&&1===a&&bi(i,2)||1==c&&1===a&&bi(i,4)&&100<t||1==c&&1<a&&bi(i,8)?s=e[c]+(0<a?A[a-1]:"")+s:1==c&&0<a&&(s=A[a-1]+s),o=Math.floor(o/10)}return(t<0?r:"")+s}var rs,ns,is={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},os={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u0554","\u0553","\u0552","\u0551","\u0550","\u054f","\u054e","\u054d","\u054c","\u054b","\u054a","\u0549","\u0548","\u0547","\u0546","\u0545","\u0544","\u0543","\u0542","\u0541","\u0540","\u053f","\u053e","\u053d","\u053c","\u053b","\u053a","\u0539","\u0538","\u0537","\u0536","\u0535","\u0534","\u0533","\u0532","\u0531"]},ss={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["\u05d9\u05f3","\u05d8\u05f3","\u05d7\u05f3","\u05d6\u05f3","\u05d5\u05f3","\u05d4\u05f3","\u05d3\u05f3","\u05d2\u05f3","\u05d1\u05f3","\u05d0\u05f3","\u05ea","\u05e9","\u05e8","\u05e7","\u05e6","\u05e4","\u05e2","\u05e1","\u05e0","\u05de","\u05dc","\u05db","\u05d9\u05d8","\u05d9\u05d7","\u05d9\u05d6","\u05d8\u05d6","\u05d8\u05d5","\u05d9","\u05d8","\u05d7","\u05d6","\u05d5","\u05d4","\u05d3","\u05d2","\u05d1","\u05d0"]},as={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["\u10f5","\u10f0","\u10ef","\u10f4","\u10ee","\u10ed","\u10ec","\u10eb","\u10ea","\u10e9","\u10e8","\u10e7","\u10e6","\u10e5","\u10e4","\u10f3","\u10e2","\u10e1","\u10e0","\u10df","\u10de","\u10dd","\u10f2","\u10dc","\u10db","\u10da","\u10d9","\u10d8","\u10d7","\u10f1","\u10d6","\u10d5","\u10d4","\u10d3","\u10d2","\u10d1","\u10d0"]},cs="\ub9c8\uc774\ub108\uc2a4",us=function(t,e,A){var r=A?". ":"",n=A?"\u3001":"",i=A?", ":"",o=A?" ":"";switch(e){case Nn.DISC:return"\u2022"+o;case Nn.CIRCLE:return"\u25e6"+o;case Nn.SQUARE:return"\u25fe"+o;case Nn.DECIMAL_LEADING_ZERO:var s=ts(t,48,57,!0,r);return s.length<4?"0"+s:s;case Nn.CJK_DECIMAL:return es(t,"\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d",n);case Nn.LOWER_ROMAN:return Zo(t,1,3999,is,Nn.DECIMAL,r).toLowerCase();case Nn.UPPER_ROMAN:return Zo(t,1,3999,is,Nn.DECIMAL,r);case Nn.LOWER_GREEK:return ts(t,945,969,!1,r);case Nn.LOWER_ALPHA:return ts(t,97,122,!1,r);case Nn.UPPER_ALPHA:return ts(t,65,90,!1,r);case Nn.ARABIC_INDIC:return ts(t,1632,1641,!0,r);case Nn.ARMENIAN:case Nn.UPPER_ARMENIAN:return Zo(t,1,9999,os,Nn.DECIMAL,r);case Nn.LOWER_ARMENIAN:return Zo(t,1,9999,os,Nn.DECIMAL,r).toLowerCase();case Nn.BENGALI:return ts(t,2534,2543,!0,r);case Nn.CAMBODIAN:case Nn.KHMER:return ts(t,6112,6121,!0,r);case Nn.CJK_EARTHLY_BRANCH:return es(t,"\u5b50\u4e11\u5bc5\u536f\u8fb0\u5df3\u5348\u672a\u7533\u9149\u620c\u4ea5",n);case Nn.CJK_HEAVENLY_STEM:return es(t,"\u7532\u4e59\u4e19\u4e01\u620a\u5df1\u5e9a\u8f9b\u58ec\u7678",n);case Nn.CJK_IDEOGRAPHIC:case Nn.TRAD_CHINESE_INFORMAL:return As(t,"\u96f6\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d","\u5341\u767e\u5343\u842c","\u8ca0",n,14);case Nn.TRAD_CHINESE_FORMAL:return As(t,"\u96f6\u58f9\u8cb3\u53c3\u8086\u4f0d\u9678\u67d2\u634c\u7396","\u62fe\u4f70\u4edf\u842c","\u8ca0",n,15);case Nn.SIMP_CHINESE_INFORMAL:return As(t,"\u96f6\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d","\u5341\u767e\u5343\u842c","\u8d1f",n,14);case Nn.SIMP_CHINESE_FORMAL:return As(t,"\u96f6\u58f9\u8d30\u53c1\u8086\u4f0d\u9646\u67d2\u634c\u7396","\u62fe\u4f70\u4edf\u842c","\u8d1f",n,15);case Nn.JAPANESE_INFORMAL:return As(t,"\u3007\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d","\u5341\u767e\u5343\u4e07","\u30de\u30a4\u30ca\u30b9",n,0);case Nn.JAPANESE_FORMAL:return As(t,"\u96f6\u58f1\u5f10\u53c2\u56db\u4f0d\u516d\u4e03\u516b\u4e5d","\u62fe\u767e\u5343\u4e07","\u30de\u30a4\u30ca\u30b9",n,7);case Nn.KOREAN_HANGUL_FORMAL:return As(t,"\uc601\uc77c\uc774\uc0bc\uc0ac\uc624\uc721\uce60\ud314\uad6c","\uc2ed\ubc31\ucc9c\ub9cc",cs,i,7);case Nn.KOREAN_HANJA_INFORMAL:return As(t,"\u96f6\u4e00\u4e8c\u4e09\u56db\u4e94\u516d\u4e03\u516b\u4e5d","\u5341\u767e\u5343\u842c",cs,i,0);case Nn.KOREAN_HANJA_FORMAL:return As(t,"\u96f6\u58f9\u8cb3\u53c3\u56db\u4e94\u516d\u4e03\u516b\u4e5d","\u62fe\u767e\u5343",cs,i,7);case Nn.DEVANAGARI:return ts(t,2406,2415,!0,r);case Nn.GEORGIAN:return Zo(t,1,19999,as,Nn.DECIMAL,r);case Nn.GUJARATI:return ts(t,2790,2799,!0,r);case Nn.GURMUKHI:return ts(t,2662,2671,!0,r);case Nn.HEBREW:return Zo(t,1,10999,ss,Nn.DECIMAL,r);case Nn.HIRAGANA:return es(t,"\u3042\u3044\u3046\u3048\u304a\u304b\u304d\u304f\u3051\u3053\u3055\u3057\u3059\u305b\u305d\u305f\u3061\u3064\u3066\u3068\u306a\u306b\u306c\u306d\u306e\u306f\u3072\u3075\u3078\u307b\u307e\u307f\u3080\u3081\u3082\u3084\u3086\u3088\u3089\u308a\u308b\u308c\u308d\u308f\u3090\u3091\u3092\u3093");case Nn.HIRAGANA_IROHA:return es(t,"\u3044\u308d\u306f\u306b\u307b\u3078\u3068\u3061\u308a\u306c\u308b\u3092\u308f\u304b\u3088\u305f\u308c\u305d\u3064\u306d\u306a\u3089\u3080\u3046\u3090\u306e\u304a\u304f\u3084\u307e\u3051\u3075\u3053\u3048\u3066\u3042\u3055\u304d\u3086\u3081\u307f\u3057\u3091\u3072\u3082\u305b\u3059");case Nn.KANNADA:return ts(t,3302,3311,!0,r);case Nn.KATAKANA:return es(t,"\u30a2\u30a4\u30a6\u30a8\u30aa\u30ab\u30ad\u30af\u30b1\u30b3\u30b5\u30b7\u30b9\u30bb\u30bd\u30bf\u30c1\u30c4\u30c6\u30c8\u30ca\u30cb\u30cc\u30cd\u30ce\u30cf\u30d2\u30d5\u30d8\u30db\u30de\u30df\u30e0\u30e1\u30e2\u30e4\u30e6\u30e8\u30e9\u30ea\u30eb\u30ec\u30ed\u30ef\u30f0\u30f1\u30f2\u30f3",n);case Nn.KATAKANA_IROHA:return es(t,"\u30a4\u30ed\u30cf\u30cb\u30db\u30d8\u30c8\u30c1\u30ea\u30cc\u30eb\u30f2\u30ef\u30ab\u30e8\u30bf\u30ec\u30bd\u30c4\u30cd\u30ca\u30e9\u30e0\u30a6\u30f0\u30ce\u30aa\u30af\u30e4\u30de\u30b1\u30d5\u30b3\u30a8\u30c6\u30a2\u30b5\u30ad\u30e6\u30e1\u30df\u30b7\u30f1\u30d2\u30e2\u30bb\u30b9",n);case Nn.LAO:return ts(t,3792,3801,!0,r);case Nn.MONGOLIAN:return ts(t,6160,6169,!0,r);case Nn.MYANMAR:return ts(t,4160,4169,!0,r);case Nn.ORIYA:return ts(t,2918,2927,!0,r);case Nn.PERSIAN:return ts(t,1776,1785,!0,r);case Nn.TAMIL:return ts(t,3046,3055,!0,r);case Nn.TELUGU:return ts(t,3174,3183,!0,r);case Nn.THAI:return ts(t,3664,3673,!0,r);case Nn.TIBETAN:return ts(t,3872,3881,!0,r);case Nn.DECIMAL:default:return ts(t,48,57,!0,r)}},ls="data-html2canvas-ignore",hs=(fs.prototype.toIFrame=function(t,A){var e=this,r=gs(t,A);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var n=t.defaultView.pageXOffset,i=t.defaultView.pageYOffset,o=r.contentWindow,s=o.document,a=Bs(r).then(function(){return Ft(e,void 0,void 0,function(){var e;return bt(this,function(t){switch(t.label){case 0:return this.scrolledElements.forEach(ys),o&&(o.scrollTo(A.left,A.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||o.scrollY===A.top&&o.scrollX===A.left||(s.documentElement.style.top=-A.top+"px",s.documentElement.style.left=-A.left+"px",s.documentElement.style.position="absolute")),e=this.options.onclone,void 0===this.clonedReferenceElement?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:s.fonts&&s.fonts.ready?[4,s.fonts.ready]:[3,2];case 1:t.sent(),t.label=2;case 2:return"function"==typeof e?[2,Promise.resolve().then(function(){return e(s)}).then(function(){return r})]:[2,r]}})})});return s.open(),s.write(ms(document.doctype)+"<html></html>"),vs(this.referenceElement.ownerDocument,n,i),s.replaceChild(s.adoptNode(this.documentElement),s.documentElement),s.close(),a},fs.prototype.createElementClone=function(t){if(qo(t))return this.createCanvasClone(t);if(Lo(t))return this.createStyleClone(t);var e=t.cloneNode(!1);return Vo(e)&&"lazy"===e.loading&&(e.loading="eager"),e},fs.prototype.createStyleClone=function(t){try{var e=t.sheet;if(e&&e.cssRules){var A=[].slice.call(e.cssRules,0).reduce(function(t,e){return e&&"string"==typeof e.cssText?t+e.cssText:t},""),r=t.cloneNode(!1);return r.textContent=A,r}}catch(t){if(ir.getInstance(this.options.id).error("Unable to access cssRules property",t),"SecurityError"!==t.name)throw t}return t.cloneNode(!1)},fs.prototype.createCanvasClone=function(t){if(this.options.inlineImages&&t.ownerDocument){var e=t.ownerDocument.createElement("img");try{return e.src=t.toDataURL(),e}catch(t){ir.getInstance(this.options.id).info("Unable to clone canvas contents, canvas is tainted")}}var A=t.cloneNode(!1);try{A.width=t.width,A.height=t.height;var r=t.getContext("2d"),n=A.getContext("2d");return n&&(r?n.putImageData(r.getImageData(0,0,t.width,t.height),0,0):n.drawImage(t,0,0)),A}catch(t){}return A},fs.prototype.cloneNode=function(t){if(Oo(t))return document.createTextNode(t.data);if(!t.ownerDocument)return t.cloneNode(!1);var e=t.ownerDocument.defaultView;if(e&&Ro(t)&&(Po(t)||Mo(t))){var A=this.createElementClone(t),r=e.getComputedStyle(t),n=e.getComputedStyle(t,":before"),i=e.getComputedStyle(t,":after");this.referenceElement===t&&Po(A)&&(this.clonedReferenceElement=A),jo(A)&&bs(A);for(var o=this.counters.parse(new Ri(r)),s=this.resolvePseudoContent(t,A,n,rs.BEFORE),a=t.firstChild;a;a=a.nextSibling)Ro(a)&&("SCRIPT"===a.tagName||a.hasAttribute(ls)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(a))||this.options.copyStyles&&Ro(a)&&Lo(a)||A.appendChild(this.cloneNode(a));s&&A.insertBefore(s,A.firstChild);var c=this.resolvePseudoContent(t,A,i,rs.AFTER);return c&&A.appendChild(c),this.counters.pop(o),r&&(this.options.copyStyles||Mo(t))&&!Xo(t)&&ws(r,A),0===t.scrollTop&&0===t.scrollLeft||this.scrolledElements.push([A,t.scrollLeft,t.scrollTop]),(Go(t)||Jo(t))&&(Go(A)||Jo(A))&&(A.value=t.value),A}return t.cloneNode(!1)},fs.prototype.resolvePseudoContent=function(d,t,e,A){var p=this;if(e){var r=e.content,g=t.ownerDocument;if(g&&r&&"none"!==r&&"-moz-alt-content"!==r&&"none"!==e.display){this.counters.parse(new Ri(e));var B=new Oi(e),w=g.createElement("html2canvaspseudoelement");ws(e,w),B.content.forEach(function(t){if(t.type===Se.STRING_TOKEN)w.appendChild(g.createTextNode(t.value));else if(t.type===Se.URL_TOKEN){var e=g.createElement("img");e.src=t.value,e.style.opacity="1",w.appendChild(e)}else if(t.type===Se.FUNCTION){if("attr"===t.name){var A=t.values.filter(dA);A.length&&w.appendChild(g.createTextNode(d.getAttribute(A[0].value)||""))}else if("counter"===t.name){var r=t.values.filter(wA),n=r[0],i=r[1];if(n&&dA(n)){var o=p.counters.getCounterValue(n.value),s=i&&dA(i)?In.parse(i.value):Nn.DECIMAL;w.appendChild(g.createTextNode(us(o,s,!1)))}}else if("counters"===t.name){var a=t.values.filter(wA),c=(n=a[0],a[1]);if(i=a[2],n&&dA(n)){var u=p.counters.getCounterValues(n.value),l=i&&dA(i)?In.parse(i.value):Nn.DECIMAL,h=c&&c.type===Se.STRING_TOKEN?c.value:"",f=u.map(function(t){return us(t,l,!1)}).join(h);w.appendChild(g.createTextNode(f))}}}else if(t.type===Se.IDENT_TOKEN)switch(t.value){case"open-quote":w.appendChild(g.createTextNode(Ui(B.quotes,p.quoteDepth++,!0)));break;case"close-quote":w.appendChild(g.createTextNode(Ui(B.quotes,--p.quoteDepth,!1)));break;default:w.appendChild(g.createTextNode(t.value))}}),w.className=Cs+" "+Qs;var n=A===rs.BEFORE?" "+Cs:" "+Qs;return Mo(t)?t.className.baseValue+=n:t.className+=n,w}}},fs.destroy=function(t){return!!t.parentNode&&(t.parentNode.removeChild(t),!0)},fs);function fs(t,e){if(this.options=e,this.scrolledElements=[],this.referenceElement=t,this.counters=new Wo,this.quoteDepth=0,!t.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(t.ownerDocument.documentElement)}(ns=rs=rs||{})[ns.BEFORE=0]="BEFORE",ns[ns.AFTER=1]="AFTER";var ds,ps,gs=function(t,e){var A=t.createElement("iframe");return A.className="html2canvas-container",A.style.visibility="hidden",A.style.position="fixed",A.style.left="-10000px",A.style.top="0px",A.style.border="0",A.width=e.width.toString(),A.height=e.height.toString(),A.scrolling="no",A.setAttribute(ls,"true"),t.body.appendChild(A),A},Bs=function(n){return new Promise(function(e,t){var A=n.contentWindow;if(!A)return t("No window assigned for iframe");var r=A.document;A.onload=n.onload=r.onreadystatechange=function(){A.onload=n.onload=r.onreadystatechange=null;var t=setInterval(function(){0<r.body.childNodes.length&&"complete"===r.readyState&&(clearInterval(t),e(n))},50)}})},ws=function(t,e){for(var A=t.length-1;0<=A;A--){var r=t.item(A);"content"!==r&&e.style.setProperty(r,t.getPropertyValue(r))}return e},ms=function(t){var e="";return t&&(e+="<!DOCTYPE ",t.name&&(e+=t.name),t.internalSubset&&(e+=t.internalSubset),t.publicId&&(e+='"'+t.publicId+'"'),t.systemId&&(e+='"'+t.systemId+'"'),e+=">"),e},vs=function(t,e,A){t&&t.defaultView&&(e!==t.defaultView.pageXOffset||A!==t.defaultView.pageYOffset)&&t.defaultView.scrollTo(e,A)},ys=function(t){var e=t[0],A=t[1],r=t[2];e.scrollLeft=A,e.scrollTop=r},Cs="___html2canvas___pseudoelement_before",Qs="___html2canvas___pseudoelement_after",Fs='{\n    content: "" !important;\n    display: none !important;\n}',bs=function(t){Us(t,"."+Cs+":before"+Fs+"\n         ."+Qs+":after"+Fs)},Us=function(t,e){var A=t.ownerDocument;if(A){var r=A.createElement("style");r.textContent=e,t.appendChild(r)}};(ps=ds=ds||{})[ps.VECTOR=0]="VECTOR",ps[ps.BEZIER_CURVE=1]="BEZIER_CURVE";function Es(t,A){return t.length===A.length&&t.some(function(t,e){return t===A[e]})}var Ns=(Ls.prototype.add=function(t,e){return new Ls(this.x+t,this.y+e)},Ls);function Ls(t,e){this.type=ds.VECTOR,this.x=t,this.y=e}function Hs(t,e,A){return new Ns(t.x+(e.x-t.x)*A,t.y+(e.y-t.y)*A)}var Ss=(xs.prototype.subdivide=function(t,e){var A=Hs(this.start,this.startControl,t),r=Hs(this.startControl,this.endControl,t),n=Hs(this.endControl,this.end,t),i=Hs(A,r,t),o=Hs(r,n,t),s=Hs(i,o,t);return e?new xs(this.start,A,i,s):new xs(s,o,n,this.end)},xs.prototype.add=function(t,e){return new xs(this.start.add(t,e),this.startControl.add(t,e),this.endControl.add(t,e),this.end.add(t,e))},xs.prototype.reverse=function(){return new xs(this.end,this.endControl,this.startControl,this.start)},xs);function xs(t,e,A,r){this.type=ds.BEZIER_CURVE,this.start=t,this.startControl=e,this.endControl=A,this.end=r}function _s(t){return t.type===ds.BEZIER_CURVE}var Is,Ts,Os=function(t){var e=t.styles,A=t.bounds,r=QA(e.borderTopLeftRadius,A.width,A.height),n=r[0],i=r[1],o=QA(e.borderTopRightRadius,A.width,A.height),s=o[0],a=o[1],c=QA(e.borderBottomRightRadius,A.width,A.height),u=c[0],l=c[1],h=QA(e.borderBottomLeftRadius,A.width,A.height),f=h[0],d=h[1],p=[];p.push((n+s)/A.width),p.push((f+u)/A.width),p.push((i+d)/A.height),p.push((a+l)/A.height);var g=Math.max.apply(Math,p);1<g&&(n/=g,i/=g,s/=g,a/=g,u/=g,l/=g,f/=g,d/=g);var B=A.width-s,w=A.height-l,m=A.width-u,v=A.height-d,y=e.borderTopWidth,C=e.borderRightWidth,Q=e.borderBottomWidth,F=e.borderLeftWidth,b=IA(e.paddingTop,t.bounds.width),U=IA(e.paddingRight,t.bounds.width),E=IA(e.paddingBottom,t.bounds.width),N=IA(e.paddingLeft,t.bounds.width);this.topLeftBorderBox=0<n||0<i?Ms(A.left,A.top,n,i,Is.TOP_LEFT):new Ns(A.left,A.top),this.topRightBorderBox=0<s||0<a?Ms(A.left+B,A.top,s,a,Is.TOP_RIGHT):new Ns(A.left+A.width,A.top),this.bottomRightBorderBox=0<u||0<l?Ms(A.left+m,A.top+w,u,l,Is.BOTTOM_RIGHT):new Ns(A.left+A.width,A.top+A.height),this.bottomLeftBorderBox=0<f||0<d?Ms(A.left,A.top+v,f,d,Is.BOTTOM_LEFT):new Ns(A.left,A.top+A.height),this.topLeftPaddingBox=0<n||0<i?Ms(A.left+F,A.top+y,Math.max(0,n-F),Math.max(0,i-y),Is.TOP_LEFT):new Ns(A.left+F,A.top+y),this.topRightPaddingBox=0<s||0<a?Ms(A.left+Math.min(B,A.width+F),A.top+y,B>A.width+F?0:s-F,a-y,Is.TOP_RIGHT):new Ns(A.left+A.width-C,A.top+y),this.bottomRightPaddingBox=0<u||0<l?Ms(A.left+Math.min(m,A.width-F),A.top+Math.min(w,A.height+y),Math.max(0,u-C),l-Q,Is.BOTTOM_RIGHT):new Ns(A.left+A.width-C,A.top+A.height-Q),this.bottomLeftPaddingBox=0<f||0<d?Ms(A.left+F,A.top+v,Math.max(0,f-F),d-Q,Is.BOTTOM_LEFT):new Ns(A.left+F,A.top+A.height-Q),this.topLeftContentBox=0<n||0<i?Ms(A.left+F+N,A.top+y+b,Math.max(0,n-(F+N)),Math.max(0,i-(y+b)),Is.TOP_LEFT):new Ns(A.left+F+N,A.top+y+b),this.topRightContentBox=0<s||0<a?Ms(A.left+Math.min(B,A.width+F+N),A.top+y+b,B>A.width+F+N?0:s-F+N,a-(y+b),Is.TOP_RIGHT):new Ns(A.left+A.width-(C+U),A.top+y+b),this.bottomRightContentBox=0<u||0<l?Ms(A.left+Math.min(m,A.width-(F+N)),A.top+Math.min(w,A.height+y+b),Math.max(0,u-(C+U)),l-(Q+E),Is.BOTTOM_RIGHT):new Ns(A.left+A.width-(C+U),A.top+A.height-(Q+E)),this.bottomLeftContentBox=0<f||0<d?Ms(A.left+F+N,A.top+v,Math.max(0,f-(F+N)),d-(Q+E),Is.BOTTOM_LEFT):new Ns(A.left+F+N,A.top+A.height-(Q+E))};(Ts=Is=Is||{})[Ts.TOP_LEFT=0]="TOP_LEFT",Ts[Ts.TOP_RIGHT=1]="TOP_RIGHT",Ts[Ts.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",Ts[Ts.BOTTOM_LEFT=3]="BOTTOM_LEFT";function Rs(t){return[t.topLeftBorderBox,t.topRightBorderBox,t.bottomRightBorderBox,t.bottomLeftBorderBox]}function Ps(t){return[t.topLeftPaddingBox,t.topRightPaddingBox,t.bottomRightPaddingBox,t.bottomLeftPaddingBox]}var Ms=function(t,e,A,r,n){var i=(Math.sqrt(2)-1)/3*4,o=A*i,s=r*i,a=t+A,c=e+r;switch(n){case Is.TOP_LEFT:return new Ss(new Ns(t,c),new Ns(t,c-s),new Ns(a-o,e),new Ns(a,e));case Is.TOP_RIGHT:return new Ss(new Ns(t,e),new Ns(t+o,e),new Ns(a,c-s),new Ns(a,c));case Is.BOTTOM_RIGHT:return new Ss(new Ns(a,e),new Ns(a,e+s),new Ns(t+o,c),new Ns(t,c));case Is.BOTTOM_LEFT:default:return new Ss(new Ns(a,c),new Ns(a-o,c),new Ns(t,e+s),new Ns(t,e))}},Ks=function(t,e,A){this.type=0,this.offsetX=t,this.offsetY=e,this.matrix=A,this.target=6},Ds=function(t,e){this.type=1,this.target=e,this.path=t},ks=function(t){this.element=t,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},zs=(js.prototype.getParentEffects=function(){var t=this.effects.slice(0);if(this.container.styles.overflowX!==xn.VISIBLE){var e=Rs(this.curves),A=Ps(this.curves);Es(e,A)||t.push(new Ds(A,6))}return t},js);function js(t,e){if(this.container=t,this.effects=e.slice(0),this.curves=new Os(t),null!==t.styles.transform){var A=t.bounds.left+t.styles.transformOrigin[0].number,r=t.bounds.top+t.styles.transformOrigin[1].number,n=t.styles.transform;this.effects.push(new Ks(A,r,n))}if(t.styles.overflowX!==xn.VISIBLE){var i=Rs(this.curves),o=Ps(this.curves);Es(i,o)?this.effects.push(new Ds(i,6)):(this.effects.push(new Ds(i,2)),this.effects.push(new Ds(o,4)))}}function qs(t){var e=t.bounds,A=t.styles;return e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))}function Vs(t){var e=t.styles,A=t.bounds,r=IA(e.paddingLeft,A.width),n=IA(e.paddingRight,A.width),i=IA(e.paddingTop,A.width),o=IA(e.paddingBottom,A.width);return A.add(r+e.borderLeftWidth,i+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+i+o))}function Xs(t,e,A){var r,n,i,o,s=(r=ta(t.styles.backgroundOrigin,e),n=t,0===r?n.bounds:(2===r?Vs:qs)(n)),a=(i=ta(t.styles.backgroundClip,e),o=t,i===zA.BORDER_BOX?o.bounds:(i===zA.CONTENT_BOX?Vs:qs)(o)),c=$s(ta(t.styles.backgroundSize,e),A,s),u=c[0],l=c[1],h=QA(ta(t.styles.backgroundPosition,e),s.width-u,s.height-l);return[ea(ta(t.styles.backgroundRepeat,e),h,c,s,a),Math.round(s.left+h[0]),Math.round(s.top+h[1]),u,l]}function Gs(t){return dA(t)&&t.value===Mr.AUTO}function Js(t){return"number"==typeof t}var Ws=function(u,l,h,f){u.container.elements.forEach(function(t){var e=bi(t.flags,4),A=bi(t.flags,2),r=new zs(t,u.getParentEffects());bi(t.styles.display,2048)&&f.push(r);var n=bi(t.flags,8)?[]:f;if(e||A){var i=e||t.styles.isPositioned()?h:l,o=new ks(r);if(t.styles.isPositioned()||t.styles.opacity<1||t.styles.isTransformed()){var s=t.styles.zIndex.order;if(s<0){var a=0;i.negativeZIndex.some(function(t,e){return s>t.element.container.styles.zIndex.order?(a=e,!1):0<a}),i.negativeZIndex.splice(a,0,o)}else if(0<s){var c=0;i.positiveZIndex.some(function(t,e){return s>=t.element.container.styles.zIndex.order?(c=e+1,!1):0<c}),i.positiveZIndex.splice(c,0,o)}else i.zeroOrAutoZIndexOrTransformedOrOpacity.push(o)}else t.styles.isFloating()?i.nonPositionedFloats.push(o):i.nonPositionedInlineLevel.push(o);Ws(r,o,e?o:h,n)}else t.styles.isInlineLevel()?l.inlineLevel.push(r):l.nonInlineLevel.push(r),Ws(r,l,h,n);bi(t.flags,8)&&Ys(t,n)})},Ys=function(t,e){for(var A=t instanceof so?t.start:1,r=t instanceof so&&t.reversed,n=0;n<e.length;n++){var i=e[n];i.container instanceof no&&"number"==typeof i.container.value&&0!==i.container.value&&(A=i.container.value),i.listValue=us(A,i.container.styles.listStyleType,!0),A+=r?-1:1}},Zs=function(t,e,A,r){var n=[];return _s(t)?n.push(t.subdivide(.5,!1)):n.push(t),_s(A)?n.push(A.subdivide(.5,!0)):n.push(A),_s(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),_s(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},$s=function(t,e,A){var r=e[0],n=e[1],i=e[2],o=t[0],s=t[1];if(yA(o)&&s&&yA(s))return[IA(o,A.width),IA(s,A.height)];var a=Js(i);if(dA(o)&&(o.value===Mr.CONTAIN||o.value===Mr.COVER))return Js(i)?A.width/A.height<i!=(o.value===Mr.COVER)?[A.width,A.width/i]:[A.height*i,A.height]:[A.width,A.height];var c=Js(r),u=Js(n),l=c||u;if(Gs(o)&&(!s||Gs(s)))return c&&u?[r,n]:a||l?l&&a?[c?r:n*i,u?n:r/i]:[c?r:A.width,u?n:A.height]:[A.width,A.height];if(a){var h=0,f=0;return yA(o)?h=IA(o,A.width):yA(s)&&(f=IA(s,A.height)),Gs(o)?h=f*i:s&&!Gs(s)||(f=h/i),[h,f]}var d=null,p=null;if(yA(o)?d=IA(o,A.width):s&&yA(s)&&(p=IA(s,A.height)),null===d||s&&!Gs(s)||(p=c&&u?d/r*n:A.height),null!==p&&Gs(o)&&(d=c&&u?p/n*r:A.width),null!==d&&null!==p)return[d,p];throw new Error("Unable to calculate background-size for element")},ta=function(t,e){var A=t[e];return void 0===A?t[0]:A},ea=function(t,e,A,r,n){var i=e[0],o=e[1],s=A[0],a=A[1];switch(t){case _r.REPEAT_X:return[new Ns(Math.round(r.left),Math.round(r.top+o)),new Ns(Math.round(r.left+r.width),Math.round(r.top+o)),new Ns(Math.round(r.left+r.width),Math.round(a+r.top+o)),new Ns(Math.round(r.left),Math.round(a+r.top+o))];case _r.REPEAT_Y:return[new Ns(Math.round(r.left+i),Math.round(r.top)),new Ns(Math.round(r.left+i+s),Math.round(r.top)),new Ns(Math.round(r.left+i+s),Math.round(r.height+r.top)),new Ns(Math.round(r.left+i),Math.round(r.height+r.top))];case _r.NO_REPEAT:return[new Ns(Math.round(r.left+i),Math.round(r.top+o)),new Ns(Math.round(r.left+i+s),Math.round(r.top+o)),new Ns(Math.round(r.left+i+s),Math.round(r.top+o+a)),new Ns(Math.round(r.left+i),Math.round(r.top+o+a))];default:return[new Ns(Math.round(n.left),Math.round(n.top)),new Ns(Math.round(n.left+n.width),Math.round(n.top)),new Ns(Math.round(n.left+n.width),Math.round(n.height+n.top)),new Ns(Math.round(n.left),Math.round(n.height+n.top))]}},Aa="Hidden Text",ra=(na.prototype.parseMetrics=function(t,e){var A=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),i=this._document.body;A.style.visibility="hidden",A.style.fontFamily=t,A.style.fontSize=e,A.style.margin="0",A.style.padding="0",i.appendChild(A),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=t,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(Aa)),A.appendChild(n),A.appendChild(r);var o=r.offsetTop-n.offsetTop+2;A.removeChild(n),A.appendChild(this._document.createTextNode(Aa)),A.style.lineHeight="normal",r.style.verticalAlign="super";var s=r.offsetTop-A.offsetTop+2;return i.removeChild(A),{baseline:o,middle:s}},na.prototype.getMetrics=function(t,e){var A=t+" "+e;return void 0===this._data[A]&&(this._data[A]=this.parseMetrics(t,e)),this._data[A]},na);function na(t){this._data={},this._document=t}var ia=(oa.prototype.applyEffects=function(t,e){for(var A=this;this._activeEffects.length;)this.popEffect();t.filter(function(t){return bi(t.target,e)}).forEach(function(t){return A.applyEffect(t)})},oa.prototype.applyEffect=function(t){this.ctx.save(),0===t.type&&(this.ctx.translate(t.offsetX,t.offsetY),this.ctx.transform(t.matrix[0],t.matrix[1],t.matrix[2],t.matrix[3],t.matrix[4],t.matrix[5]),this.ctx.translate(-t.offsetX,-t.offsetY)),1===t.type&&(this.path(t.path),this.ctx.clip()),this._activeEffects.push(t)},oa.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},oa.prototype.renderStack=function(A){return Ft(this,void 0,void 0,function(){var e;return bt(this,function(t){switch(t.label){case 0:return(e=A.element.container.styles).isVisible()?(this.ctx.globalAlpha=e.opacity,[4,this.renderStackContent(A)]):[3,2];case 1:t.sent(),t.label=2;case 2:return[2]}})})},oa.prototype.renderNode=function(e){return Ft(this,void 0,void 0,function(){return bt(this,function(t){switch(t.label){case 0:return e.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(e)]:[3,3];case 1:return t.sent(),[4,this.renderNodeContent(e)];case 2:t.sent(),t.label=3;case 3:return[2]}})})},oa.prototype.renderTextWithLetterSpacing=function(A,t){var r=this;0===t?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+A.bounds.height):Lt(A.text).map(function(t){return Ht(t)}).reduce(function(t,e){return r.ctx.fillText(e,t,A.bounds.top+A.bounds.height),t+r.ctx.measureText(e).width},A.bounds.left)},oa.prototype.createFontStyle=function(t){var e=t.fontVariant.filter(function(t){return"normal"===t||"small-caps"===t}).join(""),A=t.fontFamily.join(", "),r=hA(t.fontSize)?""+t.fontSize.number+t.fontSize.unit:t.fontSize.number+"px";return[[t.fontStyle,e,t.fontWeight,r,A].join(" "),A,r]},oa.prototype.renderTextNode=function(r,s){return Ft(this,void 0,void 0,function(){var e,A,n,i,o=this;return bt(this,function(t){return e=this.createFontStyle(s),A=e[0],n=e[1],i=e[2],this.ctx.font=A,r.textBounds.forEach(function(r){o.ctx.fillStyle=EA(s.color),o.renderTextWithLetterSpacing(r,s.letterSpacing);var t=s.textShadow;t.length&&r.text.trim().length&&(t.slice(0).reverse().forEach(function(t){o.ctx.shadowColor=EA(t.color),o.ctx.shadowOffsetX=t.offsetX.number*o.options.scale,o.ctx.shadowOffsetY=t.offsetY.number*o.options.scale,o.ctx.shadowBlur=t.blur.number,o.ctx.fillText(r.text,r.bounds.left,r.bounds.top+r.bounds.height)}),o.ctx.shadowColor="",o.ctx.shadowOffsetX=0,o.ctx.shadowOffsetY=0,o.ctx.shadowBlur=0),s.textDecorationLine.length&&(o.ctx.fillStyle=EA(s.textDecorationColor||s.color),s.textDecorationLine.forEach(function(t){switch(t){case 1:var e=o.fontMetrics.getMetrics(n,i).baseline;o.ctx.fillRect(r.bounds.left,Math.round(r.bounds.top+e),r.bounds.width,1);break;case 2:o.ctx.fillRect(r.bounds.left,Math.round(r.bounds.top),r.bounds.width,1);break;case 3:var A=o.fontMetrics.getMetrics(n,i).middle;o.ctx.fillRect(r.bounds.left,Math.ceil(r.bounds.top+A),r.bounds.width,1)}}))}),[2]})})},oa.prototype.renderReplacedElement=function(t,e,A){if(A&&0<t.intrinsicWidth&&0<t.intrinsicHeight){var r=Vs(t),n=Ps(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(A,0,0,t.intrinsicWidth,t.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},oa.prototype.renderNodeContent=function(B){return Ft(this,void 0,void 0,function(){var r,n,i,o,s,a,c,u,l,h,f,d,p,g;return bt(this,function(t){switch(t.label){case 0:this.applyEffects(B.effects,4),r=B.container,n=B.curves,i=r.styles,o=0,s=r.textNodes,t.label=1;case 1:return o<s.length?(a=s[o],[4,this.renderTextNode(a,i)]):[3,4];case 2:t.sent(),t.label=3;case 3:return o++,[3,1];case 4:if(!(r instanceof Ji))return[3,8];t.label=5;case 5:return t.trys.push([5,7,,8]),[4,this.options.cache.match(r.src)];case 6:return d=t.sent(),this.renderReplacedElement(r,n,d),[3,8];case 7:return t.sent(),ir.getInstance(this.options.id).error("Error loading image "+r.src),[3,8];case 8:if(r instanceof Zi&&this.renderReplacedElement(r,n,r.canvas),!(r instanceof eo))return[3,12];t.label=9;case 9:return t.trys.push([9,11,,12]),[4,this.options.cache.match(r.svg)];case 10:return d=t.sent(),this.renderReplacedElement(r,n,d),[3,12];case 11:return t.sent(),ir.getInstance(this.options.id).error("Error loading svg "+r.svg.substring(0,255)),[3,12];case 12:return r instanceof Eo&&r.tree?[4,new oa({id:this.options.id,scale:this.options.scale,backgroundColor:r.backgroundColor,x:0,y:0,scrollX:0,scrollY:0,width:r.width,height:r.height,cache:this.options.cache,windowWidth:r.width,windowHeight:r.height}).render(r.tree)]:[3,14];case 13:c=t.sent(),r.width&&r.height&&this.ctx.drawImage(c,0,0,r.width,r.height,r.bounds.left,r.bounds.top,r.bounds.width,r.bounds.height),t.label=14;case 14:if(r instanceof Bo&&(u=Math.min(r.bounds.width,r.bounds.height),r.type===ho?r.checked&&(this.ctx.save(),this.path([new Ns(r.bounds.left+.39363*u,r.bounds.top+.79*u),new Ns(r.bounds.left+.16*u,r.bounds.top+.5549*u),new Ns(r.bounds.left+.27347*u,r.bounds.top+.44071*u),new Ns(r.bounds.left+.39694*u,r.bounds.top+.5649*u),new Ns(r.bounds.left+.72983*u,r.bounds.top+.23*u),new Ns(r.bounds.left+.84*u,r.bounds.top+.34085*u),new Ns(r.bounds.left+.39363*u,r.bounds.top+.79*u)]),this.ctx.fillStyle=EA(go),this.ctx.fill(),this.ctx.restore()):r.type===fo&&r.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(r.bounds.left+u/2,r.bounds.top+u/2,u/4,0,2*Math.PI,!0),this.ctx.fillStyle=EA(go),this.ctx.fill(),this.ctx.restore())),sa(r)&&r.value.length){switch(this.ctx.font=this.createFontStyle(i)[0],this.ctx.fillStyle=EA(i.color),this.ctx.textBaseline="middle",this.ctx.textAlign=ca(r.styles.textAlign),g=Vs(r),l=0,r.styles.textAlign){case kn.CENTER:l+=g.width/2;break;case kn.RIGHT:l+=g.width}h=g.add(l,0,0,-g.height/2+1),this.ctx.save(),this.path([new Ns(g.left,g.top),new Ns(g.left+g.width,g.top),new Ns(g.left+g.width,g.top+g.height),new Ns(g.left,g.top+g.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Ki(r.value,h),i.letterSpacing),this.ctx.restore(),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"}if(!bi(r.styles.display,2048))return[3,20];if(null===r.styles.listStyleImage)return[3,19];if((f=r.styles.listStyleImage).type!==hr.URL)return[3,18];d=void 0,p=f.url,t.label=15;case 15:return t.trys.push([15,17,,18]),[4,this.options.cache.match(p)];case 16:return d=t.sent(),this.ctx.drawImage(d,r.bounds.left-(d.width+10),r.bounds.top),[3,18];case 17:return t.sent(),ir.getInstance(this.options.id).error("Error loading list-style-image "+p),[3,18];case 18:return[3,20];case 19:B.listValue&&r.styles.listStyleType!==Nn.NONE&&(this.ctx.font=this.createFontStyle(i)[0],this.ctx.fillStyle=EA(i.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",g=new Ut(r.bounds.left,r.bounds.top+IA(r.styles.paddingTop,r.bounds.width),r.bounds.width,(e=i.lineHeight,A=i.fontSize.number,(dA(e)&&"normal"===e.value?1.2*A:e.type===Se.NUMBER_TOKEN?A*e.number:yA(e)?IA(e,A):A)/2+1)),this.renderTextWithLetterSpacing(new Ki(B.listValue,g),i.letterSpacing),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),t.label=20;case 20:return[2]}var e,A})})},oa.prototype.renderStackContent=function(g){return Ft(this,void 0,void 0,function(){var e,A,r,n,i,o,s,a,c,u,l,h,f,d,p;return bt(this,function(t){switch(t.label){case 0:return[4,this.renderNodeBackgroundAndBorders(g.element)];case 1:t.sent(),e=0,A=g.negativeZIndex,t.label=2;case 2:return e<A.length?(p=A[e],[4,this.renderStack(p)]):[3,5];case 3:t.sent(),t.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(g.element)];case 6:t.sent(),r=0,n=g.nonInlineLevel,t.label=7;case 7:return r<n.length?(p=n[r],[4,this.renderNode(p)]):[3,10];case 8:t.sent(),t.label=9;case 9:return r++,[3,7];case 10:i=0,o=g.nonPositionedFloats,t.label=11;case 11:return i<o.length?(p=o[i],[4,this.renderStack(p)]):[3,14];case 12:t.sent(),t.label=13;case 13:return i++,[3,11];case 14:s=0,a=g.nonPositionedInlineLevel,t.label=15;case 15:return s<a.length?(p=a[s],[4,this.renderStack(p)]):[3,18];case 16:t.sent(),t.label=17;case 17:return s++,[3,15];case 18:c=0,u=g.inlineLevel,t.label=19;case 19:return c<u.length?(p=u[c],[4,this.renderNode(p)]):[3,22];case 20:t.sent(),t.label=21;case 21:return c++,[3,19];case 22:l=0,h=g.zeroOrAutoZIndexOrTransformedOrOpacity,t.label=23;case 23:return l<h.length?(p=h[l],[4,this.renderStack(p)]):[3,26];case 24:t.sent(),t.label=25;case 25:return l++,[3,23];case 26:f=0,d=g.positiveZIndex,t.label=27;case 27:return f<d.length?(p=d[f],[4,this.renderStack(p)]):[3,30];case 28:t.sent(),t.label=29;case 29:return f++,[3,27];case 30:return[2]}})})},oa.prototype.mask=function(t){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(t.slice(0).reverse()),this.ctx.closePath()},oa.prototype.path=function(t){this.ctx.beginPath(),this.formatPath(t),this.ctx.closePath()},oa.prototype.formatPath=function(t){var r=this;t.forEach(function(t,e){var A=_s(t)?t.start:t;0===e?r.ctx.moveTo(A.x,A.y):r.ctx.lineTo(A.x,A.y),_s(t)&&r.ctx.bezierCurveTo(t.startControl.x,t.startControl.y,t.endControl.x,t.endControl.y,t.end.x,t.end.y)})},oa.prototype.renderRepeat=function(t,e,A,r){this.path(t),this.ctx.fillStyle=e,this.ctx.translate(A,r),this.ctx.fill(),this.ctx.translate(-A,-r)},oa.prototype.resizeImage=function(t,e,A){if(t.width===e&&t.height===A)return t;var r=this.canvas.ownerDocument.createElement("canvas");return r.width=e,r.height=A,r.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,e,A),r},oa.prototype.renderBackgroundImage=function(T){return Ft(this,void 0,void 0,function(){var _,e,I,A,r,n;return bt(this,function(t){switch(t.label){case 0:_=T.styles.backgroundImage.length-1,e=function(e){var A,r,n,i,o,s,a,c,u,l,h,f,d,p,g,B,w,m,v,y,C,Q,F,b,U,E,N,L,H,S,x;return bt(this,function(t){switch(t.label){case 0:if(e.type!==hr.URL)return[3,5];A=void 0,r=e.url,t.label=1;case 1:return t.trys.push([1,3,,4]),[4,I.options.cache.match(r)];case 2:return A=t.sent(),[3,4];case 3:return t.sent(),ir.getInstance(I.options.id).error("Error loading background-image "+r),[3,4];case 4:return A&&(n=Xs(T,_,[A.width,A.height,A.width/A.height]),B=n[0],Q=n[1],F=n[2],v=n[3],y=n[4],p=I.ctx.createPattern(I.resizeImage(A,v,y),"repeat"),I.renderRepeat(B,p,Q,F)),[3,6];case 5:e.type===hr.LINEAR_GRADIENT?(i=Xs(T,_,[null,null,null]),B=i[0],Q=i[1],F=i[2],v=i[3],y=i[4],o=JA(e.angle,v,y),s=o[0],a=o[1],c=o[2],u=o[3],l=o[4],(h=document.createElement("canvas")).width=v,h.height=y,f=h.getContext("2d"),d=f.createLinearGradient(a,u,c,l),GA(e.stops,s).forEach(function(t){return d.addColorStop(t.stop,EA(t.color))}),f.fillStyle=d,f.fillRect(0,0,v,y),0<v&&0<y&&(p=I.ctx.createPattern(h,"repeat"),I.renderRepeat(B,p,Q,F))):e.type===hr.RADIAL_GRADIENT&&(g=Xs(T,_,[null,null,null]),B=g[0],w=g[1],m=g[2],v=g[3],y=g[4],C=0===e.position.length?[xA]:e.position,Q=IA(C[0],v),F=IA(C[C.length-1],y),b=function(t,e,A,r,n){var i=0,o=0;switch(t.size){case Hr.CLOSEST_SIDE:t.shape===Nr.CIRCLE?i=o=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(A),Math.abs(A-n)):t.shape===Nr.ELLIPSE&&(i=Math.min(Math.abs(e),Math.abs(e-r)),o=Math.min(Math.abs(A),Math.abs(A-n)));break;case Hr.CLOSEST_CORNER:if(t.shape===Nr.CIRCLE)i=o=Math.min(WA(e,A),WA(e,A-n),WA(e-r,A),WA(e-r,A-n));else if(t.shape===Nr.ELLIPSE){var s=Math.min(Math.abs(A),Math.abs(A-n))/Math.min(Math.abs(e),Math.abs(e-r)),a=YA(r,n,e,A,!0),c=a[0],u=a[1];o=s*(i=WA(c-e,(u-A)/s))}break;case Hr.FARTHEST_SIDE:t.shape===Nr.CIRCLE?i=o=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(A),Math.abs(A-n)):t.shape===Nr.ELLIPSE&&(i=Math.max(Math.abs(e),Math.abs(e-r)),o=Math.max(Math.abs(A),Math.abs(A-n)));break;case Hr.FARTHEST_CORNER:if(t.shape===Nr.CIRCLE)i=o=Math.max(WA(e,A),WA(e,A-n),WA(e-r,A),WA(e-r,A-n));else if(t.shape===Nr.ELLIPSE){s=Math.max(Math.abs(A),Math.abs(A-n))/Math.max(Math.abs(e),Math.abs(e-r));var l=YA(r,n,e,A,!1);c=l[0],u=l[1],o=s*(i=WA(c-e,(u-A)/s))}}return Array.isArray(t.size)&&(i=IA(t.size[0],r),o=2===t.size.length?IA(t.size[1],n):i),[i,o]}(e,Q,F,v,y),U=b[0],E=b[1],0<U&&0<U&&(N=I.ctx.createRadialGradient(w+Q,m+F,0,w+Q,m+F,U),GA(e.stops,2*U).forEach(function(t){return N.addColorStop(t.stop,EA(t.color))}),I.path(B),I.ctx.fillStyle=N,U!==E?(L=T.bounds.left+.5*T.bounds.width,H=T.bounds.top+.5*T.bounds.height,x=1/(S=E/U),I.ctx.save(),I.ctx.translate(L,H),I.ctx.transform(1,0,0,S,0,0),I.ctx.translate(-L,-H),I.ctx.fillRect(w,x*(m-H)+H,v,y*x),I.ctx.restore()):I.ctx.fill())),t.label=6;case 6:return _--,[2]}})},I=this,A=0,r=T.styles.backgroundImage.slice(0).reverse(),t.label=1;case 1:return A<r.length?(n=r[A],[5,e(n)]):[3,4];case 2:t.sent(),t.label=3;case 3:return A++,[3,1];case 4:return[2]}})})},oa.prototype.renderBorder=function(e,A,r){return Ft(this,void 0,void 0,function(){return bt(this,function(t){return this.path(function(t,e){switch(e){case 0:return Zs(t.topLeftBorderBox,t.topLeftPaddingBox,t.topRightBorderBox,t.topRightPaddingBox);case 1:return Zs(t.topRightBorderBox,t.topRightPaddingBox,t.bottomRightBorderBox,t.bottomRightPaddingBox);case 2:return Zs(t.bottomRightBorderBox,t.bottomRightPaddingBox,t.bottomLeftBorderBox,t.bottomLeftPaddingBox);case 3:default:return Zs(t.bottomLeftBorderBox,t.bottomLeftPaddingBox,t.topLeftBorderBox,t.topLeftPaddingBox)}}(r,A)),this.ctx.fillStyle=EA(e),this.ctx.fill(),[2]})})},oa.prototype.renderNodeBackgroundAndBorders=function(u){return Ft(this,void 0,void 0,function(){var e,A,r,n,i,o,s,a,c=this;return bt(this,function(t){switch(t.label){case 0:return this.applyEffects(u.effects,2),e=u.container.styles,A=!UA(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor},{style:e.borderRightStyle,color:e.borderRightColor},{style:e.borderBottomStyle,color:e.borderBottomColor},{style:e.borderLeftStyle,color:e.borderLeftColor}],n=aa(ta(e.backgroundClip,0),u.curves),A||e.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),UA(e.backgroundColor)||(this.ctx.fillStyle=EA(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(u.container)]):[3,2];case 1:t.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach(function(t){c.ctx.save();var e,A,r,n,i,o=Rs(u.curves),s=t.inset?0:1e4,a=(e=o,A=-s+(t.inset?1:-1)*t.spread.number,r=(t.inset?1:-1)*t.spread.number,n=t.spread.number*(t.inset?-2:2),i=t.spread.number*(t.inset?-2:2),e.map(function(t,e){switch(e){case 0:return t.add(A,r);case 1:return t.add(A+n,r);case 2:return t.add(A+n,r+i);case 3:return t.add(A,r+i)}return t}));t.inset?(c.path(o),c.ctx.clip(),c.mask(a)):(c.mask(o),c.ctx.clip(),c.path(a)),c.ctx.shadowOffsetX=t.offsetX.number+s,c.ctx.shadowOffsetY=t.offsetY.number,c.ctx.shadowColor=EA(t.color),c.ctx.shadowBlur=t.blur.number,c.ctx.fillStyle=t.inset?EA(t.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()}),t.label=2;case 2:o=i=0,s=r,t.label=3;case 3:return o<s.length?(a=s[o]).style===qr.NONE||UA(a.color)?[3,5]:[4,this.renderBorder(a.color,i,u.curves)]:[3,7];case 4:t.sent(),t.label=5;case 5:i++,t.label=6;case 6:return o++,[3,3];case 7:return[2]}})})},oa.prototype.render=function(n){return Ft(this,void 0,void 0,function(){return bt(this,function(t){switch(t.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=EA(this.options.backgroundColor),this.ctx.fillRect(this.options.x-this.options.scrollX,this.options.y-this.options.scrollY,this.options.width,this.options.height)),e=new zs(n,[]),A=new ks(e),Ws(e,A,A,r=[]),Ys(e.container,r),[4,this.renderStack(A)];case 1:return t.sent(),this.applyEffects([],2),[2,this.canvas]}var e,A,r})})},oa);function oa(t){this._activeEffects=[],this.canvas=t.canvas?t.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),(this.options=t).canvas||(this.canvas.width=Math.floor(t.width*t.scale),this.canvas.height=Math.floor(t.height*t.scale),this.canvas.style.width=t.width+"px",this.canvas.style.height=t.height+"px"),this.fontMetrics=new ra(document),this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-t.x+t.scrollX,-t.y+t.scrollY),this.ctx.textBaseline="bottom",this._activeEffects=[],ir.getInstance(t.id).debug("Canvas renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale)}var sa=function(t){return t instanceof Qo||(t instanceof vo||t instanceof Bo&&t.type!==fo&&t.type!==ho)},aa=function(t,e){switch(t){case zA.BORDER_BOX:return Rs(e);case zA.CONTENT_BOX:return[(A=e).topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox];case zA.PADDING_BOX:default:return Ps(e)}var A},ca=function(t){switch(t){case kn.CENTER:return"center";case kn.RIGHT:return"right";case kn.LEFT:default:return"left"}},ua=(la.prototype.render=function(r){return Ft(this,void 0,void 0,function(){var e,A;return bt(this,function(t){switch(t.label){case 0:return e=Ar(Math.max(this.options.windowWidth,this.options.width)*this.options.scale,Math.max(this.options.windowHeight,this.options.height)*this.options.scale,this.options.scrollX*this.options.scale,this.options.scrollY*this.options.scale,r),[4,fa(e)];case 1:return A=t.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=EA(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(A,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},la);function la(t){this.canvas=t.canvas?t.canvas:document.createElement("canvas"),this.ctx=this.canvas.getContext("2d"),this.options=t,this.canvas.width=Math.floor(t.width*t.scale),this.canvas.height=Math.floor(t.height*t.scale),this.canvas.style.width=t.width+"px",this.canvas.style.height=t.height+"px",this.ctx.scale(this.options.scale,this.options.scale),this.ctx.translate(-t.x+t.scrollX,-t.y+t.scrollY),ir.getInstance(t.id).debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale)}function ha(t){return RA(uA.create(t).parseComponentValue())}var fa=function(r){return new Promise(function(t,e){var A=new Image;A.onload=function(){t(A)},A.onerror=e,A.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})};"undefined"!=typeof window&&sr.setContext(window);function da(t){var e=void 0===t?"undefined":ma(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"}function pa(t,e){var A=document.createElement(t);if(e.className&&(A.className=e.className),e.innerHTML){A.innerHTML=e.innerHTML;for(var r=A.getElementsByTagName("script"),n=r.length;0<n--;)r[n].parentNode.removeChild(r[n])}for(var i in e.style)A.style[i]=e.style[i];return A}function ga(t,e){if("number"===da(t))return 72*t/96/e;var A={};for(var r in t)A[r]=72*t[r]/96/e;return A}function Ba(t,e){return Math.floor(t*e/72*96)}var wa=function(F,b){return Ft(void 0,void 0,void 0,function(){var e,A,r,n,i,o,s,a,c,u,l,h,f,d,p,g,B,w,m,v,y,C,Q;return bt(this,function(t){switch(t.label){case 0:if(!(e=F.ownerDocument))throw new Error("Element is not attached to a Document");if(!(A=e.defaultView))throw new Error("Document is not attached to a Window");return r=(Math.round(1e3*Math.random())+Date.now()).toString(16),n=jo(F)||"HTML"===F.tagName?function(t){var e=t.body,A=t.documentElement;if(!e||!A)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,A.scrollWidth),Math.max(e.offsetWidth,A.offsetWidth),Math.max(e.clientWidth,A.clientWidth)),n=Math.max(Math.max(e.scrollHeight,A.scrollHeight),Math.max(e.offsetHeight,A.offsetHeight),Math.max(e.clientHeight,A.clientHeight));return new Ut(0,0,r,n)}(e):Nt(F),i=n.width,o=n.height,s=n.left,a=n.top,c=Qt({},{allowTaint:!1,imageTimeout:15e3,proxy:void 0,useCORS:!1},b),u={backgroundColor:"#ffffff",cache:b.cache?b.cache:sr.create(r,c),logging:!0,removeContainer:!0,foreignObjectRendering:!1,scale:A.devicePixelRatio||1,windowWidth:A.innerWidth,windowHeight:A.innerHeight,scrollX:A.pageXOffset,scrollY:A.pageYOffset,x:s,y:a,width:Math.ceil(i),height:Math.ceil(o),id:r},l=Qt({},u,c,b),h=new Ut(l.scrollX,l.scrollY,l.windowWidth,l.windowHeight),ir.create({id:r,enabled:l.logging}),ir.getInstance(r).debug("Starting document clone"),f=new hs(F,{id:r,onclone:l.onclone,ignoreElements:l.ignoreElements,inlineImages:l.foreignObjectRendering,copyStyles:l.foreignObjectRendering}),(d=f.clonedReferenceElement)?[4,f.toIFrame(e,h)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return(p=t.sent(),g=e.documentElement?ha(getComputedStyle(e.documentElement).backgroundColor):VA.TRANSPARENT,B=e.body?ha(getComputedStyle(e.body).backgroundColor):VA.TRANSPARENT,w=b.backgroundColor,m="string"==typeof w?ha(w):null===w?VA.TRANSPARENT:4294967295,v=F===e.documentElement?UA(g)?UA(B)?m:B:g:m,y={id:r,cache:l.cache,canvas:l.canvas,backgroundColor:v,scale:l.scale,x:l.x,y:l.y,scrollX:l.scrollX,scrollY:l.scrollY,width:l.width,height:l.height,windowWidth:l.windowWidth,windowHeight:l.windowHeight},l.foreignObjectRendering)?(ir.getInstance(r).debug("Document cloned, using foreign object rendering"),[4,new ua(y).render(d)]):[3,3];case 2:return C=t.sent(),[3,5];case 3:return ir.getInstance(r).debug("Document cloned, using computed rendering"),sr.attachInstance(l.cache),ir.getInstance(r).debug("Starting DOM parsing"),Q=_o(d),sr.detachInstance(),v===Q.styles.backgroundColor&&(Q.styles.backgroundColor=VA.TRANSPARENT),ir.getInstance(r).debug("Starting renderer"),[4,new ia(y).render(Q)];case 4:C=t.sent(),t.label=5;case 5:return!0===l.removeContainer&&(hs.destroy(p)||ir.getInstance(r).error("Cannot detach cloned iframe as it is not in the DOM anymore")),ir.getInstance(r).debug("Finished rendering"),ir.destroy(r),sr.destroy(r),[2,C]}})})},ma="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},va=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var A=arguments[e];for(var r in A)Object.prototype.hasOwnProperty.call(A,r)&&(t[r]=A[r])}return t},ya="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function Ca(){throw new Error("Dynamic requires are not currently supported by rollup-plugin-commonjs")}function Qa(t){var e=va(Qa.convert(ba.resolve()),JSON.parse(JSON.stringify(Qa.template))),A=Qa.convert(ba.resolve(),e);return A=(A=A.setProgress(1,Qa,1,[Qa])).set(t)}var Fa,ba=(function(t){t.exports=function(){function A(t){var e=typeof t;return t!==null&&(e==="object"||e==="function")}function c(t){return typeof t==="function"}var t=void 0;if(Array.isArray){t=Array.isArray}else{t=function(t){return Object.prototype.toString.call(t)==="[object Array]"}}var r=t,n=0,e=void 0,i=void 0,o=function t(e,A){v[n]=e;v[n+1]=A;n+=2;if(n===2){if(i){i(y)}else{Q()}}};function s(t){i=t}function a(t){o=t}var u=typeof window!=="undefined"?window:undefined,l=u||{},h=l.MutationObserver||l.WebKitMutationObserver,f=typeof self==="undefined"&&typeof process!=="undefined"&&{}.toString.call(process)==="[object process]",d=typeof Uint8ClampedArray!=="undefined"&&typeof importScripts!=="undefined"&&typeof MessageChannel!=="undefined";function p(){return function(){return process.nextTick(y)}}function g(){if(typeof e!=="undefined"){return function(){e(y)}}return m()}function B(){var t=0;var e=new h(y);var A=document.createTextNode("");e.observe(A,{characterData:true});return function(){A.data=t=++t%2}}function w(){var t=new MessageChannel;t.port1.onmessage=y;return function(){return t.port2.postMessage(0)}}function m(){var t=setTimeout;return function(){return t(y,1)}}var v=new Array(1e3);function y(){for(var t=0;t<n;t+=2){var e=v[t];var A=v[t+1];e(A);v[t]=undefined;v[t+1]=undefined}n=0}function C(){try{var t=Function("return this")().require("vertx");e=t.runOnLoop||t.runOnContext;return g()}catch(t){return m()}}var Q=void 0;if(f){Q=p()}else if(h){Q=B()}else if(d){Q=w()}else if(u===undefined&&typeof Ca==="function"){Q=C()}else{Q=m()}function F(t,e){var A=this;var r=new this.constructor(E);if(r[U]===undefined){W(r)}var n=A._state;if(n){var i=arguments[n-1];o(function(){return V(n,r,i,A._result)})}else{z(A,r,t,e)}return r}function b(t){var e=this;if(t&&typeof t==="object"&&t.constructor===e){return t}var A=new e(E);M(A,t);return A}var U=Math.random().toString(36).substring(2);function E(){}var N=void 0,L=1,H=2,S={error:null};function x(){return new TypeError("You cannot resolve a promise with itself")}function _(){return new TypeError("A promises callback cannot return that same promise.")}function I(t){try{return t.then}catch(t){S.error=t;return S}}function T(t,e,A,r){try{t.call(e,A,r)}catch(t){return t}}function O(t,r,n){o(function(e){var A=false;var t=T(n,r,function(t){if(A){return}A=true;if(r!==t){M(e,t)}else{D(e,t)}},function(t){if(A){return}A=true;k(e,t)},"Settle: "+(e._label||" unknown promise"));if(!A&&t){A=true;k(e,t)}},t)}function R(e,t){if(t._state===L){D(e,t._result)}else if(t._state===H){k(e,t._result)}else{z(t,undefined,function(t){return M(e,t)},function(t){return k(e,t)})}}function P(t,e,A){if(e.constructor===t.constructor&&A===F&&e.constructor.resolve===b){R(t,e)}else{if(A===S){k(t,S.error);S.error=null}else if(A===undefined){D(t,e)}else if(c(A)){O(t,e,A)}else{D(t,e)}}}function M(t,e){if(t===e){k(t,x())}else if(A(e)){P(t,e,I(e))}else{D(t,e)}}function K(t){if(t._onerror){t._onerror(t._result)}j(t)}function D(t,e){if(t._state!==N){return}t._result=e;t._state=L;if(t._subscribers.length!==0){o(j,t)}}function k(t,e){if(t._state!==N){return}t._state=H;t._result=e;o(K,t)}function z(t,e,A,r){var n=t._subscribers;var i=n.length;t._onerror=null;n[i]=e;n[i+L]=A;n[i+H]=r;if(i===0&&t._state){o(j,t)}}function j(t){var e=t._subscribers;var A=t._state;if(e.length===0){return}var r=void 0,n=void 0,i=t._result;for(var o=0;o<e.length;o+=3){r=e[o];n=e[o+A];if(r){V(A,r,n,i)}else{n(i)}}t._subscribers.length=0}function q(t,e){try{return t(e)}catch(t){S.error=t;return S}}function V(t,e,A,r){var n=c(A),i=void 0,o=void 0,s=void 0,a=void 0;if(n){i=q(A,r);if(i===S){a=true;o=i.error;i.error=null}else{s=true}if(e===i){k(e,_());return}}else{i=r;s=true}if(e._state!==N);else if(n&&s){M(e,i)}else if(a){k(e,o)}else if(t===L){D(e,i)}else if(t===H){k(e,i)}}function X(A,t){try{t(function t(e){M(A,e)},function t(e){k(A,e)})}catch(t){k(A,t)}}var G=0;function J(){return G++}function W(t){t[U]=G++;t._state=undefined;t._result=undefined;t._subscribers=[]}function Y(){return new Error("Array Methods must be provided an Array")}var Z=function(){function t(t,e){this._instanceConstructor=t;this.promise=new t(E);if(!this.promise[U]){W(this.promise)}if(r(e)){this.length=e.length;this._remaining=e.length;this._result=new Array(this.length);if(this.length===0){D(this.promise,this._result)}else{this.length=this.length||0;this._enumerate(e);if(this._remaining===0){D(this.promise,this._result)}}}else{k(this.promise,Y())}}t.prototype._enumerate=function t(e){for(var A=0;this._state===N&&A<e.length;A++){this._eachEntry(e[A],A)}};t.prototype._eachEntry=function t(e,A){var r=this._instanceConstructor;var n=r.resolve;if(n===b){var i=I(e);if(i===F&&e._state!==N){this._settledAt(e._state,A,e._result)}else if(typeof i!=="function"){this._remaining--;this._result[A]=e}else if(r===nt){var o=new r(E);P(o,e,i);this._willSettleAt(o,A)}else{this._willSettleAt(new r(function(t){return t(e)}),A)}}else{this._willSettleAt(n(e),A)}};t.prototype._settledAt=function t(e,A,r){var n=this.promise;if(n._state===N){this._remaining--;if(e===H){k(n,r)}else{this._result[A]=r}}if(this._remaining===0){D(n,this._result)}};t.prototype._willSettleAt=function t(e,A){var r=this;z(e,undefined,function(t){return r._settledAt(L,A,t)},function(t){return r._settledAt(H,A,t)})};return t}();function $(t){return new Z(this,t).promise}function tt(n){var i=this;if(r(n))return new i(function(t,e){for(var A=n.length,r=0;r<A;r++)i.resolve(n[r]).then(t,e)});else return new i(function(t,e){return e(new TypeError("You must pass an array to race."))})}function et(t){var e=new this(E);return k(e,t),e}function At(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function rt(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}var nt=function(){function e(t){this[U]=J();this._result=this._state=undefined;this._subscribers=[];if(E!==t){typeof t!=="function"&&At();this instanceof e?X(this,t):rt()}}e.prototype.catch=function t(e){return this.then(null,e)};e.prototype.finally=function t(e){var A=this;var r=A.constructor;if(c(e)){return A.then(function(t){return r.resolve(e()).then(function(){return t})},function(t){return r.resolve(e()).then(function(){throw t})})}return A.then(e,e)};return e}();function it(){var t=void 0;if(void 0!==ya)t=ya;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var A=null;try{A=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===A&&!e.cast)return}t.Promise=nt}return nt.prototype.then=F,nt.all=function(t){return new Z(this,t).promise},nt.race=function(n){var i=this;return r(n)?new i(function(t,e){for(var A=n.length,r=0;r<A;r++)i.resolve(n[r]).then(t,e)}):new i(function(t,e){return e(new TypeError("You must pass an array to race."))})},nt.resolve=b,nt.reject=function(t){var e=new this(E);return k(e,t),e},nt._setScheduler=function(t){i=t},nt._setAsap=function(t){o=t},nt._asap=o,nt.polyfill=function(){var t=void 0;if(void 0!==ya)t=ya;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var A=null;try{A=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===A&&!e.cast)return}t.Promise=nt},nt.Promise=nt}()}(Fa={exports:{}}),Fa.exports).Promise;((Qa.prototype=Object.create(ba.prototype)).constructor=Qa).convert=function(t,e){return t.__proto__=e||Qa.prototype,t},Qa.template={prop:{doc:document,src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],image:{type:"jpeg",quality:.95},enableLinks:!0,html2canvas:{scale:window.devicePixelRatio||1},jsPDF:{}}},Qa.prototype.from=function(t,e){return this.then(function(){switch(e=e||function(t){switch(da(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.set({src:pa("div",{innerHTML:t})});case"element":return this.set({src:t,doc:t.ownerDocument});case"canvas":return this.set({canvas:t,doc:t.ownerDocument});case"img":return this.set({img:t,doc:t.ownerDocument});default:return this.error("Unknown source type.")}})},Qa.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},Qa.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var t=this.prop.src;t.style.width=this.prop.pageSize.inner.width+this.prop.pageSize.unit,t.style.height="auto",t.style.margin="auto",t.style.left="0",t.style.top="0",t.style.position="absolute",this.prop.container=t})},Qa.prototype.toCanvas=function(){return this.thenList([function(){return this.prop.doc.body.contains(this.prop.container)||this.toContainer()}]).then(function(){var t,e,A=va({},this.opt.html2canvas);return delete A.onrendered,t=this.prop.container,void 0===(e=A)&&(e={}),wa(t,e)}).then(function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t})},Qa.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t})},Qa.prototype.toPdf=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var t=this.prop.canvas,e=this.opt,A=e.html2canvas.scale,r=t.height,n=Math.floor(this.prop.pageSize.inner.px.height*A),i=Math.floor(this.prop.pageSize.inner.px.width*A),o=Math.ceil(r/n),s=this.prop.pageSize.inner.height,a=n,c=document.createElement("canvas"),u=c.getContext("2d");c.width=this.prop.pageSize.inner.px.width,c.height=this.prop.pageSize.inner.px.height,this.prop.pdf=this.prop.pdf||new vt(e.jsPDF);for(var l=0;l<o;l++){l===o-1&&r%n!=0&&(a=r%n,c.height=Math.round(a/A),s=a*this.prop.pageSize.inner.width/i);var h=c.width,f=c.height;u.fillStyle="white",u.fillRect(0,0,h,f),u.drawImage(t,0,l*n,i,a,0,0,h,f),l&&this.prop.pdf.addPage();var d=c.toDataURL("image/"+e.image.type,e.image.quality);this.prop.pdf.addImage(d,e.image.type,e.margin[1],e.margin[0],this.prop.pageSize.inner.width,s)}})},Qa.prototype.output=function(t,e,A){return"img"===(A=A||"pdf").toLowerCase()||"image"===A.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},Qa.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(t,e)})},Qa.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}})},Qa.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},Qa.prototype.set=function(e){if("object"!==da(e))return this;var t=Object.keys(e||{}).map(function(t){if(t in Qa.template.prop)return function(){this.prop[t]=e[t]};switch(t){case"margin":return this.setMargin.bind(this,e.margin);case"jsPDF":return function(){return this.opt.jsPDF=e.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,e.pageSize);default:return function(){this.opt[t]=e[t]}}},this);return this.then(function(){return this.thenList(t)})},Qa.prototype.get=function(e,A){return this.then(function(){var t=e in Qa.template.prop?this.prop[e]:this.opt[e];return A?A(t):t})},Qa.prototype.setMargin=function(t){return this.then(function(){switch(da(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t}).then(this.setPageSize)},Qa.prototype.setPageSize=function(t){return this.then(function(){(t=t||vt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:Ba(t.inner.width,t.k),height:Ba(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t})},Qa.prototype.setProgress=function(t,e,A,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=A&&(this.progress.n=A),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},Qa.prototype.updateProgress=function(t,e,A,r){return this.setProgress(t?this.progress.val+t:null,e||null,A?this.progress.n+A:null,r?this.progress.stack.concat(r):null)},Qa.prototype.then=function(t,e){var A=this;return this.thenCore(t,e,function(e,t){return A.updateProgress(null,null,1,[e]),ba.prototype.then.call(this,function(t){return A.updateProgress(null,e),t}).then(e,t).then(function(t){return A.updateProgress(1),t})})},Qa.prototype.thenCore=function(t,e,A){A=A||ba.prototype.then;t=t&&t.bind(this),e=e&&e.bind(this);var r=-1!==ba.toString().indexOf("[native code]")&&"Promise"===ba.name?this:Qa.convert(va({},this),ba.prototype),n=A.call(r,t,e);return Qa.convert(n,this.__proto__)},Qa.prototype.thenExternal=function(t,e){return ba.prototype.then.call(this,t,e)},Qa.prototype.thenList=function(t){var e=this;return t.forEach(function(t){e=e.thenCore(t)}),e},Qa.prototype.catch=function(t){t=t&&t.bind(this);var e=ba.prototype.catch.call(this,t);return Qa.convert(e,this)},Qa.prototype.catchExternal=function(t){return ba.prototype.catch.call(this,t)},Qa.prototype.error=function(t){return this.then(function(){throw new Error(t)})},Qa.prototype.using=Qa.prototype.set,Qa.prototype.saveAs=Qa.prototype.save,Qa.prototype.export=Qa.prototype.output,Qa.prototype.run=Qa.prototype.then,vt.getPageSize=function(t,e,A){if("object"===(void 0===t?"undefined":ma(t))){var r=t;t=r.orientation,e=r.unit||e,A=r.format||A}e=e||"mm",A=A||"a4",t=(""+(t||"P")).toLowerCase();var n=(""+A).toLowerCase(),i={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":var o=1;break;case"mm":o=72/25.4;break;case"cm":o=72/2.54;break;case"in":o=72;break;case"px":o=.75;break;case"pc":case"em":o=12;break;case"ex":o=6;break;default:throw"Invalid unit: "+e}if(i.hasOwnProperty(n))var s=i[n][1]/o,a=i[n][0]/o;else try{s=A[1],a=A[0]}catch(t){throw new Error("Invalid format: "+A)}if("p"===t||"portrait"===t){if(t="p",s<a){var c=a;a=s,s=c}}else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;if(t="l",a<s){c=a;a=s,s=c}}return{width:a,height:s,unit:e,k:o}};var Ua={toContainer:Qa.prototype.toContainer};Qa.template.opt.pagebreak={mode:["css","legacy"],before:[],after:[],avoid:[]};var Ea=[],Na={toContainer:Qa.prototype.toContainer=function(){return Ua.toContainer.call(this).then(function(){var A=this.prop.container,c=this.prop.pageSize.inner.px.height,t=[].concat(this.opt.pagebreak.mode),u={avoidAll:-1!==t.indexOf("avoid-all"),css:-1!==t.indexOf("css"),legacy:-1!==t.indexOf("legacy")},l={},r=this;["before","after","avoid"].forEach(function(t){var e=u.avoidAll&&"avoid"===t;l[t]=e?[]:[].concat(r.opt.pagebreak[t]||[]),0<l[t].length&&(l[t]=Array.prototype.slice.call(A.querySelectorAll(l[t].join(", "))))});var h=A.querySelectorAll(".html2pdf__page-break");h=Array.prototype.slice.call(h);var e=A.querySelectorAll("*");Array.prototype.forEach.call(e,function(e){var A={before:!1,after:u.legacy&&-1!==h.indexOf(e),avoid:u.avoidAll};if(u.css){var t=window.getComputedStyle(e),r=["always","page","left","right"];A={before:A.before||-1!==r.indexOf(t.breakBefore||t.pageBreakBefore),after:A.after||-1!==r.indexOf(t.breakAfter||t.pageBreakAfter),avoid:A.avoid||-1!==["avoid","avoid-page"].indexOf(t.breakInside||t.pageBreakInside)}}Object.keys(A).forEach(function(t){A[t]=A[t]||-1!==l[t].indexOf(e)});var n=e.getBoundingClientRect();if(A.avoid&&!A.before){var i=Math.floor(n.top/c),o=Math.floor(n.bottom/c),s=Math.abs(n.bottom-n.top)/c;o!==i&&s<=1&&(A.before=!0)}if(A.before){var a=pa("div",{style:{display:"block",height:c-n.top%c+"px"}});e.parentNode.insertBefore(a,e)}if(A.after){a=pa("div",{style:{display:"block",height:c-n.bottom%c+"px"}});e.parentNode.insertBefore(a,e.nextSibling)}})})},toPdf:Qa.prototype.toPdf};Qa.prototype.toContainer=function(){return Na.toContainer.call(this).then(function(){if(this.opt.enableLinks){var t=this.prop.container,e=t.querySelectorAll("a"),s=ga(t.getBoundingClientRect(),this.prop.pageSize.k);Ea=[],Array.prototype.forEach.call(e,function(t){for(var e=t.getClientRects(),A=0;A<e.length;A++){var r=ga(e[A],this.prop.pageSize.k);r.left-=s.left,r.top-=s.top;var n=Math.floor(r.top/this.prop.pageSize.inner.height)+1,i=this.opt.margin[0]+r.top%this.prop.pageSize.inner.height,o=this.opt.margin[1]+r.left;Ea.push({page:n,top:i,left:o,clientRect:r,link:t})}},this)}})},Qa.prototype.toPdf=function(){return Na.toPdf.call(this).then(function(){if(this.opt.enableLinks){Ea.forEach(function(t){this.prop.pdf.setPage(t.page),this.prop.pdf.link(t.left,t.top,t.clientRect.width,t.clientRect.height,{url:t.link.href})},this);var t=this.prop.pdf.internal.getNumberOfPages();this.prop.pdf.setPage(t)}})};function La(t,e){var A=new La.Worker(e);return t?A.from(t).save():A}La.Worker=Qa;function Ha(t,e){var A,r,n,i,o,s,a,c=t.dom.encode,u=-1===(i=(A=t).getParam("body_id","tinymce","string")).indexOf("=")?i:(n=(r=A).getParam("body_id","","hash"))[r.id]||n,l=-1===(a=(o=t).getParam("body_class","","string")).indexOf("=")?a:(s=o).getParam("body_class","","hash")[s.id]||"",h=t.getBody().dir,f=h?' dir="'+c(h)+'"':"",d=t.inline?"div":"body";return"<"+d+' id="'+c(u)+'" class="mce-content-body '+c(l)+'"'+f+">"+e+"</"+d+">"}function Sa(t,e,A){return t.inline?Ha(t,A):(n=e,i=A,o="",o+='<base href="'+(0,(r=t).dom.encode)(r.documentBaseURI.getURI())+'">',"<!DOCTYPE html><html><head>"+(o+=n)+"</head>"+Ha(r,i)+"</html>");var r,n,i,o}function xa(t,e){return A=function(t){return function(t,e){var A=t.dom;if(1!==A.nodeType)return!1;var r=A;if(void 0!==r.matches)return r.matches(e);if(void 0!==r.msMatchesSelector)return r.msMatchesSelector(e);if(void 0!==r.webkitMatchesSelector)return r.webkitMatchesSelector(e);if(void 0!==r.mozMatchesSelector)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}(t,e)},p(t.dom.childNodes,function(t){return A(O.fromDom(t))}).map(O.fromDom);var A}function _a(t,e){var A=t.getParam("pagebreak_separator","\x3c!-- pagebreak --\x3e","string"),r=new RegExp(A.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&"),"gi"),n=Wa.browser.isIE()||Wa.browser.isEdge()?'class="html2pdf__page-break"':'style="page-break-before: always;"';return e.replace(r,"<span "+n+"></span>")}function Ia(t,e){return A=[a(_a,t)],r=function(t,e){return e(t)},n=e,d(A,function(t){n=r(n,t)}),n;var A,r,n}function Ta(){var e=g(function(){return xa(H(),"div.mce-sandbox")});return function(t){var A=O.fromDom(t);return!!wt(A)&&!e().exists(function(t){return e=t,A.dom===e.dom||F(A,t)||F(t,A);var e})}}function Oa(t,e,A){!function(t,e,A){if(!(D(A)||k(A)||q(A)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",A,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(e,A+"")}(t.dom,e,A)}function Ra(t,e){return n=e,a=(i=r="class",void 0===(s=null===(o=(A=t).dom.getAttribute(i))?void 0:o)||""===s?[]:s.split(" ")).concat([n]),Oa(A,r,a.join(" ")),1;var A,r,n,i,o,s,a}function Pa(t,e){void 0!==t.dom.classList?t.dom.classList.add(e):Ra(t,e)}function Ma(t,e,A){if(!D(A))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",A,":: Element ",t),new Error("CSS value must be a string: "+A);var r;void 0!==(r=t).style&&j(r.style.getPropertyValue)&&t.style.setProperty(e,A)}function Ka(t,e){var A=t.dom;!function(t,e){for(var A=Ya(t),r=0,n=A.length;r<n;r++){var i=A[r];e(t[i],i)}}(e,function(t,e){Ma(A,e,t)})}function Da(t){function e(){return t.stopPropagation()}function A(){return t.preventDefault()}var r,n,i,o,s,a=O.fromDom(L(t).getOr(t.target)),c=(r=A,n=e,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return r(n.apply(null,t))});return i=a,o=t.clientX,s=t.clientY,{target:i,x:o,y:s,stop:e,prevent:A,kill:c,raw:t}}function ka(t,e,A,r,n){var i,o,s=(i=A,o=r,function(t){i(t)&&o(Da(t))});return t.dom.addEventListener(e,s,n),{unbind:a(Za,t,e,s,n)}}function za(t,e,A){return ka(t,e,$a,A,!1)}function ja(t,e){t.dom.appendChild(e.dom)}function qa(t,e){var A,r,n,i,o=b(t).dom,s=O.fromDom(o.createDocumentFragment()),a=(A=e,(r=(o||document).createElement("div")).innerHTML=A,U(O.fromDom(r)));n=s,d(a,function(t){ja(n,t)}),(i=t).dom.textContent="",d(U(i),function(t){tc(t)}),ja(t,s)}function Va(u){var l="mce-sandbox";return{play:function(a,c){return new Promise(function(A,r){var t,n=O.fromTag("div"),e=O.fromTag("iframe");Pa(n,l),Ka(n,{visibility:"hidden"}),ja(n,e),ja(u,n);function i(A){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];tc(n),A.apply(void 0,t)}}var o=null===(t=e.dom.contentWindow)||void 0===t?void 0:t.document;if(z(o))i(r)(new Error("sandbox iframe did not init correctly"));else{var s=za(e,"load",function(){s.unbind();var t=O.fromDom(o),e=mt(t);c(t,e).then(i(A),i(r))});o.open(),o.write(a),o.close()}})},playInline:function(t,o){return new Promise(function(e,A){var r=O.fromTag("div");Pa(r,l),Ka(r,{position:"fixed",overflow:"hidden",zIndex:"1000",left:"0",right:"0",bottom:"0",top:"0",opacity:"0"});var n=O.fromTag("div");Ka(n,{position:"absolute",left:"0",right:"0",top:"0",height:"auto",margin:"auto"}),qa(n,t),ja(r,n),ja(u,r);function i(A){return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];tc(r),A.apply(void 0,t)}}setTimeout(function(){var t=b(u);o(t,n).then(i(e),i(A))},50)})}}}function Xa(r,t){return e=r,A=t,n=function(t,e){return La().set(s(s({},ec),{html2canvas:s(s({},ec.html2canvas),{useCORS:(A=r,!!I.from(A.getParam("export_image_proxy",void 0,"string")).getOrThunk(function(){return A.getParam("imagetools_proxy",void 0,"string")})),ignoreElements:r.inline?Ta():o,windowWidth:window.innerWidth,windowHeight:window.innerHeight})})).from(e.dom).outputPdf("blob");var A},i=Va(H()),e.inline?i.playInline(A,n):i.play(A,n);var e,A,n,i}function Ga(t,e){var A,r;return t.fire("ExportPdf"),A=t,(r=e).utils.proxyImages(r.getContent()).then(a(Ia,A)).then(function(t){return Sa(A,r.utils.getStyles(),t)}).then(a(Xa,t))}function Ja(e,A){return{useDialog:o,convert:function(){return Ga(e,A)},download:function(){return Ga(e,t=A).then(a(t.downloadAs,"export.pdf"));var t}}}var Wa=tinymce.Env,Ya=Object.keys,Za=function(t,e,A,r){t.dom.removeEventListener(e,A,r)},$a=i,tc=function(t){var e=t.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},ec={margin:[10,20],html2canvas:{scale:1,useCORS:!0,logging:!1,scrollX:0,scrollY:0},jsPDF:{orientation:"p",format:"a4"}};tinymce.Resource.add("export.exporter.clientpdf",Ja)}();