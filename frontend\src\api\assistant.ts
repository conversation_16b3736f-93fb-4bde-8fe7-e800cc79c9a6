import { fetchEventSource } from '@microsoft/fetch-event-source';
import request from './request';
import { objToQueryString } from '@/utils/utils';

const abortController = new AbortController();
const signal = abortController.signal;

const prefix =
  process.env.NODE_ENV !== 'production' ? '/qa' : '';

export const createSession = (data: any) => {
  return request(`${prefix}/session/create_session`, {
    method: 'post',
    data,
  });
};

export const reqChatList = (params: any) => {
  return request(`${prefix}/chat/conversation`, {
    method: 'get',
    params,
  });
};

export const updateChat = (data: any) => {
  return request(`${prefix}/session/update_session`, {
    method: 'put',
    data,
  });
};

export const deleteChat = (params: any) => {
  return request(
    `${prefix}/session${objToQueryString(params)}`,
    {
      method: 'delete',
    },
  );
};

export const sendMessage = (
  data: any,
  cb: (data: string) => void,
) => {
  return fetchEventSource(`${prefix}/chat/v3/completions`, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'text/event-stream',
    },
    onmessage(event) {
      const { data } = event;
      try {
        const a = JSON.parse(data);
        if (a.message_end) {
          abortController.abort();
        }
        cb(data);
      } catch (e) {
        cb(data);
      }
    },
  });
};

export const getSearch = (text: string) =>
  request('/rman/v1/search/intelligent/course', {
    method: 'POST',
    body: JSON.stringify(text),
  });


export const reqAllAgents = (params: any) =>
  request(`/terminator/api/v1/application/`, { params });

// 查询aitools agent列表
export const reqAitoolsAgents = (params: any) =>
  request('/terminator/api/v1/application/aitools', { params });

export const fetchHeaderList = () =>
  request(`/unifiedplatform/v1/navigationsettings/user/navigation?type=2`);

export const reqAgentSingle = (id: string | undefined) =>
  request(`${prefix}/pubagent/assistant/${id}`);

export const reqChatHistory = (params: any) =>
  request(`${prefix}/terminator/api/v1/application/chat/getRecord`, {
    params,
  });

export const chatOnline = (
  data: any,
  cb: (data?: string, id?: string, isFinish?: boolean, role?: string) => void,
) =>
  fetchEventSource(`${prefix}/pubagent/assistant/chat`, {
    method: 'POST',
    signal,
    body: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json',
    },
    onmessage(e) {
      try {
        const { data } = e;
        const { content, event, message_id, role } = JSON.parse(data);
        if (event === 'finish') {
          abortController.abort();
          cb(undefined, message_id, true, role);
        } else {
          cb(content, message_id, false, role);
        }
      } catch (e) { }
    },
  });

export const updateQa = (id: string, name: string) =>
  request(`${prefix}/pubagent/assistant/chat_frame/${id}`, {
    method: 'PUT',
    data: { name },
  });

/**
* 流处理
* @param url  url地址
* @param data 请求body
* @returns
*/
export const postStream: (url: string, data?: unknown) => Promise<any> | any = (
  url,
  data
) => {

  const headers: HeadersInit = { 'Content-Type': 'application/json' }
  return fetch(url, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
    headers: headers
  })
}

// 查询agent详情
export const reqAgentDetail = (id: string) => {
  const params = {
    page: 1,
    page_size: 1,
    id
  }
  return request(`/terminator/api/v1/application/`, {
    params
  });
}

// 新增一条对话
export const addQa = (data: any) => {
  return postStream(`/terminator/api/v1/application/chat/sse/create`, data)
}

// 发送消息（正式对话）
export const reqMessage = (data: any) => {
  return postStream(`/terminator/api/v1/application/chat/sse/chitchat`, data)
}

export const reqQaList = (params: any) =>
  request(`${prefix}/terminator/api/v1/application/chat`, {
    params,
  });

export const deleteQa = (data: any) =>
  request(`${prefix}/terminator/api/v1/application/chat`, {
    method: 'POST',
    data,
  });
export const navigationQa = () =>
  request(`/unifiedplatform/v1/navigationsettings/user/navigation?type=3`);