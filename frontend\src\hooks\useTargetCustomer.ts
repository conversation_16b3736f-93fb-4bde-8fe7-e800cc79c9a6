/*
 * @Author: 冉志诚
 * @Date: 2025-01-09 10:44:36
 * @LastEditTime: 2025-01-09 10:44:37
 * @FilePath: \frontend\src\hooks\useTargetCustomer.ts
 * @Description:
 */
import { useSelector } from 'umi';

/*
 * @Author: 冉志诚
 * @Date: 2025-01-02 14:03:46
 * @LastEditTime: 2025-01-02 14:37:40
 * @FilePath: \coursemanger\src\hooks\useTargetCustomer.ts
 * @Description:
 */
export function useTargetCustomer() {
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  return TargetCustomer.SJ_TU;
  return parameterConfig.target_customer as TargetCustomer;
}

export enum TargetCustomer {
  NPU = 'npu',
  TCM = 'tcm',
  PP_SUC = 'ppsuc',
  YNU = 'ynu',
  SH_TECH = 'shangHaiTech',
  SJ_TU = 'sjtu',
}
