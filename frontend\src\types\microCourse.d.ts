declare namespace MicroCourse {
  interface searchParams {
    keyword?: string;
    publishStatus?: number | string;
    classificationId?: string[] | undefined;
    teacher?: string[] | undefined;
    subjectId?: string[] | undefined;
    startUpdateTime?: string;
    endUpdateTime?: string;
    page?: number;
    size?: number;
    courseType?: number;
    approvalStatus?: number;
  }

  interface IFileItem {
    filePath: string;
    duration: number;
    displayPath: string;
    fileSize: number;
    fileGuid: string;
    frameRate: number;
  }

  interface IFileGroup {
    typeCode: string;
    typeName: string;
    fileItems: IFileItem[];
  }

  // 文件格式
  interface IFileEntity {
    type: string;
    entityName: string;
    fileGroups: IFileGroup[];
  }

  interface IAttachmentFile {
    extraData: string;
    fileCreateTime?: string;
    fileGUID: string;
    fileLength: number;
    filePath: string;
    fileSize: number;
    fileState: string;
  }

  interface IAttachmentRenameForm {
    courseId: string;
    fileguid: string;
    fileName: string;
    notSynced?: boolean;
  }
}
