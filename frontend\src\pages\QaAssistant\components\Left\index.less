.left-container {
  background: url(~@/images/left.png);
  height: 100%;
  width: 320px;
  border-radius: 0 10px 10px 0;
  padding: 40px 0 40px 20px;
  overflow: hidden;

  .menu-list-wrp {
    padding-right: 20px;
    height: calc(100% - 50px);
    overflow: auto;

    &::-webkit-scrollbar-track {
      background-color: transparent;
      /*滚动条的背景颜色*/
    }
  }

  .icon-wrp {
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 40px;
    margin-right: 20px;

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 10px;
    }
  }

  .qa-add-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.14);
    border-radius: 8px;
    color: #fff;
    font-size: 14px;
    margin-bottom: 10px;
    margin-right: 20px;

    &:hover {
      background-color: rgba(255, 255, 255, 0.3);
    }

    .anticon {
      font-size: 18px;
      margin-right: 10px;
    }
  }

  .menu-item-wrp {
    font-size: 14px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 48px;
    margin-top: 10px;
    border-radius: 8px;
    padding: 0 10px;
    cursor: pointer;

    .menu-name {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .anticon {
      display: none;

      &:hover {
        color: #ff4d4f;
      }
    }

    &:hover,
    &.active {
      background: var(--primary-color);
    }

    &:hover {
      .anticon {
        display: block;
      }
    }
  }
}
