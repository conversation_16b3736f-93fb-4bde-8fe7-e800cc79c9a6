.retrieval {
  .content {
    width: 100%;
    height: calc(100vh - 52px);
    display: flex;

    >main {
      width: 100%;
      background: #F7F9FA;

      .empty {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .renderContent {
        height: 100%;
        display: flex;
        padding: 20px;

        >* {
          flex: 1;
          height: 100%;

          .title {
            width: 100%;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 800;
            height: 36px;
          }

          .showArea {
            height: calc(100% - 52px);
            padding: 20px;
          }
        }

        .before {
          background-color: #fff;

          .title {
            background-color: #f7f9fa;
          }

          .textarea {
            position: relative;

            .btn {
              position: absolute;
              right: 20px;
              bottom: 20px;
            }
          }
        }

        .after {
          .title {
            background-color: #edf5ff;
            color: rgb(84, 156, 255);
          }
        }

        .entity_view {
          width: 1304px;
          margin: 0px auto;

          .entity_view_wrapper {
            overflow: hidden;
            position: relative;
            height: 750px;

            .mejs__cleantrim-button，.mejs__tostart-button,
            .mejs__toend-button {
              display: none;
            }
          }

          .slide-box {
            .ant-slider {
              height: 46px;
              margin-bottom: 28px;

              .ant-slider-track,
              .ant-slider-rail,
              .ant-slider-step,
              .ant-slider-handle {
                height: 100%;
              }

              .ant-slider:hover .ant-slider-rail {
                background: #E7E7E7;
              }

              .ant-slider-rail {
                background: #E7E7E7;
              }

              .ant-slider-track {
                background: #549CFF;
              }

              .ant-slider:hover .ant-slider-track {
                background: #549CFF;
                opacity: 0.8;
              }

              .ant-slider-handle {
                border-radius: 0;
                margin-top: -10px;
                border: none;
                width: 12px;
                height: 56px;
                background: url('~@/images/juxing.png');
                &::after {
                  content: '';
                  width: 28px;
                  height: 28px;
                  display: block;
                }
              }

              .ant-slider-handle:focus {
                box-shadow: none;
              }
              .ant-slider-handle-1 {
                
                &::after {
                  top: 18px;
                  left: -60px;
                  background: url('~@/images/rightArrow.png');
                }
              }

              .ant-slider-handle-2 {
                &::after {
                  top: 18px;
                  left: 40px;
                  background: url('~@/images/leftArrow.png');
                }
              }
            }
          }

          .bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .bottom-text {
              font-size: 14px;
              font-weight: 400;
              color: #A4A4A4;
            }

            .btn {
              display: flex;
              gap: 15px;
              button{
                border-radius: 18px;
              }
            }
          }
        }


      }
    }
  }

  .tip {
    font-size: 12px;
    color: gray;
    text-align: center;
  }

  .uploadText {
    .tip();
    font-size: 16px;
    padding-bottom: 20px;
    color: #2e2e2e;
    font-weight: 800;
    line-height: 22px;

    .strong {
      font-weight: 400;
      color: #868686;
      display: block;
      font-size: 12px;
      line-height: 22px;
    }
  }

  .topTip {
    margin-top: 100px;

    p {
      text-align: center;
      font-size: 14px;
      font-weight: 400;
      color: #525252;
    }

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #2E2E2E;
    }
  }

  .dragger {
    margin-top: 30px;
    height: 380px !important;
    position: relative;

    .bottom {
      display: flex;
      justify-content: center;

      .resourceBtn {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 32px;
        background: #ffffff;
        border-radius: 16px;
        border: 1px solid #a4a4a4;
        color: #868686;
        width: 124px;
      }
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    color: #a4a4a4;
    flex-direction: column;
    height: 100%;

    img {
      width: 40px;
      height: 40px;
    }

    p {
      font-size: 14px;
      line-height: 20px;
      margin-top: 10px;
    }
  }

  .result {
    display: flex;
    justify-content: space-between;
    height: 100%;

    >div {
      flex: 1;
      background: #FFFFFF;
      padding: 30px;
    }

    .result-left {
      margin-right: 30px;

      .btn-upload {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 92px;
      }

      .img-preview {
        padding: 115px 0;
        background: #F7F9FA;
        border-radius: 10px;

        img {
          width: 100%;
        }
      }
    }

    .result-right {
      padding-top: 122px;

      .content-text {
        padding: 23px;
        background: #F7F9FA;
        margin-bottom: 10px;
        font-size: 14px;

        .text-title {
          font-weight: 400;
          color: #A4A4A4;
          margin-bottom: 20px;
        }

        .text {
          color: #2E2E2E;
        }
      }

      .btn-list {
        display: flex;
        gap: 20px;

        >div {
          font-size: 12px;
          font-weight: 400;
          cursor: pointer;
          color: #A4A4A4;

          img {
            margin-right: 5px;
          }
        }
      }
    }
  }
}

.uploadbox {
  .right {
    display: flex;
    align-items: center;

    .ant-select {
      width: 300px;
      height: 32px;
      margin-left: 10px;
    }
  }
}