import React, { FC, useEffect, useState } from 'react';
import "./index.less";
import IconFont from '@/components/iconFont/iconFont';
import { Tooltip, message } from 'antd';
import { PlusCircleFilled } from '@ant-design/icons';
import { createSession, deleteChat, reqChatList } from "@/api/assistant";
import { getUuid } from "@/utils/utils";
import { DEFAULT_CHAT_TITLE, QA_SELECTED_KEY, SCHOOL_SELECTED_KEY } from '@/constant/assistant';
import { sortByDate } from '../../utils';
import { useLocation } from 'umi';

interface LeftProps {
  isChat: boolean;
  chatUser?: string;
  isOut?: boolean;
  titleUid?: number;
  onSelect: (id: string) => void;
}

interface MenuProps extends LeftProps {
  data: any;
  selectId: string;
  onDelete: (id: string) => void;
}

const MenuItem: FC<MenuProps> = ({ data, selectId, isChat, onSelect, onDelete }) => {
  return (
    <div
      className={`menu-item-wrp ${selectId === data.session_id ? 'active' : ''} `}
      onClick={() => {
        if (isChat) {
          message.warning("当前无法切换聊天！");
          return;
        }
        onSelect(data.session_id);
      }}
    >
      <Tooltip title={data.title}>
        <div className='menu-name'>{data.title}</div>
      </Tooltip>
      <IconFont type="icondelete" onClick={(e: any) => {
        e.stopPropagation();
        onDelete(data.session_id);
      }} />
    </div>
  );
};

const Left: FC<LeftProps> = ({ isOut, titleUid, chatUser, isChat, onSelect }) => {
  const [list, setList] = useState<any[]>([]);
  const [selectId, setSelectId] = useState<string>("");
  const [image, setImage] = useState<string>("");
  const location: any = useLocation()

  useEffect(() => {
    if (titleUid) {
      getList();
    }
  }, [titleUid]);

  useEffect(() => {
    if (chatUser) {
      getList().then((res: any) => {
        if (res.length > 0) {
          const key = chatUser.includes("school") ? SCHOOL_SELECTED_KEY : QA_SELECTED_KEY;
          const selected_id = sessionStorage.getItem(key);
          if (selected_id && res.map((item: any) => item.session_id).includes(selected_id)) {
            handleSelect(selected_id);
          } else {
            handleSelect(res[0].session_id);
          }
        } else {
          handleAdd();
        }
      });
    }
  }, [chatUser]);

  useEffect(() => {
    if (isOut) {
      import("@/assets/img/qa-assistant.png").then((res: any) => {
        setImage(res.default);
      });
    }
  }, [isOut]);

  const handleAdd = () => {
    if (isChat) {
      message.warning("当前无法新增问答");
      return;
    }
    const uuid = getUuid();
    const data = {
      user: chatUser,
      session_id: uuid,
      title: DEFAULT_CHAT_TITLE,
      domain_id: chatUser?.includes("school") ? location.query.domain ?? 211 : null
    };
    createSession(data).then(() => {
      getList();
      handleSelect(uuid);
    });
  };
  const getList = () => {
    return reqChatList({ user: chatUser }).then((res: any) => {
      const _list = sortByDate(res);
      setList(_list ?? []);
      return _list ?? [];
    });
  };
  const handleSelect = (id: string) => {
    const key = chatUser?.includes("school") ? SCHOOL_SELECTED_KEY : QA_SELECTED_KEY;
    sessionStorage.setItem(key, id);
    setSelectId(id);
    onSelect(id);
  };
  const handleDelete = (id: string) => {
    const curIndex = list.findIndex(item => item.session_id === id);
    const param = {
      user: chatUser,
      session_id: id,
    };
    deleteChat(param).then(() => {
      message.success("删除成功");
      if (list.length === 1) {
        handleAdd();
      } else {
        const newList = list.filter(item => item.session_id !== id);
        setList(newList);
        if (selectId === id) {
          if (curIndex < list.length - 1) {
            handleSelect(newList[curIndex].session_id);
          } else {
            handleSelect(newList[curIndex - 1].session_id);
          }

        }
      }
    });
  };
  return (
    <div className='left-container'>
      {isOut && <div className='icon-wrp'>
        <img src={image} />
        <span>{chatUser?.includes("school") ? "校园服务助手" : "智能助手"}</span>
      </div>}
      <div className='qa-add-btn' onClick={handleAdd}>
        <PlusCircleFilled />
        <span>开启新问答</span>
      </div>
      <div className="menu-list-wrp">
        {list.map((item) =>
          <MenuItem key={item.session_id} data={item} isChat={isChat} selectId={selectId} onDelete={handleDelete} onSelect={handleSelect} />
        )}
      </div>
    </div>
  );
};

export default Left;