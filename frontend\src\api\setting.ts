/*
 * @Author: lijin
 * @Description: 配置权限相关接口
 * @Date: 2021-08-13 10:39:03
 * @LastEditTime: 2022-07-13 16:11:33
 * @FilePath: \frontend\src\api\setting.ts
 */
import request from './request';

async function addSetting(params: any) {
  return request(`/unifiedplatform/v1/setting`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

async function getSetting() {
  return request(`/unifiedplatform/v1/setting`);
}

async function getSettingUnauthorized() {
  return request(`/unifiedplatform/v1/setting/no/authorization`);
}

async function fetchParameterConfig() {
  return request(
    `/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=MyTeaching`,
  );
}
/**
 * 获取全局参数配置【系统】
 */
const queryParameterConfigAll = (modules: string[]) =>
  request('/unifiedplatform/v1/app/app/module/parameter/list', {
    method: 'POST',
    body: JSON.stringify(modules),
  });

/**
 * 获取 模块 和 权限 【角色】
 *
 */
const fetchUserPermissionsV2 = () =>
  request(`/unifiedplatform/v1/user/rolemodule`);

/**
 * 获取 rman全局参数
 *
 */
const fetchRmanGlobalParam = () =>
  request(`/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=zyzx`);

/**
 * 获取模块【系统】
 *
 * @param {string} moduleCode
 * @return {*}
 */
function fetchModuleSystem(moduleCode: string) {
  return request(
    `/unifiedplatform/v1/app/app/module/list?moduleCode=${moduleCode}&enable=true`,
  );
}
/**
 * 获取存储配置【系统】
 *
 * @param {string} moduleCode
 * @return {*}
 */
function fetchStorageConfig(params?:any) {
  return request(
    `/unifiedplatform/v1/storage/user/space/get`,{
      method: 'get',
      params
    }
  );
}
/**
 * 获取平台名字【系统】
 *
 */
const fetchHeaderList = () =>
  request(`/unifiedplatform/v1/navigationsettings/user/navigation?type=2`);

const fetchPlatform = async () => {
  return request('/unifiedplatform/v1/app/projectname', {
    method: 'get',
  });
};
/**
 * 获取安全配置文件
 *
 */
async function fetchSafeConfig() {
  // 安全配置放在这初始化 避免多次调用
  const res = await fetch('safeMethodConfig.json');
  const result = await res.json();
  return result;
}

async function fetchProcess(params: any) {
  return request('/unifiedplatform/external-system/process', {
    method: 'get',
    params,
  });
}

async function fetchProtocol(params: any) {
  return request('/unifiedplatform/external-system/protocol', {
    method: 'get',
    params,
  });
}

async function addExternalSystem(data: any) {
  return request('/unifiedplatform/external-system', {
    method: 'post',
    data: JSON.stringify(data),
  });
}
async function fetchExternalSystem(params?: any) {
  return request('/unifiedplatform/external-system/all', {
    method: 'get',
    params,
  });
}

async function updateExternalSystem(data: any) {
  return request('/unifiedplatform/external-system', {
    method: 'PUT',
    data: JSON.stringify(data),
  });
}

async function fetchTranscodeTemplate() {
  return request('/unifiedplatform/external-system/transcode-template', {
    method: 'get',
  });
}

async function addTranscodeTemplate(data: any) {
  return request('/unifiedplatform/external-system/transcode-template', {
    method: 'post',
    data: JSON.stringify(data),
  });
}

async function updateTranscodeTemplate(data: any) {
  return request('/unifiedplatform/external-system/transcode-template', {
    method: 'PUT',
    data: JSON.stringify(data),
  });
}

async function delExternalSystem(params: any) {
  return request(`/unifiedplatform/external-system`, {
    method: 'DELETE',
    params,
  });
}

function delTranscodeTemplate(params: any) {
  return request(`/unifiedplatform/external-system/transcode-template`, {
    method: 'DELETE',
    params,
  });
}
/**
 * 
 * 
 * 系统公告*/ 
//新建系统公告
async function addNotice(data: any) {
  return request('/unifiedplatform/v1/msg/add', {
    method: 'POST',
    data
  });
}
//公告附件上传
async function enclosureUpload(data: any) {
  return request('/unifiedplatform/v1/msg/upload/attachment', {
    method: 'POST',
    data
  });
}
//公告附件上传
async function imageUpload(data: any) {
  return request('/rman/v1/upload/save/file', {
    method: 'POST',
    headers: { 'Content-Type': 'multipart/form-data' },
    body:data
  });
}
//公告删除
async function noticeDelete(data: any) {
  return request('/unifiedplatform/v1/msg/delete', {
    method: 'DELETE',
    data
  });
}
//公告撤回
async function noticeRevoke(data: any) {
  return request('/unifiedplatform/v1/msg/revoke', {
    method: 'PATCH',
    data
  });
}
//公告发布
async function noticeRelease(data: any) {
  return request('/unifiedplatform/v1/msg/release', {
    method: 'PATCH',
    data
  });
}
//公告列表查询
async function noticeList(params: any) {
  return request('/unifiedplatform/v1/msg/list', {
    method: 'GET',
    params
  });
}
//密码复杂程度查询
async function passwordRule() {
  return request('/unifiedplatform/v1/setting/password/rule', {
    method: 'GET',
  });
}


let setting = {
  addSetting,
  getSetting,
  getSettingUnauthorized,
  fetchParameterConfig,
  queryParameterConfigAll,
  fetchUserPermissionsV2,
  fetchRmanGlobalParam,
  fetchModuleSystem,
  fetchStorageConfig,
  fetchPlatform,
  fetchSafeConfig,
  fetchHeaderList,
  fetchProcess,
  fetchProtocol,
  fetchExternalSystem,
  fetchTranscodeTemplate,
  addExternalSystem,
  updateExternalSystem,
  addTranscodeTemplate,
  delExternalSystem,
  delTranscodeTemplate,
  updateTranscodeTemplate,
  addNotice,
  enclosureUpload,
  noticeDelete,
  noticeRevoke,
  imageUpload,
  noticeRelease,
  noticeList,
  passwordRule,
};

export default setting;
