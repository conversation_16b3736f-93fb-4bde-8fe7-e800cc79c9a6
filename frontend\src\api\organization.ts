/*
 * @Author: 李晋
 * @Date: 2021-08-13 10:39:03
 * @Email: <EMAIL>
 * @LastEditors: 李武林
 * @LastEditTime: 2022-08-01 19:03:12
 * @Description: file information
 * @Company: Sobey
 */
import request from "./request";

async function rootOrganization() {
    return request(`/unifiedplatform/v1/organization/root`, {
        method: 'get'
    })
}

async function childOrganization(organizationCode: string) {
    return request(`/unifiedplatform/v1/organization/child/${organizationCode}`, {
        method: 'get',
    })
    // return request(`/unifiedplatform/v1/organization/child/scu`, {
    //     method: 'get',
    // })
}
// async function codeOrganization(organizationCode: string) {
//     return request(`/unifiedplatform/v1/organization/code?code=${organizationCode}`, {
//         method: 'get',
//     })
// }
async function codeOrganization(organizationCode: string, isCurrentOrg = false) {
    return request(`/unifiedplatform/v1/organization/user?code=${organizationCode}&isCurrentOrg=${isCurrentOrg}`, {
        method: 'get',
    })
}
async function addOrganization(data: any) {
    return request(`/unifiedplatform/v1/organization`, {
        method: 'post',
        data
    })
}

async function editOrganization(data: any) {
    return request(`/unifiedplatform/v1/organization`, {
        method: 'patch',
        data
    })
}

// 删除组织，有子机构删不掉
async function deleteOrganization(code: string) {
    return request(`/unifiedplatform/v1/organization/${code}`, {
        method: 'delete',
    })
}

// 删除组织，并删掉子机构
async function deleteOrganizationTotal(code: string) {
    return request(`/unifiedplatform/v1/organization/delete`, {
        params: {
            orgCode: code,
            isDeleteChild: true
        },
        method: 'delete',
    })
}

async function bindUser(organizationCode: string, userCode: string, actionName: string) {
    return request(`/unifiedplatform/v1/organization/user/${organizationCode}/${actionName}`, {
        method: 'post',
        data: [userCode]
    })
}

// admin查询所有组织
async function getAllOrganization() {
    return request(`/unifiedplatform/v1/organization/all/organization`, {
        method: 'GET'
    })
}

// 组织排序
async function orderOrganization(data:any) {
    return request(`/unifiedplatform/v1/organization/order`, {
        method: 'post',
        data
    })
    
}

let organization = {
    rootOrganization,
    childOrganization,
    addOrganization,
    editOrganization,
    deleteOrganization,
    deleteOrganizationTotal,
    bindUser,
    codeOrganization,
    getAllOrganization,
    orderOrganization
}

export default organization;