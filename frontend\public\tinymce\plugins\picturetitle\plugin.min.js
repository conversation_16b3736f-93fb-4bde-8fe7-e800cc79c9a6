/*
 * @Description: 图题
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2021-06-22 16:24:11
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2021-08-03 11:42:39
 */
tinymce.PluginManager.add('picturetitle', function (editor, url) {
  var pluginName = '图题';
  window.bubble = {}; //扔外部公共变量，也可以扔一个自定义的位置
  // const getNode = editor.selection.getNode();
  // var baseURL=tinymce.baseURL;
  // var iframe1 = baseURL+'/plugins/bubble/upfiles.html';
  // bubble.images_upload_handler = editor.getParam('images_upload_handler', undefined, 'function');
  // bubble.images_upload_base_path = editor.getParam('images_upload_base_path', '', 'string');
  // bubble.axupimgs_filetype = editor.getParam('axupimgs_filetype', '.png,.gif,.jpg,.jpeg', 'string');
  bubble.res = [];
  var openDialog = function () {
    var nodeToString = (node) => {
      var tmpNode = document.createElement("div");
      tmpNode.appendChild(node.cloneNode(true));
      var str = tmpNode.innerHTML;
      tmpNode = node = null;
      return str;
    }

    var dataset = editor.selection.getNode().dataset
    var initialData = {
      title: dataset?.title,
      detail: dataset?.detail
    }
    return editor.windowManager.open({
      title: pluginName,
      initialData: initialData,
      body: {
        type: 'panel',
        items: [
          {
            type: 'input',
            name: 'title',
            label: '标题',
          },
          {
            type: 'textarea',
            name: 'detail',
            height: 600,
            label: '描述',
          }
        ]
      },
      buttons: [
        {
          type: 'cancel',
          text: 'Close'
        },
        {
          type: 'submit',
          text: 'Save',
          primary: true
        }
      ],
      onSubmit: function (api) {
        var data = api.getData();
        let node = editor.selection.getNode();
        console.log(initialData)
        if (initialData.title || initialData.detail) {
          console.log(node, node?.children[0]);
          let editDom = nodeToString(node?.children[0])
          editor.selection.setContent('<figure class="image" id="sobey_picturetitle"  data-title="' + data.title + '" contenteditable="false" data-detail="' + data.detail + '" >' + editDom + '<figcaption contenteditable="true" style="margin-top:10px;">' + data.title + '</figcaption></figure>');
        } else {
          // 将输入框内容插入到内容区光标位置
          // editor.selection.setContent(`<figure class="image" id="sobey_picturetitle"  data-title=${data?.title} contenteditable="false" data-detail=${data?.detail} id="sobey_picturetitle">${editor.selection.getContent()}<figcaption contenteditable="true" style="margin-top:10px;">${data.title}</figcaption></figure>`);
          editor.selection.setContent('<figure class="image" id="sobey_picturetitle"  data-title="' + data.title + '" contenteditable="false" data-detail="' + data.detail + '" >' + editor.selection.getContent() + '<figcaption contenteditable="true" style="margin-top:10px;">' + data.title + '</figcaption></figure>');

        }
        api.close();

      },
      onChange: function (api, detail) {
      },
    });
  };

  editor.ui.registry.getAll().icons.picturetitle || editor.ui.registry.addIcon('picturetitle', '<svg t="1620791151601" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="14255" width="30" height="30"><path d="M213.333333 213.333333h597.333334v597.333334H213.333333V213.333333z m85.333334 85.333334v426.666666h426.666666V298.666667H298.666667z m170.666666 42.666666h213.333334v85.333334h-213.333334V341.333333z m-128 128h341.333334v85.333334H341.333333v-85.333334z m0 128h341.333334v85.333334H341.333333v-85.333334z m42.666667-170.666666c-25.6 0-42.666667-17.066667-42.666667-42.666667s17.066667-42.666667 42.666667-42.666667 42.666667 17.066667 42.666667 42.666667-17.066667 42.666667-42.666667 42.666667z" fill="#444444" p-id="14256"></path></svg>');

  editor.ui.registry.addButton('picturetitle', {
    icon: 'picturetitle',
    tooltip: pluginName,
    onAction: function () {
      openDialog();
    }
  });
  editor.ui.registry.addMenuItem('picturetitle', {
    icon: 'picturetitle',
    text: '图题',
    onAction: function () {
      openDialog();
    }
  });
  return {
    getMetadata: function () {
      return {
        name: pluginName,
        url: "http://tinymce.ax-z.cn/more-plugins/axupimgs.php",
      };
    }
  };
});
