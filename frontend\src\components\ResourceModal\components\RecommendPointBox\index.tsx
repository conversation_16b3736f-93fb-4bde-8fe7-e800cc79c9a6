import { IconFont } from '@/components/iconFont/iconFont_2';
import { Checkbox, Divider, Empty, List } from 'antd';
import React from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';

import './index.less';

export interface IRecommendPointProp {
  /** 知识点名字 */
  pointName: string;
  /** 知识点列表 */
  pointList: any[];
  /** 加载更多标识 */
  hasLoadMore: boolean;
  /** 选择的列表 */
  checkedList: any[];
  /** 选择变化 */
  checkChange: (value: any) => void;
  /** 加载更多 */
  loadMore: () => void;
}

const RecommendPointBox: React.FC<IRecommendPointProp> = props => {
  const {
    pointName,
    pointList,
    hasLoadMore,
    checkedList,
    checkChange,
    loadMore,
  } = props;
  return (
    <div className="recommend-point-container">
      <div style={{ color: 'var(--primary-color)', fontSize: '15px' }}>
        <IconFont type="iconzhinengtuijian" />
        推荐知识点
      </div>
      <div className="recommend-title">
        以下为
        <span className="recommend-course">
          {/* {searchpoint || currentname} */}
          {pointName}
        </span>
        课程相关备课资源推荐
      </div>
      <div id="scrollableDiv-recommend" className="recommend-scroll-list">
        {pointList?.length > 0 ? (
          <>
            <Checkbox.Group
              name="point_check-rec"
              // value={recommendPointCheckList}
              value={checkedList}
              // onChange={handleCheckRecommendPoint}
              onChange={checkChange}
              style={{ width: '100%' }}
            >
              <InfiniteScroll
                dataLength={pointList.length}
                //   next={() => {
                //     getRecommendPoint(
                //       paginationRecommend.current + 1,
                //       paginationRecommend.pageSize,
                //       searchpoint || currentname,
                //     );
                //   }}
                next={loadMore}
                hasMore={hasLoadMore}
                loader={
                  <h4 style={{ width: '100%', textAlign: 'center' }}>
                    加载中...
                  </h4>
                }
                endMessage={
                  <Divider plain style={{ width: '100%', textAlign: 'center' }}>
                    {pointList.length > 0 ? '已加载完毕所有的知识点！' : ''}
                  </Divider>
                }
                scrollableTarget="scrollableDiv-recommend"
              >
                <List
                  grid={{ gutter: 5, column: 1 }}
                  dataSource={pointList}
                  style={{ width: '99.5%' }}
                  renderItem={item => {
                    const keyWrods = item?.keywords || [];
                    return (
                      <List.Item>
                        <div
                          key={item.contentId}
                          className="recommend-item-container"
                        >
                          {item.keyframepath && item.keyframepath != '' ? (
                            <img
                              src={item.keyframepath}
                              className="course-img"
                              onError={(e: any) => {
                                let img = e.target
                                img.src = '/rman/static/images/video.png'
                                img.onError = null
                                }}
                            />
                          ) : (
                            <img
                              src={'/rman/static/images/video.png'}
                              className="course-img"
                            />
                          )}
                          <div className="course-info">
                            <span className="course-name">
                              <Checkbox value={item} />
                              <span
                                style={{
                                  width: '100%',
                                  overflow: 'hidden',
                                  whiteSpace: 'nowrap',
                                  textOverflow: 'ellipsis',
                                  cursor: 'default',
                                  paddingLeft: '5px'
                                }}
                                title={item.title}
                              >
                                {item.title}
                              </span>
                            </span>
                            <div className="course-key-word">
                              <span className="reco-keywords">
                                {keyWrods?.[0]}
                              </span>
                            </div>
                          </div>
                        </div>
                      </List.Item>
                    );
                  }}
                />
              </InfiniteScroll>
            </Checkbox.Group>
          </>
        ) : (
          <Empty description="暂无相关资源推荐" />
        )}
      </div>
    </div>
  );
};

export default RecommendPointBox;
