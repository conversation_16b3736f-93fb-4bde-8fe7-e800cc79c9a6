﻿
<!DOCTYPE html>
<html>
<head> 
<meta charset="utf-8"> 
<title>素材浏览</title> 
</head>
<body>
<object id="NSPlay" classid="CLSID:22d6f312-b0f6-11d0-94ab-0080c74c7e95" codebase="http://activex.microsoft.com/activex/controls/mplayer/en/nsmp2inf.cab#Version=6,4,5,715" standby="Loading Microsoft Windows Media Player components..." type="application/x-oleobject" align="middle" width="672" height="504">
<param name="FileName" value="">
<!-- ASX File Name -->
<param name="AutoRewind" value="0">
<!-- Display Controls -->

<param name="ShowControls" value="1">
<!-- Display Position Controls -->

<param name="ShowPositionControls" value="1">
<!-- Display Audio Controls -->

<param name="ShowAudioControls" value="1">
<!-- Display Tracker Controls -->

<param name="ShowTracker" value="1">
<!-- Show Display -->

<param name="ShowDisplay" value="0">
<!-- Display Status Bar -->

<param name="ShowStatusBar" value="1">
<!-- Diplay Go To Bar -->

<param name="ShowGotoBar" value="0">
<!-- Display Controls -->

<param name="ShowCaptioning" value="0">
<!-- Player Autostart -->

<param name="AutoStart" value= "1">
<!-- Animation at Start -->

<param name="Volume" value="-2500">
<param name="AnimationAtStart" value="0">
<!-- Transparent at Start -->

<param name="TransparentAtStart" value="0">
<!-- Do not allow a change in display size -->

<param name="AllowChangeDisplaySize" value="0">
<!-- Do not allow scanning -->

<param name="AllowScan" value="0">
<!-- Do not show contect menu on right mouse click -->

<param name="EnableContextMenu" value="0">
<!-- Do not allow playback toggling on mouse click -->
<param name="ClickToPlay" value="0">
</object>

<script>
function getQueryVariable(variable)
{
       var query = window.location.search.substring(1);
       var vars = query.split("&");
       for (var i=0;i<vars.length;i++) {
               var pair = vars[i].split("=");
               if(pair[0] == variable){return pair[1];}
       }
       return(false);
}
var prms=document.getElementById("NSPlay").FileName;

document.getElementById("NSPlay").FileName = getQueryVariable("filepath")

</script>
</body>
</html>