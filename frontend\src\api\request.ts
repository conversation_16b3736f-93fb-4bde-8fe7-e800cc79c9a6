/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import {
  extend,
  ExtendOptionsInit,
  RequestMethod,
  RequestResponse,
  RequestOptionsInit,
  RequestOptionsWithResponse,
} from 'umi-request';
import { message } from 'antd';
import config from '@/utils/config';
import { getDvaApp, history } from 'umi';
//---------------------- 重写类型定义 start （解决ts报错）------------
interface ICustomOption {
  ifHideError?: boolean; // 隐藏后端错误显示
}

interface customConstuctor {
  <T = any>(url: string, options: RequestOptionsInit & ICustomOption): Promise<
    T
  >;
}

type ICustomRequestMethod = RequestMethod & customConstuctor;

type CustomExtend = (
  options: ExtendOptionsInit & ICustomOption,
) => ICustomRequestMethod;
//---------------------- 重写类型定义 end ------------

// 请求的数量
let requsetCount: number = 0;

let baseUrl = '';
if (process.env.NODE_ENV === 'development') {
  baseUrl = '/api';
}

const codeMessageMap: any = {
  'cloud_sc.0000.0000': 'Success',
  'cloud_sc.0000.0500': '内部异常',
  'cloud_sc.0000.0501': '内部异常',
  'cloud_sc.0000.0104': '用户名或密码错误',
  'cloud_sc.0000.0122': '用户名或密码错误',
  'cloud_sc.0000.0101': '未登录',
  'cloud_sc.0000.0102': '用户名或密码错误',
  'cloud_sc.0000.0103': '注册失败',
  'cloud_sc.0000.0105': '注册失败',
  'cloud_sc.0000.0301': '参数错误',
  'cloud_sc.0000.0410': '参数错误',
};

/**
 * 路由跳转至登录页
 */
const handleToLogin = (errorCode: string): boolean => {
  const platform = window.localStorage.getItem('upform_platform');
  let loginList = ['notlogged', 'course_0000_0002', '未登录'];
  if (loginList.indexOf(errorCode) > -1) {
    window.parent.postMessage(
      JSON.stringify({ action: 'login' }),
      window.location.origin,
    );
    if (platform === 'ILA') {
      window.location.href = '/capubmam/#/login';
    } else {
      window.location.replace(
        `/unifiedlogin/v1/loginmanage/login/direction?redirect_url=${encodeURIComponent(
          window.location.href,
        )}`,
      );
    }

    // if (history.location.query?.action === 'login') {
    //   return false;
    // } else {
    //   // history.push({
    //   //   pathname: '/basic',
    //   //   query: {
    //   //     action: 'login',
    //   //   },
    //   // });

    // }
  }
  return true;
};

/**
 * 异常处理程序
 */
const errorHandler = (
  error: any,
  options: RequestOptionsInit & ICustomOption,
) => {
  try {
    const errCode: string = error.errorCode || error.message;
    const showMsg = handleToLogin(errCode);
    // 带有 ifHideError: true 的请求不显示服务器返回的错误信息
    if (options.ifHideError) {
      return;
    }

    if (showMsg) {
      //没有访问权限
      if (error.errorCode === '401' || error.statusCode === '401') {
        window.location.replace('/unifiedplatform/#/not_authority');
      } else {
        message.error(error.errorMsg || error.message || '未知错误');
      }
    }
  } catch (err) {
    message.error('未知错误');
  }
};

let cutomExtend: CustomExtend = extend;
// let cutomExtend: any = extend;
/**
 * 配置request请求时的默认参数
 */
let request: any = [];
request = cutomExtend({
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 60000,
  ifHideError: false,
});

//读取配置文件json
let configSafe: any = null;
// async function getconfig() {
//   if ((window as any).configSafe) {
//     return (window as any).configSafe;
//   } else {
//     const res = await fetch('safeMethodConfig.json');
//     const result = await res.json();
//     (window as any).configSafe = result;
//     return result;
//   }
// }

// async function handleUrl(url: string, options: any) {
//   configSafe = await getconfig();
//   // console.log(configSafe);
//   let method = options.method;
//   if (method === 'get' || method === 'post') return url;
//   options.method = 'post';
//   url = url.endsWith('/') ? `${url}${method}` : `${url}/${method}`;
//   return url;
// }

// // request拦截器, 改变url 或 options.
console.log((window as any).sessionStorage);

(request.interceptors.request as any).use(async (url: any, options: any) => {
  const { _store } = getDvaApp();
  _store.dispatch({
    type: 'global/updateShowLoading',
    payload: true,
  });
  requsetCount++;
  // configSafe = await getconfig();
  configSafe = JSON.parse(
    (window as any).sessionStorage.getItem('configSafe_upform'),
  );
  // console.log(configSafe);
  // 处理部分接口code为空字串的情况，将其重置为null

  if (
    configSafe?.SAFE_MODE &&
    options?.method &&
    configSafe._jsonMethod.includes(options.method.toLowerCase()) &&
    (window as any).localStorage.getItem('upform_platform') !== 'ILA' //兰台不走这个拦截
  ) {
    options.headers = {
      'Content-Type': 'application/json',
      realurl: url,
      realmethod: options.method,
      ...options.headers,
    };
    options.method = configSafe._safeMethod;
    return {
      url:
        baseUrl +
        url
          .split('/')
          .slice(0, 2)
          .join('/') +
        `/safeproxy`,
      options: options,
    };
  }
  // console.log(baseUrl, url);
  return {
    url: `${url.includes("/qa") ? "" : baseUrl}${url}`,
    options: {
      ...options,
    },
  };
});
// response拦截器, 处理response
request.interceptors.response.use(
  async (response: any, options: RequestOptionsInit) => {
    try {
      requsetCount--;
      let res_status = response.status;
      // if (res_status >= 400 && res_status < 500) {
      //   // message.error('客户端错误');
      //   return;
      // }
      if (res_status >= 500) {
        message.error('服务器错误');
        return;
      }
      //针对元数据配置界面调用的rman接口淡出处理
      if (res_status === 401) {
        window.location.replace('/unifiedplatform/#/not_authority');
        return;
      }
      // 如果是导出报表这样的情况 不需要处理
      if (options.responseType == 'blob') {
        return response;
      }
      let res: any = {};
      if (!response.url.includes("/session") && !response.url.includes("/chat")) {
        res = await response.clone().json();
      }
      if (
        (res?.errorCode &&
          !(
            res.errorCode === 'success' ||
            res.errorCode === '200' ||
            res.errorCode.indexOf('0000_0000') > -1
          )) ||
        (res?.success && res.success !== true) ||
        res?.statusCode === '401'
      ) {
        errorHandler(res, options);
        if (!options.ifHideError) {
          return Promise.reject(res);
        }
      }
      if (requsetCount === 0) {
        const { _store } = getDvaApp();
        _store.dispatch({
          type: 'global/updateShowLoading',
          payload: false,
        });
      }
      return response;
    } catch (err) {
      console.log(err);
      const platform = window.localStorage.getItem('upform_platform');
      if (platform !== 'ILA') {
        message.error('未知错误!');
      }
    }
  },
);

export default request;
