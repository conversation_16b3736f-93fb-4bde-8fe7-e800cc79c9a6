.left {
  width: 320px;
  height: calc(100vh - 52px);
  padding: 20px;
  background: url('~@/images/left.png');
  .new {
    color: white;
    // 虚线
    border: 1px dashed white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    margin-top: 20px;
    border-radius: 5px;

    cursor: pointer;
    > p {
      margin-top: 10px;
      font-size: 14px;
    }
  }
  .tabs {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 20px;
    max-height: calc(100% - 110px);
    overflow: auto;
    .tab {
      padding: 15px 10px;
      color: white;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      line-height: 24px;
      &:first-child {
        margin-top: 20px;
      }
      &.selected {
        background-color: var(--primary-color);
      }
    }
  }
}
