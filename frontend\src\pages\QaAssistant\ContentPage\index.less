.content-page-style {
    width: 100%;
    height: 100%;
    // padding: 20px;
    background-image: url(~@/images/chatTools/chat_background.png);
    background-size: 100% 100%;
    background-attachment: fixed;
    background-repeat: no-repeat;
    background-position: center center;

    .select-label {
        width: 50%;
        height: 50px;
        line-height: 50px;
        color: rgba(0, 0, 0, 0.8);
        font-weight: bold;
        font-size: 17px;

        .open-icon {
            width: 14px;
            height: 10px;
        }
    }

    .dropdown-style {
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        width: 100%;
        // margin-left: -20px;
        // margin-top: -20px;
        padding: 10px 20px;
        background-color: rgba(255, 255, 255, 0.5);
    }

    .line {
        width: 100%;
        padding-top: 10px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .icons {
        width: 50%;
        text-align: right;
        display: flex;
        column-gap: 10px;
        justify-content: flex-end;
        align-items: center;

        .header-img {
            width: 24px;
            height: 24px;
        }
        
    }
}