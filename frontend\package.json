{"private": true, "scripts": {"start": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:publisher": "node theme && cross-env NODE_OPTIONS=--openssl-legacy-provider UMI_ENV=publisher umi dev", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider umi build", "postinstall": "umi generate tmp && patch-package", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "test": "umi-test", "test:coverage": "umi-test --coverage", "generate": "node ./scripts/generate"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/cssinjs": "1.5.6", "@ant-design/pro-components": "2.4.2", "@ant-design/pro-layout": "^5.0.12", "@microsoft/fetch-event-source": "^2.0.1", "@types/sortablejs": "^1.13.0", "@umijs/preset-react": "1.x", "@umijs/utils": "^4.0.83", "abortcontroller-polyfill": "^1.7.5", "ahooks": "^2.10.12", "antd": "^4.24.2", "antd-img-crop": "^3.16.0", "antd-theme-generator": "^1.2.8", "array-move": "3.0.1", "better-scroll": "^2.0.4", "classnames": "^2.2.6", "compression-webpack-plugin": "5.0.1", "core-js": "^3.23.5", "css-vars-ponyfill": "^2.4.8", "dexie": "^3.2.3", "dexie-react-hooks": "^1.1.1", "docx-preview": "^0.1.14", "dompurify": "^3.0.3", "echarts": "^5.2.0", "echarts-for-react": "^3.0.1", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "html2canvas": "^1.4.1", "immer": "^10.0.2", "immutability-helper": "^3.1.1", "jquery": "^3.7.1", "lint-staged": "^10.0.7", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "mpegts.js": "^1.7.3", "pdfjs-dist": "^4.4.168", "prettier": "^1.19.1", "react": "^16.12.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^16.12.0", "react-flow-renderer": "^9.7.4", "react-infinite-scroll-component": "^6.1.0", "react-json-view": "^1.21.3", "react-markdown": "^9.0.1", "react-show-more-text": "^1.5.2", "react-sortable-hoc": "^2.0.0", "sortablejs": "^1.15.0", "umi": "3.5.41", "use-immer": "^0.9.0", "video.js": "^7.3.52", "watermark-component-for-react": "^1.0.0", "xss": "^1.0.14", "yorkie": "^2.0.0"}, "devDependencies": {"@types/dompurify": "^3.0.2", "@types/jquery": "^3.5.19", "@types/lodash": "^4.14.198", "@types/react-syntax-highlighter": "^15.5.13", "@types/video.js": "^7.3.52", "cross-env": "^7.0.3", "patch-package": "^8.0.0"}, "resolutions": {"antd": "^4.24.2", "@ant-design/cssinjs": "1.5.6", "lodash": "^4.17.21"}}