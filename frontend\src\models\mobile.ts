/*
 * @Author: 冉志诚
 * @Date: 2024-05-07 15:15:22
 * @LastEditTime: 2024-05-07 15:15:31
 * @FilePath: \frontend\src\models\mobile.ts
 * @Description: 
 */
/*
 * @Author: 冉志诚
 * @Date: 2024-05-07 14:25:23
 * @LastEditTime: 2024-05-07 14:25:24
 * @FilePath: \frontend\src\models\mobile.ts
 * @Description:
 */
import { useState } from 'react';

// 存放全局数据
const useMobile = () => {
  const [isMobile, setIsMobile] = useState(false);
  return {
    isMobile,
    setIsMobile,
  };
};

export default useMobile;
