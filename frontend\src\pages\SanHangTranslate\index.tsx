import React, { useState, useRef, useEffect } from 'react';
import './index.less'
import LanguageSelection from './components/LanguageSelection'
import TranSlateDom from './components/TranSlateDom'
import ExtensionInformation from './components/ExtensionInformation';
import Recommendation from './components/Recommendation';
const SanHangTranslate: React.FC = () => {
  const [sourceLanguage, setSourceLanguage] = useState('en');
  const [targetLanguage, setTargetLanguage] = useState('cn');
  const [model, setModel] = useState('');
  const [translatedResult, setTranslatedResult] = useState<string>('轰炸指挥飞机”指的是一种在轰炸行动中用于指挥和协调轰炸任务的飞机。这类飞机通常配备了先进的通信、导航和指挥控制系统，以便对参与轰炸的其他飞机进行有效的指挥和引导。');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 确保消息来自预期的源，以增强安全性
      // if (event.origin !== "http://expected-origin.com") return;

      if (event.data === 'resourceullScreenState') {
        if (iframeRef.current) {
          if (iframeRef.current.requestFullscreen) {
            iframeRef.current.requestFullscreen();
          } else if ((iframeRef.current as any).mozRequestFullScreen) {
            /* Firefox */
            (iframeRef.current as any).mozRequestFullScreen();
          } else if ((iframeRef.current as any).webkitRequestFullscreen) {
            /* Chrome, Safari and Opera */
            (iframeRef.current as any).webkitRequestFullscreen();
          } else if ((iframeRef.current as any).msRequestFullscreen) {
            /* IE/Edge */
            (iframeRef.current as any).msRequestFullscreen();
          }
        }
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);

  const handleTranslate = (textToTranslate: string) => {
    // setTranslatedResult(`${textToTranslate} (已翻译)`);
  };

  return (
    <div className='sanhangtranslate_view'>
        <LanguageSelection LanguageChange={(sourceLanguage,targetLanguage)=>{
          setSourceLanguage(targetLanguage);
          setTargetLanguage(sourceLanguage);
        }} modelChange={(e)=>{setModel(e)}}  />
        <div className='content'>
          <div className='left_view'>
            <TranSlateDom 
              sourceLanguage={sourceLanguage} 
              targetLanguage={targetLanguage} 
              model={model}
              translatedText={translatedResult}
              onTranslate={handleTranslate}
            />
            <ExtensionInformation keyword="轰炸行动" />
            <Recommendation />
          </div>
          <div className='right_view'>
            <iframe ref={iframeRef} src="/terminator/resource/chat?id=e9dd3cfc-7b01-11ef-9a61-faaf48e3e03e&title=智能问答助手" frameBorder={0}></iframe>
          </div>
        </div>
    </div>
  );
};

export default SanHangTranslate;
