/*
 * @Author: 李晋
 * @Date: 2021-10-29 11:41:48
 * @Email: <EMAIL>
 * @LastEditTime: 2022-05-20 15:48:02
 * @Description: file information
 * @Company: Sobey
 */

import config from '@/utils/config';

let configSafe:any = null;

// const getconfig = async()=> {
//   if ((window as any).configSafe) {
//     return (window as any).configSafe;
//   } else {
//     const res = await fetch('safeMethodConfig.json');
//     const result = await res.json();
//     (window as any).configSafe = result;
//     return result;
//   }
// }


const getDetailToolTip = (courseStatus?: NoteConstant.CourseStatus) => {
  switch(courseStatus) {
    case NoteConstant.CourseStatus.Down:
      return '内容已下架'
    case NoteConstant.CourseStatus.Delete:
      return '内容已删除'
    case NoteConstant.CourseStatus.On:
      return ''
    default:
      return ''
  }
}

namespace NoteConstant {

  export enum NoteCata {
    All = 'all', // 所有笔记
    Course = 'course', // 课程笔记
    VideoReview = 'review', // 在线课堂笔记
    Live = 'live', // 直播笔记
  }

  export enum CourseStatus {
    Down = 0, // 下架
    On = 1, // 上架
    Delete = -1, // 删除
    Other = 99, // 忽略-后端留给直播笔记的
  }


( async()=>{
  // configSafe = await getconfig()
  configSafe = JSON.parse((window as any).sessionStorage.getItem('configSafe_upform'));;
})()


  export const courseType = [
    {
      code: 'micro',
      name: '微课',
      backgroundColor: '#549CFF',
      ifShowChapter: false,
      getDetailUrl: (
        courseId: string = '',
        resourceId: string = '',
        noteId: string = '',
      ) => {
        if (courseId) {
          return `${config.baseUrl}/${configSafe?.webmodel}/course/micro/${courseId}?noteId=${noteId}`;
        }
      },
      getDetailToolTip
    },
    {
      code: 'mooc',
      name: '公开课',
      backgroundColor: '#549CFF',
      ifShowChapter: true,
      getDetailUrl: (
        courseId: string = '',
        resourceId: string = '',
        noteId: string = '',
        semester?: string,
        schoolYear?: string,
        semesterId?: number
      ) => {
        if (courseId && resourceId) {
          let url = `${config.baseUrl}/${configSafe?.webmodel}/course/mooc/${courseId}/${resourceId}?noteId=${noteId}`;
          if(semesterId){
            url = url + '&semester=' + semesterId
          }
          return url
        }
      },
      getDetailToolTip
    },
    {
      code: 'spoc',
      name: '班级课',
      backgroundColor: '#549CFF',
      ifShowChapter: true,
      getDetailUrl: (
        courseId: string = '',
        resourceId: string = '',
        noteId: string = '',
      ) => {
        if (courseId && resourceId) {
          return `${config.baseUrl}/${configSafe?.webmodel}/course/spoc/${courseId}/${resourceId}?noteId=${noteId}`;
        }
      },
      getDetailToolTip
    },
    {
      code: 'review',
      name: '在线课堂',
      backgroundColor: '#41D2A0',
      ifShowChapter: true,
      getDetailUrl: (
        courseId: string = '',
        resourceId: string = '',
        noteId: string = '',
        semester: string = '',
        schoolYear:string = '',
      ) => {
        if (courseId) {
          return `${config.baseUrl}/${configSafe?.webmodel}/videoreview/${courseId}?id=${resourceId}&schoolYear=${schoolYear}&semester=${semester}&noteId=${noteId}`;
        }
      },
      getDetailToolTip
    },
    {
      code: 'live',
      name: '直播',
      backgroundColor: '#FEA67D',
      ifShowChapter: false,
      getDetailUrl: (
        courseId: string = '',
        resourceId: string = '',
        noteId: string = '',
      ) => {
        return '';
      },
      getDetailToolTip
    },
  ];
}
export default NoteConstant;
