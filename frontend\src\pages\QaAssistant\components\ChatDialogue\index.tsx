import { reqChatHistory, reqMessage, addQa } from '@/api/assistant';
import IconFont from '@/components/iconFont/iconFont';
import NewChat from '@/images/overview/newChat.png';
import { getUuid } from '@/utils/utils';
import { useLocation } from 'umi';
import { Button, Spin, Input, message } from 'antd';
import dayjs from 'dayjs';
import { getWrite } from './common';
import userAvatar from '@/assets/img/qa-assistant.png';
import React, { FC, useEffect, useRef, useState } from 'react';
import ChatItem from '../ChatItem';
import styles from './index.less';

const { TextArea } = Input;

interface ChatContentProps {
  frameId: string;
  agentData: any
  assistantId: string | undefined;
  handleAddQa: () => void;
  updateFirstList?: (id: string) => void;
}

const ChatDialogue: FC<ChatContentProps> = ({
  frameId,
  agentData,
  assistantId,
  handleAddQa,
  updateFirstList,
}) => {
  const coRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<any[]>([]);
  // const [loading, setLoading] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [chatMessage, setChatMessage] = useState<string | null>(null);
  const [chatCourse, setChatCourse] = useState<any>([]);
  const chatMsgRef = useRef<string>('');
  const courseRef = useRef<any[]>([]);

  const { search } = useLocation();
  const searchParams = new URLSearchParams(search);


  const flatten = (arr: any[]) => {
    let result: any[] = [];
    arr.forEach(item => {
      if (Array.isArray(item)) {
        result = result.concat(flatten(item));
      } else {
        result.push(item);
      }
    });
    return result;
  }


  const getTypeList = (arr: any[], type: string) => {
    const res: any[] = []
    flatten(arr).forEach(item => {
      if (item.type === type) {
        res.push(item)
      }
    })
    return res
  }

  const toBottom = () => {
    const co: any = coRef.current;
    if (co) {
      co.scrollTop = co.scrollHeight;
    }
  };

  const getHistory = () => {
    setLoading(true);
    reqChatHistory({ chat_id: frameId }).then((res: any) => {
      setLoading(false);
      setList(res?.extend_message ?? []);
      requestAnimationFrame(toBottom);
    });
  };
  // 发送消息（正式对话）
  const handleChat = (data: any) => {
    reqMessage(data).then((response: any) => {
      const reader = response.body.getReader();
      if (response.status !== 200) throw (response);
      // 处理流数据
      const write = getWrite(
        reader,
        response.headers.get('Content-Type') !== 'application/json',
        (content: string, config: any, finish: boolean) => {
          if (finish && content) {
            // console.info('打印完成....', content)
            if (config?.record_id) {
              setList((pre) => [
                ...pre,
                {
                  content: content,
                  record_id: config.record_id,
                  role: 'assistant',
                  course: getTypeList(courseRef.current, 'micro'),
                  fragmentList: getTypeList(courseRef.current, 'referenced')
                },
              ]);

            }
            setChatMessage(null);
            setChatCourse([]);
          } else {
            // console.info('正在返回中...', content)
            // if (config?.role === 'tools') {
            //   courseRef.current = [...courseRef.current, content];
            //   setChatCourse(getTypeList(courseRef.current, 'micro'));
            // } else {
            chatMsgRef.current += content;
            requestAnimationFrame(toBottom);
            setChatMessage((pre) => pre + content);
            // }
          }
        }
      )
      requestAnimationFrame(toBottom);
      return reader.read().then(write)
    }).catch((e: any) => {
      console.info(e)
      message.error(e?.statusText || '聊天框已失效，请刷新');
      setChatMessage(null);
      setChatCourse([]);
    })

  };
  // 新创对话(新建对话)
  const createNewChat = (content: string) => {
    const id = searchParams.get('assistant_id');
    const params = { application_id: id, content, direct: true };
    addQa(params).then((response: any) => {
      if (response.status !== 200) throw (response);
      const reader = response.body.getReader();
      // 处理流数据
      const write = getWrite(
        reader,
        response.headers.get('Content-Type') !== 'application/json',
        (content: string, config: any, finish: boolean) => {
          if (finish && content) {
            // console.info('打印完成....', content)
            if (config?.record_id) {
              setList((pre) => [
                ...pre,
                {
                  content: content,
                  record_id: config.record_id,
                  role: 'assistant',
                  course: getTypeList(courseRef.current, 'micro'),
                  fragmentList: getTypeList(courseRef.current, 'referenced')
                },
              ]);
            }
            const chat_info = config?.content?.chat_info || {};
            updateFirstList && updateFirstList(chat_info.id || getUuid());
            setChatMessage(null);
            setChatCourse([]);
          } else {
            // console.info('正在返回中...', content)
            // if (config?.role === 'tools') {
            //   courseRef.current = [...courseRef.current, content];
            //   setChatCourse(getTypeList(courseRef.current, 'micro'));
            // } else {
            chatMsgRef.current += content;
            requestAnimationFrame(toBottom);
            setChatMessage((pre) => pre + content);
            // }
          }
        }
      )
      requestAnimationFrame(toBottom);
      return reader.read().then(write)
    }).catch((e: any) => {
      console.info(e)
      message.error(e?.statusText || '聊天框已失效，请刷新');
      setChatMessage(null);
      setChatCourse([]);
    })
  }

  const handleSubmit = (text?: string) => {
    if (!text && !inputValue && chatMessage === null) {
      return;
    }
    const input = text ?? inputValue;
    setInputValue('');
    chatMsgRef.current = '';
    courseRef.current = [];
    const pMsgId = list.length > 0 ? list[list.length - 1].message_id : '';

    // 正式对话参数
    const data = {
      chat_id: frameId,
      chat_record_id: pMsgId, // 父级id
      content: input,
    };

    setList((pre: any) => [...pre, { ...data, record_id: data.chat_record_id || getUuid(), role: 'user' }]);
    // 根据frameId判断3种情况
    if (frameId === '预览对话') {
      // temporaryChat(temporaryData);
    } else if (frameId === '新对话') {
      createNewChat(input);
    } else {
      handleChat(data)
    }
    setChatMessage('');
    requestAnimationFrame(toBottom);
  };
  useEffect(() => {
    setChatMessage(null);
  }, [assistantId]);
  useEffect(() => {
    console.info('frameId', frameId)
    if (frameId) {
      if (frameId === '新对话') {
        setInputValue('');
        setList([]);
    } else {
        getHistory();
        setInputValue('');
    }
    }
  }, [frameId, agentData]);
  const handleEnter = (event: any) => {
    if (event.keyCode === 13) {
      if (!event.shiftKey) {
        event.preventDefault();
        handleSubmit();
      }
    }
  };

  // 顶部的便捷提问
  const askAbout = (text: string) => {
    if (chatMessage === '') {
      setInputValue(text);
      requestAnimationFrame(toBottom);
    } else if (frameId === '预览对话' && (!agentData.model_id || !agentData.embeddings_model_id)) {
      message.warning('请先选择大模型才可进行对话~');
    } else {
      handleSubmit(text);
    }
  }

  return (
    <div className={styles.chatdialogueStyle}>
      <Spin spinning={loading} wrapperClassName="chat-loading-style">
        <div className={styles.chatLsWrp} ref={coRef}
          style={{ display: agentData && JSON.stringify(agentData) !== '{}' ? 'block' : 'none' }}
        >
          <ChatItem
            avatar={agentData.icon}
            key="default-msg"
          >
            <p>{agentData.prologue}</p>
            {agentData.recommendation_problems?.map((item: string) => (
              <p key={item}>
                <a onClick={() => askAbout(item)}>
                  <span className='point' />
                  <span>{item}</span>
                </a>
              </p>
            ))}
          </ChatItem>
          {list.map((item: any) => (
            <ChatItem
              avatar={
                item.role === 'assistant' ? agentData.icon : userAvatar
              }
              key={`${item.record_id}_${item.role}`}
              data={item}
              content={<pre>{item.content}</pre>}
              isMine={item?.role === 'user'}
            />
          ))}
          {chatMessage !== null && (
            <ChatItem
              avatar={agentData.icon}
              key="assistant-msg"
              tag="assistant-msg"
              content={
                <pre>
                  {chatMessage === '' ? '正在输入' : chatMessage}
                  {chatMessage === '' && <span className={styles.dotting}></span>}
                </pre>
              }
            />
          )}
          <div className={styles.chatBottomWrp}></div>
        </div>
        <div className={styles.fooderTextContent}
          style={{ display: agentData && JSON.stringify(agentData) !== '{}' ? 'flex' : 'none' }}
        >
          <img src={NewChat} title='开启新对话' style={{ width: 32, height: 32, cursor: 'pointer' }}
            onClick={handleAddQa}
          />
          <TextArea
            rows={1}
            placeholder="请输入问题，按enter键发送"
            value={inputValue}
            onChange={(e: any) => {
              setInputValue(e.target.value);
              requestAnimationFrame(toBottom);
            }}
            style={{ resize: 'none' }}
            onKeyDown={handleEnter}
          />
          <Button
            type="primary"
            loading={chatMessage !== null}
            className={`sub-btn ${styles.button}`}
            onClick={() => handleSubmit()}
          >
            <IconFont type="iconfabushu" />
            发送
          </Button>
        </div>
      </Spin>
    </div>
  );
};

export default ChatDialogue;
