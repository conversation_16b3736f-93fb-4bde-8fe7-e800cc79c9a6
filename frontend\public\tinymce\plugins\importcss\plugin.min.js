/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.6.2 (2020-12-08)
 */
!function(){"use strict";var n,t=tinymce.util.Tools.resolve("tinymce.PluginManager"),v=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),f=tinymce.util.Tools.resolve("tinymce.EditorManager"),m=tinymce.util.Tools.resolve("tinymce.Env"),h=tinymce.util.Tools.resolve("tinymce.util.Tools"),d=function(t){return t.getParam("importcss_selector_converter")},o=(n="array",function(t){return r=typeof(e=t),(null===e?"null":"object"==r&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==r&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":r)===n;var e,r}),i=Array.prototype.push,l=function(t,e){return function(t){for(var e=[],r=0,n=t.length;r<n;++r){if(!o(t[r]))throw new Error("Arr.flatten item "+r+" was not an array, input: "+t);i.apply(e,t[r])}return e}(function(t,e){for(var r=t.length,n=new Array(r),o=0;o<r;o++){var i=t[o];n[o]=e(i,o)}return n}(t,e))},p=function(e){return"string"==typeof e?function(t){return-1!==t.indexOf(e)}:e instanceof RegExp?function(t){return e.test(t)}:e},_=function(s,t,a){var u=[],r={};function l(t,e){var r,n,o,i=t.href;if(n=i,o=m.cacheSuffix,"string"==typeof n&&(n=n.replace("?"+o,"").replace("&"+o,"")),(i=n)&&a(i,e)&&!function(t,e){var r,n=!1!==(r=t.getParam("skin"))&&(r||"oxide");if(n){var o=t.getParam("skin_url"),i=o?t.documentBaseURI.toAbsolute(o):f.baseURL+"/skins/ui/"+n,c=f.baseURL+"/skins/content/";return e===i+"/content"+(t.inline?".inline":"")+".min.css"||-1!==e.indexOf(c)}return!1}(s,i)){h.each(t.imports,function(t){l(t,!0)});try{r=t.cssRules||t.rules}catch(c){}h.each(r,function(t){t.styleSheet?l(t.styleSheet,!0):t.selectorText&&h.each(t.selectorText.split(","),function(t){u.push(h.trim(t))})})}}h.each(s.contentCSS,function(t){r[t]=!0}),a=a||function(t,e){return e||r[t]};try{h.each(t.styleSheets,function(t){l(t)})}catch(e){}return u},x=function(t,e){var r,n=/^(?:([a-z0-9\-_]+))?(\.[a-z0-9_\-\.]+)$/i.exec(e);if(n){var o=n[1],i=n[2].substr(1).split(".").join(" "),c=h.makeMap("a,img");return n[1]?(r={title:e},t.schema.getTextBlockElements()[o]?r.block=o:t.schema.getBlockElements()[o]||c[o.toLowerCase()]?r.selector=o:r.inline=o):n[2]&&(r={inline:"span",title:e.substr(1),classes:i}),!1!==t.getParam("importcss_merge_classes")?r.classes=i:r.attributes={"class":i},r}},P=function(t,e){return null===e||!1!==t.getParam("importcss_exclusive")},r=function(y){y.on("init",function(t){var e,r,n,o,i=(e=[],r=[],n={},{addItemToGroup:function(t,e){n[t]?n[t].push(e):(r.push(t),n[t]=[e])},addItem:function(t){e.push(t)},toFormats:function(){return l(r,function(t){var e=n[t];return 0===e.length?[]:[{title:t,items:e}]}).concat(e)}}),g={},c=p(y.getParam("importcss_selector_filter")),s=(o=y.getParam("importcss_groups"),h.map(o,function(t){return h.extend({},t,{original:t,selectors:{},filter:p(t.filter),item:{text:t.title,menu:[]}})})),a=function(t,e){if(f=t,p=g,!(P(y,m=e)?f in p:f in m.selectors)){a=t,l=g,P(y,u=e)?l[a]=!0:u.selectors[a]=!0;var r=(i=(o=y).plugins.importcss,c=t,((s=e)&&s.selector_converter?s.selector_converter:d(o)?d(o):function(){return x(o,c)}).call(i,c,s));if(r){var n=r.name||v.DOM.uniqueId();return y.formatter.register(n,r),h.extend({},{title:r.title,format:n})}}var o,i,c,s,a,u,l,f,m,p;return null};h.each(_(y,y.getDoc(),p(y.getParam("importcss_file_filter"))),function(r){var t,e,n,o;-1===r.indexOf(".mce-")&&(c&&!c(r)||(n=s,o=r,0<(t=h.grep(n,function(t){return!t.filter||t.filter(o)})).length?h.each(t,function(t){var e=a(r,t);e&&i.addItemToGroup(t.title,e)}):(e=a(r,null))&&i.addItem(e)))});var u=i.toFormats();y.fire("addStyleModifications",{items:u,replace:!y.getParam("importcss_append")})})};t.add("importcss",function(t){return r(t),e=t,{convertSelectorToFormat:function(t){return x(e,t)}};var e})}();