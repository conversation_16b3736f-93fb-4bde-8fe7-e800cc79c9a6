tinymce.Resource.add("tinymce.plugins.advcode.CodeMirror", function() {
    var e = window.CodeMirror;
    window.CodeMirror = void 0, t = function() {
        "use strict";
        var e = navigator.userAgent, t = navigator.platform, f = /gecko\/\d/i.test(e), n = /MSIE \d/.test(e), r = /Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e), i = /Edge\/(\d+)/.exec(e), v = n || r || i, y = v && (n ? document.documentMode || 6 : +(i || r)[1]), d = !i && /WebKit\//.test(e), r = d && /Qt\/\d+\.\d+/.test(e), o = !i && /Chrome\//.test(e), p = /Opera\//.test(e), a = /Apple Computer/.test(navigator.vendor), l = /Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e), u = /PhantomJS/.test(e), s = !i && /AppleWebKit/.test(e) && /Mobile\/\w+/.test(e), c = /Android/.test(e), h = s || c || /webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e), g = s || /Mac/.test(t), m = /\bCrOS\b/.test(e), b = /win/i.test(t), e = p && e.match(/Version\/(\d*\.\d*)/);
        (e = e && Number(e[1])) && 15 <= e && (d = !(p = !1));
        var w = g && (r || p && (null == e || e < 12.11)), x = f || v && 9 <= y;
        function C(e) {
            return new RegExp("(^|\\s)" + e + "(?:$|\\s)\\s*");
        }
        var S, k = function(e, t) {
            var n = e.className, r = C(t).exec(n);
            r && (t = n.slice(r.index + r[0].length), e.className = n.slice(0, r.index) + (t ? r[1] + t : ""));
        };
        function L(e) {
            for (var t = e.childNodes.length; 0 < t; --t) e.removeChild(e.firstChild);
            return e;
        }
        function M(e, t) {
            return L(e).appendChild(t);
        }
        function T(e, t, n, r) {
            var i = document.createElement(e);
            if (n && (i.className = n), r && (i.style.cssText = r), "string" == typeof t) i.appendChild(document.createTextNode(t)); else if (t) for (var o = 0; o < t.length; ++o) i.appendChild(t[o]);
            return i;
        }
        function N(e, t, n, r) {
            r = T(e, t, n, r);
            return r.setAttribute("role", "presentation"), r;
        }
        function O(e, t) {
            if (3 == t.nodeType && (t = t.parentNode), e.contains) return e.contains(t);
            do {
                if (11 == t.nodeType && (t = t.host), t == e) return !0;
            } while (t = t.parentNode);
        }
        function A() {
            var t;
            try {
                t = document.activeElement;
            } catch (e) {
                t = document.body || null;
            }
            for (;t && t.shadowRoot && t.shadowRoot.activeElement; ) t = t.shadowRoot.activeElement;
            return t;
        }
        function F(e, t) {
            var n = e.className;
            C(t).test(n) || (e.className += (n ? " " : "") + t);
        }
        function D(e, t) {
            for (var n = e.split(" "), r = 0; r < n.length; r++) n[r] && !C(n[r]).test(t) && (t += " " + n[r]);
            return t;
        }
        S = document.createRange ? function(e, t, n, r) {
            var i = document.createRange();
            return i.setEnd(r || e, n), i.setStart(e, t), i;
        } : function(e, t, n) {
            var r = document.body.createTextRange();
            try {
                r.moveToElementText(e.parentNode);
            } catch (e) {
                return r;
            }
            return r.collapse(!0), r.moveEnd("character", n), r.moveStart("character", t), r;
        };
        var W = function(e) {
            e.select();
        };
        function P(e) {
            var t = Array.prototype.slice.call(arguments, 1);
            return function() {
                return e.apply(null, t);
            };
        }
        function H(e, t, n) {
            for (var r in t = t || {}, e) !e.hasOwnProperty(r) || !1 === n && t.hasOwnProperty(r) || (t[r] = e[r]);
            return t;
        }
        function I(e, t, n, r, i) {
            null == t && -1 == (t = e.search(/[^\s\u00a0]/)) && (t = e.length);
            for (var o = r || 0, l = i || 0; ;) {
                var s = e.indexOf("\t", o);
                if (s < 0 || t <= s) return l + (t - o);
                l += s - o, l += n - l % n, o = s + 1;
            }
        }
        s ? W = function(e) {
            e.selectionStart = 0, e.selectionEnd = e.value.length;
        } : v && (W = function(e) {
            try {
                e.select();
            } catch (e) {}
        });
        var E = function() {
            this.id = null;
        };
        function R(e, t) {
            for (var n = 0; n < e.length; ++n) if (e[n] == t) return n;
            return -1;
        }
        E.prototype.set = function(e, t) {
            clearTimeout(this.id), this.id = setTimeout(t, e);
        };
        var z = 30, B = {
            toString: function() {
                return "CodeMirror.Pass";
            }
        }, U = {
            scroll: !1
        }, K = {
            origin: "*mouse"
        }, G = {
            origin: "+move"
        };
        function V(e, t, n) {
            for (var r = 0, i = 0; ;) {
                var o = e.indexOf("\t", r);
                -1 == o && (o = e.length);
                var l = o - r;
                if (o == e.length || t <= i + l) return r + Math.min(l, t - i);
                if (i += o - r, r = o + 1, t <= (i += n - i % n)) return r;
            }
        }
        var q = [ "" ];
        function j(e) {
            for (;q.length <= e; ) q.push(_(q) + " ");
            return q[e];
        }
        function _(e) {
            return e[e.length - 1];
        }
        function $(e, t) {
            for (var n = [], r = 0; r < e.length; r++) n[r] = t(e[r], r);
            return n;
        }
        function X() {}
        function Y(e, t) {
            e = Object.create ? Object.create(e) : (X.prototype = e, new X());
            return t && H(t, e), e;
        }
        var Z = /[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;
        function Q(e) {
            return /\w/.test(e) || "\x80" < e && (e.toUpperCase() != e.toLowerCase() || Z.test(e));
        }
        function J(e, t) {
            return t ? !!(-1 < t.source.indexOf("\\w") && Q(e)) || t.test(e) : Q(e);
        }
        function ee(e) {
            for (var t in e) if (e.hasOwnProperty(t) && e[t]) return;
            return 1;
        }
        var te = /[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;
        function ne(e) {
            return 768 <= e.charCodeAt(0) && te.test(e);
        }
        function re(e, t, n) {
            for (;(n < 0 ? 0 < t : t < e.length) && ne(e.charAt(t)); ) t += n;
            return t;
        }
        function ie(e, t, n) {
            for (var r = n < t ? -1 : 1; ;) {
                if (t == n) return t;
                var i = (t + n) / 2, i = r < 0 ? Math.ceil(i) : Math.floor(i);
                if (i == t) return e(i) ? t : n;
                e(i) ? n = i : t = i + r;
            }
        }
        var oe = null;
        function le(e, t, n) {
            var r;
            oe = null;
            for (var i = 0; i < e.length; ++i) {
                var o = e[i];
                if (o.from < t && o.to > t) return i;
                o.to == t && (o.from != o.to && "before" == n ? r = i : oe = i), o.from == t && (o.from != o.to && "before" != n ? r = i : oe = i);
            }
            return null != r ? r : oe;
        }
        var se, ae, ue, ce, he, fe, de, pe = (se = "bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN", 
        ae = "nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111", 
        ue = /[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/, ce = /[stwN]/, he = /[LRr]/, fe = /[Lb1n]/, 
        de = /[1n]/, function(e, t) {
            var n = "ltr" == t ? "L" : "R";
            if (0 == e.length || "ltr" == t && !ue.test(e)) return !1;
            for (var r, i = e.length, o = [], l = 0; l < i; ++l) o.push((r = e.charCodeAt(l)) <= 247 ? se.charAt(r) : 1424 <= r && r <= 1524 ? "R" : 1536 <= r && r <= 1785 ? ae.charAt(r - 1536) : 1774 <= r && r <= 2220 ? "r" : 8192 <= r && r <= 8203 ? "w" : 8204 == r ? "b" : "L");
            for (var s = 0, a = n; s < i; ++s) {
                var u = o[s];
                "m" == u ? o[s] = a : a = u;
            }
            for (var c = 0, h = n; c < i; ++c) {
                var f = o[c];
                "1" == f && "r" == h ? o[c] = "n" : he.test(f) && "r" == (h = f) && (o[c] = "R");
            }
            for (var d = 1, p = o[0]; d < i - 1; ++d) {
                var g = o[d];
                "+" == g && "1" == p && "1" == o[d + 1] ? o[d] = "1" : "," != g || p != o[d + 1] || "1" != p && "n" != p || (o[d] = p), 
                p = g;
            }
            for (var m = 0; m < i; ++m) {
                var v = o[m];
                if ("," == v) o[m] = "N"; else if ("%" == v) {
                    for (var y = void 0, y = m + 1; y < i && "%" == o[y]; ++y) ;
                    for (var b = m && "!" == o[m - 1] || y < i && "1" == o[y] ? "1" : "N", w = m; w < y; ++w) o[w] = b;
                    m = y - 1;
                }
            }
            for (var x = 0, C = n; x < i; ++x) {
                var S = o[x];
                "L" == C && "1" == S ? o[x] = "L" : he.test(S) && (C = S);
            }
            for (var k = 0; k < i; ++k) if (ce.test(o[k])) {
                for (var L = void 0, L = k + 1; L < i && ce.test(o[L]); ++L) ;
                for (var M = "L" == (k ? o[k - 1] : n), T = M == ("L" == (L < i ? o[L] : n)) ? M ? "L" : "R" : n, N = k; N < L; ++N) o[N] = T;
                k = L - 1;
            }
            for (var O, A = [], F = 0; F < i; ) if (fe.test(o[F])) {
                var D = F;
                for (++F; F < i && fe.test(o[F]); ++F) ;
                A.push(new ge(0, D, F));
            } else {
                var W = F, P = A.length;
                for (++F; F < i && "L" != o[F]; ++F) ;
                for (var H = W; H < F; ) if (de.test(o[H])) {
                    W < H && A.splice(P, 0, new ge(1, W, H));
                    var I = H;
                    for (++H; H < F && de.test(o[H]); ++H) ;
                    A.splice(P, 0, new ge(2, I, H)), W = H;
                } else ++H;
                W < F && A.splice(P, 0, new ge(1, W, F));
            }
            return "ltr" == t && (1 == A[0].level && (O = e.match(/^\s+/)) && (A[0].from = O[0].length, 
            A.unshift(new ge(0, 0, O[0].length))), 1 == _(A).level && (O = e.match(/\s+$/)) && (_(A).to -= O[0].length, 
            A.push(new ge(0, i - O[0].length, i)))), "rtl" == t ? A.reverse() : A;
        });
        function ge(e, t, n) {
            this.level = e, this.from = t, this.to = n;
        }
        function me(e, t) {
            var n = e.order;
            return null == n && (n = e.order = pe(e.text, t)), n;
        }
        var ve = [], ye = function(e, t, n) {
            e.addEventListener ? e.addEventListener(t, n, !1) : e.attachEvent ? e.attachEvent("on" + t, n) : (e = e._handlers || (e._handlers = {}))[t] = (e[t] || ve).concat(n);
        };
        function be(e, t) {
            return e._handlers && e._handlers[t] || ve;
        }
        function we(e, t, n) {
            var r;
            e.removeEventListener ? e.removeEventListener(t, n, !1) : e.detachEvent ? e.detachEvent("on" + t, n) : !(e = (r = e._handlers) && r[t]) || -1 < (n = R(e, n)) && (r[t] = e.slice(0, n).concat(e.slice(n + 1)));
        }
        function xe(e, t) {
            var n = be(e, t);
            if (n.length) for (var r = Array.prototype.slice.call(arguments, 2), i = 0; i < n.length; ++i) n[i].apply(null, r);
        }
        function Ce(e, t, n) {
            return "string" == typeof t && (t = {
                type: t,
                preventDefault: function() {
                    this.defaultPrevented = !0;
                }
            }), xe(e, n || t.type, e, t), Ne(t) || t.codemirrorIgnore;
        }
        function Se(e) {
            var t = e._handlers && e._handlers.cursorActivity;
            if (t) for (var n = e.curOp.cursorActivityHandlers || (e.curOp.cursorActivityHandlers = []), r = 0; r < t.length; ++r) -1 == R(n, t[r]) && n.push(t[r]);
        }
        function ke(e, t) {
            return 0 < be(e, t).length;
        }
        function Le(e) {
            e.prototype.on = function(e, t) {
                ye(this, e, t);
            }, e.prototype.off = function(e, t) {
                we(this, e, t);
            };
        }
        function Me(e) {
            e.preventDefault ? e.preventDefault() : e.returnValue = !1;
        }
        function Te(e) {
            e.stopPropagation ? e.stopPropagation() : e.cancelBubble = !0;
        }
        function Ne(e) {
            return null != e.defaultPrevented ? e.defaultPrevented : 0 == e.returnValue;
        }
        function Oe(e) {
            Me(e), Te(e);
        }
        function Ae(e) {
            return e.target || e.srcElement;
        }
        function Fe(e) {
            var t = e.which;
            return null == t && (1 & e.button ? t = 1 : 2 & e.button ? t = 3 : 4 & e.button && (t = 2)), 
            g && e.ctrlKey && 1 == t && (t = 3), t;
        }
        var De, We, Pe = function() {
            if (v && y < 9) return !1;
            var e = T("div");
            return "draggable" in e || "dragDrop" in e;
        }();
        var He = 3 != "\n\nb".split(/\n/).length ? function(e) {
            for (var t = 0, n = [], r = e.length; t <= r; ) {
                var i = e.indexOf("\n", t);
                -1 == i && (i = e.length);
                var o = e.slice(t, "\r" == e.charAt(i - 1) ? i - 1 : i), l = o.indexOf("\r");
                -1 != l ? (n.push(o.slice(0, l)), t += l + 1) : (n.push(o), t = i + 1);
            }
            return n;
        } : function(e) {
            return e.split(/\r\n?|\n/);
        }, Ie = window.getSelection ? function(e) {
            try {
                return e.selectionStart != e.selectionEnd;
            } catch (e) {
                return !1;
            }
        } : function(e) {
            var t;
            try {
                t = e.ownerDocument.selection.createRange();
            } catch (e) {}
            return !(!t || t.parentElement() != e) && 0 != t.compareEndPoints("StartToEnd", t);
        }, Ee = "oncopy" in (r = T("div")) || (r.setAttribute("oncopy", "return;"), "function" == typeof r.oncopy), Re = null;
        var ze = {}, Be = {};
        function Ue(e) {
            if ("string" == typeof e && Be.hasOwnProperty(e)) e = Be[e]; else if (e && "string" == typeof e.name && Be.hasOwnProperty(e.name)) {
                var t = Be[e.name];
                "string" == typeof t && (t = {
                    name: t
                }), (e = Y(t, e)).name = t.name;
            } else {
                if ("string" == typeof e && /^[\w\-]+\/[\w\-]+\+xml$/.test(e)) return Ue("application/xml");
                if ("string" == typeof e && /^[\w\-]+\/[\w\-]+\+json$/.test(e)) return Ue("application/json");
            }
            return "string" == typeof e ? {
                name: e
            } : e || {
                name: "null"
            };
        }
        function Ke(e, t) {
            t = Ue(t);
            var n = ze[t.name];
            if (!n) return Ke(e, "text/plain");
            var r = n(e, t);
            if (Ge.hasOwnProperty(t.name)) {
                var i, o = Ge[t.name];
                for (i in o) o.hasOwnProperty(i) && (r.hasOwnProperty(i) && (r["_" + i] = r[i]), 
                r[i] = o[i]);
            }
            if (r.name = t.name, t.helperType && (r.helperType = t.helperType), t.modeProps) for (var l in t.modeProps) r[l] = t.modeProps[l];
            return r;
        }
        var Ge = {};
        function Ve(e, t) {
            H(t, Ge.hasOwnProperty(e) ? Ge[e] : Ge[e] = {});
        }
        function qe(e, t) {
            if (!0 === t) return t;
            if (e.copyState) return e.copyState(t);
            var n, r = {};
            for (n in t) {
                var i = t[n];
                i instanceof Array && (i = i.concat([])), r[n] = i;
            }
            return r;
        }
        function je(e, t) {
            for (var n; e.innerMode && (n = e.innerMode(t)) && n.mode != e; ) t = n.state, e = n.mode;
            return n || {
                mode: e,
                state: t
            };
        }
        function _e(e, t, n) {
            return !e.startState || e.startState(t, n);
        }
        var $e = function(e, t, n) {
            this.pos = this.start = 0, this.string = e, this.tabSize = t || 8, this.lastColumnPos = this.lastColumnValue = 0, 
            this.lineStart = 0, this.lineOracle = n;
        };
        function Xe(e, t) {
            if ((t -= e.first) < 0 || t >= e.size) throw new Error("There is no line " + (t + e.first) + " in the document.");
            for (var n = e; !n.lines; ) for (var r = 0; ;++r) {
                var i = n.children[r], o = i.chunkSize();
                if (t < o) {
                    n = i;
                    break;
                }
                t -= o;
            }
            return n.lines[t];
        }
        function Ye(e, t, n) {
            var r = [], i = t.line;
            return e.iter(t.line, n.line + 1, function(e) {
                e = e.text;
                i == n.line && (e = e.slice(0, n.ch)), i == t.line && (e = e.slice(t.ch)), r.push(e), 
                ++i;
            }), r;
        }
        function Ze(e, t, n) {
            var r = [];
            return e.iter(t, n, function(e) {
                r.push(e.text);
            }), r;
        }
        function Qe(e, t) {
            var n = t - e.height;
            if (n) for (var r = e; r; r = r.parent) r.height += n;
        }
        function Je(e) {
            if (null == e.parent) return null;
            for (var t = e.parent, n = R(t.lines, e), r = t.parent; r; r = (t = r).parent) for (var i = 0; r.children[i] != t; ++i) n += r.children[i].chunkSize();
            return n + t.first;
        }
        function et(e, t) {
            var n = e.first;
            e: do {
                for (var r = 0; r < e.children.length; ++r) {
                    var i = e.children[r], o = i.height;
                    if (t < o) {
                        e = i;
                        continue e;
                    }
                    t -= o, n += i.chunkSize();
                }
                return n;
            } while (!e.lines);
            for (var l = 0; l < e.lines.length; ++l) {
                var s = e.lines[l].height;
                if (t < s) break;
                t -= s;
            }
            return n + l;
        }
        function tt(e, t) {
            return t >= e.first && t < e.first + e.size;
        }
        function nt(e, t) {
            return String(e.lineNumberFormatter(t + e.firstLineNumber));
        }
        function rt(e, t, n) {
            if (void 0 === n && (n = null), !(this instanceof rt)) return new rt(e, t, n);
            this.line = e, this.ch = t, this.sticky = n;
        }
        function it(e, t) {
            return e.line - t.line || e.ch - t.ch;
        }
        function ot(e, t) {
            return e.sticky == t.sticky && 0 == it(e, t);
        }
        function lt(e) {
            return rt(e.line, e.ch);
        }
        function st(e, t) {
            return it(e, t) < 0 ? t : e;
        }
        function at(e, t) {
            return it(e, t) < 0 ? e : t;
        }
        function ut(e, t) {
            return Math.max(e.first, Math.min(t, e.first + e.size - 1));
        }
        function ct(e, t) {
            if (t.line < e.first) return rt(e.first, 0);
            var n = e.first + e.size - 1;
            return t.line > n ? rt(n, Xe(e, n).text.length) : (e = Xe(e, (n = t).line).text.length, 
            null == (t = n.ch) || e < t ? rt(n.line, e) : t < 0 ? rt(n.line, 0) : n);
        }
        function ht(e, t) {
            for (var n = [], r = 0; r < t.length; r++) n[r] = ct(e, t[r]);
            return n;
        }
        $e.prototype.eol = function() {
            return this.pos >= this.string.length;
        }, $e.prototype.sol = function() {
            return this.pos == this.lineStart;
        }, $e.prototype.peek = function() {
            return this.string.charAt(this.pos) || void 0;
        }, $e.prototype.next = function() {
            if (this.pos < this.string.length) return this.string.charAt(this.pos++);
        }, $e.prototype.eat = function(e) {
            var t = this.string.charAt(this.pos), e = "string" == typeof e ? t == e : t && (e.test ? e.test(t) : e(t));
            if (e) return ++this.pos, t;
        }, $e.prototype.eatWhile = function(e) {
            for (var t = this.pos; this.eat(e); ) ;
            return this.pos > t;
        }, $e.prototype.eatSpace = function() {
            for (var e = this.pos; /[\s\u00a0]/.test(this.string.charAt(this.pos)); ) ++this.pos;
            return this.pos > e;
        }, $e.prototype.skipToEnd = function() {
            this.pos = this.string.length;
        }, $e.prototype.skipTo = function(e) {
            e = this.string.indexOf(e, this.pos);
            if (-1 < e) return this.pos = e, !0;
        }, $e.prototype.backUp = function(e) {
            this.pos -= e;
        }, $e.prototype.column = function() {
            return this.lastColumnPos < this.start && (this.lastColumnValue = I(this.string, this.start, this.tabSize, this.lastColumnPos, this.lastColumnValue), 
            this.lastColumnPos = this.start), this.lastColumnValue - (this.lineStart ? I(this.string, this.lineStart, this.tabSize) : 0);
        }, $e.prototype.indentation = function() {
            return I(this.string, null, this.tabSize) - (this.lineStart ? I(this.string, this.lineStart, this.tabSize) : 0);
        }, $e.prototype.match = function(e, t, n) {
            if ("string" != typeof e) {
                var r = this.string.slice(this.pos).match(e);
                return r && 0 < r.index ? null : (r && !1 !== t && (this.pos += r[0].length), r);
            }
            r = function(e) {
                return n ? e.toLowerCase() : e;
            };
            if (r(this.string.substr(this.pos, e.length)) == r(e)) return !1 !== t && (this.pos += e.length), 
            !0;
        }, $e.prototype.current = function() {
            return this.string.slice(this.start, this.pos);
        }, $e.prototype.hideFirstChars = function(e, t) {
            this.lineStart += e;
            try {
                return t();
            } finally {
                this.lineStart -= e;
            }
        }, $e.prototype.lookAhead = function(e) {
            var t = this.lineOracle;
            return t && t.lookAhead(e);
        }, $e.prototype.baseToken = function() {
            var e = this.lineOracle;
            return e && e.baseToken(this.pos);
        };
        var ft = function(e, t) {
            this.state = e, this.lookAhead = t;
        }, dt = function(e, t, n, r) {
            this.state = t, this.doc = e, this.line = n, this.maxLookAhead = r || 0, this.baseTokens = null, 
            this.baseTokenPos = 1;
        };
        function pt(t, n, r, e) {
            var a = [ t.state.modeGen ], i = {};
            St(t, n.text, t.doc.mode, r, function(e, t) {
                return a.push(e, t);
            }, i, e);
            for (var u = r.state, o = 0; o < t.state.overlays.length; ++o) !function(e) {
                r.baseTokens = a;
                var o = t.state.overlays[e], l = 1, s = 0;
                r.state = !0, St(t, n.text, o.mode, r, function(e, t) {
                    for (var n = l; s < e; ) {
                        var r = a[l];
                        e < r && a.splice(l, 1, e, a[l + 1], r), l += 2, s = Math.min(e, r);
                    }
                    if (t) if (o.opaque) a.splice(n, l - n, e, "overlay " + t), l = n + 2; else for (;n < l; n += 2) {
                        var i = a[n + 1];
                        a[n + 1] = (i ? i + " " : "") + "overlay " + t;
                    }
                }, i), r.state = u, r.baseTokens = null, r.baseTokenPos = 1;
            }(o);
            return {
                styles: a,
                classes: i.bgClass || i.textClass ? i : null
            };
        }
        function gt(e, t, n) {
            var r, i, o;
            return t.styles && t.styles[0] == e.state.modeGen || (r = mt(e, Je(t)), i = t.text.length > e.options.maxHighlightLength && qe(e.doc.mode, r.state), 
            o = pt(e, t, r), i && (r.state = i), t.stateAfter = r.save(!i), t.styles = o.styles, 
            o.classes ? t.styleClasses = o.classes : t.styleClasses && (t.styleClasses = null), 
            n === e.doc.highlightFrontier && (e.doc.modeFrontier = Math.max(e.doc.modeFrontier, ++e.doc.highlightFrontier))), 
            t.styles;
        }
        function mt(n, r, e) {
            var t = n.doc, i = n.display;
            if (!t.mode.startState) return new dt(t, !0, r);
            var o = function(e, t, n) {
                for (var r, i, o = e.doc, l = n ? -1 : t - (e.doc.mode.innerMode ? 1e3 : 100), s = t; l < s; --s) {
                    if (s <= o.first) return o.first;
                    var a = Xe(o, s - 1), u = a.stateAfter;
                    if (u && (!n || s + (u instanceof ft ? u.lookAhead : 0) <= o.modeFrontier)) return s;
                    a = I(a.text, null, e.options.tabSize);
                    (null == i || a < r) && (i = s - 1, r = a);
                }
                return i;
            }(n, r, e), l = o > t.first && Xe(t, o - 1).stateAfter, s = l ? dt.fromSaved(t, l, o) : new dt(t, _e(t.mode), o);
            return t.iter(o, r, function(e) {
                vt(n, e.text, s);
                var t = s.line;
                e.stateAfter = t == r - 1 || t % 5 == 0 || t >= i.viewFrom && t < i.viewTo ? s.save() : null, 
                s.nextLine();
            }), e && (t.modeFrontier = s.line), s;
        }
        function vt(e, t, n, r) {
            var i = e.doc.mode, o = new $e(t, e.options.tabSize, n);
            for (o.start = o.pos = r || 0, "" == t && yt(i, n.state); !o.eol(); ) bt(i, o, n.state), 
            o.start = o.pos;
        }
        function yt(e, t) {
            if (e.blankLine) return e.blankLine(t);
            if (e.innerMode) {
                t = je(e, t);
                return t.mode.blankLine ? t.mode.blankLine(t.state) : void 0;
            }
        }
        function bt(e, t, n, r) {
            for (var i = 0; i < 10; i++) {
                r && (r[0] = je(e, n).mode);
                var o = e.token(t, n);
                if (t.pos > t.start) return o;
            }
            throw new Error("Mode " + e.name + " failed to advance stream.");
        }
        dt.prototype.lookAhead = function(e) {
            var t = this.doc.getLine(this.line + e);
            return null != t && e > this.maxLookAhead && (this.maxLookAhead = e), t;
        }, dt.prototype.baseToken = function(e) {
            if (!this.baseTokens) return null;
            for (;this.baseTokens[this.baseTokenPos] <= e; ) this.baseTokenPos += 2;
            var t = this.baseTokens[this.baseTokenPos + 1];
            return {
                type: t && t.replace(/( |^)overlay .*/, ""),
                size: this.baseTokens[this.baseTokenPos] - e
            };
        }, dt.prototype.nextLine = function() {
            this.line++, 0 < this.maxLookAhead && this.maxLookAhead--;
        }, dt.fromSaved = function(e, t, n) {
            return t instanceof ft ? new dt(e, qe(e.mode, t.state), n, t.lookAhead) : new dt(e, qe(e.mode, t), n);
        }, dt.prototype.save = function(e) {
            e = !1 !== e ? qe(this.doc.mode, this.state) : this.state;
            return 0 < this.maxLookAhead ? new ft(e, this.maxLookAhead) : e;
        };
        var wt = function(e, t, n) {
            this.start = e.start, this.end = e.pos, this.string = e.current(), this.type = t || null, 
            this.state = n;
        };
        function xt(e, t, n, r) {
            var i, o, l = e.doc, s = l.mode, a = Xe(l, (t = ct(l, t)).line), u = mt(e, t.line, n), c = new $e(a.text, e.options.tabSize, u);
            for (r && (o = []); (r || c.pos < t.ch) && !c.eol(); ) c.start = c.pos, i = bt(s, c, u.state), 
            r && o.push(new wt(c, i, qe(l.mode, u.state)));
            return r ? o : new wt(c, i, u.state);
        }
        function Ct(e, t) {
            if (e) for (;;) {
                var n = e.match(/(?:^|\s+)line-(background-)?(\S+)/);
                if (!n) break;
                e = e.slice(0, n.index) + e.slice(n.index + n[0].length);
                var r = n[1] ? "bgClass" : "textClass";
                null == t[r] ? t[r] = n[2] : new RegExp("(?:^|s)" + n[2] + "(?:$|s)").test(t[r]) || (t[r] += " " + n[2]);
            }
            return e;
        }
        function St(e, t, n, r, i, o, l) {
            var s = n.flattenSpans;
            null == s && (s = e.options.flattenSpans);
            var a = 0, u = null, c = new $e(t, e.options.tabSize, r), h = e.options.addModeClass && [ null ];
            for ("" == t && Ct(yt(n, r.state), o); !c.eol(); ) {
                var f, d = c.pos > e.options.maxHighlightLength ? (s = !1, l && vt(e, t, r, c.pos), 
                c.pos = t.length, null) : Ct(bt(n, c, r.state, h), o);
                if (!h || (f = h[0].name) && (d = "m-" + (d ? f + " " + d : f)), !s || u != d) {
                    for (;a < c.start; ) i(a = Math.min(c.start, a + 5e3), u);
                    u = d;
                }
                c.start = c.pos;
            }
            for (;a < c.pos; ) {
                var p = Math.min(c.pos, a + 5e3);
                i(p, u), a = p;
            }
        }
        var kt = !1, Lt = !1;
        function Mt(e, t, n) {
            this.marker = e, this.from = t, this.to = n;
        }
        function Tt(e, t) {
            if (e) for (var n = 0; n < e.length; ++n) {
                var r = e[n];
                if (r.marker == t) return r;
            }
        }
        function Nt(e, t) {
            if (t.full) return null;
            var n = tt(e, t.from.line) && Xe(e, t.from.line).markedSpans, r = tt(e, t.to.line) && Xe(e, t.to.line).markedSpans;
            if (!n && !r) return null;
            var i = t.from.ch, o = t.to.ch, e = 0 == it(t.from, t.to), l = function(e, t, n) {
                var r;
                if (e) for (var i = 0; i < e.length; ++i) {
                    var o, l = e[i], s = l.marker;
                    !(null == l.from || (s.inclusiveLeft ? l.from <= t : l.from < t)) && (l.from != t || "bookmark" != s.type || n && l.marker.insertLeft) || (o = null == l.to || (s.inclusiveRight ? l.to >= t : l.to > t), 
                    (r = r || []).push(new Mt(s, l.from, o ? null : l.to)));
                }
                return r;
            }(n, i, e), s = function(e, t, n) {
                var r;
                if (e) for (var i = 0; i < e.length; ++i) {
                    var o, l = e[i], s = l.marker;
                    !(null == l.to || (s.inclusiveRight ? l.to >= t : l.to > t)) && (l.from != t || "bookmark" != s.type || n && !l.marker.insertLeft) || (o = null == l.from || (s.inclusiveLeft ? l.from <= t : l.from < t), 
                    (r = r || []).push(new Mt(s, o ? null : l.from - t, null == l.to ? null : l.to - t)));
                }
                return r;
            }(r, o, e), a = 1 == t.text.length, u = _(t.text).length + (a ? i : 0);
            if (l) for (var c = 0; c < l.length; ++c) {
                var h, f = l[c];
                null == f.to && ((h = Tt(s, f.marker)) ? a && (f.to = null == h.to ? null : h.to + u) : f.to = i);
            }
            if (s) for (var d = 0; d < s.length; ++d) {
                var p = s[d];
                null != p.to && (p.to += u), null == p.from ? Tt(l, p.marker) || (p.from = u, a && (l = l || []).push(p)) : (p.from += u, 
                a && (l = l || []).push(p));
            }
            l = l && Ot(l), s && s != l && (s = Ot(s));
            var g = [ l ];
            if (!a) {
                var m, v = t.text.length - 2;
                if (0 < v && l) for (var y = 0; y < l.length; ++y) null == l[y].to && (m = m || []).push(new Mt(l[y].marker, null, null));
                for (var b = 0; b < v; ++b) g.push(m);
                g.push(s);
            }
            return g;
        }
        function Ot(e) {
            for (var t = 0; t < e.length; ++t) {
                var n = e[t];
                null != n.from && n.from == n.to && !1 !== n.marker.clearWhenEmpty && e.splice(t--, 1);
            }
            return e.length ? e : null;
        }
        function At(e) {
            var t = e.markedSpans;
            if (t) {
                for (var n = 0; n < t.length; ++n) t[n].marker.detachLine(e);
                e.markedSpans = null;
            }
        }
        function Ft(e, t) {
            if (t) {
                for (var n = 0; n < t.length; ++n) t[n].marker.attachLine(e);
                e.markedSpans = t;
            }
        }
        function Dt(e) {
            return e.inclusiveLeft ? -1 : 0;
        }
        function Wt(e) {
            return e.inclusiveRight ? 1 : 0;
        }
        function Pt(e, t) {
            var n = e.lines.length - t.lines.length;
            if (0 != n) return n;
            var r = e.find(), i = t.find(), n = it(r.from, i.from) || Dt(e) - Dt(t);
            if (n) return -n;
            i = it(r.to, i.to) || Wt(e) - Wt(t);
            return i || t.id - e.id;
        }
        function Ht(e, t) {
            var n, r = Lt && e.markedSpans;
            if (r) for (var i, o = 0; o < r.length; ++o) (i = r[o]).marker.collapsed && null == (t ? i.from : i.to) && (!n || Pt(n, i.marker) < 0) && (n = i.marker);
            return n;
        }
        function It(e) {
            return Ht(e, !0);
        }
        function Et(e) {
            return Ht(e, !1);
        }
        function Rt(e, t, n, r, i) {
            var t = Xe(e, t), o = Lt && t.markedSpans;
            if (o) for (var l = 0; l < o.length; ++l) {
                var s = o[l];
                if (s.marker.collapsed) {
                    var a = s.marker.find(0), u = it(a.from, n) || Dt(s.marker) - Dt(i), c = it(a.to, r) || Wt(s.marker) - Wt(i);
                    if (!(0 <= u && c <= 0 || u <= 0 && 0 <= c) && (u <= 0 && (s.marker.inclusiveRight && i.inclusiveLeft ? 0 <= it(a.to, n) : 0 < it(a.to, n)) || 0 <= u && (s.marker.inclusiveRight && i.inclusiveLeft ? it(a.from, r) <= 0 : it(a.from, r) < 0))) return 1;
                }
            }
        }
        function zt(e) {
            for (var t; t = It(e); ) e = t.find(-1, !0).line;
            return e;
        }
        function Bt(e, t) {
            var n = Xe(e, t), e = zt(n);
            return n == e ? t : Je(e);
        }
        function Ut(e, t) {
            if (t > e.lastLine()) return t;
            var n, r = Xe(e, t);
            if (!Kt(e, r)) return t;
            for (;n = Et(r); ) r = n.find(1, !0).line;
            return Je(r) + 1;
        }
        function Kt(e, t) {
            var n = Lt && t.markedSpans;
            if (n) for (var r, i = 0; i < n.length; ++i) if ((r = n[i]).marker.collapsed) {
                if (null == r.from) return !0;
                if (!r.marker.widgetNode && 0 == r.from && r.marker.inclusiveLeft && function e(t, n, r) {
                    if (null == r.to) {
                        var i = r.marker.find(1, !0);
                        return e(t, i.line, Tt(i.line.markedSpans, r.marker));
                    }
                    if (r.marker.inclusiveRight && r.to == n.text.length) return !0;
                    for (var o = void 0, l = 0; l < n.markedSpans.length; ++l) if ((o = n.markedSpans[l]).marker.collapsed && !o.marker.widgetNode && o.from == r.to && (null == o.to || o.to != r.from) && (o.marker.inclusiveLeft || r.marker.inclusiveRight) && e(t, n, o)) return !0;
                }(e, t, r)) return !0;
            }
        }
        function Gt(e) {
            for (var t = 0, n = (e = zt(e)).parent, r = 0; r < n.lines.length; ++r) {
                var i = n.lines[r];
                if (i == e) break;
                t += i.height;
            }
            for (var o = n.parent; o; o = (n = o).parent) for (var l = 0; l < o.children.length; ++l) {
                var s = o.children[l];
                if (s == n) break;
                t += s.height;
            }
            return t;
        }
        function Vt(e) {
            if (0 == e.height) return 0;
            for (var t, n = e.text.length, r = e; t = It(r); ) {
                var i = t.find(0, !0), r = i.from.line;
                n += i.from.ch - i.to.ch;
            }
            for (r = e; t = Et(r); ) {
                var o = t.find(0, !0);
                n -= r.text.length - o.from.ch, n += (r = o.to.line).text.length - o.to.ch;
            }
            return n;
        }
        function qt(e) {
            var n = e.display, e = e.doc;
            n.maxLine = Xe(e, e.first), n.maxLineLength = Vt(n.maxLine), n.maxLineChanged = !0, 
            e.iter(function(e) {
                var t = Vt(e);
                t > n.maxLineLength && (n.maxLineLength = t, n.maxLine = e);
            });
        }
        var jt = function(e, t, n) {
            this.text = e, Ft(this, t), this.height = n ? n(this) : 1;
        };
        jt.prototype.lineNo = function() {
            return Je(this);
        }, Le(jt);
        var _t = {}, $t = {};
        function Xt(e, t) {
            if (!e || /^\s*$/.test(e)) return null;
            t = t.addModeClass ? $t : _t;
            return t[e] || (t[e] = e.replace(/\S+/g, "cm-$&"));
        }
        function Yt(e, t) {
            var n = N("span", null, null, d ? "padding-right: .1px" : null), r = {
                pre: N("pre", [ n ], "CodeMirror-line"),
                content: n,
                col: 0,
                pos: 0,
                cm: e,
                trailingSpace: !1,
                splitSpaces: e.getOption("lineWrapping")
            };
            t.measure = {};
            for (var i = 0; i <= (t.rest ? t.rest.length : 0); i++) {
                var o = i ? t.rest[i - 1] : t.line, l = void 0;
                r.pos = 0, r.addToken = Qt, function(e) {
                    if (null != We) return We;
                    var t = M(e, document.createTextNode("A\u062eA")), n = S(t, 0, 1).getBoundingClientRect(), t = S(t, 1, 2).getBoundingClientRect();
                    return L(e), n && n.left != n.right && (We = t.right - n.right < 3);
                }(e.display.measure) && (l = me(o, e.doc.direction)) && (r.addToken = function(h, f) {
                    return function(e, t, n, r, i, o, l) {
                        n = n ? n + " cm-force-border" : "cm-force-border";
                        for (var s = e.pos, a = s + t.length; ;) {
                            for (var u = void 0, c = 0; c < f.length && !((u = f[c]).to > s && u.from <= s); c++) ;
                            if (u.to >= a) return h(e, t, n, r, i, o, l);
                            h(e, t.slice(0, u.to - s), n, r, null, o, l), r = null, t = t.slice(u.to - s), s = u.to;
                        }
                    };
                }(r.addToken, l)), r.map = [], function(e, t, n) {
                    var r = e.markedSpans, i = e.text, o = 0;
                    if (!r) {
                        for (var l = 1; l < n.length; l += 2) t.addToken(t, i.slice(o, o = n[l]), Xt(n[l + 1], t.cm.options));
                        return;
                    }
                    for (var s, a, u, c, h, f, d, p = i.length, g = 0, m = 1, v = "", y = 0; ;) {
                        if (y == g) {
                            u = c = h = a = "", f = d = null, y = 1 / 0;
                            for (var b = [], w = void 0, x = 0; x < r.length; ++x) {
                                var C = r[x], S = C.marker;
                                if ("bookmark" == S.type && C.from == g && S.widgetNode) b.push(S); else if (C.from <= g && (null == C.to || C.to > g || S.collapsed && C.to == g && C.from == g)) {
                                    if (null != C.to && C.to != g && y > C.to && (y = C.to, c = ""), S.className && (u += " " + S.className), 
                                    S.css && (a = (a ? a + ";" : "") + S.css), S.startStyle && C.from == g && (h += " " + S.startStyle), 
                                    S.endStyle && C.to == y && (w = w || []).push(S.endStyle, C.to), S.title && ((d = d || {}).title = S.title), 
                                    S.attributes) for (var k in S.attributes) (d = d || {})[k] = S.attributes[k];
                                    S.collapsed && (!f || Pt(f.marker, S) < 0) && (f = C);
                                } else C.from > g && y > C.from && (y = C.from);
                            }
                            if (w) for (var L = 0; L < w.length; L += 2) w[L + 1] == y && (c += " " + w[L]);
                            if (!f || f.from == g) for (var M = 0; M < b.length; ++M) Jt(t, 0, b[M]);
                            if (f && (f.from || 0) == g) {
                                if (Jt(t, (null == f.to ? p + 1 : f.to) - g, f.marker, null == f.from), null == f.to) return;
                                f.to == g && (f = !1);
                            }
                        }
                        if (p <= g) break;
                        for (var T = Math.min(p, y); ;) {
                            if (v) {
                                var N, O = g + v.length;
                                if (f || (N = T < O ? v.slice(0, T - g) : v, t.addToken(t, N, s ? s + u : u, h, g + N.length == y ? c : "", a, d)), 
                                T <= O) {
                                    v = v.slice(T - g), g = T;
                                    break;
                                }
                                g = O, h = "";
                            }
                            v = i.slice(o, o = n[m++]), s = Xt(n[m++], t.cm.options);
                        }
                    }
                }(o, r, gt(e, o, t != e.display.externalMeasured && Je(o))), o.styleClasses && (o.styleClasses.bgClass && (r.bgClass = D(o.styleClasses.bgClass, r.bgClass || "")), 
                o.styleClasses.textClass && (r.textClass = D(o.styleClasses.textClass, r.textClass || ""))), 
                0 == r.map.length && r.map.push(0, 0, r.content.appendChild(function(e) {
                    null == De && (t = T("span", "\u200b"), M(e, T("span", [ t, document.createTextNode("x") ])), 
                    0 != e.firstChild.offsetHeight && (De = t.offsetWidth <= 1 && 2 < t.offsetHeight && !(v && y < 8)));
                    var t = De ? T("span", "\u200b") : T("span", "\xa0", null, "display: inline-block; width: 1px; margin-right: -1px");
                    return t.setAttribute("cm-text", ""), t;
                }(e.display.measure))), 0 == i ? (t.measure.map = r.map, t.measure.cache = {}) : ((t.measure.maps || (t.measure.maps = [])).push(r.map), 
                (t.measure.caches || (t.measure.caches = [])).push({}));
            }
            return d && (n = r.content.lastChild, (/\bcm-tab\b/.test(n.className) || n.querySelector && n.querySelector(".cm-tab")) && (r.content.className = "cm-tab-wrap-hack")), 
            xe(e, "renderLine", e, t.line, r.pre), r.pre.className && (r.textClass = D(r.pre.className, r.textClass || "")), 
            r;
        }
        function Zt(e) {
            var t = T("span", "\u2022", "cm-invalidchar");
            return t.title = "\\u" + e.charCodeAt(0).toString(16), t.setAttribute("aria-label", t.title), 
            t;
        }
        function Qt(e, t, n, r, i, o, l) {
            if (t) {
                var s, a = e.splitSpaces ? function(e, t) {
                    if (1 < e.length && !/  /.test(e)) return e;
                    for (var n = t, r = "", i = 0; i < e.length; i++) {
                        var o = e.charAt(i);
                        " " != o || !n || i != e.length - 1 && 32 != e.charCodeAt(i + 1) || (o = "\xa0"), 
                        r += o, n = " " == o;
                    }
                    return r;
                }(t, e.trailingSpace) : t, u = e.cm.state.specialChars, c = !1;
                if (u.test(t)) {
                    s = document.createDocumentFragment();
                    for (var h = 0; ;) {
                        u.lastIndex = h;
                        var f = u.exec(t), d = f ? f.index - h : t.length - h;
                        if (d && (p = document.createTextNode(a.slice(h, h + d)), v && y < 9 ? s.appendChild(T("span", [ p ])) : s.appendChild(p), 
                        e.map.push(e.pos, e.pos + d, p), e.col += d, e.pos += d), !f) break;
                        h += 1 + d;
                        var p = void 0;
                        "\t" == f[0] ? (d = (d = e.cm.options.tabSize) - e.col % d, (p = s.appendChild(T("span", j(d), "cm-tab"))).setAttribute("role", "presentation"), 
                        p.setAttribute("cm-text", "\t"), e.col += d) : ("\r" == f[0] || "\n" == f[0] ? (p = s.appendChild(T("span", "\r" == f[0] ? "\u240d" : "\u2424", "cm-invalidchar"))).setAttribute("cm-text", f[0]) : ((p = e.cm.options.specialCharPlaceholder(f[0])).setAttribute("cm-text", f[0]), 
                        v && y < 9 ? s.appendChild(T("span", [ p ])) : s.appendChild(p)), e.col += 1), e.map.push(e.pos, e.pos + 1, p), 
                        e.pos++;
                    }
                } else e.col += t.length, s = document.createTextNode(a), e.map.push(e.pos, e.pos + t.length, s), 
                v && y < 9 && (c = !0), e.pos += t.length;
                if (e.trailingSpace = 32 == a.charCodeAt(t.length - 1), n || r || i || c || o) {
                    n = n || "";
                    r && (n += r), i && (n += i);
                    var g = T("span", [ s ], n, o);
                    if (l) for (var m in l) l.hasOwnProperty(m) && "style" != m && "class" != m && g.setAttribute(m, l[m]);
                    return e.content.appendChild(g);
                }
                e.content.appendChild(s);
            }
        }
        function Jt(e, t, n, r) {
            var i = !r && n.widgetNode;
            i && e.map.push(e.pos, e.pos + t, i), !r && e.cm.display.input.needsContentAttribute && (i = i || e.content.appendChild(document.createElement("span"))).setAttribute("cm-marker", n.id), 
            i && (e.cm.display.input.setUneditable(i), e.content.appendChild(i)), e.pos += t, 
            e.trailingSpace = !1;
        }
        function en(e, t, n) {
            this.line = t, this.rest = function(e) {
                for (var t, n; t = Et(e); ) e = t.find(1, !0).line, (n = n || []).push(e);
                return n;
            }(t), this.size = this.rest ? Je(_(this.rest)) - n + 1 : 1, this.node = this.text = null, 
            this.hidden = Kt(e, t);
        }
        function tn(e, t, n) {
            for (var r = [], i = t; i < n; i = l) {
                var o = new en(e.doc, Xe(e.doc, i), i), l = i + o.size;
                r.push(o);
            }
            return r;
        }
        var nn = null;
        function rn(e, t) {
            var n = e.ownsGroup;
            if (n) try {
                !function(e) {
                    var t = e.delayedCallbacks, n = 0;
                    do {
                        for (;n < t.length; n++) t[n].call(null);
                        for (var r = 0; r < e.ops.length; r++) {
                            var i = e.ops[r];
                            if (i.cursorActivityHandlers) for (;i.cursorActivityCalled < i.cursorActivityHandlers.length; ) i.cursorActivityHandlers[i.cursorActivityCalled++].call(null, i.cm);
                        }
                    } while (n < t.length);
                }(n);
            } finally {
                nn = null, t(n);
            }
        }
        var on = null;
        function ln(e, t) {
            var n = be(e, t);
            if (n.length) {
                var r, i = Array.prototype.slice.call(arguments, 2);
                nn ? r = nn.delayedCallbacks : on ? r = on : (r = on = [], setTimeout(sn, 0));
                for (var o = 0; o < n.length; ++o) !function(e) {
                    r.push(function() {
                        return n[e].apply(null, i);
                    });
                }(o);
            }
        }
        function sn() {
            var e = on;
            on = null;
            for (var t = 0; t < e.length; ++t) e[t]();
        }
        function an(e, t, n, r) {
            for (var i = 0; i < t.changes.length; i++) {
                var o = t.changes[i];
                "text" == o ? function(e, t) {
                    var n = t.text.className, r = cn(e, t);
                    t.text == t.node && (t.node = r.pre);
                    t.text.parentNode.replaceChild(r.pre, t.text), t.text = r.pre, r.bgClass != t.bgClass || r.textClass != t.textClass ? (t.bgClass = r.bgClass, 
                    t.textClass = r.textClass, hn(e, t)) : n && (t.text.className = n);
                }(e, t) : "gutter" == o ? fn(e, t, n, r) : "class" == o ? hn(e, t) : "widget" == o && function(e, t, n) {
                    t.alignable && (t.alignable = null);
                    for (var r = t.node.firstChild, i = void 0; r; r = i) i = r.nextSibling, "CodeMirror-linewidget" == r.className && t.node.removeChild(r);
                    dn(e, t, n);
                }(e, t, r);
            }
            t.changes = null;
        }
        function un(e) {
            return e.node == e.text && (e.node = T("div", null, null, "position: relative"), 
            e.text.parentNode && e.text.parentNode.replaceChild(e.node, e.text), e.node.appendChild(e.text), 
            v && y < 8 && (e.node.style.zIndex = 2)), e.node;
        }
        function cn(e, t) {
            var n = e.display.externalMeasured;
            return n && n.line == t.line ? (e.display.externalMeasured = null, t.measure = n.measure, 
            n.built) : Yt(e, t);
        }
        function hn(e, t) {
            var n, r;
            n = e, (r = (i = t).bgClass ? i.bgClass + " " + (i.line.bgClass || "") : i.line.bgClass) && (r += " CodeMirror-linebackground"), 
            i.background ? r ? i.background.className = r : (i.background.parentNode.removeChild(i.background), 
            i.background = null) : r && (e = un(i), i.background = e.insertBefore(T("div", null, r), e.firstChild), 
            n.display.input.setUneditable(i.background)), t.line.wrapClass ? un(t).className = t.line.wrapClass : t.node != t.text && (t.node.className = "");
            var i = t.textClass ? t.textClass + " " + (t.line.textClass || "") : t.line.textClass;
            t.text.className = i || "";
        }
        function fn(e, t, n, r) {
            t.gutter && (t.node.removeChild(t.gutter), t.gutter = null), t.gutterBackground && (t.node.removeChild(t.gutterBackground), 
            t.gutterBackground = null), t.line.gutterClass && (o = un(t), t.gutterBackground = T("div", null, "CodeMirror-gutter-background " + t.line.gutterClass, "left: " + (e.options.fixedGutter ? r.fixedPos : -r.gutterTotalWidth) + "px; width: " + r.gutterTotalWidth + "px"), 
            e.display.input.setUneditable(t.gutterBackground), o.insertBefore(t.gutterBackground, t.text));
            var i = t.line.gutterMarkers;
            if (e.options.lineNumbers || i) {
                var o = un(t), l = t.gutter = T("div", null, "CodeMirror-gutter-wrapper", "left: " + (e.options.fixedGutter ? r.fixedPos : -r.gutterTotalWidth) + "px");
                if (e.display.input.setUneditable(l), o.insertBefore(l, t.text), t.line.gutterClass && (l.className += " " + t.line.gutterClass), 
                !e.options.lineNumbers || i && i["CodeMirror-linenumbers"] || (t.lineNumber = l.appendChild(T("div", nt(e.options, n), "CodeMirror-linenumber CodeMirror-gutter-elt", "left: " + r.gutterLeft["CodeMirror-linenumbers"] + "px; width: " + e.display.lineNumInnerWidth + "px"))), 
                i) for (var s = 0; s < e.display.gutterSpecs.length; ++s) {
                    var a = e.display.gutterSpecs[s].className, u = i.hasOwnProperty(a) && i[a];
                    u && l.appendChild(T("div", [ u ], "CodeMirror-gutter-elt", "left: " + r.gutterLeft[a] + "px; width: " + r.gutterWidth[a] + "px"));
                }
            }
        }
        function dn(e, t, n) {
            if (pn(e, t.line, t, n, !0), t.rest) for (var r = 0; r < t.rest.length; r++) pn(e, t.rest[r], t, n, !1);
        }
        function pn(e, t, n, r, i) {
            if (t.widgets) for (var o = un(n), l = 0, s = t.widgets; l < s.length; ++l) {
                var a = s[l], u = T("div", [ a.node ], "CodeMirror-linewidget");
                a.handleMouseEvents || u.setAttribute("cm-ignore-events", "true"), function(e, t, n, r) {
                    {
                        e.noHScroll && ((n.alignable || (n.alignable = [])).push(t), n = r.wrapperWidth, 
                        t.style.left = r.fixedPos + "px", e.coverGutter || (n -= r.gutterTotalWidth, t.style.paddingLeft = r.gutterTotalWidth + "px"), 
                        t.style.width = n + "px");
                    }
                    e.coverGutter && (t.style.zIndex = 5, t.style.position = "relative", e.noHScroll || (t.style.marginLeft = -r.gutterTotalWidth + "px"));
                }(a, u, n, r), e.display.input.setUneditable(u), i && a.above ? o.insertBefore(u, n.gutter || n.text) : o.appendChild(u), 
                ln(a, "redraw");
            }
        }
        function gn(e) {
            if (null != e.height) return e.height;
            var t, n = e.doc.cm;
            return n ? (O(document.body, e.node) || (t = "position: relative;", e.coverGutter && (t += "margin-left: -" + n.display.gutters.offsetWidth + "px;"), 
            e.noHScroll && (t += "width: " + n.display.wrapper.clientWidth + "px;"), M(n.display.measure, T("div", [ e.node ], null, t))), 
            e.height = e.node.parentNode.offsetHeight) : 0;
        }
        function mn(e, t) {
            for (var n = Ae(t); n != e.wrapper; n = n.parentNode) if (!n || 1 == n.nodeType && "true" == n.getAttribute("cm-ignore-events") || n.parentNode == e.sizer && n != e.mover) return 1;
        }
        function vn(e) {
            return e.lineSpace.offsetTop;
        }
        function yn(e) {
            return e.mover.offsetHeight - e.lineSpace.offsetHeight;
        }
        function bn(e) {
            if (e.cachedPaddingH) return e.cachedPaddingH;
            var t = M(e.measure, T("pre", "x")), t = window.getComputedStyle ? window.getComputedStyle(t) : t.currentStyle, t = {
                left: parseInt(t.paddingLeft),
                right: parseInt(t.paddingRight)
            };
            return isNaN(t.left) || isNaN(t.right) || (e.cachedPaddingH = t), t;
        }
        function wn(e) {
            return z - e.display.nativeBarWidth;
        }
        function xn(e) {
            return e.display.scroller.clientWidth - wn(e) - e.display.barWidth;
        }
        function Cn(e) {
            return e.display.scroller.clientHeight - wn(e) - e.display.barHeight;
        }
        function Sn(e, t, n) {
            if (e.line == t) return {
                map: e.measure.map,
                cache: e.measure.cache
            };
            for (var r = 0; r < e.rest.length; r++) if (e.rest[r] == t) return {
                map: e.measure.maps[r],
                cache: e.measure.caches[r]
            };
            for (var i = 0; i < e.rest.length; i++) if (Je(e.rest[i]) > n) return {
                map: e.measure.maps[i],
                cache: e.measure.caches[i],
                before: !0
            };
        }
        function kn(e, t, n, r) {
            return Tn(e, Mn(e, t), n, r);
        }
        function Ln(e, t) {
            if (t >= e.display.viewFrom && t < e.display.viewTo) return e.display.view[er(e, t)];
            e = e.display.externalMeasured;
            return e && t >= e.lineN && t < e.lineN + e.size ? e : void 0;
        }
        function Mn(e, t) {
            var n, r, i = Je(t), o = Ln(e, i);
            o && !o.text ? o = null : o && o.changes && (an(e, o, i, Xn(e)), e.curOp.forceUpdate = !0), 
            o || (n = e, e = Je(r = zt(r = t)), (r = n.display.externalMeasured = new en(n.doc, r, e)).lineN = e, 
            e = r.built = Yt(n, r), r.text = e.pre, M(n.display.lineMeasure, e.pre), o = r);
            i = Sn(o, t, i);
            return {
                line: t,
                view: o,
                rect: null,
                map: i.map,
                cache: i.cache,
                before: i.before,
                hasHeights: !1
            };
        }
        function Tn(e, t, n, r, i) {
            t.before && (n = -1);
            var o, l = n + (r || "");
            return t.cache.hasOwnProperty(l) ? o = t.cache[l] : (t.rect || (t.rect = t.view.text.getBoundingClientRect()), 
            t.hasHeights || (function(e, t, n) {
                var r = e.options.lineWrapping, e = r && xn(e);
                if (!t.measure.heights || r && t.measure.width != e) {
                    var i = t.measure.heights = [];
                    if (r) {
                        t.measure.width = e;
                        for (var o = t.text.firstChild.getClientRects(), l = 0; l < o.length - 1; l++) {
                            var s = o[l], a = o[l + 1];
                            2 < Math.abs(s.bottom - a.bottom) && i.push((s.bottom + a.top) / 2 - n.top);
                        }
                    }
                    i.push(n.bottom - n.top);
                }
            }(e, t.view, t.rect), t.hasHeights = !0), (o = function(e, t, n, r) {
                var i, o = An(t.map, n, r), l = o.node, s = o.start, a = o.end, u = o.collapse;
                if (3 == l.nodeType) {
                    for (var c = 0; c < 4; c++) {
                        for (;s && ne(t.line.text.charAt(o.coverStart + s)); ) --s;
                        for (;o.coverStart + a < o.coverEnd && ne(t.line.text.charAt(o.coverStart + a)); ) ++a;
                        if ((i = v && y < 9 && 0 == s && a == o.coverEnd - o.coverStart ? l.parentNode.getBoundingClientRect() : function(e, t) {
                            var n = On;
                            if ("left" == t) for (var r = 0; r < e.length && (n = e[r]).left == n.right; r++) ; else for (var i = e.length - 1; 0 <= i && (n = e[i]).left == n.right; i--) ;
                            return n;
                        }(S(l, s, a).getClientRects(), r)).left || i.right || 0 == s) break;
                        a = s, --s, u = "right";
                    }
                    v && y < 11 && (i = function(e, t) {
                        if (!window.screen || null == screen.logicalXDPI || screen.logicalXDPI == screen.deviceXDPI || !function(e) {
                            if (null != Re) return Re;
                            var e = (t = M(e, T("span", "x"))).getBoundingClientRect(), t = S(t, 0, 1).getBoundingClientRect();
                            return Re = 1 < Math.abs(e.left - t.left);
                        }(e)) return t;
                        var n = screen.logicalXDPI / screen.deviceXDPI, e = screen.logicalYDPI / screen.deviceYDPI;
                        return {
                            left: t.left * n,
                            right: t.right * n,
                            top: t.top * e,
                            bottom: t.bottom * e
                        };
                    }(e.display.measure, i));
                } else 0 < s && (u = r = "right"), i = e.options.lineWrapping && 1 < (g = l.getClientRects()).length ? g["right" == r ? g.length - 1 : 0] : l.getBoundingClientRect();
                {
                    !(v && y < 9) || s || i && (i.left || i.right) || (m = l.parentNode.getClientRects()[0], 
                    i = m ? {
                        left: m.left,
                        right: m.left + $n(e.display),
                        top: m.top,
                        bottom: m.bottom
                    } : On);
                }
                for (var h = i.top - t.rect.top, n = i.bottom - t.rect.top, f = (h + n) / 2, d = t.view.measure.heights, p = 0; p < d.length - 1 && !(f < d[p]); p++) ;
                var g = p ? d[p - 1] : 0, m = d[p], m = {
                    left: ("right" == u ? i.right : i.left) - t.rect.left,
                    right: ("left" == u ? i.left : i.right) - t.rect.left,
                    top: g,
                    bottom: m
                };
                i.left || i.right || (m.bogus = !0);
                e.options.singleCursorHeightPerLine || (m.rtop = h, m.rbottom = n);
                return m;
            }(e, t, n, r)).bogus || (t.cache[l] = o)), {
                left: o.left,
                right: o.right,
                top: i ? o.rtop : o.top,
                bottom: i ? o.rbottom : o.bottom
            };
        }
        var Nn, On = {
            left: 0,
            right: 0,
            top: 0,
            bottom: 0
        };
        function An(e, t, n) {
            for (var r, i, o, l, s, a, u = 0; u < e.length; u += 3) if (s = e[u], a = e[u + 1], 
            t < s ? (i = 0, o = 1, l = "left") : t < a ? o = (i = t - s) + 1 : (u == e.length - 3 || t == a && e[u + 3] > t) && (i = (o = a - s) - 1, 
            a <= t && (l = "right")), null != i) {
                if (r = e[u + 2], s == a && n == (r.insertLeft ? "left" : "right") && (l = n), "left" == n && 0 == i) for (;u && e[u - 2] == e[u - 3] && e[u - 1].insertLeft; ) r = e[2 + (u -= 3)], 
                l = "left";
                if ("right" == n && i == a - s) for (;u < e.length - 3 && e[u + 3] == e[u + 4] && !e[u + 5].insertLeft; ) r = e[(u += 3) + 2], 
                l = "right";
                break;
            }
            return {
                node: r,
                start: i,
                end: o,
                collapse: l,
                coverStart: s,
                coverEnd: a
            };
        }
        function Fn(e) {
            if (e.measure && (e.measure.cache = {}, e.measure.heights = null, e.rest)) for (var t = 0; t < e.rest.length; t++) e.measure.caches[t] = {};
        }
        function Dn(e) {
            e.display.externalMeasure = null, L(e.display.lineMeasure);
            for (var t = 0; t < e.display.view.length; t++) Fn(e.display.view[t]);
        }
        function Wn(e) {
            Dn(e), e.display.cachedCharWidth = e.display.cachedTextHeight = e.display.cachedPaddingH = null, 
            e.options.lineWrapping || (e.display.maxLineChanged = !0), e.display.lineNumChars = null;
        }
        function Pn() {
            return o && c ? -(document.body.getBoundingClientRect().left - parseInt(getComputedStyle(document.body).marginLeft)) : window.pageXOffset || (document.documentElement || document.body).scrollLeft;
        }
        function Hn() {
            return o && c ? -(document.body.getBoundingClientRect().top - parseInt(getComputedStyle(document.body).marginTop)) : window.pageYOffset || (document.documentElement || document.body).scrollTop;
        }
        function In(e) {
            var t = 0;
            if (e.widgets) for (var n = 0; n < e.widgets.length; ++n) e.widgets[n].above && (t += gn(e.widgets[n]));
            return t;
        }
        function En(e, t, n, r, i) {
            if (i || (i = In(t), n.top += i, n.bottom += i), "line" == r) return n;
            r = r || "local";
            t = Gt(t);
            return "local" == r ? t += vn(e.display) : t -= e.display.viewOffset, "page" != r && "window" != r || (t += (e = e.display.lineSpace.getBoundingClientRect()).top + ("window" == r ? 0 : Hn()), 
            r = e.left + ("window" == r ? 0 : Pn()), n.left += r, n.right += r), n.top += t, 
            n.bottom += t, n;
        }
        function Rn(e, t, n) {
            if ("div" == n) return t;
            var r = t.left, t = t.top;
            "page" == n ? (r -= Pn(), t -= Hn()) : "local" != n && n || (r += (n = e.display.sizer.getBoundingClientRect()).left, 
            t += n.top);
            e = e.display.lineSpace.getBoundingClientRect();
            return {
                left: r - e.left,
                top: t - e.top
            };
        }
        function zn(e, t, n, r, i) {
            return En(e, r = r || Xe(e.doc, t.line), kn(e, r, t.ch, i), n);
        }
        function Bn(n, e, r, i, o, l) {
            function s(e, t) {
                e = Tn(n, o, e, t ? "right" : "left", l);
                return t ? e.left = e.right : e.right = e.left, En(n, i, e, r);
            }
            i = i || Xe(n.doc, e.line), o = o || Mn(n, i);
            var a = me(i, n.doc.direction), t = e.ch, u = e.sticky;
            if (t >= i.text.length ? (t = i.text.length, u = "before") : t <= 0 && (t = 0, u = "after"), 
            !a) return s("before" == u ? t - 1 : t, "before" == u);
            function c(e, t, n) {
                return s(n ? e - 1 : e, 1 == a[t].level != n);
            }
            var h = le(a, t, u), e = oe, h = c(t, h, "before" == u);
            return null != e && (h.other = c(t, e, "before" != u)), h;
        }
        function Un(e, t) {
            var n = 0;
            t = ct(e.doc, t), e.options.lineWrapping || (n = $n(e.display) * t.ch);
            t = Xe(e.doc, t.line), e = Gt(t) + vn(e.display);
            return {
                left: n,
                right: n,
                top: e,
                bottom: e + t.height
            };
        }
        function Kn(e, t, n, r, i) {
            n = rt(e, t, n);
            return n.xRel = i, r && (n.outside = !0), n;
        }
        function Gn(e, t, n) {
            var r = e.doc;
            if ((n += e.display.viewOffset) < 0) return Kn(r.first, 0, null, !0, -1);
            var i = et(r, n), o = r.first + r.size - 1;
            if (o < i) return Kn(r.first + r.size - 1, Xe(r, o).text.length, null, !0, 1);
            t < 0 && (t = 0);
            for (var l = Xe(r, i); ;) {
                var s = function(n, e, t, r, i) {
                    i -= Gt(e);
                    var o = Mn(n, e), l = In(e), s = 0, a = e.text.length, u = !0, c = me(e, n.doc.direction);
                    {
                        c && (d = (n.options.lineWrapping ? function(e, t, n, r, i, o, l) {
                            var l = Vn(e, t, r, l), s = l.begin, a = l.end;
                            /\s/.test(t.text.charAt(a - 1)) && a--;
                            for (var u = null, c = null, h = 0; h < i.length; h++) {
                                var f, d = i[h];
                                d.from >= a || d.to <= s || (f = 1 != d.level, f = Tn(e, r, f ? Math.min(a, d.to) - 1 : Math.max(s, d.from)).right, 
                                f = f < o ? o - f + 1e9 : f - o, (!u || f < c) && (u = d, c = f));
                            }
                            u = u || i[i.length - 1];
                            u.from < s && (u = {
                                from: s,
                                to: u.to,
                                level: u.level
                            });
                            u.to > a && (u = {
                                from: u.from,
                                to: a,
                                level: u.level
                            });
                            return u;
                        } : function(n, r, i, o, l, s, a) {
                            var e = ie(function(e) {
                                var t = l[e], e = 1 != t.level;
                                return jn(Bn(n, rt(i, e ? t.to : t.from, e ? "before" : "after"), "line", r, o), s, a, !0);
                            }, 0, l.length - 1), t = l[e];
                            {
                                var u;
                                0 < e && (u = 1 != t.level, jn(u = Bn(n, rt(i, u ? t.from : t.to, u ? "after" : "before"), "line", r, o), s, a, !0) && u.top > a && (t = l[e - 1]));
                            }
                            return t;
                        })(n, e, t, o, c, r, i), u = 1 != d.level, s = u ? d.from : d.to - 1, a = u ? d.to : d.from - 1);
                    }
                    var h = null, f = null, c = ie(function(e) {
                        var t = Tn(n, o, e);
                        return t.top += l, t.bottom += l, jn(t, r, i, !1) && (t.top <= i && t.left <= r && (h = e, 
                        f = t), 1);
                    }, s, a), d = !1;
                    {
                        var p, g;
                        f ? (p = r - f.left < f.right - r, c = h + ((g = p == u) ? 0 : 1), g = g ? "after" : "before", 
                        p = p ? f.left : f.right) : (u || c != a && c != s || c++, g = 0 == c || c != e.text.length && Tn(n, o, c - (u ? 1 : 0)).bottom + l <= i == u ? "after" : "before", 
                        u = Bn(n, rt(t, c, g), "line", e, o), p = u.left, d = i < u.top || i >= u.bottom);
                    }
                    return c = re(e.text, c, 1), Kn(t, c, g, d, r - p);
                }(e, l, i, t, n), a = function(e, t) {
                    var n, r = Lt && e.markedSpans;
                    if (r) for (var i = 0; i < r.length; ++i) {
                        var o = r[i];
                        o.marker.collapsed && (null == o.from || o.from < t) && (null == o.to || o.to > t) && (!n || Pt(n, o.marker) < 0) && (n = o.marker);
                    }
                    return n;
                }(l, s.ch + (0 < s.xRel ? 1 : 0));
                if (!a) return s;
                a = a.find(1);
                if (a.line == i) return a;
                l = Xe(r, i = a.line);
            }
        }
        function Vn(t, e, n, r) {
            r -= In(e);
            var i = e.text.length, e = ie(function(e) {
                return Tn(t, n, e - 1).bottom <= r;
            }, i, 0);
            return {
                begin: e,
                end: i = ie(function(e) {
                    return Tn(t, n, e).top > r;
                }, e, i)
            };
        }
        function qn(e, t, n, r) {
            return Vn(e, t, n = n || Mn(e, t), En(e, t, Tn(e, n, r), "line").top);
        }
        function jn(e, t, n, r) {
            return !(e.bottom <= n) && (e.top > n || (r ? e.left : e.right) > t);
        }
        function _n(e) {
            if (null != e.cachedTextHeight) return e.cachedTextHeight;
            if (null == Nn) {
                Nn = T("pre");
                for (var t = 0; t < 49; ++t) Nn.appendChild(document.createTextNode("x")), Nn.appendChild(T("br"));
                Nn.appendChild(document.createTextNode("x"));
            }
            M(e.measure, Nn);
            var n = Nn.offsetHeight / 50;
            return 3 < n && (e.cachedTextHeight = n), L(e.measure), n || 1;
        }
        function $n(e) {
            if (null != e.cachedCharWidth) return e.cachedCharWidth;
            var t = T("span", "xxxxxxxxxx"), n = T("pre", [ t ]);
            M(e.measure, n);
            t = t.getBoundingClientRect(), t = (t.right - t.left) / 10;
            return 2 < t && (e.cachedCharWidth = t), t || 10;
        }
        function Xn(e) {
            for (var t = e.display, n = {}, r = {}, i = t.gutters.clientLeft, o = t.gutters.firstChild, l = 0; o; o = o.nextSibling, 
            ++l) {
                var s = e.display.gutterSpecs[l].className;
                n[s] = o.offsetLeft + o.clientLeft + i, r[s] = o.clientWidth;
            }
            return {
                fixedPos: Yn(t),
                gutterTotalWidth: t.gutters.offsetWidth,
                gutterLeft: n,
                gutterWidth: r,
                wrapperWidth: t.wrapper.clientWidth
            };
        }
        function Yn(e) {
            return e.scroller.getBoundingClientRect().left - e.sizer.getBoundingClientRect().left;
        }
        function Zn(r) {
            var i = _n(r.display), o = r.options.lineWrapping, l = o && Math.max(5, r.display.scroller.clientWidth / $n(r.display) - 3);
            return function(e) {
                if (Kt(r.doc, e)) return 0;
                var t = 0;
                if (e.widgets) for (var n = 0; n < e.widgets.length; n++) e.widgets[n].height && (t += e.widgets[n].height);
                return o ? t + (Math.ceil(e.text.length / l) || 1) * i : t + i;
            };
        }
        function Qn(e) {
            var t = e.doc, n = Zn(e);
            t.iter(function(e) {
                var t = n(e);
                t != e.height && Qe(e, t);
            });
        }
        function Jn(e, t, n, r) {
            var i = e.display;
            if (!n && "true" == Ae(t).getAttribute("cm-not-content")) return null;
            var o, l, s = i.lineSpace.getBoundingClientRect();
            try {
                o = t.clientX - s.left, l = t.clientY - s.top;
            } catch (t) {
                return null;
            }
            var a, i = Gn(e, o, l);
            return r && 1 == i.xRel && (a = Xe(e.doc, i.line).text).length == i.ch && (a = I(a, a.length, e.options.tabSize) - a.length, 
            i = rt(i.line, Math.max(0, Math.round((o - bn(e.display).left) / $n(e.display)) - a))), 
            i;
        }
        function er(e, t) {
            if (t >= e.display.viewTo) return null;
            if ((t -= e.display.viewFrom) < 0) return null;
            for (var n = e.display.view, r = 0; r < n.length; r++) if ((t -= n[r].size) < 0) return r;
        }
        function tr(e, t, n, r) {
            null == t && (t = e.doc.first), null == n && (n = e.doc.first + e.doc.size), r = r || 0;
            var i, o, l = e.display;
            r && n < l.viewTo && (null == l.updateLineNumbers || l.updateLineNumbers > t) && (l.updateLineNumbers = t), 
            e.curOp.viewChanged = !0, t >= l.viewTo ? Lt && Bt(e.doc, t) < l.viewTo && rr(e) : n <= l.viewFrom ? Lt && Ut(e.doc, n + r) > l.viewFrom ? rr(e) : (l.viewFrom += r, 
            l.viewTo += r) : t <= l.viewFrom && n >= l.viewTo ? rr(e) : t <= l.viewFrom ? (i = ir(e, n, n + r, 1)) ? (l.view = l.view.slice(i.index), 
            l.viewFrom = i.lineN, l.viewTo += r) : rr(e) : n >= l.viewTo ? (o = ir(e, t, t, -1)) ? (l.view = l.view.slice(0, o.index), 
            l.viewTo = o.lineN) : rr(e) : (i = ir(e, t, t, -1), o = ir(e, n, n + r, 1), i && o ? (l.view = l.view.slice(0, i.index).concat(tn(e, i.lineN, o.lineN)).concat(l.view.slice(o.index)), 
            l.viewTo += r) : rr(e));
            e = l.externalMeasured;
            e && (n < e.lineN ? e.lineN += r : t < e.lineN + e.size && (l.externalMeasured = null));
        }
        function nr(e, t, n) {
            e.curOp.viewChanged = !0;
            var r = e.display, i = e.display.externalMeasured;
            i && t >= i.lineN && t < i.lineN + i.size && (r.externalMeasured = null), t < r.viewFrom || t >= r.viewTo || (null == (t = r.view[er(e, t)]).node || -1 == R(t = t.changes || (t.changes = []), n) && t.push(n));
        }
        function rr(e) {
            e.display.viewFrom = e.display.viewTo = e.doc.first, e.display.view = [], e.display.viewOffset = 0;
        }
        function ir(e, t, n, r) {
            var i, o = er(e, t), l = e.display.view;
            if (!Lt || n == e.doc.first + e.doc.size) return {
                index: o,
                lineN: n
            };
            for (var s = e.display.viewFrom, a = 0; a < o; a++) s += l[a].size;
            if (s != t) {
                if (0 < r) {
                    if (o == l.length - 1) return null;
                    i = s + l[o].size - t, o++;
                } else i = s - t;
                t += i, n += i;
            }
            for (;Bt(e.doc, n) != n; ) {
                if (o == (r < 0 ? 0 : l.length - 1)) return null;
                n += r * l[o - (r < 0 ? 1 : 0)].size, o += r;
            }
            return {
                index: o,
                lineN: n
            };
        }
        function or(e) {
            for (var t = e.display.view, n = 0, r = 0; r < t.length; r++) {
                var i = t[r];
                i.hidden || i.node && !i.changes || ++n;
            }
            return n;
        }
        function lr(e) {
            e.display.input.showSelection(e.display.input.prepareSelection());
        }
        function sr(e, t) {
            void 0 === t && (t = !0);
            for (var n, r, i = e.doc, o = {}, l = o.cursors = document.createDocumentFragment(), s = o.selection = document.createDocumentFragment(), a = 0; a < i.sel.ranges.length; a++) {
                !t && a == i.sel.primIndex || ((n = i.sel.ranges[a]).from().line >= e.display.viewTo || n.to().line < e.display.viewFrom || (((r = n.empty()) || e.options.showCursorWhenSelecting) && ar(e, n.head, l), 
                r || function(i, e, t) {
                    var n = i.display, o = i.doc, l = document.createDocumentFragment(), r = bn(i.display), S = r.left, k = Math.max(n.sizerWidth, xn(i) - n.sizer.offsetLeft) - r.right, L = "ltr" == o.direction;
                    function M(e, t, n, r) {
                        t < 0 && (t = 0), t = Math.round(t), r = Math.round(r), l.appendChild(T("div", null, "CodeMirror-selected", "position: absolute; left: " + e + "px;\n                             top: " + t + "px; width: " + (null == n ? k - e : n) + "px;\n                             height: " + (r - t) + "px"));
                    }
                    function s(n, g, m) {
                        var v, y, r = Xe(o, n), b = r.text.length;
                        function w(e, t) {
                            return zn(i, rt(n, e), "div", r, t);
                        }
                        function x(e, t, n) {
                            e = qn(i, r, null, e), t = "ltr" == t == ("after" == n) ? "left" : "right";
                            return w("after" == n ? e.begin : e.end - (/\s/.test(r.text.charAt(e.end - 1)) ? 2 : 1), t)[t];
                        }
                        var C = me(r, o.direction);
                        return function(e, t, n, r) {
                            if (!e) return r(t, n, "ltr", 0);
                            for (var i = !1, o = 0; o < e.length; ++o) {
                                var l = e[o];
                                (l.from < n && l.to > t || t == n && l.to == t) && (r(Math.max(l.from, t), Math.min(l.to, n), 1 == l.level ? "rtl" : "ltr", o), 
                                i = !0);
                            }
                            i || r(t, n, "ltr");
                        }(C, g || 0, null == m ? b : m, function(e, t, n, r) {
                            var i, o, l, s, a = "ltr" == n, u = w(e, a ? "left" : "right"), c = w(t - 1, a ? "right" : "left"), h = null == g && 0 == e, f = null == m && t == b, d = 0 == r, p = !C || r == C.length - 1;
                            c.top - u.top <= 3 ? (i = (L ? f : h) && p, r = (L ? h : f) && d ? S : (a ? u : c).left, 
                            i = i ? k : (a ? c : u).right, M(r, u.top, i - r, u.bottom)) : (n = a ? (o = L && h && d ? S : u.left, 
                            l = L ? k : x(e, n, "before"), s = L ? S : x(t, n, "after"), L && f && p ? k : c.right) : (o = L ? x(e, n, "before") : S, 
                            l = !L && h && d ? k : u.right, s = !L && f && p ? S : c.left, L ? x(t, n, "after") : k), 
                            M(o, u.top, l - o, u.bottom), u.bottom < c.top && M(S, u.bottom, null, c.top), M(s, c.top, n - s, c.bottom)), 
                            (!v || ur(u, v) < 0) && (v = u), ur(c, v) < 0 && (v = c), (!y || ur(u, y) < 0) && (y = u), 
                            ur(c, y) < 0 && (y = c);
                        }), {
                            start: v,
                            end: y
                        };
                    }
                    var a = e.from(), n = e.to();
                    {
                        a.line == n.line ? s(a.line, a.ch, n.ch) : (r = Xe(o, a.line), e = Xe(o, n.line), 
                        e = zt(r) == zt(e), r = s(a.line, a.ch, e ? r.text.length + 1 : null).end, n = s(n.line, e ? 0 : null, n.ch).start, 
                        e && (r.top < n.top - 2 ? (M(r.right, r.top, null, r.bottom), M(S, n.top, n.left, n.bottom)) : M(r.right, r.top, n.left - r.right, r.bottom)), 
                        r.bottom < n.top && M(S, r.bottom, null, n.top));
                    }
                    t.appendChild(l);
                }(e, n, s)));
            }
            return o;
        }
        function ar(e, t, n) {
            var r = Bn(e, t, "div", null, null, !e.options.singleCursorHeightPerLine), t = n.appendChild(T("div", "\xa0", "CodeMirror-cursor"));
            t.style.left = r.left + "px", t.style.top = r.top + "px", t.style.height = Math.max(0, r.bottom - r.top) * e.options.cursorHeight + "px", 
            r.other && ((n = n.appendChild(T("div", "\xa0", "CodeMirror-cursor CodeMirror-secondarycursor"))).style.display = "", 
            n.style.left = r.other.left + "px", n.style.top = r.other.top + "px", n.style.height = .85 * (r.other.bottom - r.other.top) + "px");
        }
        function ur(e, t) {
            return e.top - t.top || e.left - t.left;
        }
        function cr(e) {
            var t, n;
            e.state.focused && (t = e.display, clearInterval(t.blinker), n = !0, t.cursorDiv.style.visibility = "", 
            0 < e.options.cursorBlinkRate ? t.blinker = setInterval(function() {
                return t.cursorDiv.style.visibility = (n = !n) ? "" : "hidden";
            }, e.options.cursorBlinkRate) : e.options.cursorBlinkRate < 0 && (t.cursorDiv.style.visibility = "hidden"));
        }
        function hr(e) {
            e.state.focused || (e.display.input.focus(), dr(e));
        }
        function fr(e) {
            e.state.delayingBlurEvent = !0, setTimeout(function() {
                e.state.delayingBlurEvent && (e.state.delayingBlurEvent = !1, pr(e));
            }, 100);
        }
        function dr(e, t) {
            e.state.delayingBlurEvent && (e.state.delayingBlurEvent = !1), "nocursor" != e.options.readOnly && (e.state.focused || (xe(e, "focus", e, t), 
            e.state.focused = !0, F(e.display.wrapper, "CodeMirror-focused"), e.curOp || e.display.selForContextMenu == e.doc.sel || (e.display.input.reset(), 
            d && setTimeout(function() {
                return e.display.input.reset(!0);
            }, 20)), e.display.input.receivedFocus()), cr(e));
        }
        function pr(e, t) {
            e.state.delayingBlurEvent || (e.state.focused && (xe(e, "blur", e, t), e.state.focused = !1, 
            k(e.display.wrapper, "CodeMirror-focused")), clearInterval(e.display.blinker), setTimeout(function() {
                e.state.focused || (e.display.shift = !1);
            }, 150));
        }
        function gr(e) {
            for (var t = e.display, n = t.lineDiv.offsetTop, r = 0; r < t.view.length; r++) {
                var i, o = t.view[r], l = e.options.lineWrapping, s = void 0, a = 0;
                if (!o.hidden) {
                    v && y < 8 ? (s = (i = o.node.offsetTop + o.node.offsetHeight) - n, n = i) : (s = (u = o.node.getBoundingClientRect()).bottom - u.top, 
                    !l && o.text.firstChild && (a = o.text.firstChild.getBoundingClientRect().right - u.left - 1));
                    var u = o.line.height - s;
                    if ((.005 < u || u < -.005) && (Qe(o.line, s), mr(o.line), o.rest)) for (var c = 0; c < o.rest.length; c++) mr(o.rest[c]);
                    a > e.display.sizerWidth && ((a = Math.ceil(a / $n(e.display))) > e.display.maxLineLength && (e.display.maxLineLength = a, 
                    e.display.maxLine = o.line, e.display.maxLineChanged = !0));
                }
            }
        }
        function mr(e) {
            if (e.widgets) for (var t = 0; t < e.widgets.length; ++t) {
                var n = e.widgets[t], r = n.node.parentNode;
                r && (n.height = r.offsetHeight);
            }
        }
        function vr(e, t, n) {
            var r = n && null != n.top ? Math.max(0, n.top) : e.scroller.scrollTop, r = Math.floor(r - vn(e)), i = n && null != n.bottom ? n.bottom : r + e.wrapper.clientHeight, o = et(t, r), r = et(t, i);
            return n && n.ensure && (i = n.ensure.from.line, n = n.ensure.to.line, i < o ? r = et(t, Gt(Xe(t, o = i)) + e.wrapper.clientHeight) : Math.min(n, t.lastLine()) >= r && (o = et(t, Gt(Xe(t, n)) - e.wrapper.clientHeight), 
            r = n)), {
                from: o,
                to: Math.max(r, o + 1)
            };
        }
        function yr(e, t) {
            var n = e.display, r = _n(e.display);
            t.top < 0 && (t.top = 0);
            var i = (e.curOp && null != e.curOp.scrollTop ? e.curOp : n.scroller).scrollTop, o = Cn(e), l = {};
            t.bottom - t.top > o && (t.bottom = t.top + o);
            var s = e.doc.height + yn(n), a = t.top < r, r = t.bottom > s - r;
            t.top < i ? l.scrollTop = a ? 0 : t.top : t.bottom > i + o && ((u = Math.min(t.top, (r ? s : t.bottom) - o)) != i && (l.scrollTop = u));
            var u = (e.curOp && null != e.curOp.scrollLeft ? e.curOp : n.scroller).scrollLeft, e = xn(e) - (e.options.fixedGutter ? n.gutters.offsetWidth : 0), n = t.right - t.left > e;
            return n && (t.right = t.left + e), t.left < 10 ? l.scrollLeft = 0 : t.left < u ? l.scrollLeft = Math.max(0, t.left - (n ? 0 : 10)) : t.right > e + u - 3 && (l.scrollLeft = t.right + (n ? 0 : 10) - e), 
            l;
        }
        function br(e, t) {
            null != t && (Cr(e), e.curOp.scrollTop = (null == e.curOp.scrollTop ? e.doc : e.curOp).scrollTop + t);
        }
        function wr(e) {
            Cr(e);
            var t = e.getCursor();
            e.curOp.scrollToPos = {
                from: t,
                to: t,
                margin: e.options.cursorScrollMargin
            };
        }
        function xr(e, t, n) {
            null == t && null == n || Cr(e), null != t && (e.curOp.scrollLeft = t), null != n && (e.curOp.scrollTop = n);
        }
        function Cr(e) {
            var t = e.curOp.scrollToPos;
            t && (e.curOp.scrollToPos = null, Sr(e, Un(e, t.from), Un(e, t.to), t.margin));
        }
        function Sr(e, t, n, r) {
            r = yr(e, {
                left: Math.min(t.left, n.left),
                top: Math.min(t.top, n.top) - r,
                right: Math.max(t.right, n.right),
                bottom: Math.max(t.bottom, n.bottom) + r
            });
            xr(e, r.scrollLeft, r.scrollTop);
        }
        function kr(e, t) {
            Math.abs(e.doc.scrollTop - t) < 2 || (f || Vr(e, {
                top: t
            }), Lr(e, t, !0), f && Vr(e), zr(e, 100));
        }
        function Lr(e, t, n) {
            t = Math.min(e.display.scroller.scrollHeight - e.display.scroller.clientHeight, t), 
            e.display.scroller.scrollTop == t && !n || (e.doc.scrollTop = t, e.display.scrollbars.setScrollTop(t), 
            e.display.scroller.scrollTop != t && (e.display.scroller.scrollTop = t));
        }
        function Mr(e, t, n, r) {
            t = Math.min(t, e.display.scroller.scrollWidth - e.display.scroller.clientWidth), 
            (n ? t == e.doc.scrollLeft : Math.abs(e.doc.scrollLeft - t) < 2) && !r || (e.doc.scrollLeft = t, 
            _r(e), e.display.scroller.scrollLeft != t && (e.display.scroller.scrollLeft = t), 
            e.display.scrollbars.setScrollLeft(t));
        }
        function Tr(e) {
            var t = e.display, n = t.gutters.offsetWidth, r = Math.round(e.doc.height + yn(e.display));
            return {
                clientHeight: t.scroller.clientHeight,
                viewHeight: t.wrapper.clientHeight,
                scrollWidth: t.scroller.scrollWidth,
                clientWidth: t.scroller.clientWidth,
                viewWidth: t.wrapper.clientWidth,
                barLeft: e.options.fixedGutter ? n : 0,
                docHeight: r,
                scrollHeight: r + wn(e) + t.barHeight,
                nativeBarWidth: t.nativeBarWidth,
                gutterWidth: n
            };
        }
        e = function(e, t, n) {
            this.cm = n;
            var r = this.vert = T("div", [ T("div", null, null, "min-width: 1px") ], "CodeMirror-vscrollbar"), i = this.horiz = T("div", [ T("div", null, null, "height: 100%; min-height: 1px") ], "CodeMirror-hscrollbar");
            r.tabIndex = i.tabIndex = -1, e(r), e(i), ye(r, "scroll", function() {
                r.clientHeight && t(r.scrollTop, "vertical");
            }), ye(i, "scroll", function() {
                i.clientWidth && t(i.scrollLeft, "horizontal");
            }), this.checkedZeroWidth = !1, v && y < 8 && (this.horiz.style.minHeight = this.vert.style.minWidth = "18px");
        };
        e.prototype.update = function(e) {
            var t, n = e.scrollWidth > e.clientWidth + 1, r = e.scrollHeight > e.clientHeight + 1, i = e.nativeBarWidth;
            return r ? (this.vert.style.display = "block", this.vert.style.bottom = n ? i + "px" : "0", 
            t = e.viewHeight - (n ? i : 0), this.vert.firstChild.style.height = Math.max(0, e.scrollHeight - e.clientHeight + t) + "px") : (this.vert.style.display = "", 
            this.vert.firstChild.style.height = "0"), n ? (this.horiz.style.display = "block", 
            this.horiz.style.right = r ? i + "px" : "0", this.horiz.style.left = e.barLeft + "px", 
            t = e.viewWidth - e.barLeft - (r ? i : 0), this.horiz.firstChild.style.width = Math.max(0, e.scrollWidth - e.clientWidth + t) + "px") : (this.horiz.style.display = "", 
            this.horiz.firstChild.style.width = "0"), !this.checkedZeroWidth && 0 < e.clientHeight && (0 == i && this.zeroWidthHack(), 
            this.checkedZeroWidth = !0), {
                right: r ? i : 0,
                bottom: n ? i : 0
            };
        }, e.prototype.setScrollLeft = function(e) {
            this.horiz.scrollLeft != e && (this.horiz.scrollLeft = e), this.disableHoriz && this.enableZeroWidthBar(this.horiz, this.disableHoriz, "horiz");
        }, e.prototype.setScrollTop = function(e) {
            this.vert.scrollTop != e && (this.vert.scrollTop = e), this.disableVert && this.enableZeroWidthBar(this.vert, this.disableVert, "vert");
        }, e.prototype.zeroWidthHack = function() {
            var e = g && !l ? "12px" : "18px";
            this.horiz.style.height = this.vert.style.width = e, this.horiz.style.pointerEvents = this.vert.style.pointerEvents = "none", 
            this.disableHoriz = new E(), this.disableVert = new E();
        }, e.prototype.enableZeroWidthBar = function(n, r, i) {
            n.style.pointerEvents = "auto", r.set(1e3, function e() {
                var t = n.getBoundingClientRect();
                ("vert" == i ? document.elementFromPoint(t.right - 1, (t.top + t.bottom) / 2) : document.elementFromPoint((t.right + t.left) / 2, t.bottom - 1)) != n ? n.style.pointerEvents = "none" : r.set(1e3, e);
            });
        }, e.prototype.clear = function() {
            var e = this.horiz.parentNode;
            e.removeChild(this.horiz), e.removeChild(this.vert);
        };
        r = function() {};
        function Nr(e, t) {
            t = t || Tr(e);
            var n = e.display.barWidth, r = e.display.barHeight;
            Or(e, t);
            for (var i = 0; i < 4 && n != e.display.barWidth || r != e.display.barHeight; i++) n != e.display.barWidth && e.options.lineWrapping && gr(e), 
            Or(e, Tr(e)), n = e.display.barWidth, r = e.display.barHeight;
        }
        function Or(e, t) {
            var n = e.display, r = n.scrollbars.update(t);
            n.sizer.style.paddingRight = (n.barWidth = r.right) + "px", n.sizer.style.paddingBottom = (n.barHeight = r.bottom) + "px", 
            n.heightForcer.style.borderBottom = r.bottom + "px solid transparent", r.right && r.bottom ? (n.scrollbarFiller.style.display = "block", 
            n.scrollbarFiller.style.height = r.bottom + "px", n.scrollbarFiller.style.width = r.right + "px") : n.scrollbarFiller.style.display = "", 
            r.bottom && e.options.coverGutterNextToScrollbar && e.options.fixedGutter ? (n.gutterFiller.style.display = "block", 
            n.gutterFiller.style.height = r.bottom + "px", n.gutterFiller.style.width = t.gutterWidth + "px") : n.gutterFiller.style.display = "";
        }
        r.prototype.update = function() {
            return {
                bottom: 0,
                right: 0
            };
        }, r.prototype.setScrollLeft = function() {}, r.prototype.setScrollTop = function() {}, 
        r.prototype.clear = function() {};
        var Ar = {
            native: e,
            null: r
        };
        function Fr(n) {
            n.display.scrollbars && (n.display.scrollbars.clear(), n.display.scrollbars.addClass && k(n.display.wrapper, n.display.scrollbars.addClass)), 
            n.display.scrollbars = new Ar[n.options.scrollbarStyle](function(e) {
                n.display.wrapper.insertBefore(e, n.display.scrollbarFiller), ye(e, "mousedown", function() {
                    n.state.focused && setTimeout(function() {
                        return n.display.input.focus();
                    }, 0);
                }), e.setAttribute("cm-not-content", "true");
            }, function(e, t) {
                ("horizontal" == t ? Mr : kr)(n, e);
            }, n), n.display.scrollbars.addClass && F(n.display.wrapper, n.display.scrollbars.addClass);
        }
        var Dr = 0;
        function Wr(e) {
            e.curOp = {
                cm: e,
                viewChanged: !1,
                startHeight: e.doc.height,
                forceUpdate: !1,
                updateInput: 0,
                typing: !1,
                changeObjs: null,
                cursorActivityHandlers: null,
                cursorActivityCalled: 0,
                selectionChanged: !1,
                updateMaxLine: !1,
                scrollLeft: null,
                scrollTop: null,
                scrollToPos: null,
                focus: !1,
                id: ++Dr
            }, e = e.curOp, nn ? nn.ops.push(e) : e.ownsGroup = nn = {
                ops: [ e ],
                delayedCallbacks: []
            };
        }
        function Pr(e) {
            e = e.curOp;
            e && rn(e, function(e) {
                for (var t = 0; t < e.ops.length; t++) e.ops[t].cm.curOp = null;
                !function(e) {
                    for (var t = e.ops, n = 0; n < t.length; n++) !function(e) {
                        var t = e.cm, n = t.display;
                        (function(e) {
                            var t = e.display;
                            !t.scrollbarsClipped && t.scroller.offsetWidth && (t.nativeBarWidth = t.scroller.offsetWidth - t.scroller.clientWidth, 
                            t.heightForcer.style.height = wn(e) + "px", t.sizer.style.marginBottom = -t.nativeBarWidth + "px", 
                            t.sizer.style.borderRightWidth = wn(e) + "px", t.scrollbarsClipped = !0);
                        })(t), e.updateMaxLine && qt(t);
                        e.mustUpdate = e.viewChanged || e.forceUpdate || null != e.scrollTop || e.scrollToPos && (e.scrollToPos.from.line < n.viewFrom || e.scrollToPos.to.line >= n.viewTo) || n.maxLineChanged && t.options.lineWrapping, 
                        e.update = e.mustUpdate && new Ur(t, e.mustUpdate && {
                            top: e.scrollTop,
                            ensure: e.scrollToPos
                        }, e.forceUpdate);
                    }(t[n]);
                    for (var r = 0; r < t.length; r++) !function(e) {
                        e.updatedDisplay = e.mustUpdate && Kr(e.cm, e.update);
                    }(t[r]);
                    for (var i = 0; i < t.length; i++) !function(e) {
                        var t = e.cm, n = t.display;
                        e.updatedDisplay && gr(t);
                        e.barMeasure = Tr(t), n.maxLineChanged && !t.options.lineWrapping && (e.adjustWidthTo = kn(t, n.maxLine, n.maxLine.text.length).left + 3, 
                        t.display.sizerWidth = e.adjustWidthTo, e.barMeasure.scrollWidth = Math.max(n.scroller.clientWidth, n.sizer.offsetLeft + e.adjustWidthTo + wn(t) + t.display.barWidth), 
                        e.maxScrollLeft = Math.max(0, n.sizer.offsetLeft + e.adjustWidthTo - xn(t)));
                        (e.updatedDisplay || e.selectionChanged) && (e.preparedSelection = n.input.prepareSelection());
                    }(t[i]);
                    for (var o = 0; o < t.length; o++) !function(e) {
                        var t = e.cm;
                        null != e.adjustWidthTo && (t.display.sizer.style.minWidth = e.adjustWidthTo + "px", 
                        e.maxScrollLeft < t.doc.scrollLeft && Mr(t, Math.min(t.display.scroller.scrollLeft, e.maxScrollLeft), !0), 
                        t.display.maxLineChanged = !1);
                        var n = e.focus && e.focus == A();
                        e.preparedSelection && t.display.input.showSelection(e.preparedSelection, n);
                        !e.updatedDisplay && e.startHeight == t.doc.height || Nr(t, e.barMeasure);
                        e.updatedDisplay && jr(t, e.barMeasure);
                        e.selectionChanged && cr(t);
                        t.state.focused && e.updateInput && t.display.input.reset(e.typing);
                        n && hr(e.cm);
                    }(t[o]);
                    for (var l = 0; l < t.length; l++) !function(e) {
                        var t = e.cm, n = t.display, r = t.doc;
                        e.updatedDisplay && Gr(t, e.update);
                        null == n.wheelStartX || null == e.scrollTop && null == e.scrollLeft && !e.scrollToPos || (n.wheelStartX = n.wheelStartY = null);
                        null != e.scrollTop && Lr(t, e.scrollTop, e.forceScroll);
                        null != e.scrollLeft && Mr(t, e.scrollLeft, !0, !0);
                        {
                            var i;
                            e.scrollToPos && (i = function(e, t, n, r) {
                                null == r && (r = 0), e.options.lineWrapping || t != n || (n = "before" == (t = t.ch ? rt(t.line, "before" == t.sticky ? t.ch - 1 : t.ch, "after") : t).sticky ? rt(t.line, t.ch + 1, "before") : t);
                                for (var i = 0; i < 5; i++) {
                                    var o, l = !1, s = Bn(e, t), a = n && n != t ? Bn(e, n) : s, u = yr(e, o = {
                                        left: Math.min(s.left, a.left),
                                        top: Math.min(s.top, a.top) - r,
                                        right: Math.max(s.left, a.left),
                                        bottom: Math.max(s.bottom, a.bottom) + r
                                    }), s = e.doc.scrollTop, a = e.doc.scrollLeft;
                                    if (null != u.scrollTop && (kr(e, u.scrollTop), 1 < Math.abs(e.doc.scrollTop - s) && (l = !0)), 
                                    null != u.scrollLeft && (Mr(e, u.scrollLeft), 1 < Math.abs(e.doc.scrollLeft - a) && (l = !0)), 
                                    !l) break;
                                }
                                return o;
                            }(t, ct(r, e.scrollToPos.from), ct(r, e.scrollToPos.to), e.scrollToPos.margin), 
                            function(e, t) {
                                var n, r, i;
                                Ce(e, "scrollCursorIntoView") || (r = (n = e.display).sizer.getBoundingClientRect(), 
                                i = null, t.top + r.top < 0 ? i = !0 : t.bottom + r.top > (window.innerHeight || document.documentElement.clientHeight) && (i = !1), 
                                null == i || u || (t = T("div", "\u200b", null, "position: absolute;\n                         top: " + (t.top - n.viewOffset - vn(e.display)) + "px;\n                         height: " + (t.bottom - t.top + wn(e) + n.barHeight) + "px;\n                         left: " + t.left + "px; width: " + Math.max(2, t.right - t.left) + "px;"), 
                                e.display.lineSpace.appendChild(t), t.scrollIntoView(i), e.display.lineSpace.removeChild(t)));
                            }(t, i));
                        }
                        var o = e.maybeHiddenMarkers, l = e.maybeUnhiddenMarkers;
                        if (o) for (var s = 0; s < o.length; ++s) o[s].lines.length || xe(o[s], "hide");
                        if (l) for (var a = 0; a < l.length; ++a) l[a].lines.length && xe(l[a], "unhide");
                        n.wrapper.offsetHeight && (r.scrollTop = t.display.scroller.scrollTop);
                        e.changeObjs && xe(t, "changes", t, e.changeObjs);
                        e.update && e.update.finish();
                    }(t[l]);
                }(e);
            });
        }
        function Hr(e, t) {
            if (e.curOp) return t();
            Wr(e);
            try {
                return t();
            } finally {
                Pr(e);
            }
        }
        function Ir(e, t) {
            return function() {
                if (e.curOp) return t.apply(e, arguments);
                Wr(e);
                try {
                    return t.apply(e, arguments);
                } finally {
                    Pr(e);
                }
            };
        }
        function Er(e) {
            return function() {
                if (this.curOp) return e.apply(this, arguments);
                Wr(this);
                try {
                    return e.apply(this, arguments);
                } finally {
                    Pr(this);
                }
            };
        }
        function Rr(t) {
            return function() {
                var e = this.cm;
                if (!e || e.curOp) return t.apply(this, arguments);
                Wr(e);
                try {
                    return t.apply(this, arguments);
                } finally {
                    Pr(e);
                }
            };
        }
        function zr(e, t) {
            e.doc.highlightFrontier < e.display.viewTo && e.state.highlight.set(t, P(Br, e));
        }
        function Br(l) {
            var s, a, u, c = l.doc;
            c.highlightFrontier >= l.display.viewTo || (s = +new Date() + l.options.workTime, 
            a = mt(l, c.highlightFrontier), u = [], c.iter(a.line, Math.min(c.first + c.size, l.display.viewTo + 500), function(e) {
                if (a.line >= l.display.viewFrom) {
                    var t = e.styles, n = e.text.length > l.options.maxHighlightLength ? qe(c.mode, a.state) : null, r = pt(l, e, a, !0);
                    n && (a.state = n), e.styles = r.styles;
                    n = e.styleClasses, r = r.classes;
                    r ? e.styleClasses = r : n && (e.styleClasses = null);
                    for (var i = !t || t.length != e.styles.length || n != r && (!n || !r || n.bgClass != r.bgClass || n.textClass != r.textClass), o = 0; !i && o < t.length; ++o) i = t[o] != e.styles[o];
                    i && u.push(a.line), e.stateAfter = a.save(), a.nextLine();
                } else e.text.length <= l.options.maxHighlightLength && vt(l, e.text, a), e.stateAfter = a.line % 5 == 0 ? a.save() : null, 
                a.nextLine();
                if (+new Date() > s) return zr(l, l.options.workDelay), !0;
            }), c.highlightFrontier = a.line, c.modeFrontier = Math.max(c.modeFrontier, a.line), 
            u.length && Hr(l, function() {
                for (var e = 0; e < u.length; e++) nr(l, u[e], "text");
            }));
        }
        var Ur = function(e, t, n) {
            var r = e.display;
            this.viewport = t, this.visible = vr(r, e.doc, t), this.editorIsHidden = !r.wrapper.offsetWidth, 
            this.wrapperHeight = r.wrapper.clientHeight, this.wrapperWidth = r.wrapper.clientWidth, 
            this.oldDisplayWidth = xn(e), this.force = n, this.dims = Xn(e), this.events = [];
        };
        function Kr(e, t) {
            var n = e.display, r = e.doc;
            if (t.editorIsHidden) return rr(e), !1;
            if (!t.force && t.visible.from >= n.viewFrom && t.visible.to <= n.viewTo && (null == n.updateLineNumbers || n.updateLineNumbers >= n.viewTo) && n.renderedView == n.view && 0 == or(e)) return !1;
            $r(e) && (rr(e), t.dims = Xn(e));
            var i = r.first + r.size, o = Math.max(t.visible.from - e.options.viewportMargin, r.first), l = Math.min(i, t.visible.to + e.options.viewportMargin);
            n.viewFrom < o && o - n.viewFrom < 20 && (o = Math.max(r.first, n.viewFrom)), n.viewTo > l && n.viewTo - l < 20 && (l = Math.min(i, n.viewTo)), 
            Lt && (o = Bt(e.doc, o), l = Ut(e.doc, l));
            var s = o != n.viewFrom || l != n.viewTo || n.lastWrapHeight != t.wrapperHeight || n.lastWrapWidth != t.wrapperWidth;
            r = o, i = l, 0 == (l = (o = e).display).view.length || r >= l.viewTo || i <= l.viewFrom ? (l.view = tn(o, r, i), 
            l.viewFrom = r) : (l.viewFrom > r ? l.view = tn(o, r, l.viewFrom).concat(l.view) : l.viewFrom < r && (l.view = l.view.slice(er(o, r))), 
            l.viewFrom = r, l.viewTo < i ? l.view = l.view.concat(tn(o, l.viewTo, i)) : l.viewTo > i && (l.view = l.view.slice(0, er(o, i)))), 
            l.viewTo = i, n.viewOffset = Gt(Xe(e.doc, n.viewFrom)), e.display.mover.style.top = n.viewOffset + "px";
            o = or(e);
            if (!s && 0 == o && !t.force && n.renderedView == n.view && (null == n.updateLineNumbers || n.updateLineNumbers >= n.viewTo)) return !1;
            l = function(e) {
                if (e.hasFocus()) return null;
                var t = A();
                if (!t || !O(e.display.lineDiv, t)) return null;
                var n = {
                    activeElt: t
                };
                return !window.getSelection || (t = window.getSelection()).anchorNode && t.extend && O(e.display.lineDiv, t.anchorNode) && (n.anchorNode = t.anchorNode, 
                n.anchorOffset = t.anchorOffset, n.focusNode = t.focusNode, n.focusOffset = t.focusOffset), 
                n;
            }(e);
            return 4 < o && (n.lineDiv.style.display = "none"), function(n, e, t) {
                var r = n.display, i = n.options.lineNumbers, o = r.lineDiv, l = o.firstChild;
                function s(e) {
                    var t = e.nextSibling;
                    return d && g && n.display.currentWheelTarget == e ? e.style.display = "none" : e.parentNode.removeChild(e), 
                    t;
                }
                for (var a = r.view, u = r.viewFrom, c = 0; c < a.length; c++) {
                    var h = a[c];
                    if (!h.hidden) if (h.node && h.node.parentNode == o) {
                        for (;l != h.node; ) l = s(l);
                        var f = i && null != e && e <= u && h.lineNumber;
                        h.changes && (-1 < R(h.changes, "gutter") && (f = !1), an(n, h, u, t)), f && (L(h.lineNumber), 
                        h.lineNumber.appendChild(document.createTextNode(nt(n.options, u)))), l = h.node.nextSibling;
                    } else {
                        f = function(e, t, n, r) {
                            var i = cn(e, t);
                            return t.text = t.node = i.pre, i.bgClass && (t.bgClass = i.bgClass), i.textClass && (t.textClass = i.textClass), 
                            hn(e, t), fn(e, t, n, r), dn(e, t, r), t.node;
                        }(n, h, u, t);
                        o.insertBefore(f, l);
                    }
                    u += h.size;
                }
                for (;l; ) l = s(l);
            }(e, n.updateLineNumbers, t.dims), 4 < o && (n.lineDiv.style.display = ""), n.renderedView = n.view, 
            (i = l) && i.activeElt && i.activeElt != A() && (i.activeElt.focus(), i.anchorNode && O(document.body, i.anchorNode) && O(document.body, i.focusNode) && (o = window.getSelection(), 
            (l = document.createRange()).setEnd(i.anchorNode, i.anchorOffset), l.collapse(!1), 
            o.removeAllRanges(), o.addRange(l), o.extend(i.focusNode, i.focusOffset))), L(n.cursorDiv), 
            L(n.selectionDiv), n.gutters.style.height = n.sizer.style.minHeight = 0, s && (n.lastWrapHeight = t.wrapperHeight, 
            n.lastWrapWidth = t.wrapperWidth, zr(e, 400)), !(n.updateLineNumbers = null);
        }
        function Gr(e, t) {
            for (var n = t.viewport, r = !0; (r && e.options.lineWrapping && t.oldDisplayWidth != xn(e) || (n && null != n.top && (n = {
                top: Math.min(e.doc.height + yn(e.display) - Cn(e), n.top)
            }), t.visible = vr(e.display, e.doc, n), !(t.visible.from >= e.display.viewFrom && t.visible.to <= e.display.viewTo))) && Kr(e, t); r = !1) {
                gr(e);
                var i = Tr(e);
                lr(e), Nr(e, i), jr(e, i), t.force = !1;
            }
            t.signal(e, "update", e), e.display.viewFrom == e.display.reportedViewFrom && e.display.viewTo == e.display.reportedViewTo || (t.signal(e, "viewportChange", e, e.display.viewFrom, e.display.viewTo), 
            e.display.reportedViewFrom = e.display.viewFrom, e.display.reportedViewTo = e.display.viewTo);
        }
        function Vr(e, t) {
            var n = new Ur(e, t);
            Kr(e, n) && (gr(e), Gr(e, n), t = Tr(e), lr(e), Nr(e, t), jr(e, t), n.finish());
        }
        function qr(e) {
            var t = e.gutters.offsetWidth;
            e.sizer.style.marginLeft = t + "px";
        }
        function jr(e, t) {
            e.display.sizer.style.minHeight = t.docHeight + "px", e.display.heightForcer.style.top = t.docHeight + "px", 
            e.display.gutters.style.height = t.docHeight + e.display.barHeight + wn(e) + "px";
        }
        function _r(e) {
            var t = e.display, n = t.view;
            if (t.alignWidgets || t.gutters.firstChild && e.options.fixedGutter) {
                for (var r = Yn(t) - t.scroller.scrollLeft + e.doc.scrollLeft, i = t.gutters.offsetWidth, o = r + "px", l = 0; l < n.length; l++) if (!n[l].hidden) {
                    e.options.fixedGutter && (n[l].gutter && (n[l].gutter.style.left = o), n[l].gutterBackground && (n[l].gutterBackground.style.left = o));
                    var s = n[l].alignable;
                    if (s) for (var a = 0; a < s.length; a++) s[a].style.left = o;
                }
                e.options.fixedGutter && (t.gutters.style.left = r + i + "px");
            }
        }
        function $r(e) {
            if (e.options.lineNumbers) {
                var t = e.doc, n = nt(e.options, t.first + t.size - 1), r = e.display;
                if (n.length != r.lineNumChars) {
                    var i = r.measure.appendChild(T("div", [ T("div", n) ], "CodeMirror-linenumber CodeMirror-gutter-elt")), t = i.firstChild.offsetWidth, i = i.offsetWidth - t;
                    return r.lineGutter.style.width = "", r.lineNumInnerWidth = Math.max(t, r.lineGutter.offsetWidth - i) + 1, 
                    r.lineNumWidth = r.lineNumInnerWidth + i, r.lineNumChars = r.lineNumInnerWidth ? n.length : -1, 
                    r.lineGutter.style.width = r.lineNumWidth + "px", qr(e.display), 1;
                }
            }
        }
        function Xr(e, t) {
            for (var n = [], r = !1, i = 0; i < e.length; i++) {
                var o = e[i], l = null;
                if ("string" != typeof o && (l = o.style, o = o.className), "CodeMirror-linenumbers" == o) {
                    if (!t) continue;
                    r = !0;
                }
                n.push({
                    className: o,
                    style: l
                });
            }
            return t && !r && n.push({
                className: "CodeMirror-linenumbers",
                style: null
            }), n;
        }
        function Yr(e) {
            var t = e.gutters, n = e.gutterSpecs;
            L(t), e.lineGutter = null;
            for (var r = 0; r < n.length; ++r) {
                var i = n[r], o = i.className, l = i.style, i = t.appendChild(T("div", null, "CodeMirror-gutter " + o));
                l && (i.style.cssText = l), "CodeMirror-linenumbers" == o && ((e.lineGutter = i).style.width = (e.lineNumWidth || 1) + "px");
            }
            t.style.display = n.length ? "" : "none", qr(e);
        }
        function Zr(e) {
            Yr(e.display), tr(e), _r(e);
        }
        function Qr(e, t, n, r) {
            var i = this;
            this.input = n, i.scrollbarFiller = T("div", null, "CodeMirror-scrollbar-filler"), 
            i.scrollbarFiller.setAttribute("cm-not-content", "true"), i.gutterFiller = T("div", null, "CodeMirror-gutter-filler"), 
            i.gutterFiller.setAttribute("cm-not-content", "true"), i.lineDiv = N("div", null, "CodeMirror-code"), 
            i.selectionDiv = T("div", null, null, "position: relative; z-index: 1"), i.cursorDiv = T("div", null, "CodeMirror-cursors"), 
            i.measure = T("div", null, "CodeMirror-measure"), i.lineMeasure = T("div", null, "CodeMirror-measure"), 
            i.lineSpace = N("div", [ i.measure, i.lineMeasure, i.selectionDiv, i.cursorDiv, i.lineDiv ], null, "position: relative; outline: none");
            var o = N("div", [ i.lineSpace ], "CodeMirror-lines");
            i.mover = T("div", [ o ], null, "position: relative"), i.sizer = T("div", [ i.mover ], "CodeMirror-sizer"), 
            i.sizerWidth = null, i.heightForcer = T("div", null, null, "position: absolute; height: " + z + "px; width: 1px;"), 
            i.gutters = T("div", null, "CodeMirror-gutters"), i.lineGutter = null, i.scroller = T("div", [ i.sizer, i.heightForcer, i.gutters ], "CodeMirror-scroll"), 
            i.scroller.setAttribute("tabIndex", "-1"), i.wrapper = T("div", [ i.scrollbarFiller, i.gutterFiller, i.scroller ], "CodeMirror"), 
            v && y < 8 && (i.gutters.style.zIndex = -1, i.scroller.style.paddingRight = 0), 
            d || f && h || (i.scroller.draggable = !0), e && (e.appendChild ? e.appendChild(i.wrapper) : e(i.wrapper)), 
            i.viewFrom = i.viewTo = t.first, i.reportedViewFrom = i.reportedViewTo = t.first, 
            i.view = [], i.renderedView = null, i.externalMeasured = null, i.viewOffset = 0, 
            i.lastWrapHeight = i.lastWrapWidth = 0, i.updateLineNumbers = null, i.nativeBarWidth = i.barHeight = i.barWidth = 0, 
            i.scrollbarsClipped = !1, i.lineNumWidth = i.lineNumInnerWidth = i.lineNumChars = null, 
            i.alignWidgets = !1, i.cachedCharWidth = i.cachedTextHeight = i.cachedPaddingH = null, 
            i.maxLine = null, i.maxLineLength = 0, i.maxLineChanged = !1, i.wheelDX = i.wheelDY = i.wheelStartX = i.wheelStartY = null, 
            i.shift = !1, i.selForContextMenu = null, i.activeTouch = null, i.gutterSpecs = Xr(r.gutters, r.lineNumbers), 
            Yr(i), n.init(i);
        }
        Ur.prototype.signal = function(e, t) {
            ke(e, t) && this.events.push(arguments);
        }, Ur.prototype.finish = function() {
            for (var e = 0; e < this.events.length; e++) xe.apply(null, this.events[e]);
        };
        var Jr = 0, ei = null;
        function ti(e) {
            var t = e.wheelDeltaX, n = e.wheelDeltaY;
            return null == t && e.detail && e.axis == e.HORIZONTAL_AXIS && (t = e.detail), null == n && e.detail && e.axis == e.VERTICAL_AXIS ? n = e.detail : null == n && (n = e.wheelDelta), 
            {
                x: t,
                y: n
            };
        }
        function ni(e) {
            e = ti(e);
            return e.x *= ei, e.y *= ei, e;
        }
        function ri(e, t) {
            var n = ti(t), r = n.x, i = n.y, o = e.display, l = o.scroller, s = l.scrollWidth > l.clientWidth, a = l.scrollHeight > l.clientHeight;
            if (r && s || i && a) {
                if (i && g && d) e: for (var u = t.target, c = o.view; u != l; u = u.parentNode) for (var h = 0; h < c.length; h++) if (c[h].node == u) {
                    e.display.currentWheelTarget = u;
                    break e;
                }
                if (r && !f && !p && null != ei) return i && a && kr(e, Math.max(0, l.scrollTop + i * ei)), 
                Mr(e, Math.max(0, l.scrollLeft + r * ei)), (!i || i && a) && Me(t), void (o.wheelStartX = null);
                i && null != ei && (n = i * ei, a = (s = e.doc.scrollTop) + o.wrapper.clientHeight, 
                n < 0 ? s = Math.max(0, s + n - 50) : a = Math.min(e.doc.height, a + n + 50), Vr(e, {
                    top: s,
                    bottom: a
                })), Jr < 20 && (null == o.wheelStartX ? (o.wheelStartX = l.scrollLeft, o.wheelStartY = l.scrollTop, 
                o.wheelDX = r, o.wheelDY = i, setTimeout(function() {
                    var e, t;
                    null != o.wheelStartX && (t = l.scrollLeft - o.wheelStartX, t = (e = l.scrollTop - o.wheelStartY) && o.wheelDY && e / o.wheelDY || t && o.wheelDX && t / o.wheelDX, 
                    o.wheelStartX = o.wheelStartY = null, t && (ei = (ei * Jr + t) / (Jr + 1), ++Jr));
                }, 200)) : (o.wheelDX += r, o.wheelDY += i));
            }
        }
        v ? ei = -.53 : f ? ei = 15 : o ? ei = -.7 : a && (ei = -1 / 3);
        var ii = function(e, t) {
            this.ranges = e, this.primIndex = t;
        };
        ii.prototype.primary = function() {
            return this.ranges[this.primIndex];
        }, ii.prototype.equals = function(e) {
            if (e == this) return !0;
            if (e.primIndex != this.primIndex || e.ranges.length != this.ranges.length) return !1;
            for (var t = 0; t < this.ranges.length; t++) {
                var n = this.ranges[t], r = e.ranges[t];
                if (!ot(n.anchor, r.anchor) || !ot(n.head, r.head)) return !1;
            }
            return !0;
        }, ii.prototype.deepCopy = function() {
            for (var e = [], t = 0; t < this.ranges.length; t++) e[t] = new oi(lt(this.ranges[t].anchor), lt(this.ranges[t].head));
            return new ii(e, this.primIndex);
        }, ii.prototype.somethingSelected = function() {
            for (var e = 0; e < this.ranges.length; e++) if (!this.ranges[e].empty()) return !0;
            return !1;
        }, ii.prototype.contains = function(e, t) {
            t = t || e;
            for (var n = 0; n < this.ranges.length; n++) {
                var r = this.ranges[n];
                if (0 <= it(t, r.from()) && it(e, r.to()) <= 0) return n;
            }
            return -1;
        };
        var oi = function(e, t) {
            this.anchor = e, this.head = t;
        };
        function li(e, t, n) {
            var r = e && e.options.selectionsMayTouch, e = t[n];
            t.sort(function(e, t) {
                return it(e.from(), t.from());
            }), n = R(t, e);
            for (var i = 1; i < t.length; i++) {
                var o, l = t[i], s = t[i - 1], a = it(s.to(), l.from());
                (r && !l.empty() ? 0 < a : 0 <= a) && (o = at(s.from(), l.from()), a = st(s.to(), l.to()), 
                s = s.empty() ? l.from() == l.head : s.from() == s.head, i <= n && --n, t.splice(--i, 2, new oi(s ? a : o, s ? o : a)));
            }
            return new ii(t, n);
        }
        function si(e, t) {
            return new ii([ new oi(e, t || e) ], 0);
        }
        function ai(e) {
            return e.text ? rt(e.from.line + e.text.length - 1, _(e.text).length + (1 == e.text.length ? e.from.ch : 0)) : e.to;
        }
        function ui(e, t) {
            if (it(e, t.from) < 0) return e;
            if (it(e, t.to) <= 0) return ai(t);
            var n = e.line + t.text.length - (t.to.line - t.from.line) - 1, r = e.ch;
            return e.line == t.to.line && (r += ai(t).ch - t.to.ch), rt(n, r);
        }
        function ci(e, t) {
            for (var n = [], r = 0; r < e.sel.ranges.length; r++) {
                var i = e.sel.ranges[r];
                n.push(new oi(ui(i.anchor, t), ui(i.head, t)));
            }
            return li(e.cm, n, e.sel.primIndex);
        }
        function hi(e, t, n) {
            return e.line == t.line ? rt(n.line, e.ch - t.ch + n.ch) : rt(n.line + (e.line - t.line), e.ch);
        }
        function fi(e) {
            e.doc.mode = Ke(e.options, e.doc.modeOption), di(e);
        }
        function di(e) {
            e.doc.iter(function(e) {
                e.stateAfter && (e.stateAfter = null), e.styles && (e.styles = null);
            }), e.doc.modeFrontier = e.doc.highlightFrontier = e.doc.first, zr(e, 100), e.state.modeGen++, 
            e.curOp && tr(e);
        }
        function pi(e, t) {
            return 0 == t.from.ch && 0 == t.to.ch && "" == _(t.text) && (!e.cm || e.cm.options.wholeLineUpdateBefore);
        }
        function gi(e, o, t, l) {
            function i(e) {
                return t ? t[e] : null;
            }
            function n(e, t, n) {
                var r, i;
                r = t, i = n, t = l, (n = e).text = r, n.stateAfter && (n.stateAfter = null), n.styles && (n.styles = null), 
                null != n.order && (n.order = null), At(n), Ft(n, i), (t = t ? t(n) : 1) != n.height && Qe(n, t), 
                ln(e, "change", e, o);
            }
            function r(e, t) {
                for (var n = [], r = e; r < t; ++r) n.push(new jt(c[r], i(r), l));
                return n;
            }
            var s, a = o.from, u = o.to, c = o.text, h = Xe(e, a.line), f = Xe(e, u.line), d = _(c), p = i(c.length - 1), g = u.line - a.line;
            o.full ? (e.insert(0, r(0, c.length)), e.remove(c.length, e.size - c.length)) : pi(e, o) ? (s = r(0, c.length - 1), 
            n(f, f.text, p), g && e.remove(a.line, g), s.length && e.insert(a.line, s)) : h == f ? 1 == c.length ? n(h, h.text.slice(0, a.ch) + d + h.text.slice(u.ch), p) : ((s = r(1, c.length - 1)).push(new jt(d + h.text.slice(u.ch), p, l)), 
            n(h, h.text.slice(0, a.ch) + c[0], i(0)), e.insert(a.line + 1, s)) : 1 == c.length ? (n(h, h.text.slice(0, a.ch) + c[0] + f.text.slice(u.ch), i(0)), 
            e.remove(a.line + 1, g)) : (n(h, h.text.slice(0, a.ch) + c[0], i(0)), n(f, d + f.text.slice(u.ch), p), 
            p = r(1, c.length - 1), 1 < g && e.remove(a.line + 1, g - 1), e.insert(a.line + 1, p)), 
            ln(e, "change", e, o);
        }
        function mi(e, s, a) {
            !function e(t, n, r) {
                if (t.linked) for (var i = 0; i < t.linked.length; ++i) {
                    var o, l = t.linked[i];
                    l.doc != n && (o = r && l.sharedHist, a && !o || (s(l.doc, o), e(l.doc, t, o)));
                }
            }(e, null, !0);
        }
        function vi(e, t) {
            if (t.cm) throw new Error("This document is already in use.");
            Qn((e.doc = t).cm = e), fi(e), yi(e), e.options.lineWrapping || qt(e), e.options.mode = t.modeOption, 
            tr(e);
        }
        function yi(e) {
            ("rtl" == e.doc.direction ? F : k)(e.display.lineDiv, "CodeMirror-rtl");
        }
        function bi(e) {
            this.done = [], this.undone = [], this.undoDepth = 1 / 0, this.lastModTime = this.lastSelTime = 0, 
            this.lastOp = this.lastSelOp = null, this.lastOrigin = this.lastSelOrigin = null, 
            this.generation = this.maxGeneration = e || 1;
        }
        function wi(e, t) {
            var n = {
                from: lt(t.from),
                to: ai(t),
                text: Ye(e, t.from, t.to)
            };
            return Li(e, n, t.from.line, t.to.line + 1), mi(e, function(e) {
                return Li(e, n, t.from.line, t.to.line + 1), 0;
            }, !0), n;
        }
        function xi(e) {
            for (;e.length; ) {
                if (!_(e).ranges) break;
                e.pop();
            }
        }
        function Ci(e, t, n, r) {
            var i = e.history;
            i.undone.length = 0;
            var o, l, s = +new Date();
            if ((i.lastOp == r || i.lastOrigin == t.origin && t.origin && ("+" == t.origin.charAt(0) && i.lastModTime > s - (e.cm ? e.cm.options.historyEventDelay : 500) || "*" == t.origin.charAt(0))) && (o = (a = i).lastOp == r ? (xi(a.done), 
            _(a.done)) : a.done.length && !_(a.done).ranges ? _(a.done) : 1 < a.done.length && !a.done[a.done.length - 2].ranges ? (a.done.pop(), 
            _(a.done)) : void 0)) l = _(o.changes), 0 == it(t.from, t.to) && 0 == it(t.from, l.to) ? l.to = ai(t) : o.changes.push(wi(e, t)); else {
                var a = _(i.done);
                for (a && a.ranges || ki(e.sel, i.done), o = {
                    changes: [ wi(e, t) ],
                    generation: i.generation
                }, i.done.push(o); i.done.length > i.undoDepth; ) i.done.shift(), i.done[0].ranges || i.done.shift();
            }
            i.done.push(n), i.generation = ++i.maxGeneration, i.lastModTime = i.lastSelTime = s, 
            i.lastOp = i.lastSelOp = r, i.lastOrigin = i.lastSelOrigin = t.origin, l || xe(e, "historyAdded");
        }
        function Si(e, t, n, r) {
            var i, o, l, s = e.history, a = r && r.origin;
            n == s.lastSelOp || a && s.lastSelOrigin == a && (s.lastModTime == s.lastSelTime && s.lastOrigin == a || (i = e, 
            o = a, l = _(s.done), e = t, "*" == (o = o.charAt(0)) || "+" == o && l.ranges.length == e.ranges.length && l.somethingSelected() == e.somethingSelected() && new Date() - i.history.lastSelTime <= (i.cm ? i.cm.options.historyEventDelay : 500))) ? s.done[s.done.length - 1] = t : ki(t, s.done), 
            s.lastSelTime = +new Date(), s.lastSelOrigin = a, s.lastSelOp = n, r && !1 !== r.clearRedo && xi(s.undone);
        }
        function ki(e, t) {
            var n = _(t);
            n && n.ranges && n.equals(e) || t.push(e);
        }
        function Li(t, n, e, r) {
            var i = n["spans_" + t.id], o = 0;
            t.iter(Math.max(t.first, e), Math.min(t.first + t.size, r), function(e) {
                e.markedSpans && ((i = i || (n["spans_" + t.id] = {}))[o] = e.markedSpans), ++o;
            });
        }
        function Mi(e, t) {
            var n = t["spans_" + e.id];
            if (!n) return null;
            for (var r = [], i = 0; i < t.text.length; ++i) r.push(function(e) {
                if (!e) return null;
                for (var t, n = 0; n < e.length; ++n) e[n].marker.explicitlyCleared ? t = t || e.slice(0, n) : t && t.push(e[n]);
                return t ? t.length ? t : null : e;
            }(n[i]));
            return r;
        }
        function Ti(e, t) {
            var n = Mi(e, t), r = Nt(e, t);
            if (!n) return r;
            if (!r) return n;
            for (var i = 0; i < n.length; ++i) {
                var o = n[i], l = r[i];
                if (o && l) e: for (var s = 0; s < l.length; ++s) {
                    for (var a = l[s], u = 0; u < o.length; ++u) if (o[u].marker == a.marker) continue e;
                    o.push(a);
                } else l && (n[i] = l);
            }
            return n;
        }
        function Ni(e, t, n) {
            for (var r = [], i = 0; i < e.length; ++i) {
                var o = e[i];
                if (o.ranges) r.push(n ? ii.prototype.deepCopy.call(o) : o); else {
                    var l = o.changes, s = [];
                    r.push({
                        changes: s
                    });
                    for (var a = 0; a < l.length; ++a) {
                        var u, c = l[a];
                        if (s.push({
                            from: c.from,
                            to: c.to,
                            text: c.text
                        }), t) for (var h in c) (u = h.match(/^spans_(\d+)$/)) && -1 < R(t, Number(u[1])) && (_(s)[h] = c[h], 
                        delete c[h]);
                    }
                }
            }
            return r;
        }
        function Oi(e, t, n, r) {
            if (r) {
                r = e.anchor;
                return n && ((e = it(t, r) < 0) != it(n, r) < 0 ? (r = t, t = n) : e != it(t, n) < 0 && (t = n)), 
                new oi(r, t);
            }
            return new oi(n || t, t);
        }
        function Ai(e, t, n, r, i) {
            null == i && (i = e.cm && (e.cm.display.shift || e.extend)), Hi(e, new ii([ Oi(e.sel.primary(), t, n, i) ], 0), r);
        }
        function Fi(e, t, n) {
            for (var r = [], i = e.cm && (e.cm.display.shift || e.extend), o = 0; o < e.sel.ranges.length; o++) r[o] = Oi(e.sel.ranges[o], t[o], null, i);
            Hi(e, li(e.cm, r, e.sel.primIndex), n);
        }
        function Di(e, t, n, r) {
            var i = e.sel.ranges.slice(0);
            i[t] = n, Hi(e, li(e.cm, i, e.sel.primIndex), r);
        }
        function Wi(e, t, n, r) {
            Hi(e, si(t, n), r);
        }
        function Pi(e, t, n) {
            var r = e.history.done, i = _(r);
            i && i.ranges ? Ii(e, r[r.length - 1] = t, n) : Hi(e, t, n);
        }
        function Hi(e, t, n) {
            Ii(e, t, n), Si(e, e.sel, e.cm ? e.cm.curOp.id : NaN, n);
        }
        function Ii(e, t, n) {
            var r, i;
            (ke(e, "beforeSelectionChange") || e.cm && ke(e.cm, "beforeSelectionChange")) && (r = e, 
            i = n, i = {
                ranges: (o = t).ranges,
                update: function(e) {
                    this.ranges = [];
                    for (var t = 0; t < e.length; t++) this.ranges[t] = new oi(ct(r, e[t].anchor), ct(r, e[t].head));
                },
                origin: i && i.origin
            }, xe(r, "beforeSelectionChange", r, i), r.cm && xe(r.cm, "beforeSelectionChange", r.cm, i), 
            t = i.ranges != o.ranges ? li(r.cm, i.ranges, i.ranges.length - 1) : o);
            var o = n && n.bias || (it(t.primary().head, e.sel.primary().head) < 0 ? -1 : 1);
            Ei(e, zi(e, t, o, !0)), n && !1 === n.scroll || !e.cm || wr(e.cm);
        }
        function Ei(e, t) {
            t.equals(e.sel) || (e.sel = t, e.cm && (e.cm.curOp.updateInput = 1, e.cm.curOp.selectionChanged = !0, 
            Se(e.cm)), ln(e, "cursorActivity", e));
        }
        function Ri(e) {
            Ei(e, zi(e, e.sel, null, !1));
        }
        function zi(e, t, n, r) {
            for (var i, o = 0; o < t.ranges.length; o++) {
                var l = t.ranges[o], s = t.ranges.length == e.sel.ranges.length && e.sel.ranges[o], a = Ui(e, l.anchor, s && s.anchor, n, r), s = Ui(e, l.head, s && s.head, n, r);
                !i && a == l.anchor && s == l.head || ((i = i || t.ranges.slice(0, o))[o] = new oi(a, s));
            }
            return i ? li(e.cm, i, t.primIndex) : t;
        }
        function Bi(e, t, n, r, i) {
            var o = Xe(e, t.line);
            if (o.markedSpans) for (var l = 0; l < o.markedSpans.length; ++l) {
                var s = o.markedSpans[l], a = s.marker;
                if ((null == s.from || (a.inclusiveLeft ? s.from <= t.ch : s.from < t.ch)) && (null == s.to || (a.inclusiveRight ? s.to >= t.ch : s.to > t.ch))) {
                    if (i && (xe(a, "beforeCursorEnter"), a.explicitlyCleared)) {
                        if (o.markedSpans) {
                            --l;
                            continue;
                        }
                        break;
                    }
                    if (a.atomic) {
                        if (n) {
                            var u = a.find(r < 0 ? 1 : -1), s = void 0;
                            if ((r < 0 ? a.inclusiveRight : a.inclusiveLeft) && (u = Ki(e, u, -r, u && u.line == t.line ? o : null)), 
                            u && u.line == t.line && (s = it(u, n)) && (r < 0 ? s < 0 : 0 < s)) return Bi(e, u, t, r, i);
                        }
                        u = a.find(r < 0 ? -1 : 1);
                        return (r < 0 ? a.inclusiveLeft : a.inclusiveRight) && (u = Ki(e, u, r, u.line == t.line ? o : null)), 
                        u ? Bi(e, u, t, r, i) : null;
                    }
                }
            }
            return t;
        }
        function Ui(e, t, n, r, i) {
            r = r || 1, r = Bi(e, t, n, r, i) || !i && Bi(e, t, n, r, !0) || Bi(e, t, n, -r, i) || !i && Bi(e, t, n, -r, !0);
            return r || (e.cantEdit = !0, rt(e.first, 0));
        }
        function Ki(e, t, n, r) {
            return n < 0 && 0 == t.ch ? t.line > e.first ? ct(e, rt(t.line - 1)) : null : 0 < n && t.ch == (r || Xe(e, t.line)).text.length ? t.line < e.first + e.size - 1 ? rt(t.line + 1, 0) : null : new rt(t.line, t.ch + n);
        }
        function Gi(e) {
            e.setSelection(rt(e.firstLine(), 0), rt(e.lastLine()), U);
        }
        function Vi(i, e, t) {
            var o = {
                canceled: !1,
                from: e.from,
                to: e.to,
                text: e.text,
                origin: e.origin,
                cancel: function() {
                    return o.canceled = !0;
                }
            };
            return t && (o.update = function(e, t, n, r) {
                e && (o.from = ct(i, e)), t && (o.to = ct(i, t)), n && (o.text = n), void 0 !== r && (o.origin = r);
            }), xe(i, "beforeChange", i, o), i.cm && xe(i.cm, "beforeChange", i.cm, o), o.canceled ? (i.cm && (i.cm.curOp.updateInput = 2), 
            null) : {
                from: o.from,
                to: o.to,
                text: o.text,
                origin: o.origin
            };
        }
        function qi(e, t, n) {
            if (e.cm) {
                if (!e.cm.curOp) return Ir(e.cm, qi)(e, t, n);
                if (e.cm.state.suppressEdits) return;
            }
            if (!(ke(e, "beforeChange") || e.cm && ke(e.cm, "beforeChange")) || (t = Vi(e, t, !0))) {
                var r = kt && !n && function(e, t, n) {
                    var r = null;
                    if (e.iter(t.line, n.line + 1, function(e) {
                        if (e.markedSpans) for (var t = 0; t < e.markedSpans.length; ++t) {
                            var n = e.markedSpans[t].marker;
                            !n.readOnly || r && -1 != R(r, n) || (r = r || []).push(n);
                        }
                    }), !r) return null;
                    for (var i = [ {
                        from: t,
                        to: n
                    } ], o = 0; o < r.length; ++o) for (var l = r[o], s = l.find(0), a = 0; a < i.length; ++a) {
                        var u, c, h, f = i[a];
                        it(f.to, s.from) < 0 || 0 < it(f.from, s.to) || (u = [ a, 1 ], c = it(f.from, s.from), 
                        h = it(f.to, s.to), (c < 0 || !l.inclusiveLeft && !c) && u.push({
                            from: f.from,
                            to: s.from
                        }), (0 < h || !l.inclusiveRight && !h) && u.push({
                            from: s.to,
                            to: f.to
                        }), i.splice.apply(i, u), a += u.length - 3);
                    }
                    return i;
                }(e, t.from, t.to);
                if (r) for (var i = r.length - 1; 0 <= i; --i) ji(e, {
                    from: r[i].from,
                    to: r[i].to,
                    text: i ? [ "" ] : t.text,
                    origin: t.origin
                }); else ji(e, t);
            }
        }
        function ji(e, n) {
            var t, r;
            1 == n.text.length && "" == n.text[0] && 0 == it(n.from, n.to) || (t = ci(e, n), 
            Ci(e, n, t, e.cm ? e.cm.curOp.id : NaN), Xi(e, n, t, Nt(e, n)), r = [], mi(e, function(e, t) {
                t || -1 != R(r, e.history) || (Ji(e.history, n), r.push(e.history)), Xi(e, n, null, Nt(e, n));
            }));
        }
        function _i(i, o, e) {
            var t = i.cm && i.cm.state.suppressEdits;
            if (!t || e) {
                for (var l, n = i.history, r = i.sel, s = "undo" == o ? n.done : n.undone, a = "undo" == o ? n.undone : n.done, u = 0; u < s.length && (l = s[u], 
                e ? !l.ranges || l.equals(i.sel) : l.ranges); u++) ;
                if (u != s.length) {
                    for (n.lastOrigin = n.lastSelOrigin = null; ;) {
                        if (!(l = s.pop()).ranges) {
                            if (t) return void s.push(l);
                            break;
                        }
                        if (ki(l, a), e && !l.equals(i.sel)) return void Hi(i, l, {
                            clearRedo: !1
                        });
                        r = l;
                    }
                    var c = [];
                    ki(r, a), a.push({
                        changes: c,
                        generation: n.generation
                    }), n.generation = l.generation || ++n.maxGeneration;
                    for (var h = ke(i, "beforeChange") || i.cm && ke(i.cm, "beforeChange"), f = l.changes.length - 1; 0 <= f; --f) {
                        var d = function(e) {
                            var n = l.changes[e];
                            if (n.origin = o, h && !Vi(i, n, !1)) return s.length = 0, {};
                            c.push(wi(i, n));
                            var t = e ? ci(i, n) : _(s);
                            Xi(i, n, t, Ti(i, n)), !e && i.cm && i.cm.scrollIntoView({
                                from: n.from,
                                to: ai(n)
                            });
                            var r = [];
                            mi(i, function(e, t) {
                                t || -1 != R(r, e.history) || (Ji(e.history, n), r.push(e.history)), Xi(e, n, null, Ti(e, n));
                            });
                        }(f);
                        if (d) return d.v;
                    }
                }
            }
        }
        function $i(e, t) {
            if (0 != t && (e.first += t, e.sel = new ii($(e.sel.ranges, function(e) {
                return new oi(rt(e.anchor.line + t, e.anchor.ch), rt(e.head.line + t, e.head.ch));
            }), e.sel.primIndex), e.cm)) {
                tr(e.cm, e.first, e.first - t, t);
                for (var n = e.cm.display, r = n.viewFrom; r < n.viewTo; r++) nr(e.cm, r, "gutter");
            }
        }
        function Xi(e, t, n, r) {
            if (e.cm && !e.cm.curOp) return Ir(e.cm, Xi)(e, t, n, r);
            var i;
            t.to.line < e.first ? $i(e, t.text.length - 1 - (t.to.line - t.from.line)) : t.from.line > e.lastLine() || (t.from.line < e.first && ($i(e, i = t.text.length - 1 - (e.first - t.from.line)), 
            t = {
                from: rt(e.first, 0),
                to: rt(t.to.line + i, t.to.ch),
                text: [ _(t.text) ],
                origin: t.origin
            }), i = e.lastLine(), t.to.line > i && (t = {
                from: t.from,
                to: rt(i, Xe(e, i).text.length),
                text: [ t.text[0] ],
                origin: t.origin
            }), t.removed = Ye(e, t.from, t.to), n = n || ci(e, t), e.cm ? function(e, t, n) {
                var r = e.doc, i = e.display, o = t.from, l = t.to, s = !1, a = o.line;
                e.options.lineWrapping || (a = Je(zt(Xe(r, o.line))), r.iter(a, l.line + 1, function(e) {
                    if (e == i.maxLine) return s = !0;
                }));
                -1 < r.sel.contains(t.from, t.to) && Se(e);
                gi(r, t, n, Zn(e)), e.options.lineWrapping || (r.iter(a, o.line + t.text.length, function(e) {
                    var t = Vt(e);
                    t > i.maxLineLength && (i.maxLine = e, i.maxLineLength = t, i.maxLineChanged = !0, 
                    s = !1);
                }), s && (e.curOp.updateMaxLine = !0));
                (function(e, t) {
                    if (e.modeFrontier = Math.min(e.modeFrontier, t), !(e.highlightFrontier < t - 10)) {
                        for (var n = e.first, r = t - 1; n < r; r--) {
                            var i = Xe(e, r).stateAfter;
                            if (i && (!(i instanceof ft) || r + i.lookAhead < t)) {
                                n = r + 1;
                                break;
                            }
                        }
                        e.highlightFrontier = Math.min(e.highlightFrontier, n);
                    }
                })(r, o.line), zr(e, 400);
                a = t.text.length - (l.line - o.line) - 1;
                t.full ? tr(e) : o.line != l.line || 1 != t.text.length || pi(e.doc, t) ? tr(e, o.line, l.line + 1, a) : nr(e, o.line, "text");
                r = ke(e, "changes"), a = ke(e, "change");
                {
                    (a || r) && (t = {
                        from: o,
                        to: l,
                        text: t.text,
                        removed: t.removed,
                        origin: t.origin
                    }, a && ln(e, "change", e, t), r && (e.curOp.changeObjs || (e.curOp.changeObjs = [])).push(t));
                }
                e.display.selForContextMenu = null;
            }(e.cm, t, r) : gi(e, t, r), Ii(e, n, U));
        }
        function Yi(e, t, n, r, i) {
            var o;
            it(r = r || n, n) < 0 && (n = (o = [ r, n ])[0], r = o[1]), "string" == typeof t && (t = e.splitLines(t)), 
            qi(e, {
                from: n,
                to: r,
                text: t,
                origin: i
            });
        }
        function Zi(e, t, n, r) {
            n < e.line ? e.line += r : t < e.line && (e.line = t, e.ch = 0);
        }
        function Qi(e, t, n, r) {
            for (var i = 0; i < e.length; ++i) {
                var o = e[i], l = !0;
                if (o.ranges) {
                    o.copied || ((o = e[i] = o.deepCopy()).copied = !0);
                    for (var s = 0; s < o.ranges.length; s++) Zi(o.ranges[s].anchor, t, n, r), Zi(o.ranges[s].head, t, n, r);
                } else {
                    for (var a = 0; a < o.changes.length; ++a) {
                        var u = o.changes[a];
                        if (n < u.from.line) u.from = rt(u.from.line + r, u.from.ch), u.to = rt(u.to.line + r, u.to.ch); else if (t <= u.to.line) {
                            l = !1;
                            break;
                        }
                    }
                    l || (e.splice(0, i + 1), i = 0);
                }
            }
        }
        function Ji(e, t) {
            var n = t.from.line, r = t.to.line, t = t.text.length - (r - n) - 1;
            Qi(e.done, n, r, t), Qi(e.undone, n, r, t);
        }
        function eo(e, t, n, r) {
            var i = t, o = t;
            return "number" == typeof t ? o = Xe(e, ut(e, t)) : i = Je(t), null == i ? null : (r(o, i) && e.cm && nr(e.cm, i, n), 
            o);
        }
        function to(e) {
            this.lines = e, this.parent = null;
            for (var t = 0, n = 0; n < e.length; ++n) e[n].parent = this, t += e[n].height;
            this.height = t;
        }
        function no(e) {
            this.children = e;
            for (var t = 0, n = 0, r = 0; r < e.length; ++r) {
                var i = e[r];
                t += i.chunkSize(), n += i.height, i.parent = this;
            }
            this.size = t, this.height = n, this.parent = null;
        }
        oi.prototype.from = function() {
            return at(this.anchor, this.head);
        }, oi.prototype.to = function() {
            return st(this.anchor, this.head);
        }, oi.prototype.empty = function() {
            return this.head.line == this.anchor.line && this.head.ch == this.anchor.ch;
        }, to.prototype = {
            chunkSize: function() {
                return this.lines.length;
            },
            removeInner: function(e, t) {
                for (var n, r = e, i = e + t; r < i; ++r) {
                    var o = this.lines[r];
                    this.height -= o.height, (n = o).parent = null, At(n), ln(o, "delete");
                }
                this.lines.splice(e, t);
            },
            collapse: function(e) {
                e.push.apply(e, this.lines);
            },
            insertInner: function(e, t, n) {
                this.height += n, this.lines = this.lines.slice(0, e).concat(t).concat(this.lines.slice(e));
                for (var r = 0; r < t.length; ++r) t[r].parent = this;
            },
            iterN: function(e, t, n) {
                for (var r = e + t; e < r; ++e) if (n(this.lines[e])) return !0;
            }
        }, no.prototype = {
            chunkSize: function() {
                return this.size;
            },
            removeInner: function(e, t) {
                this.size -= t;
                for (var n, r = 0; r < this.children.length; ++r) {
                    var i = this.children[r], o = i.chunkSize();
                    if (e < o) {
                        var l = Math.min(t, o - e), s = i.height;
                        if (i.removeInner(e, l), this.height -= s - i.height, o == l && (this.children.splice(r--, 1), 
                        i.parent = null), 0 == (t -= l)) break;
                        e = 0;
                    } else e -= o;
                }
                this.size - t < 25 && (1 < this.children.length || !(this.children[0] instanceof to)) && (n = [], 
                this.collapse(n), this.children = [ new to(n) ], this.children[0].parent = this);
            },
            collapse: function(e) {
                for (var t = 0; t < this.children.length; ++t) this.children[t].collapse(e);
            },
            insertInner: function(e, t, n) {
                this.size += t.length, this.height += n;
                for (var r = 0; r < this.children.length; ++r) {
                    var i = this.children[r], o = i.chunkSize();
                    if (e <= o) {
                        if (i.insertInner(e, t, n), i.lines && 50 < i.lines.length) {
                            for (var l = i.lines.length % 25 + 25, s = l; s < i.lines.length; ) {
                                var a = new to(i.lines.slice(s, s += 25));
                                i.height -= a.height, this.children.splice(++r, 0, a), a.parent = this;
                            }
                            i.lines = i.lines.slice(0, l), this.maybeSpill();
                        }
                        break;
                    }
                    e -= o;
                }
            },
            maybeSpill: function() {
                if (!(this.children.length <= 10)) {
                    var e = this;
                    do {
                        var t, n = new no(e.children.splice(e.children.length - 5, 5));
                    } while (e.parent ? (e.size -= n.size, e.height -= n.height, t = R(e.parent.children, e), 
                    e.parent.children.splice(t + 1, 0, n)) : (((t = new no(e.children)).parent = e).children = [ t, n ], 
                    e = t), n.parent = e.parent, 10 < e.children.length);
                    e.parent.maybeSpill();
                }
            },
            iterN: function(e, t, n) {
                for (var r = 0; r < this.children.length; ++r) {
                    var i = this.children[r], o = i.chunkSize();
                    if (e < o) {
                        var l = Math.min(t, o - e);
                        if (i.iterN(e, l, n)) return !0;
                        if (0 == (t -= l)) break;
                        e = 0;
                    } else e -= o;
                }
            }
        };
        function ro(e, t, n) {
            if (n) for (var r in n) n.hasOwnProperty(r) && (this[r] = n[r]);
            this.doc = e, this.node = t;
        }
        function io(e, t, n) {
            Gt(t) < (e.curOp && e.curOp.scrollTop || e.doc.scrollTop) && br(e, n);
        }
        ro.prototype.clear = function() {
            var e = this.doc.cm, t = this.line.widgets, n = this.line, r = Je(n);
            if (null != r && t) {
                for (var i = 0; i < t.length; ++i) t[i] == this && t.splice(i--, 1);
                t.length || (n.widgets = null);
                var o = gn(this);
                Qe(n, Math.max(0, n.height - o)), e && (Hr(e, function() {
                    io(e, n, -o), nr(e, r, "widget");
                }), ln(e, "lineWidgetCleared", e, this, r));
            }
        }, ro.prototype.changed = function() {
            var e = this, t = this.height, n = this.doc.cm, r = this.line;
            this.height = null;
            var i = gn(this) - t;
            i && (Kt(this.doc, r) || Qe(r, r.height + i), n && Hr(n, function() {
                n.curOp.forceUpdate = !0, io(n, r, i), ln(n, "lineWidgetChanged", n, e, Je(r));
            }));
        }, Le(ro);
        var oo = 0, lo = function(e, t) {
            this.lines = [], this.type = t, this.doc = e, this.id = ++oo;
        };
        function so(t, n, r, e, i) {
            if (e && e.shared) return function(e, n, r, i, o) {
                (i = H(i)).shared = !1;
                var l = [ so(e, n, r, i, o) ], s = l[0], a = i.widgetNode;
                return mi(e, function(e) {
                    a && (i.widgetNode = a.cloneNode(!0)), l.push(so(e, ct(e, n), ct(e, r), i, o));
                    for (var t = 0; t < e.linked.length; ++t) if (e.linked[t].isParent) return;
                    s = _(l);
                }), new ao(l, s);
            }(t, n, r, e, i);
            if (t.cm && !t.cm.curOp) return Ir(t.cm, so)(t, n, r, e, i);
            var o = new lo(t, i), i = it(n, r);
            if (e && H(e, o, !1), 0 < i || 0 == i && !1 !== o.clearWhenEmpty) return o;
            if (o.replacedWith && (o.collapsed = !0, o.widgetNode = N("span", [ o.replacedWith ], "CodeMirror-widget"), 
            e.handleMouseEvents || o.widgetNode.setAttribute("cm-ignore-events", "true"), e.insertLeft && (o.widgetNode.insertLeft = !0)), 
            o.collapsed) {
                if (Rt(t, n.line, n, r, o) || n.line != r.line && Rt(t, r.line, n, r, o)) throw new Error("Inserting collapsed marker partially overlapping an existing one");
                Lt = !0;
            }
            o.addToHistory && Ci(t, {
                from: n,
                to: r,
                origin: "markText"
            }, t.sel, NaN);
            var l, s = n.line, a = t.cm;
            if (t.iter(s, r.line + 1, function(e) {
                var t;
                a && o.collapsed && !a.options.lineWrapping && zt(e) == a.display.maxLine && (l = !0), 
                o.collapsed && s != n.line && Qe(e, 0), t = e, e = new Mt(o, s == n.line ? n.ch : null, s == r.line ? r.ch : null), 
                t.markedSpans = t.markedSpans ? t.markedSpans.concat([ e ]) : [ e ], e.marker.attachLine(t), 
                ++s;
            }), o.collapsed && t.iter(n.line, r.line + 1, function(e) {
                Kt(t, e) && Qe(e, 0);
            }), o.clearOnEnter && ye(o, "beforeCursorEnter", function() {
                return o.clear();
            }), o.readOnly && (kt = !0, (t.history.done.length || t.history.undone.length) && t.clearHistory()), 
            o.collapsed && (o.id = ++oo, o.atomic = !0), a) {
                if (l && (a.curOp.updateMaxLine = !0), o.collapsed) tr(a, n.line, r.line + 1); else if (o.className || o.startStyle || o.endStyle || o.css || o.attributes || o.title) for (var u = n.line; u <= r.line; u++) nr(a, u, "text");
                o.atomic && Ri(a.doc), ln(a, "markerAdded", a, o);
            }
            return o;
        }
        lo.prototype.clear = function() {
            var e = this;
            if (!this.explicitlyCleared) {
                var t, n = this.doc.cm, r = n && !n.curOp;
                r && Wr(n), !ke(this, "clear") || (t = this.find()) && ln(this, "clear", t.from, t.to);
                for (var i = null, o = null, l = 0; l < this.lines.length; ++l) {
                    var s = e.lines[l], a = Tt(s.markedSpans, e);
                    n && !e.collapsed ? nr(n, Je(s), "text") : n && (null != a.to && (o = Je(s)), null != a.from && (i = Je(s))), 
                    s.markedSpans = function(e, t) {
                        for (var n, r = 0; r < e.length; ++r) e[r] != t && (n = n || []).push(e[r]);
                        return n;
                    }(s.markedSpans, a), null == a.from && e.collapsed && !Kt(e.doc, s) && n && Qe(s, _n(n.display));
                }
                if (n && this.collapsed && !n.options.lineWrapping) for (var u = 0; u < this.lines.length; ++u) {
                    var c = zt(e.lines[u]), h = Vt(c);
                    h > n.display.maxLineLength && (n.display.maxLine = c, n.display.maxLineLength = h, 
                    n.display.maxLineChanged = !0);
                }
                null != i && n && this.collapsed && tr(n, i, o + 1), this.lines.length = 0, this.explicitlyCleared = !0, 
                this.atomic && this.doc.cantEdit && (this.doc.cantEdit = !1, n && Ri(n.doc)), n && ln(n, "markerCleared", n, this, i, o), 
                r && Pr(n), this.parent && this.parent.clear();
            }
        }, lo.prototype.find = function(e, t) {
            var n, r;
            null == e && "bookmark" == this.type && (e = 1);
            for (var i = 0; i < this.lines.length; ++i) {
                var o = this.lines[i], l = Tt(o.markedSpans, this);
                if (null != l.from && (n = rt(t ? o : Je(o), l.from), -1 == e)) return n;
                if (null != l.to && (r = rt(t ? o : Je(o), l.to), 1 == e)) return r;
            }
            return n && {
                from: n,
                to: r
            };
        }, lo.prototype.changed = function() {
            var n = this, r = this.find(-1, !0), i = this, o = this.doc.cm;
            r && o && Hr(o, function() {
                var e = r.line, t = Je(r.line), t = Ln(o, t);
                t && (Fn(t), o.curOp.selectionChanged = o.curOp.forceUpdate = !0), o.curOp.updateMaxLine = !0, 
                Kt(i.doc, e) || null == i.height || (t = i.height, i.height = null, (t = gn(i) - t) && Qe(e, e.height + t)), 
                ln(o, "markerChanged", o, n);
            });
        }, lo.prototype.attachLine = function(e) {
            var t;
            !this.lines.length && this.doc.cm && ((t = this.doc.cm.curOp).maybeHiddenMarkers && -1 != R(t.maybeHiddenMarkers, this) || (t.maybeUnhiddenMarkers || (t.maybeUnhiddenMarkers = [])).push(this)), 
            this.lines.push(e);
        }, lo.prototype.detachLine = function(e) {
            this.lines.splice(R(this.lines, e), 1), !this.lines.length && this.doc.cm && ((e = this.doc.cm.curOp).maybeHiddenMarkers || (e.maybeHiddenMarkers = [])).push(this);
        }, Le(lo);
        var ao = function(e, t) {
            this.markers = e, this.primary = t;
            for (var n = 0; n < e.length; ++n) e[n].parent = this;
        };
        function uo(e) {
            return e.findMarks(rt(e.first, 0), e.clipPos(rt(e.lastLine())), function(e) {
                return e.parent;
            });
        }
        ao.prototype.clear = function() {
            if (!this.explicitlyCleared) {
                this.explicitlyCleared = !0;
                for (var e = 0; e < this.markers.length; ++e) this.markers[e].clear();
                ln(this, "clear");
            }
        }, ao.prototype.find = function(e, t) {
            return this.primary.find(e, t);
        }, Le(ao);
        var co = 0, ho = function(e, t, n, r, i) {
            if (!(this instanceof ho)) return new ho(e, t, n, r, i);
            null == n && (n = 0), no.call(this, [ new to([ new jt("", null) ]) ]), this.first = n, 
            this.scrollTop = this.scrollLeft = 0, this.cantEdit = !1, this.cleanGeneration = 1;
            n = rt(this.modeFrontier = this.highlightFrontier = n, 0);
            this.sel = si(n), this.history = new bi(null), this.id = ++co, this.modeOption = t, 
            this.lineSep = r, this.direction = "rtl" == i ? "rtl" : "ltr", this.extend = !1, 
            "string" == typeof e && (e = this.splitLines(e)), gi(this, {
                from: n,
                to: n,
                text: e
            }), Hi(this, si(n), U);
        };
        ho.prototype = Y(no.prototype, {
            constructor: ho,
            iter: function(e, t, n) {
                n ? this.iterN(e - this.first, t - e, n) : this.iterN(this.first, this.first + this.size, e);
            },
            insert: function(e, t) {
                for (var n = 0, r = 0; r < t.length; ++r) n += t[r].height;
                this.insertInner(e - this.first, t, n);
            },
            remove: function(e, t) {
                this.removeInner(e - this.first, t);
            },
            getValue: function(e) {
                var t = Ze(this, this.first, this.first + this.size);
                return !1 === e ? t : t.join(e || this.lineSeparator());
            },
            setValue: Rr(function(e) {
                var t = rt(this.first, 0), n = this.first + this.size - 1;
                qi(this, {
                    from: t,
                    to: rt(n, Xe(this, n).text.length),
                    text: this.splitLines(e),
                    origin: "setValue",
                    full: !0
                }, !0), this.cm && xr(this.cm, 0, 0), Hi(this, si(t), U);
            }),
            replaceRange: function(e, t, n, r) {
                Yi(this, e, t = ct(this, t), n = n ? ct(this, n) : t, r);
            },
            getRange: function(e, t, n) {
                t = Ye(this, ct(this, e), ct(this, t));
                return !1 === n ? t : t.join(n || this.lineSeparator());
            },
            getLine: function(e) {
                e = this.getLineHandle(e);
                return e && e.text;
            },
            getLineHandle: function(e) {
                if (tt(this, e)) return Xe(this, e);
            },
            getLineNumber: Je,
            getLineHandleVisualStart: function(e) {
                return "number" == typeof e && (e = Xe(this, e)), zt(e);
            },
            lineCount: function() {
                return this.size;
            },
            firstLine: function() {
                return this.first;
            },
            lastLine: function() {
                return this.first + this.size - 1;
            },
            clipPos: function(e) {
                return ct(this, e);
            },
            getCursor: function(e) {
                var t = this.sel.primary(), t = null == e || "head" == e ? t.head : "anchor" == e ? t.anchor : "end" == e || "to" == e || !1 === e ? t.to() : t.from();
                return t;
            },
            listSelections: function() {
                return this.sel.ranges;
            },
            somethingSelected: function() {
                return this.sel.somethingSelected();
            },
            setCursor: Rr(function(e, t, n) {
                Wi(this, ct(this, "number" == typeof e ? rt(e, t || 0) : e), null, n);
            }),
            setSelection: Rr(function(e, t, n) {
                Wi(this, ct(this, e), ct(this, t || e), n);
            }),
            extendSelection: Rr(function(e, t, n) {
                Ai(this, ct(this, e), t && ct(this, t), n);
            }),
            extendSelections: Rr(function(e, t) {
                Fi(this, ht(this, e), t);
            }),
            extendSelectionsBy: Rr(function(e, t) {
                Fi(this, ht(this, $(this.sel.ranges, e)), t);
            }),
            setSelections: Rr(function(e, t, n) {
                if (e.length) {
                    for (var r = [], i = 0; i < e.length; i++) r[i] = new oi(ct(this, e[i].anchor), ct(this, e[i].head));
                    null == t && (t = Math.min(e.length - 1, this.sel.primIndex)), Hi(this, li(this.cm, r, t), n);
                }
            }),
            addSelection: Rr(function(e, t, n) {
                var r = this.sel.ranges.slice(0);
                r.push(new oi(ct(this, e), ct(this, t || e))), Hi(this, li(this.cm, r, r.length - 1), n);
            }),
            getSelection: function(e) {
                for (var t = this.sel.ranges, n = 0; n < t.length; n++) var r = Ye(this, t[n].from(), t[n].to()), i = i ? i.concat(r) : r;
                return !1 === e ? i : i.join(e || this.lineSeparator());
            },
            getSelections: function(e) {
                for (var t = [], n = this.sel.ranges, r = 0; r < n.length; r++) {
                    var i = Ye(this, n[r].from(), n[r].to());
                    !1 !== e && (i = i.join(e || this.lineSeparator())), t[r] = i;
                }
                return t;
            },
            replaceSelection: function(e, t, n) {
                for (var r = [], i = 0; i < this.sel.ranges.length; i++) r[i] = e;
                this.replaceSelections(r, t, n || "+input");
            },
            replaceSelections: Rr(function(e, t, n) {
                for (var r = [], i = this.sel, o = 0; o < i.ranges.length; o++) {
                    var l = i.ranges[o];
                    r[o] = {
                        from: l.from(),
                        to: l.to(),
                        text: this.splitLines(e[o]),
                        origin: n
                    };
                }
                for (var t = t && "end" != t && function(e, t, n) {
                    for (var r = [], i = u = rt(e.first, 0), o = 0; o < t.length; o++) {
                        var l = t[o], s = hi(l.from, u, i), a = hi(ai(l), u, i), u = l.to, i = a;
                        "around" == n ? (l = it((l = e.sel.ranges[o]).head, l.anchor) < 0, r[o] = new oi(l ? a : s, l ? s : a)) : r[o] = new oi(s, s);
                    }
                    return new ii(r, e.sel.primIndex);
                }(this, r, t), s = r.length - 1; 0 <= s; s--) qi(this, r[s]);
                t ? Pi(this, t) : this.cm && wr(this.cm);
            }),
            undo: Rr(function() {
                _i(this, "undo");
            }),
            redo: Rr(function() {
                _i(this, "redo");
            }),
            undoSelection: Rr(function() {
                _i(this, "undo", !0);
            }),
            redoSelection: Rr(function() {
                _i(this, "redo", !0);
            }),
            setExtending: function(e) {
                this.extend = e;
            },
            getExtending: function() {
                return this.extend;
            },
            historySize: function() {
                for (var e = this.history, t = 0, n = 0, r = 0; r < e.done.length; r++) e.done[r].ranges || ++t;
                for (var i = 0; i < e.undone.length; i++) e.undone[i].ranges || ++n;
                return {
                    undo: t,
                    redo: n
                };
            },
            clearHistory: function() {
                this.history = new bi(this.history.maxGeneration);
            },
            markClean: function() {
                this.cleanGeneration = this.changeGeneration(!0);
            },
            changeGeneration: function(e) {
                return e && (this.history.lastOp = this.history.lastSelOp = this.history.lastOrigin = null), 
                this.history.generation;
            },
            isClean: function(e) {
                return this.history.generation == (e || this.cleanGeneration);
            },
            getHistory: function() {
                return {
                    done: Ni(this.history.done),
                    undone: Ni(this.history.undone)
                };
            },
            setHistory: function(e) {
                var t = this.history = new bi(this.history.maxGeneration);
                t.done = Ni(e.done.slice(0), null, !0), t.undone = Ni(e.undone.slice(0), null, !0);
            },
            setGutterMarker: Rr(function(e, n, r) {
                return eo(this, e, "gutter", function(e) {
                    var t = e.gutterMarkers || (e.gutterMarkers = {});
                    return !(t[n] = r) && ee(t) && (e.gutterMarkers = null), 1;
                });
            }),
            clearGutter: Rr(function(t) {
                var n = this;
                this.iter(function(e) {
                    e.gutterMarkers && e.gutterMarkers[t] && eo(n, e, "gutter", function() {
                        return e.gutterMarkers[t] = null, ee(e.gutterMarkers) && (e.gutterMarkers = null), 
                        1;
                    });
                });
            }),
            lineInfo: function(e) {
                var t;
                if ("number" == typeof e) {
                    if (!tt(this, e)) return null;
                    if (!(e = Xe(this, t = e))) return null;
                } else if (null == (t = Je(e))) return null;
                return {
                    line: t,
                    handle: e,
                    text: e.text,
                    gutterMarkers: e.gutterMarkers,
                    textClass: e.textClass,
                    bgClass: e.bgClass,
                    wrapClass: e.wrapClass,
                    widgets: e.widgets
                };
            },
            addLineClass: Rr(function(e, n, r) {
                return eo(this, e, "gutter" == n ? "gutter" : "class", function(e) {
                    var t = "text" == n ? "textClass" : "background" == n ? "bgClass" : "gutter" == n ? "gutterClass" : "wrapClass";
                    if (e[t]) {
                        if (C(r).test(e[t])) return;
                        e[t] += " " + r;
                    } else e[t] = r;
                    return 1;
                });
            }),
            removeLineClass: Rr(function(e, o, l) {
                return eo(this, e, "gutter" == o ? "gutter" : "class", function(e) {
                    var t = "text" == o ? "textClass" : "background" == o ? "bgClass" : "gutter" == o ? "gutterClass" : "wrapClass", n = e[t];
                    if (n) {
                        if (null == l) e[t] = null; else {
                            var r = n.match(C(l));
                            if (!r) return;
                            var i = r.index + r[0].length;
                            e[t] = n.slice(0, r.index) + (r.index && i != n.length ? " " : "") + n.slice(i) || null;
                        }
                        return 1;
                    }
                });
            }),
            addLineWidget: Rr(function(e, t, n) {
                return e = e, i = new ro(r = this, t, n), (o = r.cm) && i.noHScroll && (o.display.alignWidgets = !0), 
                eo(r, e, "widget", function(e) {
                    var t = e.widgets || (e.widgets = []);
                    return null == i.insertAt ? t.push(i) : t.splice(Math.min(t.length - 1, Math.max(0, i.insertAt)), 0, i), 
                    i.line = e, o && !Kt(r, e) && (t = Gt(e) < r.scrollTop, Qe(e, e.height + gn(i)), 
                    t && br(o, i.height), o.curOp.forceUpdate = !0), 1;
                }), o && ln(o, "lineWidgetAdded", o, i, "number" == typeof e ? e : Je(e)), i;
                var r, i, o;
            }),
            removeLineWidget: function(e) {
                e.clear();
            },
            markText: function(e, t, n) {
                return so(this, ct(this, e), ct(this, t), n, n && n.type || "range");
            },
            setBookmark: function(e, t) {
                t = {
                    replacedWith: t && (null == t.nodeType ? t.widget : t),
                    insertLeft: t && t.insertLeft,
                    clearWhenEmpty: !1,
                    shared: t && t.shared,
                    handleMouseEvents: t && t.handleMouseEvents
                };
                return so(this, e = ct(this, e), e, t, "bookmark");
            },
            findMarksAt: function(e) {
                var t = [], n = Xe(this, (e = ct(this, e)).line).markedSpans;
                if (n) for (var r = 0; r < n.length; ++r) {
                    var i = n[r];
                    (null == i.from || i.from <= e.ch) && (null == i.to || i.to >= e.ch) && t.push(i.marker.parent || i.marker);
                }
                return t;
            },
            findMarks: function(i, o, l) {
                i = ct(this, i), o = ct(this, o);
                var s = [], a = i.line;
                return this.iter(i.line, o.line + 1, function(e) {
                    var t = e.markedSpans;
                    if (t) for (var n = 0; n < t.length; n++) {
                        var r = t[n];
                        null != r.to && a == i.line && i.ch >= r.to || null == r.from && a != i.line || null != r.from && a == o.line && r.from >= o.ch || l && !l(r.marker) || s.push(r.marker.parent || r.marker);
                    }
                    ++a;
                }), s;
            },
            getAllMarks: function() {
                var r = [];
                return this.iter(function(e) {
                    var t = e.markedSpans;
                    if (t) for (var n = 0; n < t.length; ++n) null != t[n].from && r.push(t[n].marker);
                }), r;
            },
            posFromIndex: function(t) {
                var n, r = this.first, i = this.lineSeparator().length;
                return this.iter(function(e) {
                    e = e.text.length + i;
                    if (t < e) return n = t, !0;
                    t -= e, ++r;
                }), ct(this, rt(r, n));
            },
            indexFromPos: function(e) {
                var t = (e = ct(this, e)).ch;
                if (e.line < this.first || e.ch < 0) return 0;
                var n = this.lineSeparator().length;
                return this.iter(this.first, e.line, function(e) {
                    t += e.text.length + n;
                }), t;
            },
            copy: function(e) {
                var t = new ho(Ze(this, this.first, this.first + this.size), this.modeOption, this.first, this.lineSep, this.direction);
                return t.scrollTop = this.scrollTop, t.scrollLeft = this.scrollLeft, t.sel = this.sel, 
                t.extend = !1, e && (t.history.undoDepth = this.history.undoDepth, t.setHistory(this.getHistory())), 
                t;
            },
            linkedDoc: function(e) {
                e = e || {};
                var t = this.first, n = this.first + this.size;
                null != e.from && e.from > t && (t = e.from), null != e.to && e.to < n && (n = e.to);
                t = new ho(Ze(this, t, n), e.mode || this.modeOption, t, this.lineSep, this.direction);
                return e.sharedHist && (t.history = this.history), (this.linked || (this.linked = [])).push({
                    doc: t,
                    sharedHist: e.sharedHist
                }), t.linked = [ {
                    doc: this,
                    isParent: !0,
                    sharedHist: e.sharedHist
                } ], function(e, t) {
                    for (var n = 0; n < t.length; n++) {
                        var r = t[n], i = r.find(), o = e.clipPos(i.from), i = e.clipPos(i.to);
                        it(o, i) && (i = so(e, o, i, r.primary, r.primary.type), r.markers.push(i), i.parent = r);
                    }
                }(t, uo(this)), t;
            },
            unlinkDoc: function(e) {
                var t;
                if (e instanceof ul && (e = e.doc), this.linked) for (var n = 0; n < this.linked.length; ++n) {
                    if (this.linked[n].doc == e) {
                        this.linked.splice(n, 1), e.unlinkDoc(this), function(o) {
                            for (var e = 0; e < o.length; e++) !function(e) {
                                var t = o[e], n = [ t.primary.doc ];
                                mi(t.primary.doc, function(e) {
                                    return n.push(e);
                                });
                                for (var r = 0; r < t.markers.length; r++) {
                                    var i = t.markers[r];
                                    -1 == R(n, i.doc) && (i.parent = null, t.markers.splice(r--, 1));
                                }
                            }(e);
                        }(uo(this));
                        break;
                    }
                }
                e.history == this.history && (t = [ e.id ], mi(e, function(e) {
                    return t.push(e.id);
                }, !0), e.history = new bi(null), e.history.done = Ni(this.history.done, t), e.history.undone = Ni(this.history.undone, t));
            },
            iterLinkedDocs: function(e) {
                mi(this, e);
            },
            getMode: function() {
                return this.mode;
            },
            getEditor: function() {
                return this.cm;
            },
            splitLines: function(e) {
                return this.lineSep ? e.split(this.lineSep) : He(e);
            },
            lineSeparator: function() {
                return this.lineSep || "\n";
            },
            setDirection: Rr(function(e) {
                var t;
                "rtl" != e && (e = "ltr"), e != this.direction && (this.direction = e, this.iter(function(e) {
                    return e.order = null;
                }), this.cm && Hr(t = this.cm, function() {
                    yi(t), tr(t);
                }));
            })
        }), ho.prototype.eachLine = ho.prototype.iter;
        var fo = 0;
        function po(e) {
            var r = this;
            if (go(r), !Ce(r, e) && !mn(r.display, e)) {
                Me(e), v && (fo = +new Date());
                var i = Jn(r, e, !0), t = e.dataTransfer.files;
                if (i && !r.isReadOnly()) if (t && t.length && window.FileReader && window.File) for (var o = t.length, l = Array(o), s = 0, n = 0; n < o; ++n) !function(e, t) {
                    var n;
                    r.options.allowDropFileTypes && -1 == R(r.options.allowDropFileTypes, e.type) || ((n = new FileReader()).onload = Ir(r, function() {
                        var e = n.result;
                        /[\x00-\x08\x0e-\x1f]{2}/.test(e) && (e = ""), l[t] = e, ++s == o && (e = {
                            from: i = ct(r.doc, i),
                            to: i,
                            text: r.doc.splitLines(l.join(r.doc.lineSeparator())),
                            origin: "paste"
                        }, qi(r.doc, e), Pi(r.doc, si(i, ai(e))));
                    }), n.readAsText(e));
                }(t[n], n); else {
                    if (r.state.draggingText && -1 < r.doc.sel.contains(i)) return r.state.draggingText(e), 
                    void setTimeout(function() {
                        return r.display.input.focus();
                    }, 20);
                    try {
                        var a, u = e.dataTransfer.getData("Text");
                        if (u) {
                            if (r.state.draggingText && !r.state.draggingText.copy && (a = r.listSelections()), 
                            Ii(r.doc, si(i, i)), a) for (var c = 0; c < a.length; ++c) Yi(r.doc, "", a[c].anchor, a[c].head, "drag");
                            r.replaceSelection(u, "around", "paste"), r.display.input.focus();
                        }
                    } catch (e) {}
                }
            }
        }
        function go(e) {
            e.display.dragCursor && (e.display.lineSpace.removeChild(e.display.dragCursor), 
            e.display.dragCursor = null);
        }
        function mo(t) {
            if (document.getElementsByClassName) {
                for (var e = document.getElementsByClassName("CodeMirror"), n = [], r = 0; r < e.length; r++) {
                    var i = e[r].CodeMirror;
                    i && n.push(i);
                }
                n.length && n[0].operation(function() {
                    for (var e = 0; e < n.length; e++) t(n[e]);
                });
            }
        }
        var vo = !1;
        function yo() {
            var e;
            vo || (ye(window, "resize", function() {
                null == e && (e = setTimeout(function() {
                    e = null, mo(bo);
                }, 100));
            }), ye(window, "blur", function() {
                return mo(pr);
            }), vo = !0);
        }
        function bo(e) {
            var t = e.display;
            t.cachedCharWidth = t.cachedTextHeight = t.cachedPaddingH = null, t.scrollbarsClipped = !1, 
            e.setSize();
        }
        for (var wo = {
            3: "Pause",
            8: "Backspace",
            9: "Tab",
            13: "Enter",
            16: "Shift",
            17: "Ctrl",
            18: "Alt",
            19: "Pause",
            20: "CapsLock",
            27: "Esc",
            32: "Space",
            33: "PageUp",
            34: "PageDown",
            35: "End",
            36: "Home",
            37: "Left",
            38: "Up",
            39: "Right",
            40: "Down",
            44: "PrintScrn",
            45: "Insert",
            46: "Delete",
            59: ";",
            61: "=",
            91: "Mod",
            92: "Mod",
            93: "Mod",
            106: "*",
            107: "=",
            109: "-",
            110: ".",
            111: "/",
            145: "ScrollLock",
            173: "-",
            186: ";",
            187: "=",
            188: ",",
            189: "-",
            190: ".",
            191: "/",
            192: "`",
            219: "[",
            220: "\\",
            221: "]",
            222: "'",
            63232: "Up",
            63233: "Down",
            63234: "Left",
            63235: "Right",
            63272: "Delete",
            63273: "Home",
            63275: "End",
            63276: "PageUp",
            63277: "PageDown",
            63302: "Insert"
        }, xo = 0; xo < 10; xo++) wo[xo + 48] = wo[xo + 96] = String(xo);
        for (var Co = 65; Co <= 90; Co++) wo[Co] = String.fromCharCode(Co);
        for (var So = 1; So <= 12; So++) wo[So + 111] = wo[So + 63235] = "F" + So;
        var ko = {};
        function Lo(e) {
            var t, n, r, i, o = e.split(/-(?!$)/);
            e = o[o.length - 1];
            for (var l = 0; l < o.length - 1; l++) {
                var s = o[l];
                if (/^(cmd|meta|m)$/i.test(s)) i = !0; else if (/^a(lt)?$/i.test(s)) t = !0; else if (/^(c|ctrl|control)$/i.test(s)) n = !0; else {
                    if (!/^s(hift)?$/i.test(s)) throw new Error("Unrecognized modifier name: " + s);
                    r = !0;
                }
            }
            return t && (e = "Alt-" + e), n && (e = "Ctrl-" + e), i && (e = "Cmd-" + e), r && (e = "Shift-" + e), 
            e;
        }
        function Mo(e) {
            var t, n, r = {};
            for (t in e) if (e.hasOwnProperty(t)) {
                var i = e[t];
                if (!/^(name|fallthrough|(de|at)tach)$/.test(t)) if ("..." != i) {
                    for (var o = $(t.split(" "), Lo), l = 0; l < o.length; l++) {
                        var s = void 0, a = void 0, s = l == o.length - 1 ? (a = o.join(" "), i) : (a = o.slice(0, l + 1).join(" "), 
                        "..."), u = r[a];
                        if (u) {
                            if (u != s) throw new Error("Inconsistent bindings for " + a);
                        } else r[a] = s;
                    }
                    delete e[t];
                } else delete e[t];
            }
            for (n in r) e[n] = r[n];
            return e;
        }
        function To(e, t, n, r) {
            var i = (t = Fo(t)).call ? t.call(e, r) : t[e];
            if (!1 === i) return "nothing";
            if ("..." === i) return "multi";
            if (null != i && n(i)) return "handled";
            if (t.fallthrough) {
                if ("[object Array]" != Object.prototype.toString.call(t.fallthrough)) return To(e, t.fallthrough, n, r);
                for (var o = 0; o < t.fallthrough.length; o++) {
                    var l = To(e, t.fallthrough[o], n, r);
                    if (l) return l;
                }
            }
        }
        function No(e) {
            e = "string" == typeof e ? e : wo[e.keyCode];
            return "Ctrl" == e || "Alt" == e || "Shift" == e || "Mod" == e;
        }
        function Oo(e, t, n) {
            var r = e;
            return t.altKey && "Alt" != r && (e = "Alt-" + e), (w ? t.metaKey : t.ctrlKey) && "Ctrl" != r && (e = "Ctrl-" + e), 
            (w ? t.ctrlKey : t.metaKey) && "Cmd" != r && (e = "Cmd-" + e), !n && t.shiftKey && "Shift" != r && (e = "Shift-" + e), 
            e;
        }
        function Ao(e, t) {
            if (p && 34 == e.keyCode && e.char) return !1;
            var n = wo[e.keyCode];
            return null != n && !e.altGraphKey && (3 == e.keyCode && e.code && (n = e.code), 
            Oo(n, e, t));
        }
        function Fo(e) {
            return "string" == typeof e ? ko[e] : e;
        }
        function Do(t, e) {
            for (var n = t.doc.sel.ranges, r = [], i = 0; i < n.length; i++) {
                for (var o = e(n[i]); r.length && it(o.from, _(r).to) <= 0; ) {
                    var l = r.pop();
                    if (it(l.from, o.from) < 0) {
                        o.from = l.from;
                        break;
                    }
                }
                r.push(o);
            }
            Hr(t, function() {
                for (var e = r.length - 1; 0 <= e; e--) Yi(t.doc, "", r[e].from, r[e].to, "+delete");
                wr(t);
            });
        }
        function Wo(e, t, n) {
            n = re(e.text, t + n, n);
            return n < 0 || n > e.text.length ? null : n;
        }
        function Po(e, t, n) {
            e = Wo(e, t.ch, n);
            return null == e ? null : new rt(t.line, e, n < 0 ? "after" : "before");
        }
        function Ho(e, t, n, r, i) {
            if (e) {
                var o = me(n, t.doc.direction);
                if (o) {
                    var l, s, a, e = i < 0 ? _(o) : o[0], o = i < 0 == (1 == e.level) ? "after" : "before";
                    return 0 < e.level || "rtl" == t.doc.direction ? (l = Mn(t, n), s = i < 0 ? n.text.length - 1 : 0, 
                    a = Tn(t, l, s).top, s = ie(function(e) {
                        return Tn(t, l, e).top == a;
                    }, i < 0 == (1 == e.level) ? e.from : e.to - 1, s), "before" == o && (s = Wo(n, s, 1))) : s = i < 0 ? e.to : e.from, 
                    new rt(r, s, o);
                }
            }
            return new rt(r, i < 0 ? n.text.length : 0, i < 0 ? "before" : "after");
        }
        function Io(t, n, s, e) {
            var a = me(n, t.doc.direction);
            if (!a) return Po(n, s, e);
            s.ch >= n.text.length ? (s.ch = n.text.length, s.sticky = "before") : s.ch <= 0 && (s.ch = 0, 
            s.sticky = "after");
            var r = le(a, s.ch, s.sticky), i = a[r];
            if ("ltr" == t.doc.direction && i.level % 2 == 0 && (0 < e ? i.to > s.ch : i.from < s.ch)) return Po(n, s, e);
            function u(e, t) {
                return Wo(n, e instanceof rt ? e.ch : e, t);
            }
            function o(e) {
                return t.options.lineWrapping ? (l = l || Mn(t, n), qn(t, n, l, e)) : {
                    begin: 0,
                    end: n.text.length
                };
            }
            var l, c = o("before" == s.sticky ? u(s, -1) : s.ch);
            if ("rtl" == t.doc.direction || 1 == i.level) {
                var h = 1 == i.level == e < 0, f = u(s, h ? 1 : -1);
                if (null != f && (h ? f <= i.to && f <= c.end : f >= i.from && f >= c.begin)) {
                    var d = h ? "before" : "after";
                    return new rt(s.line, f, d);
                }
            }
            d = function(e, t, n) {
                for (var r = function(e, t) {
                    return t ? new rt(s.line, u(e, 1), "before") : new rt(s.line, e, "after");
                }; 0 <= e && e < a.length; e += t) {
                    var i = a[e], o = 0 < t == (1 != i.level), l = o ? n.begin : u(n.end, -1);
                    if (i.from <= l && l < i.to) return r(l, o);
                    if (l = o ? i.from : u(i.to, -1), n.begin <= l && l < n.end) return r(l, o);
                }
            }, r = d(r + e, e, c);
            if (r) return r;
            c = 0 < e ? c.end : u(c.begin, -1);
            return null == c || 0 < e && c == n.text.length || !(r = d(0 < e ? 0 : a.length - 1, e, o(c))) ? null : r;
        }
        ko.basic = {
            Left: "goCharLeft",
            Right: "goCharRight",
            Up: "goLineUp",
            Down: "goLineDown",
            End: "goLineEnd",
            Home: "goLineStartSmart",
            PageUp: "goPageUp",
            PageDown: "goPageDown",
            Delete: "delCharAfter",
            Backspace: "delCharBefore",
            "Shift-Backspace": "delCharBefore",
            Tab: "defaultTab",
            "Shift-Tab": "indentAuto",
            Enter: "newlineAndIndent",
            Insert: "toggleOverwrite",
            Esc: "singleSelection"
        }, ko.pcDefault = {
            "Ctrl-A": "selectAll",
            "Ctrl-D": "deleteLine",
            "Ctrl-Z": "undo",
            "Shift-Ctrl-Z": "redo",
            "Ctrl-Y": "redo",
            "Ctrl-Home": "goDocStart",
            "Ctrl-End": "goDocEnd",
            "Ctrl-Up": "goLineUp",
            "Ctrl-Down": "goLineDown",
            "Ctrl-Left": "goGroupLeft",
            "Ctrl-Right": "goGroupRight",
            "Alt-Left": "goLineStart",
            "Alt-Right": "goLineEnd",
            "Ctrl-Backspace": "delGroupBefore",
            "Ctrl-Delete": "delGroupAfter",
            "Ctrl-S": "save",
            "Ctrl-F": "find",
            "Ctrl-G": "findNext",
            "Shift-Ctrl-G": "findPrev",
            "Shift-Ctrl-F": "replace",
            "Shift-Ctrl-R": "replaceAll",
            "Ctrl-[": "indentLess",
            "Ctrl-]": "indentMore",
            "Ctrl-U": "undoSelection",
            "Shift-Ctrl-U": "redoSelection",
            "Alt-U": "redoSelection",
            fallthrough: "basic"
        }, ko.emacsy = {
            "Ctrl-F": "goCharRight",
            "Ctrl-B": "goCharLeft",
            "Ctrl-P": "goLineUp",
            "Ctrl-N": "goLineDown",
            "Alt-F": "goWordRight",
            "Alt-B": "goWordLeft",
            "Ctrl-A": "goLineStart",
            "Ctrl-E": "goLineEnd",
            "Ctrl-V": "goPageDown",
            "Shift-Ctrl-V": "goPageUp",
            "Ctrl-D": "delCharAfter",
            "Ctrl-H": "delCharBefore",
            "Alt-D": "delWordAfter",
            "Alt-Backspace": "delWordBefore",
            "Ctrl-K": "killLine",
            "Ctrl-T": "transposeChars",
            "Ctrl-O": "openLine"
        }, ko.macDefault = {
            "Cmd-A": "selectAll",
            "Cmd-D": "deleteLine",
            "Cmd-Z": "undo",
            "Shift-Cmd-Z": "redo",
            "Cmd-Y": "redo",
            "Cmd-Home": "goDocStart",
            "Cmd-Up": "goDocStart",
            "Cmd-End": "goDocEnd",
            "Cmd-Down": "goDocEnd",
            "Alt-Left": "goGroupLeft",
            "Alt-Right": "goGroupRight",
            "Cmd-Left": "goLineLeft",
            "Cmd-Right": "goLineRight",
            "Alt-Backspace": "delGroupBefore",
            "Ctrl-Alt-Backspace": "delGroupAfter",
            "Alt-Delete": "delGroupAfter",
            "Cmd-S": "save",
            "Cmd-F": "find",
            "Cmd-G": "findNext",
            "Shift-Cmd-G": "findPrev",
            "Cmd-Alt-F": "replace",
            "Shift-Cmd-Alt-F": "replaceAll",
            "Cmd-[": "indentLess",
            "Cmd-]": "indentMore",
            "Cmd-Backspace": "delWrappedLineLeft",
            "Cmd-Delete": "delWrappedLineRight",
            "Cmd-U": "undoSelection",
            "Shift-Cmd-U": "redoSelection",
            "Ctrl-Up": "goDocStart",
            "Ctrl-Down": "goDocEnd",
            fallthrough: [ "basic", "emacsy" ]
        }, ko.default = g ? ko.macDefault : ko.pcDefault;
        var Eo = {
            selectAll: Gi,
            singleSelection: function(e) {
                return e.setSelection(e.getCursor("anchor"), e.getCursor("head"), U);
            },
            killLine: function(n) {
                return Do(n, function(e) {
                    if (e.empty()) {
                        var t = Xe(n.doc, e.head.line).text.length;
                        return e.head.ch == t && e.head.line < n.lastLine() ? {
                            from: e.head,
                            to: rt(e.head.line + 1, 0)
                        } : {
                            from: e.head,
                            to: rt(e.head.line, t)
                        };
                    }
                    return {
                        from: e.from(),
                        to: e.to()
                    };
                });
            },
            deleteLine: function(t) {
                return Do(t, function(e) {
                    return {
                        from: rt(e.from().line, 0),
                        to: ct(t.doc, rt(e.to().line + 1, 0))
                    };
                });
            },
            delLineLeft: function(e) {
                return Do(e, function(e) {
                    return {
                        from: rt(e.from().line, 0),
                        to: e.from()
                    };
                });
            },
            delWrappedLineLeft: function(n) {
                return Do(n, function(e) {
                    var t = n.charCoords(e.head, "div").top + 5;
                    return {
                        from: n.coordsChar({
                            left: 0,
                            top: t
                        }, "div"),
                        to: e.from()
                    };
                });
            },
            delWrappedLineRight: function(n) {
                return Do(n, function(e) {
                    var t = n.charCoords(e.head, "div").top + 5, t = n.coordsChar({
                        left: n.display.lineDiv.offsetWidth + 100,
                        top: t
                    }, "div");
                    return {
                        from: e.from(),
                        to: t
                    };
                });
            },
            undo: function(e) {
                return e.undo();
            },
            redo: function(e) {
                return e.redo();
            },
            undoSelection: function(e) {
                return e.undoSelection();
            },
            redoSelection: function(e) {
                return e.redoSelection();
            },
            goDocStart: function(e) {
                return e.extendSelection(rt(e.firstLine(), 0));
            },
            goDocEnd: function(e) {
                return e.extendSelection(rt(e.lastLine()));
            },
            goLineStart: function(t) {
                return t.extendSelectionsBy(function(e) {
                    return Ro(t, e.head.line);
                }, {
                    origin: "+move",
                    bias: 1
                });
            },
            goLineStartSmart: function(t) {
                return t.extendSelectionsBy(function(e) {
                    return zo(t, e.head);
                }, {
                    origin: "+move",
                    bias: 1
                });
            },
            goLineEnd: function(t) {
                return t.extendSelectionsBy(function(e) {
                    return function(e, t) {
                        var n = Xe(e.doc, t), r = function(e) {
                            for (var t; t = Et(e); ) e = t.find(1, !0).line;
                            return e;
                        }(n);
                        r != n && (t = Je(r));
                        return Ho(!0, e, n, t, -1);
                    }(t, e.head.line);
                }, {
                    origin: "+move",
                    bias: -1
                });
            },
            goLineRight: function(t) {
                return t.extendSelectionsBy(function(e) {
                    e = t.cursorCoords(e.head, "div").top + 5;
                    return t.coordsChar({
                        left: t.display.lineDiv.offsetWidth + 100,
                        top: e
                    }, "div");
                }, G);
            },
            goLineLeft: function(t) {
                return t.extendSelectionsBy(function(e) {
                    e = t.cursorCoords(e.head, "div").top + 5;
                    return t.coordsChar({
                        left: 0,
                        top: e
                    }, "div");
                }, G);
            },
            goLineLeftSmart: function(n) {
                return n.extendSelectionsBy(function(e) {
                    var t = n.cursorCoords(e.head, "div").top + 5, t = n.coordsChar({
                        left: 0,
                        top: t
                    }, "div");
                    return t.ch < n.getLine(t.line).search(/\S/) ? zo(n, e.head) : t;
                }, G);
            },
            goLineUp: function(e) {
                return e.moveV(-1, "line");
            },
            goLineDown: function(e) {
                return e.moveV(1, "line");
            },
            goPageUp: function(e) {
                return e.moveV(-1, "page");
            },
            goPageDown: function(e) {
                return e.moveV(1, "page");
            },
            goCharLeft: function(e) {
                return e.moveH(-1, "char");
            },
            goCharRight: function(e) {
                return e.moveH(1, "char");
            },
            goColumnLeft: function(e) {
                return e.moveH(-1, "column");
            },
            goColumnRight: function(e) {
                return e.moveH(1, "column");
            },
            goWordLeft: function(e) {
                return e.moveH(-1, "word");
            },
            goGroupRight: function(e) {
                return e.moveH(1, "group");
            },
            goGroupLeft: function(e) {
                return e.moveH(-1, "group");
            },
            goWordRight: function(e) {
                return e.moveH(1, "word");
            },
            delCharBefore: function(e) {
                return e.deleteH(-1, "char");
            },
            delCharAfter: function(e) {
                return e.deleteH(1, "char");
            },
            delWordBefore: function(e) {
                return e.deleteH(-1, "word");
            },
            delWordAfter: function(e) {
                return e.deleteH(1, "word");
            },
            delGroupBefore: function(e) {
                return e.deleteH(-1, "group");
            },
            delGroupAfter: function(e) {
                return e.deleteH(1, "group");
            },
            indentAuto: function(e) {
                return e.indentSelection("smart");
            },
            indentMore: function(e) {
                return e.indentSelection("add");
            },
            indentLess: function(e) {
                return e.indentSelection("subtract");
            },
            insertTab: function(e) {
                return e.replaceSelection("\t");
            },
            insertSoftTab: function(e) {
                for (var t = [], n = e.listSelections(), r = e.options.tabSize, i = 0; i < n.length; i++) {
                    var o = n[i].from(), o = I(e.getLine(o.line), o.ch, r);
                    t.push(j(r - o % r));
                }
                e.replaceSelections(t);
            },
            defaultTab: function(e) {
                e.somethingSelected() ? e.indentSelection("add") : e.execCommand("insertTab");
            },
            transposeChars: function(l) {
                return Hr(l, function() {
                    for (var e, t, n, r = l.listSelections(), i = [], o = 0; o < r.length; o++) {
                        r[o].empty() && (e = r[o].head, (t = Xe(l.doc, e.line).text) && (e.ch == t.length && (e = new rt(e.line, e.ch - 1)), 
                        0 < e.ch ? (e = new rt(e.line, e.ch + 1), l.replaceRange(t.charAt(e.ch - 1) + t.charAt(e.ch - 2), rt(e.line, e.ch - 2), e, "+transpose")) : e.line > l.doc.first && ((n = Xe(l.doc, e.line - 1).text) && (e = new rt(e.line, 1), 
                        l.replaceRange(t.charAt(0) + l.doc.lineSeparator() + n.charAt(n.length - 1), rt(e.line - 1, n.length - 1), e, "+transpose")))), 
                        i.push(new oi(e, e)));
                    }
                    l.setSelections(i);
                });
            },
            newlineAndIndent: function(r) {
                return Hr(r, function() {
                    for (var e = r.listSelections(), t = e.length - 1; 0 <= t; t--) r.replaceRange(r.doc.lineSeparator(), e[t].anchor, e[t].head, "+input");
                    e = r.listSelections();
                    for (var n = 0; n < e.length; n++) r.indentLine(e[n].from().line, null, !0);
                    wr(r);
                });
            },
            openLine: function(e) {
                return e.replaceSelection("\n", "start");
            },
            toggleOverwrite: function(e) {
                return e.toggleOverwrite();
            }
        };
        function Ro(e, t) {
            var n = Xe(e.doc, t), r = zt(n);
            return r != n && (t = Je(r)), Ho(!0, e, r, t, 1);
        }
        function zo(e, t) {
            var n = Ro(e, t.line), r = Xe(e.doc, n.line), e = me(r, e.doc.direction);
            if (e && 0 != e[0].level) return n;
            r = Math.max(0, r.text.search(/\S/)), t = t.line == n.line && t.ch <= r && t.ch;
            return rt(n.line, t ? 0 : r, n.sticky);
        }
        function Bo(e, t, n) {
            if ("string" == typeof t && !(t = Eo[t])) return !1;
            e.display.input.ensurePolled();
            var r = e.display.shift, i = !1;
            try {
                e.isReadOnly() && (e.state.suppressEdits = !0), n && (e.display.shift = !1), i = t(e) != B;
            } finally {
                e.display.shift = r, e.state.suppressEdits = !1;
            }
            return i;
        }
        var Uo = new E();
        function Ko(e, t, n, r) {
            var i = e.state.keySeq;
            if (i) {
                if (No(t)) return "handled";
                if (/\'$/.test(t) ? e.state.keySeq = null : Uo.set(50, function() {
                    e.state.keySeq == i && (e.state.keySeq = null, e.display.input.reset());
                }), Go(e, i + " " + t, n, r)) return !0;
            }
            return Go(e, t, n, r);
        }
        function Go(e, t, n, r) {
            r = function(e, t, n) {
                for (var r = 0; r < e.state.keyMaps.length; r++) {
                    var i = To(t, e.state.keyMaps[r], n, e);
                    if (i) return i;
                }
                return e.options.extraKeys && To(t, e.options.extraKeys, n, e) || To(t, e.options.keyMap, n, e);
            }(e, t, r);
            return "multi" == r && (e.state.keySeq = t), "handled" == r && ln(e, "keyHandled", e, t, n), 
            "handled" != r && "multi" != r || (Me(n), cr(e)), !!r;
        }
        function Vo(t, e) {
            var n = Ao(e, !0);
            return !!n && (e.shiftKey && !t.state.keySeq ? Ko(t, "Shift-" + n, e, function(e) {
                return Bo(t, e, !0);
            }) || Ko(t, n, e, function(e) {
                if ("string" == typeof e ? /^go[A-Z]/.test(e) : e.motion) return Bo(t, e);
            }) : Ko(t, n, e, function(e) {
                return Bo(t, e);
            }));
        }
        var qo = null;
        function jo(e) {
            var t, n, r, i = this;
            function o(e) {
                18 != e.keyCode && e.altKey || (k(r, "CodeMirror-crosshair"), we(document, "keyup", o), 
                we(document, "mouseover", o));
            }
            i.curOp.focus = A(), Ce(i, e) || (v && y < 11 && 27 == e.keyCode && (e.returnValue = !1), 
            t = e.keyCode, i.display.shift = 16 == t || e.shiftKey, n = Vo(i, e), p && (qo = n ? t : null, 
            !n && 88 == t && !Ee && (g ? e.metaKey : e.ctrlKey) && i.replaceSelection("", null, "cut")), 
            18 != t || /\bCodeMirror-crosshair\b/.test(i.display.lineDiv.className) || (F(r = i.display.lineDiv, "CodeMirror-crosshair"), 
            ye(document, "keyup", o), ye(document, "mouseover", o)));
        }
        function _o(e) {
            16 == e.keyCode && (this.doc.sel.shift = !1), Ce(this, e);
        }
        function $o(e) {
            var t = this;
            if (!(mn(t.display, e) || Ce(t, e) || e.ctrlKey && !e.altKey || g && e.metaKey)) {
                var n, r = e.keyCode, i = e.charCode;
                if (p && r == qo) return qo = null, void Me(e);
                p && (!e.which || e.which < 10) && Vo(t, e) || "\b" != (i = String.fromCharCode(null == i ? r : i)) && (Ko(n = t, "'" + i + "'", e, function(e) {
                    return Bo(n, e, !0);
                }) || t.display.input.onKeyPress(e));
            }
        }
        var Xo, Yo, Zo = function(e, t, n) {
            this.time = e, this.pos = t, this.button = n;
        };
        function Qo(e) {
            var t, n, r, i, o, l = this, s = l.display;
            Ce(l, e) || s.activeTouch && s.input.supportsTouch() || (s.input.ensurePolled(), 
            s.shift = e.shiftKey, mn(s, e) ? d || (s.scroller.draggable = !1, setTimeout(function() {
                return s.scroller.draggable = !0;
            }, 100)) : tl(l, e) || (t = Jn(l, e), n = Fe(e), i = t ? (r = t, i = n, o = +new Date(), 
            Yo && Yo.compare(o, r, i) ? (Xo = Yo = null, "triple") : Xo && Xo.compare(o, r, i) ? (Yo = new Zo(o, r, i), 
            Xo = null, "double") : (Xo = new Zo(o, r, i), Yo = null, "single")) : "single", 
            window.focus(), 1 == n && l.state.selectingText && l.state.selectingText(e), t && function(n, e, r, t, i) {
                var o = "Click";
                "double" == t ? o = "Double" + o : "triple" == t && (o = "Triple" + o);
                return Ko(n, Oo(o = (1 == e ? "Left" : 2 == e ? "Middle" : "Right") + o, i), i, function(e) {
                    if ("string" == typeof e && (e = Eo[e]), !e) return !1;
                    var t = !1;
                    try {
                        n.isReadOnly() && (n.state.suppressEdits = !0), t = e(n, r) != B;
                    } finally {
                        n.state.suppressEdits = !1;
                    }
                    return t;
                });
            }(l, n, t, i, e) || (1 == n ? t ? function(e, t, n, r) {
                v ? setTimeout(P(hr, e), 0) : e.curOp.focus = A();
                var i, o = function(e, t, n) {
                    var r = e.getOption("configureMouse"), i = r ? r(e, t, n) : {};
                    {
                        null == i.unit && (r = m ? n.shiftKey && n.metaKey : n.altKey, i.unit = r ? "rectangle" : "single" == t ? "char" : "double" == t ? "word" : "line");
                    }
                    null != i.extend && !e.doc.extend || (i.extend = e.doc.extend || n.shiftKey);
                    null == i.addNew && (i.addNew = g ? n.metaKey : n.ctrlKey);
                    null == i.moveOnDrag && (i.moveOnDrag = !(g ? n.altKey : n.ctrlKey));
                    return i;
                }(e, n, r), l = e.doc.sel;
                (e.options.dragDrop && Pe && !e.isReadOnly() && "single" == n && -1 < (i = l.contains(t)) && (it((i = l.ranges[i]).from(), t) < 0 || 0 < t.xRel) && (0 < it(i.to(), t) || t.xRel < 0) ? function(t, n, r, i) {
                    var o = t.display, l = !1, s = Ir(t, function(e) {
                        d && (o.scroller.draggable = !1), t.state.draggingText = !1, we(o.wrapper.ownerDocument, "mouseup", s), 
                        we(o.wrapper.ownerDocument, "mousemove", a), we(o.scroller, "dragstart", u), we(o.scroller, "drop", s), 
                        l || (Me(e), i.addNew || Ai(t.doc, r, null, null, i.extend), d || v && 9 == y ? setTimeout(function() {
                            o.wrapper.ownerDocument.body.focus(), o.input.focus();
                        }, 20) : o.input.focus());
                    }), a = function(e) {
                        l = l || 10 <= Math.abs(n.clientX - e.clientX) + Math.abs(n.clientY - e.clientY);
                    }, u = function() {
                        return l = !0;
                    };
                    d && (o.scroller.draggable = !0);
                    (t.state.draggingText = s).copy = !i.moveOnDrag, o.scroller.dragDrop && o.scroller.dragDrop();
                    ye(o.wrapper.ownerDocument, "mouseup", s), ye(o.wrapper.ownerDocument, "mousemove", a), 
                    ye(o.scroller, "dragstart", u), ye(o.scroller, "drop", s), fr(t), setTimeout(function() {
                        return o.input.focus();
                    }, 20);
                } : function(f, e, d, p) {
                    var l = f.display, g = f.doc;
                    Me(e);
                    var m, v, y = g.sel, t = y.ranges;
                    p.addNew && !p.extend ? (v = g.sel.contains(d), m = -1 < v ? t[v] : new oi(d, d)) : (m = g.sel.primary(), 
                    v = g.sel.primIndex);
                    {
                        "rectangle" == p.unit ? (p.addNew || (m = new oi(d, d)), d = Jn(f, e, !0, !0), v = -1) : (e = Jo(f, d, p.unit), 
                        m = p.extend ? Oi(m, e.anchor, e.head, p.extend) : e);
                    }
                    p.addNew ? -1 == v ? (v = t.length, Hi(g, li(f, t.concat([ m ]), v), {
                        scroll: !1,
                        origin: "*mouse"
                    })) : 1 < t.length && t[v].empty() && "char" == p.unit && !p.extend ? (Hi(g, li(f, t.slice(0, v).concat(t.slice(v + 1)), 0), {
                        scroll: !1,
                        origin: "*mouse"
                    }), y = g.sel) : Di(g, v, m, K) : (Hi(g, new ii([ m ], v = 0), K), y = g.sel);
                    var b = d;
                    function s(e) {
                        if (0 != it(b, e)) if (b = e, "rectangle" == p.unit) {
                            for (var t = [], n = f.options.tabSize, r = I(Xe(g, d.line).text, d.ch, n), i = I(Xe(g, e.line).text, e.ch, n), o = Math.min(r, i), l = Math.max(r, i), s = Math.min(d.line, e.line), a = Math.min(f.lastLine(), Math.max(d.line, e.line)); s <= a; s++) {
                                var u = Xe(g, s).text, c = V(u, o, n);
                                o == l ? t.push(new oi(rt(s, c), rt(s, c))) : u.length > c && t.push(new oi(rt(s, c), rt(s, V(u, l, n))));
                            }
                            t.length || t.push(new oi(d, d)), Hi(g, li(f, y.ranges.slice(0, v).concat(t), v), {
                                origin: "*mouse",
                                scroll: !1
                            }), f.scrollIntoView(e);
                        } else {
                            var h, r = m, i = Jo(f, e, p.unit), e = r.anchor, e = 0 < it(i.anchor, e) ? (h = i.head, 
                            at(r.from(), i.anchor)) : (h = i.anchor, st(r.to(), i.head)), i = y.ranges.slice(0);
                            i[v] = function(e, t) {
                                var n = t.anchor, r = t.head, i = Xe(e.doc, n.line);
                                if (0 == it(n, r) && n.sticky == r.sticky) return t;
                                var o = me(i);
                                if (!o) return t;
                                var l = le(o, n.ch, n.sticky), s = o[l];
                                if (s.from != n.ch && s.to != n.ch) return t;
                                i = l + (s.from == n.ch == (1 != s.level) ? 0 : 1);
                                if (0 == i || i == o.length) return t;
                                {
                                    a = r.line != n.line ? 0 < (r.line - n.line) * ("ltr" == e.doc.direction ? 1 : -1) : (e = le(o, r.ch, r.sticky), 
                                    a = e - l || (r.ch - n.ch) * (1 == s.level ? -1 : 1), e == i - 1 || e == i ? a < 0 : 0 < a);
                                }
                                var i = o[i + (a ? -1 : 0)], a = a == (1 == i.level), i = a ? i.from : i.to, a = a ? "after" : "before";
                                return n.ch == i && n.sticky == a ? t : new oi(new rt(n.line, i, a), r);
                            }(f, new oi(ct(g, e), h)), Hi(g, li(f, i, v), K);
                        }
                    }
                    var a = l.wrapper.getBoundingClientRect(), u = 0;
                    function n(e) {
                        f.state.selectingText = !1, u = 1 / 0, e && (Me(e), l.input.focus()), we(l.wrapper.ownerDocument, "mousemove", r), 
                        we(l.wrapper.ownerDocument, "mouseup", i), g.history.lastSelOrigin = null;
                    }
                    var r = Ir(f, function(e) {
                        (0 !== e.buttons && Fe(e) ? function e(t) {
                            var n, r, i = ++u, o = Jn(f, t, !0, "rectangle" == p.unit);
                            o && (0 != it(o, b) ? (f.curOp.focus = A(), s(o), n = vr(l, g), (o.line >= n.to || o.line < n.from) && setTimeout(Ir(f, function() {
                                u == i && e(t);
                            }), 150)) : (r = t.clientY < a.top ? -20 : t.clientY > a.bottom ? 20 : 0) && setTimeout(Ir(f, function() {
                                u == i && (l.scroller.scrollTop += r, e(t));
                            }), 50));
                        } : n)(e);
                    }), i = Ir(f, n);
                    f.state.selectingText = i, ye(l.wrapper.ownerDocument, "mousemove", r), ye(l.wrapper.ownerDocument, "mouseup", i);
                })(e, r, t, o);
            }(l, t, i, e) : Ae(e) == s.scroller && Me(e) : 2 == n ? (t && Ai(l.doc, t), setTimeout(function() {
                return s.input.focus();
            }, 20)) : 3 == n && (x ? l.display.input.onContextMenu(e) : fr(l)))));
        }
        function Jo(e, t, n) {
            if ("char" == n) return new oi(t, t);
            if ("word" == n) return e.findWordAt(t);
            if ("line" == n) return new oi(rt(t.line, 0), ct(e.doc, rt(t.line + 1, 0)));
            t = n(e, t);
            return new oi(t.from, t.to);
        }
        function el(e, t, n, r) {
            var i, o;
            if (t.touches) i = t.touches[0].clientX, o = t.touches[0].clientY; else try {
                i = t.clientX, o = t.clientY;
            } catch (t) {
                return !1;
            }
            if (i >= Math.floor(e.display.gutters.getBoundingClientRect().right)) return !1;
            r && Me(t);
            var l = e.display, r = l.lineDiv.getBoundingClientRect();
            if (o > r.bottom || !ke(e, n)) return Ne(t);
            o -= r.top - l.viewOffset;
            for (var s = 0; s < e.display.gutterSpecs.length; ++s) {
                var a = l.gutters.childNodes[s];
                if (a && a.getBoundingClientRect().right >= i) return xe(e, n, e, et(e.doc, o), e.display.gutterSpecs[s].className, t), 
                Ne(t);
            }
        }
        function tl(e, t) {
            return el(e, t, "gutterClick", !0);
        }
        function nl(e, t) {
            var n, r;
            mn(e.display, t) || (r = t, ke(n = e, "gutterContextMenu") && el(n, r, "gutterContextMenu", !1)) || Ce(e, t, "contextmenu") || x || e.display.input.onContextMenu(t);
        }
        function rl(e) {
            e.display.wrapper.className = e.display.wrapper.className.replace(/\s*cm-s-\S+/g, "") + e.options.theme.replace(/(^|\s)\s*/g, " cm-s-"), 
            Wn(e);
        }
        Zo.prototype.compare = function(e, t, n) {
            return this.time + 400 > e && 0 == it(t, this.pos) && n == this.button;
        };
        var il = {
            toString: function() {
                return "CodeMirror.Init";
            }
        }, ol = {}, ll = {};
        function sl(e, t, n) {
            !t != !(n && n != il) && (n = e.display.dragFunctions, (t = t ? ye : we)(e.display.scroller, "dragstart", n.start), 
            t(e.display.scroller, "dragenter", n.enter), t(e.display.scroller, "dragover", n.over), 
            t(e.display.scroller, "dragleave", n.leave), t(e.display.scroller, "drop", n.drop));
        }
        function al(e) {
            e.options.lineWrapping ? (F(e.display.wrapper, "CodeMirror-wrap"), e.display.sizer.style.minWidth = "", 
            e.display.sizerWidth = null) : (k(e.display.wrapper, "CodeMirror-wrap"), qt(e)), 
            Qn(e), tr(e), Wn(e), setTimeout(function() {
                return Nr(e);
            }, 100);
        }
        function ul(e, t) {
            var n = this;
            if (!(this instanceof ul)) return new ul(e, t);
            this.options = t = t ? H(t) : {}, H(ol, t, !1);
            var r = t.value;
            "string" == typeof r ? r = new ho(r, t.mode, null, t.lineSeparator, t.direction) : t.mode && (r.modeOption = t.mode), 
            this.doc = r;
            var i, o = new ul.inputStyles[t.inputStyle](this), o = this.display = new Qr(e, r, o, t);
            for (i in rl(o.wrapper.CodeMirror = this), t.lineWrapping && (this.display.wrapper.className += " CodeMirror-wrap"), 
            Fr(this), this.state = {
                keyMaps: [],
                overlays: [],
                modeGen: 0,
                overwrite: !1,
                delayingBlurEvent: !1,
                focused: !1,
                suppressEdits: !1,
                pasteIncoming: -1,
                cutIncoming: -1,
                selectingText: !1,
                draggingText: !1,
                highlight: new E(),
                keySeq: null,
                specialChars: null
            }, t.autofocus && !h && o.input.focus(), v && y < 11 && setTimeout(function() {
                return n.display.input.reset(!0);
            }, 20), function(r) {
                var i = r.display;
                ye(i.scroller, "mousedown", Ir(r, Qo)), ye(i.scroller, "dblclick", v && y < 11 ? Ir(r, function(e) {
                    var t;
                    Ce(r, e) || (!(t = Jn(r, e)) || tl(r, e) || mn(r.display, e) || (Me(e), t = r.findWordAt(t), 
                    Ai(r.doc, t.anchor, t.head)));
                }) : function(e) {
                    return Ce(r, e) || Me(e);
                });
                ye(i.scroller, "contextmenu", function(e) {
                    return nl(r, e);
                });
                var n, o = {
                    end: 0
                };
                function l() {
                    i.activeTouch && (n = setTimeout(function() {
                        return i.activeTouch = null;
                    }, 1e3), (o = i.activeTouch).end = +new Date());
                }
                function s(e, t) {
                    if (null == t.left) return 1;
                    var n = t.left - e.left, e = t.top - e.top;
                    return 400 < n * n + e * e;
                }
                ye(i.scroller, "touchstart", function(e) {
                    var t;
                    Ce(r, e) || function(e) {
                        if (1 == e.touches.length) {
                            e = e.touches[0];
                            return e.radiusX <= 1 && e.radiusY <= 1;
                        }
                    }(e) || tl(r, e) || (i.input.ensurePolled(), clearTimeout(n), t = +new Date(), i.activeTouch = {
                        start: t,
                        moved: !1,
                        prev: t - o.end <= 300 ? o : null
                    }, 1 == e.touches.length && (i.activeTouch.left = e.touches[0].pageX, i.activeTouch.top = e.touches[0].pageY));
                }), ye(i.scroller, "touchmove", function() {
                    i.activeTouch && (i.activeTouch.moved = !0);
                }), ye(i.scroller, "touchend", function(e) {
                    var t, n = i.activeTouch;
                    n && !mn(i, e) && null != n.left && !n.moved && new Date() - n.start < 300 && (t = r.coordsChar(i.activeTouch, "page"), 
                    t = !n.prev || s(n, n.prev) ? new oi(t, t) : !n.prev.prev || s(n, n.prev.prev) ? r.findWordAt(t) : new oi(rt(t.line, 0), ct(r.doc, rt(t.line + 1, 0))), 
                    r.setSelection(t.anchor, t.head), r.focus(), Me(e)), l();
                }), ye(i.scroller, "touchcancel", l), ye(i.scroller, "scroll", function() {
                    i.scroller.clientHeight && (kr(r, i.scroller.scrollTop), Mr(r, i.scroller.scrollLeft, !0), 
                    xe(r, "scroll", r));
                }), ye(i.scroller, "mousewheel", function(e) {
                    return ri(r, e);
                }), ye(i.scroller, "DOMMouseScroll", function(e) {
                    return ri(r, e);
                }), ye(i.wrapper, "scroll", function() {
                    return i.wrapper.scrollTop = i.wrapper.scrollLeft = 0;
                }), i.dragFunctions = {
                    enter: function(e) {
                        Ce(r, e) || Oe(e);
                    },
                    over: function(e) {
                        var t, n;
                        Ce(r, e) || ((n = Jn(t = r, n = e)) && (ar(t, n, n = document.createDocumentFragment()), 
                        t.display.dragCursor || (t.display.dragCursor = T("div", null, "CodeMirror-cursors CodeMirror-dragcursors"), 
                        t.display.lineSpace.insertBefore(t.display.dragCursor, t.display.cursorDiv)), M(t.display.dragCursor, n)), 
                        Oe(e));
                    },
                    start: function(e) {
                        return t = r, n = e, void (v && (!t.state.draggingText || +new Date() - fo < 100) ? Oe(n) : Ce(t, n) || mn(t.display, n) || (n.dataTransfer.setData("Text", t.getSelection()), 
                        n.dataTransfer.effectAllowed = "copyMove", n.dataTransfer.setDragImage && !a && ((e = T("img", null, null, "position: fixed; left: 0; top: 0;")).src = "data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==", 
                        p && (e.width = e.height = 1, t.display.wrapper.appendChild(e), e._top = e.offsetTop), 
                        n.dataTransfer.setDragImage(e, 0, 0), p && e.parentNode.removeChild(e))));
                        var t, n;
                    },
                    drop: Ir(r, po),
                    leave: function(e) {
                        Ce(r, e) || go(r);
                    }
                };
                var e = i.input.getField();
                ye(e, "keyup", function(e) {
                    return _o.call(r, e);
                }), ye(e, "keydown", Ir(r, jo)), ye(e, "keypress", Ir(r, $o)), ye(e, "focus", function(e) {
                    return dr(r, e);
                }), ye(e, "blur", function(e) {
                    return pr(r, e);
                });
            }(this), yo(), Wr(this), this.curOp.forceUpdate = !0, vi(this, r), t.autofocus && !h || this.hasFocus() ? setTimeout(P(dr, this), 20) : pr(this), 
            ll) ll.hasOwnProperty(i) && ll[i](n, t[i], il);
            $r(this), t.finishInit && t.finishInit(this);
            for (var l = 0; l < cl.length; ++l) cl[l](n);
            Pr(this), d && t.lineWrapping && "optimizelegibility" == getComputedStyle(o.lineDiv).textRendering && (o.lineDiv.style.textRendering = "auto");
        }
        ul.defaults = ol, ul.optionHandlers = ll;
        var cl = [];
        function hl(e, t, n, r) {
            var i, o = e.doc;
            null == n && (n = "add"), "smart" == n && (o.mode.indent ? i = mt(e, t).state : n = "prev");
            var l = e.options.tabSize, s = Xe(o, t), a = I(s.text, null, l);
            s.stateAfter && (s.stateAfter = null);
            var u, c = s.text.match(/^\s*/)[0];
            if (r || /\S/.test(s.text)) {
                if ("smart" == n && ((u = o.mode.indent(i, s.text.slice(c.length), s.text)) == B || 150 < u)) {
                    if (!r) return;
                    n = "prev";
                }
            } else u = 0, n = "not";
            "prev" == n ? u = t > o.first ? I(Xe(o, t - 1).text, null, l) : 0 : "add" == n ? u = a + e.options.indentUnit : "subtract" == n ? u = a - e.options.indentUnit : "number" == typeof n && (u = a + n), 
            u = Math.max(0, u);
            var h = "", f = 0;
            if (e.options.indentWithTabs) for (var d = Math.floor(u / l); d; --d) f += l, h += "\t";
            if (f < u && (h += j(u - f)), h != c) return Yi(o, h, rt(t, 0), rt(t, c.length), "+input"), 
            !(s.stateAfter = null);
            for (var p = 0; p < o.sel.ranges.length; p++) {
                var g = o.sel.ranges[p];
                if (g.head.line == t && g.head.ch < c.length) {
                    g = rt(t, c.length);
                    Di(o, p, new oi(g, g));
                    break;
                }
            }
        }
        ul.defineInitHook = function(e) {
            return cl.push(e);
        };
        var fl = null;
        function dl(e) {
            fl = e;
        }
        function pl(e, t, n, r, i) {
            var o = e.doc;
            e.display.shift = !1, r = r || o.sel;
            var l = +new Date() - 200, s = "paste" == i || e.state.pasteIncoming > l, a = He(t), u = null;
            if (s && 1 < r.ranges.length) if (fl && fl.text.join("\n") == t) {
                if (r.ranges.length % fl.text.length == 0) {
                    u = [];
                    for (var c = 0; c < fl.text.length; c++) u.push(o.splitLines(fl.text[c]));
                }
            } else a.length == r.ranges.length && e.options.pasteLinesPerSelection && (u = $(a, function(e) {
                return [ e ];
            }));
            for (var h = e.curOp.updateInput, f = r.ranges.length - 1; 0 <= f; f--) {
                var d = r.ranges[f], p = d.from(), g = d.to();
                d.empty() && (n && 0 < n ? p = rt(p.line, p.ch - n) : e.state.overwrite && !s ? g = rt(g.line, Math.min(Xe(o, g.line).text.length, g.ch + _(a).length)) : s && fl && fl.lineWise && fl.text.join("\n") == t && (p = g = rt(p.line, 0)));
                g = {
                    from: p,
                    to: g,
                    text: u ? u[f % u.length] : a,
                    origin: i || (s ? "paste" : e.state.cutIncoming > l ? "cut" : "+input")
                };
                qi(e.doc, g), ln(e, "inputRead", e, g);
            }
            t && !s && ml(e, t), wr(e), e.curOp.updateInput < 2 && (e.curOp.updateInput = h), 
            e.curOp.typing = !0, e.state.pasteIncoming = e.state.cutIncoming = -1;
        }
        function gl(e, t) {
            var n = e.clipboardData && e.clipboardData.getData("Text");
            return n && (e.preventDefault(), t.isReadOnly() || t.options.disableInput || Hr(t, function() {
                return pl(t, n, 0, null, "paste");
            }), 1);
        }
        function ml(e, t) {
            if (e.options.electricChars && e.options.smartIndent) for (var n = e.doc.sel, r = n.ranges.length - 1; 0 <= r; r--) {
                var i = n.ranges[r];
                if (!(100 < i.head.ch || r && n.ranges[r - 1].head.line == i.head.line)) {
                    var o = e.getModeAt(i.head), l = !1;
                    if (o.electricChars) {
                        for (var s = 0; s < o.electricChars.length; s++) if (-1 < t.indexOf(o.electricChars.charAt(s))) {
                            l = hl(e, i.head.line, "smart");
                            break;
                        }
                    } else o.electricInput && o.electricInput.test(Xe(e.doc, i.head.line).text.slice(0, i.head.ch)) && (l = hl(e, i.head.line, "smart"));
                    l && ln(e, "electricInput", e, i.head.line);
                }
            }
        }
        function vl(e) {
            for (var t = [], n = [], r = 0; r < e.doc.sel.ranges.length; r++) {
                var i = e.doc.sel.ranges[r].head.line, i = {
                    anchor: rt(i, 0),
                    head: rt(i + 1, 0)
                };
                n.push(i), t.push(e.getRange(i.anchor, i.head));
            }
            return {
                text: t,
                ranges: n
            };
        }
        function yl(e, t, n, r) {
            e.setAttribute("autocorrect", n ? "" : "off"), e.setAttribute("autocapitalize", r ? "" : "off"), 
            e.setAttribute("spellcheck", !!t);
        }
        function bl() {
            var e = T("textarea", null, null, "position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; outline: none"), t = T("div", [ e ], null, "overflow: hidden; position: relative; width: 3px; height: 0px;");
            return d ? e.style.width = "1000px" : e.setAttribute("wrap", "off"), s && (e.style.border = "1px solid black"), 
            yl(e), t;
        }
        function wl(r, i, o, e, l) {
            var t = i, n = o, s = Xe(r, i.line);
            function a(e) {
                var t, n = l ? Io(r.cm, s, i, o) : Po(s, i, o);
                if (null == n) {
                    if (e || (t = i.line + o) < r.first || t >= r.first + r.size || (i = new rt(t, i.ch, i.sticky), 
                    !(s = Xe(r, t)))) return;
                    i = Ho(l, r.cm, s, i.line, o);
                } else i = n;
                return 1;
            }
            if ("char" == e) a(); else if ("column" == e) a(!0); else if ("word" == e || "group" == e) for (var u = null, c = "group" == e, h = r.cm && r.cm.getHelper(i, "wordChars"), f = !0; !(o < 0) || a(!f); f = !1) {
                var d = s.text.charAt(i.ch) || "\n", d = J(d, h) ? "w" : c && "\n" == d ? "n" : !c || /\s/.test(d) ? null : "p";
                if (!c || f || d || (d = "s"), u && u != d) {
                    o < 0 && (o = 1, a(), i.sticky = "after");
                    break;
                }
                if (d && (u = d), 0 < o && !a(!f)) break;
            }
            n = Ui(r, i, t, n, !0);
            return ot(t, n) && (n.hitSide = !0), n;
        }
        function xl(e, t, n, r) {
            var i, o, l, s = e.doc, a = t.left;
            for ("page" == r ? (i = Math.min(e.display.wrapper.clientHeight, window.innerHeight || document.documentElement.clientHeight), 
            i = Math.max(i - .5 * _n(e.display), 3), o = (0 < n ? t.bottom : t.top) + n * i) : "line" == r && (o = 0 < n ? t.bottom + 3 : t.top - 3); (l = Gn(e, a, o)).outside; ) {
                if (n < 0 ? o <= 0 : o >= s.height) {
                    l.hitSide = !0;
                    break;
                }
                o += 5 * n;
            }
            return l;
        }
        e = function(e) {
            this.cm = e, this.lastAnchorNode = this.lastAnchorOffset = this.lastFocusNode = this.lastFocusOffset = null, 
            this.polling = new E(), this.composing = null, this.gracePeriod = !1, this.readDOMTimeout = null;
        };
        function Cl(e, t) {
            var n = Ln(e, t.line);
            if (!n || n.hidden) return null;
            var r = Xe(e.doc, t.line), n = Sn(n, r, t.line), r = me(r, e.doc.direction), e = "left";
            r && (e = le(r, t.ch) % 2 ? "right" : "left");
            e = An(n.map, t.ch, e);
            return e.offset = "right" == e.collapse ? e.end : e.start, e;
        }
        function Sl(e, t) {
            return t && (e.bad = !0), e;
        }
        function kl(e, t, n) {
            var r;
            if (t == e.display.lineDiv) {
                if (!(r = e.display.lineDiv.childNodes[n])) return Sl(e.clipPos(rt(e.display.viewTo - 1)), !0);
                t = null, n = 0;
            } else for (r = t; ;r = r.parentNode) {
                if (!r || r == e.display.lineDiv) return null;
                if (r.parentNode && r.parentNode == e.display.lineDiv) break;
            }
            for (var i = 0; i < e.display.view.length; i++) {
                var o = e.display.view[i];
                if (o.node == r) return function(u, e, t) {
                    var n = u.text.firstChild, r = !1;
                    if (!e || !O(n, e)) return Sl(rt(Je(u.line), 0), !0);
                    if (e == n && (r = !0, e = n.childNodes[t], t = 0, !e)) {
                        var i = u.rest ? _(u.rest) : u.line;
                        return Sl(rt(Je(i), i.text.length), r);
                    }
                    var i = 3 == e.nodeType ? e : null, o = e;
                    i || 1 != e.childNodes.length || 3 != e.firstChild.nodeType || (i = e.firstChild, 
                    t = t && i.nodeValue.length);
                    for (;o.parentNode != n; ) o = o.parentNode;
                    var c = u.measure, h = c.maps;
                    function l(e, t, n) {
                        for (var r = -1; r < (h ? h.length : 0); r++) for (var i = r < 0 ? c.map : h[r], o = 0; o < i.length; o += 3) {
                            var l = i[o + 2];
                            if (l == e || l == t) {
                                var s = Je(r < 0 ? u.line : u.rest[r]), a = i[o] + n;
                                return (n < 0 || l != e) && (a = i[o + (n ? 1 : 0)]), rt(s, a);
                            }
                        }
                    }
                    var s = l(i, o, t);
                    if (s) return Sl(s, r);
                    for (var a = o.nextSibling, f = i ? i.nodeValue.length - t : 0; a; a = a.nextSibling) {
                        if (s = l(a, a.firstChild, 0)) return Sl(rt(s.line, s.ch - f), r);
                        f += a.textContent.length;
                    }
                    for (var d = o.previousSibling, p = t; d; d = d.previousSibling) {
                        if (s = l(d, d.firstChild, -1)) return Sl(rt(s.line, s.ch + p), r);
                        p += d.textContent.length;
                    }
                }(o, t, n);
            }
        }
        e.prototype.init = function(e) {
            var t = this, o = this, l = o.cm, s = o.div = e.lineDiv;
            function n(e) {
                if (!Ce(l, e)) {
                    if (l.somethingSelected()) dl({
                        lineWise: !1,
                        text: l.getSelections()
                    }), "cut" == e.type && l.replaceSelection("", null, "cut"); else {
                        if (!l.options.lineWiseCopyCut) return;
                        var t = vl(l);
                        dl({
                            lineWise: !0,
                            text: t.text
                        }), "cut" == e.type && l.operation(function() {
                            l.setSelections(t.ranges, 0, U), l.replaceSelection("", null, "cut");
                        });
                    }
                    if (e.clipboardData) {
                        e.clipboardData.clearData();
                        var n = fl.text.join("\n");
                        if (e.clipboardData.setData("Text", n), e.clipboardData.getData("Text") == n) return void e.preventDefault();
                    }
                    var r = bl(), e = r.firstChild;
                    l.display.lineSpace.insertBefore(r, l.display.lineSpace.firstChild), e.value = fl.text.join("\n");
                    var i = document.activeElement;
                    W(e), setTimeout(function() {
                        l.display.lineSpace.removeChild(r), i.focus(), i == s && o.showPrimarySelection();
                    }, 50);
                }
            }
            yl(s, l.options.spellcheck, l.options.autocorrect, l.options.autocapitalize), ye(s, "paste", function(e) {
                Ce(l, e) || gl(e, l) || y <= 11 && setTimeout(Ir(l, function() {
                    return t.updateFromDOM();
                }), 20);
            }), ye(s, "compositionstart", function(e) {
                t.composing = {
                    data: e.data,
                    done: !1
                };
            }), ye(s, "compositionupdate", function(e) {
                t.composing || (t.composing = {
                    data: e.data,
                    done: !1
                });
            }), ye(s, "compositionend", function(e) {
                t.composing && (e.data != t.composing.data && t.readFromDOMSoon(), t.composing.done = !0);
            }), ye(s, "touchstart", function() {
                return o.forceCompositionEnd();
            }), ye(s, "input", function() {
                t.composing || t.readFromDOMSoon();
            }), ye(s, "copy", n), ye(s, "cut", n);
        }, e.prototype.prepareSelection = function() {
            var e = sr(this.cm, !1);
            return e.focus = this.cm.state.focused, e;
        }, e.prototype.showSelection = function(e, t) {
            e && this.cm.display.view.length && ((e.focus || t) && this.showPrimarySelection(), 
            this.showMultipleSelections(e));
        }, e.prototype.getSelection = function() {
            return this.cm.display.wrapper.ownerDocument.getSelection();
        }, e.prototype.showPrimarySelection = function() {
            var e = this.getSelection(), t = this.cm, n = t.doc.sel.primary(), r = n.from(), i = n.to();
            if (t.display.viewTo == t.display.viewFrom || r.line >= t.display.viewTo || i.line < t.display.viewFrom) e.removeAllRanges(); else {
                var o = kl(t, e.anchorNode, e.anchorOffset), n = kl(t, e.focusNode, e.focusOffset);
                if (!o || o.bad || !n || n.bad || 0 != it(at(o, n), r) || 0 != it(st(o, n), i)) {
                    var n = t.display.view, l = r.line >= t.display.viewFrom && Cl(t, r) || {
                        node: n[0].measure.map[2],
                        offset: 0
                    }, s = i.line < t.display.viewTo && Cl(t, i);
                    if (s || (s = {
                        node: (u = (u = n[n.length - 1].measure).maps ? u.maps[u.maps.length - 1] : u.map)[u.length - 1],
                        offset: u[u.length - 2] - u[u.length - 3]
                    }), l && s) {
                        var a, u = e.rangeCount && e.getRangeAt(0);
                        try {
                            a = S(l.node, l.offset, s.offset, s.node);
                        } catch (e) {}
                        a && (!f && t.state.focused ? (e.collapse(l.node, l.offset), a.collapsed || (e.removeAllRanges(), 
                        e.addRange(a))) : (e.removeAllRanges(), e.addRange(a)), u && null == e.anchorNode ? e.addRange(u) : f && this.startGracePeriod()), 
                        this.rememberSelection();
                    } else e.removeAllRanges();
                }
            }
        }, e.prototype.startGracePeriod = function() {
            var e = this;
            clearTimeout(this.gracePeriod), this.gracePeriod = setTimeout(function() {
                e.gracePeriod = !1, e.selectionChanged() && e.cm.operation(function() {
                    return e.cm.curOp.selectionChanged = !0;
                });
            }, 20);
        }, e.prototype.showMultipleSelections = function(e) {
            M(this.cm.display.cursorDiv, e.cursors), M(this.cm.display.selectionDiv, e.selection);
        }, e.prototype.rememberSelection = function() {
            var e = this.getSelection();
            this.lastAnchorNode = e.anchorNode, this.lastAnchorOffset = e.anchorOffset, this.lastFocusNode = e.focusNode, 
            this.lastFocusOffset = e.focusOffset;
        }, e.prototype.selectionInEditor = function() {
            var e = this.getSelection();
            if (!e.rangeCount) return !1;
            e = e.getRangeAt(0).commonAncestorContainer;
            return O(this.div, e);
        }, e.prototype.focus = function() {
            "nocursor" != this.cm.options.readOnly && (this.selectionInEditor() || this.showSelection(this.prepareSelection(), !0), 
            this.div.focus());
        }, e.prototype.blur = function() {
            this.div.blur();
        }, e.prototype.getField = function() {
            return this.div;
        }, e.prototype.supportsTouch = function() {
            return !0;
        }, e.prototype.receivedFocus = function() {
            var t = this;
            this.selectionInEditor() ? this.pollSelection() : Hr(this.cm, function() {
                return t.cm.curOp.selectionChanged = !0;
            }), this.polling.set(this.cm.options.pollInterval, function e() {
                t.cm.state.focused && (t.pollSelection(), t.polling.set(t.cm.options.pollInterval, e));
            });
        }, e.prototype.selectionChanged = function() {
            var e = this.getSelection();
            return e.anchorNode != this.lastAnchorNode || e.anchorOffset != this.lastAnchorOffset || e.focusNode != this.lastFocusNode || e.focusOffset != this.lastFocusOffset;
        }, e.prototype.pollSelection = function() {
            if (null == this.readDOMTimeout && !this.gracePeriod && this.selectionChanged()) {
                var e, t, n = this.getSelection(), r = this.cm;
                if (c && o && this.cm.display.gutterSpecs.length && function(e) {
                    for (var t = e; t; t = t.parentNode) if (/CodeMirror-gutter-wrapper/.test(t.className)) return !0;
                    return !1;
                }(n.anchorNode)) return this.cm.triggerOnKeyDown({
                    type: "keydown",
                    keyCode: 8,
                    preventDefault: Math.abs
                }), this.blur(), void this.focus();
                this.composing || (this.rememberSelection(), e = kl(r, n.anchorNode, n.anchorOffset), 
                t = kl(r, n.focusNode, n.focusOffset), e && t && Hr(r, function() {
                    Hi(r.doc, si(e, t), U), (e.bad || t.bad) && (r.curOp.selectionChanged = !0);
                }));
            }
        }, e.prototype.pollContent = function() {
            null != this.readDOMTimeout && (clearTimeout(this.readDOMTimeout), this.readDOMTimeout = null);
            var e, t = this.cm, n = t.display, r = t.doc.sel.primary(), i = r.from(), r = r.to();
            if (0 == i.ch && i.line > t.firstLine() && (i = rt(i.line - 1, Xe(t.doc, i.line - 1).length)), 
            r.ch == Xe(t.doc, r.line).text.length && r.line < t.lastLine() && (r = rt(r.line + 1, 0)), 
            i.line < n.viewFrom || r.line > n.viewTo - 1) return !1;
            m = i.line == n.viewFrom || 0 == (m = er(t, i.line)) ? (e = Je(n.view[0].line), 
            n.view[0].node) : (e = Je(n.view[m].line), n.view[m - 1].node.nextSibling);
            var o, r = er(t, r.line), r = r == n.view.length - 1 ? (o = n.viewTo - 1, n.lineDiv.lastChild) : (o = Je(n.view[r + 1].line) - 1, 
            n.view[r + 1].node.previousSibling);
            if (!m) return !1;
            for (var l = t.doc.splitLines(function(l, e, t, s, a) {
                var n = "", u = !1, c = l.doc.lineSeparator(), h = !1;
                function f() {
                    u && (n += c, h && (n += c), u = h = !1);
                }
                function d(e) {
                    e && (f(), n += e);
                }
                for (;!function e(t) {
                    if (1 == t.nodeType) {
                        var n = t.getAttribute("cm-text");
                        if (n) d(n); else if (n = t.getAttribute("cm-marker")) (n = l.findMarks(rt(s, 0), rt(a + 1, 0), (o = +n, 
                        function(e) {
                            return e.id == o;
                        }))).length && (r = n[0].find(0)) && d(Ye(l.doc, r.from, r.to).join(c)); else if ("false" != t.getAttribute("contenteditable")) {
                            var r = /^(pre|div|p|li|table|br)$/i.test(t.nodeName);
                            if (/^br$/i.test(t.nodeName) || 0 != t.textContent.length) {
                                r && f();
                                for (var i = 0; i < t.childNodes.length; i++) e(t.childNodes[i]);
                                /^(pre|p)$/i.test(t.nodeName) && (h = !0), r && (u = !0);
                            }
                        }
                    } else 3 == t.nodeType && d(t.nodeValue.replace(/\u200b/g, "").replace(/\u00a0/g, " "));
                    var o;
                }(e), e != t; ) e = e.nextSibling, h = !1;
                return n;
            }(t, m, r, e, o)), s = Ye(t.doc, rt(e, 0), rt(o, Xe(t.doc, o).text.length)); 1 < l.length && 1 < s.length; ) if (_(l) == _(s)) l.pop(), 
            s.pop(), o--; else {
                if (l[0] != s[0]) break;
                l.shift(), s.shift(), e++;
            }
            for (var a = 0, u = 0, c = l[0], h = s[0], f = Math.min(c.length, h.length); a < f && c.charCodeAt(a) == h.charCodeAt(a); ) ++a;
            for (var d = _(l), p = _(s), g = Math.min(d.length - (1 == l.length ? a : 0), p.length - (1 == s.length ? a : 0)); u < g && d.charCodeAt(d.length - u - 1) == p.charCodeAt(p.length - u - 1); ) ++u;
            if (1 == l.length && 1 == s.length && e == i.line) for (;a && a > i.ch && d.charCodeAt(d.length - u - 1) == p.charCodeAt(p.length - u - 1); ) a--, 
            u++;
            l[l.length - 1] = d.slice(0, d.length - u).replace(/^\u200b+/, ""), l[0] = l[0].slice(a).replace(/\u200b+$/, "");
            var m = rt(e, a), r = rt(o, s.length ? _(s).length - u : 0);
            return 1 < l.length || l[0] || it(m, r) ? (Yi(t.doc, l, m, r, "+input"), !0) : void 0;
        }, e.prototype.ensurePolled = function() {
            this.forceCompositionEnd();
        }, e.prototype.reset = function() {
            this.forceCompositionEnd();
        }, e.prototype.forceCompositionEnd = function() {
            this.composing && (clearTimeout(this.readDOMTimeout), this.composing = null, this.updateFromDOM(), 
            this.div.blur(), this.div.focus());
        }, e.prototype.readFromDOMSoon = function() {
            var e = this;
            null == this.readDOMTimeout && (this.readDOMTimeout = setTimeout(function() {
                if (e.readDOMTimeout = null, e.composing) {
                    if (!e.composing.done) return;
                    e.composing = null;
                }
                e.updateFromDOM();
            }, 80));
        }, e.prototype.updateFromDOM = function() {
            var e = this;
            !this.cm.isReadOnly() && this.pollContent() || Hr(this.cm, function() {
                return tr(e.cm);
            });
        }, e.prototype.setUneditable = function(e) {
            e.contentEditable = "false";
        }, e.prototype.onKeyPress = function(e) {
            0 == e.charCode || this.composing || (e.preventDefault(), this.cm.isReadOnly() || Ir(this.cm, pl)(this.cm, String.fromCharCode(null == e.charCode ? e.keyCode : e.charCode), 0));
        }, e.prototype.readOnlyChanged = function(e) {
            this.div.contentEditable = String("nocursor" != e);
        }, e.prototype.onContextMenu = function() {}, e.prototype.resetPosition = function() {}, 
        e.prototype.needsContentAttribute = !0;
        var Ll, Ml, Tl, Nl, Ol, r = function(e) {
            this.cm = e, this.prevInput = "", this.pollingFast = !1, this.polling = new E(), 
            this.hasSelection = !1, this.composing = null;
        };
        function Al(e, t, r, n) {
            Ll.defaults[e] = t, r && (Ml[e] = n ? function(e, t, n) {
                n != il && r(e, t, n);
            } : r);
        }
        r.prototype.init = function(n) {
            var e = this, r = this, i = this.cm;
            this.createField(n);
            var o = this.textarea;
            function t(e) {
                if (!Ce(i, e)) {
                    if (i.somethingSelected()) dl({
                        lineWise: !1,
                        text: i.getSelections()
                    }); else {
                        if (!i.options.lineWiseCopyCut) return;
                        var t = vl(i);
                        dl({
                            lineWise: !0,
                            text: t.text
                        }), "cut" == e.type ? i.setSelections(t.ranges, null, U) : (r.prevInput = "", o.value = t.text.join("\n"), 
                        W(o));
                    }
                    "cut" == e.type && (i.state.cutIncoming = +new Date());
                }
            }
            n.wrapper.insertBefore(this.wrapper, n.wrapper.firstChild), s && (o.style.width = "0px"), 
            ye(o, "input", function() {
                v && 9 <= y && e.hasSelection && (e.hasSelection = null), r.poll();
            }), ye(o, "paste", function(e) {
                Ce(i, e) || gl(e, i) || (i.state.pasteIncoming = +new Date(), r.fastPoll());
            }), ye(o, "cut", t), ye(o, "copy", t), ye(n.scroller, "paste", function(e) {
                if (!mn(n, e) && !Ce(i, e)) {
                    if (!o.dispatchEvent) return i.state.pasteIncoming = +new Date(), void r.focus();
                    var t = new Event("paste");
                    t.clipboardData = e.clipboardData, o.dispatchEvent(t);
                }
            }), ye(n.lineSpace, "selectstart", function(e) {
                mn(n, e) || Me(e);
            }), ye(o, "compositionstart", function() {
                var e = i.getCursor("from");
                r.composing && r.composing.range.clear(), r.composing = {
                    start: e,
                    range: i.markText(e, i.getCursor("to"), {
                        className: "CodeMirror-composing"
                    })
                };
            }), ye(o, "compositionend", function() {
                r.composing && (r.poll(), r.composing.range.clear(), r.composing = null);
            });
        }, r.prototype.createField = function(e) {
            this.wrapper = bl(), this.textarea = this.wrapper.firstChild;
        }, r.prototype.prepareSelection = function() {
            var e, t = this.cm, n = t.display, r = t.doc, i = sr(t);
            return t.options.moveInputWithCursor && (e = Bn(t, r.sel.primary().head, "div"), 
            t = n.wrapper.getBoundingClientRect(), r = n.lineDiv.getBoundingClientRect(), i.teTop = Math.max(0, Math.min(n.wrapper.clientHeight - 10, e.top + r.top - t.top)), 
            i.teLeft = Math.max(0, Math.min(n.wrapper.clientWidth - 10, e.left + r.left - t.left))), 
            i;
        }, r.prototype.showSelection = function(e) {
            var t = this.cm.display;
            M(t.cursorDiv, e.cursors), M(t.selectionDiv, e.selection), null != e.teTop && (this.wrapper.style.top = e.teTop + "px", 
            this.wrapper.style.left = e.teLeft + "px");
        }, r.prototype.reset = function(e) {
            var t, n;
            this.contextMenuPending || this.composing || ((t = this.cm).somethingSelected() ? (this.prevInput = "", 
            n = t.getSelection(), this.textarea.value = n, t.state.focused && W(this.textarea), 
            v && 9 <= y && (this.hasSelection = n)) : e || (this.prevInput = this.textarea.value = "", 
            v && 9 <= y && (this.hasSelection = null)));
        }, r.prototype.getField = function() {
            return this.textarea;
        }, r.prototype.supportsTouch = function() {
            return !1;
        }, r.prototype.focus = function() {
            if ("nocursor" != this.cm.options.readOnly && (!h || A() != this.textarea)) try {
                this.textarea.focus();
            } catch (e) {}
        }, r.prototype.blur = function() {
            this.textarea.blur();
        }, r.prototype.resetPosition = function() {
            this.wrapper.style.top = this.wrapper.style.left = 0;
        }, r.prototype.receivedFocus = function() {
            this.slowPoll();
        }, r.prototype.slowPoll = function() {
            var e = this;
            this.pollingFast || this.polling.set(this.cm.options.pollInterval, function() {
                e.poll(), e.cm.state.focused && e.slowPoll();
            });
        }, r.prototype.fastPoll = function() {
            var t = !1, n = this;
            n.pollingFast = !0, n.polling.set(20, function e() {
                n.poll() || t ? (n.pollingFast = !1, n.slowPoll()) : (t = !0, n.polling.set(60, e));
            });
        }, r.prototype.poll = function() {
            var e = this, t = this.cm, n = this.textarea, r = this.prevInput;
            if (this.contextMenuPending || !t.state.focused || Ie(n) && !r && !this.composing || t.isReadOnly() || t.options.disableInput || t.state.keySeq) return !1;
            var i = n.value;
            if (i == r && !t.somethingSelected()) return !1;
            if (v && 9 <= y && this.hasSelection === i || g && /[\uf700-\uf7ff]/.test(i)) return t.display.input.reset(), 
            !1;
            if (t.doc.sel == t.display.selForContextMenu) {
                var o = i.charCodeAt(0);
                if (8203 != o || r || (r = "\u200b"), 8666 == o) return this.reset(), this.cm.execCommand("undo");
            }
            for (var l = 0, s = Math.min(r.length, i.length); l < s && r.charCodeAt(l) == i.charCodeAt(l); ) ++l;
            return Hr(t, function() {
                pl(t, i.slice(l), r.length - l, null, e.composing ? "*compose" : null), 1e3 < i.length || -1 < i.indexOf("\n") ? n.value = e.prevInput = "" : e.prevInput = i, 
                e.composing && (e.composing.range.clear(), e.composing.range = t.markText(e.composing.start, t.getCursor("to"), {
                    className: "CodeMirror-composing"
                }));
            }), !0;
        }, r.prototype.ensurePolled = function() {
            this.pollingFast && this.poll() && (this.pollingFast = !1);
        }, r.prototype.onKeyPress = function() {
            v && 9 <= y && (this.hasSelection = null), this.fastPoll();
        }, r.prototype.onContextMenu = function(e) {
            var n = this, r = n.cm, i = r.display, o = n.textarea;
            n.contextMenuPending && n.contextMenuPending();
            var l, s, t, a, u = Jn(r, e), c = i.scroller.scrollTop;
            function h() {
                var e, t;
                null != o.selectionStart && (t = "\u200b" + ((e = r.somethingSelected()) ? o.value : ""), 
                o.value = "\u21da", o.value = t, n.prevInput = e ? "" : "\u200b", o.selectionStart = 1, 
                o.selectionEnd = t.length, i.selForContextMenu = r.doc.sel);
            }
            function f() {
                var e, t;
                n.contextMenuPending == f && (n.contextMenuPending = !1, n.wrapper.style.cssText = s, 
                o.style.cssText = l, v && y < 9 && i.scrollbars.setScrollTop(i.scroller.scrollTop = c), 
                null != o.selectionStart && ((!v || v && y < 9) && h(), e = 0, t = function() {
                    i.selForContextMenu == r.doc.sel && 0 == o.selectionStart && 0 < o.selectionEnd && "\u200b" == n.prevInput ? Ir(r, Gi)(r) : e++ < 10 ? i.detectingSelectAll = setTimeout(t, 500) : (i.selForContextMenu = null, 
                    i.input.reset());
                }, i.detectingSelectAll = setTimeout(t, 200)));
            }
            u && !p && (r.options.resetSelectionOnContextMenu && -1 == r.doc.sel.contains(u) && Ir(r, Hi)(r.doc, si(u), U), 
            l = o.style.cssText, s = n.wrapper.style.cssText, u = n.wrapper.offsetParent.getBoundingClientRect(), 
            n.wrapper.style.cssText = "position: static", o.style.cssText = "position: absolute; width: 30px; height: 30px;\n      top: " + (e.clientY - u.top - 5) + "px; left: " + (e.clientX - u.left - 5) + "px;\n      z-index: 1000; background: " + (v ? "rgba(255, 255, 255, .05)" : "transparent") + ";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);", 
            d && (t = window.scrollY), i.input.focus(), d && window.scrollTo(null, t), i.input.reset(), 
            r.somethingSelected() || (o.value = n.prevInput = " "), n.contextMenuPending = f, 
            i.selForContextMenu = r.doc.sel, clearTimeout(i.detectingSelectAll), v && 9 <= y && h(), 
            x ? (Oe(e), a = function() {
                we(window, "mouseup", a), setTimeout(f, 20);
            }, ye(window, "mouseup", a)) : setTimeout(f, 50));
        }, r.prototype.readOnlyChanged = function(e) {
            e || this.reset(), this.textarea.disabled = "nocursor" == e;
        }, r.prototype.setUneditable = function() {}, r.prototype.needsContentAttribute = !1, 
        Ml = (Ll = ul).optionHandlers, Ll.defineOption = Al, Ll.Init = il, Al("value", "", function(e, t) {
            return e.setValue(t);
        }, !0), Al("mode", null, function(e, t) {
            e.doc.modeOption = t, fi(e);
        }, !0), Al("indentUnit", 2, fi, !0), Al("indentWithTabs", !1), Al("smartIndent", !0), 
        Al("tabSize", 4, function(e) {
            di(e), Wn(e), tr(e);
        }, !0), Al("lineSeparator", null, function(e, r) {
            if (e.doc.lineSep = r) {
                var i = [], o = e.doc.first;
                e.doc.iter(function(e) {
                    for (var t = 0; ;) {
                        var n = e.text.indexOf(r, t);
                        if (-1 == n) break;
                        t = n + r.length, i.push(rt(o, n));
                    }
                    o++;
                });
                for (var t = i.length - 1; 0 <= t; t--) Yi(e.doc, r, i[t], rt(i[t].line, i[t].ch + r.length));
            }
        }), Al("specialChars", /[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b-\u200f\u2028\u2029\ufeff]/g, function(e, t, n) {
            e.state.specialChars = new RegExp(t.source + (t.test("\t") ? "" : "|\t"), "g"), 
            n != il && e.refresh();
        }), Al("specialCharPlaceholder", Zt, function(e) {
            return e.refresh();
        }, !0), Al("electricChars", !0), Al("inputStyle", h ? "contenteditable" : "textarea", function() {
            throw new Error("inputStyle can not (yet) be changed in a running editor");
        }, !0), Al("spellcheck", !1, function(e, t) {
            return e.getInputField().spellcheck = t;
        }, !0), Al("autocorrect", !1, function(e, t) {
            return e.getInputField().autocorrect = t;
        }, !0), Al("autocapitalize", !1, function(e, t) {
            return e.getInputField().autocapitalize = t;
        }, !0), Al("rtlMoveVisually", !b), Al("wholeLineUpdateBefore", !0), Al("theme", "default", function(e) {
            rl(e), Zr(e);
        }, !0), Al("keyMap", "default", function(e, t, n) {
            t = Fo(t), n = n != il && Fo(n);
            n && n.detach && n.detach(e, t), t.attach && t.attach(e, n || null);
        }), Al("extraKeys", null), Al("configureMouse", null), Al("lineWrapping", !1, al, !0), 
        Al("gutters", [], function(e, t) {
            e.display.gutterSpecs = Xr(t, e.options.lineNumbers), Zr(e);
        }, !0), Al("fixedGutter", !0, function(e, t) {
            e.display.gutters.style.left = t ? Yn(e.display) + "px" : "0", e.refresh();
        }, !0), Al("coverGutterNextToScrollbar", !1, function(e) {
            return Nr(e);
        }, !0), Al("scrollbarStyle", "native", function(e) {
            Fr(e), Nr(e), e.display.scrollbars.setScrollTop(e.doc.scrollTop), e.display.scrollbars.setScrollLeft(e.doc.scrollLeft);
        }, !0), Al("lineNumbers", !1, function(e, t) {
            e.display.gutterSpecs = Xr(e.options.gutters, t), Zr(e);
        }, !0), Al("firstLineNumber", 1, Zr, !0), Al("lineNumberFormatter", function(e) {
            return e;
        }, Zr, !0), Al("showCursorWhenSelecting", !1, lr, !0), Al("resetSelectionOnContextMenu", !0), 
        Al("lineWiseCopyCut", !0), Al("pasteLinesPerSelection", !0), Al("selectionsMayTouch", !1), 
        Al("readOnly", !1, function(e, t) {
            "nocursor" == t && (pr(e), e.display.input.blur()), e.display.input.readOnlyChanged(t);
        }), Al("disableInput", !1, function(e, t) {
            t || e.display.input.reset();
        }, !0), Al("dragDrop", !0, sl), Al("allowDropFileTypes", null), Al("cursorBlinkRate", 530), 
        Al("cursorScrollMargin", 0), Al("cursorHeight", 1, lr, !0), Al("singleCursorHeightPerLine", !0, lr, !0), 
        Al("workTime", 100), Al("workDelay", 100), Al("flattenSpans", !0, di, !0), Al("addModeClass", !1, di, !0), 
        Al("pollInterval", 100), Al("undoDepth", 200, function(e, t) {
            return e.doc.history.undoDepth = t;
        }), Al("historyEventDelay", 1250), Al("viewportMargin", 10, function(e) {
            return e.refresh();
        }, !0), Al("maxHighlightLength", 1e4, di, !0), Al("moveInputWithCursor", !0, function(e, t) {
            t || e.display.input.resetPosition();
        }), Al("tabindex", null, function(e, t) {
            return e.display.input.getField().tabIndex = t || "";
        }), Al("autofocus", null), Al("direction", "ltr", function(e, t) {
            return e.doc.setDirection(t);
        }, !0), Al("phrases", null), Nl = (Tl = ul).optionHandlers, Ol = Tl.helpers = {}, 
        Tl.prototype = {
            constructor: Tl,
            focus: function() {
                window.focus(), this.display.input.focus();
            },
            setOption: function(e, t) {
                var n = this.options, r = n[e];
                n[e] == t && "mode" != e || (n[e] = t, Nl.hasOwnProperty(e) && Ir(this, Nl[e])(this, t, r), 
                xe(this, "optionChange", this, e));
            },
            getOption: function(e) {
                return this.options[e];
            },
            getDoc: function() {
                return this.doc;
            },
            addKeyMap: function(e, t) {
                this.state.keyMaps[t ? "push" : "unshift"](Fo(e));
            },
            removeKeyMap: function(e) {
                for (var t = this.state.keyMaps, n = 0; n < t.length; ++n) if (t[n] == e || t[n].name == e) return t.splice(n, 1), 
                !0;
            },
            addOverlay: Er(function(e, t) {
                var n = e.token ? e : Tl.getMode(this.options, e);
                if (n.startState) throw new Error("Overlays may not be stateful.");
                !function(e, t, n) {
                    for (var r = 0, i = n(t); r < e.length && n(e[r]) <= i; ) r++;
                    e.splice(r, 0, t);
                }(this.state.overlays, {
                    mode: n,
                    modeSpec: e,
                    opaque: t && t.opaque,
                    priority: t && t.priority || 0
                }, function(e) {
                    return e.priority;
                }), this.state.modeGen++, tr(this);
            }),
            removeOverlay: Er(function(e) {
                for (var t = this.state.overlays, n = 0; n < t.length; ++n) {
                    var r = t[n].modeSpec;
                    if (r == e || "string" == typeof e && r.name == e) return t.splice(n, 1), this.state.modeGen++, 
                    void tr(this);
                }
            }),
            indentLine: Er(function(e, t, n) {
                "string" != typeof t && "number" != typeof t && (t = null == t ? this.options.smartIndent ? "smart" : "prev" : t ? "add" : "subtract"), 
                tt(this.doc, e) && hl(this, e, t, n);
            }),
            indentSelection: Er(function(e) {
                for (var t = this, n = this.doc.sel.ranges, r = -1, i = 0; i < n.length; i++) {
                    var o = n[i];
                    if (o.empty()) o.head.line > r && (hl(t, o.head.line, e, !0), r = o.head.line, i == t.doc.sel.primIndex && wr(t)); else {
                        for (var l = o.from(), s = o.to(), o = Math.max(r, l.line), r = Math.min(t.lastLine(), s.line - (s.ch ? 0 : 1)) + 1, a = o; a < r; ++a) hl(t, a, e);
                        o = t.doc.sel.ranges;
                        0 == l.ch && n.length == o.length && 0 < o[i].from().ch && Di(t.doc, i, new oi(l, o[i].to()), U);
                    }
                }
            }),
            getTokenAt: function(e, t) {
                return xt(this, e, t);
            },
            getLineTokens: function(e, t) {
                return xt(this, rt(e), t, !0);
            },
            getTokenTypeAt: function(e) {
                e = ct(this.doc, e);
                var t, n = gt(this, Xe(this.doc, e.line)), r = 0, i = (n.length - 1) / 2, o = e.ch;
                if (0 == o) t = n[2]; else for (;;) {
                    var l = r + i >> 1;
                    if ((l ? n[2 * l - 1] : 0) >= o) i = l; else {
                        if (!(n[2 * l + 1] < o)) {
                            t = n[2 * l + 2];
                            break;
                        }
                        r = 1 + l;
                    }
                }
                e = t ? t.indexOf("overlay ") : -1;
                return e < 0 ? t : 0 == e ? null : t.slice(0, e - 1);
            },
            getModeAt: function(e) {
                var t = this.doc.mode;
                return t.innerMode ? Tl.innerMode(t, this.getTokenAt(e).state).mode : t;
            },
            getHelper: function(e, t) {
                return this.getHelpers(e, t)[0];
            },
            getHelpers: function(e, t) {
                var n = [];
                if (!Ol.hasOwnProperty(t)) return n;
                var r = Ol[t], i = this.getModeAt(e);
                if ("string" == typeof i[t]) r[i[t]] && n.push(r[i[t]]); else if (i[t]) for (var o = 0; o < i[t].length; o++) {
                    var l = r[i[t][o]];
                    l && n.push(l);
                } else i.helperType && r[i.helperType] ? n.push(r[i.helperType]) : r[i.name] && n.push(r[i.name]);
                for (var s = 0; s < r._global.length; s++) {
                    var a = r._global[s];
                    a.pred(i, this) && -1 == R(n, a.val) && n.push(a.val);
                }
                return n;
            },
            getStateAfter: function(e, t) {
                var n = this.doc;
                return mt(this, (e = ut(n, null == e ? n.first + n.size - 1 : e)) + 1, t).state;
            },
            cursorCoords: function(e, t) {
                var n = this.doc.sel.primary(), n = null == e ? n.head : "object" == typeof e ? ct(this.doc, e) : e ? n.from() : n.to();
                return Bn(this, n, t || "page");
            },
            charCoords: function(e, t) {
                return zn(this, ct(this.doc, e), t || "page");
            },
            coordsChar: function(e, t) {
                return Gn(this, (e = Rn(this, e, t || "page")).left, e.top);
            },
            lineAtHeight: function(e, t) {
                return e = Rn(this, {
                    top: e,
                    left: 0
                }, t || "page").top, et(this.doc, e + this.display.viewOffset);
            },
            heightAtLine: function(e, t, n) {
                var r, i = !1, e = "number" == typeof e ? (r = this.doc.first + this.doc.size - 1, 
                e < this.doc.first ? e = this.doc.first : r < e && (e = r, i = !0), Xe(this.doc, e)) : e;
                return En(this, e, {
                    top: 0,
                    left: 0
                }, t || "page", n || i).top + (i ? this.doc.height - Gt(e) : 0);
            },
            defaultTextHeight: function() {
                return _n(this.display);
            },
            defaultCharWidth: function() {
                return $n(this.display);
            },
            getViewport: function() {
                return {
                    from: this.display.viewFrom,
                    to: this.display.viewTo
                };
            },
            addWidget: function(e, t, n, r, i) {
                var o, l, s = this.display, a = (e = Bn(this, ct(this.doc, e))).bottom, u = e.left;
                t.style.position = "absolute", t.setAttribute("cm-ignore-events", "true"), this.display.input.setUneditable(t), 
                s.sizer.appendChild(t), "over" == r ? a = e.top : "above" != r && "near" != r || (o = Math.max(s.wrapper.clientHeight, this.doc.height), 
                l = Math.max(s.sizer.clientWidth, s.lineSpace.clientWidth), ("above" == r || e.bottom + t.offsetHeight > o) && e.top > t.offsetHeight ? a = e.top - t.offsetHeight : e.bottom + t.offsetHeight <= o && (a = e.bottom), 
                u + t.offsetWidth > l && (u = l - t.offsetWidth)), t.style.top = a + "px", t.style.left = t.style.right = "", 
                "right" == i ? (u = s.sizer.clientWidth - t.offsetWidth, t.style.right = "0px") : ("left" == i ? u = 0 : "middle" == i && (u = (s.sizer.clientWidth - t.offsetWidth) / 2), 
                t.style.left = u + "px"), n && (n = this, t = {
                    left: u,
                    top: a,
                    right: u + t.offsetWidth,
                    bottom: a + t.offsetHeight
                }, null != (t = yr(n, t)).scrollTop && kr(n, t.scrollTop), null != t.scrollLeft && Mr(n, t.scrollLeft));
            },
            triggerOnKeyDown: Er(jo),
            triggerOnKeyPress: Er($o),
            triggerOnKeyUp: _o,
            triggerOnMouseDown: Er(Qo),
            execCommand: function(e) {
                if (Eo.hasOwnProperty(e)) return Eo[e].call(null, this);
            },
            triggerElectric: Er(function(e) {
                ml(this, e);
            }),
            findPosH: function(e, t, n, r) {
                var i = 1;
                t < 0 && (i = -1, t = -t);
                for (var o = ct(this.doc, e), l = 0; l < t && !(o = wl(this.doc, o, i, n, r)).hitSide; ++l) ;
                return o;
            },
            moveH: Er(function(t, n) {
                var r = this;
                this.extendSelectionsBy(function(e) {
                    return r.display.shift || r.doc.extend || e.empty() ? wl(r.doc, e.head, t, n, r.options.rtlMoveVisually) : t < 0 ? e.from() : e.to();
                }, G);
            }),
            deleteH: Er(function(n, r) {
                var e = this.doc.sel, i = this.doc;
                e.somethingSelected() ? i.replaceSelection("", null, "+delete") : Do(this, function(e) {
                    var t = wl(i, e.head, n, r, !1);
                    return n < 0 ? {
                        from: t,
                        to: e.head
                    } : {
                        from: e.head,
                        to: t
                    };
                });
            }),
            findPosV: function(e, t, n, r) {
                var i = 1, o = r;
                t < 0 && (i = -1, t = -t);
                for (var l = ct(this.doc, e), s = 0; s < t; ++s) {
                    var a = Bn(this, l, "div");
                    if (null == o ? o = a.left : a.left = o, (l = xl(this, a, i, n)).hitSide) break;
                }
                return l;
            },
            moveV: Er(function(r, i) {
                var o = this, l = this.doc, s = [], a = !this.display.shift && !l.extend && l.sel.somethingSelected();
                if (l.extendSelectionsBy(function(e) {
                    if (a) return r < 0 ? e.from() : e.to();
                    var t = Bn(o, e.head, "div");
                    null != e.goalColumn && (t.left = e.goalColumn), s.push(t.left);
                    var n = xl(o, t, r, i);
                    return "page" == i && e == l.sel.primary() && br(o, zn(o, n, "div").top - t.top), 
                    n;
                }, G), s.length) for (var e = 0; e < l.sel.ranges.length; e++) l.sel.ranges[e].goalColumn = s[e];
            }),
            findWordAt: function(e) {
                var t = Xe(this.doc, e.line).text, n = e.ch, r = e.ch;
                if (t) {
                    var i = this.getHelper(e, "wordChars");
                    "before" != e.sticky && r != t.length || !n ? ++r : --n;
                    for (var o = t.charAt(n), l = J(o, i) ? function(e) {
                        return J(e, i);
                    } : /\s/.test(o) ? function(e) {
                        return /\s/.test(e);
                    } : function(e) {
                        return !/\s/.test(e) && !J(e);
                    }; 0 < n && l(t.charAt(n - 1)); ) --n;
                    for (;r < t.length && l(t.charAt(r)); ) ++r;
                }
                return new oi(rt(e.line, n), rt(e.line, r));
            },
            toggleOverwrite: function(e) {
                null != e && e == this.state.overwrite || (((this.state.overwrite = !this.state.overwrite) ? F : k)(this.display.cursorDiv, "CodeMirror-overwrite"), 
                xe(this, "overwriteToggle", this, this.state.overwrite));
            },
            hasFocus: function() {
                return this.display.input.getField() == A();
            },
            isReadOnly: function() {
                return !(!this.options.readOnly && !this.doc.cantEdit);
            },
            scrollTo: Er(function(e, t) {
                xr(this, e, t);
            }),
            getScrollInfo: function() {
                var e = this.display.scroller;
                return {
                    left: e.scrollLeft,
                    top: e.scrollTop,
                    height: e.scrollHeight - wn(this) - this.display.barHeight,
                    width: e.scrollWidth - wn(this) - this.display.barWidth,
                    clientHeight: Cn(this),
                    clientWidth: xn(this)
                };
            },
            scrollIntoView: Er(function(e, t) {
                var n;
                null == e ? (e = {
                    from: this.doc.sel.primary().head,
                    to: null
                }, null == t && (t = this.options.cursorScrollMargin)) : "number" == typeof e ? e = {
                    from: rt(e, 0),
                    to: null
                } : null == e.from && (e = {
                    from: e,
                    to: null
                }), e.to || (e.to = e.from), e.margin = t || 0, null != e.from.line ? (n = e, Cr(t = this), 
                t.curOp.scrollToPos = n) : Sr(this, e.from, e.to, e.margin);
            }),
            setSize: Er(function(e, t) {
                function n(e) {
                    return "number" == typeof e || /^\d+$/.test(String(e)) ? e + "px" : e;
                }
                var r = this;
                null != e && (this.display.wrapper.style.width = n(e)), null != t && (this.display.wrapper.style.height = n(t)), 
                this.options.lineWrapping && Dn(this);
                var i = this.display.viewFrom;
                this.doc.iter(i, this.display.viewTo, function(e) {
                    if (e.widgets) for (var t = 0; t < e.widgets.length; t++) if (e.widgets[t].noHScroll) {
                        nr(r, i, "widget");
                        break;
                    }
                    ++i;
                }), this.curOp.forceUpdate = !0, xe(this, "refresh", this);
            }),
            operation: function(e) {
                return Hr(this, e);
            },
            startOperation: function() {
                return Wr(this);
            },
            endOperation: function() {
                return Pr(this);
            },
            refresh: Er(function() {
                var e = this.display.cachedTextHeight;
                tr(this), this.curOp.forceUpdate = !0, Wn(this), xr(this, this.doc.scrollLeft, this.doc.scrollTop), 
                qr(this.display), (null == e || .5 < Math.abs(e - _n(this.display))) && Qn(this), 
                xe(this, "refresh", this);
            }),
            swapDoc: Er(function(e) {
                var t = this.doc;
                return t.cm = null, this.state.selectingText && this.state.selectingText(), vi(this, e), 
                Wn(this), this.display.input.reset(), xr(this, e.scrollLeft, e.scrollTop), this.curOp.forceScroll = !0, 
                ln(this, "swapDoc", this, t), t;
            }),
            phrase: function(e) {
                var t = this.options.phrases;
                return t && Object.prototype.hasOwnProperty.call(t, e) ? t[e] : e;
            },
            getInputField: function() {
                return this.display.input.getField();
            },
            getWrapperElement: function() {
                return this.display.wrapper;
            },
            getScrollerElement: function() {
                return this.display.scroller;
            },
            getGutterElement: function() {
                return this.display.gutters;
            }
        }, Le(Tl), Tl.registerHelper = function(e, t, n) {
            Ol.hasOwnProperty(e) || (Ol[e] = Tl[e] = {
                _global: []
            }), Ol[e][t] = n;
        }, Tl.registerGlobalHelper = function(e, t, n, r) {
            Tl.registerHelper(e, t, r), Ol[e]._global.push({
                pred: n,
                val: r
            });
        };
        var Fl, Dl, Wl = "iter insert remove copy getEditor constructor".split(" ");
        for (Fl in ho.prototype) ho.prototype.hasOwnProperty(Fl) && R(Wl, Fl) < 0 && (ul.prototype[Fl] = function(e) {
            return function() {
                return e.apply(this.doc, arguments);
            };
        }(ho.prototype[Fl]));
        return Le(ho), ul.inputStyles = {
            textarea: r,
            contenteditable: e
        }, ul.defineMode = function(e) {
            ul.defaults.mode || "null" == e || (ul.defaults.mode = e), function(e, t) {
                2 < arguments.length && (t.dependencies = Array.prototype.slice.call(arguments, 2)), 
                ze[e] = t;
            }.apply(this, arguments);
        }, ul.defineMIME = function(e, t) {
            Be[e] = t;
        }, ul.defineMode("null", function() {
            return {
                token: function(e) {
                    return e.skipToEnd();
                }
            };
        }), ul.defineMIME("text/plain", "null"), ul.defineExtension = function(e, t) {
            ul.prototype[e] = t;
        }, ul.defineDocExtension = function(e, t) {
            ho.prototype[e] = t;
        }, ul.fromTextArea = function(t, e) {
            var n;
            function r() {
                t.value = s.getValue();
            }
            if ((e = e ? H(e) : {}).value = t.value, !e.tabindex && t.tabIndex && (e.tabindex = t.tabIndex), 
            !e.placeholder && t.placeholder && (e.placeholder = t.placeholder), null == e.autofocus && (n = A(), 
            e.autofocus = n == t || null != t.getAttribute("autofocus") && n == document.body), 
            t.form && (ye(t.form, "submit", r), !e.leaveSubmitMethodAlone)) {
                var i = t.form, o = i.submit;
                try {
                    var l = i.submit = function() {
                        r(), i.submit = o, i.submit(), i.submit = l;
                    };
                } catch (e) {}
            }
            e.finishInit = function(e) {
                e.save = r, e.getTextArea = function() {
                    return t;
                }, e.toTextArea = function() {
                    e.toTextArea = isNaN, r(), t.parentNode.removeChild(e.getWrapperElement()), t.style.display = "", 
                    t.form && (we(t.form, "submit", r), "function" == typeof t.form.submit && (t.form.submit = o));
                };
            }, t.style.display = "none";
            var s = ul(function(e) {
                return t.parentNode.insertBefore(e, t.nextSibling);
            }, e);
            return s;
        }, (Dl = ul).off = we, Dl.on = ye, Dl.wheelEventPixels = ni, Dl.Doc = ho, Dl.splitLines = He, 
        Dl.countColumn = I, Dl.findColumn = V, Dl.isWordChar = Q, Dl.Pass = B, Dl.signal = xe, 
        Dl.Line = jt, Dl.changeEnd = ai, Dl.scrollbarModel = Ar, Dl.Pos = rt, Dl.cmpPos = it, 
        Dl.modes = ze, Dl.mimeModes = Be, Dl.resolveMode = Ue, Dl.getMode = Ke, Dl.modeExtensions = Ge, 
        Dl.extendMode = Ve, Dl.copyState = qe, Dl.startState = _e, Dl.innerMode = je, Dl.commands = Eo, 
        Dl.keyMap = ko, Dl.keyName = Ao, Dl.isModifierKey = No, Dl.lookupKey = To, Dl.normalizeKeyMap = Mo, 
        Dl.StringStream = $e, Dl.SharedTextMarker = ao, Dl.TextMarker = lo, Dl.LineWidget = ro, 
        Dl.e_preventDefault = Me, Dl.e_stopPropagation = Te, Dl.e_stop = Oe, Dl.addClass = F, 
        Dl.contains = O, Dl.rmClass = k, Dl.keyNames = wo, ul.version = "5.46.0", ul;
    }, this.CodeMirror = t(), function(f) {
        "use strict";
        function t() {
            this.posFrom = this.posTo = this.lastQuery = this.query = null, this.overlay = null;
        }
        function d(e) {
            return e.state.search || (e.state.search = new t());
        }
        function i(e) {
            return "string" == typeof e && e == e.toLowerCase();
        }
        function h(e, t, n) {
            return e.getSearchCursor(t, n, {
                caseFold: i(t),
                multiline: !0
            });
        }
        function p(e, t, n, r, i) {
            e.openDialog ? e.openDialog(t, i, {
                value: r,
                selectValueOnOpen: !0
            }) : i(prompt(n, r));
        }
        function r(e) {
            return e.replace(/\\(.)/g, function(e, t) {
                return "n" == t ? "\n" : "r" == t ? "\r" : t;
            });
        }
        function o(e) {
            var t = e.match(/^\/(.*)\/([a-z]*)$/);
            if (t) try {
                e = new RegExp(t[1], -1 == t[2].indexOf("i") ? "" : "i");
            } catch (e) {} else e = r(e);
            return ("string" == typeof e ? "" == e : e.test("")) && (e = /x^/), e;
        }
        function g(e, t, n) {
            var r;
            t.queryText = n, t.query = o(n), e.removeOverlay(t.overlay, i(t.query)), t.overlay = (r = t.query, 
            n = i(t.query), "string" == typeof r ? r = new RegExp(r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&"), n ? "gi" : "g") : r.global || (r = new RegExp(r.source, r.ignoreCase ? "gi" : "g")), 
            {
                token: function(e) {
                    r.lastIndex = e.pos;
                    var t = r.exec(e.string);
                    if (t && t.index == e.pos) return e.pos += t[0].length || 1, "searching";
                    t ? e.pos = t.index : e.skipToEnd();
                }
            }), e.addOverlay(t.overlay), e.showMatchesOnScrollbar && (t.annotate && (t.annotate.clear(), 
            t.annotate = null), t.annotate = e.showMatchesOnScrollbar(t.query, i(t.query)));
        }
        function n(i, t, e, n) {
            var r = d(i);
            if (r.query) return m(i, t);
            var o, l, s, a, u, c, h = i.getSelection() || r.lastQuery;
            h instanceof RegExp && "x^" == h.source && (h = null), e && i.openDialog ? (o = null, 
            l = function(e, t) {
                f.e_stop(t), e && (e != r.queryText && (g(i, r, e), r.posFrom = r.posTo = i.getCursor()), 
                o && (o.style.opacity = 1), m(i, t.shiftKey, function(e, t) {
                    var n;
                    t.line < 3 && document.querySelector && (n = i.display.wrapper.querySelector(".CodeMirror-dialog")) && n.getBoundingClientRect().bottom - 4 > i.cursorCoords(t, "window").top && ((o = n).style.opacity = .4);
                }));
            }, a = y(s = i), u = h, c = l, e = function(e, t) {
                var n = f.keyName(e), r = i.getOption("extraKeys"), n = r && r[n] || f.keyMap[i.getOption("keyMap")][n];
                "findNext" == n || "findPrev" == n || "findPersistentNext" == n || "findPersistentPrev" == n ? (f.e_stop(e), 
                g(i, d(i), t), i.execCommand(n)) : "find" != n && "findPersistent" != n || (f.e_stop(e), 
                l(t, e));
            }, s.openDialog(a, c, {
                value: u,
                selectValueOnOpen: !0,
                closeOnEnter: !1,
                onClose: function() {
                    v(s);
                },
                onKeyDown: e
            }), n && h && (g(i, r, h), m(i, t))) : p(i, y(i), "Search for:", h, function(e) {
                e && !r.query && i.operation(function() {
                    g(i, r, e), r.posFrom = r.posTo = i.getCursor(), m(i, t);
                });
            });
        }
        function m(n, r, i) {
            n.operation(function() {
                var e = d(n), t = h(n, e.query, r ? e.posFrom : e.posTo);
                (t.find(r) || (t = h(n, e.query, r ? f.Pos(n.lastLine()) : f.Pos(n.firstLine(), 0))).find(r)) && (n.setSelection(t.from(), t.to()), 
                n.scrollIntoView({
                    from: t.from(),
                    to: t.to()
                }, 20), e.posFrom = t.from(), e.posTo = t.to(), i && i(t.from(), t.to()));
            });
        }
        function v(t) {
            t.operation(function() {
                var e = d(t);
                e.lastQuery = e.query, e.query && (e.query = e.queryText = null, t.removeOverlay(e.overlay), 
                e.annotate && (e.annotate.clear(), e.annotate = null));
            });
        }
        function y(e) {
            return '<span class="CodeMirror-search-label">' + e.phrase("Search:") + '</span> <input type="text" style="width: 10em" class="CodeMirror-search-field"/> <span style="color: #888" class="CodeMirror-search-hint">' + e.phrase("(Use /re/ syntax for regexp search)") + "</span>";
        }
        function b(t, r, i) {
            t.operation(function() {
                for (var n, e = h(t, r); e.findNext(); ) {
                    "string" != typeof r ? (n = t.getRange(e.from(), e.to()).match(r), e.replace(i.replace(/\$(\d)/g, function(e, t) {
                        return n[t];
                    }))) : e.replace(i);
                }
            });
        }
        function l(c, e) {
            var t, n;
            c.getOption("readOnly") || (t = c.getSelection() || d(c).lastQuery, n = '<span class="CodeMirror-search-label">' + (e ? c.phrase("Replace all:") : c.phrase("Replace:")) + "</span>", 
            p(c, n + ' <input type="text" style="width: 10em" class="CodeMirror-search-field"/> <span style="color: #888" class="CodeMirror-search-hint">' + c.phrase("(Use /re/ syntax for regexp search)") + "</span>", n, t, function(u) {
                u && (u = o(u), p(c, '<span class="CodeMirror-search-label">' + c.phrase("With:") + '</span> <input type="text" style="width: 10em" class="CodeMirror-search-field"/>', c.phrase("Replace with:"), "", function(o) {
                    var l, s, a;
                    o = r(o), e ? b(c, u, o) : (v(c), l = h(c, u, c.getCursor("from")), s = function() {
                        var e, t, n, r, i = l.from();
                        !(e = l.findNext()) && (l = h(c, u), !(e = l.findNext()) || i && l.from().line == i.line && l.from().ch == i.ch) || (c.setSelection(l.from(), l.to()), 
                        c.scrollIntoView({
                            from: l.from(),
                            to: l.to()
                        }), n = '<span class="CodeMirror-search-label">' + (r = t = c).phrase("Replace?") + "</span> <button>" + r.phrase("Yes") + "</button> <button>" + r.phrase("No") + "</button> <button>" + r.phrase("All") + "</button> <button>" + r.phrase("Stop") + "</button> ", 
                        i = c.phrase("Replace?"), r = [ function() {
                            a(e);
                        }, s, function() {
                            b(c, u, o);
                        } ], t.openConfirm ? t.openConfirm(n, r) : confirm(i) && r[0]());
                    }, a = function(n) {
                        l.replace("string" == typeof u ? o : o.replace(/\$(\d)/g, function(e, t) {
                            return n[t];
                        })), s();
                    }, s());
                }));
            }));
        }
        f.commands.find = function(e) {
            v(e), n(e);
        }, f.commands.findPersistent = function(e) {
            v(e), n(e, !1, !0);
        }, f.commands.findPersistentNext = function(e) {
            n(e, !1, !0, !0);
        }, f.commands.findPersistentPrev = function(e) {
            n(e, !0, !0, !0);
        }, f.commands.findNext = n, f.commands.findPrev = function(e) {
            n(e, !0);
        }, f.commands.clearSearch = v, f.commands.replace = l, f.commands.replaceAll = function(e) {
            l(e, !0);
        };
    }(CodeMirror), function(i) {
        "use strict";
        var m, v, y = i.Pos;
        function d(e, t) {
            for (var n, r, n = null != (r = (n = e).flags) ? r : (n.ignoreCase ? "i" : "") + (n.global ? "g" : "") + (n.multiline ? "m" : ""), i = n, o = 0; o < t.length; o++) -1 == i.indexOf(t.charAt(o)) && (i += t.charAt(o));
            return n == i ? e : new RegExp(e.source, i);
        }
        function p(e, t, n) {
            t = d(t, "g");
            for (var r = n.line, i = n.ch, o = e.lastLine(); r <= o; r++, i = 0) {
                t.lastIndex = i;
                var l = e.getLine(r), l = t.exec(l);
                if (l) return {
                    from: y(r, l.index),
                    to: y(r, l.index + l[0].length),
                    match: l
                };
            }
        }
        function g(e, t) {
            for (var n, r = 0; ;) {
                t.lastIndex = r;
                var i = t.exec(e);
                if (!i) return n;
                if ((r = (n = i).index + (n[0].length || 1)) == e.length) return n;
            }
        }
        function b(e, t, n, r) {
            if (e.length == t.length) return n;
            for (var i = 0, o = n + Math.max(0, e.length - t.length); ;) {
                if (i == o) return i;
                var l = i + o >> 1, s = r(e.slice(0, l)).length;
                if (s == n) return l;
                n < s ? o = l : i = 1 + l;
            }
        }
        function r(n, r, e, t) {
            var i;
            this.atOccurrence = !1, this.doc = n, e = e ? n.clipPos(e) : y(0, 0), this.pos = {
                from: e,
                to: e
            }, "object" == typeof t ? i = t.caseFold : (i = t, t = null), "string" == typeof r ? (null == i && (i = !1), 
            this.matches = function(e, t) {
                return (e ? function(e, t, n, r) {
                    if (!t.length) return null;
                    var i = r ? m : v, o = i(t).split(/\r|\n\r?/);
                    e: for (var l = n.line, s = n.ch, a = e.firstLine() - 1 + o.length; a <= l; l--, 
                    s = -1) {
                        var u = e.getLine(l);
                        -1 < s && (u = u.slice(0, s));
                        var c = i(u);
                        if (1 == o.length) {
                            var h = c.lastIndexOf(o[0]);
                            if (-1 != h) return {
                                from: y(l, b(u, c, h, i)),
                                to: y(l, b(u, c, h + o[0].length, i))
                            };
                        } else {
                            var f = o[o.length - 1];
                            if (c.slice(0, f.length) == f) {
                                for (var d = 1, n = l - o.length + 1; d < o.length - 1; d++) if (i(e.getLine(n + d)) != o[d]) continue e;
                                var p = e.getLine(l + 1 - o.length), h = i(p);
                                if (h.slice(h.length - o[0].length) == o[0]) return {
                                    from: y(l + 1 - o.length, b(p, h, p.length - o[0].length, i)),
                                    to: y(l, b(u, c, f.length, i))
                                };
                            }
                        }
                    }
                } : function(e, t, n, r) {
                    if (!t.length) return null;
                    var i = r ? m : v, o = i(t).split(/\r|\n\r?/);
                    e: for (var l = n.line, s = n.ch, a = e.lastLine() + 1 - o.length; l <= a; l++, 
                    s = 0) {
                        var u = e.getLine(l).slice(s), c = i(u);
                        if (1 == o.length) {
                            var h = c.indexOf(o[0]);
                            if (-1 != h) {
                                n = b(u, c, h, i) + s;
                                return {
                                    from: y(l, b(u, c, h, i) + s),
                                    to: y(l, b(u, c, h + o[0].length, i) + s)
                                };
                            }
                        } else {
                            var f = c.length - o[0].length;
                            if (c.slice(f) == o[0]) {
                                for (var d = 1; d < o.length - 1; d++) if (i(e.getLine(l + d)) != o[d]) continue e;
                                var p = e.getLine(l + o.length - 1), g = i(p), h = o[o.length - 1];
                                if (g.slice(0, h.length) == h) return {
                                    from: y(l, b(u, c, f, i) + s),
                                    to: y(l + o.length - 1, b(p, g, h.length, i))
                                };
                            }
                        }
                    }
                })(n, r, t, i);
            }) : (r = d(r, "gm"), t && !1 === t.multiline ? this.matches = function(e, t) {
                return (e ? function(e, t, n) {
                    t = d(t, "g");
                    for (var r = n.line, i = n.ch, o = e.firstLine(); o <= r; r--, i = -1) {
                        var l = e.getLine(r);
                        -1 < i && (l = l.slice(0, i));
                        l = g(l, t);
                        if (l) return {
                            from: y(r, l.index),
                            to: y(r, l.index + l[0].length),
                            match: l
                        };
                    }
                } : p)(n, r, t);
            } : this.matches = function(e, t) {
                return (e ? function(e, t, n) {
                    t = d(t, "gm");
                    for (var r = 1, i = n.line, o = e.firstLine(); o <= i; ) {
                        for (var l = 0; l < r; l++) var s = e.getLine(i--), a = null == a ? s.slice(0, n.ch) : s + "\n" + a;
                        r *= 2;
                        var u = g(a, t);
                        if (u) {
                            var c = a.slice(0, u.index).split("\n"), h = u[0].split("\n"), f = i + c.length, c = c[c.length - 1].length;
                            return {
                                from: y(f, c),
                                to: y(f + h.length - 1, 1 == h.length ? c + h[0].length : h[h.length - 1].length),
                                match: u
                            };
                        }
                    }
                } : function(e, t, n) {
                    if (!/\\s|\\n|\n|\\W|\\D|\[\^/.test(t.source)) return p(e, t, n);
                    t = d(t, "gm");
                    for (var r = 1, i = n.line, o = e.lastLine(); i <= o; ) {
                        for (var l = 0; l < r && !(o < i); l++) var s = e.getLine(i++), a = null == a ? s : a + "\n" + s;
                        r *= 2, t.lastIndex = n.ch;
                        var u = t.exec(a);
                        if (u) {
                            var c = a.slice(0, u.index).split("\n"), h = u[0].split("\n"), f = n.line + c.length - 1, c = c[c.length - 1].length;
                            return {
                                from: y(f, c),
                                to: y(f + h.length - 1, 1 == h.length ? c + h[0].length : h[h.length - 1].length),
                                match: u
                            };
                        }
                    }
                })(n, r, t);
            });
        }
        v = String.prototype.normalize ? (m = function(e) {
            return e.normalize("NFD").toLowerCase();
        }, function(e) {
            return e.normalize("NFD");
        }) : (m = function(e) {
            return e.toLowerCase();
        }, function(e) {
            return e;
        }), r.prototype = {
            findNext: function() {
                return this.find(!1);
            },
            findPrevious: function() {
                return this.find(!0);
            },
            find: function(e) {
                for (var t = this.matches(e, this.doc.clipPos(e ? this.pos.from : this.pos.to)); t && 0 == i.cmpPos(t.from, t.to); ) e ? t.from.ch ? t.from = y(t.from.line, t.from.ch - 1) : t = t.from.line == this.doc.firstLine() ? null : this.matches(e, this.doc.clipPos(y(t.from.line - 1))) : t.to.ch < this.doc.getLine(t.to.line).length ? t.to = y(t.to.line, t.to.ch + 1) : t = t.to.line == this.doc.lastLine() ? null : this.matches(e, y(t.to.line + 1, 0));
                if (t) return this.pos = t, this.atOccurrence = !0, this.pos.match || !0;
                var n = y(e ? this.doc.firstLine() : this.doc.lastLine() + 1, 0);
                return this.pos = {
                    from: n,
                    to: n
                }, this.atOccurrence = !1;
            },
            from: function() {
                if (this.atOccurrence) return this.pos.from;
            },
            to: function() {
                if (this.atOccurrence) return this.pos.to;
            },
            replace: function(e, t) {
                this.atOccurrence && (e = i.splitLines(e), this.doc.replaceRange(e, this.pos.from, this.pos.to, t), 
                this.pos.to = y(this.pos.from.line + e.length - 1, e[e.length - 1].length + (1 == e.length ? this.pos.from.ch : 0)));
            }
        }, i.defineExtension("getSearchCursor", function(e, t, n) {
            return new r(this.doc, e, t, n);
        }), i.defineDocExtension("getSearchCursor", function(e, t, n) {
            return new r(this, e, t, n);
        }), i.defineExtension("selectMatches", function(e, t) {
            for (var n = [], r = this.getSearchCursor(e, this.getCursor("from"), t); r.findNext() && !(0 < i.cmpPos(r.to(), this.getCursor("to"))); ) n.push({
                anchor: r.from(),
                head: r.to()
            });
            n.length && this.setSelections(n, 0);
        });
    }(CodeMirror), function(e) {
        "use strict";
        function s(e, t) {
            var n = Number(t);
            return /^[-+]/.test(t) ? e.getCursor().line + n : n - 1;
        }
        e.commands.jumpToLine = function(r) {
            var e, t, n, i, o, l = r.getCursor();
            t = (o = e = r).phrase("Jump to line:") + ' <input type="text" style="width: 10em" class="CodeMirror-search-field"/> <span style="color: #888" class="CodeMirror-search-hint">' + o.phrase("(Use line:column or scroll% syntax)") + "</span>", 
            n = r.phrase("Jump to line:"), i = l.line + 1 + ":" + l.ch, o = function(e) {
                var t, n;
                e && ((t = /^\s*([\+\-]?\d+)\s*\:\s*(\d+)\s*$/.exec(e)) ? r.setCursor(s(r, t[1]), Number(t[2])) : (t = /^\s*([\+\-]?\d+(\.\d+)?)\%\s*/.exec(e)) ? (n = Math.round(r.lineCount() * Number(t[1]) / 100), 
                /^[-+]/.test(t[1]) && (n = l.line + n + 1), r.setCursor(n - 1, l.ch)) : (t = /^\s*\:?\s*([\+\-]?\d+)\s*/.exec(e)) && r.setCursor(s(r, t[1]), l.ch));
            }, e.openDialog ? e.openDialog(t, o, {
                value: i,
                selectValueOnOpen: !0
            }) : o(prompt(n, i));
        }, e.keyMap.default["Alt-G"] = "jumpToLine";
    }(CodeMirror), function(h) {
        function f(e, t, n) {
            var r = e.getWrapperElement(), e = r.appendChild(document.createElement("div"));
            return e.className = n ? "CodeMirror-dialog CodeMirror-dialog-bottom" : "CodeMirror-dialog CodeMirror-dialog-top", 
            "string" == typeof t ? e.innerHTML = t : e.appendChild(t), h.addClass(r, "dialog-opened"), 
            e;
        }
        function d(e, t) {
            e.state.currentNotificationClose && e.state.currentNotificationClose(), e.state.currentNotificationClose = t;
        }
        h.defineExtension("openDialog", function(e, t, n) {
            n = n || {}, d(this, null);
            var r = f(this, e, n.bottom), i = !1, o = this;
            function l(e) {
                "string" == typeof e ? s.value = e : i || (i = !0, h.rmClass(r.parentNode, "dialog-opened"), 
                r.parentNode.removeChild(r), o.focus(), n.onClose && n.onClose(r));
            }
            var s = r.getElementsByTagName("input")[0];
            return s ? (s.focus(), n.value && (s.value = n.value, !1 !== n.selectValueOnOpen && s.select()), 
            n.onInput && h.on(s, "input", function(e) {
                n.onInput(e, s.value, l);
            }), n.onKeyUp && h.on(s, "keyup", function(e) {
                n.onKeyUp(e, s.value, l);
            }), h.on(s, "keydown", function(e) {
                n && n.onKeyDown && n.onKeyDown(e, s.value, l) || ((27 == e.keyCode || !1 !== n.closeOnEnter && 13 == e.keyCode) && (s.blur(), 
                h.e_stop(e), l()), 13 == e.keyCode && t(s.value, e));
            }), !1 !== n.closeOnBlur && h.on(s, "blur", l)) : (e = r.getElementsByTagName("button")[0]) && (h.on(e, "click", function() {
                l(), o.focus();
            }), !1 !== n.closeOnBlur && h.on(e, "blur", l), e.focus()), l;
        }), h.defineExtension("openConfirm", function(e, t, n) {
            d(this, null);
            var r = f(this, e, n && n.bottom), i = r.getElementsByTagName("button"), o = !1, l = this, s = 1;
            function a() {
                o || (o = !0, h.rmClass(r.parentNode, "dialog-opened"), r.parentNode.removeChild(r), 
                l.focus());
            }
            i[0].focus();
            for (var u = 0; u < i.length; ++u) {
                var c = i[u];
                !function(t) {
                    h.on(c, "click", function(e) {
                        h.e_preventDefault(e), a(), t && t(l);
                    });
                }(t[u]), h.on(c, "blur", function() {
                    --s, setTimeout(function() {
                        s <= 0 && a();
                    }, 200);
                }), h.on(c, "focus", function() {
                    ++s;
                });
            }
        }), h.defineExtension("openNotification", function(e, t) {
            d(this, o);
            var n, r = f(this, e, t && t.bottom), i = !1, t = t && void 0 !== t.duration ? t.duration : 5e3;
            function o() {
                i || (i = !0, clearTimeout(n), h.rmClass(r.parentNode, "dialog-opened"), r.parentNode.removeChild(r));
            }
            return h.on(r, "click", function(e) {
                h.e_preventDefault(e), o();
            }), t && (n = setTimeout(o, t)), o;
        });
    }(CodeMirror), function(c) {
        "use strict";
        function i(i, o, e, l) {
            var s;
            e && e.call ? (s = e, e = null) : s = h(i, e, "rangeFinder"), "number" == typeof o && (o = c.Pos(o, 0));
            var a = h(i, e, "minFoldSize");
            function t(e) {
                var t = s(i, o);
                if (!t || t.to.line - t.from.line < a) return null;
                for (var n = i.findMarksAt(t.from), r = 0; r < n.length; ++r) if (n[r].__isFold && "fold" !== l) {
                    if (!e) return null;
                    t.cleared = !0, n[r].clear();
                }
                return t;
            }
            var n, r, u = t(!0);
            if (h(i, e, "scanUp")) for (;!u && o.line > i.firstLine(); ) o = c.Pos(o.line - 1, 0), 
            u = t(!1);
            u && !u.cleared && "unfold" !== l && (n = function(e, t) {
                e = h(e, t, "widget");
                {
                    "string" == typeof e ? (t = document.createTextNode(e), (e = document.createElement("span")).appendChild(t), 
                    e.className = "CodeMirror-foldmarker") : e = e && e.cloneNode(!0);
                }
                return e;
            }(i, e), c.on(n, "mousedown", function(e) {
                r.clear(), c.e_preventDefault(e);
            }), (r = i.markText(u.from, u.to, {
                replacedWith: n,
                clearOnEnter: h(i, e, "clearOnEnter"),
                __isFold: !0
            })).on("clear", function(e, t) {
                c.signal(i, "unfold", i, e, t);
            }), c.signal(i, "fold", i, u.from, u.to));
        }
        c.newFoldFunction = function(n, r) {
            return function(e, t) {
                i(e, t, {
                    rangeFinder: n,
                    widget: r
                });
            };
        }, c.defineExtension("foldCode", function(e, t, n) {
            i(this, e, t, n);
        }), c.defineExtension("isFolded", function(e) {
            for (var t = this.findMarksAt(e), n = 0; n < t.length; ++n) if (t[n].__isFold) return !0;
        }), c.commands.toggleFold = function(e) {
            e.foldCode(e.getCursor());
        }, c.commands.fold = function(e) {
            e.foldCode(e.getCursor(), null, "fold");
        }, c.commands.unfold = function(e) {
            e.foldCode(e.getCursor(), null, "unfold");
        }, c.commands.foldAll = function(n) {
            n.operation(function() {
                for (var e = n.firstLine(), t = n.lastLine(); e <= t; e++) n.foldCode(c.Pos(e, 0), null, "fold");
            });
        }, c.commands.unfoldAll = function(n) {
            n.operation(function() {
                for (var e = n.firstLine(), t = n.lastLine(); e <= t; e++) n.foldCode(c.Pos(e, 0), null, "unfold");
            });
        }, c.registerHelper("fold", "combine", function() {
            var i = Array.prototype.slice.call(arguments, 0);
            return function(e, t) {
                for (var n = 0; n < i.length; ++n) {
                    var r = i[n](e, t);
                    if (r) return r;
                }
            };
        }), c.registerHelper("fold", "auto", function(e, t) {
            for (var n = e.getHelpers(t, "fold"), r = 0; r < n.length; r++) {
                var i = n[r](e, t);
                if (i) return i;
            }
        });
        var r = {
            rangeFinder: c.fold.auto,
            widget: "\u2194",
            minFoldSize: 0,
            scanUp: !1,
            clearOnEnter: !0
        };
        function h(e, t, n) {
            if (t && void 0 !== t[n]) return t[n];
            e = e.options.foldOptions;
            return (e && void 0 !== e[n] ? e : r)[n];
        }
        c.defineOption("foldOptions", null), c.defineExtension("foldOption", function(e, t) {
            return h(this, e, t);
        });
    }(CodeMirror), function(r) {
        "use strict";
        r.defineOption("foldGutter", !1, function(e, t, n) {
            n && n != r.Init && (e.clearGutter(e.state.foldGutter.options.gutter), e.state.foldGutter = null, 
            e.off("gutterClick", s), e.off("change", h), e.off("viewportChange", f), e.off("fold", d), 
            e.off("unfold", d), e.off("swapDoc", h)), t && (e.state.foldGutter = new i(function(e) {
                !0 === e && (e = {});
                null == e.gutter && (e.gutter = "CodeMirror-foldgutter");
                null == e.indicatorOpen && (e.indicatorOpen = "CodeMirror-foldgutter-open");
                null == e.indicatorFolded && (e.indicatorFolded = "CodeMirror-foldgutter-folded");
                return e;
            }(t)), l(e), e.on("gutterClick", s), e.on("change", h), e.on("viewportChange", f), 
            e.on("fold", d), e.on("unfold", d), e.on("swapDoc", h));
        });
        var a = r.Pos;
        function i(e) {
            this.options = e, this.from = this.to = 0;
        }
        function u(e, t) {
            for (var n = e.findMarks(a(t, 0), a(t + 1, 0)), r = 0; r < n.length; ++r) if (n[r].__isFold && n[r].find().from.line == t) return n[r];
        }
        function c(e) {
            if ("string" != typeof e) return e.cloneNode(!0);
            var t = document.createElement("div");
            return t.className = e + " CodeMirror-guttermarker-subtle", t;
        }
        function o(r, e, t) {
            var i = r.state.foldGutter.options, o = e, l = r.foldOption(i, "minFoldSize"), s = r.foldOption(i, "rangeFinder");
            r.eachLine(e, t, function(e) {
                var t, n = null;
                u(r, o) ? n = c(i.indicatorFolded) : (t = a(o, 0), (t = s && s(r, t)) && t.to.line - t.from.line >= l && (n = c(i.indicatorOpen))), 
                r.setGutterMarker(e, i.gutter, n), ++o;
            });
        }
        function l(e) {
            var t = e.getViewport(), n = e.state.foldGutter;
            n && (e.operation(function() {
                o(e, t.from, t.to);
            }), n.from = t.from, n.to = t.to);
        }
        function s(e, t, n) {
            var r = e.state.foldGutter;
            !r || n == (n = r.options).gutter && ((r = u(e, t)) ? r.clear() : e.foldCode(a(t, 0), n.rangeFinder));
        }
        function h(e) {
            var t, n = e.state.foldGutter;
            n && (t = n.options, n.from = n.to = 0, clearTimeout(n.changeUpdate), n.changeUpdate = setTimeout(function() {
                l(e);
            }, t.foldOnChangeTimeSpan || 600));
        }
        function f(t) {
            var e, n = t.state.foldGutter;
            n && (e = n.options, clearTimeout(n.changeUpdate), n.changeUpdate = setTimeout(function() {
                var e = t.getViewport();
                n.from == n.to || 20 < e.from - n.to || 20 < n.from - e.to ? l(t) : t.operation(function() {
                    e.from < n.from && (o(t, e.from, n.from), n.from = e.from), e.to > n.to && (o(t, n.to, e.to), 
                    n.to = e.to);
                });
            }, e.updateViewportTimeSpan || 400));
        }
        function d(e, t) {
            var n = e.state.foldGutter;
            !n || (t = t.line) >= n.from && t < n.to && o(e, t, t + 1);
        }
    }(CodeMirror), function(e) {
        "use strict";
        var a = e.Pos;
        function s(e, t) {
            return e.line - t.line || e.ch - t.ch;
        }
        var t = "A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD", r = new RegExp("<(/?)([" + t + "][A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD-:.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*)", "g");
        function u(e, t, n, r) {
            this.line = t, this.ch = n, this.cm = e, this.text = e.getLine(t), this.min = r ? Math.max(r.from, e.firstLine()) : e.firstLine(), 
            this.max = r ? Math.min(r.to - 1, e.lastLine()) : e.lastLine();
        }
        function c(e, t) {
            t = e.cm.getTokenTypeAt(a(e.line, t));
            return t && /\btag\b/.test(t);
        }
        function i(e) {
            return !(e.line >= e.max) && (e.ch = 0, e.text = e.cm.getLine(++e.line), 1);
        }
        function h(e) {
            return !(e.line <= e.min) && (e.text = e.cm.getLine(--e.line), e.ch = e.text.length, 
            1);
        }
        function f(e) {
            for (;;) {
                var t = e.text.indexOf(">", e.ch);
                if (-1 == t) {
                    if (i(e)) continue;
                    return;
                }
                if (c(e, t + 1)) {
                    var n = e.text.lastIndexOf("/", t), n = -1 < n && !/\S/.test(e.text.slice(n + 1, t));
                    return e.ch = t + 1, n ? "selfClose" : "regular";
                }
                e.ch = t + 1;
            }
        }
        function d(e) {
            for (;;) {
                var t = e.ch ? e.text.lastIndexOf("<", e.ch - 1) : -1;
                if (-1 == t) {
                    if (h(e)) continue;
                    return;
                }
                if (c(e, t + 1)) {
                    r.lastIndex = t, e.ch = t;
                    var n = r.exec(e.text);
                    if (n && n.index == t) return n;
                } else e.ch = t;
            }
        }
        function p(e) {
            for (;;) {
                r.lastIndex = e.ch;
                var t = r.exec(e.text);
                if (!t) {
                    if (i(e)) continue;
                    return;
                }
                if (c(e, t.index + 1)) return e.ch = t.index + t[0].length, t;
                e.ch = t.index + 1;
            }
        }
        function g(e, t) {
            for (var n = []; ;) {
                var r, i = p(e), o = e.line, l = e.ch - (i ? i[0].length : 0);
                if (!i || !(r = f(e))) return;
                if ("selfClose" != r) if (i[1]) {
                    for (var s = n.length - 1; 0 <= s; --s) if (n[s] == i[2]) {
                        n.length = s;
                        break;
                    }
                    if (s < 0 && (!t || t == i[2])) return {
                        tag: i[2],
                        from: a(o, l),
                        to: a(e.line, e.ch)
                    };
                } else n.push(i[2]);
            }
        }
        function m(e, t) {
            for (var n = []; ;) {
                var r = function(e) {
                    for (;;) {
                        var t = e.ch ? e.text.lastIndexOf(">", e.ch - 1) : -1;
                        if (-1 == t) {
                            if (h(e)) continue;
                            return;
                        }
                        if (c(e, t + 1)) {
                            var n = e.text.lastIndexOf("/", t), n = -1 < n && !/\S/.test(e.text.slice(n + 1, t));
                            return e.ch = t + 1, n ? "selfClose" : "regular";
                        }
                        e.ch = t;
                    }
                }(e);
                if (!r) return;
                if ("selfClose" != r) {
                    var i = e.line, r = e.ch, o = d(e);
                    if (!o) return;
                    if (o[1]) n.push(o[2]); else {
                        for (var l = n.length - 1; 0 <= l; --l) if (n[l] == o[2]) {
                            n.length = l;
                            break;
                        }
                        if (l < 0 && (!t || t == o[2])) return {
                            tag: o[2],
                            from: a(e.line, e.ch),
                            to: a(i, r)
                        };
                    }
                } else d(e);
            }
        }
        e.registerHelper("fold", "xml", function(e, t) {
            for (var n = new u(e, t.line, 0); ;) {
                var r = p(n);
                if (!r || n.line != t.line) return;
                var i = f(n);
                if (!i) return;
                if (!r[1] && "selfClose" != i) {
                    i = a(n.line, n.ch), r = g(n, r[2]);
                    return r && 0 < s(r.from, i) ? {
                        from: i,
                        to: r.from
                    } : null;
                }
            }
        }), e.findMatchingTag = function(e, t, n) {
            var r = new u(e, t.line, t.ch, n);
            if (-1 != r.text.indexOf(">") || -1 != r.text.indexOf("<")) {
                var i = f(r), o = i && a(r.line, r.ch), l = i && d(r);
                if (i && l && !(0 < s(r, t))) {
                    t = {
                        from: a(r.line, r.ch),
                        to: o,
                        tag: l[2]
                    };
                    return "selfClose" == i ? {
                        open: t,
                        close: null,
                        at: "open"
                    } : l[1] ? {
                        open: m(r, l[2]),
                        close: t,
                        at: "close"
                    } : {
                        open: t,
                        close: g(r = new u(e, o.line, o.ch, n), l[2]),
                        at: "open"
                    };
                }
            }
        }, e.findEnclosingTag = function(e, t, n, r) {
            for (var i = new u(e, t.line, t.ch, n); ;) {
                var o = m(i, r);
                if (!o) break;
                var l = g(new u(e, t.line, t.ch, n), o.tag);
                if (l) return {
                    open: o,
                    close: l
                };
            }
        }, e.scanForClosingTag = function(e, t, n, r) {
            return g(new u(e, t.line, t.ch, r ? {
                from: 0,
                to: r
            } : null), n);
        };
    }(CodeMirror), function(r) {
        "use strict";
        function i(e) {
            e.state.tagHit && e.state.tagHit.clear(), e.state.tagOther && e.state.tagOther.clear(), 
            e.state.tagHit = e.state.tagOther = null;
        }
        function o(n) {
            n.state.failedTagMatch = !1, n.operation(function() {
                var e, t;
                i(n), n.somethingSelected() || (t = n.getCursor(), (e = n.getViewport()).from = Math.min(e.from, t.line), 
                e.to = Math.max(t.line + 1, e.to), (t = r.findMatchingTag(n, t, e)) && (!n.state.matchBothTags || (e = "open" == t.at ? t.open : t.close) && (n.state.tagHit = n.markText(e.from, e.to, {
                    className: "CodeMirror-matchingtag"
                })), (t = "close" == t.at ? t.open : t.close) ? n.state.tagOther = n.markText(t.from, t.to, {
                    className: "CodeMirror-matchingtag"
                }) : n.state.failedTagMatch = !0));
            });
        }
        function l(e) {
            e.state.failedTagMatch && o(e);
        }
        r.defineOption("matchTags", !1, function(e, t, n) {
            n && n != r.Init && (e.off("cursorActivity", o), e.off("viewportChange", l), i(e)), 
            t && (e.state.matchBothTags = "object" == typeof t && t.bothTags, e.on("cursorActivity", o), 
            e.on("viewportChange", l), o(e));
        }), r.commands.toMatchingTag = function(e) {
            var t = r.findMatchingTag(e, e.getCursor());
            !t || (t = "close" == t.at ? t.open : t.close) && e.extendSelection(t.to, t.from);
        };
    }(CodeMirror), function(L) {
        "use strict";
        var M = "CodeMirror-hint-active";
        function r(e, t) {
            this.cm = e, this.options = t, this.widget = null, this.debounce = 0, this.tick = 0, 
            this.startPos = this.cm.getCursor("start"), this.startLen = this.cm.getLine(this.startPos.line).length - this.cm.getSelection().length;
            var n = this;
            e.on("cursorActivity", this.activityFunc = function() {
                n.cursorActivity();
            });
        }
        L.showHint = function(e, t, n) {
            if (!t) return e.showHint(n);
            n && n.async && (t.async = !0);
            var r = {
                hint: t
            };
            if (n) for (var i in n) r[i] = n[i];
            return e.showHint(r);
        }, L.defineExtension("showHint", function(e) {
            e = function(e, t, n) {
                var r = e.options.hintOptions, i = {};
                for (o in l) i[o] = l[o];
                if (r) for (var o in r) void 0 !== r[o] && (i[o] = r[o]);
                if (n) for (var o in n) void 0 !== n[o] && (i[o] = n[o]);
                i.hint.resolve && (i.hint = i.hint.resolve(e, t));
                return i;
            }(this, this.getCursor("start"), e);
            var t = this.listSelections();
            if (!(1 < t.length)) {
                if (this.somethingSelected()) {
                    if (!e.hint.supportsSelection) return;
                    for (var n = 0; n < t.length; n++) if (t[n].head.line != t[n].anchor.line) return;
                }
                this.state.completionActive && this.state.completionActive.close();
                e = this.state.completionActive = new r(this, e);
                e.options.hint && (L.signal(this, "startCompletion", this), e.update(!0));
            }
        }), L.defineExtension("closeHint", function() {
            this.state.completionActive && this.state.completionActive.close();
        });
        var i = window.requestAnimationFrame || function(e) {
            return setTimeout(e, 1e3 / 60);
        }, o = window.cancelAnimationFrame || clearTimeout;
        function T(e) {
            return "string" == typeof e ? e : e.text;
        }
        function N(e, t) {
            for (;t && t != e; ) {
                if ("LI" === t.nodeName.toUpperCase() && t.parentNode == e) return t;
                t = t.parentNode;
            }
        }
        function n(i, e) {
            this.completion = i, this.data = e, this.picked = !1;
            var n = this, o = i.cm, l = o.getInputField().ownerDocument, s = l.defaultView || l.parentWindow, a = this.hints = l.createElement("ul"), t = i.cm.options.theme;
            a.className = "CodeMirror-hints " + t, this.selectedHint = e.selectedHint || 0;
            for (var r = e.list, u = 0; u < r.length; ++u) {
                var c = a.appendChild(l.createElement("li")), h = r[u], f = "CodeMirror-hint" + (u != this.selectedHint ? "" : " " + M);
                null != h.className && (f = h.className + " " + f), c.className = f, h.render ? h.render(c, e, h) : c.appendChild(l.createTextNode(h.displayText || T(h))), 
                c.hintId = u;
            }
            var d = o.cursorCoords(i.options.alignWithWord ? e.from : null), p = d.left, g = d.bottom, m = !0;
            a.style.left = p + "px", a.style.top = g + "px";
            var v = s.innerWidth || Math.max(l.body.offsetWidth, l.documentElement.offsetWidth), y = s.innerHeight || Math.max(l.body.offsetHeight, l.documentElement.offsetHeight);
            (i.options.container || l.body).appendChild(a);
            var b = a.getBoundingClientRect(), w = b.bottom - y, t = a.scrollHeight > a.clientHeight + 1, x = o.getScrollInfo();
            0 < w && (w = b.bottom - b.top, 0 < d.top - (d.bottom - b.top) - w ? (a.style.top = (g = d.top - w) + "px", 
            m = !1) : y < w && (a.style.height = y - 5 + "px", a.style.top = (g = d.bottom - b.top) + "px", 
            S = o.getCursor(), e.from.ch != S.ch && (d = o.cursorCoords(S), a.style.left = (p = d.left) + "px", 
            b = a.getBoundingClientRect())));
            var C, S = b.right - v;
            if (0 < S && (b.right - b.left > v && (a.style.width = v - 5 + "px", S -= b.right - b.left - v), 
            a.style.left = (p = d.left - S) + "px"), t) for (var k = a.firstChild; k; k = k.nextSibling) k.style.paddingRight = o.display.nativeBarWidth + "px";
            return o.addKeyMap(this.keyMap = function(e, r) {
                var i = {
                    Up: function() {
                        r.moveFocus(-1);
                    },
                    Down: function() {
                        r.moveFocus(1);
                    },
                    PageUp: function() {
                        r.moveFocus(1 - r.menuSize(), !0);
                    },
                    PageDown: function() {
                        r.moveFocus(r.menuSize() - 1, !0);
                    },
                    Home: function() {
                        r.setFocus(0);
                    },
                    End: function() {
                        r.setFocus(r.length - 1);
                    },
                    Enter: r.pick,
                    Tab: r.pick,
                    Esc: r.close
                };
                /Mac/.test(navigator.platform) && (i["Ctrl-P"] = function() {
                    r.moveFocus(-1);
                }, i["Ctrl-N"] = function() {
                    r.moveFocus(1);
                });
                var t = e.options.customKeys, o = t ? {} : i;
                function n(e, t) {
                    var n = "string" != typeof t ? function(e) {
                        return t(e, r);
                    } : i.hasOwnProperty(t) ? i[t] : t;
                    o[e] = n;
                }
                if (t) for (var l in t) t.hasOwnProperty(l) && n(l, t[l]);
                var s = e.options.extraKeys;
                if (s) for (var l in s) s.hasOwnProperty(l) && n(l, s[l]);
                return o;
            }(i, {
                moveFocus: function(e, t) {
                    n.changeActive(n.selectedHint + e, t);
                },
                setFocus: function(e) {
                    n.changeActive(e);
                },
                menuSize: function() {
                    return n.screenAmount();
                },
                length: r.length,
                close: function() {
                    i.close();
                },
                pick: function() {
                    n.pick();
                },
                data: e
            })), i.options.closeOnUnfocus && (o.on("blur", this.onBlur = function() {
                C = setTimeout(function() {
                    i.close();
                }, 100);
            }), o.on("focus", this.onFocus = function() {
                clearTimeout(C);
            })), o.on("scroll", this.onScroll = function() {
                var e = o.getScrollInfo(), t = o.getWrapperElement().getBoundingClientRect(), n = g + x.top - e.top, r = n - (s.pageYOffset || (l.documentElement || l.body).scrollTop);
                if (m || (r += a.offsetHeight), r <= t.top || r >= t.bottom) return i.close();
                a.style.top = n + "px", a.style.left = p + x.left - e.left + "px";
            }), L.on(a, "dblclick", function(e) {
                e = N(a, e.target || e.srcElement);
                e && null != e.hintId && (n.changeActive(e.hintId), n.pick());
            }), L.on(a, "click", function(e) {
                e = N(a, e.target || e.srcElement);
                e && null != e.hintId && (n.changeActive(e.hintId), i.options.completeOnSingleClick && n.pick());
            }), L.on(a, "mousedown", function() {
                setTimeout(function() {
                    o.focus();
                }, 20);
            }), L.signal(e, "select", r[this.selectedHint], a.childNodes[this.selectedHint]), 
            !0;
        }
        function s(e, t, n, r) {
            e.async ? e(t, r, n) : (n = e(t, n)) && n.then ? n.then(r) : r(n);
        }
        r.prototype = {
            close: function() {
                this.active() && (this.cm.state.completionActive = null, this.tick = null, this.cm.off("cursorActivity", this.activityFunc), 
                this.widget && this.data && L.signal(this.data, "close"), this.widget && this.widget.close(), 
                L.signal(this.cm, "endCompletion", this.cm));
            },
            active: function() {
                return this.cm.state.completionActive == this;
            },
            pick: function(e, t) {
                t = e.list[t];
                t.hint ? t.hint(this.cm, e, t) : this.cm.replaceRange(T(t), t.from || e.from, t.to || e.to, "complete"), 
                L.signal(e, "pick", t), this.close();
            },
            cursorActivity: function() {
                this.debounce && (o(this.debounce), this.debounce = 0);
                var e, t = this.cm.getCursor(), n = this.cm.getLine(t.line);
                t.line != this.startPos.line || n.length - t.ch != this.startLen - this.startPos.ch || t.ch < this.startPos.ch || this.cm.somethingSelected() || !t.ch || this.options.closeCharacters.test(n.charAt(t.ch - 1)) ? this.close() : ((e = this).debounce = i(function() {
                    e.update();
                }), this.widget && this.widget.disable());
            },
            update: function(t) {
                var n, r;
                null != this.tick && (r = ++(n = this).tick, s(this.options.hint, this.cm, this.options, function(e) {
                    n.tick == r && n.finishUpdate(e, t);
                }));
            },
            finishUpdate: function(e, t) {
                this.data && L.signal(this.data, "update");
                t = this.widget && this.widget.picked || t && this.options.completeSingle;
                this.widget && this.widget.close(), (this.data = e) && e.list.length && (t && 1 == e.list.length ? this.pick(e, 0) : (this.widget = new n(this, e), 
                L.signal(e, "shown")));
            }
        }, n.prototype = {
            close: function() {
                var e;
                this.completion.widget == this && (this.completion.widget = null, this.hints.parentNode.removeChild(this.hints), 
                this.completion.cm.removeKeyMap(this.keyMap), e = this.completion.cm, this.completion.options.closeOnUnfocus && (e.off("blur", this.onBlur), 
                e.off("focus", this.onFocus)), e.off("scroll", this.onScroll));
            },
            disable: function() {
                this.completion.cm.removeKeyMap(this.keyMap);
                var e = this;
                this.keyMap = {
                    Enter: function() {
                        e.picked = !0;
                    }
                }, this.completion.cm.addKeyMap(this.keyMap);
            },
            pick: function() {
                this.completion.pick(this.data, this.selectedHint);
            },
            changeActive: function(e, t) {
                e >= this.data.list.length ? e = t ? this.data.list.length - 1 : 0 : e < 0 && (e = t ? 0 : this.data.list.length - 1), 
                this.selectedHint != e && ((t = this.hints.childNodes[this.selectedHint]) && (t.className = t.className.replace(" " + M, "")), 
                (t = this.hints.childNodes[this.selectedHint = e]).className += " " + M, t.offsetTop < this.hints.scrollTop ? this.hints.scrollTop = t.offsetTop - 3 : t.offsetTop + t.offsetHeight > this.hints.scrollTop + this.hints.clientHeight && (this.hints.scrollTop = t.offsetTop + t.offsetHeight - this.hints.clientHeight + 3), 
                L.signal(this.data, "select", this.data.list[this.selectedHint], t));
            },
            screenAmount: function() {
                return Math.floor(this.hints.clientHeight / this.hints.firstChild.offsetHeight) || 1;
            }
        }, L.registerHelper("hint", "auto", {
            resolve: function(e, t) {
                var n, l = e.getHelpers(t, "hint");
                if (l.length) {
                    t = function(e, r, i) {
                        var o = function(e, t) {
                            if (!e.somethingSelected()) return t;
                            for (var n = [], r = 0; r < t.length; r++) t[r].supportsSelection && n.push(t[r]);
                            return n;
                        }(e, l);
                        !function t(n) {
                            if (n == o.length) return r(null);
                            s(o[n], e, i, function(e) {
                                e && 0 < e.list.length ? r(e) : t(n + 1);
                            });
                        }(0);
                    };
                    return t.async = !0, t.supportsSelection = !0, t;
                }
                return (n = e.getHelper(e.getCursor(), "hintWords")) ? function(e) {
                    return L.hint.fromList(e, {
                        words: n
                    });
                } : L.hint.anyword ? function(e, t) {
                    return L.hint.anyword(e, t);
                } : function() {};
            }
        }), L.registerHelper("hint", "fromList", function(e, t) {
            var n, r = e.getCursor(), i = e.getTokenAt(r), o = L.Pos(r.line, i.start), e = r;
            i.start < r.ch && /\w/.test(i.string.charAt(r.ch - i.start - 1)) ? n = i.string.substr(0, r.ch - i.start) : (n = "", 
            o = r);
            for (var l = [], s = 0; s < t.words.length; s++) {
                var a = t.words[s];
                a.slice(0, n.length) == n && l.push(a);
            }
            if (l.length) return {
                list: l,
                from: o,
                to: e
            };
        }), L.commands.autocomplete = L.showHint;
        var l = {
            hint: L.hint.auto,
            completeSingle: !0,
            alignWithWord: !0,
            closeCharacters: /[\s()\[\]{};:>,]/,
            closeOnUnfocus: !0,
            completeOnSingleClick: !0,
            container: null,
            customKeys: null,
            extraKeys: null
        };
        L.defineOption("hintOptions", null);
    }(CodeMirror), function(L) {
        "use strict";
        var M = L.Pos;
        function T(e, t, n) {
            return n ? 0 <= e.indexOf(t) : 0 == e.lastIndexOf(t, 0);
        }
        L.registerHelper("hint", "xml", function(e, t) {
            var n = t && t.schemaInfo, r = t && t.quoteChar || '"', i = t && t.matchInMiddle;
            if (n) {
                var o = e.getCursor(), l = e.getTokenAt(o);
                l.end > o.ch && (l.end = o.ch, l.string = l.string.slice(0, o.ch - l.start));
                var s = L.innerMode(e.getMode(), l.state);
                if ("xml" == s.mode.name) {
                    var a, u, c, h = [], f = !1, d = /\btag\b/.test(l.type) && !/>$/.test(l.string), t = d && /^\w/.test(l.string);
                    if (t ? (S = e.getLine(o.line).slice(Math.max(0, l.start - 2), l.start), (c = /<\/$/.test(S) ? "close" : /<$/.test(S) ? "open" : null) && (u = l.start - ("close" == c ? 2 : 1))) : d && "<" == l.string ? c = "open" : d && "</" == l.string && (c = "close"), 
                    !d && !s.state.tagName || c) {
                        t && (a = l.string), f = c;
                        var t = s.state.context, p = t && n[t.tagName], g = t ? p && p.children : n["!top"];
                        if (g && "close" != c) for (var m = 0; m < g.length; ++m) a && !T(g[m], a, i) || h.push("<" + g[m]); else if ("close" != c) for (var v in n) !n.hasOwnProperty(v) || "!top" == v || "!attrs" == v || a && !T(v, a, i) || h.push("<" + v);
                        t && (!a || "close" == c && T(t.tagName, a, i)) && h.push("</" + t.tagName + ">");
                    } else {
                        var y = (p = n[s.state.tagName]) && p.attrs, b = n["!attrs"];
                        if (!y && !b) return;
                        if (y) {
                            if (b) {
                                var w, x = {};
                                for (w in b) b.hasOwnProperty(w) && (x[w] = b[w]);
                                for (w in y) y.hasOwnProperty(w) && (x[w] = y[w]);
                                y = x;
                            }
                        } else y = b;
                        if ("string" == l.type || "=" == l.string) {
                            var C, S, p = (S = e.getRange(M(o.line, Math.max(0, o.ch - 60)), M(o.line, "string" == l.type ? l.start : l.end))).match(/([^\s\u00a0=<>\"\']+)=$/);
                            if (!p || !y.hasOwnProperty(p[1]) || !(C = y[p[1]])) return;
                            "function" == typeof C && (C = C.call(this, e)), "string" == l.type && (a = l.string, 
                            S = 0, /['"]/.test(l.string.charAt(0)) && (r = l.string.charAt(0), a = l.string.slice(1), 
                            S++), p = l.string.length, /['"]/.test(l.string.charAt(p - 1)) && (r = l.string.charAt(p - 1), 
                            a = l.string.substr(S, p - 2)), !S || (e = e.getLine(o.line)).length > l.end && e.charAt(l.end) == r && l.end++, 
                            f = !0);
                            for (m = 0; m < C.length; ++m) a && !T(C[m], a, i) || h.push(r + C[m] + r);
                        } else for (var k in "attribute" == l.type && (a = l.string, f = !0), y) !y.hasOwnProperty(k) || a && !T(k, a, i) || h.push(k);
                    }
                    return {
                        list: h,
                        from: f ? M(o.line, null == u ? l.start : u) : o,
                        to: f ? M(o.line, l.end) : o
                    };
                }
            }
        });
    }(CodeMirror), function(i) {
        "use strict";
        var e, t = "ab aa af ak sq am ar an hy as av ae ay az bm ba eu be bn bh bi bs br bg my ca ch ce ny zh cv kw co cr hr cs da dv nl dz en eo et ee fo fj fi fr ff gl ka de el gn gu ht ha he hz hi ho hu ia id ie ga ig ik io is it iu ja jv kl kn kr ks kk km ki rw ky kv kg ko ku kj la lb lg li ln lo lt lu lv gv mk mg ms ml mt mi mr mh mn na nv nb nd ne ng nn no ii nr oc oj cu om or os pa pi fa pl ps pt qu rm rn ro ru sa sc sd se sm sg sr gd sn si sk sl so st es su sw ss sv ta te tg th ti bo tk tl tn to tr ts tt tw ty ug uk ur uz ve vi vo wa cy wo fy xh yi yo za zu".split(" "), n = [ "_blank", "_self", "_top", "_parent" ], r = [ "ascii", "utf-8", "utf-16", "latin1", "latin1" ], o = [ "get", "post", "put", "delete" ], l = [ "application/x-www-form-urlencoded", "multipart/form-data", "text/plain" ], s = [ "all", "screen", "print", "embossed", "braille", "handheld", "print", "projection", "screen", "tty", "tv", "speech", "3d-glasses", "resolution [>][<][=] [X]", "device-aspect-ratio: X/Y", "orientation:portrait", "orientation:landscape", "device-height: [X]", "device-width: [X]" ], a = {
            attrs: {}
        }, u = {
            a: {
                attrs: {
                    href: null,
                    ping: null,
                    type: null,
                    media: s,
                    target: n,
                    hreflang: t
                }
            },
            abbr: a,
            acronym: a,
            address: a,
            applet: a,
            area: {
                attrs: {
                    alt: null,
                    coords: null,
                    href: null,
                    target: null,
                    ping: null,
                    media: s,
                    hreflang: t,
                    type: null,
                    shape: [ "default", "rect", "circle", "poly" ]
                }
            },
            article: a,
            aside: a,
            audio: {
                attrs: {
                    src: null,
                    mediagroup: null,
                    crossorigin: [ "anonymous", "use-credentials" ],
                    preload: [ "none", "metadata", "auto" ],
                    autoplay: [ "", "autoplay" ],
                    loop: [ "", "loop" ],
                    controls: [ "", "controls" ]
                }
            },
            b: a,
            base: {
                attrs: {
                    href: null,
                    target: n
                }
            },
            basefont: a,
            bdi: a,
            bdo: a,
            big: a,
            blockquote: {
                attrs: {
                    cite: null
                }
            },
            body: a,
            br: a,
            button: {
                attrs: {
                    form: null,
                    formaction: null,
                    name: null,
                    value: null,
                    autofocus: [ "", "autofocus" ],
                    disabled: [ "", "autofocus" ],
                    formenctype: l,
                    formmethod: o,
                    formnovalidate: [ "", "novalidate" ],
                    formtarget: n,
                    type: [ "submit", "reset", "button" ]
                }
            },
            canvas: {
                attrs: {
                    width: null,
                    height: null
                }
            },
            caption: a,
            center: a,
            cite: a,
            code: a,
            col: {
                attrs: {
                    span: null
                }
            },
            colgroup: {
                attrs: {
                    span: null
                }
            },
            command: {
                attrs: {
                    type: [ "command", "checkbox", "radio" ],
                    label: null,
                    icon: null,
                    radiogroup: null,
                    command: null,
                    title: null,
                    disabled: [ "", "disabled" ],
                    checked: [ "", "checked" ]
                }
            },
            data: {
                attrs: {
                    value: null
                }
            },
            datagrid: {
                attrs: {
                    disabled: [ "", "disabled" ],
                    multiple: [ "", "multiple" ]
                }
            },
            datalist: {
                attrs: {
                    data: null
                }
            },
            dd: a,
            del: {
                attrs: {
                    cite: null,
                    datetime: null
                }
            },
            details: {
                attrs: {
                    open: [ "", "open" ]
                }
            },
            dfn: a,
            dir: a,
            div: a,
            dl: a,
            dt: a,
            em: a,
            embed: {
                attrs: {
                    src: null,
                    type: null,
                    width: null,
                    height: null
                }
            },
            eventsource: {
                attrs: {
                    src: null
                }
            },
            fieldset: {
                attrs: {
                    disabled: [ "", "disabled" ],
                    form: null,
                    name: null
                }
            },
            figcaption: a,
            figure: a,
            font: a,
            footer: a,
            form: {
                attrs: {
                    action: null,
                    name: null,
                    "accept-charset": r,
                    autocomplete: [ "on", "off" ],
                    enctype: l,
                    method: o,
                    novalidate: [ "", "novalidate" ],
                    target: n
                }
            },
            frame: a,
            frameset: a,
            h1: a,
            h2: a,
            h3: a,
            h4: a,
            h5: a,
            h6: a,
            head: {
                attrs: {},
                children: [ "title", "base", "link", "style", "meta", "script", "noscript", "command" ]
            },
            header: a,
            hgroup: a,
            hr: a,
            html: {
                attrs: {
                    manifest: null
                },
                children: [ "head", "body" ]
            },
            i: a,
            iframe: {
                attrs: {
                    src: null,
                    srcdoc: null,
                    name: null,
                    width: null,
                    height: null,
                    sandbox: [ "allow-top-navigation", "allow-same-origin", "allow-forms", "allow-scripts" ],
                    seamless: [ "", "seamless" ]
                }
            },
            img: {
                attrs: {
                    alt: null,
                    src: null,
                    ismap: null,
                    usemap: null,
                    width: null,
                    height: null,
                    crossorigin: [ "anonymous", "use-credentials" ]
                }
            },
            input: {
                attrs: {
                    alt: null,
                    dirname: null,
                    form: null,
                    formaction: null,
                    height: null,
                    list: null,
                    max: null,
                    maxlength: null,
                    min: null,
                    name: null,
                    pattern: null,
                    placeholder: null,
                    size: null,
                    src: null,
                    step: null,
                    value: null,
                    width: null,
                    accept: [ "audio/*", "video/*", "image/*" ],
                    autocomplete: [ "on", "off" ],
                    autofocus: [ "", "autofocus" ],
                    checked: [ "", "checked" ],
                    disabled: [ "", "disabled" ],
                    formenctype: l,
                    formmethod: o,
                    formnovalidate: [ "", "novalidate" ],
                    formtarget: n,
                    multiple: [ "", "multiple" ],
                    readonly: [ "", "readonly" ],
                    required: [ "", "required" ],
                    type: [ "hidden", "text", "search", "tel", "url", "email", "password", "datetime", "date", "month", "week", "time", "datetime-local", "number", "range", "color", "checkbox", "radio", "file", "submit", "image", "reset", "button" ]
                }
            },
            ins: {
                attrs: {
                    cite: null,
                    datetime: null
                }
            },
            kbd: a,
            keygen: {
                attrs: {
                    challenge: null,
                    form: null,
                    name: null,
                    autofocus: [ "", "autofocus" ],
                    disabled: [ "", "disabled" ],
                    keytype: [ "RSA" ]
                }
            },
            label: {
                attrs: {
                    for: null,
                    form: null
                }
            },
            legend: a,
            li: {
                attrs: {
                    value: null
                }
            },
            link: {
                attrs: {
                    href: null,
                    type: null,
                    hreflang: t,
                    media: s,
                    sizes: [ "all", "16x16", "16x16 32x32", "16x16 32x32 64x64" ]
                }
            },
            map: {
                attrs: {
                    name: null
                }
            },
            mark: a,
            menu: {
                attrs: {
                    label: null,
                    type: [ "list", "context", "toolbar" ]
                }
            },
            meta: {
                attrs: {
                    content: null,
                    charset: r,
                    name: [ "viewport", "application-name", "author", "description", "generator", "keywords" ],
                    "http-equiv": [ "content-language", "content-type", "default-style", "refresh" ]
                }
            },
            meter: {
                attrs: {
                    value: null,
                    min: null,
                    low: null,
                    high: null,
                    max: null,
                    optimum: null
                }
            },
            nav: a,
            noframes: a,
            noscript: a,
            object: {
                attrs: {
                    data: null,
                    type: null,
                    name: null,
                    usemap: null,
                    form: null,
                    width: null,
                    height: null,
                    typemustmatch: [ "", "typemustmatch" ]
                }
            },
            ol: {
                attrs: {
                    reversed: [ "", "reversed" ],
                    start: null,
                    type: [ "1", "a", "A", "i", "I" ]
                }
            },
            optgroup: {
                attrs: {
                    disabled: [ "", "disabled" ],
                    label: null
                }
            },
            option: {
                attrs: {
                    disabled: [ "", "disabled" ],
                    label: null,
                    selected: [ "", "selected" ],
                    value: null
                }
            },
            output: {
                attrs: {
                    for: null,
                    form: null,
                    name: null
                }
            },
            p: a,
            param: {
                attrs: {
                    name: null,
                    value: null
                }
            },
            pre: a,
            progress: {
                attrs: {
                    value: null,
                    max: null
                }
            },
            q: {
                attrs: {
                    cite: null
                }
            },
            rp: a,
            rt: a,
            ruby: a,
            s: a,
            samp: a,
            script: {
                attrs: {
                    type: [ "text/javascript" ],
                    src: null,
                    async: [ "", "async" ],
                    defer: [ "", "defer" ],
                    charset: r
                }
            },
            section: a,
            select: {
                attrs: {
                    form: null,
                    name: null,
                    size: null,
                    autofocus: [ "", "autofocus" ],
                    disabled: [ "", "disabled" ],
                    multiple: [ "", "multiple" ]
                }
            },
            small: a,
            source: {
                attrs: {
                    src: null,
                    type: null,
                    media: null
                }
            },
            span: a,
            strike: a,
            strong: a,
            style: {
                attrs: {
                    type: [ "text/css" ],
                    media: s,
                    scoped: null
                }
            },
            sub: a,
            summary: a,
            sup: a,
            table: a,
            tbody: a,
            td: {
                attrs: {
                    colspan: null,
                    rowspan: null,
                    headers: null
                }
            },
            textarea: {
                attrs: {
                    dirname: null,
                    form: null,
                    maxlength: null,
                    name: null,
                    placeholder: null,
                    rows: null,
                    cols: null,
                    autofocus: [ "", "autofocus" ],
                    disabled: [ "", "disabled" ],
                    readonly: [ "", "readonly" ],
                    required: [ "", "required" ],
                    wrap: [ "soft", "hard" ]
                }
            },
            tfoot: a,
            th: {
                attrs: {
                    colspan: null,
                    rowspan: null,
                    headers: null,
                    scope: [ "row", "col", "rowgroup", "colgroup" ]
                }
            },
            thead: a,
            time: {
                attrs: {
                    datetime: null
                }
            },
            title: a,
            tr: a,
            track: {
                attrs: {
                    src: null,
                    label: null,
                    default: null,
                    kind: [ "subtitles", "captions", "descriptions", "chapters", "metadata" ],
                    srclang: t
                }
            },
            tt: a,
            u: a,
            ul: a,
            var: a,
            video: {
                attrs: {
                    src: null,
                    poster: null,
                    width: null,
                    height: null,
                    crossorigin: [ "anonymous", "use-credentials" ],
                    preload: [ "auto", "metadata", "none" ],
                    autoplay: [ "", "autoplay" ],
                    mediagroup: [ "movie" ],
                    muted: [ "", "muted" ],
                    controls: [ "", "controls" ]
                }
            },
            wbr: a
        }, c = {
            accesskey: [ "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" ],
            class: null,
            contenteditable: [ "true", "false" ],
            contextmenu: null,
            dir: [ "ltr", "rtl", "auto" ],
            draggable: [ "true", "false", "auto" ],
            dropzone: [ "copy", "move", "link", "string:", "file:" ],
            hidden: [ "hidden" ],
            id: null,
            inert: [ "inert" ],
            itemid: null,
            itemprop: null,
            itemref: null,
            itemscope: [ "itemscope" ],
            itemtype: null,
            lang: [ "en", "es" ],
            spellcheck: [ "true", "false" ],
            autocorrect: [ "true", "false" ],
            autocapitalize: [ "true", "false" ],
            style: null,
            tabindex: [ "1", "2", "3", "4", "5", "6", "7", "8", "9" ],
            title: null,
            translate: [ "yes", "no" ],
            onclick: null,
            rel: [ "stylesheet", "alternate", "author", "bookmark", "help", "license", "next", "nofollow", "noreferrer", "prefetch", "prev", "search", "tag" ]
        };
        function h(e) {
            for (var t in c) c.hasOwnProperty(t) && (e.attrs[t] = c[t]);
        }
        for (e in h(a), u) u.hasOwnProperty(e) && u[e] != a && h(u[e]);
        i.htmlSchema = u, i.registerHelper("hint", "html", function(e, t) {
            var n = {
                schemaInfo: u
            };
            if (t) for (var r in t) n[r] = t[r];
            return i.hint.xml(e, n);
        });
    }(CodeMirror), function(S) {
        "use strict";
        var k = {
            autoSelfClosers: {
                area: !0,
                base: !0,
                br: !0,
                col: !0,
                command: !0,
                embed: !0,
                frame: !0,
                hr: !0,
                img: !0,
                input: !0,
                keygen: !0,
                link: !0,
                meta: !0,
                param: !0,
                source: !0,
                track: !0,
                wbr: !0,
                menuitem: !0
            },
            implicitlyClosed: {
                dd: !0,
                li: !0,
                optgroup: !0,
                option: !0,
                p: !0,
                rp: !0,
                rt: !0,
                tbody: !0,
                td: !0,
                tfoot: !0,
                th: !0,
                tr: !0
            },
            contextGrabbers: {
                dd: {
                    dd: !0,
                    dt: !0
                },
                dt: {
                    dd: !0,
                    dt: !0
                },
                li: {
                    li: !0
                },
                option: {
                    option: !0,
                    optgroup: !0
                },
                optgroup: {
                    optgroup: !0
                },
                p: {
                    address: !0,
                    article: !0,
                    aside: !0,
                    blockquote: !0,
                    dir: !0,
                    div: !0,
                    dl: !0,
                    fieldset: !0,
                    footer: !0,
                    form: !0,
                    h1: !0,
                    h2: !0,
                    h3: !0,
                    h4: !0,
                    h5: !0,
                    h6: !0,
                    header: !0,
                    hgroup: !0,
                    hr: !0,
                    menu: !0,
                    nav: !0,
                    ol: !0,
                    p: !0,
                    pre: !0,
                    section: !0,
                    table: !0,
                    ul: !0
                },
                rp: {
                    rp: !0,
                    rt: !0
                },
                rt: {
                    rp: !0,
                    rt: !0
                },
                tbody: {
                    tbody: !0,
                    tfoot: !0
                },
                td: {
                    td: !0,
                    th: !0
                },
                tfoot: {
                    tbody: !0
                },
                th: {
                    td: !0,
                    th: !0
                },
                thead: {
                    tbody: !0,
                    tfoot: !0
                },
                tr: {
                    tr: !0
                }
            },
            doNotIndent: {
                pre: !0
            },
            allowUnquoted: !0,
            allowMissing: !0,
            caseFold: !0
        }, L = {
            autoSelfClosers: {},
            implicitlyClosed: {},
            contextGrabbers: {},
            doNotIndent: {},
            allowUnquoted: !1,
            allowMissing: !1,
            allowMissingTagName: !1,
            caseFold: !1
        };
        S.defineMode("xml", function(e, t) {
            var n, o, l, s = e.indentUnit, a = {}, r = t.htmlMode ? k : L;
            for (n in r) a[n] = r[n];
            for (n in t) a[n] = t[n];
            function u(t, n) {
                function e(e) {
                    return (n.tokenize = e)(t, n);
                }
                var r = t.next();
                if ("<" == r) return t.eat("!") ? t.eat("[") ? t.match("CDATA[") ? e(i("atom", "]]>")) : null : t.match("--") ? e(i("comment", "--\x3e")) : t.match("DOCTYPE", !0, !0) ? (t.eatWhile(/[\w\._\-]/), 
                e(function r(i) {
                    return function(e, t) {
                        for (var n; null != (n = e.next()); ) {
                            if ("<" == n) return t.tokenize = r(i + 1), t.tokenize(e, t);
                            if (">" == n) {
                                if (1 != i) return t.tokenize = r(i - 1), t.tokenize(e, t);
                                t.tokenize = u;
                                break;
                            }
                        }
                        return "meta";
                    };
                }(1))) : null : t.eat("?") ? (t.eatWhile(/[\w\._\-]/), n.tokenize = i("meta", "?>"), 
                "meta") : (o = t.eat("/") ? "closeTag" : "openTag", n.tokenize = c, "tag bracket");
                if ("&" != r) return t.eatWhile(/[^&<]/), null;
                r = t.eat("#") ? t.eat("x") ? t.eatWhile(/[a-fA-F\d]/) && t.eat(";") : t.eatWhile(/[\d]/) && t.eat(";") : t.eatWhile(/[\w\.\-:]/) && t.eat(";");
                return r ? "atom" : "error";
            }
            function c(e, t) {
                var n = e.next();
                if (">" == n || "/" == n && e.eat(">")) return t.tokenize = u, o = ">" == n ? "endTag" : "selfcloseTag", 
                "tag bracket";
                if ("=" == n) return o = "equals", null;
                if ("<" != n) return /[\'\"]/.test(n) ? (t.tokenize = (r = n, i.isInAttribute = !0, 
                i), t.stringStartCol = e.column(), t.tokenize(e, t)) : (e.match(/^[^\s\u00a0=<>\"\']*[^\s\u00a0=<>\"\'\/]/), 
                "word");
                t.tokenize = u, t.state = p, t.tagName = t.tagStart = null;
                var r, t = t.tokenize(e, t);
                return t ? t + " tag error" : "tag error";
                function i(e, t) {
                    for (;!e.eol(); ) if (e.next() == r) {
                        t.tokenize = c;
                        break;
                    }
                    return "string";
                }
            }
            function i(n, r) {
                return function(e, t) {
                    for (;!e.eol(); ) {
                        if (e.match(r)) {
                            t.tokenize = u;
                            break;
                        }
                        e.next();
                    }
                    return n;
                };
            }
            function h(e, t, n) {
                this.prev = e.context, this.tagName = t, this.indent = e.indented, this.startOfLine = n, 
                (a.doNotIndent.hasOwnProperty(t) || e.context && e.context.noIndent) && (this.noIndent = !0);
            }
            function f(e) {
                e.context && (e.context = e.context.prev);
            }
            function d(e, t) {
                for (var n; ;) {
                    if (!e.context) return;
                    if (n = e.context.tagName, !a.contextGrabbers.hasOwnProperty(n) || !a.contextGrabbers[n].hasOwnProperty(t)) return;
                    f(e);
                }
            }
            function p(e, t, n) {
                return "openTag" == e ? (n.tagStart = t.column(), g) : "closeTag" == e ? m : p;
            }
            function g(e, t, n) {
                return "word" == e ? (n.tagName = t.current(), l = "tag", b) : a.allowMissingTagName && "endTag" == e ? (l = "tag bracket", 
                b(e, 0, n)) : (l = "error", g);
            }
            function m(e, t, n) {
                if ("word" != e) return a.allowMissingTagName && "endTag" == e ? (l = "tag bracket", 
                v(e, 0, n)) : (l = "error", y);
                t = t.current();
                return n.context && n.context.tagName != t && a.implicitlyClosed.hasOwnProperty(n.context.tagName) && f(n), 
                n.context && n.context.tagName == t || !1 === a.matchClosing ? (l = "tag", v) : (l = "tag error", 
                y);
            }
            function v(e, t, n) {
                return "endTag" != e ? (l = "error", v) : (f(n), p);
            }
            function y(e, t, n) {
                return l = "error", v(e, 0, n);
            }
            function b(e, t, n) {
                if ("word" == e) return l = "attribute", w;
                if ("endTag" != e && "selfcloseTag" != e) return l = "error", b;
                var r = n.tagName, i = n.tagStart;
                return n.tagName = n.tagStart = null, "selfcloseTag" == e || a.autoSelfClosers.hasOwnProperty(r) ? d(n, r) : (d(n, r), 
                n.context = new h(n, r, i == n.indented)), p;
            }
            function w(e, t, n) {
                return "equals" == e ? x : (a.allowMissing || (l = "error"), b(e, 0, n));
            }
            function x(e, t, n) {
                return "string" == e ? C : "word" == e && a.allowUnquoted ? (l = "string", b) : (l = "error", 
                b(e, 0, n));
            }
            function C(e, t, n) {
                return "string" == e ? C : b(e, 0, n);
            }
            return u.isInText = !0, {
                startState: function(e) {
                    var t = {
                        tokenize: u,
                        state: p,
                        indented: e || 0,
                        tagName: null,
                        tagStart: null,
                        context: null
                    };
                    return null != e && (t.baseIndent = e), t;
                },
                token: function(e, t) {
                    if (!t.tagName && e.sol() && (t.indented = e.indentation()), e.eatSpace()) return null;
                    o = null;
                    var n = t.tokenize(e, t);
                    return (n || o) && "comment" != n && (l = null, t.state = t.state(o || n, e, t), 
                    l && (n = "error" == l ? n + " error" : l)), n;
                },
                indent: function(e, t, n) {
                    var r = e.context;
                    if (e.tokenize.isInAttribute) return e.tagStart == e.indented ? e.stringStartCol + 1 : e.indented + s;
                    if (r && r.noIndent) return S.Pass;
                    if (e.tokenize != c && e.tokenize != u) return n ? n.match(/^(\s*)/)[0].length : 0;
                    if (e.tagName) return !1 !== a.multilineTagIndentPastTag ? e.tagStart + e.tagName.length + 2 : e.tagStart + s * (a.multilineTagIndentFactor || 1);
                    if (a.alignCDATA && /<!\[CDATA\[/.test(t)) return 0;
                    var i = t && /^<(\/)?([\w_:\.-]*)/.exec(t);
                    if (i && i[1]) for (;r; ) {
                        if (r.tagName == i[2]) {
                            r = r.prev;
                            break;
                        }
                        if (!a.implicitlyClosed.hasOwnProperty(r.tagName)) break;
                        r = r.prev;
                    } else if (i) for (;r; ) {
                        var o = a.contextGrabbers[r.tagName];
                        if (!o || !o.hasOwnProperty(i[2])) break;
                        r = r.prev;
                    }
                    for (;r && r.prev && !r.startOfLine; ) r = r.prev;
                    return r ? r.indent + s : e.baseIndent || 0;
                },
                electricInput: /<\/[\s\w:]+>$/,
                blockCommentStart: "\x3c!--",
                blockCommentEnd: "--\x3e",
                configuration: a.htmlMode ? "html" : "xml",
                helperType: a.htmlMode ? "html" : "xml",
                skipAttribute: function(e) {
                    e.state == x && (e.state = b);
                }
            };
        }), S.defineMIME("text/xml", "xml"), S.defineMIME("application/xml", "xml"), S.mimeModes.hasOwnProperty("text/html") || S.defineMIME("text/html", {
            name: "xml",
            htmlMode: !0
        });
    }(CodeMirror), function(h) {
        "use strict";
        var i = {
            script: [ [ "lang", /(javascript|babel)/i, "javascript" ], [ "type", /^(?:text|application)\/(?:x-)?(?:java|ecma)script$|^module$|^$/i, "javascript" ], [ "type", /./, "text/plain" ], [ null, null, "javascript" ] ],
            style: [ [ "lang", /^css$/i, "css" ], [ "type", /^(text\/)?(x-)?(stylesheet|css)$/i, "css" ], [ "type", /./, "text/plain" ], [ null, null, "css" ] ]
        };
        var n = {};
        function f(e, t) {
            t = e.match(n[t = t] || (n[t] = new RegExp("\\s+" + t + "\\s*=\\s*('|\")?([^'\"]+)('|\")?\\s*")));
            return t ? /^\s*(.*?)\s*$/.exec(t[2])[1] : "";
        }
        function d(e, t) {
            return new RegExp((t ? "^" : "") + "</s*" + e + "s*>", "i");
        }
        function o(e, t) {
            for (var n in e) for (var r = t[n] || (t[n] = []), i = e[n], o = i.length - 1; 0 <= o; o--) r.unshift(i[o]);
        }
        h.defineMode("htmlmixed", function(s, e) {
            var a = h.getMode(s, {
                name: "xml",
                htmlMode: !0,
                multilineTagIndentFactor: e.multilineTagIndentFactor,
                multilineTagIndentPastTag: e.multilineTagIndentPastTag
            }), u = {}, t = e && e.tags, n = e && e.scriptTypes;
            if (o(i, u), t && o(t, u), n) for (var r = n.length - 1; 0 <= r; r--) u.script.unshift([ "type", n[r].matches, n[r].mode ]);
            function c(e, t) {
                var n, o, l, r = a.token(e, t.htmlState), i = /\btag\b/.test(r);
                return i && !/[<>\s\/]/.test(e.current()) && (n = t.htmlState.tagName && t.htmlState.tagName.toLowerCase()) && u.hasOwnProperty(n) ? t.inTag = n + " " : t.inTag && i && />$/.test(e.current()) ? (n = /^([\S]+) (.*)/.exec(t.inTag), 
                t.inTag = null, i = ">" == e.current() && function(e, t) {
                    for (var n = 0; n < e.length; n++) {
                        var r = e[n];
                        if (!r[0] || r[1].test(f(t, r[0]))) return r[2];
                    }
                }(u[n[1]], n[2]), i = h.getMode(s, i), o = d(n[1], !0), l = d(n[1], !1), t.token = function(e, t) {
                    return e.match(o, !1) ? (t.token = c, t.localState = t.localMode = null) : (n = e, 
                    r = l, i = t.localMode.token(e, t.localState), e = n.current(), -1 < (t = e.search(r)) ? n.backUp(e.length - t) : e.match(/<\/?$/) && (n.backUp(e.length), 
                    n.match(r, !1) || n.match(e)), i);
                    var n, r, i;
                }, t.localMode = i, t.localState = h.startState(i, a.indent(t.htmlState, "", ""))) : t.inTag && (t.inTag += e.current(), 
                e.eol() && (t.inTag += " ")), r;
            }
            return {
                startState: function() {
                    return {
                        token: c,
                        inTag: null,
                        localMode: null,
                        localState: null,
                        htmlState: h.startState(a)
                    };
                },
                copyState: function(e) {
                    var t;
                    return e.localState && (t = h.copyState(e.localMode, e.localState)), {
                        token: e.token,
                        inTag: e.inTag,
                        localMode: e.localMode,
                        localState: t,
                        htmlState: h.copyState(a, e.htmlState)
                    };
                },
                token: function(e, t) {
                    return t.token(e, t);
                },
                indent: function(e, t, n) {
                    return !e.localMode || /^\s*<\//.test(t) ? a.indent(e.htmlState, t, n) : e.localMode.indent ? e.localMode.indent(e.localState, t, n) : h.Pass;
                },
                innerMode: function(e) {
                    return {
                        state: e.localState || e.htmlState,
                        mode: e.localMode || a
                    };
                }
            };
        }, "xml", "javascript", "css"), h.defineMIME("text/html", "htmlmixed");
    }(CodeMirror), function(c) {
        "use strict";
        var e = c.commands, h = c.Pos;
        function t(t, n) {
            t.extendSelectionsBy(function(e) {
                return t.display.shift || t.doc.extend || e.empty() ? function(e, t, n) {
                    if (n < 0 && 0 == t.ch) return e.clipPos(h(t.line - 1));
                    var r = e.getLine(t.line);
                    if (0 < n && t.ch >= r.length) return e.clipPos(h(t.line + 1, 0));
                    for (var i, o = "start", l = t.ch, s = n < 0 ? 0 : r.length; l != s; l += n, 0) {
                        var a = r.charAt(n < 0 ? l - 1 : l), u = "_" != a && c.isWordChar(a) ? "w" : "o";
                        if ("w" == u && a.toUpperCase() == a && (u = "W"), "start" == o) "o" != u && (o = "in", 
                        i = u); else if ("in" == o && i != u) {
                            if ("w" == i && "W" == u && n < 0 && l--, !("W" == i && "w" == u && 0 < n)) break;
                            i = "w";
                        }
                    }
                    return h(t.line, l);
                }(t.doc, e.head, n) : n < 0 ? e.from() : e.to();
            });
        }
        function n(l, s) {
            if (l.isReadOnly()) return c.Pass;
            l.operation(function() {
                for (var e = l.listSelections().length, t = [], n = -1, r = 0; r < e; r++) {
                    var i, o = l.listSelections()[r].head;
                    o.line <= n || (i = h(o.line + (s ? 0 : 1), 0), l.replaceRange("\n", i, null, "+insertLine"), 
                    l.indentLine(i.line, null, !0), t.push({
                        head: i,
                        anchor: i
                    }), n = o.line + 1);
                }
                l.setSelections(t);
            }), l.execCommand("indentAuto");
        }
        function u(e, t) {
            for (var n = t.ch, r = n, i = e.getLine(t.line); n && c.isWordChar(i.charAt(n - 1)); ) --n;
            for (;r < i.length && c.isWordChar(i.charAt(r)); ) ++r;
            return {
                from: h(t.line, n),
                to: h(t.line, r),
                word: i.slice(n, r)
            };
        }
        function r(e, t) {
            for (var n = e.listSelections(), r = [], i = 0; i < n.length; i++) {
                var o = n[i], l = e.findPosV(o.anchor, t, "line", o.anchor.goalColumn), s = e.findPosV(o.head, t, "line", o.head.goalColumn);
                l.goalColumn = null != o.anchor.goalColumn ? o.anchor.goalColumn : e.cursorCoords(o.anchor, "div").left, 
                s.goalColumn = null != o.head.goalColumn ? o.head.goalColumn : e.cursorCoords(o.head, "div").left;
                s = {
                    anchor: l,
                    head: s
                };
                r.push(o), r.push(s);
            }
            e.setSelections(r);
        }
        e.goSubwordLeft = function(e) {
            t(e, -1);
        }, e.goSubwordRight = function(e) {
            t(e, 1);
        }, e.scrollLineUp = function(e) {
            var t, n = e.getScrollInfo();
            e.somethingSelected() || (t = e.lineAtHeight(n.top + n.clientHeight, "local"), e.getCursor().line >= t && e.execCommand("goLineUp")), 
            e.scrollTo(null, n.top - e.defaultTextHeight());
        }, e.scrollLineDown = function(e) {
            var t, n = e.getScrollInfo();
            e.somethingSelected() || (t = e.lineAtHeight(n.top, "local") + 1, e.getCursor().line <= t && e.execCommand("goLineDown")), 
            e.scrollTo(null, n.top + e.defaultTextHeight());
        }, e.splitSelectionByLine = function(e) {
            for (var t = e.listSelections(), n = [], r = 0; r < t.length; r++) for (var i = t[r].from(), o = t[r].to(), l = i.line; l <= o.line; ++l) o.line > i.line && l == o.line && 0 == o.ch || n.push({
                anchor: l == i.line ? i : h(l, 0),
                head: l == o.line ? o : h(l)
            });
            e.setSelections(n, 0);
        }, e.singleSelectionTop = function(e) {
            var t = e.listSelections()[0];
            e.setSelection(t.anchor, t.head, {
                scroll: !1
            });
        }, e.selectLine = function(e) {
            for (var t = e.listSelections(), n = [], r = 0; r < t.length; r++) {
                var i = t[r];
                n.push({
                    anchor: h(i.from().line, 0),
                    head: h(i.to().line + 1, 0)
                });
            }
            e.setSelections(n);
        }, e.insertLineAfter = function(e) {
            return n(e, !1);
        }, e.insertLineBefore = function(e) {
            return n(e, !0);
        }, e.selectNextOccurrence = function(e) {
            var t = e.getCursor("from"), n = e.getCursor("to"), r = e.state.sublimeFindFullWord == e.doc.sel;
            if (0 == c.cmpPos(t, n)) {
                var i = u(e, t);
                if (!i.word) return;
                e.setSelection(i.from, i.to), r = !0;
            } else {
                t = e.getRange(t, n), t = r ? new RegExp("\\b" + t + "\\b") : t, n = e.getSearchCursor(t, n);
                if (!(n.findNext() || (n = e.getSearchCursor(t, h(e.firstLine(), 0))).findNext()) || function(e, t, n) {
                    for (var r = 0; r < e.length; r++) if (e[r].from() == t && e[r].to() == n) return !0;
                    return !1;
                }(e.listSelections(), n.from(), n.to())) return c.Pass;
                e.addSelection(n.from(), n.to());
            }
            r && (e.state.sublimeFindFullWord = e.doc.sel);
        }, e.addCursorToPrevLine = function(e) {
            r(e, -1);
        }, e.addCursorToNextLine = function(e) {
            r(e, 1);
        };
        function i(e) {
            for (var t = e.listSelections(), n = [], r = 0; r < t.length; r++) {
                var i = t[r], o = i.head, l = e.scanForBracket(o, -1);
                if (!l) return;
                for (;;) {
                    var s = e.scanForBracket(o, 1);
                    if (!s) return;
                    if (s.ch == "(){}[]".charAt("(){}[]".indexOf(l.ch) + 1)) {
                        var a = h(l.pos.line, l.pos.ch + 1);
                        if (0 != c.cmpPos(a, i.from()) || 0 != c.cmpPos(s.pos, i.to())) {
                            n.push({
                                anchor: a,
                                head: s.pos
                            });
                            break;
                        }
                        if (!(l = e.scanForBracket(l.pos, -1))) return;
                    }
                    o = h(s.pos.line, s.pos.ch + 1);
                }
            }
            return e.setSelections(n), 1;
        }
        function o(l, s) {
            if (l.isReadOnly()) return c.Pass;
            for (var a, e = l.listSelections(), u = [], t = 0; t < e.length; t++) {
                var n = e[t];
                if (!n.empty()) {
                    for (var r = n.from().line, i = n.to().line; t < e.length - 1 && e[t + 1].from().line == i; ) i = e[++t].to().line;
                    e[t].to().ch || i--, u.push(r, i);
                }
            }
            u.length ? a = !0 : u.push(l.firstLine(), l.lastLine()), l.operation(function() {
                for (var e = [], t = 0; t < u.length; t += 2) {
                    var n = u[t], r = u[t + 1], i = h(n, 0), o = h(r), n = l.getRange(i, o, !1);
                    s ? n.sort() : n.sort(function(e, t) {
                        var n = e.toUpperCase(), r = t.toUpperCase();
                        return n != r && (e = n, t = r), e < t ? -1 : e == t ? 0 : 1;
                    }), l.replaceRange(n, i, o), a && e.push({
                        anchor: i,
                        head: h(r + 1, 0)
                    });
                }
                a && l.setSelections(e, 0);
            });
        }
        function l(s, a) {
            s.operation(function() {
                for (var e = s.listSelections(), t = [], n = [], r = 0; r < e.length; r++) {
                    (l = e[r]).empty() ? (t.push(r), n.push("")) : n.push(a(s.getRange(l.from(), l.to())));
                }
                s.replaceSelections(n, "around", "case");
                for (r = t.length - 1; 0 <= r; r--) {
                    var i, o, l = e[t[r]];
                    o && 0 < c.cmpPos(l.head, o) || (o = (i = u(s, l.head)).from, s.replaceRange(a(i.word), i.from, i.to));
                }
            });
        }
        function s(e) {
            var t = e.getCursor("from"), n = e.getCursor("to");
            if (0 == c.cmpPos(t, n)) {
                var r = u(e, t);
                if (!r.word) return;
                t = r.from, n = r.to;
            }
            return {
                from: t,
                to: n,
                query: e.getRange(t, n),
                word: r
            };
        }
        function a(e, t) {
            var n, r, i = s(e);
            i && (n = i.query, r = e.getSearchCursor(n, t ? i.to : i.from), (t ? r.findNext() : r.findPrevious()) ? e.setSelection(r.from(), r.to()) : (r = e.getSearchCursor(n, t ? h(e.firstLine(), 0) : e.clipPos(h(e.lastLine()))), 
            (t ? r.findNext() : r.findPrevious()) ? e.setSelection(r.from(), r.to()) : i.word && e.setSelection(i.from, i.to)));
        }
        e.selectScope = function(e) {
            i(e) || e.execCommand("selectAll");
        }, e.selectBetweenBrackets = function(e) {
            if (!i(e)) return c.Pass;
        }, e.goToBracket = function(n) {
            n.extendSelectionsBy(function(e) {
                var t = n.scanForBracket(e.head, 1);
                if (t && 0 != c.cmpPos(t.pos, e.head)) return t.pos;
                t = n.scanForBracket(e.head, -1);
                return t && h(t.pos.line, t.pos.ch + 1) || e.head;
            });
        }, e.swapLineUp = function(i) {
            if (i.isReadOnly()) return c.Pass;
            for (var e = i.listSelections(), o = [], t = i.firstLine() - 1, l = [], n = 0; n < e.length; n++) {
                var r = e[n], s = r.from().line - 1, a = r.to().line;
                l.push({
                    anchor: h(r.anchor.line - 1, r.anchor.ch),
                    head: h(r.head.line - 1, r.head.ch)
                }), 0 != r.to().ch || r.empty() || --a, t < s ? o.push(s, a) : o.length && (o[o.length - 1] = a), 
                t = a;
            }
            i.operation(function() {
                for (var e = 0; e < o.length; e += 2) {
                    var t = o[e], n = o[e + 1], r = i.getLine(t);
                    i.replaceRange("", h(t, 0), h(t + 1, 0), "+swapLine"), n > i.lastLine() ? i.replaceRange("\n" + r, h(i.lastLine()), null, "+swapLine") : i.replaceRange(r + "\n", h(n, 0), null, "+swapLine");
                }
                i.setSelections(l), i.scrollIntoView();
            });
        }, e.swapLineDown = function(i) {
            if (i.isReadOnly()) return c.Pass;
            for (var e = i.listSelections(), o = [], t = i.lastLine() + 1, n = e.length - 1; 0 <= n; n--) {
                var r = e[n], l = r.to().line + 1, s = r.from().line;
                0 != r.to().ch || r.empty() || l--, l < t ? o.push(l, s) : o.length && (o[o.length - 1] = s), 
                t = s;
            }
            i.operation(function() {
                for (var e = o.length - 2; 0 <= e; e -= 2) {
                    var t = o[e], n = o[e + 1], r = i.getLine(t);
                    t == i.lastLine() ? i.replaceRange("", h(t - 1), h(t), "+swapLine") : i.replaceRange("", h(t, 0), h(t + 1, 0), "+swapLine"), 
                    i.replaceRange(r + "\n", h(n, 0), null, "+swapLine");
                }
                i.scrollIntoView();
            });
        }, e.toggleCommentIndented = function(e) {
            e.toggleComment({
                indent: !0
            });
        }, e.joinLines = function(a) {
            for (var e = a.listSelections(), u = [], t = 0; t < e.length; t++) {
                for (var n = e[t], r = n.from(), i = r.line, o = n.to().line; t < e.length - 1 && e[t + 1].from().line == o; ) o = e[++t].to().line;
                u.push({
                    start: i,
                    end: o,
                    anchor: !n.empty() && r
                });
            }
            a.operation(function() {
                for (var e = 0, t = [], n = 0; n < u.length; n++) {
                    for (var r, i = u[n], o = i.anchor && h(i.anchor.line - e, i.anchor.ch), l = i.start; l <= i.end; l++) {
                        var s = l - e;
                        l == i.end && (r = h(s, a.getLine(s).length + 1)), s < a.lastLine() && (a.replaceRange(" ", h(s), h(1 + s, /^\s*/.exec(a.getLine(1 + s))[0].length)), 
                        ++e);
                    }
                    t.push({
                        anchor: o || r,
                        head: r
                    });
                }
                a.setSelections(t, 0);
            });
        }, e.duplicateLine = function(r) {
            r.operation(function() {
                for (var e = r.listSelections().length, t = 0; t < e; t++) {
                    var n = r.listSelections()[t];
                    n.empty() ? r.replaceRange(r.getLine(n.head.line) + "\n", h(n.head.line, 0)) : r.replaceRange(r.getRange(n.from(), n.to()), n.from());
                }
                r.scrollIntoView();
            });
        }, e.sortLines = function(e) {
            o(e, !0);
        }, e.sortLinesInsensitive = function(e) {
            o(e, !1);
        }, e.nextBookmark = function(e) {
            var t = e.state.sublimeBookmarks;
            if (t) for (;t.length; ) {
                var n = t.shift(), r = n.find();
                if (r) return t.push(n), e.setSelection(r.from, r.to);
            }
        }, e.prevBookmark = function(e) {
            var t = e.state.sublimeBookmarks;
            if (t) for (;t.length; ) {
                t.unshift(t.pop());
                var n = t[t.length - 1].find();
                if (n) return e.setSelection(n.from, n.to);
                t.pop();
            }
        }, e.toggleBookmark = function(e) {
            for (var t = e.listSelections(), n = e.state.sublimeBookmarks || (e.state.sublimeBookmarks = []), r = 0; r < t.length; r++) {
                for (var i = t[r].from(), o = t[r].to(), l = t[r].empty() ? e.findMarksAt(i) : e.findMarks(i, o), s = 0; s < l.length; s++) if (l[s].sublimeBookmark) {
                    l[s].clear();
                    for (var a = 0; a < n.length; a++) n[a] == l[s] && n.splice(a--, 1);
                    break;
                }
                s == l.length && n.push(e.markText(i, o, {
                    sublimeBookmark: !0,
                    clearWhenEmpty: !1
                }));
            }
        }, e.clearBookmarks = function(e) {
            var t = e.state.sublimeBookmarks;
            if (t) for (var n = 0; n < t.length; n++) t[n].clear();
            t.length = 0;
        }, e.selectBookmarks = function(e) {
            var t = e.state.sublimeBookmarks, n = [];
            if (t) for (var r = 0; r < t.length; r++) {
                var i = t[r].find();
                i ? n.push({
                    anchor: i.from,
                    head: i.to
                }) : t.splice(r--, 0);
            }
            n.length && e.setSelections(n, 0);
        }, e.smartBackspace = function(s) {
            if (s.somethingSelected()) return c.Pass;
            s.operation(function() {
                for (var e = s.listSelections(), t = s.getOption("indentUnit"), n = e.length - 1; 0 <= n; n--) {
                    var r = e[n].head, i = s.getRange({
                        line: r.line,
                        ch: 0
                    }, r), o = c.countColumn(i, null, s.getOption("tabSize")), l = s.findPosH(r, -1, "char", !1);
                    !i || /\S/.test(i) || o % t != 0 || (o = new h(r.line, c.findColumn(i, o - t, t))).ch != r.ch && (l = o), 
                    s.replaceRange("", l, r, "+delete");
                }
            });
        }, e.delLineRight = function(n) {
            n.operation(function() {
                for (var e = n.listSelections(), t = e.length - 1; 0 <= t; t--) n.replaceRange("", e[t].anchor, h(e[t].to().line), "+delete");
                n.scrollIntoView();
            });
        }, e.upcaseAtCursor = function(e) {
            l(e, function(e) {
                return e.toUpperCase();
            });
        }, e.downcaseAtCursor = function(e) {
            l(e, function(e) {
                return e.toLowerCase();
            });
        }, e.setSublimeMark = function(e) {
            e.state.sublimeMark && e.state.sublimeMark.clear(), e.state.sublimeMark = e.setBookmark(e.getCursor());
        }, e.selectToSublimeMark = function(e) {
            var t = e.state.sublimeMark && e.state.sublimeMark.find();
            t && e.setSelection(e.getCursor(), t);
        }, e.deleteToSublimeMark = function(e) {
            var t, n, r = e.state.sublimeMark && e.state.sublimeMark.find();
            r && (n = e.getCursor(), t = r, 0 < c.cmpPos(n, t) && (r = t, t = n, n = r), e.state.sublimeKilled = e.getRange(n, t), 
            e.replaceRange("", n, t));
        }, e.swapWithSublimeMark = function(e) {
            var t = e.state.sublimeMark && e.state.sublimeMark.find();
            t && (e.state.sublimeMark.clear(), e.state.sublimeMark = e.setBookmark(e.getCursor()), 
            e.setCursor(t));
        }, e.sublimeYank = function(e) {
            null != e.state.sublimeKilled && e.replaceSelection(e.state.sublimeKilled, null, "paste");
        }, e.showInCenter = function(e) {
            var t = e.cursorCoords(null, "local");
            e.scrollTo(null, (t.top + t.bottom) / 2 - e.getScrollInfo().clientHeight / 2);
        }, e.findUnder = function(e) {
            a(e, !0);
        }, e.findUnderPrevious = function(e) {
            a(e, !1);
        }, e.findAllUnder = function(e) {
            var t = s(e);
            if (t) {
                for (var n = e.getSearchCursor(t.query), r = [], i = -1; n.findNext(); ) r.push({
                    anchor: n.from(),
                    head: n.to()
                }), n.from().line <= t.from.line && n.from().ch <= t.from.ch && i++;
                e.setSelections(r, i);
            }
        };
        var f = c.keyMap;
        f.macSublime = {
            "Cmd-Left": "goLineStartSmart",
            "Shift-Tab": "indentLess",
            "Shift-Ctrl-K": "deleteLine",
            "Alt-Q": "wrapLines",
            "Ctrl-Left": "goSubwordLeft",
            "Ctrl-Right": "goSubwordRight",
            "Ctrl-Alt-Up": "scrollLineUp",
            "Ctrl-Alt-Down": "scrollLineDown",
            "Cmd-L": "selectLine",
            "Shift-Cmd-L": "splitSelectionByLine",
            Esc: "singleSelectionTop",
            "Cmd-Enter": "insertLineAfter",
            "Shift-Cmd-Enter": "insertLineBefore",
            "Cmd-D": "selectNextOccurrence",
            "Shift-Cmd-Space": "selectScope",
            "Shift-Cmd-M": "selectBetweenBrackets",
            "Cmd-M": "goToBracket",
            "Cmd-Ctrl-Up": "swapLineUp",
            "Cmd-Ctrl-Down": "swapLineDown",
            "Cmd-/": "toggleCommentIndented",
            "Cmd-J": "joinLines",
            "Shift-Cmd-D": "duplicateLine",
            F5: "sortLines",
            "Cmd-F5": "sortLinesInsensitive",
            F2: "nextBookmark",
            "Shift-F2": "prevBookmark",
            "Cmd-F2": "toggleBookmark",
            "Shift-Cmd-F2": "clearBookmarks",
            "Alt-F2": "selectBookmarks",
            Backspace: "smartBackspace",
            "Cmd-K Cmd-K": "delLineRight",
            "Cmd-K Cmd-U": "upcaseAtCursor",
            "Cmd-K Cmd-L": "downcaseAtCursor",
            "Cmd-K Cmd-Space": "setSublimeMark",
            "Cmd-K Cmd-A": "selectToSublimeMark",
            "Cmd-K Cmd-W": "deleteToSublimeMark",
            "Cmd-K Cmd-X": "swapWithSublimeMark",
            "Cmd-K Cmd-Y": "sublimeYank",
            "Cmd-K Cmd-C": "showInCenter",
            "Cmd-K Cmd-G": "clearBookmarks",
            "Cmd-K Cmd-Backspace": "delLineLeft",
            "Cmd-K Cmd-0": "unfoldAll",
            "Cmd-K Cmd-J": "unfoldAll",
            "Ctrl-Shift-Up": "addCursorToPrevLine",
            "Ctrl-Shift-Down": "addCursorToNextLine",
            "Cmd-F3": "findUnder",
            "Shift-Cmd-F3": "findUnderPrevious",
            "Alt-F3": "findAllUnder",
            "Shift-Cmd-[": "fold",
            "Shift-Cmd-]": "unfold",
            "Cmd-I": "findIncremental",
            "Shift-Cmd-I": "findIncrementalReverse",
            "Cmd-H": "replace",
            F3: "findNext",
            "Shift-F3": "findPrev",
            fallthrough: "macDefault"
        }, c.normalizeKeyMap(f.macSublime), f.pcSublime = {
            "Shift-Tab": "indentLess",
            "Shift-Ctrl-K": "deleteLine",
            "Alt-Q": "wrapLines",
            "Ctrl-T": "transposeChars",
            "Alt-Left": "goSubwordLeft",
            "Alt-Right": "goSubwordRight",
            "Ctrl-Up": "scrollLineUp",
            "Ctrl-Down": "scrollLineDown",
            "Ctrl-L": "selectLine",
            "Shift-Ctrl-L": "splitSelectionByLine",
            Esc: "singleSelectionTop",
            "Ctrl-Enter": "insertLineAfter",
            "Shift-Ctrl-Enter": "insertLineBefore",
            "Ctrl-D": "selectNextOccurrence",
            "Shift-Ctrl-Space": "selectScope",
            "Shift-Ctrl-M": "selectBetweenBrackets",
            "Ctrl-M": "goToBracket",
            "Shift-Ctrl-Up": "swapLineUp",
            "Shift-Ctrl-Down": "swapLineDown",
            "Ctrl-/": "toggleCommentIndented",
            "Ctrl-J": "joinLines",
            "Shift-Ctrl-D": "duplicateLine",
            F9: "sortLines",
            "Ctrl-F9": "sortLinesInsensitive",
            F2: "nextBookmark",
            "Shift-F2": "prevBookmark",
            "Ctrl-F2": "toggleBookmark",
            "Shift-Ctrl-F2": "clearBookmarks",
            "Alt-F2": "selectBookmarks",
            Backspace: "smartBackspace",
            "Ctrl-K Ctrl-K": "delLineRight",
            "Ctrl-K Ctrl-U": "upcaseAtCursor",
            "Ctrl-K Ctrl-L": "downcaseAtCursor",
            "Ctrl-K Ctrl-Space": "setSublimeMark",
            "Ctrl-K Ctrl-A": "selectToSublimeMark",
            "Ctrl-K Ctrl-W": "deleteToSublimeMark",
            "Ctrl-K Ctrl-X": "swapWithSublimeMark",
            "Ctrl-K Ctrl-Y": "sublimeYank",
            "Ctrl-K Ctrl-C": "showInCenter",
            "Ctrl-K Ctrl-G": "clearBookmarks",
            "Ctrl-K Ctrl-Backspace": "delLineLeft",
            "Ctrl-K Ctrl-0": "unfoldAll",
            "Ctrl-K Ctrl-J": "unfoldAll",
            "Ctrl-Alt-Up": "addCursorToPrevLine",
            "Ctrl-Alt-Down": "addCursorToNextLine",
            "Ctrl-F3": "findUnder",
            "Shift-Ctrl-F3": "findUnderPrevious",
            "Alt-F3": "findAllUnder",
            "Shift-Ctrl-[": "fold",
            "Shift-Ctrl-]": "unfold",
            "Ctrl-I": "findIncremental",
            "Shift-Ctrl-I": "findIncrementalReverse",
            "Ctrl-H": "replace",
            F3: "findNext",
            "Shift-F3": "findPrev",
            fallthrough: "pcDefault"
        }, c.normalizeKeyMap(f.pcSublime);
        e = f.default == f.macDefault;
        f.sublime = e ? f.macSublime : f.pcSublime;
    }(CodeMirror);
    var t = window.CodeMirror;
    return window.CodeMirror = e, t;
}.bind(this));