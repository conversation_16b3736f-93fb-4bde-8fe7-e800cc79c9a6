// import React, { FC } from 'react';
// import ReactMarkdown from 'react-markdown';
// import { message } from 'antd';
// import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
// import { vscDarkPlus, coyWithoutShadows, darcula } from 'react-syntax-highlighter/dist/esm/styles/prism';
// import './styles.less';
// import { useMemo } from 'react';


// type tProps = {
//   textContent: string
//   darkMode?: boolean; // markdown文本
// }

// const them = {
//   dark: vscDarkPlus,
//   light: coyWithoutShadows
// };

// const RewriteMarkDown = (props: tProps) => {
//   const { textContent, darkMode } = props;


//   if (typeof darkMode === 'undefined') {
//     them.light = darcula;
//   }
//   if (typeof darkMode === 'boolean') {
//     them.light = coyWithoutShadows;
//   }

//   // 复制
//   const handleCopy = (codeString: any, e: any) => {
//     e.stopPropagation();
//     let timer: any = null;
//     e.target.innerHTML = '已复制!';

//     timer = setTimeout(() => {
//       e.target.innerHTML = '复制代码';
//       clearTimeout(timer);
//       timer = null;
//     }, 2000);
//     navigator.clipboard.writeText(codeString)
//       .then(() => {
//         message.success('复制成功！');
//       })
//       .catch(err => {
//         message.error(err || '复制失败！');
//       });
//   };

//   // 折叠
//   const foldContent = (e: any) => {
//     const siblings = [];  // 除去自身的兄弟节点
//     const currentNode = e.target;
//     // 获取父元素的子节点
//     const childNodes = currentNode.parentNode.childNodes;
//     // 循环遍历所有子节点，将除了自己之外的兄弟节点添加到数组中
//     for (let i = 0; i < childNodes.length; i++) {
//       if (childNodes[i] !== currentNode) {
//         siblings.push(childNodes[i]);
//       }
//     }

//     siblings.forEach((item) => {
//       // 获取元素的最终计算样式
//       const displayStyle = window.getComputedStyle(item).display;
//       displayStyle === 'none' ? item.style.display = '' : item.style.display = 'none';
//     })

//   }

//   const ReactMarkdownDOM = useMemo(() => {
//     return (
//       <ReactMarkdown
//         className="rewrite-markdown-style"
//         components={{
//           code({ node, inline, className, children, ...props }) {
//             const match = /language-(\w+)/.exec(className || '');
//             return !inline && match ? (
//               <div>
//                 <div className='right-top-tip'
//                   onClick={(e) => { foldContent(e) }}>
//                   <span>
//                     {match[1]}
//                   </span>
//                   <span
//                     className="copy-button-markdown"
//                     onClick={(e) => { handleCopy(String(children).replace(/\n$/, ''), e); }}
//                   >
//                     复制代码
//                   </span>
//                 </div>
//                 <SyntaxHighlighter
//                   showLineNumbers={true}
//                   style={darkMode ? them.dark : them.light}
//                   language={match[1]}
//                   PreTag='div'
//                   customStyle={{ margin: 0 }}
//                   {...props}
//                 >
//                   {String(children).replace(/\n$/, '')}
//                 </SyntaxHighlighter>
//               </div>
//             ) : (
//               <code className={className} {...props}>
//                 {children}
//               </code>
//             );
//           }
//         }}
//       >
//         {textContent}
//       </ReactMarkdown>
//     )
//   }, [textContent])

//   return ReactMarkdownDOM;
// };

// export default RewriteMarkDown;