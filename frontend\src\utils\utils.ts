import { message } from 'antd';

/**
 * @description 取当前页面的根路径,打开新页面
 * @param rootPathname
 * @param pathname
 */
export function openNewPage(
  rootPathname: string,
  pathname: string,
  query?: Record<string, any>,
) {
  const url = `${
    location.origin
  }${rootPathname}/#${pathname}${
    query ? objToQueryString(query) : ''
  }`;
  window.open(url);
}

/**
 * @description: 对象转query string
 * @param o Object
 * @param  needMask 是否需要query问号
 */
export const objToQueryString = (
  o: Record<string, any>,
  needMask = true,
) => {
  return `${needMask ? '?' : ''}${Object.entries(o)
    .reduce(
      (searchParams, [name, value]) => (
        searchParams.append(name, String(value)),
        searchParams
      ),
      new URLSearchParams(),
    )
    .toString()}`;
};

/** @description: 生成样式 */
export function ClassNames(...rest: string[]) {
  return rest.join(' ');
}

/** @description: 生成柔和的随机颜色 */
export function randomColor() {
  return `hsl(${Math.random() * 360}, 100%, 75%)`;
}

/** @description 数字转汉字周 */
export function numToWeek(num: number) {
  return ['一', '二', '三', '四', '五', '六', '日'][
    num - 1
  ];
}
/**@description 蒋两个数字化为没有公约数的形式并返回 */
export function getNoCommonDivisor(
  num1: number,
  num2: number,
) {
  let [a, b] = [num1, num2];
  while (b !== 0) {
    [a, b] = [b, a % b];
  }
  return [num1 / a, num2 / a];
}

/** @description 获取文件后缀*/
export function urlSuffix(url: string) {
  return (
    url
      .split('.')
      .pop()
      ?.toLowerCase() ?? ''
  );
}

/**
 * @param keys 取key
 * @description 取对象中的某些key
 */
export function pick<
  T extends Record<string, any>,
  K extends keyof T
>(obj: T, keys: K[]) {
  return Object.fromEntries(
    Object.entries(obj).filter(([key]) =>
      keys.includes(key as K),
    ),
  ) as Pick<T, K>;
}

/**
 * @description 对象字段去undefined null
 */
export const removeEmpty = <T extends Record<string, any>>(
  obj: T,
): NonNullable<T> => {
  Object.keys(obj).forEach((key) => {
    if (obj[key] === undefined || obj[key] === null) {
      delete obj[key];
    }
  });
  return obj;
};

/**
 * @description: 监听条件是否满足,满足执行回调
 * @param  condition 条件
 * @param  callback 回调
 * @param  time 间隔时间
 */
export async function ensure(
  condition: () => boolean,
  callback?: Function,
  time = 400,
) {
  if (condition()) {
    callback?.();
  } else {
    await sleep(time);
    ensure(condition, callback, time);
  }
}
/**
 * @param time 延迟时间
 * @description 睡一会~
 */
export const sleep = (time: number) => {
  return new Promise((res, rej) => {
    setTimeout(() => {
      res(true);
    }, time);
  });
};

/**
 *  @description file转base64
 *  @param file 文件
 */
export function fileToBase64(
  file?: File,
): Promise<string | ArrayBuffer | null> {
  return new Promise((resolve, reject) => {
    if (!file) return resolve(null);
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      resolve(reader.result);
    };
    reader.onerror = (error) => {
      reject(error);
    };
  });
}

/**
 * @param fn
 * @param delay
 * @description 防抖
 */
export const debounce = (fn: Function, delay: number) => {
  let timer: any = null;
  return (...rest: any) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...rest);
    }, delay);
  };
};

/**
 * @param keys 排除的key
 * @description 排除对象中的某些key
 */
export function omit<
  T extends Record<string, any>,
  K extends keyof T
>(obj: T, keys: K[]) {
  return Object.fromEntries(
    Object.entries(obj).filter(
      ([key]) => !keys.includes(key as K),
    ),
  ) as Omit<T, K>;
}

/**
 * @description: 数组转对象
 */
export function arrayToObject<T extends any>(array: T[]) {
  return Object.fromEntries(
    array.map((item) => [item, item]),
  );
}

export function deepCopy(obj: any) {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * @description 将数组元素从index开始所有元素向后移动x位
 * @example moveArrayElement([1,2,3,4,5],1,2) => [1,empty,empty,2,3,4,5]
 */
export function moveArrayElement<T>(
  arr: T[],
  index: number,
  x: number,
) {
  const element = arr.splice(index, arr.length - index);
  // arr.splice(index+x, 0, ...element);
  for (let i = 0; i < element.length; i++) {
    arr[i + index + x] = element[i];
  }
  return arr;
}

// 当splice超出数组长度添加不了元素的问题.
export function spliceAdd<T>(
  arr: T[],
  index: number,
  ...rest: T[]
) {
  if (index === arr.length - 1) {
    arr.push(...rest);
  } else {
    arr.splice(index, 0, ...rest);
  }
  return arr;
}

export const isDevelopment =
  process.env.NODE_ENV === 'development';

/** 清除数组中的空值 */
export function clearAryEmpty<T extends any>(
  arr: T[],
): NonNullable<T>[] {
  return arr.filter((v) => !isNul(v)) as NonNullable<T>[];
}

/** 判断是否为空值 */
export function isNul<T extends any>(v: T) {
  return v === null || v === undefined;
}

/** 从桶返回的url一般不带头,拼接 */
export function getImgUrlFromBucket(url?: string) {
  if (!url) {
    return url;
  }
  const head = isDevelopment
    ? 'http://172.16.151.202'
    : location.origin;
  return head + url;
}

// 兼容性考虑
export function blobToArrayBuffer(blob: Blob) {
  return new Promise<ArrayBuffer>((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.onload = function() {
      resolve(this.result as ArrayBuffer);
    };
    fileReader.onerror = function() {
      reject(new Error('blob转arraybuffer失败'));
    };
    fileReader.readAsArrayBuffer(blob);
  });
}
export function blobToFile(blob: Blob, name: string) {
  return new File([blob], name);
}

export async function txtToString(blob: Blob) {
  const buffer = await blobToArrayBuffer(blob);
  return new TextDecoder('utf-8').decode(buffer);
}

export function stringToBlob(text: string) {
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  return new Blob([data], { type: 'text/plain' });
}

// file to blob
export function fileToBlob(file: File) {
  return new Blob([file], { type: file.type });
}

// 判断blob类型 是否为docx的格式
export function isDocx(blob: Blob) {
  return (
    blob.type ===
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  );
}

export function isTxt(blob: Blob) {
  return blob.type === 'text/plain';
}

export function getBlobTypeMap() {
  return {
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      'docx',
    'text/plain': 'txt',
    'application/pdf': 'pdf',
  };
}

export function suffixToBlobType(suffix: string) {
  const map = getBlobTypeMap();
  return Object.entries(map).find(
    ([key, value]) => value === suffix,
  )?.[0];
}

export function getBlobTypeToSuffix(
  blob?: Blob,
): string | undefined {
  if (!blob) {
    return;
  }
  const map = getBlobTypeMap();
  return map[blob.type as keyof typeof map];
}

/**
 * @description: 返回contenttype默认值为'application/x-www-form-urlencoded'的header
 * @param {string} contentType
 */
export const generatContentType = (
  contentType: string = 'application/x-www-form-urlencoded',
) => ({
  'Content-Type': contentType,
});

// 返回英文的前n个单词,汉字的前n个字
export function getWords(str: string, n = 1) {
  const isEnglish = str.split(' ').length > 1;
  return str
    .split('\n')[0]
    .split(' ')
    .slice(0, n)
    .join(' ');
}

/**
 * @description: 对象转formdata
 */
export function objToFormData(o: Record<string, any>) {
  const keys = Object.keys(o);
  const formData = new FormData();
  keys.forEach((key) => {
    if (o[key] !== undefined && o[key] !== null) {
      formData.append(key, o[key]);
    }
  });
  return formData;
}

export function changeBlobType(blob: Blob, type: string) {
  return new Blob([blob], { type });
}

// blob下载
export function downloadBlob(
  blob: Blob,
  filename = '未命名',
) {
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  window.URL.revokeObjectURL(url);
}

export const getUuid = () => {
  if (typeof crypto === 'object') {
    if (typeof crypto.randomUUID === 'function') {
      return crypto.randomUUID();
    }
    if (
      typeof crypto.getRandomValues === 'function' &&
      typeof Uint8Array === 'function'
    ) {
      const callback = (c: any) => {
        const num = Number(c);
        return (
          num ^
          (crypto.getRandomValues(new Uint8Array(1))[0] &
            (15 >> (num / 4)))
        ).toString(16);
      };
      return (
        '' +
        [1e7] +
        -1e3 +
        -4e3 +
        -8e3 +
        -1e11
      ).replace(/[018]/g, callback);
    }
  }
  let timestamp = new Date().getTime();
  let perForNow =
    (typeof performance !== 'undefined' &&
      performance.now &&
      performance.now() * 1000) ||
    0;
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
    /[xy]/g,
    (c) => {
      let random = Math.random() * 16;
      if (timestamp > 0) {
        random = (timestamp + random) % 16 | 0;
        timestamp = Math.floor(timestamp / 16);
      } else {
        random = (perForNow + random) % 16 | 0;
        perForNow = Math.floor(perForNow / 16);
      }
      return (c === 'x'
        ? random
        : (random & 0x3) | 0x8
      ).toString(16);
    },
  );
};

export function openFileDialog(accept: string) {
  return new Promise<File[]>((resolve) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = accept;
    input.onchange = () => {
      resolve(Array.from(input.files ?? []));
    };
    input.click();
  });
}

// 判断文件格式
export function isFileFormat(file: File, format: string[]) {
  return format.includes(`.${urlSuffix(file.name)}`);
}
// 判断文件大小
export function isFileSize(file: File, size: number) {
  return file.size < size;
}
// uni8转字符串
export function uint8ToString(arr: Uint8Array) {
  let str = '';
  arr.forEach((v) => {
    str += String.fromCharCode(v);
  });
  return str;
}

export async function readTextEventStream(
  reader?: ReadableStreamDefaultReader<Uint8Array>,
) {
  let res = '';
  const textDecoder = new TextDecoder('utf-8');
  if (!reader) {
    throw new Error('无效的readableStream');
  }
  while (true) {
    const { done, value } = await reader?.read();
    if (done) {
      console.log('读取完成', res);
      return res;
    } else {
      res += textDecoder.decode(value);
      console.log('读取到块chunk');
    }
  }
}

// 复制兼容老浏览器
export async function copy(text: string) {
  try {
    await navigator.clipboard.writeText(text);
  } catch (error) {
    const input = document.createElement('textarea');
    input.value = text;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);
  }
  message.success('复制成功');
}

// 获取路径除开hash
export function getPathname() {
  return location.href.split('#')[0];
}

// webpack相对路径转绝对路径,使用webpack nodejs处理,如果是node_modules中则使用编译后路径
export function isNil(val: any) {
  return val === undefined || val === null;
}
