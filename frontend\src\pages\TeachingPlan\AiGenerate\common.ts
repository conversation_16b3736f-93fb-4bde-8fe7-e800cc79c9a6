/**
 * 获取一个递归函数,处理流式数据
 * @param reader  流数据
 * @param stream  是否是流式数据
 */
export const getWrite = (reader: any, stream: boolean, callback: (content: any, config: any, finish: boolean, newChatInfo?: any) => void) => {
    let tempResult = '';
    let responseText = '';
    let configTools: any = null;
    let firstChatInfo: any = null;
    /**
     *
     * @param done  是否结束
     * @param value 值
     */
    const write_stream = ({ done, value }: { done: boolean; value: any }) => {
        try {
            if (done) {
                return
            }
            const decoder = new TextDecoder('utf-8')
            let str = decoder.decode(value, { stream: true })
            // let str2 = str.replace(/retry:\d+/g, '').trim();
            // 这里解释一下 start 因为数据流返回流并不是按照后端chunk返回 我们希望得到的chunk是data:{xxx}\n\n 但是它获取到的可能是 data:{ -> xxx}\n\n 总而言之就是 fetch不能保证每个chunk都说以data:开始 \n\n结束
            tempResult += str
            // const split = tempResult.match(/data:.*}\n\n/g)
            const split = tempResult.match(/data:.*}\nretry:1000\n\n/g)
            if (split) {
                str = split.join('')
                tempResult = tempResult.replace(str, '')
            } else {
                return reader.read().then(write_stream)
            }
            // 这里解释一下 end
            if (str && str.startsWith('data:')) {
                if (split) {
                    for (const index in split) {
                        let chunk1 = split[index].replace(/retry:\d+/g, '').trim();
                        let chunk = JSON?.parse(chunk1.replace('data:', ''));
                        if (chunk?.choices) { // 为了适配upfrom大模型测试对话接口的结构数据
                            chunk = {
                                content:chunk?.choices?.[0]?.delta?.content,
                                event: chunk?.choices?.[0]?.finish_reason === 'stop' ? 'finish' : 'add',
                                role: chunk?.choices?.[0]?.delta?.role,
                                record_id: chunk?.id,
                                upform: true,
                            }
                        }
                        const content = chunk?.content || '';

                        // console.info('chunk', 'chunk', {chunk})
                        if (chunk && (chunk?.role === 'assistant' || !chunk?.role)) {
                            responseText += content;
                        }
                        
                        configTools = chunk;

                        if (chunk && chunk?.role === 'tools' && chunk.content?.content?.type === "chat_info") {
                            firstChatInfo = chunk.content.content.chat_info || {};
                        }
                        chunk && callback(content, configTools, false, firstChatInfo);
                        if (chunk.event === 'finish') {
                            // 流处理成功 返回成功回调
                            callback(responseText || '', configTools, true, firstChatInfo);
                            return Promise.resolve()
                        }
                    }
                }
            }
        } catch (e) {
            return Promise.reject(e)
        }
        return reader.read().then(write_stream)
    }
    /**
     * 处理 json 响应
     * @param param0
     */
    const write_json = ({ done, value }: { done: boolean; value: any }) => {
        if (done) {
            const result_block = JSON.parse(tempResult)
            if (result_block.code === 500) {
                return Promise.reject(result_block.message)
            } else {
                if (result_block.content) {
                    // console.info('content', 'content', result_block.content)
                }
            }
            return
        }
        if (value) {
            const decoder = new TextDecoder('utf-8')
            tempResult += decoder.decode(value)
        }
        return reader.read().then(write_json)
    }
    return stream ? write_stream : write_json
}