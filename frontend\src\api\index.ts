/*
 * @Author: <PERSON>武林
 * @Date: 2021-08-20 13:45:16
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-07-18 15:03:53
 * @FilePath: \frontend\src\api\index.ts
 * @Description:
 *
 * Copyright (c) 2022 by <PERSON>武林/索贝数码, All Rights Reserved.
 */
// import area from "./area";
// import server from "./server";
// import camera from "./camera";
// import task from "./task";
import menus from './menus';
import user from './user';
import role from './role';
import organization from './organization';
import setting from './setting';
import permission from './permission';
import gatewayApis from './gatewayApis';
import monitoring from './monitoring';
import synchronization from './synchronization';
import navigation from './navigation';
import online from './online';
import specialtopic from './specialtopic';
import auditconfiguration from './auditconfiguration';
let api = {
  user,
  menus,
  role,
  organization,
  setting,
  permission,
  gatewayApis,
  monitoring,
  synchronization,
  navigation,
  online,
  specialtopic,
  auditconfiguration,
};

export default api;

export interface IResponse<
  T = object | string | [] | null
> {
  error_code: string;
  error_msg: string;
  extend_message: T;
}
export interface UpFormResponse<
  T = object | string | [] | null
> {
  errorCode: string;
  errorMsg: string;
  extendMessage: T;
}
export interface UpFormListResponse<T extends any>
  extends UpFormResponse<{
    results: T;
    pageIndex: number;
    pageSize: number;
    pageTotal: number;
    recordTotal: number;
  }> {}

export interface RmanResponse<
  T = object | string | [] | null
> {
  error: any;
  data: T;
  success: boolean;
}
export interface SupervisionResponse<
  T = object | string | [] | null
> {
  message: string;
  stackMessage: string;
  statusCode: number;
  hostId: string;
  data: T;
}
export interface AIResponse<
  T = object | string | [] | null
> {
  message: string;
  stackMessage: string;
  statusCode: number;
  hostId: string;
  data: T;
}
