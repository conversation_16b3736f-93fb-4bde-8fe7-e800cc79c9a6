/*
 * @Author: z<PERSON><PERSON><PERSON> ran
 * @Date: 2022-12-28 16:07:08
 * @LastEditTime: 2022-12-28 16:07:08
 * @FilePath: \sc-24365-web\frontend\src\components\\index.tsx
 * @Description:
 */

import { Progress, Spin } from 'antd';
import * as docx from 'docx-preview';
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import './index.less';
import { blobToArrayBuffer } from '@/utils/utils';
import Access from '../Access';

const Word: React.FC<Props> = ({ blob, setErr }) => {
  const [loading, setLoading] = useState(true);
  const ref = useRef<HTMLDivElement>(null);
  const getData = useCallback(async () => {
    setLoading(true);
    try {
      if (!blob) return;
      const arrayBuffer = await blobToArrayBuffer(blob);
      await docx.renderAsync(
        arrayBuffer,
        ref.current!,
        ref.current!,
        {
          className: 'docx', //class name/prefix for default and document style classes
          inWrapper: true, //enables rendering of wrapper around document content
          ignoreWidth: false, //disables rendering width of page
          ignoreHeight: false, //disables rendering height of page
          ignoreFonts: false, //disables fonts rendering
          breakPages: true, //enables page breaking on page breaks
          ignoreLastRenderedPageBreak: true, //disables page breaking on lastRenderedPageBreak elements
          experimental: false, //enables experimental features (tab stops calculation)
          trimXmlDeclaration: true, //if true, xml declaration will be removed from xml documents before parsing
        },
      );
    } catch (error) {
      console.log(error);
      setErr(true);
    }
    setLoading(false);
  }, [blob]);
  useEffect(() => {
    getData();
  }, [getData]);

  return (
    <div
      style={{
        width: '100%',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
      className="word">
      <Access accessible={loading}>
        <Spin tip="渲染中..." />
      </Access>
      <div
        style={{
          width: '100%',
          display: loading ? 'none' : 'block',
        }}
        ref={ref}
        id="word-container"></div>
    </div>
  );
};
export default Word;

interface Props {
  blob?: Blob;
  setErr: (err: boolean) => void;
}
