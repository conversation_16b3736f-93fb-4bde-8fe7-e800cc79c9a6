import { CUSTOMER_NPU } from '@/components/header/header';
import React from 'react';
import { useSelector } from 'umi';

const App: React.FC<AppProps> = ({ children }) => {
  const { parameterConfig } = useSelector<
    { global: any },
    {
      buttonPermission: string[];
      parameterConfig: any;
      permission: any;
    }
  >((state) => state.global);
  const showOther =
    parameterConfig?.target_customer === CUSTOMER_NPU;
  return (
    <div id={showOther ? 'other-app-header' : 'normal-app-header'}>
      {children}
    </div>
  );
};

interface AppProps {
  children: React.ReactNode;
}
export default App;
App.displayName = 'App';
