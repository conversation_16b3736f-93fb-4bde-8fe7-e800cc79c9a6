import React, { FC, useState, useRef, useEffect } from 'react';
import Access from '@/components/Access';
import { Input, Image, Radio, Button, message } from 'antd';
import { CheckCircleOutlined } from '@ant-design/icons';
import './index.less';
import './reset.less';
import PlanContent from './components/planContent'
import Empty from './components/empty'
import LoadingPage from './components/loadingPage'
import RequireSet from './components/requireSet'
import { getWrite } from './AiGenerate/common';
import { getUuid } from '@/utils'
import { stopRequest } from '@/utils/gptMessage';
import { reqMessage, reqAllMessage, reqTemplateFieldApi, reqMapMessage, reqFileMessage, reqResourseMessage, reqChapterMessage } from '@/api/teachingPlan'
export interface TeachingPlanProps {
  height: number;
}
const TeachingPlan: FC<TeachingPlanProps> = () => {
  const [modeData] = useState<any>([
    { id: 1, type: 1, name: '按章节出教案', img: require('@/assets/img/teachingPlan/zhangjie01.png'), active_img: require('@/assets/img/teachingPlan/zhangjie02.png') },
    { id: 2, type: 2, name: '按图谱出教案', img: require('@/assets/img/teachingPlan/tupu01.png'), active_img: require('@/assets/img/teachingPlan/tupu02.png') },
    { id: 3, type: 3, name: '按文本出教案', img: require('@/assets/img/teachingPlan/wenben01.png'), active_img: require('@/assets/img/teachingPlan/wenben02.png') },
    { id: 4, type: 4, name: '按材料出教案', img: require('@/assets/img/teachingPlan/cailiao01.png'), active_img: require('@/assets/img/teachingPlan/cailiao02.png') }
  ])
  const [activeMode, setActiveMode] = useState<string | number>(1)
  const [list, setList] = useState<any[]>([]);
  const chatMsgRef = useRef<string>('');
  const [inputValue, setInputValue] = useState<string>('')
  const planContentRef = useRef<any>(null)
  const mapRef = useRef<any>(null)
  const [showEmpty, setShowEmpty] = useState<boolean>(true)
  const [showTpoic, setShowTpoic] = useState<boolean>(false)
  const [showLoading, setShowLoading] = React.useState<boolean>(false)
  const [allFinished, setAllFinished] = useState<boolean>(true)
  const [templateName, setTemplateName] = useState<string>('example2')
  const templateData: any = useRef<any>(null)
  const [isScrollBottom, setIsScrollBottom] = useState<boolean>(false)// 重新生成

  const findNameByKey = (arr: any, key: string, value: string) => {
    const item = arr.find((obj: any) => obj[key] === value);
    return item ? item.key : null;
  }
  const pushDataFun = (fieldName: string, content: string) => {
    setList((pre) => {
      // 判断fieldname是否在数组中存在
      const index = pre.findIndex(item => item.name === fieldName);
      if (index !== -1) {
        // 如果存在，则更新该元素的内容
        pre[index].content = content;
      } else {
        // 如果不存在，则添加新元素
        const fieldKey = findNameByKey(templateData.current, 'name', fieldName);
        pre.push({ name: fieldName, key: fieldKey, content: content, id: getUuid(), readOnly: true });
      }
      return [...pre];
    });
  }
  const extractTitleContent = (htmlStr: string) => {
    const pattern = /<title>([\s\S]*?)<\/title>/i;
    const match = htmlStr.match(pattern);
    if (match) {
      return match[1].trim();  // 返回标签内的内容，去除首尾空白
    }
    return null;  // 没有匹配到返回 null
  }
  const extractTitleContent2 = (htmlStr: string) => {
    const pattern = /<\/title>([\s\S]*?)<title>/i;
    const match = htmlStr.match(pattern);
    if (match) {
      return match[1].trim();
    }
    return null;
  }
  const trimStr = (str: string) => {
    // 去掉开头第一个 '>'（如果存在）
    if (str.startsWith('>')) {
      str = str.slice(1);
    }
    // 去掉结尾最后一个 '<title'（如果存在）
    if (str.endsWith('<title')) {
      str = str.slice(0, -7); // '<title>'.length === 7
    }
    return str;
  }
  const chatMessageFunction = (fun: any, fieldInfo?: any, single?: boolean) => {
    let allContent = ''
    let curValue = ''
    let curStr = ''
    return new Promise((resolve) => {
      fun.then((response: any) => {
        setShowLoading(false)
        setShowTpoic(true)
        const reader = response.body.getReader();
        if (response.headers.get('Content-Type') === 'application/json') throw ('服务器错误！');
        if (response.status !== 200) throw (response);
        // 处理流数据
        const write = getWrite(
          reader,
          response.headers.get('Content-Type') !== 'application/json',
          (content: string, config: any, finish: boolean, firstChatInfo) => {
            if (finish) {
              // console.info('打印完成....', content)
              setIsScrollBottom(false)
              setAllFinished(true)
              resolve(true)
            } else {
              // console.info('正在返回中...', content)
              // resolve(true);
              chatMsgRef.current += content;
              allContent += content;
              if (single) {
                setList((pre) => {
                  // 判断fieldname是否在数组中存在
                  const index = pre.findIndex(item => item.name === fieldInfo.fieldName && item.key === fieldInfo.fieldKey);
                  pre[index].content = allContent;
                  return [...pre];
                });
              } else {
                curStr += content
                let isHasTitle = extractTitleContent(curStr)
                if (isHasTitle) {
                  let newCurValue = trimStr(curValue)
                  pushDataFun(isHasTitle, newCurValue)
                  curValue += content
                }
                let isHasContent = extractTitleContent2(curStr)
                if (isHasContent) {
                  curStr = '<title>'
                  curValue = ''
                }
              }
            }
          }
        )
        reader.read().then(write)
      }).catch((e: any) => {
        // resolve(false);
        setIsScrollBottom(false)
        message.error(e?.statusText || '服务器异常！');
      }).finally(() => { });
    })
  }

  // 处理生成的方式
  const gererateMode = () => {
    let params: any = {
      templateName: 'example2',
      brief: inputValue
    }
    // 按文本
    if (activeMode == 3) {
      if (!inputValue) {
        message.error('请输入文本内容!')
        return false
      }
    } else if (activeMode == 2) {
      // 按图谱
      const mapInfo = mapRef.current?.mapInfo
      if (!mapInfo) {
        message.error('请选择一个图谱!')
        return false
      }
      params['mapId'] = mapInfo.mapId
    } else if (activeMode == 4) {
      // 按材料
      const file = mapRef.current?.fileList
      if (!file || file.length == 0) {
        message.error('请选择一个文件!')
        return false
      }
    } else if (activeMode == 1) {
      // 按章节
      const chapterInfo = mapRef.current?.chapterInfo
      if (!chapterInfo || chapterInfo.length == 0) {
        message.error('请添加章节!')
        return false
      }
      params['chapterParams'] = chapterInfo?.map((item: any) => {
        return {
          firstLevelName: item.name,
          children: item.children.map((it2: any) => it2.name)
        }
      })
    }
    return params
  }
  // 左侧开始生成
  const handleGenerate = async () => {
    const parmas: any = gererateMode()
    if (!parmas) return
    setList([])
    setShowTpoic(false)
    setShowLoading(true)
    setShowEmpty(false)
    setAllFinished(false)
    const data = await reqTemplateFieldApi(parmas.templateName);
    if (!data) {
      message.error('获取失败')
      setShowLoading(false)
      setShowEmpty(true)
      return
    }
    templateData.current = data
    setIsScrollBottom(true)
    if (activeMode == 3) {
      const requestApi = reqAllMessage({ ...parmas });
      await chatMessageFunction(requestApi);
    } else if (activeMode == 2) {
      const requestApi = reqMapMessage({ ...parmas });
      await chatMessageFunction(requestApi);
    } else if (activeMode == 4) {
      const file = mapRef.current?.fileList
      // 判断是自己上传的文件还是资源库先择的文件
      const fileMethod = mapRef.current?.fileMethod
      if (fileMethod == 1) {
        const requestApi = reqResourseMessage({ ...parmas, videoId: file[0].id });
        await chatMessageFunction(requestApi);
      } else {
        let formData = new FormData();
        formData.append('file', file[0].originFileObj);
        formData.append('templateName', parmas.templateName);
        formData.append('brief', parmas.inputValue);
        const requestApi = reqFileMessage(formData);
        await chatMessageFunction(requestApi);
      }
    } else if (activeMode == 1) {
      const requestApi = reqChapterMessage({ ...parmas });
      await chatMessageFunction(requestApi);
    }
  };

  // 重新生成
  const reGenerate = async (item: any) => {
    const parmas: any = gererateMode()
    if (!parmas) return
    message.info('正在处理中,请稍后...')
    if (activeMode == 3) {
      const requestApi = reqMessage({ ...parmas, fieldName: item.name });
      await chatMessageFunction(requestApi, { fieldName: item.name, fieldKey: item.key }, true);
    } else if (activeMode == 2) {
      const requestApi = reqMapMessage({ ...parmas, fieldName: item.name });
      await chatMessageFunction(requestApi, { fieldName: item.name, fieldKey: item.key }, true);
    } else if (activeMode == 4) {
      const file = mapRef.current?.fileList
      // 判断是自己上传的文件还是资源库先择的文件
      const fileMethod = mapRef.current?.fileMethod
      if (fileMethod == 1) {
        const requestApi = reqResourseMessage({ ...parmas, fieldName: item.name, videoId: file[0].id });
        await chatMessageFunction(requestApi, { fieldName: item.name, fieldKey: item.key }, true);
      } else {
        let formData = new FormData();
        formData.append('file', file[0].originFileObj);
        formData.append('templateName', parmas.templateName);
        formData.append('fieldName', item.name);
        formData.append('brief', parmas.inputValue);
        const requestApi = reqFileMessage(formData);
        await chatMessageFunction(requestApi, { fieldName: item.name, fieldKey: item.key }, true);
      }
    } else if (activeMode == 1) {
      const requestApi = reqChapterMessage({ ...parmas, fieldName: item.name });
      await chatMessageFunction(requestApi, { fieldName: item.name, fieldKey: item.key }, true);
    }
  }
  return (
    <div className='teaching-plan'>
      <div className="content">
        <div className='left'>
          <div className='left-title'>
            <img src={require("@/assets/img/teachingPlan/ai_logo1.png")} alt="title" />
            <img src={require("@/assets/img/teachingPlan/ai_logo_tit1.png")} alt="title" />
          </div>
          <div className='teaching-plan-mode'>
            {modeData.map((item: any) => {
              return <div key={item.id} className={`mode-item ${activeMode == item.id ? 'active-mode-item' : ''}`} onClick={() => {
                setActiveMode(item.id)
              }}>
                <img src={activeMode == item.id ? item.active_img : item.img} alt="" />
                <span>{item.name}</span>
              </div>
            })
            }
          </div>
          <div className='set-teaching-plan-content'>
            <RequireSet
              emitInputValue={(val) => {
                setInputValue(val)
              }}
              mapRef={mapRef}
              selectType={activeMode}
            />
            <div className='template-content'>
              <div className='template-title'>
                <img src={require('@/assets/img/teachingPlan/moban_icon.png')} alt="" />
                <span>教案模板</span>
              </div>
              <div className='template-list'>
                <div className='template-item'>
                  <Image
                    src={require('@/assets/img/teachingPlan/example2.png')}
                  />
                  <Radio checked={true}>模板01</Radio>
                </div>
              </div>
            </div>
          </div>
          <Button onClick={() => {
            if (allFinished) {
              handleGenerate()
            }
          }}>{!allFinished ? '开始生成中...' : '开始生成'}</Button>
          {/* <Button onClick={() => {
            stopRequest()
          }}>中断请求</Button> */}
        </div>
        <main className='right'>
          {showTpoic && <PlanContent
            isScrollBottom={isScrollBottom}
            planContentRef={planContentRef}
            list={list}
            emitReGenerate={reGenerate}
            allFinished={allFinished}
            templateName={templateName}
            emitDeleteTopic={(val)=>{
              setList([...val])
            }}
          />}
          {showEmpty && <Empty />}
          <LoadingPage showLoading={showLoading} />
        </main>
      </div>
    </div>
  );
};

export default TeachingPlan;