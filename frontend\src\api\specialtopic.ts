/*
 * @Author: lijin
 * @Description: 配置权限相关接口
 * @Date: 2021-08-13 10:39:03
 * @LastEditTime: 2022-07-13 16:11:33
 * @FilePath: \frontend\src\api\setting.ts
 */
import request from './request';

// 获取专题列表
async function getAllSpecialtopic(params: any) {
    return request('/learn/v1/thematic/configure/data/get/config', {
      method: 'get',
      params,
    });
  }

  // 获取专题列表带分页
  async function getAllSpecialtopicpage(params: any) {
    return request('/learn/v1/thematic/configure/data/get/config/page', {
      method: 'post',
      params,
    });
  }

  // 添加专题
  async function addSpecialtopic(data: any) {
    return request('/learn/v1/thematic/configure/data/insert/config', {
      method: 'post',
      data: JSON.stringify(data),
    });
  }

  // 编辑专题
  async function editSpecialtopic(data: any) {
    return request('/learn/v1/thematic/configure/data/update/config', {
      method: 'post',
      data: JSON.stringify(data),
    });
  }

  // 删除专题
  async function deleteSpecialtopic(id: any) {
    return request(`/learn/v1/thematic/configure/data/delete/config/${id}`, {
      method: 'get',
    });
  }

  // 批量修改
  async function updateSpecialtopic(data: any) {
    return request('/learn/v1/thematic/configure/data/batch/update/config', {
      method: 'post',
      data: JSON.stringify(data),
    });
  }

  // 通过code查询关联的课程
  async function getCourseByCode(params: any) {
    return request('/learn/v1/teaching/course/get/course/config', {
      method: 'get',
      params,
    });
  }


  // 大川板块获取列表
  async function getDcList() {
    return request('/learn/v1/dachuanplate/config/list', {
      method: 'post',
    });
  }

  // 根据板块id获取专题
  async function getSpecialtopicByDcId(id: string) {
    return request(`/learn/v1/dachuanplate/config/get/${id}`, {
      method: 'get',
    });
  }

  // 绑定专题
  async function bindSpecialtopic(id:string,data: any) {
    return request(`/learn/v1/dachuanplate/config/binding/${id}`, {
      method: 'post',
      data: JSON.stringify(data),
    });
  }

  // 解绑专题
  async function unbindSpecialtopic(id:string,data: any) {
    return request(`/learn/v1/dachuanplate/config/relieve/${id}`, {
      method: 'post',
      data: JSON.stringify(data),
    });
  }


  // 排序
  async function sortSpecialtopic(id:string, data:any) {
    return request(`/learn/v1/dachuanplate/config/move/thematic/${id}`, {
      method: 'post',
      data: JSON.stringify(data),
    });
  }

  // 修改专题的行数
  async function updateSpecialtopicRow(data:any) {
    return request(`/learn/v1/dachuanplate/config/edit`, {
      method: 'post',
      data: JSON.stringify(data),
    });
  }





let specialtopic = {
  getAllSpecialtopic,
  getAllSpecialtopicpage,
  addSpecialtopic,
  editSpecialtopic,
  deleteSpecialtopic,
  updateSpecialtopic,
  getCourseByCode,
  getDcList,
  getSpecialtopicByDcId,
  bindSpecialtopic,
  unbindSpecialtopic,
  sortSpecialtopic,
  updateSpecialtopicRow
};

export default specialtopic;
