import React, { FC, useEffect, useRef, useState } from 'react';
import { aiAgentItem } from '../models';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import AssistantAvatar from '@/assets/img/qa-assistant.png';
import { Avatar } from 'antd';
import { isDevelopment, openNewPage } from '@/utils/utils';
import { history } from 'umi';
import styles from './index.less';

interface Props {
    title: string;
    list: aiAgentItem[];
    open?: boolean;
    newStyle?: boolean;
}

const Content: FC<Props> = ({
    title,
    list,
    open,
    newStyle
}) => {

    const baseWidth = 95;

    const listRef: any = useRef<HTMLElement>(null);
    const [foldState, setFoldState] = useState(false); // 折叠状态，元素是否单排溢出
    const [clickMore, setClickMore] = useState(false); // 是否点击更多


    // 自定义防抖
    const debounce = (fun: () => void, wait: number = 500) => {
        let timer: any = null;
        const db = () => {
            if (timer) {
                clearTimeout(timer);
                timer = null;
            }

            timer = setTimeout(() => {
                fun();
                timer = null;
            }, wait);
        }

        return db;

    }

    // 点击更多
    const showAllContent = () => {
        const dom: HTMLElement = listRef.current;
        dom && (dom.style.overflow = 'visible');
        dom && (dom.style.height = '100%');
        setClickMore(true);
    }

    // 点击收起
    const shrinkContent = () => {
        const dom: HTMLElement = listRef.current;
        dom && (dom.style.overflow = 'hidden');
        dom && (dom.style.height = '75px');
        setClickMore(false);
    }

    // 跳转
    const toLink = (router: string) => {
        // AI Agent管理 / ai工具 / 剪辑工具 打开新窗口
        if (open) {
            if (router.startsWith('http')) {
                window.open(router);
                return;
            }
            if (router.startsWith('/')) {
                const links = router.split('#');
                openNewPage(
                    links[0].slice(0, links[0].length - 1),
                    links[1] ?? '',
                );
            } else {
                openNewPage(
                    isDevelopment ? '' : '/aitools',
                    `/${router}`,
                );
            }
        } else {
            window.location.href = router;
        }
    }

    // 窗口变化
    const changeWindowSize = () => {
        const contentDom: any = document.getElementById("treasure-content");
        const width =contentDom?.getBoundingClientRect()?.width;

        const maxContainLength = width / baseWidth; // 单排能容纳最大的元素个数
        const toolsLength = list.length; // 工具的个数，资源
        const listState = maxContainLength > toolsLength; // true表示能容纳下，false不能容纳，需折叠（更多）

        if (!listState) {
            setFoldState(true);
        } else {
            setFoldState(false);
        }

        // console.info('监听窗口发生变化~', maxContainLength, toolsLength, title)
    }
    const dbChangeWindowSize = debounce(changeWindowSize, 200);

    useEffect(() => {
        if (list.length > 0) {
            changeWindowSize();
            window.addEventListener('resize', dbChangeWindowSize)
        }
    }, [list])

    useEffect(() => {
        return () => {
            window.removeEventListener('resize', dbChangeWindowSize)
        }
    }, [])

    return (
        <div className={styles.contentStyle}>
            <header className={styles.header}>
                <span>{title}</span>
                {clickMore && <span className={styles.shrink} onClick={shrinkContent}>
                    收起
                    <UpOutlined style={{ fontSize: 12, color: 'rgba(0, 0, 0, 0.6)', marginLeft: 3 }} />
                </span>}
            </header>

            <div className={styles.list} ref={listRef}
                // style={{ width: foldState && !clickMore ? 'calc(100% - 70px)' : '100%' }}
            >
                {list.length > 0 ?
                    list.map((item) => (
                        <div key={item.key} className={styles.contentItem} style={{ width: baseWidth }}
                            onClick={() => { toLink(item.router) }}
                        >
                            {newStyle ?
                                <div style={{
                                    width: 40, height: 40, borderRadius: 40,
                                    border: '1px solid #EAECF4',
                                    background: 'linear-gradient( 180deg, #FCFDFF 0%, #E6E8ED 100%)',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center'
                                }}>
                                    <Avatar
                                        size={25}
                                        src={item.avatar || AssistantAvatar}
                                        icon={<img src={AssistantAvatar} />}
                                        draggable={false}
                                    />
                                </div> :
                                <Avatar
                                    size={40}
                                    src={item.avatar || AssistantAvatar}
                                    icon={<img src={AssistantAvatar} />}
                                    draggable={false}
                                />}
                            <div className={styles.text} title={item.title}>{item.title}</div>
                        </div>
                    )) :
                    <span style={{ margin: '20px 0 0 20px' }}>无</span>}
                {foldState && !clickMore && <span className={styles.more} title='点击查看更多' onClick={showAllContent}>
                    更多...
                </span>}
            </div>

        </div>
    )
}
export default Content;