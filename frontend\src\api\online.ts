import request from './request';
function XSRF() {
  return request(`/rman/v1/api/Security/xsrf-token`, {
    method: 'get',
  });
}

const getCookie = (key: any) => {
  const tips = document.cookie.split(';');
  let a: any = [];
  let x = '';
  tips.map((item: any) => {
    a = item.split('=');
    if (a[0].trim() == key) {
      x = a[1];
    }
  });
  return x;
};
function onlinelist(params: any) {
  return request(`/rman/v1/OnLineUser/getuserlist`, {
    method: 'post',
    data: JSON.stringify(params),
    headers: { 'X-XSRF-TOKEN': getCookie('XSRF-TOKEN') },
  });
}

async function onlineupdata(params: any) {
  return request(`/rman/v1/OnLineUser/offline`, {
    method: 'post',
    data: JSON.stringify(params),
  });
}

async function onlineretrieve(params: any) {
  return request(
    `/rman/v1/OnLineUser/searchbyname?name=${params.keyword}&nameType=${params.type}&pageIndex=${params.pageIndex}&pageSize=${params.pageSize}`,
    {
      method: 'get',
      data: JSON.stringify(params),
      headers: { 'X-XSRF-TOKEN': getCookie('XSRF-TOKEN') },
    },
  );
}
let online = {
  onlinelist,
  onlineupdata,
  XSRF,
  onlineretrieve,
};

export default online;
