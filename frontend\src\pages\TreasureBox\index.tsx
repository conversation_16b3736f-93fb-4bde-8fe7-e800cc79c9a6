import React, { FC, useEffect, useState, useMemo } from 'react';
import { AppstoreOutlined, MessageOutlined } from '@ant-design/icons';
import { aiAgentItem } from './models';
import { Spin } from "antd";
import ToolsImg from '@/images/chatTools/tools.png';
import AIiconImg from '@/images/chatTools/aiIcon.png';
import HiImg from '@/images/chatTools/hi.png';
import AI0 from '@/images/chatTools/ai0.png';
import AI1 from '@/images/chatTools/ai1.png';
import AI2 from '@/images/chatTools/ai2.png';
import AI3 from '@/images/chatTools/ai3.png';
import KnowladgeImg from '@/images/chatTools/knowladge.png';
import AgentImg from '@/images/chatTools/agent.png';
import { reqAitoolsAgents, fetchHeaderList } from '@/api/assistant';
import { AICards, ClipCards } from '@/pages/Overview/index';
import defaultAvatar from '@/assets/img/defualt-avatar.png';
import Content from './components/content';
import { IGlobal } from '@/models/global';
import { history, useSelector, useLocation } from 'umi';
import styles from './index.less';

const TreasureBoxPage: FC = () => {

    const { search } = useLocation();
    const searchParams = new URLSearchParams(search);
    const id = searchParams.get('id');

    const [loading, setLoading] = useState(false);
    const [aiAgentList, setAiAgentList] = useState<Array<aiAgentItem>>([]); // AI Agent应用
    const [aiAgentMangList, setAiAgentMangList] = useState<Array<aiAgentItem>>([
        { key: 1, title: '创建AI Agent', avatar: defaultAvatar, router: '' },
        { key: 2, title: '知识库', avatar: defaultAvatar, router: '' },
    ]); //AI Agent管理

    // 获取Agent应用
    const getAgentList = () => {
        setLoading(true);
        reqAitoolsAgents({ page: 1, page_size: 99999 }).then((res: any) => {
            setLoading(false);
            let data = res?.extend_message?.records || [];
            data = data.map((item: any) => ({
                ...item,
                key: item.id,
                title: item.name,
                avatar: item.icon,
                router: `/terminator/aitools/chat?id=${item.id}`
            }))
            setAiAgentList(data);
        });
    }

    // // 获取AI Agent管理----获取AI Agent管理路由
    const getAIAgentRouter = () => {
        let router = `/treasureBox?id=${id}`;
        fetchHeaderList().then((res: any) => {
            const data = res?.extendMessage || [];
            router = data.find((item: any) => item.name === 'AI Agent')?.link;
            setAiAgentMangList([
                { key: 1, title: '创建AI Agent', avatar: AgentImg, router: `${router}?type=agent` },
                { key: 2, title: '知识库', avatar: KnowladgeImg, router: `${router}?type=knowledge` },
            ])
        })

        return router;
    }

    const modules = useSelector<any>(
        (state: any) => state.global.modules,
    ) as string[];

    const { userInfo, fullScreenState } = useSelector<any, IGlobal>(
        (state: any) => state.global,
    );

    const userOnlyStudent = userInfo?.roles?.length === 1 && userInfo.roles[0].roleCode === 'r_student';

    // 获取AI工具
    const getAIToolsList = useMemo(() => {
        let data = [];
        if (modules.length === 0) {
            data = [];
        }
        data = AICards.filter((card) => {
            return modules.includes(card.code);
        });
        data = data.map((item) => ({
            key: item.key,
            title: item.title,
            avatar: item.icon,
            router: item.link
        }))
        return data;
    }, [modules]);

    // 获取剪辑工具
    const getClipList = useMemo(() => {
        let data = [];
        if (modules.length === 0) {
            data = [];
        }
        data = ClipCards.filter((card) => {
            return modules.includes(card.code);
        });
        data = data.map((item) => ({
            key: item.key,
            title: item.title,
            avatar: item.icon,
            router: item.link
        }))
        return data;
    }, [modules]);

    useEffect(() => {
        getAgentList();
        getAIAgentRouter();
    }, [])

    const linkPage = (router: string) => {
        history.push(router)
    }

    const toChat = () => {
        window.location.href = `/terminator/aitools/chat?id=${id}`
    }

    return (
        <div className={styles.treasureBoxPage}>
            <div className={styles.header}>
                <div className={styles.headerLeft}>
                    <img src={ToolsImg}
                        className={styles.headerImg}
                    />
                    百宝箱
                </div>
                <div className={styles.headerRight} onClick={toChat}>
                    <img src={AIiconImg}
                        className={styles.headerAiImg}
                    />
                    <span style={{ position: 'relative', right: 20, cursor: 'pointer' }}>
                        <img src={AI0}
                            className={styles.aiImg}
                            style={{ right: 12 }}
                        />
                        <img src={AI1}
                            className={styles.aiImg}
                            style={{ right: 4 }}
                        />
                        <img src={AI2}
                            className={styles.aiImg}
                            style={{ right: -4 }}
                        />
                        <img src={AI3}
                            className={styles.aiImg}
                        />
                    </span>
                </div>
            </div>
            {/* <div className={styles.line} /> */}
            <div className={styles.content}>
                <Spin spinning={loading}>
                    <div className={styles.user} id="treasure-content">
                        <img src={HiImg} style={{ width: 42, height: 42, display: 'block', marginBottom: 10 }} />
                        {`您好，请问有什么可以帮到您！`}
                    </div>
                    <Content title='AI Agent应用' list={aiAgentList} />
                    {!userOnlyStudent && <Content title='AI Agent管理' list={aiAgentMangList} open />}
                    <Content title='AI工具' list={getAIToolsList} open newStyle />
                    {!userOnlyStudent && <Content title='剪辑工具' list={getClipList} open newStyle />}
                </Spin>
            </div>
        </div>
    )
}

export default TreasureBoxPage;