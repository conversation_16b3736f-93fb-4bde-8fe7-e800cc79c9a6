span[data-mytooltip] {
  position: relative;
  display: inline;
  cursor: pointer;
}

span[data-mytooltip]:after,
span[data-mytooltip]:before {
  top: 100%;
  left: 50%;
}

span[data-mytooltip]:after,
span[data-mytooltip]:before {
  visibility: hidden;
  position: absolute;
  /* top: 100%;
  left: 50%; */
  transition: all 0.05s;
}

span[data-mytooltip]:after {
  /* word-wrap: break-word; */
  word-break: break-all;
  content: attr(data-mytooltip);
  transform: translate(-50%, 4px);
  padding: 12px 24px;
  padding: 10px;
  font-size: 15px;
  font-family: FZLTHJW--GB1-0, FZLTHJW--GB1;
  font-weight: normal;
  color: #333333;
  background-color: #fff;
  background-clip: padding-box;
  border-radius: 2px;
  box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%),
    0 9px 28px 8px rgb(0 0 0 / 5%);
  z-index: 4;
  width: 200%;
  min-width: 220px;
  max-width: 240px;
  line-height: 24px;
  text-indent: 0;
}

span[data-mytooltip]:before {
  content: '';
  background-color: #fff;
  transform: translate(-50%, 0px) rotate(45deg);
  border-color: #fff transparent transparent #fff;
  box-shadow: -2px -2px 5px rgb(0 0 0 / 6%);
  display: block;
  width: 4px;
  height: 4px;
  background: 0 0;
  border-style: solid;
  border-width: 5px;
  z-index: 10;
}

span[data-mytooltip]:hover:after,
span[data-mytooltip]:hover:before {
  transition: all 0.05s;
  visibility: visible;
}

span[data-mytooltip]:hover:after {
  transform: translate(-50%, 8px);
}

span[data-mytooltip]:hover:before {
  transform: translate(-50%, 4px) rotate(45deg);
}

.sobey_h1 > span,
.sobey_h2 > span,
.sobey_h3 > span,
.sobey_h4 > span,
.sobey_h5 > span,
.sobey_h6 > span {
  font-weight: 700;
  font-family: 'Microsoft YaHei', 'Helvetica Neue', 'PingFang SC', sans-serif !important;
}

.sobey_h1,
.sobey_h2,
.sobey_h3,
.sobey_h4,
.sobey_h5,
.sobey_h6 {
  font-weight: bold;
}

.sobey_h1 > span,
.sobey_h1 {
  font-size: 30px !important;
}

.sobey_h2 > span,
.sobey_h2 {
  font-size: 26px !important;
}

.sobey_h3 > span,
.sobey_h3 {
  font-size: 24px !important;
}

.sobey_h4 > span,
.sobey_h4 {
  font-size: 22px !important;
}

.sobey_h5 > span .sobey_h5 {
  font-size: 18px !important;
}

.sobey_h6 > span,
.sobey_h6 {
  font-size: 16px !important;
}

#sobey_bubble {
  /* word-break: break-all; */
}

#sobey_mp3_small,
#sobey_mp3_pause {
  background: url(https://shared-https.ydstatic.com/dict/v2016/result/new-sprite.png)
    no-repeat;
  overflow: hidden;
  display: inline-block;
  outline: 0;
  width: 16px !important;
  height: 25px !important;
  vertical-align: bottom;
  background-position: -121px 3px;
}

#sobey_mp3_small:hover,
#sobey_mp3_pause:hover {
  background-position: -92px 3px;
}
