.translate_dom{
    width: calc(100% - 40px);
    height: 400px;
    background: #FFFFFF;
    border-radius: 6px;
    padding: 20px;
    margin-left: 20px;
    display: flex;


    .translate_input{
        width: 50%;
        height: 100%;
        border: 1px solid #EDEDED;
        border-radius: 6px;
        border-right: none;
        padding: 10px 20px;

        textarea{
            width: 100%;
            height: calc(100% - 110px);
            margin-top: 14px;
            outline: none;
            border: none;
            resize: none;
        }

        .option_box{
            width: 100%;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            img{
                width: 22px;
                cursor: pointer;
            }
        }
    }

    .translate_output{ 
        width: 50%;
        height: 100%;
        padding: 10px 20px;
        background: #F7F8F8;

        .output_box{
            width: 100%;
            height: calc(100% - 110px);
            margin-top: 14px;
            outline: none;
            border: none;
            resize: none;
        }

        .option_box{
            width: 100%;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .copy{
                width: 20px;
                cursor: pointer;
            }

            .bofang{
                width: 28px;
                height: 28px;
                border-radius: 50%;
                background-color: #E2E6F7;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;

                img{
                    width: 16px;
                }
            }

            .bofang.disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
        }
    }

    
}