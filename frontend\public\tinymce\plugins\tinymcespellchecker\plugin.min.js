/* Ephox Spell Checker Pro
 *
 * Copyright 2010-2016 Ephox Corporation.  All rights reserved.
 *
 * Version: 2.2.0-191
 */

!function(){"use strict";function e(n){return parseInt(n,10)}function r(n,e){return 0==(e=n-e)?0:0<e?1:-1}function t(n,e,t){return{major:n,minor:e,patch:t}}function d(n){return(n=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(n))?t(e(n[1]),e(n[2]),e(n[3])):t(0,0,0)}function g(n,e){var t=r(n.major,e.major);return 0!==t||0!==(t=r(n.minor,e.minor))?t:0!==(e=r(n.patch,e.patch))?e:0}function m(n){return d([(n=n).majorVersion,n.minorVersion].join(".").split(".").slice(0,3).join("."))}function v(n,e){return!!n&&-1===g(m(n),d(e))}function u(t,r){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))}}function a(n){return n}var w=function(){},l=function(n){return function(){return n}};function c(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}function b(n){n()}var f=l(!1),i=l(!0),n=function(){return s},s={fold:function(n,e){return n()},is:f,isSome:f,isNone:i,getOr:h,getOrThunk:p,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:l(null),getOrUndefined:l(void 0),or:h,orThunk:p,map:n,each:w,bind:n,exists:f,forall:i,filter:n,equals:o,equals_:o,toArray:function(){return[]},toString:l("none()")};function o(n){return n.isNone()}function p(n){return n()}function h(n){return n}function y(n,e){for(var t=W(n),r=0,o=t.length;r<o;r++){var u=t[r];e(n[u],u)}}function x(n,t){return V(n,function(n,e){return{k:e,v:t(n,e)}})}function k(n,t){var r=[];return y(n,function(n,e){r.push(t(n,e))}),r}function T(n){return k(n,function(n){return n})}function S(n,e){return U(n,e)?z.from(n[e]):z.none()}function O(n){return!(null==n)}function _(n,e){return nn.call(n,e)}function E(n,e){return-1<_(n,e)}function C(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1}function I(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t}function A(n,e,t){return rn(n,function(n){t=e(t,n)}),t}function D(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return z.some(u);if(t(u,r))break}return z.none()}(n,e,f)}function R(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return z.some(t)}return z.none()}function L(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!Y(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);en.apply(e,n[t])}return e}function M(n,e){return L(tn(n,e))}function j(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0}function N(n){return(n=Z.call(n,0)).reverse(),n}function P(n){return e=n,(n=0)<=n&&n<e.length?z.some(e[n]):z.none();var e}var F,B=function(t){function n(){return o}function e(n){return n(t)}var r=l(t),o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:i,isNone:f,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:n,orThunk:n,map:function(n){return B(n(t))},each:function(n){n(t)},bind:e,exists:e,forall:e,filter:function(n){return n(t)?o:s},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(f,function(n){return e(t,n)})}};return o},z={some:B,none:n,from:function(n){return null==n?s:B(n)}},W=Object.keys,q=Object.hasOwnProperty,V=function(n,t){var r={};return y(n,function(n,e){e=t(n,e);r[e.k]=e.v}),r},U=function(n,e){return q.call(n,e)},H=function(t){return function(n){return n=typeof(e=n),(null===e?"null":"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n)===t;var e}},J=function(e){return function(n){return typeof n===e}},K=H("string"),X=H("object"),Y=H("array"),$=J("boolean"),G=J("function"),Q=J("number"),Z=Array.prototype.slice,nn=Array.prototype.indexOf,en=Array.prototype.push,tn=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},rn=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},on=function(t){return{is:function(n){return t===n},isValue:i,isError:f,getOr:l(t),getOrThunk:l(t),getOrDie:l(t),or:function(n){return on(t)},orThunk:function(n){return on(t)},fold:function(n,e){return e(t)},map:function(n){return on(n(t))},mapError:function(n){return on(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return z.some(t)}}},un=function(t){return{is:f,isValue:f,isError:i,getOr:a,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return un(t)},mapError:function(n){return un(n(t))},each:w,bind:function(n){return un(t)},exists:f,forall:i,toOptional:z.none}},cn={value:on,error:un,fromOption:function(n,e){return n.fold(function(){return un(e)},on)}};(n=F=F||{})[n.Error=0]="Error",n[n.Value=1]="Value";function fn(n,e,t){return n.stype===F.Error?e(n.serror):t(n.svalue)}function an(n){return{stype:F.Value,svalue:n}}function sn(n){return{stype:F.Error,serror:n}}function ln(n){return X(n)&&100<W(n).length?" removed due to size":JSON.stringify(n,null,2)}function dn(n,e){return An([{path:n,getErrorInfo:e}])}function gn(n){var e=[],t=[];return rn(n,function(n){n.fold(function(n){e.push(n)},function(n){t.push(n)})}),{errors:e,values:t}}function mn(n,e){var t={};return t[n]=e,t}function pn(n){return u(An,L)(n)}function hn(t,r,o){return S(r,o).fold(function(){return n=o,e=r,dn(t,function(){return'Could not find valid *strict* value for "'+n+'" in '+ln(e)});var n,e},Cn)}function vn(n,e,t){return e=S(n,e).fold(function(){return t(n)},a),Cn(e)}function yn(i,c,n,f){return n.fold(function(t,e,n,r){function o(n){return n=r.extract(i.concat([t]),f,n),Ln(n,function(n){return mn(e,f(n))})}function u(n){return n.fold(function(){var n=mn(e,f(z.none()));return Cn(n)},function(n){n=r.extract(i.concat([t]),f,n);return Ln(n,function(n){return mn(e,f(z.some(n)))})})}return n.fold(function(){return Dn(hn(i,c,t),o)},function(n){return Dn(vn(c,t,n),o)},function(){return Dn(Cn(S(c,t)),u)},function(n){return Dn(function(e,n,t){n=S(e,n).map(function(n){return!0===n?t(e):n});return Cn(n)}(c,t,n),u)},function(n){var e=n(c),n=Ln(vn(c,t,l({})),function(n){return Nn(e,n)});return Dn(n,o)})},function(n,e){e=e(c);return Cn(mn(n,f(e)))})}function wn(r){return{extract:function(t,n,e){return Rn(r(e,n),function(n){return e=n,dn(t,function(){return e});var e})},toString:function(){return"val"}}}function bn(r){return{extract:function(n,e,t){return function(e,t,n,r){n=tn(n,function(n){return yn(e,t,n,r)});return Wn(n,{})}(n,t,r,e)},toString:function(){return"obj{\n"+tn(r,function(n){return n.fold(function(n,e,t,r){return n+" -> "+r.toString()},function(n,e){return"state("+n+")"})}).join("\n")+"}"}}}function xn(n,e,t){return En(function(n,e,t,r){t=e.extract([n],t,r);return Mn(t,function(n){return{input:r,errors:n}})}(n,e,a,t))}function kn(n,e,t){return xn(n,e,t).fold(function(n){throw new Error(Jn(n))},a)}var Tn,Sn,On,_n,En=function(n){return fn(n,cn.error,cn.value)},Cn=an,In=function(n){var e=[],t=[];return rn(n,function(n){fn(n,function(n){return t.push(n)},function(n){return e.push(n)})}),{values:e,errors:t}},An=sn,Dn=function(n,e){return n.stype===F.Value?e(n.svalue):n},Rn=function(n,e){return n.stype===F.Error?e(n.serror):n},Ln=function(n,e){return n.stype===F.Value?{stype:F.Value,svalue:e(n.svalue)}:n},Mn=function(n,e){return n.stype===F.Error?{stype:F.Error,serror:e(n.serror)}:n},H=function(i){if(!Y(i))throw new Error("cases must be an array");if(0===i.length)throw new Error("there must be at least one case");var c=[],t={};return rn(i,function(n,r){var e=W(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],u=n[o];if(void 0!==t[o])throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!Y(u))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var n=arguments.length;if(n!==u.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+u.length+" ("+u+"), got "+n);for(var t=new Array(n),e=0;e<t.length;e++)t[e]=arguments[e];return{fold:function(){if(arguments.length!==i.length)throw new Error("Wrong number of arguments to fold. Expected "+i.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(n){var e=W(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!j(c,function(n){return E(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},jn=Object.prototype.hasOwnProperty,J=function(i){return function(){for(var n=new Array(arguments.length),e=0;e<n.length;e++)n[e]=arguments[e];if(0===n.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<n.length;r++){var o,u=n[r];for(o in u)jn.call(u,o)&&(t[o]=i(t[o],u[o]))}return t}},Nn=J(function(n,e){return X(n)&&X(e)?Nn(n,e):e}),Pn=J(function(n,e){return e}),Fn=H([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Bn=Fn.strict,zn=(Fn.asOption,Fn.defaultedThunk,Fn.asDefaultedOptionThunk,Fn.mergeWithThunk,H([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),mn),Wn=function(n,e){n=In(n);return 0<n.errors.length?pn(n.errors):(n=n.values,e=e,0<n.length?Cn(Nn(e,Pn.apply(void 0,n))):Cn(e))},qn=function(n){n=In(n);return 0<n.errors.length?pn(n.errors):Cn(n.values)},n=H([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),Vn=l(wn(Cn)),Un=u(function(o){return{extract:function(t,r,n){n=tn(n,function(n,e){return o.extract(t.concat(["["+e+"]"]),r,n)});return qn(n)},toString:function(){return"array("+o.toString()+")"}}},bn),Hn=(n.state,n.field),Jn=function(n){return"Errors: \n"+function(n){n=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return tn(n,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors).join("\n")+"\n\nInput object: "+ln(n.input)},Kn=function(){return(Kn=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)},J={},n={exports:J};Sn=J,On=n,_n=Tn=void 0,function(n){"object"==typeof Sn&&void 0!==On?On.exports=n():"function"==typeof Tn&&Tn.amd?Tn([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function r(o,u,i){function c(e,n){if(!u[e]){if(!o[e]){var t="function"==typeof _n&&_n;if(!n&&t)return t(e,!0);if(f)return f(e,!0);throw(t=new Error("Cannot find module '"+e+"'")).code="MODULE_NOT_FOUND",t}t=u[e]={exports:{}},o[e][0].call(t.exports,function(n){return c(o[e][1][n]||n)},t,t.exports,r,o,u,i)}return u[e].exports}for(var f="function"==typeof _n&&_n,n=0;n<i.length;n++)c(i[n]);return c}({1:[function(n,e,t){var r,o,e=e.exports={};function u(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function c(e){if(r===setTimeout)return setTimeout(e,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(n){try{return r.call(null,e,0)}catch(n){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(n){r=u}try{o="function"==typeof clearTimeout?clearTimeout:i}catch(n){o=i}}();var f,a=[],s=!1,l=-1;function d(){s&&f&&(s=!1,f.length?a=f.concat(a):l=-1,a.length&&g())}function g(){if(!s){var n=c(d);s=!0;for(var e=a.length;e;){for(f=a,a=[];++l<e;)f&&f[l].run();l=-1,e=a.length}f=null,s=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===i||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(n){try{return o.call(null,e)}catch(n){return o.call(this,e)}}}(n)}}function m(n,e){this.fun=n,this.array=e}function p(){}e.nextTick=function(n){var e=new Array(arguments.length-1);if(1<arguments.length)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];a.push(new m(n,e)),1!==a.length||s||c(g)},m.prototype.run=function(){this.fun.apply(null,this.array)},e.title="browser",e.browser=!0,e.env={},e.argv=[],e.version="",e.versions={},e.on=p,e.addListener=p,e.once=p,e.off=p,e.removeListener=p,e.removeAllListeners=p,e.emit=p,e.prependListener=p,e.prependOnceListener=p,e.listeners=function(n){return[]},e.binding=function(n){throw new Error("process.binding is not supported")},e.cwd=function(){return"/"},e.chdir=function(n){throw new Error("process.chdir is not supported")},e.umask=function(){return 0}},{}],2:[function(n,l,e){(function(e){function r(){}function u(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],s(n,this)}function o(t,r){for(;3===t._state;)t=t._value;0!==t._state?(t._handled=!0,u._immediateFn(function(){var n,e=1===t._state?r.onFulfilled:r.onRejected;if(null!==e){try{n=e(t._value)}catch(n){return void c(r.promise,n)}i(r.promise,n)}else(1===t._state?i:c)(r.promise,t._value)})):t._deferreds.push(r)}function i(e,n){try{if(n===e)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if(n instanceof u)return e._state=3,e._value=n,void f(e);if("function"==typeof t)return void s((r=t,o=n,function(){r.apply(o,arguments)}),e)}e._state=1,e._value=n,f(e)}catch(n){c(e,n)}var r,o}function c(n,e){n._state=2,n._value=e,f(n)}function f(n){2===n._state&&0===n._deferreds.length&&u._immediateFn(function(){n._handled||u._unhandledRejectionFn(n._value)});for(var e=0,t=n._deferreds.length;e<t;e++)o(n,n._deferreds[e]);n._deferreds=null}function a(n,e,t){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof e?e:null,this.promise=t}function s(n,e){var t=!1;try{n(function(n){t||(t=!0,i(e,n))},function(n){t||(t=!0,c(e,n))})}catch(n){if(t)return;t=!0,c(e,n)}}var n,t;n=this,t=setTimeout,u.prototype.catch=function(n){return this.then(null,n)},u.prototype.then=function(n,e){var t=new this.constructor(r);return o(this,new a(n,e,t)),t},u.all=function(n){var c=Array.prototype.slice.call(n);return new u(function(o,u){if(0===c.length)return o([]);var i=c.length;for(var n=0;n<c.length;n++)!function e(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var r=n.then;if("function"==typeof r)return void r.call(n,function(n){e(t,n)},u)}c[t]=n,0==--i&&o(c)}catch(n){u(n)}}(n,c[n])})},u.resolve=function(e){return e&&"object"==typeof e&&e.constructor===u?e:new u(function(n){n(e)})},u.reject=function(t){return new u(function(n,e){e(t)})},u.race=function(o){return new u(function(n,e){for(var t=0,r=o.length;t<r;t++)o[t].then(n,e)})},u._immediateFn="function"==typeof e?function(n){e(n)}:function(n){t(n,0)},u._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},u._setImmediateFn=function(n){u._immediateFn=n},u._setUnhandledRejectionFn=function(n){u._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=u:n.Promise||(n.Promise=u)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(f,n,a){(function(n,e){var r=f("process/browser.js").nextTick,t=Function.prototype.apply,o=Array.prototype.slice,u={},i=0;function c(n,e){this._id=n,this._clearFn=e}a.setTimeout=function(){return new c(t.call(setTimeout,window,arguments),clearTimeout)},a.setInterval=function(){return new c(t.call(setInterval,window,arguments),clearInterval)},a.clearTimeout=a.clearInterval=function(n){n.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},a.enroll=function(n,e){clearTimeout(n._idleTimeoutId),n._idleTimeout=e},a.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},a._unrefActive=a.active=function(n){clearTimeout(n._idleTimeoutId);var e=n._idleTimeout;0<=e&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},e))},a.setImmediate="function"==typeof n?n:function(n){var e=i++,t=!(arguments.length<2)&&o.call(arguments,1);return u[e]=!0,r(function(){u[e]&&(t?n.apply(null,t):n.call(null),a.clearImmediate(e))}),e},a.clearImmediate="function"==typeof e?e:function(n){delete u[n]}}).call(this,f("timers").setImmediate,f("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,e,t){var r=n("promise-polyfill"),n="undefined"!=typeof window?window:Function("return this;")();e.exports={boltExport:n.Promise||r}},{"promise-polyfill":2}]},{},[4])(4)});function Xn(n){setTimeout(function(){throw n},0)}function Yn(n){return oe(re.nu(n))}function $n(){return(new Date).getTime()}function Gn(n,t){function r(n,e,t){i[n]={result:e,timestamp:t}}function o(n,e){return n-e<t}void 0===t&&(t=36e5);var u,i={};return X(n)&&(n=n,u=$n(),y(n,function(n,e){o(u,n.timestamp)&&r(e,n.result,n.timestamp)})),{set:r,get:function(e,n){return z.from(i[n]).filter(function(n){return o(e,n.timestamp)}).map(function(n){return n.result})},dump:function(){return i}}}var Qn,Zn=n.exports.boltExport,ne=function(n){var t=z.none(),e=[],r=function(n){o()?i(n):e.push(n)},o=function(){return t.isSome()},u=function(n){rn(n,i)},i=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){o()||(t=z.some(n),u(e),e=[])}),{get:r,map:function(t){return ne(function(e){r(function(n){e(t(n))})})},isReady:o}},ee={nu:ne,pure:function(e){return ne(function(n){n(e)})}},te=function(t){function n(n){t().then(n,Xn)}return{map:function(n){return te(function(){return t().then(n)})},bind:function(e){return te(function(){return t().then(function(n){return e(n).toPromise()})})},anonBind:function(n){return te(function(){return t().then(function(){return n.toPromise()})})},toLazy:function(){return ee.nu(n)},toCached:function(){var n=null;return te(function(){return null===n&&(n=t()),n})},toPromise:t,get:n}},re={nu:function(n){return te(function(){return new Zn(n)})},pure:function(n){return te(function(){return Zn.resolve(n)})}},oe=function(u){return Kn(Kn({},u),{toCached:function(){return oe(u.toCached())},bindFuture:function(e){return oe(u.bind(function(n){return n.fold(function(n){return re.pure(cn.error(n))},function(n){return e(n)})}))},bindResult:function(e){return oe(u.map(function(n){return n.bind(e)}))},mapResult:function(e){return oe(u.map(function(n){return n.map(e)}))},mapError:function(e){return oe(u.map(function(n){return n.mapError(e)}))},foldResult:function(e,t){return u.map(function(n){return n.fold(e,t)})},withTimeout:function(n,o){return oe(re.nu(function(e){var t=!1,r=setTimeout(function(){t=!0,e(cn.error(o()))},n);u.get(function(n){t||(clearTimeout(r),e(n))})}))}})},J=function(n){return oe(re.pure(cn.value(n)))},ue={nu:Yn,wrap:oe,pure:J,value:J,error:function(n){return oe(re.pure(cn.error(n)))},fromResult:function(n){return oe(re.pure(n))},fromFuture:function(n){return oe(n.map(cn.value))},fromPromise:function(n){return Yn(function(e){n.then(function(n){e(cn.value(n))},function(n){e(cn.error(n))})})}};(n=Qn=Qn||{}).JSON="json",n.Blob="blob",n.Text="text",n.FormData="formdata",n.MultipartFormData="multipart/form-data";function ie(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e}function ce(n,e){return-1!==n.indexOf(e)}function fe(t){return re.nu(function(e){var n=new FileReader;n.onload=function(n){n=n.target?n.target.result:new Blob([]);e(n)},n.readAsText(t)})}function ae(n){try{var e=JSON.parse(n);return cn.value(e)}catch(n){return cn.error("Response was not JSON.")}}function se(n){return re.pure(n.response)}function le(n,e){switch(n){case Qn.JSON:return ae(e.response).fold(function(){return se(e)},re.pure);case Qn.Blob:return n=e,z.from(n.response).map(fe).getOr(re.pure("no response content"));case Qn.Text:default:return se(e)}}function de(n,e){function t(){return ue.pure(e.response)}function r(n){return ue.error({message:n,status:e.status,responseText:e.responseText})}switch(n){case Qn.JSON:return ae(e.response).fold(r,ue.pure);case Qn.Blob:case Qn.Text:return t();default:return r("unknown data type")}}function ge(n){var e=(o=n.body,z.from(o).bind(function(n){switch(n.type){case Qn.JSON:return z.some("application/json");case Qn.FormData:return z.some("application/x-www-form-urlencoded; charset=UTF-8");case Qn.MultipartFormData:return z.none();case Qn.Text:default:return z.some("text/plain")}})),t=!0===n.credentials?z.some(!0):z.none(),r=function(n){switch(n){case Qn.Blob:return"application/octet-stream";case Qn.JSON:return"application/json, text/javascript";case Qn.Text:return"text/plain";default:return""}}(n.responseType)+", */*; q=0.01",o=void 0!==n.headers?n.headers:{};return{contentType:e,responseType:function(n){switch(n){case Qn.JSON:return z.none();case Qn.Blob:return z.some("blob");case Qn.Text:return z.some("text");default:return z.none()}}(n.responseType),credentials:t,accept:r,headers:o,progress:G(n.progress)?z.some(n.progress):z.none()}}function me(n){var t=new FormData;return y(n,function(n,e){t.append(e,n)}),t}function pe(i){return ue.nu(function(r){var t,o=new XMLHttpRequest;o.open(i.method,(t=i.url,z.from(i.query).map(function(n){var e=k(n,function(n,e){return encodeURIComponent(e)+"="+encodeURIComponent(n)}),n=ce(t,"?")?"&":"?";return 0<e.length?t+n+e.join("&"):t}).getOr(t)),!0);var u,n=ge(i);u=o,(n=n).contentType.each(function(n){return u.setRequestHeader("Content-Type",n)}),u.setRequestHeader("Accept",n.accept),n.credentials.each(function(n){return u.withCredentials=n}),n.responseType.each(function(n){return u.responseType=n}),n.progress.each(function(e){return u.upload.addEventListener("progress",function(n){return e(n.loaded,n.total)})}),y(n.headers,function(n,e){return u.setRequestHeader(e,n)});function e(){var e,n,t;e=i.url,n=i.responseType,le(n,t=o).map(function(n){return{message:0===t.status?"Unknown HTTP error (possible cross-domain request)":"Could not load url "+e+": "+t.statusText,status:t.status,responseText:n}}).get(function(n){return r(cn.error(n))})}o.onerror=e,o.onload=function(){var n;0===o.status&&(n=i.url,!ie(n,"file:",0))||o.status<100||400<=o.status?e():de(i.responseType,o).get(r)},n=i.body,z.from(n).map(function(n){return n.type===Qn.JSON?JSON.stringify(n.data):n.type===Qn.FormData||n.type===Qn.MultipartFormData?me(n.data):n.data}).fold(function(){return o.send()},function(n){o.send(n)})})}function he(n){return n.fold(function(n){var e=n.responseText,n=X(e)?e:n.message;return cn.error(n)},cn.value)}function ve(o,u){var n,i=S(n=u,"tiny-api-key").orThunk(function(){return S(n,"tinymce-api-key")}).orThunk(function(){return S(n,"textbox-api-key")}).getOrUndefined();return{execute:function(n){var e,t,r,n=(n={url:(t=i,r=-1===(e=o).indexOf("?")?"?":"&",t?e+r+"apiKey="+encodeURIComponent(t):e),body:(n=n,{type:Qn.JSON,data:n}),responseType:Qn.JSON,credentials:!0,headers:u},pe(Kn(Kn({},n),{method:"post"})).map(he));return ue.wrap(n)},cancelCurrent:w}}function ye(e,t){var r=[];return{execute:function(n){return(0===e?ue.fromPromise(new Zn(function(n,e){r.push({resume:n,reject:function(){return e("InternalCancel")}})})):(--e,ue.pure(void 0))).bindFuture(function(){return t.execute(n).map(function(n){return z.from(r.shift()).fold(function(){return e+=1},function(n){return setTimeout(n.resume,0)}),n})})},cancelCurrent:function(){for(var n;r.length;)null!==(n=r.pop())&&void 0!==n&&n.reject()}}}function we(n,e,t,r,o,u){var i,c,f,a,s,l;return 0===(t=(i=e.get,c=o,t=t,f=r,!0===u?{known:{},unknown:t}:A(t,function(e,n){var t=f(n);return i(c,t).fold(function(){return{known:e.known,unknown:e.unknown.concat([n])}},function(n){n=zn(t,n);return{known:Kn(Kn({},e.known),n),unknown:e.unknown}})},{known:{},unknown:[]}))).unknown.length?ue.value(t.known):(n=n,a=e,s=t.known,t=t.unknown,l=o,n(t,u).mapResult(function(n){return y(n,function(n,e){a.set(e,n,l)}),Kn(Kn({},s),n)}))}function be(n){return"["+n.join(", ")+"]"}function xe(e,n,t){return C(n,function(n){return E(e,n)})?cn.error("Embed response source: "+be(e)+" contains more than one of "+be(Qe)):cn.value(t)}function ke(i,n){return n(function(r){var o=[],u=0;0===i.length?r([]):rn(i,function(n,e){var t;n.get((t=e,function(n){o[t]=n,++u>=i.length&&r(o)}))})})}function Te(n){return ke(n,re.nu)}function Se(n){var e=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++nt+String(e)}function Oe(e,r,t,n,o){return o=function(n,e){for(var t=[],r=0;r<n.length;r+=e){var o=Z.call(n,r,r+e);t.push(o)}return t}(n,o),o=tn(o,function(n){n=kn("ephox.spelling.service.many.ajax.service.get",et,{language:t,words:n});return e.execute(n)}),o=Te(o).map(function(n){var e,t=gn(n);if(0<t.values.length){n=tn(t.values,r),n=A(n,Pn,{});return cn.value(n)}if(j(t.errors,tt))return cn.value({});if(0<t.errors.length){t=t.errors.filter((e=tt,function(n){return!e(n)})).join(", ");return cn.error("Failures communicating with the spelling server: "+t)}return cn.error("Unknown error")}),ue.wrap(o)}function _e(t,o,n,r,e,u,i,c){function f(n){return n.check}return we(function(n,e){return Oe(t,f,r,n,i)},n,e,a,u,void 0!==c&&c).mapResult(function(n){var t=[],r={};return y(n,function(n,e){n?o.set(e,rt,u):r[e]=t}),r})}function Ee(t,o,r,n,u,i,e){function c(n){return n.spell}return we(function(n,e){return Oe(t,c,r,n,i)},o,n,a,u,void 0!==e&&e).mapResult(function(e){rn(n,function(n){U(e,n)||o.set(n,rt,u)});var r={};return y(e,function(e,t){o.get(u,t).each(function(n){n!==rt&&(r[t]=e)})}),r})}function Ce(n){void 0===n&&(n={});var t={};return y(n,function(n,e){t[e]=Gn(n)}),t}function Ie(n,e){return S(n,e).getOrThunk(function(){return Gn()})}function Ae(n){return e={},rn(n,function(n){e[n]={}}),W(e).sort();var e}function De(n,e){return function(n,e){for(var t=null!=e?e:He,r=0;r<n.length&&null!=t;++r)t=t[n[r]];return t}(n.split("."),e)}function Re(n){return function(n,e){e=ft(n,e);if(null==e)throw new Error(n+" not available on this browser");return e}("HTMLElement",n)}function Le(n){return n.dom.nodeName.toLowerCase()}function Me(n){return n.dom.nodeType}function je(n){return 8===Me(n)||"#comment"===Le(n)}function Ne(n){return e=n.dom,n=De("ownerDocument.defaultView",e),Re(n).prototype.isPrototypeOf(e);var e}function Pe(e){return function(n){return at(n)&&Le(n)===e}}function Fe(n,e,t){if(!(K(t)||$(t)||Q(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")}function Be(n,e){return null===(e=n.dom.getAttribute(e))?void 0:e}function ze(n,e){return z.from(Be(n,e))}var We,qe,Ve,Ue,He="undefined"!=typeof window?window:Function("return this;")(),Je=function(n,e){return ie(n,e,n.length-e.length)},Ke=(We=/^\s+|\s+$/g,function(n){return n.replace(We,"")}),J=function(n){return Hn(n,n,Bn(),Vn())},n=function(n,e){return Hn(n,n,(e=e,Fn.defaultedThunk(l(e))),Vn())},Xe="iframely",Ye="oembed",$e="fallback",Ge="wikipedia",Qe=[Xe,Ye,$e],Ze=(bn([J("url"),J("maxWidth"),n("fresh",!1)]),bn([J("status_code"),J("sub_code"),J("msg")]),bn([J("html"),(qe="rels",Ve=[Hn("source","source",Bn(),(Ue=function(n){return Y(n)?E(e=n,Xe)?xe(e,[Ye,$e,Ge],Xe):E(e,Ye)?xe(e,[Xe,$e,Ge],Ye):E(e,$e)?xe(e,[Xe,Ye,Ge],$e):E(e,Ge)?xe(e,[Xe,Ye,$e],Ge):cn.error("Embed response source: "+be(e)+" did not contain any of: "+be(Qe)):cn.error("Sources was not an array: "+n);var e},wn(function(n){return Ue(n).fold(An,Cn)})))],Hn(qe,qe,Bn(),bn(Ve)))]),[J("url"),n("fresh",!1)]),nt=(bn([Hn("urls","urls",Bn(),Un(Ze))]),H([{invalid:["invalidUrl"]},{unknown:["unknownUrl"]},{valid:["validUrl"]}]),0),et=bn([J("language"),J("words")]),tt=function(n){return"InternalCancel"===n},rt=Se("good-spelling"),ot={chunkSize:1500,maxRetries:3,maxInFlight:8},ut=function(n,e,t,r,c){void 0===c&&(c=ot);var u=Ce(r),i=Ce({}),r=function(n){return ye(c.maxInFlight,(u=c.maxRetries,{execute:function(n){for(var e=function(){return i.execute(n)},t=function(n){return n.fold(e,ue.pure)},r=e(),o=1;o<u;++o)r=r.bind(t);return ue.wrap(r)},cancelCurrent:(i=n).cancelCurrent}));var u,i},f=r(ve(n,t)),o=r(ve(e,t));return{checkMany:function(n,e){var t=Ae(e),r=$n(),o=Ie(i,n),e=Ie(u,n);return i[n]=o,u[n]=e,_e(f,e,o,n,t,r,c.chunkSize)},correctOne:function(n,e){var t=$n(),r=Ie(u,n);return u[n]=r,Ee(o,r,n,[e],t,c.chunkSize)},correctMany:function(n,e){var t=Ae(e),r=$n(),e=Ie(u,n);return u[n]=e,Ee(o,e,n,t,r,c.chunkSize)},cancelCurrent:function(){f.cancelCurrent(),o.cancelCurrent()},dumpCache:function(){return x(u,function(n){return n.dump()})}}},it=function(n){if(null==n)throw new Error("Node cannot be null or undefined");return{dom:n}},ct={fromHtml:function(n,e){e=(e||document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return it(e.childNodes[0])},fromTag:function(n,e){n=(e||document).createElement(n);return it(n)},fromText:function(n,e){n=(e||document).createTextNode(n);return it(n)},fromDom:it,fromPoint:function(n,e,t){return z.from(n.dom.elementFromPoint(e,t)).map(it)}},ft=De,n=function(e){return function(n){return Me(n)===e}},at=n(1),st=n(3),lt=n(9),dt=n(11),gt=function(n,e,t){Fe(n.dom,e,t)},mt=function(n,e){n.dom.removeAttribute(e)};function pt(n,e){var t=String(e).toLowerCase();return D(n,function(n){return n.search(t)})}function ht(n){return window.matchMedia(n).matches}function vt(n,e){if(1!==(n=n.dom).nodeType)return!1;if(void 0!==n.matches)return n.matches(e);if(void 0!==n.msMatchesSelector)return n.msMatchesSelector(e);if(void 0!==n.webkitMatchesSelector)return n.webkitMatchesSelector(e);if(void 0!==n.mozMatchesSelector)return n.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")}function yt(n){return ct.fromDom(n.dom.ownerDocument)}function wt(n){return lt(n)?n:yt(n)}function bt(n){return z.from(n.dom.parentNode).map(ct.fromDom)}function xt(n,e){for(var t=G(e)?e:f,r=n.dom,o=[];null!==r.parentNode&&void 0!==r.parentNode;){var u=r.parentNode,i=ct.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o}function kt(n){return z.from(n.dom.previousSibling).map(ct.fromDom)}function Tt(n){return z.from(n.dom.nextSibling).map(ct.fromDom)}function St(n,e){return n=n.dom.childNodes,z.from(n[e]).map(ct.fromDom)}function Ot(n,e){return{element:n,offset:e}}function _t(n){return n=wr(n),dt(n)?z.some(n):z.none()}function Et(n){return ct.fromDom(n.dom.host)}function Ct(n,e,t){for(var r=n.dom,o=G(t)?t:f;r.parentNode;){r=r.parentNode;var u=ct.fromDom(r);if(e(u))return z.some(u);if(o(u))break}return z.none()}function It(n,e,t){return Ct(n,function(n){return vt(n,e)},t)}function At(n,e,t){var r;return r=It,t=t,vt(n=n,e=e)?z.some(n):G(t)&&t(n)?z.none():r(n,e,t)}function Dt(n){return n.getParam("spellchecker_dialog",!1,"boolean")}function Rt(n){return void 0!==n.style&&G(n.style.getPropertyValue)}function Lt(n,e,t){!function(n,e,t){if(!K(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Rt(n)&&n.style.setProperty(e,t)}(n.dom,e,t)}function Mt(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||xr(n)?r:_r(t,e)}function jt(n,e){return n=n.dom,e=_r(n,e),z.from(e).filter(function(n){return 0<n.length})}function Nt(n,e){var t=n.dom;e=e,Rt(t=t)&&t.style.removeProperty(e),ze(n,"style").map(Ke).is("")&&mt(n,"style")}function Pt(e,t){bt(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})}function Ft(n,e){Tt(n).fold(function(){bt(n).each(function(n){Er(n,e)})},function(n){Pt(n,e)})}function Bt(e,t){St(e,0).fold(function(){Er(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})}function zt(n,e){Pt(n,e),Er(e,n)}function Wt(e,n){rn(n,function(n){Pt(e,n)})}function qt(t,r){rn(r,function(n,e){e=0===e?t:r[e-1];Ft(e,n)})}function Vt(e,n){rn(n,function(n){Er(e,n)})}function Ut(n){null!==(n=n.dom).parentNode&&n.parentNode.removeChild(n)}function Ht(n){var e=hr(n);0<e.length&&Wt(n,e),Ut(n)}function Jt(n,e){return t=e,1!==(e=n=void 0===(e=n)?document:e.dom).nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount?[]:tn(n.querySelectorAll(t),ct.fromDom);var t}var Kt,Xt,Yt,$t=function(){return Gt(0,0)},Gt=function(n,e){return{major:n,minor:e}},Qt={nu:Gt,detect:function(n,e){e=String(e).toLowerCase();return 0===n.length?$t():function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}}(n,e);if(!t)return{major:0,minor:0};n=function(n){return Number(e.replace(t,"$"+n))};return Gt(n(1),n(2))}(n,e)},unknown:$t},Zt=function(n,t){return pt(n,t).map(function(n){var e=Qt.detect(n.versionRegexes,t);return{current:n.name,version:e}})},nr=function(n,t){return pt(n,t).map(function(n){var e=Qt.detect(n.versionRegexes,t);return{current:n.name,version:e}})},J=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,n=function(e){return function(n){return ce(n,e)}},J=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return ce(n,"edge/")&&ce(n,"chrome")&&ce(n,"safari")&&ce(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,J],search:function(n){return ce(n,"chrome")&&!ce(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return ce(n,"msie")||ce(n,"trident")}},{name:"Opera",versionRegexes:[J,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:n("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:n("firefox")},{name:"Safari",versionRegexes:[J,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(ce(n,"safari")||ce(n,"mobile/"))&&ce(n,"applewebkit")}}],n=[{name:"Windows",search:n("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return ce(n,"iphone")||ce(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:n("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:n("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:n("linux"),versionRegexes:[]},{name:"Solaris",search:n("sunos"),versionRegexes:[]},{name:"FreeBSD",search:n("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:n("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],er={browsers:l(J),oses:l(n)},tr="Firefox",rr=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:n("Edge"),isChrome:n("Chrome"),isIE:n("IE"),isOpera:n("Opera"),isFirefox:n(tr),isSafari:n("Safari")}},or={unknown:function(){return rr({current:void 0,version:Qt.unknown()})},nu:rr,edge:l("Edge"),chrome:l("Chrome"),ie:l("IE"),opera:l("Opera"),firefox:l(tr),safari:l("Safari")},ur="Windows",ir="Android",cr="Solaris",fr="FreeBSD",ar="ChromeOS",sr=function(n){var e=n.current,t=n.version,n=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:n(ur),isiOS:n("iOS"),isAndroid:n(ir),isOSX:n("OSX"),isLinux:n("Linux"),isSolaris:n(cr),isFreeBSD:n(fr),isChromeOS:n(ar)}},lr={unknown:function(){return sr({current:void 0,version:Qt.unknown()})},nu:sr,windows:l(ur),ios:l("iOS"),android:l(ir),linux:l("Linux"),osx:l("OSX"),solaris:l(cr),freebsd:l(fr),chromeos:l(ar)},dr=function(n,e){var t,r,o=er.browsers(),u=er.oses(),i=Zt(o,n).fold(or.unknown,or.nu),c=nr(u,n).fold(lr.unknown,lr.nu);return{browser:i,os:c,deviceType:(t=i,r=n,o=e,i=(u=c).isiOS()&&!0===/ipad/i.test(r),n=u.isiOS()&&!i,e=u.isiOS()||u.isAndroid(),c=e||o("(pointer:coarse)"),o=i||!n&&e&&o("(min-device-width:768px)"),e=n||e&&!o,t=t.isSafari()&&u.isiOS()&&!1===/safari/i.test(r),r=!e&&!o&&!t,{isiPad:l(i),isiPhone:l(n),isTablet:l(o),isPhone:l(e),isTouch:l(c),isAndroid:u.isAndroid,isiOS:u.isiOS,isWebView:l(t),isDesktop:l(r)})}},gr=(Yt=!(Kt=function(){return dr(navigator.userAgent,ht)}),function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return Yt||(Yt=!0,Xt=Kt.apply(null,n)),Xt}),mr=function(n,e){return n.dom===e.dom},pr=vt,hr=function(n){return tn(n.dom.childNodes,ct.fromDom)},vr=function(n){return n.dom.childNodes.length},J=G(Element.prototype.attachShadow)&&G(Node.prototype.getRootNode),yr=l(J),wr=J?function(n){return ct.fromDom(n.dom.getRootNode())}:wt,br=function(n){return O(n.dom.shadowRoot)},xr=function(n){var e=st(n)?n.dom.parentNode:n.dom;if(null==e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return _t(ct.fromDom(e)).fold(function(){return o.body.contains(e)},(t=xr,r=Et,function(n){return t(r(n))}))},kr={ar:"Arabic",ca:"Catalan",zh:"Chinese",zh_cn:"Chinese (Simplified)",zh_tw:"Chinese (Traditional)",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch",en:"English",en_au:"English (Australia)",en_ca:"English (Canada)",en_gb:"English (United Kingdom)",en_us:"English (United States)",fa:"Farsi",fi:"Finnish",fr:"French",fr_ca:"French (Canada)",de:"German",el:"Greek",he:"Hebrew",hu:"Hungarian",it:"Italian",ja:"Japanese",kk:"Kazakh",ko:"Korean",nb:"Norwegian",no:"Norwegian",pl:"Polish",pt:"Portuguese",pt_br:"Portuguese (Brazil)",pt_pt:"Portuguese (Portugal)",ro:"Romanian",ru:"Russian",sk:"Slovak",sl:"Slovenian",es:"Spanish",es_419:"Spanish (Latin America)",es_es:"Spanish (Spain)",sv:"Swedish",tt:"Tatar",th:"Thai",tr:"Turkish",uk:"Ukrainian"},Tr=["en_us","en_gb","da","nl","fi","fr","de","it","nb","pt","pt_pt","es","sv"],Sr=[{value:"no.language",text:"Off"}],Or={useDialog:Dt,getLanguage:function(n){return n.getParam("spellchecker_language","en_us","string")},setLanguage:function(n,e){n.settings.spellchecker_language=e},getLanguages:function(n){var e=!Dt(n),t=n.getParam("spellchecker_languages",Tr),n=[];return K(t)?n=function(n){n=n.split(",");return tn(n,function(n){n=n.split("=");return{value:n[1],text:n[0]}})}(t):function(n,e){if(Y(n)){for(var t=0,r=n.length;t<r;++t)if(!e(n[t]))return!1;return!0}return!1}(t,K)&&(n=tn(t,function(n){return{value:n,text:U(kr,n)?kr[n]:n.toUpperCase()}})),e?Sr.concat(n):n},getWhitelist:function(n){return n.getParam("spellchecker_whitelist",[],"array")},getApiKey:function(n){return n.getParam("api_key")||n.getParam("spellchecker_api_key")},getHandler:function(n){return n.getParam("spellchecker_handler")},getRpcUrl:function(n){return n.getParam("spellchecker_rpc_url","","string")},isActive:function(n){return n.getParam("spellchecker_active",!0,"boolean")},getSelectContentLanguages:function(n){return n.getParam("spellchecker_select_languages","en,es,fr,de,pt,zh")},getSpellingServiceSettings:function(n){return{chunkSize:n.getParam("spellchecker_service_chunk_size",ot.chunkSize,"number"),maxRetries:n.getParam("spellchecker_service_max_retries",ot.maxRetries,"number"),maxInFlight:n.getParam("spellchecker_service_max_in_flight",ot.maxInFlight,"number")}}},_r=function(n,e){return Rt(n)?n.style.getPropertyValue(e):""},Er=function(n,e){n.dom.appendChild(e.dom)},Cr=function(n,e){var t=[];return rn(hr(n),function(n){e(n)&&(t=t.concat([n])),t=t.concat(Cr(n,e))}),t};function Ir(n){return Mr.get(n)}function Ar(n,e){return Mr.set(n,e)}var Dr,Rr,Lr,Mr=(Dr=st,Rr="text",{get:function(n){if(!Dr(n))throw new Error("Can only get "+Rr+" value of a "+Rr+" node");return Lr(n).getOr("")},getOption:Lr=function(n){return Dr(n)?z.from(n.dom.nodeValue):z.none()},set:function(n,e){if(!Dr(n))throw new Error("Can only set raw "+Rr+" value of a "+Rr+" node");n.dom.nodeValue=e}}),jr=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function Nr(){return{up:l({selector:It,closest:At,predicate:Ct,all:xt}),down:l({selector:Jt,predicate:Cr}),styles:l({get:Mt,getRaw:jt,set:Lt,remove:Nt}),attrs:l({get:Be,set:gt,remove:mt,copyTo:function(n,e){var t,n=A(n.dom.attributes,function(n,e){return n[e.name]=e.value,n},{});n=n,t=e.dom,y(n,function(n,e){Fe(t,e,n)})}}),insert:l({before:Pt,after:Ft,afterAll:qt,append:Er,appendAll:Vt,prepend:Bt,wrap:zt}),remove:l({unwrap:Ht,remove:Ut}),create:l({nu:ct.fromTag,clone:function(n){return ct.fromDom(n.dom.cloneNode(!1))},text:ct.fromText}),query:l({comparePosition:function(n,e){return n.dom.compareDocumentPosition(e.dom)},prevSibling:kt,nextSibling:Tt}),property:l({children:hr,name:Le,parent:bt,document:function(n){return wt(n).dom},isText:st,isComment:je,isElement:at,getText:Ir,setText:Ar,isBoundary:function(n){return!!at(n)&&("body"===Le(n)||E(jr,Le(n)))},isEmptyTag:function(n){return!!at(n)&&E(["br","img","hr","input"],Le(n))},isNonEditable:function(n){return at(n)&&"false"===Be(n,"contenteditable")}}),eq:mr,is:pr}}function Pr(n,e,t){return{element:n,start:e,finish:t}}function Fr(n,e,t,r){var o,u,i,c,f=(u=e,i=t,c=r,(f=yt(o=n).dom.createRange()).setStart(o.dom,u),f.setEnd(i.dom,c),f),r=mr(n,t)&&e===r;return f.collapsed&&!r}function Br(n){return st(n)&&0===Ir(n).length}function zr(o){var u=St(o.startContainer(),o.startOffset()).filter(Pe("img"));return Tt(o.startContainer()).filter(Pe("img")).orThunk(function(){return u}).bind(function(r){return kt(o.endContainer()).filter(Pe("img")).bind(function(n){var e=mr(r,n),t=vr(o.startContainer())===o.startOffset(),n=0===o.endOffset();return e&&(t||u.isSome())&&n?z.some(r):z.none()})})}function Wr(n){return n.filter(function(n){return!n.collapsed()}).bind(function(n){return function(n){var e=n.startContainer();if(!mr(e,n.endContainer()))return z.none();if(Pe("img")(e))return z.some(e);var t=Math.min(n.startOffset(),n.endOffset()),n=Math.max(n.startOffset(),n.endOffset()),e=St(e,t).filter(Pe("img"));return n===t||n===t+1?e:io&&n===t+2?e.filter(function(n){return Tt(n).filter(Br).isSome()}):z.none()}(n).orThunk(function(){return zr(n)})})}function qr(n){return n.slice(0).sort()}function Vr(e,n){0<(n=I(n,function(n){return!E(e,n)})).length&&function(n){throw new Error("Unsupported keys for object: "+qr(n).join(", "))}(n)}function Ur(n,e){return{item:n,mode:e}}function Hr(n,e,t,r){return void 0===r&&(r=so),n.property().parent(e).map(function(n){return Ur(n,r)})}function Jr(n,e,t,r){return ho(n,e,t,so,po.right(),r)}function Kr(){return{left:po.left,right:po.right}}function Xr(n,e){return{ch:n.charAt(e),offset:e}}function Yr(n){return O(n)&&void 0!==n.index&&0<=n.index?z.some(n.index):z.none()}function $r(n,e){return t=e,E(["br","img","hr","input"],n.property().name(t))||n.property().isBoundary(e);var t}function Gr(e,n,t,r){var o=ro(e,n,t);return e.property().isText(n)?z.none():(t=e,n=o.element,o=e.property().isText,r=r,ho(t,n,o,so,po.left(),r).map(function(n){return no(n,e.property().getText(n).length)}))}function Qr(e,n,t){function r(n){return $r(e,n)}return Gr(e,n,t,r).orThunk(function(){return function(n,e,t,r){t=ro(n,e,t);return n.property().isText(e)?z.none():Jr(n,t.element,n.property().isText,r).map(function(n){return no(n,0)})}(e,n,t,r)}).getOr(no(n,t))}function Zr(n,e){var t=no(n,e);return st(n)?(n=n,e=e,Io.scanRight(Ao,n,e).getOr(t)):t}var no=function(n,e){return{element:n,offset:e}},eo=function(n,e){if(n.property().isText(e))return no(e,n.property().getText(e).length);var t=n.property().children(e);return 0<t.length?eo(n,t[t.length-1]):no(e,t.length)},to=function(n,e,t){var r=n.property().children(e);return 0<r.length&&t<r.length?to(n,r[t],0):0<r.length&&n.property().isElement(e)&&r.length===t?eo(n,r[r.length-1]):no(e,t)},ro=to,oo=Nr(),uo=function(n,e){return ro(oo,n,e)},io=gr().browser.isFirefox(),co={detect:function(n){return Wr(n).map(function(n){return{image:function(){return n}}})}},fo=function(n,e,t,r){var o=mr(n,t)&&e===r;return{startContainer:l(n),startOffset:l(e),endContainer:l(t),endOffset:l(r),collapsed:l(o)}},ao=function(r,o,u){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(e,n){if(!Y(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");rn(n,function(n){if(!K(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}("required",o),t=qr(o),D(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=W(e);j(o,function(n){return E(t,n)})||function(n,e){throw new Error("All required keys ("+qr(n).join(", ")+") were not specified. Specified keys were: "+qr(e).join(", ")+".")}(o,t),r(o,t);var n=I(o,function(n){return!u.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+qr(n).join(", ")+") were not.")}(n,u.label),e}},so=function(n,e,t,r){return void 0===r&&(r=lo),t.sibling(n,e).map(function(n){return Ur(n,r)})},lo=function(n,e,t,r){void 0===r&&(r=lo);e=n.property().children(e);return t.first(e).map(function(n){return Ur(n,r)})},go=[{current:Hr,next:so,fallback:z.none()},{current:so,next:lo,fallback:z.some(Hr)},{current:lo,next:lo,fallback:z.some(so)}],mo=function(e,t,r,o,n){return void 0===n&&(n=go),D(n,function(n){return n.current===r}).bind(function(n){return n.current(e,t,o,n.next).orThunk(function(){return n.fallback.bind(function(n){return mo(e,t,n,o)})})})},po={left:function(){return{sibling:function(n,e){return n.query().prevSibling(e)},first:function(n){return 0<n.length?z.some(n[n.length-1]):z.none()}}},right:function(){return{sibling:function(n,e){return n.query().nextSibling(e)},first:function(n){return 0<n.length?z.some(n[0]):z.none()}}}},ho=function(e,n,t,r,o,u){return mo(e,n,r,o).bind(function(n){return u(n.item)?z.none():t(n.item)?z.some(n.item):ho(e,n.item,t,n.mode,o,u)})},vo=Jr,yo=mo,wo="\ufeff",bo=Kr().left(),xo=Kr().right(),ko=H([{abort:[]},{kontinue:[]},{finish:["info"]}]),To=H([{aborted:[]},{edge:["element"]},{success:["info"]}]),So=function(t,n,r,e,o,u,i){function c(){return i.fold(To.aborted,To.edge)}function f(e){return yo(t,n,r,u).fold(c,function(n){return So(t,n.item,n.mode,z.none(),o,u,e)})}if($r(t,n))return c();if(t.property().isText(n)){var a=t.property().getText(n);return o(t,ko,n,a,e).fold(c,function(){return f(z.some(n))},To.success)}return f(i)},n=function(n,e,t,r){t=Qr(n,e,t);return So(n,t.element,so,z.some(t.offset),r,bo,z.none())},J=function(n,e,t,r){t=Qr(n,e,t);return So(n,t.element,so,z.some(t.offset),r,xo,z.none())},Oo=ao(Vr,["regex","attempt"],{validate:G,label:"function"}),_o=n,Eo=J,Co=function(e,n,t){var r=f;if(!e.property().isText(n))return z.none();var o=e.property().getText(n);return t<=o.length?z.some(no(n,t)):vo(e,n,e.property().isText,r).bind(function(n){return Co(e,n,t-o.length)})},Io={previousChar:function(n,e){for(var t=e.getOr(n.length)-1;0<=t;t--)if(n.charAt(t)!==wo)return z.some(Xr(n,t));return z.none()},nextChar:function(n,e){for(var t=e.getOr(0)+1;t<n.length;t++)if(n.charAt(t)!==wo)return z.some(Xr(n,t));return z.none()},repeatLeft:_o,repeatRight:Eo,expandLeft:function(n,e,t,r){var u=Oo(r);return _o(n,e,t,function(n,e,t,r,o){o=o.getOr(r.length);return function(n,e){e.lastIndex=-1;var t=N(n).join("");return Yr(t.match(e)).map(function(n){return t.length-1-n})}(r.substring(0,o),u.regex()).fold(function(){return e.kontinue()},function(n){return u.attempt(e,t,r,n)})})},expandRight:function(n,e,t,r){var c=Oo(r);return Eo(n,e,t,function(n,e,t,r,o){var u,i=o.getOr(0);return(u=r.substring(i),(o=c.regex()).lastIndex=-1,Yr(u.match(o))).fold(function(){return e.kontinue()},function(n){return c.attempt(e,t,r,i+n)})})},scanRight:Co},Ao=Nr(),Do={handle:function(n){var e=Zr(n.startContainer(),n.startOffset()),n=Zr(n.endContainer(),n.endOffset());return fo(e.element,e.offset,n.element,n.offset)}},Ro=gr().browser,Lo=function(n){return Fr(n.endContainer(),n.endOffset(),n.startContainer(),n.startOffset())},Mo=function(n){var e,t,r,t=(e=n.startContainer(),t=n.startOffset(),(0<(r=hr(e)).length&&t<r.length?Ot(r[t],0):Ot(e,t)).element);return at(t)&&"false"===Be(t,"contenteditable")&&mr(n.startContainer(),n.endContainer())&&n.startOffset()===n.endOffset()-1},jo=function(n){return st(n)||"br"===Le(n)};function No(r){function o(){var n=r.selection.getRng(),e=ct.fromDom(n.startContainer),t=ct.fromDom(n.endContainer);return fo(e,n.startOffset,t,n.endOffset)}function t(n,e){var t=n(),n=Do.handle(e),e=o();return(!mr(e.startContainer(),n.startContainer())||!mr(e.endContainer(),n.endContainer())||e.startOffset()!==n.startOffset()||e.endOffset()!==n.endOffset())&&(e=n,(n=r.dom.createRng()).setStart(e.startContainer().dom,e.startOffset()),n.setEnd(e.endContainer().dom,e.endOffset()),r.selection.setRng(n)),t}function u(n,e){return jo(e.startContainer())&&jo(e.endContainer())||co.detect(z.some(e)).isSome()?t(n,e):(e=n,n=r.selection.getBookmark(),e=e(),r.selection.moveToBookmark(n),e)}return{transaction:function(n){var e=o(),t=e.collapsed()||!Ro.isIE()||Lo(e),e=function(n,e){if(!e||Ro.isFirefox()||Mo(n))return n;e=uo(n.endContainer(),n.endOffset()),n=uo(n.startContainer(),n.startOffset());return fo(n.element,n.offset,e.element,e.offset)}(e,t);return t?u(n,e):n()}}}function Po(n){return!n.inline&&ce(n.getParam("plugins","","string"),"autoresize")}function Fo(n){return!n.inline&&!Po(n)}function Bo(n){return n.dom.getBoundingClientRect()}function zo(n){function e(){return n.stopPropagation()}function t(){return n.preventDefault()}var r=ct.fromDom(function(n){if(yr()&&O(n.target)){var e=ct.fromDom(n.target);if(at(e)&&br(e)&&n.composed&&n.composedPath){e=n.composedPath();if(e)return P(e)}}return z.from(n.target)}(n).getOr(n.target)),o=u(t,e);return{target:r,x:n.clientX,y:n.clientY,stop:e,prevent:t,kill:o,raw:n}}function Wo(n,e,t,r,o){var u,i,r=(u=t,i=r,function(n){u(n)&&i(zo(n))});return n.dom.addEventListener(e,r,o),{unbind:c(Nu,n,e,r,o)}}function qo(n,e,t){return Wo(n,e,Pu,t,!1)}function Vo(n,e){return void 0===(e=Be(n,e))||""===e?[]:e.split(" ")}function Uo(n){return void 0!==n.dom.classList}function Ho(n,e){return function(n,e,t){t=Vo(n,e).concat([t]);return gt(n,e,t.join(" ")),!0}(n,"class",e)}function Jo(n,e){return r=e,0<(n=I(Vo(t=n,e="class"),function(n){return n!==r})).length?gt(t,e,n.join(" ")):mt(t,e),0;var t,r}function Ko(n,e){Uo(n)?n.dom.classList.add(e):Ho(n,e)}function Xo(n){0===(Uo(n)?n.dom.classList:Vo(n,"class")).length&&mt(n,"class")}function Yo(n,e){Uo(n)?n.dom.classList.remove(e):Jo(n,e),Xo(n)}function $o(n,e){return R(n,function(n){return n.start===e})}function Go(n,e,t){var r;return e=e=t(n,e),r=n.start,tn(e,function(n){return Kn(Kn({},n),{start:n.start+r,finish:n.finish+r})})}function Qo(e,n){return e.up().closest(n,"[lang]",f).bind(function(n){n=e.attrs().get(n,"lang");return void 0===n?z.none():z.some(n)})}function Zo(n,e,t,r){return{item:n,start:e,finish:t,text:r}}function nu(n,e){return{items:n,abort:e}}function eu(n,e){return n=n.property().getText(e),Zo(e,0,n.length,n)}function tu(n,e,t){return nu([],!0)}function ru(n,e,t){return nu([],!1)}function ou(n,e,t){var r=n.property().getText(e);return t(r).fold(function(){return nu([Zo(e,0,r.length,r)],!1)},function(n){n=n[0]===n[1]?[]:[Zo(e,n[0],n[1],r.substring(n[0],n[1]))];return nu(n,!0)})}function uu(n,e,t,r){return{term:function(){return new RegExp(n,r.getOr("g"))},prefix:e,suffix:t}}function iu(n,e){for(var t=e.term(),r=[],o=t.exec(n);o;){var u=o.index+e.prefix(o),i=o[0].length-e.prefix(o)-e.suffix(o);r.push({start:u,finish:u+i}),t.lastIndex=u+i,o=t.exec(n)}return r}function cu(n,e){return c(n.eq,e)}function fu(t,r,n,e){var o=t.property().children(r);if(t.eq(r,n[0]))return z.some([n[0]]);if(t.eq(r,e[0]))return z.some([e[0]]);function u(n){var e=N(n),n=(n=R(e,cu(t,r)).getOr(-1))<e.length-1?e[n+1]:e[n];return R(o,cu(t,n))}var n=u(n),i=u(e);return n.bind(function(t){return i.map(function(n){var e=Math.min(t,n),n=Math.max(t,n);return o.slice(e,n+1)})})}function au(n,e,t){return{word:n,left:e,right:t}}function su(n){return E(Oi,n)}function lu(n){var e,t=n.word;return 2<=t.length&&su(t.charAt(t.length-1))&&!su(t.charAt(t.length-2))?(t=(e=n).word,au(t.substring(0,t.length-1),e.left,z.some(t.charAt(t.length-1)))):n}function du(n){var e=n.word,t=C(_i,function(n){return-1<e.indexOf(n)})?2:1,r=e.substring(0,t);return j(r,su)&&!su(e.charAt(t))?(t=(r=n).word,au(t.substring(1),z.some(t.charAt(0)),r.right)):n}function gu(r){var n=mi(hi()+"+"),n=vi(r,n),o=r.length;return tn(n,function(n){var e=n.start,t=n.finish,n=r.substring(e,t),e=0<e?z.some(r.charAt(e-1)):z.none(),t=t<o?z.some(r.charAt(t)):z.none();return function(n){n=lu(n);return du(n)}(au(n,e,t))})}function mu(i,n,e,t,c,f){for(var r=function(n){return!i.eq(n.item,e)||n.mode===lo&&!i.property().isText(n.item)&&0!==i.property().children(n.item).length},a=ui(t),o=z.some({item:n,mode:lo}).filter(r);o.isSome();)o.each(function(n){return e=a,t=c,r=f,o=n,u=(n=i).property().isElement(o.item)?z.from(n.attrs().get(o.item,"lang")):z.none(),void(n.property().isText(o.item)?e.addDetail(t(n,o.item)):n.property().isBoundary(o.item)?Iu.cata(r.assess(o.item),w,function(){return o.mode===lo?e.openBoundary(u,o.item):e.closeBoundary(u,o.item),z.some(o)},w):n.property().isEmptyTag(o.item)?e.addEmpty(o.item):o.mode===lo?e.openInline(u,o.item):e.closeInline(u,o.item));var e,t,r,o,u}),o=o.bind(function(n){return e=f,t=n,i.property().isBoundary(t.item)?Iu.cata(e.assess(t.item),function(){return t.mode!==Hr?z.some({item:t.item,mode:so}):z.none()},function(){return z.some(t)},function(){return z.none()}):z.some(t);var e,t}).filter(r).bind(function(n){return yo(i,n.item,n.mode,Kr().right())}).filter(r);return i.property().isText(e)&&a.addDetail(c(i,e)),a.done()}function pu(f,n,e,a,s,l){return e=Si(f,n,e).bind(function(n){if(0===n.length)return z.none();var e,t,r,o,u,i,c=n[0],n=n[n.length-1];return t=c,r=n,o=a,u=s,i=l,((e=f).eq(t,r)?z.some(t):e.property().parent(t)).map(function(n){n=ii(e,n).getOr(o);return mu(e,t,r,n,u,i)})}).getOr([]),{zones:tn(e,function(n){var e=n.details,t=n.lang,n=tn(e,function(n){return n.text}).join(""),e=tn(e,function(n){return n.item});return{lang:t,words:gu(n),elements:e}})}}function hu(n,e,t,r){var o,u,e=(e=Ti(o=n,u=e,u,f),i=e.isEmpty&&(u=u,!(i=o).property().isText(u)&&0===i.down().predicate(u,i.property().isText).length),{left:e.left,right:e.right,isEmpty:i}),i=Ei(e.left,e.right);return e.isEmpty?Ci():pu(n,e.left.item,e.right.item,t,i,r)}function vu(n,e,t,r,o,u){return 1===(u=pu(n,e,t,r,u,oi.anything()).zones).length?(u=u[0],o=o,u.lang===o?z.some(u):z.none()):z.none()}function yu(n,e,t,r,o){return vu(n,e,t,r,o,fi)}function wu(n,e,t,r){return n.property().isBoundary(e)?yu(n,e,e,t,r):n.property().isEmptyTag(e)?z.none():(o=n,u=e,t=ci(n=t,e=r),r=Ti(o,u,u,t),t=Ei(r.left,r.right),r.isEmpty?Ii(o,u,n,e):vu(o,r.left.item,r.right.item,n,e,t));var o,u}function bu(n){return M(n,function(n){return Cr(n,st)})}function xu(n){return z.from(Be(n,Bi))}function ku(n,e){return z.from(Be(n,e))}function Tu(r,n,e){var t=M(e,function(n){return[n.start,n.finish]}),o=Wu(n,t,function(n,e){return function(r,n,e){var t=r.property().getText(n),o=I(Vu(t,e),function(n){return 0<n.length});if(o.length<=1)return[Pr(n,0,t.length)];r.property().setText(n,o[0]);e=zu(o.slice(1),function(n,e){var t=r.create().text(n),n=Pr(t,e,e+n.length);return z.some(n)},o[0].length),t=tn(e,function(n){return n.element});return r.insert().afterAll(n,t),[Pr(n,0,o[0].length)].concat(e)}(r,n.element,e)});return tn(e,function(n){var e=qu(o,n.start,n.finish),t=tn(e,function(n){return n.element}),e=tn(t,r.property().getText).join("");return{elements:t,word:n.word,exact:e}})}function Su(t,n,o,e){return e=ni(t,n,e),M(e,function(n){var r,e=M(n,function(n){return n.fold(Gu,Gu,function(n){return[n]},Gu)}),n=tn(e,t.property().getText).join(""),n=yi(n,o),e=(r=t,zu(e,function(n,e){var t=e+r.property().getText(n).length;return z.from(Pr(n,e,t))}));return Tu(t,e,n)})}function Ou(n,e){var t,r=M(n.elements,function(n){return zi.marker(n,e).fold(l([]),function(n){return[n]})});return t=t=Se("mce-cram"),n=n,r=z.from(r[0]),{id:l(t),match:l(n),current:l(r)}}function _u(n,e){var u={};return rn(n,function(n){zi.marker(n,e.label()).filter(xr).each(function(o){e.detail(o).each(function(n){var e=n.id(),t=n.word(),r=n.lang(),n=zi.sameId(o);0<n.length&&(u[e]=(n=n,t=t,r=r,{id:l(e),elements:l(n),word:l(t),lang:l(r)}))})})}),u}function Eu(n,e,t,r){var o=Ri(n),r=Li(n,t,r);return o===e&&r.is(e)}function Cu(n){return zi.nonIgnoredMarker(n,Hi.label(),Hi.ignoreLabel())}var J=H([{aboveView:["item"]},{inView:["item"]},{belowView:["item"]}]),Iu=Kn(Kn({},J),{cata:function(n,e,t,r){return n.fold(e,t,r)}}),Au=function(u){function i(){return Fo(u)?u.getWin():window}function c(){if(Po(u)){var e=(u.inline?u.getBody():u.getContentAreaContainer()).getBoundingClientRect();return function(n){n=Bo(n);return Kn(Kn({},n),{top:n.top+e.top,bottom:n.bottom+e.top})}}return Bo}return{sections:function(){var n=u.getBody();if(!n)return[];var e=i(),t=c(),r=e.innerHeight,n=ct.fromDom(n),o=hr(n);return R(o,function(n){if(at(n)){n=t(n);return n.top<r&&0<n.bottom}}).map(function(e){var n=R(o.slice(e+1),function(n){if(at(n)){n=t(n);return!(n.top<r&&0<n.bottom)}}).map(function(n){return e+1+n}).getOr(o.length);return o.slice(e,n)}).getOr([n])},viewport:function(){var n=i(),r=n?n.innerHeight:0,o=c();return{assess:function(n){if(!at(n))return Iu.inView(n);var e,t=o(n);return(e=r,(t=t).bottom<0?Iu.aboveView:t.top>e?Iu.belowView:Iu.inView)(n)}}}}},Du={fireSpellcheckStart:function(n){return n.fire("SpellcheckStart")},fireSpellcheckEnd:function(n){return n.fire("SpellcheckEnd")},fireIgnore:function(n,e){return n.fire("SpellcheckerIgnore",{word:e})},fireIgnoreAll:function(n,e){return n.fire("SpellcheckerIgnoreAll",{word:e})},fireError:function(n,e){return n.fire("SpellcheckError",e)}},Ru=H([{single:["element"]},{range:["start","soffset","finish","foffset"]}]),Lu={cata:function(n,e,t){return n.fold(e,t)},single:Ru.single,range:function(n,e,t,r){var o=Fr(t,r,n,e),u=o?no(n,e):no(t,r),e=o?no(t,r):no(n,e);return Ru.range(u.element,u.offset,e.element,e.offset)}},Mu=function(e){var o=[];return{bind:function(n){if(void 0===n)throw new Error("Event bind error: undefined handler");o.push(n)},unbind:function(e){o=I(o,function(n){return n!==e})},trigger:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r={};rn(e,function(n,e){r[n]=t[e]}),rn(o,function(n){n(r)})}}},ju=function(n){return{registry:x(n,function(n){return{bind:n.bind,unbind:n.unbind}}),trigger:x(n,function(n){return n.trigger})}},Nu=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Pu=i,Fu=function(n){return 32===(n.which||n.keyCode)},Bu=function(o){function n(n){var e;Fu(n)&&(n=o.selection.getRng(),e=fo(ct.fromDom(n.startContainer),n.startOffset,ct.fromDom(n.endContainer),n.endOffset),i.runIfUnlocked(function(){return c.trigger.lookup(e.startContainer())}))}var e,t,r,u,i=(e=!1,{lock:function(){return e?z.none():(e=!0,z.some(void 0))},unlock:function(){if(!e)throw new Error("Double unlock");e=!1},runIfUnlocked:function(n){e||n()}}),c=ju({lookup:Mu(["element"]),scroll:Mu([])}),f=(t=function(){i.runIfUnlocked(function(){return c.trigger.scroll()})},r=1e3,u=null,{cancel:function(){null!==u&&(clearTimeout(u),u=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==u&&clearTimeout(u),u=setTimeout(function(){t.apply(null,n),u=null},r)}});return o.on("keyup",n),o.on("remove",function(){o.off("keyup",n)}),o.on("init",function(){function n(n){n.selectionChange||(n=ct.fromDom(n.element),mr(e,n)||(i.runIfUnlocked(function(){return c.trigger.lookup(e)}),e=n))}var e=ct.fromDom(o.getBody()),t=Fo(o)?o.getDoc():document,t=ct.fromDom(t),r=qo(t,"scroll",f.throttle);f.throttle(),o.on("nodechange",n),o.on("remove",function(){o.off("nodechange",n),r.unbind()})}),{events:c.registry,transact:function(n){i.lock().fold(function(){return console.warn("TinyMCE spellchecker state error")},function(){return n.get(function(){f.throttle(),i.unlock()})})}}},J=H([{before:["element"]},{after:["element"]},{rest:["element"]},{last:["element"]},{invalid:["element","offset"]}]),zu=(J.before,J.after,J.rest,J.last,J.invalid,function(n,t,e){return void 0===e&&(e=0),A(n,function(e,n){return t(n,e.len).fold(l(e),function(n){return{len:n.finish,list:e.list.concat([n])}})},{len:e,list:[]}).list}),Wu=function(n,e,t){return 0===e.length?n:M(n,function(r){var n=M(e,function(n){return(t=n)>=(e=r).start&&t<=e.finish?[n-r.start]:[];var e,t});return 0<n.length?Go(r,n,t):[r]})},qu=function(r,n,o){var n=$o(r,n),u=$o(r,o);return n.bind(function(n){var e,t,e=u.getOr((t=o,(e=r)[e.length-1]&&e[e.length-1].finish===t?e.length+1:-1));return-1<e?z.some(r.slice(n,e)):z.none()}).getOr([])},Vu=function(r,n){if(0===n.length)return[r];var e=A(n,function(n,e){if(0===e)return n;var t=r.substring(n.prev,e);return{prev:e,values:n.values.concat([t])}},{prev:0,values:[]}),n=n[n.length-1];return n<r.length?e.values.concat(r.substring(n)):e.values},J=H([{none:[]},{start:["element"]},{middle:["before","after"]},{end:["element"]}]),J=(J.none,J.start,J.middle,J.end,H([{include:["item"]},{excludeWith:["item"]},{excludeWithout:["item"]}])),Uu={include:J.include,excludeWith:J.excludeWith,excludeWithout:J.excludeWithout,cata:function(n,e,t,r){return n.fold(e,t,r)}},Hu=function(n,t){var r=[],o=[];return rn(n,function(n){var e=t(n);Uu.cata(e,function(){o.push(n)},function(){0<o.length&&r.push(o),r.push([n]),o=[]},function(){0<o.length&&r.push(o),o=[]})}),0<o.length&&r.push(o),r},J=H([{boundary:["item","universe"]},{empty:["item","universe"]},{text:["item","universe"]},{nonEditable:["item","universe"]}]),Ju=f,Ku=i,Xu=l(0),Yu=l(1),H=function(n){return Kn(Kn({},n),{isBoundary:function(){return n.fold(Ku,Ju,Ju,Ju)},toText:function(){return n.fold(z.none,z.none,function(n){return z.some(n)},z.none)},is:function(t){return n.fold(Ju,Ju,function(n,e){return e.eq(n,t)},Ju)},len:function(){return n.fold(Xu,Yu,function(n,e){return e.property().getText(n).length},Yu)}})},$u={text:u(H,J.text),boundary:u(H,J.boundary),empty:u(H,J.empty),nonEditable:u(H,J.empty),cata:function(n,e,t,r,o){return n.fold(e,t,r,o)}},Gu=l([]),Qu=function(e,n,t){if(e.property().isText(n))return[$u.text(n,e)];if(e.property().isEmptyTag(n))return[$u.empty(n,e)];if(e.property().isNonEditable(n))return[];if(e.property().isElement(n)){var r=e.property().children(n),o=e.property().isBoundary(n)?[$u.boundary(n,e)]:[],r=void 0!==t&&t(n)?[]:M(r,function(n){return Qu(e,n,t)});return o.concat(r).concat(o)}return[]},Zu=Qu,ni=function(e,n,t){n=M(n,function(n){return Zu(e,n,t)}),n=Hu(n,function(n){return n.match({boundary:function(){return Uu.excludeWithout(n)},empty:function(){return Uu.excludeWith(n)},text:function(){return Uu.include(n)},nonEditable:function(){return Uu.excludeWithout(n)}})});return I(n,function(n){return 0<n.length})},ei=function(e,t){return{element:t,wrap:function(n){e.insert().append(t,n)}}},ti=function(t,n,r){if(0===n.length)return n;n=I(n,function(n){return t.property().isText(n)&&0<t.property().getText(n).length});return tn(n,function(n){var e=r();return t.insert().before(n,e.element),e.wrap(n),e.element})},ri=Nr(),H={assess:Iu.inView},oi={anything:l(H)},ui=function(e){function r(n){n.each(function(n){c.push(n)})}function t(n){n.each(function(n){c=c.slice(0,c.length-1)})}function o(){0<f.length&&n.push({lang:a,details:f})}function u(n){o(),f=[],a=n}function i(n){return n.or(z.from(c[c.length-1])).getOr(e)}var c=[],n=[],f=[],a=e;return{openInline:function(n,e){var t=i(n);t!==a&&u(t),r(n)},closeInline:function(n,e){t(n)},addDetail:function(n){var e=i(z.none());e!==a&&u(e),f.push(n)},addEmpty:function(n){var e=i(z.none());u(e)},openBoundary:function(n,e){r(n);n=i(n);u(n)},closeBoundary:function(n,e){t(n);n=i(n);u(n)},done:function(){return o(),n.slice(0)}}},ii=Qo,ci=function(t,r){return function(n,e){e=Qo(n,e).getOr(t);return r!==e}},fi=eu,ai=function(n,e){return n.property().isText(e)?eu(n,e):Zo(e,0,0,"")},si=function(n,e,t,r){return(n.property().isBoundary(e)||n.property().isEmptyTag(e)||r(n,e)?tu:n.property().isText(e)?ou:ru)(n,e,t)},J="\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019",li=l("[^\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019]"),H=l("[\\w'\\-\\u0100-\\u017F\\u00C0-\\u00FF\ufeff\\u2018\\u2019]"),di=function(n){return function(n){n="((?:^'?)|(?:"+li()+"+'?))"+n+"((?:'?$)|(?:'?"+li()+"+))";return uu(n,function(n){return 1<n.length?n[1].length:0},function(n){return 2<n.length?n[2].length:0},z.none())}(n.replace(/[-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&"))},gi=uu,mi=function(n){return uu(n,l(0),l(0),z.none())},pi=li,hi=H,vi=iu,yi=function(t,n){return function(n){n=Array.prototype.slice.call(n,0);return n.sort(function(n,e){return n.start<e.start?-1:e.start<n.start?1:0}),n}(M(n,function(e){var n=iu(t,e.pattern);return tn(n,function(n){return Kn(Kn({},e),n)})}))},wi=new RegExp(pi()+"+","g"),bi=l(0),J=Kr(),H=J.left(),J=J.right(),xi={left:{sibling:H.sibling,first:H.first,slicer:function(e){return function(n){n=vi(n,gi(pi(),bi,bi,z.none()));return z.from(n[n.length-1]).map(function(n){return n.start})}(e).map(function(n){return[n+1,e.length]})}},right:{sibling:J.sibling,first:J.first,slicer:function(n){return function(n){n=n.search(wi);return-1<n?z.some(n):z.none()}(n).map(function(n){return[0,n]})}}},ki=function(t,n,e,r,o){e=yo(t,n,e,r).map(function(n){var e=si(t,n.item,r.slicer,o),n=e.abort?[]:ki(t,n.item,n.mode,r,o);return e.items.concat(n)}).getOr([]);return I(e,function(n){return 0<n.text.trim().length})},Ti=function(n,e,t,r){var o=ki(n,e,so,xi.left,r),r=ki(n,t,so,xi.right,r);return{left:0<o.length?o[o.length-1]:ai(n,e),right:0<r.length?r[r.length-1]:ai(n,t),isEmpty:0===o.length&&0===r.length}},Si=function(e,n,t){var r=function(e,n,t,r){void 0===r&&(r=f);var o=[n].concat(e.up().all(n)),n=[t].concat(e.up().all(t)),t=function(e){return R(e,r).fold(function(){return e},function(n){return e.slice(0,n+1)})},o=t(o),u=t(n),n=D(o,function(n){return C(u,cu(e,n))});return{firstpath:o,secondpath:u,shared:n}}(e,n,t);return r.shared.bind(function(n){return fu(e,n,r.firstpath,r.secondpath)})},Oi=["'","‘","’"],_i=M(Oi,function(e){return tn(["twas"],function(n){return e+n})}),Ei=function(t,r){return function(n,e){return n.eq(e,t.item)?t:n.eq(e,r.item)?r:fi(n,e)}},Ci=function(){return{zones:[]}},Ii=function(n,e,t,r){var o=(n.property().isText(e)?n.property().getText(e):n.property().children(e)).length,u=ro(n,e,0),o=ro(n,e,o);return yu(n,u.element,o.element,t,r)},Ai=Nr(),Di=bu,Ri=function(n){n=bu(n);return tn(n,function(n){return st(n)?Ir(n):""}).join("")},Li=function(n,e,t){return z.from(n[0]).bind(function(n){return wu(Ai,n,e,t).bind(function(n){n=n.words;return 1===n.length?z.some(n[0].word):z.none()})})},Mi=function(n){return M(n,hr)},ji=function(e,t){var r=Mi(e),o=Di(e);return z.from(e[0]).bind(function(n){Wt(n,r);n=z.from(o[0]);return n.each(function(n){Ar(n,t)}),rn(o.slice(1),function(n){Ar(n,"")}),rn(e,Ut),n})},H=function(n){var e=Ni(n);return{resolve:function(n){n=n.split(" ");return tn(n,function(n){return Pi(e,n)}).join(" ")}}},Ni=function(n){return n.replace(/\./g,"-")},Pi=function(n,e){return n+"-"+e},J=(H("ephox.flour"),{resolve:H("data.mce").resolve}),Fi=J.resolve("annotation"),Bi=J.resolve("highlight-id"),zi={nonIgnoredMarker:function(n,e,t){return At(n,"."+e+":not(."+t+")")},allNonIgnoredMarkers:function(n,e,t){return tn(n.dom.select("."+e+":not(."+t+")"),ct.fromDom)},marker:function(n,e){return At(n,"."+e)},id:xu,set:function(n,e){gt(n,Bi,e),Ko(n,e)},sameId:function(t){return xu(t).fold(l([t]),function(n){var e=yt(t);return Jt(e,"."+n)})},all:function(t){var n;return(n=t,z.from(Be(n,Fi))).fold(l([t]),function(e){var n=yt(t),n=Jt(n,"span["+Fi+"]");return I(n,function(n){return Be(n,Fi)===e})})},otherCurrents:function(n,e){n=yt(n);return Jt(n,"."+e)},label:l(Fi)},H={resolve:H("mce").resolve},Wi=J.resolve("lingo"),qi=H.resolve("spellchecker-annotation"),Vi=H.resolve("spellchecker-word"),J=H.resolve("spellchecker-ignore"),Ui=H.resolve("spellchecker-current"),Hi={nu:function(){var e,n=ct.fromTag("span");return e=n,rn([qi,Vi],function(n){Ko(e,n)}),gt(n,"aria-invalid","spelling"),ei(ri,n)},set:function(n,e,t){gt(n,"data-mce-bogus",1),gt(n,zi.label(),t),gt(n,Wi,e)},setCurrent:function(n){Ko(n,Ui)},unSetCurrent:function(n){Yo(n,Ui)},label:l(qi),wordLabel:l(Vi),ignoreLabel:l(J),currentLabel:l(Ui),detail:function(r){return zi.id(r).bind(function(t){return ku(r,zi.label()).bind(function(e){return ku(r,Wi).map(function(n){return{id:l(t),word:l(e),lang:l(n),element:l(r)}})})})}},Ji=function(t){return{fold:function(n,e){return e(t)}}},Ki=function(t){return{fold:function(n,e){return n(t)}}},Xi=H.resolve("wrap-disabled"),Yi=function(n){n=zi.sameId(n);rn(n,function(n){Yo(n,Xi)})},$i=function(n,e,t,r){t=tn(t,function(n){return{word:n,pattern:di(n)}});return Su(n,e,t,r)},Gi=Nr(),Qi=function(n,e,t){return $i(Gi,n,e,t)},Zi=function(n,e){return tn(n,function(n){return Ou(n,e)})},nc=function(t){return{fold:function(n,e){return e(t)}}},ec=function(t){return{fold:function(n,e){return n(t)}}},tc=function(n,e,t,r,o){n=function(n,e,t){n=T(n);return tn(n,function(n){return(n.lang()===t&&Eu(n.elements(),n.word(),e,n.lang())?nc:ec)(n)})}(_u(n.elements,e),o,n.lang);rn(n,function(n){function e(e){return function(n){rn(n.elements(),e)}}n.fold(e(r),e(t))})},rc={label:Hi.label,highlight:function(o,u,n,e){tc(u,Hi,Yi,Ht,o.lang());e=Qi(u.elements,n,e),e=Zi(e,Hi.label());return M(e,function(r){return r.current().fold(function(){var n=r.match(),e=n.word;if(o.ignore(n))return[];var t=I(n.elements,function(n){return(Ne(n)?z.some(n):bt(n).filter(Ne)).fold(f,function(n){return"false"!==n.dom.contentEditable})}),t=(n=t,t=Hi.nu,ti(ri,n,t)||[]);return rn(t,function(n){zi.set(n,r.id()),Hi.set(n,u.lang,e)}),0<t.length?[Ji(t)]:[]},function(n){return[Ki(n)]})})},marker:Cu,allMarkers:function(n){return zi.allNonIgnoredMarkers(n,Hi.label(),Hi.ignoreLabel())},substitute:function(n,e){n=Cu(n).fold(l([]),zi.sameId);return ji(n,e)},ignore:function(n){n=Cu(n).fold(l([]),zi.sameId);rn(n,function(n){Yo(n,Hi.wordLabel()),Ko(n,Hi.ignoreLabel())})},ignoreAll:function(n){n=Cu(n).fold(l([]),zi.all);rn(n,Ht)},detail:function(n){return Hi.detail(n)},setCurrent:function(n){var e=zi.otherCurrents(n,Hi.currentLabel());rn(e,Hi.unSetCurrent),Hi.setCurrent(n)}},oc=rc.label(),uc={isWrap:function(n){return e=oc,Uo(n=n)&&n.dom.classList.contains(e);var e}},ic=function(n,e){return{area:l(n),f:l(e)}};function cc(n,e,t,r){return n.property().isBoundary(e)?pu(n,e,e,t,fi,r):n.property().isEmptyTag(e)?sc():hu(n,e,t,r)}function fc(n,e,t,r,o,u,i){return t=ro(n,e,t),o=ro(n,r,o),n.eq(t.element,o.element)?cc(n,t.element,u,i):function(n,e,t,r,o){e=Ti(n,e,t,f),t=Ei(e.left,e.right);return pu(n,e.left.item,e.right.item,r,t,o)}(n,t.element,o.element,u,i)}var ac=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},sc=Ci,lc=Nr(),dc=function(n,e,t){return cc(lc,n,e,t)},gc=function(n,e,t,r,o,u){return fc(lc,n,e,t,r,o,u)},mc=function(){return sc()},pc=function(){return{assess:function(n){return Iu.inView(n)}}};function hc(n,c){function i(n,e,t,r,o,u){var i=(u?pc:c)();e(n,Lu.cata(t,function(n){return xr(n)?dc(n,f.get(),i):mc()},function(n,e,t,r){return xr(n)&&xr(t)?gc(n,e,t,r,f.get(),i):mc()}),f.get(),r,o)}var t,r,o,f=ac(n),a=(r=!(t=[]),o=function(){r=!0,z.from(t.shift()).fold(function(){r=!1},function(n){n.f()(n.area(),o)})},{enqueue:function(n,e){t.push(ic(n,e)),r||o()},destroy:function(){t=[]}});return{run:function(n,e,t,r,o,u){i(n,e,t,r,o,u)},setLang:f.set,schedule:function(e,r,n,o,u){a.enqueue(n,function(n,t){u()?t():i(e,r,n,function(n,e){u()?e():n&&t()},o,!1)})},destroy:function(){a.destroy()}}}function vc(n,e){return n.fold(f,function(n){return!n.ignore&&(n.suggestions.isSome()||!e)})}function yc(n){var e=new RegExp(wo,"g"),t=new RegExp("[‘’]","g");return n.replace(e,"").replace(t,"'")}function wc(n){return cn.error(new Error(n))}function bc(n,e){return I(n,function(n){return!E(e,n)})}function xc(r,e,o){return re.nu(function(t){var n=tn(r.words,function(n){return yc(n.word)}),n=bc(n,o);e(n,r.lang).get(function(n){return n.fold(function(n){n=wc(n);t(n)},function(n){var e,e=(e=r,n=n,cn.value({spelling:l(n),zone:l(e)}));t(e)})})})}function kc(e,r,o,u){return re.nu(function(t){var n=bc(e,u);o.correctionMany(n,r).get(function(n){return n.fold(function(n){n=wc(n);t(n)},function(n){var e;t((e=r,n=n,cn.value({spelling:l(n),lang:l(e)})))})})})}function Tc(n){return function(n,e){if(0===n.length)return[];for(var t=e(n[0]),r=[],o=[],u=0,i=n.length;u<i;u++){var c=n[u],f=e(c);f!==t&&(r.push(o),o=[]),t=f,o.push(c)}return 0!==o.length&&r.push(o),r}(function(n,e){n=Z.call(n,0);return n.sort(e),n}(n,function(n,e){return n.detail.lang()<e.detail.lang()?-1:e.detail.lang()<n.detail.lang()?1:0}),function(n){return n.detail.lang()})}var Sc={update:function(e,t,r){var n=W(r);rn(n,function(n){e.add(t,n,r[n])})},getInCache:function(e,t,n,r){return I(n,function(n){n=e.get(t,n.word);return vc(n,r)})},getInCacheString:function(e,t,n,r){return I(n,function(n){n=e.get(t,n);return vc(n,r)})}},Oc={highlight:function(n,e,t){var t={ignore:f,lang:l(t)},r=tn(e,function(n){return n.word}),e=I(r,function(n,e){return function(n,e){e=_(n,e);return-1===e?z.none():z.some(e)}(r,n).filter(function(n){return n===e}).isSome()});rc.highlight(t,n,e,l(!1))}},_c={scan:function(r,e,n,t,o){function u(n){var e=n.zone();Sc.update(r,e.lang,n.spelling());var n=W(n.spelling()),t=Sc.getInCacheString(r,e.lang,n,o),n=I(e.words,function(n){n=yc(n.word);return E(t,n)});return{zone:l(e),words:l(n)}}return tn(n,function(n){return xc(n,e,t).map(function(n){return n.map(u)})})},fetch:function(e,t,n,r){function o(n){Sc.update(e,n.lang(),n.spelling())}n=Tc(n),n=tn(n,function(n){var e=tn(n,function(n){return n.detail.word()});return kc(e,n[0].detail.lang(),t,r)});return Te(n).map(function(n){return rn(n,function(n){n.each(o)}),gn(n)})}},Ec=!0;function Cc(n,u,s,i){function c(n,e,t){n(function(){rn(e,function(n){Oc.highlight(n.zone(),n.words(),t)})})}function f(t,r,o,u){var i,c,f,a;0!==t.length?(i=0,c=new Map,f=ac(!1),a=function(){s.cancelCurrent(),f.set(!0)},rn(t,function(n,e){n.get(function(n){if(c.set(e,n),o)for(;c.has(i);)f.get()||u(c.get(i)),i+=1;else f.get()||u(n);f.get()||r(t.length===c.size,a)})}),r(!1,a)):r(!0,w)}return{local:function(n,e,t,r){e=e.zones,e=tn(e,function(n){var e=Sc.getInCache(u,n.lang,n.words,Ec);return{zone:l(n),words:l(e)}});c(n,e,t),r(!0,w)},remote:function(e,n,t,r,o){n=n.zones,n=_c.scan(u,s.correctionMany,n,i,Ec);f(n,r,o,function(n){return n.fold(w,function(n){return c(e,[n],t)})})},check:function(e,n,t,r,o){n=n.zones,n=_c.scan(u,s.check,n,i,!Ec);f(n,r,o,function(n){return n.fold(w,function(n){return c(e,[n],t)})})},fetch:function(n){return _c.fetch(u,s,n,i)}}}function Ic(n,e){var t=(t=-1<(t=(n=(t=n).toLowerCase().replace("-","_")).indexOf(";"))?n.substring(0,t):n,n=Ke(t),t=-1<(t=n.indexOf("_"))?n.substring(0,t):n,{full:n,languageCode:t});return e[t.full]||e[t.languageCode]||""}function Ac(n){return function(n,e){n=n.split(","),n=tn(n,function(n){return Ic(n,e)});return D(n,function(n){return""!==n})}(n,Pn({zh_hk:"zh_tw",iw:"he",no:"nb",nn:"nb",en:"en",ar:"ar",ca:"ca",cs:"cs",da:"da",de:"de",el:"el",es:"es",fa:"fa",fi:"fi",fr:"fr",he:"he",hr:"hr",hu:"hu",it:"it",ja:"ja",kk:"kk",ko:"ko",nl:"nl",nb:"nb",pl:"pl",pt:"pt",pt_pt:"pt_pt",ro:"ro",ru:"ru",sl:"sl",sk:"sk",sv:"sv",th:"th",tr:"tr",uk:"uk",zh:"zh",zh_tw:"zh_tw"},Lc)).filter(function(n){return E(Mc,n)})}function Dc(n,e){var t;return(Je(t=n,n="/")?t:t+n)+e}var Rc="no.language",Lc={en_au:"en_gb",en_bz:"en_gb",en_ca:"en_gb",en_gb:"en_gb",en_ie:"en_gb",en_in:"en_gb",en_jm:"en_gb",en_my:"en_gb",en_nz:"en_gb",en_ph:"en_gb",en_sg:"en_gb",en_tt:"en_gb",en_za:"en_gb",en_zw:"en_gb",en_us:"en_us",en:"en_us"},Mc=["da","de","en","en_au","en_br","en_gb","en_uk","en_us","es","fi","fr","it","nb","nl","pt","pt_pt","sv"],jc={determine:function(n){return n===Rc?z.some(n):Ac(n)},isOn:function(n){return n!==Rc},offKey:l(Rc)},Nc={consoleMessage:function(n){if(G((e=n).message)&&G(e.status)&&G(e.responseText)){e=n.responseText();return n.message()+". Response code "+n.status()+(0===e.length?".":". Error details: "+e)}return n;var e}},Pc=function(n){var t=z.none(),e=[],r=function(n){o()?i(n):e.push(n)},o=function(){return t.isSome()},u=function(n){rn(n,i)},i=function(e){t.each(function(n){setTimeout(function(){e(n)},0)})};return n(function(n){o()||(t=z.some(n),u(e),e=[])}),{get:r,getSync:function(){return t.fold(function(){return cn.error(re.nu(r))},cn.value)},map:function(t){return Pc(function(e){r(function(n){e(t(n))})})},isReady:o}},Fc={nu:Pc,pure:function(e){return Pc(function(n){n(e)})}},Bc={get:function(n){n=Or.getApiKey(n);return null===n?{}:{"tiny-api-key":n}}},zc={spelling:l("1")},Wc={corsQuery:function(n){return Dc(n,"version")},spelling:function(n){return Dc(n,zc.spelling())+"/"}};function qc(n,t,o,u,e){function i(e){return function(n){return console.error(e,Nc.consoleMessage(n)),f.trigger.failed(t),n}}var r=Wc.spelling(t),c=ut(r+"correct",r+"correction",Bc.get(n),void 0,e),f=ju({failed:Mu(["resource"])}),a=Fc.nu(function(n){c.checkMany(o,["startup"]).get(n)});a.get(function(n){return n.mapError(i("Unable to communicate with the spelling service:"))});function s(t,r){function e(n){var e=u?t:o;return n.fold(function(n){return re.pure(cn.error(n))},function(){return r(e).mapError(i("Error response from the spelling service:"))})}return a.getSync().fold(function(n){return n.bind(e)},e)}return{check:function(e,n){return s(n,function(n){return c.checkMany(n,e)})},correction:function(e,n){return s(n,function(n){return c.correctOne(n,e)})},correctionMany:function(e,n){return s(n,function(n){return c.correctMany(n,e)})},events:f.registry,state:function(){return c.dumpCache()},cancelCurrent:c.cancelCurrent}}function Vc(e){var u=e.gaol,t=e.editor,n=e.language,r=z.from(Or.getHandler(t)).getOrThunk(function(){return qc(t,e.url,n,e.multilingual,e.serverSettings)}),o=ju({failed:Mu(["resource"])});r.events.failed.bind(function(n){o.trigger.failed(n.resource)});function i(){return!jc.isOn(u.spelling().get())}var c=hc(n,e.viewable.viewport),f=Cc(0,e.cache,r,Or.getWhitelist(t)),a=function(){!function(n){n=t.dom.select("span."+n);rn(n,function(n){!function(n){var e=n.parentNode,t=n.childNodes;if(e){for(;0<t.length;)e.insertBefore(t[0],n);e.removeChild(n)}}(n)})}("mce-spellchecker-word")};return e.editor.on("remove",function(){c.destroy()}),{run:function(n,e){var t=u.spelling().get();jc.isOn(t)&&(c.setLang(t),c.run(n,f.local,e,w,!1,!1),c.schedule(n,f.remote,e,!1,i))},fetch:f.fetch,check:function(n,e,t,r,o){c.setLang(u.nonOffSpelling()),c.run(n,f.check,e,t,r,o)},refresh:function(){var n=u.spelling().get();jc.isOn(n)&&e.asYouType||a()},clear:a,state:r.state,events:o.registry}}function Uc(r,e,t,n,o,u){var i=Au(r),c=No(r),f=Vc({editor:r,gaol:n,viewable:i,cache:t,url:e,language:n.spelling().get(),multilingual:!0,asYouType:o,serverSettings:u});f.events.failed.bind(function(){var n=r.translate(["The spelling service was not found: ({0})",e]);Du.fireError(r,{message:n}),console.error(n)}),r.on("init",function(){var n=r.getBody(),n=ct.fromDom(n);gt(n,"spellcheck",!1),f.refresh()}),r.on("remove",function(){var n=r.getBody(),n=ct.fromDom(n);mt(n,"spellcheck"),t.destroy()});function a(n,e,t,r){return e=(uc.isWrap(n)?uo:no)(n,e),r=(uc.isWrap(t)?uo:no)(t,r),Lu.range(e.element,e.offset,r.element,r.offset)}function s(n){var e=n[0],t=n[n.length-1],n=vr(t);return a(e,0,t,n)}function l(){var n=i.sections();0<n.length&&(n=s(n),f.run(c.transaction,n))}n=f.clear,u=f.fetch,o=o?z.some(Bu(r)):z.none();o.each(function(n){n.events.lookup.bind(function(n){n=Lu.single(n.element);f.run(c.transaction,n)}),n.events.scroll.bind(l)});o=o.fold(function(){return function(n){return n.get(w)}},function(n){return n.transact});return{scanView:l,scanRange:function(n,e,t,r){r=a(n,e,t,r);f.run(c.transaction,r)},checkDocument:function(n,e){var t=ct.fromDom(r.getBody()),t=hr(t);0<t.length&&(t=s(t),f.check(c.transaction,t,n,e,!0))},fetch:u,clear:n,transact:o}}function Hc(n){return"img"===Le(n)?1:(e=n,Mr.getOption(e).fold(function(){return hr(n).length},function(n){return n.length}));var e}function Jc(n,t){return rc.marker(n).bind(rc.detail).map(function(n){var e=t.suggestions(n.lang(),n.word());return{detail:n,suggestions:e}})}function Kc(r,o){function u(n,e){return{text:e,onAction:function(){ff(r,n,e)}}}var i={getSubstituteItemsOverMax:function(e,n,t){var r=n.slice(0,t),t=n.slice(t),r=tn(r,function(n){return u(e,n)}),o=tn(t,function(n){return u(e,n)}),t={type:"submenu",text:"More...",getSubmenuItems:function(){return o}};return r.concat([t])},getSubstituteItems:function(e,n){return tn(n,function(n){return u(e,n)})},getSeparator:function(){return{type:"separator"}},getIgnoreItem:function(n,e){return{text:"Ignore",onAction:function(){sf(r,n,e)}}},getIgnoreAllItem:function(n,e,t){return{text:"Ignore all",onAction:function(){lf(r,n,e,t,o)}}}};r.ui.registry.addContextMenu("spellchecker",{update:function(n){var t,n=ct.fromDom(n);return af(t=n,o).bind(function(e){return e.suggestions.map(function(n){return df(t,e.detail.lang(),e.detail.word(),n,i)})}).getOr([])}})}function Xc(o,n,t,r,e){var u,i,c=tn(n,function(n){return Kn(Kn({},n),{type:"choiceitem"})}),f={tooltip:"Spellcheck...",icon:"spell-check",onAction:function(){o.execCommand("mceSpellcheckDocument")}},a={tooltip:"Spellcheck...",icon:"spell-check",onAction:function(){o.execCommand("mceSpellcheckDocument")},select:function(n){return n===t.spelling().get()},fetch:function(n){n(c)},onItemAction:function(n,e){r.changeLanguage(e)}},s={tooltip:"Spellcheck",icon:"spell-check",onAction:r.toggleLanguage,onSetup:function(n){function e(){n.setActive(jc.isOn(t.spelling().get()))}return e(),o.on(yf,e),function(){return o.off(yf,e)}},select:function(n){return n===t.spelling().get()},fetch:function(n){n(c)},onItemAction:function(n,e){r.changeLanguage(e)}},n={text:"Spellcheck...",icon:"spell-check",onAction:function(){o.execCommand("mceSpellcheckDocument")}};function l(n){return{type:"togglemenuitem",text:n.text,onAction:function(){return r.changeLanguage(n.value)},active:n.value===t.spelling().get()}}e?(o.ui.registry.addSplitButton("spellchecker",s),o.ui.registry.addToggleMenuItem("spellchecker",{text:"Spellcheck",icon:"spell-check",onAction:r.toggleLanguage,onSetup:function(n){function e(){return n.setActive(jc.isOn(t.spelling().get()))}return e(),o.on(yf,e),function(){return o.off(yf,e)}}}),o.ui.registry.addButton("spellcheckdialog",f),o.ui.registry.addMenuItem("spellcheckdialog",n)):(o.ui.registry.addSplitButton("spellchecker",a),o.ui.registry.addMenuItem("spellchecker",n)),o.ui.registry.addNestedMenuItem("spellcheckerlanguage",{text:"Spellcheck Language",getSubmenuItems:function(){return tn(c,l)}}),a=tinymce,n="5.0.3",a&&1===g(m(a),d(n))&&(n=Or.getSelectContentLanguages(o),i={},rn(n.split(","),function(n){n=n.toLowerCase(),i[n]=U(kr,n)?kr[n]:n.toUpperCase()}),u=i,o.ui.registry.addMenuButton("language",{tooltip:"Language",icon:"translate",fetch:function(n){var e,t,r=(e=o,t=u,At(ct.fromDom(e.selection.getNode()),"[lang]").map(function(n){n=Be(n,"lang").toLowerCase();return U(t,n)||(t[n]=U(kr,n)?kr[n]:n.toUpperCase()),n}));n(k(u,function(n,t){return{type:"togglemenuitem",value:t,text:n,onSetup:function(n){return r.is(t)&&n.setActive(!0),w},onAction:function(){var n,e;e=t,(n=o).formatter.has("powerspell_language_select")||n.formatter.register("powerspell_language_select",{inline:"span",attributes:{lang:"%value"}}),n.undoManager.transact(function(){n.focus(),n.formatter.toggle("powerspell_language_select",{value:e}),n.nodeChanged()})}}}))},onSetup:function(e){function n(n){e.setActive(function(n){n=n.parents;return C(n,function(n){n=ct.fromDom(n);return at(n)&&ze(n,"lang").exists(function(n){return""!==n})})}(n))}return o.on("NodeChange",n),function(){o.off("NodeChange",n)}}}))}function Yc(){var e=ac(z.none());return{clear:function(){return e.set(z.none())},set:function(n){return e.set(z.some(n))},isSet:function(){return e.get().isSome()},on:function(n){return e.get().each(n)}}}function $c(t,n,r,e){return{title:"Spellcheck",body:{type:"panel",items:[{type:"input",name:"textlabel",label:"Misspelled word"},{type:"selectbox",name:"suggestions",label:"Suggestions",size:6,items:n}]},initialData:{textlabel:r,suggestions:0<e.length?e[0]:""},onClose:t.onClose,onAction:function(n,e){switch(e.name){case"change":t.replace(n,r);break;case"ignore":t.ignore(n);break;case"ignoreall":t.ignoreAll(n)}n.focus("textlabel")},buttons:[{type:"custom",primary:!0,name:"change",text:"Change"},{type:"custom",name:"ignore",text:"Ignore"},{type:"custom",name:"ignoreall",text:"Ignore all"}]}}function Gc(n,e,t){var r=tn(e,function(n){return{text:n,value:n}});return $c(n,r,t.word(),e)}function Qc(o,u,t,n,i){function c(n,e,t){function r(){n.focus("textlabel")}void 0===t&&(t=0),n.redial(e),0<t?setTimeout(r,t):r()}function r(t,r){s.get()||(o.selection.scrollIntoView(t.detail.element().dom),t.suggestions.fold(function(){var e,n=re.nu(function(e){i.getWordsSuggestionsAsync([t],function(){return e(z.none())},function(n){return e(z.some(n))},0)}).map(function(n){return n.map(function(n){return Gc(d,n,t.detail)})});n=n,(e=r).block("Finding word suggestions"),n.get(function(n){e.unblock(),n.fold(function(){console.log("TinyMCE spellchecker fetch error")},function(n){c(e,n)})})},function(n){n=Gc(d,n,t.detail);c(r,n,a)}))}function e(){return re.nu(function(n){t.clear(),s.set(!1),m.set(!0);var e=$c(d,[],"",[]),e=o.windowManager.open(e,f);p(e),n()}).bind(function(){return re.nu(function(n){return setTimeout(n)})}).bind(function(){return re.nu(function(r){i.getWordsAsyncWithProgress(function(n,e){m.set(!n),l.set(function(){n||e(),r()});var t=pf(o),t=gf(t,u),t=I(t,function(n){return n.suggestions.isNone()});i.getWordsSuggestionsAsync(t,w,w,0);t=mf(o);(n||t.isSome())&&(g.get()(),g.set(w))},!0)})})}var f=v(tinymce,"5.6.0")?{}:{inline:"toolbar"},a=gr().browser.isIE()?250:0,s=ac(!0),l=Yc(),d={replace:function(n,e){var t=n.getData(),r=t.textlabel!==e?t.textlabel:t.suggestions;mf(o).each(function(n){return ff(o,n.element(),r)}),p(n)},ignore:function(n){mf(o).each(function(n){return sf(o,n.element(),n.word())}),p(n)},ignoreAll:function(n){mf(o).each(function(n){return lf(o,n.element(),n.word(),n.lang(),u)}),p(n)},onClose:function(){l.on(b),l.clear(),s.set(!0),hf(o,t,n)}},g=ac(w),m=ac(!1),p=function(e){mf(o).bind(function(n){return af(n.element(),u)}).fold(function(){var n;m.get()?(e.block("Finding word suggestions"),g.set(function(){e.unblock(),p(e)})):(n=o.translate("No misspellings found."),o.notificationManager.open({text:n,type:"info",timeout:3e3}),e.close())},function(n){r(n,e)})},h={open:function(){return t.transact(e())}};return wf(o,h),1}function Zc(u,r){function o(n){var t;return af(t=n,r).bind(function(e){return e.suggestions.map(function(n){return df(t,e.detail.lang(),e.detail.word(),n,c)})}).getOr([])}var i=function(r,n){return tn(n,function(n){return e=r,{text:t=n,onclick:function(){ff(u,e,t)}};var e,t})},c={getSubstituteItemsOverMax:function(n,e,t){t=e.slice(0,t);return i(n,t)},getSubstituteItems:i,getSeparator:function(){return{text:"-"}},getIgnoreItem:function(n,e){return{text:"Ignore",onclick:function(){sf(u,n,e)}}},getIgnoreAllItem:function(n,e,t){return{text:"Ignore all",onclick:function(){lf(u,n,e,t,r)}}}};u.on("contextmenu",function(n){var e=ct.fromDom(n.target),t=o(e);0<t.length&&(n.preventDefault(),n.stopImmediatePropagation(),function(n,e){var t=tinymce.ui.Factory.create("menu",{items:e,context:"contextmenu",onautohide:function(n){-1!==n.target.className.indexOf("spellchecker")&&n.preventDefault()},onhide:function(){t.remove()}});t.renderTo(document.body);var r=bf.DOM.getPos(u.getContentAreaContainer()),o=u.dom.getPos(n.dom),e=u.dom.getRoot();"BODY"===e.nodeName?(o.x-=e.ownerDocument.documentElement.scrollLeft||e.scrollLeft,o.y-=e.ownerDocument.documentElement.scrollTop||e.scrollTop):(o.x-=e.scrollLeft,o.y-=e.scrollTop),r.x+=o.x,r.y+=o.y,t.moveTo(r.x,r.y+n.dom.offsetHeight)}(e,t))},!0)}function nf(t,n,r,e,o){function u(n){var e=r.spelling().get();rn(n.control.items(),function(n){n.active(n.data.data===e)})}var i=tn(n,function(n){return{selectable:!0,text:n.text,data:n.value,onclick:function(){e.changeLanguage(n.value)}}}),c={icon:"spellchecker",type:"button",tooltip:"Spellcheck...",onclick:function(){t.execCommand("mceSpellcheckDocument")}},f=Kn(Kn({},c),{type:"splitbutton",menu:i,onshow:u}),a={type:"splitbutton",tooltip:"Spellcheck",menu:i,onclick:e.toggleLanguage,onPostRender:function(n){function e(){n.control.active(jc.isOn(r.spelling().get()))}e(),t.on(xf,e)},onshow:u},n={text:"Spellcheck...",context:"tools",onclick:function(){t.execCommand("mceSpellcheckDocument")}};o?(t.addButton("spellchecker",a),t.addMenuItem("spellchecker",{text:"Spellcheck",context:"tools",onclick:e.toggleLanguage,onPostRender:function(n){function e(){return n.control.active(jc.isOn(r.spelling().get()))}e(),t.on(xf,e)},selectable:!0}),t.addButton("spellcheckdialog",c),t.addMenuItem("spellcheckdialog",n)):(t.addButton("spellchecker",f),t.addMenuItem("spellchecker",n)),t.addMenuItem("spellcheckerlanguage",{text:"Spellcheck Language",context:"tools",menu:i,onPostRender:function(n){n.control.on("show",function(n){u(n)})}})}function ef(o,u,n,e,r){function i(){var n=o.translate("No misspellings found.");o.notificationManager.open({text:n,type:"info",timeout:3e3})}function c(e,t){h.on(function(n){n.find("#text")[0].value(e);n=n.find("#suggestions")[0];n.options(t),n.getEl().selectedIndex=0,n.getEl().focus()})}function f(n){var e=n.find("#text")[0],t=n.find("#change")[0],r=n.find("#ignore")[0],n=n.find("#ignoreall")[0];e.disabled(!1),t.disabled(!1),r.disabled(!1),n.disabled(!1)}function a(n){h.on(function(n){var e,t,r,o;t=(e=n).find("#text")[0],r=e.find("#suggestions")[0],o=e.find("#change")[0],n=e.find("#ignore")[0],e=e.find("#ignoreall")[0],t.value("Finding word suggestions"),t.disabled(!0),r.options([]),o.disabled(!0),n.disabled(!0),e.disabled(!0)});var n=gf(n,u),t=I(n,function(n){return n.suggestions.isNone()});r.getWordsSuggestionsAsync(t,function(){h.on(function(n){f(n)})},function(n){h.on(function(n){f(n)});var e=t[0].detail;o.selection.scrollIntoView(e.element().dom),c(e.word(),n)},0)}function s(n){n=gf(n,u),0<I(n,function(n){return n.suggestions.isNone()}).length&&r.getWordsSuggestionsAsync(n,w,w,0)}function l(n){var t,e=pf(o);0<e.length?af((t=e)[0],u).each(function(e){e.suggestions.fold(function(){a(t)},function(n){o.selection.scrollIntoView(e.detail.element().dom),c(e.detail.word(),n),s(t)})}):(i(),n.close())}function d(n){h.on(function(n){n.close()})}function g(n,e,t){return Math.min(Math.max(n,e),t)}function m(n){var e=o.getBody(),t=o.inline?o.getBody():o.getContentAreaContainer(),e=("BODY"===(e=e).nodeName&&(e=e.parentNode),e.scrollHeight>e.clientHeight?-25:-10);n.moveRel(t,"tr-tr"),n.moveBy(e,10),t=10,n=(e=n).layoutRect(),e.moveTo(g(n.x,0,window.innerWidth-n.w),g(n.y,t,window.innerHeight-n.h-t))}function p(n,e){var t,e=tinymce.ui.Factory.create("window",{layout:"flex",align:"stretch",direction:"column",title:"Spellcheck",spacing:10,padding:10,callbacks:t=v,minWidth:350,items:[{type:"label",name:"textlabel",text:"Misspelled word"},{type:"textbox",name:"text",ariaLabel:"Misspelled word",spellcheck:!1,value:n},{type:"label",name:"suggestionslabel",text:"Suggestions"},{type:"container",layout:"flex",direction:"row",align:"stretch",spacing:10,items:[{type:"selectbox",name:"suggestions",ariaLabel:"Suggestion",minWidth:150,flex:1,size:6,border:1,options:e},{type:"container",layout:"flex",flex:1,spacing:5,direction:"column",pack:"center",align:"stretch",items:[{type:"button",subtype:"primary",name:"change",text:"Change",onclick:"change"},{type:"button",name:"ignore",text:"Ignore",onclick:"ignore"},{type:"button",name:"ignoreall",text:"Ignore all",onclick:"ignoreall"}]}]}],onsubmit:function(n){n.preventDefault(),t.replace()},onclose:function(n){t.onClose()},buttons:[]});return h.set(e),e.modal=!1,e.on("open",function(){return h.on(m)}),o.on("remove",d),e.renderTo().reflow(),e.moveTo(0,0),e}var h=Yc(),t=Yc(),v={change:y=function(n){h.on(function(r){mf(o).each(function(n){var e=r.find("#text")[0],t=r.find("#suggestions")[0],e=e.value(),t=t.getEl().value,e=e===n.word()?t:e;ff(o,n.element(),e),l(r)})})},submit:y,ignore:function(n){h.on(function(n){mf(o).each(function(n){sf(o,n.element(),n.word())}),l(n)})},ignoreall:function(n){h.on(function(n){mf(o).each(function(n){lf(o,n.element(),n.word(),n.lang(),u)}),l(n)})},close:d,onClose:function(){hf(o,n,e),h.clear(),t.on(b)}},y={open:function(){return n.transact(re.nu(function(e){n.clear(),r.getWordsAsync(function(){var r,n=pf(o);0<n.length?(t.set(e),n=(r=n)[0],af(n,u).each(function(t){t.suggestions.fold(function(){p(t.detail.word(),[]),a(r)},function(n){var e=p(t.detail.word(),n).find("#suggestions")[0];e.options(n),e.getEl().selectedIndex=0,e.getEl().focus(),s(r)})})):(e(),i())})}))}};return o.on("ResizeWindow",function(){kf.requestAnimationFrame(function(){h.on(m)})}),wf(o,y),1}function tf(n,e){return{suggestions:n,ignore:e}}function rf(n){function r(n){return c.hasOwnProperty(n)&&void 0!==c[n]?c[n]:{}}function e(n,e){var t=r(n);e(t),c[n]=t}function o(n,e){return e(r(n))}function u(n,r,o){e(n,function(n){var e,t=yc(r);U(n,t)&&!n[t].suggestions.isNone()||(e=0<o.length?z.some(o):z.none(),n[t]=tf(e,!1))})}function i(n,r){e(n,function(n){var e=yc(r),t=n.hasOwnProperty(e)?n[e]:void 0,t=void 0!==t?t.suggestions:z.none(),t=tf(t,!0);n[e]=t})}void 0===n&&(n={});var c={};return y(n,function(n,t){y(n,function(n,e){!0===n.ignore&&i(t,e),void 0!==n.suggestions&&u(t,e,n.suggestions)})}),{add:u,get:function(n,t){return o(n,function(n){var e=yc(t);return n.hasOwnProperty(e)?z.from(n[e]):z.none()})},ignore:i,suggestions:function(n,t){return o(n,function(n){var e=yc(t);return(n.hasOwnProperty(e)?z.from(n[e]):z.none()).bind(function(n){return n.ignore?z.none():n.suggestions})})},state:function(){return x(c,function(n,e){return x(n,function(n,e){return{ignore:n.ignore,suggestions:n.suggestions.getOr([])}})})},destroy:function(){c={}}}}function of(n,e){return(n=n,z.from(Tf.getItem(n)).map(JSON.parse)).getOr(e)}function uf(n){return of(n,{})}function cf(n,e){e=JSON.stringify(e),Tf.setItem(n,e)}var ff=function(t,n,e){t.undoManager.transact(function(){rc.substitute(n,e).each(function(n){var e=Hc(n);t.selection.setCursorLocation(n.dom,e),t.focus()})})},af=Jc,sf=function(n,e,t){n.undoManager.transact(function(){rc.ignore(e)}),Du.fireIgnore(n,t)},lf=function(n,e,t,r,o){o.ignore(r,t),rc.ignoreAll(e),Du.fireIgnoreAll(n,t)},df=function(n,e,t,r,o){r=4<r.length?o.getSubstituteItemsOverMax(n,r,4):o.getSubstituteItems(n,r),e=[o.getSeparator(),o.getIgnoreItem(n,t),o.getIgnoreAllItem(n,t,e)];return r.concat(e)},gf=function(n,e,t){void 0===t&&(t=100);var r,t=n.length>t?t:n.length,t=n.slice(0,t),t=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e}(tn(t,function(n){return Jc(n,e)}));return r={},rn(t,function(n){r[n.detail.word()]=n}),T(r)},mf=function(n){n=rc.allMarkers(n);return P(n).bind(rc.detail)},pf=function(n){return rc.allMarkers(n)},hf=function(n,e,t){e.clear(),t&&!n.removed&&e.scanView()},vf=function(e,t,u,i,r){function o(n){((n=jc.isOn(n))?u.scanView:u.clear)(),(n?Du.fireSpellcheckStart:Du.fireSpellcheckEnd)(e)}var n=function(n,e){u.checkDocument(n,e)},c=function(e,n,t,r){function o(){r<3?c(e,n,t,r+1):n()}u.fetch(e).get(function(n){0<n.values.length?Jc(e[0].detail.element(),i).fold(function(){o()},function(n){n.suggestions.fold(function(){o()},function(n){t(n)})}):o()})};return{changeLanguage:function(n){t.spelling().setAndCache(e,n),r&&o(n)},toggleLanguage:function(){var n=jc.isOn(t.spelling().get())?jc.offKey():t.nonOffSpelling();t.spelling().setAndCache(e,n),o(n)},getWordsAsync:function(e){n(function(n){n&&e()},!1)},getWordsAsyncWithProgress:n,getWordsSuggestionsAsync:c}},yf="SpellcheckStart SpellcheckEnd",wf=function(n,e){n.addCommand("mceSpellcheckDocument",e.open)},bf=tinymce.dom.DOMUtils,xf="SpellcheckStart SpellcheckEnd",kf=tinymce.util.Delay,Tf=function(){try{var n=He.localStorage,e="__storage_test__";return n.setItem(e,e),n.removeItem(e),z.some(n)}catch(n){return z.none()}}().fold(function(){var t={};return{getItem:function(n){return t[n]},setItem:function(n,e){t[n]=e}}},function(t){return{getItem:function(n){return t.getItem("mce."+n)},setItem:function(n,e){t.setItem("mce."+n,e)}}}),Sf={read:uf,readOr:of,write:cf,access:function(n){return{read:c(uf,n),write:c(cf,n)}}};tinymce.PluginManager.requireLangPack("tinymcespellchecker","ar,bg_BG,ca,cs,da,de,el,es,eu,fa,fi,fr_FR,he_IL,hr,hu_HU,id,it,ja,kk,ko_KR,nb_NO,nl,pl,pt_BR,pt_PT,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,zh_CN,zh_TW"),tinymce.PluginManager.add("tinymcespellchecker",function(n,e){if(v(tinymce,"4.2.8"))return console.error('The "tinymcespellchecker" plugin requires at least version 4.2.8 of TinyMCE.'),function(){};var t,r,o,u=Or.getLanguage(n),i=Or.getRpcUrl(n),c=rf({}),f=(s=u,a=Sf.readOr("spelling",s),s=ac(a),t=ac("no.language"===a?"en_us":a),r=s.set,s=s.get,o={setAndCache:function(n,e){Or.setLanguage(n,e),r(e),Sf.write("spelling",e),jc.isOn(e)&&t.set(e)},set:r,get:s},{spelling:function(){return o},nonOffSpelling:t.get});Or.isActive(n)||"no.language"===f.spelling().get()||f.spelling().set("no.language");var u=Or.getLanguages(n),a=!Or.useDialog(n),s=Uc(n,i,c,f,a,Or.getSpellingServiceSettings(n)),i=vf(n,f,s,c,a);v(tinymce,"5.0.0")?(Zc(n,c),nf(n,u,f,i,a),ef(n,c,s,a,i)):(Kc(n,c),Xc(n,u,f,i,a),Qc(n,c,s,a,i))})}();