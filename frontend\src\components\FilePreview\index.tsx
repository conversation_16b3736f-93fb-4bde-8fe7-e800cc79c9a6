/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 16:24:06
 * @LastEditTime: 2023-09-14 16:24:07
 * @FilePath: \frontend\src\pages\Translate\components\Content\index.tsx
 * @Description:
 */
import Access from '@/components/Access';
import Word from '@/components/Word';
import {
  getBlobTypeToSuffix,
  txtToString,
} from '@/utils/utils';
import React, { useEffect, useMemo, useState } from 'react';
import PDF from '../PDF';
// 传入blob进行渲染
const Content: React.FC<ContentProps> = ({ blob }) => {
  const suffix = getBlobTypeToSuffix(blob);
  const [text, setText] = useState('');
  const [err, setErr] = useState(false);
  useEffect(() => {
    if (suffix === 'txt' && blob) {
      (async () => {
        const str = await txtToString(blob);
        setText(str);
      })();
    } else {
      setText('');
    }
  }, [blob]);
  return (
    <div>
      <Access accessible={suffix === 'txt'}>
        <p
          style={{
            whiteSpace: 'pre-line',
            fontSize: '16px',
            lineHeight: '24px',
          }}
          dangerouslySetInnerHTML={{ __html: text }}></p>
      </Access>
      <Access accessible={suffix === 'docx'}>
        <Word setErr={setErr} blob={blob} />
      </Access>
      <Access accessible={suffix === 'pdf'}>
        <PDF setErr={setErr} blob={blob} />
      </Access>
    </div>
  );
};

interface ContentProps {
  blob?: Blob;
}
export default Content;
Content.displayName = 'Content';
