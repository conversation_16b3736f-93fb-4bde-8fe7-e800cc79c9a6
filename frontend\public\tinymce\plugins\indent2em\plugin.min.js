/**
 * indent2em (Enhancement 1.2v) 2021-01-12
 * The tinymce-plugins is used to set the first line indent (Enhancement)
 * 
 * https://github.com/Five-great/tinymce-plugins
 * 
 * Copyright 2020, Five(<PERSON>) The Chengdu, China https://www.fivecc.cn/
 *
 * Licensed under MIT
 */
tinymce.PluginManager.add("indent2em",function(d,n){function l(c,f){return(c=f.match(new RegExp(c+':?(.+?)"?[;}]')))?c[1]:!1}var m=tinymce.util.Tools.resolve("tinymce.util.Tools"),h=d.getParam("indent2em_val","2em"),k=function(){var c=d.dom,f=d.selection.getSelectedBlocks(),g="";m.each(f,function(b){var a="",e="";b.attributes.style?e=l("letter-spacing",b.attributes.style.textContent):"";b&&b.children["0"]&&b.children["0"].attributes&&b.children["0"].attributes.style?a=(a=l("font-size",b.children["0"].attributes.style.textContent))?
2*(parseInt(a)+parseInt(e?e:0))+"px":2*(parseInt(e?e:0)+16)+"px":e?a=2*(parseInt(e?e:0)+16)+"px":"";""==g&&(g=c.getStyle(b,"text-indent")==("2em"!=h?h:a?a:"2em")?"remove":"add");"add"==g?c.setStyle(b,"text-indent","2em"!=h?h:a?a:"2em"):(a=c.getAttrib(b,"style"),a=a.replace(/text-indent?(.+?)"?[;}]/ig,""),c.setAttrib(b,"style",a))})};d.ui.registry.getAll().icons.indent2em||d.ui.registry.addIcon("indent2em",'<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="24" height="24"><path d="M170.666667 563.2v-102.4H887.466667v102.4zM170.666667 836.266667v-102.4H887.466667v102.4zM512 290.133333v-102.4H887.466667v102.4zM238.933333 341.333333V136.533333l204.8 102.4z" fill="#2c2c2c" p-id="5210"></path></svg>');
d.ui.registry.addToggleButton("indent2em",{icon:"indent2em",tooltip:"\u9996\u884c\u7f29\u8fdb",onAction:function(){k()},onSetup:function(c,f){return function(g){return c.selection.selectorChangedWithUnbind(f.join(","),g.setActive).unbind}}(d,['*[style*="text-indent"]','*[data-mce-style*="text-indent"]'])});d.ui.registry.addMenuItem("indent2em",{text:"\u9996\u884c\u7f29\u8fdb",onAction:function(){k()}});d.addCommand("indent2em",k);return{getMetadata:function(){return{name:"\u9996\u884c\u7f29\u8fdb",
url:"https://github.com/Five-great/tinymce-plugins"}}}});