import React, { FC, useEffect, useState } from 'react';
import { LeftOutlined, CloseOutlined } from '@ant-design/icons';
import { deleteQa, reqQaList, reqAgentDetail } from '@/api/assistant';
import { message, Avatar, Spin } from "antd";
import BackImg from '@/images/chatTools/back.png';
import AssistantAvatar from '@/assets/img/qa-assistant.png';
import { history, useSelector, useLocation } from "umi";
import styles from './index.less';
import './antd.less';

const HistoryChatPage: FC = () => {

    const [list, setList] = useState<any[]>([]);
    const [agentInfo, setAgentInfo] = useState<any>({});
    const [loading, setLoading] = useState(false);

    const { search } = useLocation();
    const searchParams = new URLSearchParams(search);
    const id = searchParams.get('id');

    const { fullScreenState } = useSelector<any>(
        (state: any) => state.global,
    );

    interface MenuProps {
        data: any;
        onDelete: (id: string) => void;
    }

    const MenuItem: FC<MenuProps> = ({ data, onDelete }) => {

        // 格式化agentId
        const changeFrameId = (str: string, positions: number[]) => {
            let result = '';
            let index = 0;

            positions.forEach((position) => {
                result += str.slice(index, position);
                result += '-';
                index = position;
            });

            result += str.slice(index); // 添加剩余的字符
            return result;
        }

        const toLink = (agentId: string, frameId: string) => {
            const agentIdFrame = agentId.indexOf('-') > -1 ? agentId : changeFrameId(agentId, [8, 12, 16, 20]);
            if (agentId && frameId) {
                window.location.href = `/terminator/aitools/chat?id=${agentIdFrame}&frame_id=${frameId}`
            } else {
                message.warning('当前聊天记录不存在~')
            }

        }
        return (
            <div
                className={styles.menuItemWrp}
                onClick={() => { toLink(data.application_id, data.id) }}
            >
                <div className={styles.chatRow}>
                    <Avatar
                        size={34}
                        src={agentInfo.icon}
                        icon={<img src={AssistantAvatar}></img>}
                        draggable={false}
                    />
                    <div className={styles.textInfo}>
                        <div className={styles.menuTitle}>{agentInfo.name}</div>
                        <div className={styles.menuName}>{data.name}</div>
                    </div>
                </div>
                <CloseOutlined
                    onClick={(e: any) => { e.stopPropagation(); onDelete(data.id) }}
                    className={styles.deleteIcon}
                />
            </div>
        );
    };

    // 获取历史聊天list
    const getQaList = () => {
        setLoading(true);
        return reqQaList({ application_id: id }).then((res: any) => {
            setLoading(false);
            const data = res.extend_message || [];
            setList(data);
        });
    };

    const getAgentDetails = () => {
        id && reqAgentDetail(id).then((res: any) => {
            if (res.extend_message?.records) {
                setAgentInfo(res.extend_message.records[0])
            }

        })
    }

    // 删除某条聊天记录
    const handleDeleteQa = (id: string) => {
        deleteQa({ chat_id: id }).then(() => {
            getQaList();
        });
    };

    useEffect(() => {
        getQaList();
        getAgentDetails();
    }, []);

    const toChat = () => {
        window.location.href = `/terminator/aitools/chat?id=${id}`
    }

    return (
        <div className={styles.historyChatStyle}>
            <div className={styles.header}>
                <img src={BackImg}
                    onClick={toChat}
                    style={{ width: 17, height: 14, marginRight: 8, cursor: 'pointer' }}
                />
                <span>历史对话</span>
            </div>
            {/* <div className={styles.line} /> */}
            <Spin spinning={loading} wrapperClassName="history-loading-style">
                <div className={styles.content}>
                    {list.length > 0 ?
                        list.map((item) =>
                            <MenuItem key={item.id} data={item} onDelete={handleDeleteQa} />
                        ) :
                        <span className={styles.noneTip}>暂无~</span>}
                </div>
            </Spin>
        </div>
    )
}

export default HistoryChatPage;