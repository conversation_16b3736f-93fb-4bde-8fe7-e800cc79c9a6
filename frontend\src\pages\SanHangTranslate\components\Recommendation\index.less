// ... existing code ...
.recommendation{
    padding: 20px;
    padding-bottom: 20px;
    background-color: #fff;
    margin-top: 20px;
    width: calc(100% - 40px);
    border-radius: 5px;
    margin-left: 20px;
    margin-bottom: 20px;
    .title_section {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        .square_icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        .title_text {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }
    }

    .content_section {
       

        .resource_card {
            width: 330px; /* 根据图片调整宽度 */
            height: 230px;
            background: linear-gradient( 180deg, #F4F5FC 0%, #FAFBFE 100%);
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            border: 1px solid #f0f0f0; /* 添加边框 */
            cursor: pointer;

            .card_header {
                display: flex;
                align-items: center;
                padding: 10px 15px;
                border-bottom: 1px solid #f0f0f0; /* 分隔线 */

                .card_icon {
                    width: 18px;
                    height: 18px;
                    margin-right: 8px;
                }

                .card_icon_text {
                    font-size: 14px;
                    color: #555;
                    font-weight: bold;
                }
            }

            .card_body{
                width: 100%;
                height: calc(100% - 40px);
                padding: 10px;

                .book_content{
                    background: #FFFFFF;
                    width: 100%;
                    padding: 10px;
                    height: 100%;
                    border-radius: 6px;
                    display: flex;
                    
                    .cover{
                        width: 110px;
                        height: 150px;
                        border-radius: 4px;                        
                    }

                    .title_box{
                        width: calc(100% - 120px);
                        height: 100%;
                        padding-left: 14px;                        

                        .title{
                            font-weight: 600;
                            font-size: 14px;
                            color: #2E2E2E;
                            line-height: 20px;
                            text-align: left;
                            display: block;
                        }

                        .description{
                            font-size: 12px;
                            color: #868686;
                            line-height: 17px;
                            text-align: justify;
                            // 文本超出8行显示省略号
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            -webkit-line-clamp: 8;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }
                }

                .video_content{
                    background: #FFFFFF;
                    width: 100%;
                    padding: 10px;
                    height: 100%;
                    border-radius: 6px;
                    display: flex;
                    flex-wrap: wrap;
                    
                    .cover{
                        width: 100%;
                        height: 120px;
                        border-radius: 4px;                        
                    }

                    .title_box{
                        width: 100%;
                        height: 40px;     
                        display: flex;    
                        align-items: center;            

                        .title{
                            font-size: 14px;
                            color: #2E2E2E;
                            line-height: 20px;
                            text-align: left;
                            display: block;
                        }
                    }
                }
            }
        }
    }
}