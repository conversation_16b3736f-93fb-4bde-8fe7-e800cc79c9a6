import NoteConstant, { CourseStatus } from './../constant/Note';
// import NoteConstant from "@/constant/Note";

/*
 * @Author: 李晋
 * @Date: 2021-10-29 14:27:30
 * @Email: <EMAIL>
 * @LastEditTime: 2022-04-28 19:26:08
 * @Description: file information
 * @Company: Sobey
 */
declare namespace NoteType {
  /**
   * 新增课程笔记请求参数
   */
  export interface StudyNotesRequest {
    /**
     * 入点
     */
    pointIn?: number; // int64
    /**
     * 出点
     */
    pointOut?: number; // int64
    /**
     * 课程Id
     */
    courseId?: string;
    /**
     * 课程名称
     */
    courseName?: string;
    /**
     * 小节
     */
    sectionId?: string;
    /**
     * 小节名称
     */
    sectionName?: string;
    /**
     * 资源ID
     */
    resourceId?: string;
    /**
     * 资源名称
     */
    resourceName?: string;
    /**
     * 笔记类型
     */
    noteType?: string;
    /**
     * 笔记内容
     */
    note?: string;
    /**
     * 备注信息
     */
    remark?: string;
    /**
     * 图片Base64编码字符串
     */
    imageBase64?: string;
  }
  export interface NoteSelectParam {
    searchParse?: string;
    /**
     *笔记类型('all','course','review','live')
     */
    noteType?: string;
    pageIndex?: number;
    pageSize?: number;
    orderBy?: string;
    subOrderBy?: string;
  }
  /**
   * 学习笔记
   */
  export interface StudyNotes {
    /**
     * 主键Id
     */
    id: string;
    /**
     * 新增时间
     */
    addTime?: number; // int64
    /**
     * 新增标识
     */
    addIdentityId?: string;
    /**
     * 修改时间
     */
    modifyTime?: number; // int64
    /**
     * 修改标识
     */
    modifyIdentityId?: string;
    /**
     * 是否删除
     */
    isDelete?: number; // int32
    /**
     * 备注信息
     */
    remark?: string;
    /**
     * 入点
     */
    pointIn?: number; // int64
    /**
     * 出点
     */
    pointOut?: number; // int64
    /**
     * 课程Id
     */
    courseId?: string;
    /**
     * 课程名称
     */
    courseName?: string;
    /**
     * 课程类型
     */
    courseType?: number | string;
    /**
     * 课程状态
     * 0-下架  1-上架  -1-删除 99-忽略
     */
    courseStatus?: NoteConstant.CourseStatus
    /**
     * 小节
     */
    sectionId?: string;
    /**
     * 小节名称
     */
    sectionName?: string;
    /**
     * 资源ID
     */
    resourceId?: string;
    /**
     * 资源名称
     */
    resourceName?: string;
    /**
     * 笔记类型
     */
    noteType?: string;
    /**
     * 笔记内容
     */
    note?: string;
    /**
     * 图片地址
     */
    keyframe?: string;
    /**
     * 高亮字段
     */
    highLightDto?: {
      highCourseName?: string,
      highRemark?: string
    };

    semester?: string;
    schoolYear?: string;
  }
}
