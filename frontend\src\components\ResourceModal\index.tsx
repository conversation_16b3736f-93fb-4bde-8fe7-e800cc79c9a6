import {
  folderChildren,
  getAllpoint,
  getTreebylevel,
  getmycollectionlist,
  getmyvideolist,
  queryRecommendPoint,
  queryRecommendResource,
  searchResAll,
  searchResList,
  shareMyself,
} from '@/api/addCourse';
import { IconFont } from '@/components/iconFont/iconFont_2';
import { IGlobal } from '@/models/global';
import useLocale from '@/hooks/useLocale';
import { SearchOutlined } from '@ant-design/icons';
import {
  Breadcrumb,
  Button,
  Checkbox,
  DatePicker,
  Divider,
  Image,
  Input,
  List,
  Modal,
  Pagination,
  Radio,
  Select,
  Space,
  Spin,
  Table,
  Tabs,
  Tree,
  message,
} from 'antd';
import React, { FC, ReactText, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useDispatch, useSelector } from 'umi';
import RecommendPointBox from './components/RecommendPointBox';
import RecommendSourceBox from './components/RecommendSourceBox';
import './index.less';
import CanvasResources from './components/CanvasResources';
import { importThirdResource } from '@/api/canvasapi';
// import RecommendBox from './RecommendBox'
const { Option } = Select;
const { RangePicker } = DatePicker;
const { DirectoryTree } = Tree;
const { Search } = Input;
interface ITreeItemProps {
  title: string;
  key: string;
  children?: Array<ITreeItemProps>;
}
const ResourceModal: FC<{
  visible: boolean;
  treeData: ResourceModal.treeItem[];
  onConfirm: (res: any[]) => void;
  PointConfirm?: any;
  CanvasConfirm?: any;
  showPoint?: boolean;
  onCancel: () => void;
  onShowDetail: (id: string, detail: { name: string; type: string }) => void;
  fileType?: string[];
  multi?: boolean;
  selectMode?: 'folder' | 'file';
  currentname?: string;
  isconvert?: boolean;
  copyPath?: string;
}> = ({
  visible,
  treeData,
  onConfirm,
  PointConfirm,
  CanvasConfirm,
  onCancel,
  fileType,
  multi,
  onShowDetail,
  selectMode = 'file',
  currentname = '',
  showPoint = false,
  isconvert = false,
  copyPath = ''
}) => {
    const { t } = useLocale();
    const options: Array<any> = [
      { label: t('全部'), value: '' },
      { label: t('音频'), value: 'biz_sobey_audio' },
      { label: t('视频'), value: 'biz_sobey_video' },
      { label: t('图片'), value: 'biz_sobey_picture' },
      { label: t('文档'), value: 'biz_sobey_document' },
      { label: t('文件夹'), value: 'folder' },
    ];
    const parameterConfig = useSelector<any, any>(
      ({ global }) => global.parameterConfig,
    );

    const mapinfo: any = useSelector<any, any>(
      (state: { coursemap: { mapinfo: any } }) => state.coursemap.mapinfo,
    );

    const { rmanGlobalParameter } = useSelector<
      { global: IGlobal },
      IGlobal
    >(state => state.global);
    const [loading, setLoading] = useState<boolean>(false);
    const [searchWord, setSearchWord] = useState<string>('');
    // 推荐资源的名字
    const [recommendSourceName, setRecommendSourceName] = useState('');

    const [listData, setListData] = useState<ResourceModal.listItem[]>([]);
    const [breadCrumb, setBreadCrumb] = useState<Array<any>>([
      { name: '', folderId: '', path: '' },
    ]);

    const [directoryId, setDirectoryId] = useState<ReactText>('');
    const [expandedKeys, setExpandedKeys] = useState<React.ReactText[]>([]);
    const [resType, setResType] = useState(fileType ? fileType[0] : '');
    const [analysis, setAnalysis] = useState<any[]>(['asr_status~2']);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [totalPage, setTotalPage] = useState<number>(1);

    const [starttime, setStarttime] = useState<string>('');
    const [endtime, setEndtime] = useState<string>('');
    const [folderPath, setFolderPath] = useState<any>('');
    const [loadKeys, setLoadKeys] = useState<any>([]);
    const [resource, setResource] = useState<any[]>([]);

    const [mode, setMode] = useState<'block' | 'list'>('block');

    const dispatch = useDispatch();
    const [resourceTreeData, setTreeData] = useState<Array<any>>([]); //zzh
    const [autoExpandParent, setAutoExpandParent] = useState<boolean>(true);

    const [myVideo, setMyVideo] = useState<boolean>(false); // 我的录播
    const [myCollection, setMyCollection] = useState<boolean>(false); // 我的收藏
    const [myShared, setMyShared] = useState<boolean>(false); // 我的分享
    const mySharedCurrentPath = useRef<any>(undefined); // 我的分享 当前文件名

    const confirmLoading: boolean = useSelector<Models.Store, boolean>(
      state => state.microCourse.confirmLoading,
    );
    // canvasloading
    const [canvasLoading, setCanvasLoading] = useState<boolean>(false);

    // 分页对象
    const [pagination, setPagination] = useState({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    // 推荐知识点的分页
    const [paginationRecommend, setPaginationRecommend] = useState({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    // 推荐资源
    const [paginationResource, setPaginationResource] = useState({
      current: 1,
      pageSize: 10,
      total: 0,
    });
    // 搜索知识点的输入框
    const [searchpoint, setSearchpoint] = useState<string>('');
    //推荐知识点
    const [recommendPointName, setRecommendPointName] = useState('');
    // 所有的知识点
    const [allpoint, setAllpoint] = useState<any[]>([]);
    // 是否展示加载更多
    const [hasMore, setHasMore] = useState<boolean>(true);
    //是否展示推荐资源的加载更改
    const [hasMoreRecommend, setHasMoreRecommend] = useState(true);
    const [hasMorePoint, setHasMorePoint] = useState(true);
    // 当前选中的tab
    const [tabActiveKey, setTabActiveKey] = useState<string>('1');
    // 当前选中的知识点
    const [pointResource, setPointResource] = useState<any[]>([]);
    // 当前选中的canvas资源
    const [canvasResource, setCanvasResource] = useState<any[]>([]);
    // 是否清空了
    const isClear = useRef<boolean>(false);
    //推荐的资源
    const [recommendResource, setRecommendResource] = useState<any[]>([]);
    const { userInfo } = useSelector<any, any>((state) => state.global);
    const [isAssistant, setIsAssistant] = useState<boolean>(false);

    useEffect(() => {
      getpointlist(pagination.current, pagination.pageSize, searchpoint);
      window.onbeforeunload = () => {
        sessionStorage.removeItem('cvod_resource_modal');
      };
      return () => {
        sessionStorage.removeItem('cvod_resource_modal');
        window.onbeforeunload = null;
      };
    }, []);
    useEffect(() => {
      if (userInfo) {
        const roless = userInfo.roles?.map((item: any) => item.roleCode)
        if (roless?.includes('r_teacher_assistant')) {
          setIsAssistant(true)
        } else {
          setIsAssistant(false)
        }
      }
    }, [userInfo])
    // 获取知识点列表
    const getpointlist = async (
      pageIndex: number,
      pageSize: number,
      name: string,
    ) => {
      getAllpoint({
        pageIndex,
        pageSize,
        name,
      }).then((res: any) => {
        if (res && res.success) {
          if (res.data.data.length < pagination.pageSize) {
            setHasMore(false);
          } else {
            setHasMore(true);
          }
          setPagination({
            ...pagination,
            current: pageIndex,
            pageSize: pageSize,
            total: res.data.pageTotal,
          });
          let newarr: any = [];
          res.data.data.forEach((item: any) => {
            item.knowledgePoints.forEach((item2: any) => {
              newarr.push({
                ...item2,
                contentId: item.contentId,
                name: item.name,
                createDate: item.createDate,
                suffix: item.suffix,
              });
            });
          });
          if (pageIndex == 1) {
            setAllpoint(newarr);
          } else {
            setAllpoint([...allpoint, ...newarr]);
          }
        }
      });
    };
    useEffect(() => {
      if (!currentname || !visible) {
        return;
      }
      getRecommendResource(1, 10, currentname);
      getRecommendPoint(1, 10, currentname);
    }, [currentname, visible]);
    // 获取推荐的资源
    const getRecommendResource = (
      pageIndex: number,
      pageSize: number,
      name: string,
    ) => {
      if (!name || !visible) {
        return;
      }
      queryRecommendResource({
        knowledgePointName: name,
        page: pageIndex,
        size: pageSize,
      }).then(res => {
        if (res?.success) {
          // setRecommendResource(res.data.results);
          setPaginationResource({
            ...paginationResource,
            current: pageIndex,
            pageSize: pageSize,
            total: res.data.pageTotal,
          });
          if (res.data.results.length < pagination.pageSize) {
            setHasMoreRecommend(false);
          } else {
            setHasMoreRecommend(true);
          }
          if (pageIndex == 1) {
            setRecommendResource(res.data.results);
          } else {
            setRecommendResource([...recommendResource, ...res.data.results]);
          }
        }
      });
    };
    //推荐的知识点
    const [recommendPointList, setRecommendPointList] = useState<any[]>([]);
    // 获取推荐知识点
    const getRecommendPoint = (
      pageIndex: number,
      pageSize: number,
      name: string,
    ) => {
      queryRecommendPoint(
        [name],
        { name: name, pageIndex: pageIndex, pageSize: pageSize },
      ).then(res => {
        if (res.success) {
          if (res.data.data.length < pagination.pageSize) {
            setHasMorePoint(false);
          } else {
            setHasMorePoint(true);
          }
          setPaginationRecommend({
            ...paginationRecommend,
            current: pageIndex,
            pageSize: pageSize,
            total: res.data.pageTotal,
          });
          let newarr: any = [];
          res.data.data.forEach((item: any) => {
            item.knowledgePoints.forEach((item2: any) => {
              newarr.push({
                ...item2,
                contentId: item.contentId,
                name: item.name,
                createDate: item.createDate,
                suffix: item.suffix,
              });
            });
          });

          if (pageIndex == 1) {
            setRecommendPointList(newarr);
          } else {
            setRecommendPointList([...recommendPointList, ...newarr]);
          }
        }
      });
    };

    useEffect(() => {
      setResType(fileType ? fileType[0] : '');
    }, [fileType]);

    useEffect(() => {
      console.log('rmanGlobalParameter', rmanGlobalParameter);
    }, [rmanGlobalParameter]);

    useEffect(() => {
      // 初始化list数据
      if (treeData.length > 0) {
        setLoadKeys(['myVideo', 'myCollection', 'myShared']);
        let newData: any = [];
        treeData.map((item: any) => {
          if (item.title === t('公共资源')) {
            item.children?.forEach((item: any) => {
              newData.push({ ...item, layer: 1 });
            });
          } else {
            newData.push(item);
          }
        });
        newData = newData.filter(Boolean); //过滤空对象
        console.log('rmanGlobalParameter', rmanGlobalParameter);
        if (!rmanGlobalParameter.includes('folder_group_display') && isAssistant) {
          newData.forEach((item: any, index: number) => {
            if (item.title === t('群组资源')) {
              newData.splice(1, index);
            }
          });
        }
        if (rmanGlobalParameter.includes('my_video_display') && !isAssistant) {
          newData.push({
            key: 'myVideo',
            title: t('我的录播'),
            icon: <IconFont type="iconwodelubo" />,
          });
        }
        if (rmanGlobalParameter.includes('my_collection_display') && !isAssistant) {
          newData.push({
            key: 'myCollection',
            title: t('我的收藏'),
            icon: <IconFont type="iconxingzhuang2" />,
          });
        }
        // if (rmanGlobalParameter.includes('my_shared_display')) {
        //   temp.push({
        //     key: 'myShared',
        //     title: '我的分享',
        //     icon: <IconFont type='icona-xingzhuangbeifen2' />
        //   });
        // }
        // getList(treeData[0].key, treeData[0].path, currentPage, resType);
        if (!directoryId) {
          setDirectoryId(newData[0].key);
        }

        setFolderPath(newData[0].path);
        setTreeData(newData);
      }
    }, [treeData]);
    useEffect(() => {
      if (directoryId || folderPath) {
        if (
          resType === '' &&
          searchWord === '' &&
          starttime === '' &&
          endtime === '' &&
          analysis.length === 0
        ) {
          if (myCollection) {
            searchMyCollection();
          } else {
            if (myVideo) {
              searchMyVideo();
            } else {
              if (myShared) {
                searchShareMyself();
              } else {
                getList(directoryId, folderPath, currentPage, resType);
              }
            }
          }
          // myCollection ? searchMyCollection()  : myVideo ? searchMyVideo() : myShared ? searchShareMyself() : getList(directoryId, folderPath, currentPage, resType);
        } else {
          if (myCollection) {
            searchMyCollection();
          } else {
            if (myVideo) {
              searchMyVideo();
            } else {
              if (myShared) {
                searchShareMyself();
              } else {
                getResAll(
                  directoryId,
                  resType,
                  folderPath,
                  searchWord,
                  starttime,
                  endtime,
                  currentPage,
                  analysis,
                );
              }
            }
          }
          // myCollection ? searchMyCollection()
          //   : myVideo ? searchMyVideo()
          //     : myShared ? searchShareMyself() :
          //       getResAll(
          //         directoryId,
          //         resType,
          //         folderPath,
          //         searchWord,
          //         starttime,
          //         endtime,
          //         currentPage,
          //       );
        }
      }
    }, [directoryId, folderPath, resType, currentPage, analysis]);

    useEffect(() => {
      if (!visible && directoryId) {
        sessionStorage.setItem(
          'cvod_resource_modal',
          JSON.stringify({
            directoryId,
            folderPath,
            expandedKeys,
          }),
        );
      } else if (visible) {
        const storageStr = sessionStorage.getItem('cvod_resource_modal');
        if (storageStr) {
          const { directoryId, folderPath, expandedKeys } = JSON.parse(
            storageStr,
          );

          setDirectoryId(directoryId);
          setFolderPath(folderPath);
          setExpandedKeys(expandedKeys);
          setMyVideo(directoryId === 'myVideo');
          setMyShared(directoryId === 'myShared');
          setMyCollection(directoryId === 'myCollection');
          // getList(directoryId, folderPath, 1, resType);
        }
      }
    }, [visible]);
    // 获取我的收藏
    const searchMyCollection = async () => {
      getmycollectionlist({
        pageIndex: currentPage,
        pageSize: 9,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
      }).then(res => {
        console.log('获取我的收藏', res);
        if (res && res.data && res.success) {
          const temp = res.data.results.filter(
            (item: any) => item.isDelete !== 1,
          );
          //过滤掉已失效的素材
          setTotalPage(res.data.total);
          setListData(temp);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
    };
    // 获取我的录播
    const searchMyVideo = async () => {
      const conditions =
        analysis?.map((item: string) =>
          item.includes('labels') || item.includes('word')
            ? {
              field: item.split('~')[0],
              searchRelation: Number(item.split('~')[1]),
              value: [],
            }
            : {
              field: item.split('~')[0],
              searchRelation: 0,
              value: [item.split('~')[1]],
            },
        ) ?? [];
      getmyvideolist({
        folderId: directoryId,
        keyword: [searchWord],
        folderPath: 'undefined/',
        conditions,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],

        pageIndex: currentPage,
        pageSize: 9,
      }).then(res => {
        if (res && res.data && res.success) {
          setTotalPage(res.data.recordTotal);
          setListData(res.data.data);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
    };
    const searchShareMyself = async () => {
      shareMyself({
        pageIndex: currentPage,
        pageSize: 9,
        sortFields: [
          {
            field: 'createDate_',
            isDesc: true,
          },
        ],
      }).then(res => {
        if (res && res.data && res.success) {
          mySharedCurrentPath.current = ''; //必须要清空之前选中的路径值
          setTotalPage(res.data.recordTotal);
          setListData(res.data.results);
          setTotalPage(res.data.total);
          setResource([]);
          setBreadCrumb([]);
        } else {
          setListData([]);
        }
      });
    };
    // 获取list数据
    const getList = (
      id: any,
      folderPath: any,
      currentPage: number,
      type?: string,
      analysis?: string[],
    ) => {
      const condition =
        analysis?.map((item: string) =>
          item.includes('labels') || item.includes('word')
            ? {
              field: item.split('~')[0],
              searchRelation: Number(item.split('~')[1]),
              value: [],
            }
            : {
              field: item.split('~')[0],
              searchRelation: 0,
              value: [item.split('~')[1]],
            },
        ) ?? [];
      setLoading(true);
      searchResList(
        id,
        folderPath,
        currentPage,
        type ? [type, 'folder'] : undefined,
        9,
        searchWord,
        condition,
      )
        .then((res: any) => {
          setLoading(false);
          if (res && res.success) {
            //剔除公共资源
            if (res.data.breadcrumbs[0].name === t('公共资源')) {
              res.data.breadcrumbs.splice(0, 1);
            }
            //对我的分享定制路径
            if (myShared) {
              for (let i = 0; i < res.data.breadcrumbs.length; i++) {
                if (
                  res.data.breadcrumbs[i].path === mySharedCurrentPath.current
                ) {
                  res.data.breadcrumbs.splice(0, i);
                  break;
                }
              }
              res.data.breadcrumbs.unshift({
                name: t('分享给我的'),
                folderId: '',
                path: '',
              });
            }
            setBreadCrumb(res.data.breadcrumbs);
            !myShared &&
              setFolderPath(
                res.data.breadcrumbs[res.data.breadcrumbs.length - 1].path,
              );

            setListData(res.data.data);
            setTotalPage(res.data.recordTotal);
          } else {
            message.error(t('接口返回错误!'));
            setListData([]);
          }
        })
        .catch(error => {
          console.log(error);
        });
    };
    // 深度搜索
    const getResAll = (
      id: any,
      type: string,
      path: string,
      keyword: string,
      starttime: string,
      endtime: string,
      current: number,
      analysis: string[],
    ) => {
      setLoading(true);
      const condition =
        analysis?.map((item: string) =>
          item.includes('labels') || item.includes('word')
            ? {
              field: item.split('~')[0],
              searchRelation: Number(item.split('~')[1]),
              value: [],
            }
            : {
              field: item.split('~')[0],
              searchRelation: 0,
              value: [item.split('~')[1]],
            },
        ) ?? [];
      searchResAll(
        id,
        type,
        path,
        keyword,
        starttime,
        endtime,
        current,
        isconvert,
        condition,
      )
        .then(res => {
          setLoading(false);
          if (res && res.success) {
            setListData(res.data.data);
            setTotalPage(res.data.recordTotal);
          } else {
            message.error(t('接口返回错误!'));
            setListData([]);
          }
        })
        .catch(error => {
          console.log(error);
        });
    };

    // 首字母转小写
    const firstToLower = (str: string) => {
      return str.charAt(0).toLowerCase() + str.slice(1);
    };

    const handleOk = () => {
      if (tabActiveKey == '1') {
        if (resource.length === 0 && recommendCheckList.length === 0) {
          message.warning(t('请选择资源！'));
          return;
        }

        const selectList = [...resource, ...recommendCheckList];
        onConfirm(
          selectList.map(r => ({
            ...r,
            contentId: r.contentId_,
            name: r.name_,
          })),
        );
      } else if (tabActiveKey == '2') {
        if (pointResource.length === 0 && recommendPointCheckList?.length === 0) {
          message.warning(t('请选择知识点！'));
          return;
        }

        PointConfirm([...pointResource, ...recommendPointCheckList]);
      } else {
        if (canvasResource.length === 0) {
          message.warning(t('请选择知识点！'));
          return;
        }
        const canvasResourceobj: any = [];
        canvasResource.forEach(item => {
          canvasResourceobj.push({
            "url": item.url,
            "fileName": item.display_name,
            "fileSize": item.size,
            "sourceGuid": item.uuid,
            "sourcePlatform": "canvas",
            "extend": {
              "targetFolder": "个人资源/知识图谱资源区/" + mapinfo.mapName
            }
          });
        });
        setCanvasLoading(true);
        importThirdResource(canvasResourceobj).then((res: any) => {
          if (res.success) {
            const newobj = canvasResource.map((item: any) => {
              return {
                name: res.data[firstToLower(item.uuid)].name,
                type_: res.data[firstToLower(item.uuid)].type,
                keyframe_: null,
                contentId: res.data[firstToLower(item.uuid)].contentId,
                contentId_: res.data[firstToLower(item.uuid)].contentId,
                iscanvas: true
              }
            })
            CanvasConfirm(newobj);
          }
        }).finally(() => {
          setCanvasLoading(false);
        })
      }
      // setDirectoryId('');
      !fileType && setResType('');
      setSearchWord('');
      setAnalysis(['asr_status~2']);
      setStarttime('');
      setEndtime('');
      setCurrentPage(1);
      setTotalPage(1);
      // setExpandedKeys([]);
      setResource([]);
      setPointResource([]);
      setRecommendCheckList([]);
      setRecommendPointCheckList([]);
    };
    const handleCancel = () => {
      onCancel();
      // setDirectoryId('');
    };
    // 目录选中事件
    const onSelect = (keys: React.Key[], node: any) => {
      setMyVideo(keys[0] === 'myVideo');
      if (keys[0] === 'myShared' || keys[0] === 'myCollection') setAnalysis(['asr_status~2']);
      setMyShared(keys[0] === 'myShared');
      setMyCollection(keys[0] === 'myCollection');
      // if(keys[0] === 'myShared'){

      //   return
      // }
      console.log(node);
      setDirectoryId(keys[0]);
      setExpandedKeys([...expandedKeys, keys[0]]);
      // setResType('');
      // setSearchWord('');
      setStarttime('');
      setEndtime('');
      setCurrentPage(1);
      setTotalPage(1);
      if (node.node && node.node.path) {
        //点击的左侧目录树
        setFolderPath(node.node.path);
        // getList(keys[0], node.node.path, 1, resType);
      } else {
        //选中的右侧文件夹
        console.info('当前路径111', folderPath);
        // getList(keys[0], folderPath, 1, resType);
      }
    };
    const loadData = (node: any, isRoot: boolean = false): Promise<any> => {
      const { key, children, code, title, path } = node;
      return new Promise(async resolve => {
        if (!loadKeys.includes(key)) {
          setLoadKeys([...loadKeys, key]);
        }
        if (children && children.length > 0) {
          resolve(null);
          return;
        }
        function updateTreeData(
          list: ITreeItemProps[],
          key: string,
          // id:string,
          children: ITreeItemProps[],
        ): ITreeItemProps[] {
          return list.map(node => {
            if (node.key === key) {
              return {
                ...node,
                children,
              };
            } else if (node.children) {
              return {
                ...node,
                children: updateTreeData(node.children, key, children),
              };
            }
            return node;
          });
        }

        folderChildren(path).then((res: any) => {
          if (res && res.data.success === true) {
            console.log(res);
            setTreeData(origin =>
              updateTreeData(
                origin,
                key,
                res.data.data.map((item: any) => {
                  // 对无子节点设置已加载标识实现去掉展开符并保留文件夹的图标(ps:不能使用叶子节点isLeaf标识,不然图标就是文件图标)
                  if (
                    item.childCount === 0 &&
                    !loadKeys.includes(item.contentId)
                  ) {
                    setLoadKeys((origin: any) => origin.concat([item.contentId]));
                  }
                  return {
                    title: item.name,
                    key: item.contentId,
                    path: item.path,
                  };
                }),
              ),
            );

            resolve(null);
          }
        });
      });
    };
    //zzh 2021-8-27 树形目录异步加载 封装 end
    // 目录展开事件
    const onExpand = (
      expandedKeys: React.ReactText[],
      info: {
        node: any;
        expanded: boolean;
        nativeEvent: MouseEvent;
      },
    ) => {
      setExpandedKeys(expandedKeys);
      setAutoExpandParent(false);
    };
    // 类型改变事件
    const typeChange = (value: string) => {
      setResType(value);
      setCurrentPage(1);
      // if (
      //   value === '' &&
      //   searchWord === '' &&
      //   starttime === '' &&
      //   endtime === ''
      // ) {
      //   getList(directoryId, folderPath, 1, resType);
      // } else {
      //   getResAll(
      //     directoryId,
      //     value,
      //     folderPath,
      //     searchWord,
      //     starttime,
      //     endtime,
      //     1,
      //   );
      // }
    };
    // 搜索框内容改变事件
    const searchWordChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value.replace(/(^\s*)|(\s*$)/g, '');
      setSearchWord(value);
    };
    // 搜索框失去焦点事件
    const inoutBlur = () => {
      setCurrentPage(1);
      // getResAll(
      //   directoryId,
      //   resType,
      //   folderPath,
      //   searchWord,
      //   starttime,
      //   endtime,
      //   1,
      // );
    };
    // 日期改变事件
    const dateChange = (dates: any, dateStrings: string[]) => {
      const startTime = dateStrings[0] ? dateStrings[0] + ' 00:00:00' : '';
      const endTime = dateStrings[1] ? dateStrings[1] + ' 23:59:59' : '';
      setStarttime(startTime);
      setEndtime(endTime);
      setCurrentPage(1);
      getResAll(
        directoryId,
        resType,
        folderPath,
        searchWord,
        startTime,
        endTime,
        1,
        analysis,
      );
    };
    // 搜索按钮点击事件按
    const searchClick = () => {
      if (
        resType === '' &&
        searchWord === '' &&
        starttime === '' &&
        endtime === '' &&
        analysis.length === 0
      ) {
        getList(directoryId, folderPath, currentPage, resType, analysis);
        getRecommendResource(1, 10, currentname);
      } else {
        getResAll(
          directoryId,
          resType,
          folderPath,
          searchWord,
          starttime,
          endtime,
          currentPage,
          analysis,
        );
        getRecommendResource(1, 10, searchWord);
      }
    };

    // 重置
    const reset = () => {
      searchClick();
    };

    // 导航条跳转
    const goBreadcrumb = (item: any) => {
      console.log(item);
      //针对我的分享 文件夹回退
      if (myShared) {
        if (!item.path && !item.folderId) {
          searchShareMyself();
          setBreadCrumb([]);
        } else {
          getList('', item.path, currentPage, resType, analysis);
        }
      } else {
        if (item.path !== folderPath) {
          setFolderPath(item.path);
          // getList(item.folderId, item.path, currentPage, resType);
          //取上级路径备用
          const parentPath_ = item.path.split('/');
          if (parentPath_[parentPath_.length - 1] !== 'public') parentPath_.pop(); //回退到公共资源时不能剔除最后一级路径
          // 2021-12-15 处理导航条跳转重命名bug
          searchFolderListForLeftTree(
            parentPath_.join('/'),
            getdirectory(item.path),
          );
        }
      }
    };
    // 获取最后一级的路径
    const getdirectory = (tree: any): string => {
      const directory = tree.split('/');
      if (directory.length > 0) {
        let str1 = directory[directory.length - 1];
        let str2 = directory[directory.length - 2]; //由于返回数据不同 得单独处理
        if (str1.includes('public')) {
          return t('公共资源');
        } else if (str2.includes('private')) {
          return t('个人资源');
        } else {
          return directory[directory.length - 1];
        }
      }
      return '';
    };
    // 全部素材检索--查询上级目录节点key 供左侧树同步选中
    const searchFolderListForLeftTree = async (path: string, name: string) => {
      let node: any;
      if (name === t('个人资源') || name === t('公共资源')) {
        //单独处理
        getTreebylevel().then(res => {
          if (res && res.success) {
            res.data.forEach((item: any) => {
              // console.log(item.name_)
              if (item.name === name) {
                node = item;
              }
            });
            console.log('当前上级节点', node);
            setDirectoryId(node?.id);
          } else {
            console.error(res);
          }
        });
      } else {
        searchResList('', path, currentPage, undefined, 100) //100是为了保证一次性能查完所有数据 再大了后台接口不支持
          .then((res: any) => {
            if (res && res.success) {
              res.data.data?.forEach((item: any) => {
                // console.log(item.name_)
                if (item.name_ === name) {
                  node = item;
                }
              });
            } else {
              message.error(t('接口返回错误!'));
              setListData([]);
            }
            console.log('当前上级节点', node);
            setDirectoryId(node?.contentId_);
          })
          .catch(error => {
            console.log(error);
          });
      }
    };

    // 页码改变
    const pageChange = (page: number) => {
      setCurrentPage(page);
      // if (
      //   resType === '' &&
      //   searchWord === '' &&
      //   starttime === '' &&
      //   endtime === ''
      // ) {
      //   getList(directoryId, folderPath, page, resType);
      // } else {
      //   getResAll(
      //     directoryId,
      //     resType,
      //     folderPath,
      //     searchWord,
      //     starttime,
      //     endtime,
      //     page,
      //   );
      // }
    };

    // 进入文件夹
    const resDetail = (item: any) => {
      if (item.type_ === 'folder') {
        //针对我的分享得单独处理
        if (myShared) {
          // 手动传path 避免与其他混淆
          !mySharedCurrentPath.current
            ? (mySharedCurrentPath.current = item.tree_[0] + '/' + item.name_)
            : '';
          getList('', item.tree_[0] + '/' + item.name_, 1, analysis);
        } else {
          onSelect([item.contentId_], item);
        }
      }
    };

    // 展示资源详情
    const showDetail = (item: any) => {
      let type_ = 'folder';
      let typeSplit = item.type_.split('_');
      type_ = typeSplit[typeSplit.length - 1];
      if (type_ !== 'folder') {
        onShowDetail(item.contentId_, {
          name: item.name_,
          type: type_,
        });
      }
    };
    // 选中推荐资源
    const [recommendCheckList, setRecommendCheckList] = useState<any[]>([]);
    const handleCheckRecommend = (value: any) => {
      setRecommendCheckList(value);
    };
    // 选中推荐知识点
    const [recommendPointCheckList, setRecommendPointCheckList] = useState<any[]>(
      [],
    );
    const handleCheckRecommendPoint = (value: any) => {
      setRecommendPointCheckList(value);
    };
    // 选择资源
    const itemCheckedChange = (value: any[]) => {
      if (fileType && value && value.some(v => !fileType.includes(v.type_))) {
        message.warning(t('请选择视频素材！'));
        return false;
      }
      // 取出value中的最后一项  -- 我这里的需求是只能选择一项
      const lastItem = value[value.length - 1];
      // setResource(value);
      setResource([lastItem]);
      // if (e.target.checked) {
      //   setResource((prevState) => [
      //     ...prevState,
      //     {
      //       contentId: e.target.value.contentId_,
      //       name: e.target.value.name,
      //     },
      //   ]);
      // } else {
      //   setResource((prevState) => {
      //     const newState = [...prevState];
      //     // 删除选中
      //     newState.splice(
      //       newState.findIndex(
      //         (item) => item.contentId === e.target.value.contentId_,
      //       ),
      //       1,
      //     );
      //     return newState;
      //   });
      // }
    };

    // 选择知识点
    const pointCheckedChange = (value: any[]) => {
      setPointResource(value);
    };

    const columns = [
      {
        title: t('素材名'),
        dataIndex: 'name_',
        render: (name: string, record: any) => (
          <div
            onClick={() => {
              resDetail(record);
              showDetail(record);
            }}
            className="res-title"
          >
            <Image
              src={record.keyframe_ || record.keyframe}
              className="cover-img"
              fallback="/rman/static/images/damage.png"
              preview={false}
            />

            {name}
          </div>
        ),
      },
      {
        title: t('扩展名'),
        dataIndex: 'fileext',
        width: 120,
        render: (fileext: string) => fileext || '-',
      },
      {
        title: t('入库时间'),
        width: 180,
        dataIndex: 'createDate_',
      },
      {
        title: t('语音识别'),
        width: 120,
        dataIndex: 'asr_status',
        render: (status: number, record: any) =>
          record.type_ === 'biz_sobey_audio' || record.type_ === 'biz_sobey_video'
            ? status == 1
              ? t('分析中')
              : status == 2
                ? t('分析完成')
                : status == -1
                  ? t('分析失败')
                  : t('未分析')
            : '-',
      },
      {
        title: t('是否有知识点'),
        width: 120,
        dataIndex: 'intelliState',
        render: (status: number, record: any) =>
          record.type_ === 'biz_sobey_video'
            ? status == 2
              ? t('已有')
              : t('暂无')
            : '-',
      },
    ];

    // 自定义渲染 radio
    const radiorenddom = (item: any) => {
      if (selectMode == 'folder') {
        return item.type_ == 'folder' ? (
          <Radio
            onClick={e => {
              e.stopPropagation();
            }}
            value={item}
          >
            {item.name_}
          </Radio>
        ) : (
          <span className="span_name">{item.name_}</span>
        );
      } else {
        return item.type_ !== 'folder' ? (
          <Radio
            onClick={e => {
              e.stopPropagation();
            }}
            value={item}
          >
            {item.name_}
          </Radio>
        ) : (
          <span className="span_name">{item.name_}</span>
        );
      }
    };

    const l100Ns2Hms = (l100Ns: any) => {
      let dHour: any = Math.floor(l100Ns / (3600 * Math.pow(10.0, 7)));
      if (dHour < 10) {
        dHour = '0' + dHour;
      }
      let llResidue: any = l100Ns % (3600 * Math.pow(10.0, 7));
      let dMin: any = Math.floor(llResidue / (60 * Math.pow(10.0, 7)));
      if (dMin < 10) {
        dMin = '0' + dMin;
      }
      llResidue = llResidue % Math.floor(60 * Math.pow(10.0, 7));
      let dSec: any = Math.floor(llResidue / Math.pow(10.0, 7));
      if (dSec < 10) {
        dSec = '0' + dSec;
      }
      // 判断是否小于10 否则补 0
      return `${dHour ? dHour + ':' : '00:'}${dMin ? dMin + ':' : '00:'}${dSec}`;
    };

    const onRecommenChange = (data: any) => {
      if (data.select) {
        setResource([...resource, data.select]);
      } else if (data.unselect) {
        setResource(
          resource.filter(
            (item: any) => item.contentId_ !== data.unselect.contentId_,
          ),
        );
      }
    };

    return (
      <div className="resource-model">
        <Modal
          // width="calc(100% - 445px)"
          // width="1100px"
          className="resourceModal"
          style={{ height: '72vh' }}
          visible={visible}
          onOk={handleOk}
          confirmLoading={(confirmLoading || canvasLoading)}
          onCancel={handleCancel}
          width="80%"
        // destroyOnClose={true}
        >
          <Tabs
            defaultActiveKey="1"
            activeKey={tabActiveKey}
            onChange={e => {
              setTabActiveKey(e);
              setAnalysis(['asr_status~2']);
            }}
          >
            <Tabs.TabPane tab={t('资源')} key="1">
              <div style={{ display: 'flex' }}>
                <div style={{ overflowY: 'hidden' }}>
                  <Spin tip="Loading..." spinning={loading}>
                    <div className="modal-content">
                      <div className="directory">
                        <DirectoryTree
                          defaultSelectedKeys={
                            treeData.length > 0 ? [treeData[0].key] : []
                          }
                          onSelect={onSelect}
                          // key={JSON.stringify(directoryId)}
                          autoExpandParent={autoExpandParent}
                          selectedKeys={[directoryId]}
                          loadedKeys={loadKeys}
                          expandedKeys={expandedKeys}
                          onExpand={onExpand}
                          loadData={loadData}
                          // treeData={treeData as any}
                          treeData={resourceTreeData}
                        />
                      </div>
                      <div className="content">
                        <div className="search-group">
                          <Input.Group compact>
                            <Select
                              value={resType}
                              disabled={fileType ? true : false}
                              style={{ width: '85px', textAlign: 'center' }}
                              onChange={typeChange}
                            >
                              {options.map(item => (
                                <Option key={item.value} value={item.value}>
                                  {item.label}
                                </Option>
                              ))}
                            </Select>
                            <Input
                              style={{ width: '200px' }}
                              // defaultValue={currentname}
                              // placeholder={currentname}
                              placeholder={currentname}
                              value={searchWord}
                              suffix={
                                <SearchOutlined
                                  onClick={() => {
                                    getResAll(
                                      directoryId,
                                      resType,
                                      folderPath,
                                      searchWord,
                                      starttime,
                                      endtime,
                                      currentPage,
                                      analysis,
                                    );
                                    getRecommendResource(
                                      1,
                                      10,
                                      searchWord || currentname,
                                    );
                                    setRecommendSourceName(
                                      searchWord || currentname,
                                    );
                                  }}
                                />
                              }
                              onChange={searchWordChange}
                              onBlur={inoutBlur}
                              onPressEnter={() => {
                                if (currentPage === 1) {
                                  getResAll(
                                    directoryId,
                                    resType,
                                    folderPath,
                                    searchWord,
                                    starttime,
                                    endtime,
                                    currentPage,
                                    analysis,
                                  );
                                  getRecommendResource(
                                    1,
                                    10,
                                    searchWord || currentname,
                                  );
                                } else {
                                  inoutBlur();
                                }
                                setRecommendSourceName(searchWord || currentname);
                              }}
                            />
                          </Input.Group>
                          {!myShared && !myCollection && (
                            <Select
                              value={analysis}
                              style={{ width: '260px' }}
                              placeholder={t('是否有语音文本、知识点、标签')}
                              mode="multiple"
                              disabled={true}
                              allowClear
                              onChange={(value: any) => setAnalysis(value)}
                            >
                              <Option value="asr_status~2" key="asr_status">
                                {t('有语音文本')}
                              </Option>
                              <Option value="intelliState~2" key="intelliState">
                                {t('有知识点')}
                              </Option>
                              <Option value="labels~11" key="labels">
                                {t('有标签')}
                              </Option>
                              <Option value="word~11" key="word">
                                {t('有敏感词')}
                              </Option>
                            </Select>
                          )}
                          {/* <RangePicker
                      ranges={{
                       最近一周: [moment().subtract(6, 'days'), moment()],
                       最近一个月: [moment().subtract(30, 'days'), moment()],
                       最近三个月: [moment().subtract(90, 'days'), moment()],
                      }}
                      format="YYYY-MM-DD"
                      onChange={dateChange}
                      />
                      <Button type="primary" onClick={searchClick}>
                      搜索
                      </Button> */}
                          <Space className="mode-switch-wrapper">
                            <Button
                              onClick={searchClick}
                              title={t('刷新')}
                              type="primary"
                              size="small"
                              style={{ marginRight: '10px' }}
                              icon={<IconFont type="iconshuaxin" />}
                            />

                            <div
                              className={
                                mode === 'block'
                                  ? 'mode-item active'
                                  : 'mode-item'
                              }
                              onClick={() => setMode('block')}
                            >
                              <IconFont type="iconhebingxingzhuangfuzhi2" />
                            </div>
                            <div
                              className={
                                mode === 'list' ? 'mode-item active' : 'mode-item'
                              }
                              onClick={() => setMode('list')}
                            >
                              <IconFont type="iconliebiao" />
                            </div>
                          </Space>
                        </div>
                        <div className="breadcrumb">
                          <Breadcrumb>
                            {breadCrumb.length > 3
                              ? breadCrumb.map((item: any, index: number) => (
                                <>
                                  {index === breadCrumb.length - 4 && ( //倒数第四个
                                    <Breadcrumb.Item key={index}>
                                      <a>{'...'}</a>
                                    </Breadcrumb.Item>
                                  )}

                                  {index > breadCrumb.length - 4 && ( // 只显示后三级目录
                                    <Breadcrumb.Item key={index}>
                                      <a
                                        onClick={() => goBreadcrumb(item)}
                                        title={item.name}
                                      >
                                        {item.name.length > 5
                                          ? item.name.substring(0, 5) + '...'
                                          : item.name}
                                      </a>
                                    </Breadcrumb.Item>
                                  )}
                                </>
                              ))
                              : breadCrumb.map((item: any, index: number) => (
                                <Breadcrumb.Item key={index}>
                                  <a
                                    onClick={() => goBreadcrumb(item)}
                                    title={item.name}
                                  >
                                    {item.name.length > 5
                                      ? item.name.substring(0, 5) + '...'
                                      : item.name}
                                  </a>
                                </Breadcrumb.Item>
                              ))}
                          </Breadcrumb>
                          <div>
                            <span>{t('没有需要的资源？')}</span>
                            <a
                              href="/rman/#/basic/rmanCenterList"
                              target="_blank"
                            >
                              {t('前往资源库上传')}
                            </a>
                          </div>
                        </div>
                        {multi ? (
                          <Checkbox.Group
                            name="res_check"
                            value={resource}
                            style={{ width: '100%' }}
                            onChange={itemCheckedChange}
                          >
                            {mode === 'block' ? (
                              <div className="list">
                                {listData.map(item => {
                                  return (
                                    <div
                                      className="list-item"
                                      key={item.contentId_}
                                      onClick={() => {
                                        resDetail(item);
                                      }}
                                    >
                                      <div
                                        className="img-box"
                                        onClick={() => {
                                          showDetail(item);
                                        }}
                                      >
                                        {/* {item.keyframe_ ? (
                                <img src={item.keyframe_} alt="" />
                                ) : (
                                <img src={item.keyframe} alt="" />
                                )} */}
                                        {item.keyframe_?.indexOf(
                                          '/static/images/',
                                        ) != -1 ? (
                                          <img
                                            style={{
                                              maxWidth: '60%',
                                              maxHeight: '60%',
                                            }}
                                            src={item.keyframe_}
                                            alt=""
                                            onError={() =>
                                              '/rman/static/images/damage.png'
                                            }
                                          />
                                        ) : (
                                          <Image
                                            preview={false}
                                            src={item.keyframe_}
                                            alt=""
                                            fallback="/rman/static/images/damage.png"
                                          />
                                        )}
                                      </div>
                                      <div
                                        className="item-title"
                                        title={item.name_}
                                      >
                                        {item.type_ !== 'folder' ? (
                                          <Checkbox value={item}>
                                            {item.name_}
                                          </Checkbox>
                                        ) : (
                                          <span className="span_name">
                                            {item.name_}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            ) : (
                              <Table
                                columns={[
                                  {
                                    title: '',
                                    dataIndex: 'check',
                                    key: 'check',
                                    width: 50,
                                    render: (_text: any, record: any) =>
                                      // <Checkbox value={record} />
                                      record.type_ !== 'folder' ? (
                                        <Checkbox value={record} />
                                      ) : (
                                        ''
                                      ),
                                  },
                                  ...columns,
                                ]}
                                rowKey="contentId_"
                                scroll={{ y: 'calc(100vh - 450px)' }}
                                dataSource={listData}
                                pagination={false}
                                className="res-table"
                              />
                            )}
                          </Checkbox.Group>
                        ) : (
                          <Radio.Group
                            name="res_radio"
                            style={{ width: '100%' }}
                            value={resource[0]}
                            onChange={e => {
                              itemCheckedChange([e.target.value]);
                            }}
                          >
                            {mode === 'block' ? (
                              <div className="list">
                                {listData.map(item => {
                                  return (
                                    <div
                                      className="list-item"
                                      key={item.contentId_}
                                      onClick={() => {
                                        resDetail(item); // 如果是选择文件夹就不处理
                                      }}
                                    >
                                      <div
                                        className="img-box"
                                        onClick={() => {
                                          showDetail(item);
                                        }}
                                      >
                                        {/* {item.keyframe_ ? (
                                 <img src={item.keyframe_} alt="" />
                                ) : (
                                 <img src={item.keyframe} alt="" />
                                )} */}
                                        {item.keyframe_?.indexOf(
                                          '/static/images/',
                                        ) != -1 ? (
                                          <img
                                            style={{
                                              maxWidth: '60%',
                                              maxHeight: '60%',
                                            }}
                                            src={item.keyframe_}
                                            alt=""
                                          />
                                        ) : (
                                          <Image
                                            src={item.keyframe_}
                                            alt=""
                                            fallback="/rman/static/images/damage.png"
                                            preview={false}
                                          />
                                        )}
                                      </div>
                                      <div
                                        className="item-title"
                                        title={item.name_}
                                      >
                                        {radiorenddom(item)}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            ) : (
                              <Table
                                columns={[
                                  {
                                    title: '',
                                    dataIndex: 'check',
                                    key: 'check',
                                    width: 50,
                                    render: (_text: any, record: any) => {
                                      if (selectMode == 'folder') {
                                        if (record.type_ == 'folder') {
                                          return <Radio value={record} />;
                                        } else {
                                          return (
                                            <Radio value={record} disabled />
                                          );
                                        }
                                      } else {
                                        if (record.type_ !== 'folder') {
                                          return <Radio value={record} />;
                                        } else {
                                          return (
                                            <Radio value={record} disabled />
                                          );
                                        }
                                      }
                                    },
                                  },
                                  ...columns,
                                ]}
                                rowKey="contentId_"
                                scroll={{ y: 'calc(100vh - 450px)' }}
                                dataSource={listData}
                                pagination={false}
                                className="res-table"
                              />
                            )}
                          </Radio.Group>
                        )}

                        <Pagination
                          className="pagination"
                          defaultCurrent={1}
                          total={totalPage}
                          pageSize={9}
                          showSizeChanger={false}
                          hideOnSinglePage={true}
                          onChange={pageChange}
                          current={currentPage}
                        />
                      </div>
                    </div>
                  </Spin>
                </div>
                {/* <RecommendSourceBox
                recommendList={recommendResource}
                pointName={recommendSourceName || currentname}
                checkedList={recommendCheckList}
                hasMoreRecommend={hasMoreRecommend}
                checkChange={handleCheckRecommend}
                loadMore={() => {
                  getRecommendResource(
                    paginationResource.current + 1,
                    paginationResource.pageSize,
                    searchWord || currentname,
                  );
                }}
              /> */}
              </div>
            </Tabs.TabPane>
            {showPoint && <Tabs.TabPane tab={t('知识点')} key="2">
              <div className="knowlege-container">
                <div className="knowlege-list">
                  <div>
                    <Search
                      defaultValue={searchpoint}
                      value={searchpoint}
                      placeholder={currentname || t('请输入知识点')}
                      onSearch={(e, event) => {
                        if (e == '') {
                          if (isClear.current) {
                            getpointlist(1, pagination.pageSize, '');
                          } else {
                            setSearchpoint(currentname);
                            getpointlist(1, pagination.pageSize, currentname);
                          }
                          getRecommendPoint(1, pagination.pageSize, currentname);
                        } else {
                          setSearchpoint(e);
                          getpointlist(1, pagination.pageSize, e);
                          getRecommendPoint(1, pagination.pageSize, e);
                        }
                        setRecommendPointName(e || currentname);
                      }}
                      onChange={e => {
                        if (e.target.value == '') {
                          isClear.current = true;
                        }
                        setSearchpoint(e.target.value);
                      }}
                      style={{ width: 200, marginBottom: '15px' }}
                    />
                  </div>
                  <div
                    id="scrollableDiv"
                    style={{
                      width: '100%',
                      height: 550,
                      overflow: 'auto',
                    }}
                  >
                    <Checkbox.Group
                      name="point_check"
                      value={pointResource}
                      onChange={pointCheckedChange}
                      style={{ width: '100%' }}
                    >
                      <InfiniteScroll
                        dataLength={allpoint.length}
                        next={() => {
                          getpointlist(
                            pagination.current + 1,
                            pagination.pageSize,
                            searchpoint,
                          );
                        }}
                        hasMore={hasMore}
                        loader={
                          <h4 style={{ width: '100%', textAlign: 'center' }}>
                            {t('加载中...')}
                          </h4>
                        }
                        endMessage={
                          <Divider
                            plain
                            style={{ width: '100%', textAlign: 'center' }}
                          >
                            {allpoint.length > 0
                              ? t('已加载完毕所有的知识点！')
                              : ''}
                          </Divider>
                        }
                        scrollableTarget="scrollableDiv"
                      >
                        <List
                          // grid={{ gutter: 10, column: 5 }}
                          grid={{ gutter: 10, column: 4 }}
                          dataSource={allpoint}
                          style={{ width: '99.5%' }}
                          // pagination={pagination}
                          renderItem={item => (
                            <List.Item>
                              <div className="card_point_item">
                                <div
                                  className="cover_img"
                                  onClick={() => {
                                    if (item.video_clip_path == 'document') {
                                      window.open(`/rman/#/basic/rmanDetail/${item.contentId}?showArrow=true&guid_=${item.guid_}`);
                                    } else {
                                      window.open(
                                        `/rman/#/basic/rmanDetail/${item.contentId}?inpoint=${item.inpoint}`,
                                      );
                                    }
                                  }}
                                >
                                  {item.keyframepath &&
                                    item.keyframepath != '' ? (
                                    <img
                                      src={item.keyframepath}
                                      style={{ width: '100%' }}
                                    />
                                  ) : (
                                    <img
                                      src={'/rman/static/images/video.png'}
                                      style={{ width: '40%' }}
                                    />
                                  )}

                                  <div className="time_box">
                                    <span>
                                      {l100Ns2Hms(item.outpoint - item.inpoint)}
                                    </span>
                                  </div>
                                </div>
                                <div className="point_name">
                                  <Checkbox value={item}></Checkbox>
                                  <span title={item.title} className="span_name">
                                    {item.title}
                                  </span>
                                </div>
                                <div className="point_link">
                                  <span title={t('来源：') + item.name}>
                                    {t('来源：')}
                                    {item.name}
                                  </span>
                                </div>
                              </div>
                            </List.Item>
                          )}
                        />
                      </InfiniteScroll>
                    </Checkbox.Group>
                  </div>
                </div>
                <RecommendPointBox
                  pointName={recommendPointName || currentname}
                  pointList={recommendPointList}
                  checkedList={recommendPointCheckList}
                  hasLoadMore={hasMorePoint}
                  checkChange={handleCheckRecommendPoint}
                  loadMore={() =>
                    getRecommendPoint(
                      paginationRecommend.current + 1,
                      paginationRecommend.pageSize,
                      searchpoint || currentname,
                    )
                  }
                />
              </div>
            </Tabs.TabPane>}
            {parameterConfig.show_canvas == 'true' && <Tabs.TabPane tab={t('Canvas平台')} key="3">
              <CanvasResources onSelect={(e: any) => {
                setCanvasResource(e);
              }} />
            </Tabs.TabPane>}
          </Tabs>
        </Modal>
      </div>
    );
  };

export default ResourceModal;
