/*
 * @Author: 冉志诚
 * @Date: 2023-09-14 10:29:57
 * @LastEditTime: 2023-09-14 10:30:05
 * @FilePath: \frontend\src\pages\Translate\index.tsx
 * @Description:
 */
import Header from '@/components/header/header';
import Left from '../Retrieval/components/Left';
import React, { useEffect, useState } from 'react';
import style from './index.less';
import {
  Affix,
  Button,
  Empty,
  Input,
  message,
  Upload,
} from 'antd';
import Access from '@/components/Access';
import { useImmer } from 'use-immer';
import FilePreview from '@/components/FilePreview';
import './reset.less';
import {
  blobToFile,
  changeBlobType,
  downloadBlob,
  fileToBlob,
  getBlobTypeToSuffix,
  getWords,
  isFileFormat,
  isFileSize,
  openFileDialog,
  sleep,
  stringToBlob,
  suffixToBlobType,
  txtToString,
} from '@/utils/utils';
import { DraggerProps } from 'antd/lib/upload/Dragger';
import LoadingIcon from '@/images/loading.png';
import { TranslateService } from '@/api/translate';

import { InfoModelType } from '@/db/models/infoModel';
import { Info } from '@/db/controllers/info';
import ChatContent from '../Retrieval/components/ChatContent';
import { Chatdoc } from '@/api/chatdoc';
import { AI } from '@/constant/ai';

const Retrieval: React.FC<TranslateProps> = ({
  children,
}) => {
  const [isEmpty, setIsEmpty] = useState(false);
  const [text, setText] = useState('');
  const [selectedKey, setSelectedKey] = useImmer<
    React.Key | undefined
  >(undefined);
  const [documentData, setDocumentData] = useImmer<
    Partial<InfoModelType>
  >({
    after: undefined,
  });

  // 应该查询数据库
  useEffect(() => {
    setDocumentData({
      before: undefined,
      after: undefined,
    });
  }, [selectedKey]);
  const [loading, setLoading] = useImmer({
    submit: false,
    render: false,
  });

  async function getData(id: number) {
    const result = await Info.getResultById(id);

    if (result) {
      setDocumentData(result);
      if (!result.contentId) {
        setLoading((draft) => {
          draft.submit = true;
        });
        try {
          const {
            result: { data },
          } = await Chatdoc.mainContent(
            blobToFile(result.before!, result.title),
          );
          const { choices } = data;
          const doc_id =
            (result.id ?? 0).toString() +
            Math.random()
              .toString()
              .slice(2, 10);
          const after = choices[0].content ?? '';
          const time = Date.now();
          await Info.updateResult({
            ...result,
            contentId: doc_id,
            after,
            finishTime: time,
          });
          //! 写入contentID
          setDocumentData((draft) => {
            draft.contentId = doc_id;
            draft.after = after;
            draft.finishTime = time;
          });
        } catch (error) {
          console.error(error);
          //@ts-ignore
          message.error('上传失败');
        }
        setLoading((draft) => {
          draft.submit = false;
        });
      }
    }
  }
  useEffect(() => {
    (async () => {
      setLoading((draft) => {
        draft.render = true;
      });
      if (selectedKey) {
        await getData(selectedKey as number);
      }
      setLoading((draft) => {
        draft.render = false;
      });
    })();
    setText('');
  }, [selectedKey]);
  return (
    <div id="translate">
      <Affix offsetTop={0}>
        <Header showNav={true} customTitle="文档内容总结" />
      </Affix>
      <div className={style.content}>
        <Left
          createTitle="本地上传"
          pageKey={AI.SUMMARIZE}
          setIsEmpty={setIsEmpty}
          selectedKey={selectedKey}
          setSelectedKey={setSelectedKey}
        />
        <main>
          {isEmpty || !selectedKey ? (
            <div className={style.empty}>
              <Empty
                description={
                  <div className={style.desc_empty}>
                    <p>请在左侧选择或新增上传</p>
                    <p>
                      上传文档，分析完成后将为您提取总结文档内容要点，帮助您快速了解文档信息。
                    </p>
                    <p>上传要求：PDF文档，20M以下</p>
                  </div>
                }
              />
            </div>
          ) : (
            <div className={style.renderContent}>
              <div
                className={style.before}
                id="translate-before">
                <div className={style.showArea}>
                  <Access
                    accessible={!!documentData.before}>
                    <Access accessible={!loading.submit}>
                      <FilePreview
                        blob={documentData.before}
                      />
                    </Access>
                    <Access accessible={loading.submit}>
                      <div className={style.loading}>
                        <img src={LoadingIcon} />
                        <p> 上传中... </p>
                        <p>请耐心等待</p>
                      </div>
                    </Access>
                  </Access>
                </div>
              </div>
              <div
                className={style.after}
                id="translate-after">
                <Affix
                  offsetTop={0}
                  target={() => {
                    return document.querySelector(
                      '#translate-after',
                    ) as HTMLElement;
                  }}>
                  <h2 className={style.title}>
                    <p>{documentData.title}</p>
                  </h2>
                </Affix>
                <div
                  className={style.showArea}
                  style={{ padding: 0 }}>
                  <ChatContent
                    emptyDesc="无学习资料无法开始总结"
                    defaultTime={documentData.finishTime}
                    defaultMessage={`以下是文档内容总结\n${documentData.after ??
                      ''}`}
                    sessionId={documentData.contentId}
                    readonly
                  />
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

interface TranslateProps {
  children: React.ReactNode;
}
export default Retrieval;
Retrieval.displayName = 'Translate';
