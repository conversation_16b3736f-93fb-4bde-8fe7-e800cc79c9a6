// db.ts
import Dexie, { Table } from 'dexie';

export type InfoModelType = {
  id?: number;
  /**
   * 结果
   * 时间不够不做多表联查
   * 结构为json字符串 [{isMine: true, content: 'xxx',createTime:number},]
   * */
  after?: string;
  before?: Blob;
  /** 区分页面 */
  key: string;
  /** 显示的名称 */
  title: string;
  /**后端返回的值 */
  contentId?: string;
  /** 渲染完毕时间 */
  finishTime?: number;
};
export type ChatAfterType = {
  isMine: boolean;
  content: string;
  createTime: number;
};

//! 若数据结构改变应当修改版本号
class Info extends Dexie {
  InfoModel!: Table<InfoModelType>;
  constructor() {
    super('Info');
    this.version(2).stores({
      // Primary key and indexed props 索引 不要索引文件
      InfoModel: '++id,key,contentId',
    });
  }
}

export const { InfoModel } = new Info();
