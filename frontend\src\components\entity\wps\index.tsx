import React, { FC, useEffect, useState } from 'react';
import { IBaseEntityTypes } from '@/types/entityTypes';
import Jwps from './Jwps';

const WPS: FC<IBaseEntityTypes> = ({ src }) => {
  const [simpleMode, setSimpleMode] = useState(false);

  const openWps = (url: string) => {
    const wps = Jwps.config({
      mode: simpleMode ? 'simple' : 'normal',
      mount: document.querySelector('#wps_root'),
      wpsUrl: url,
      //wpsUrl: 'http://172.16.197.171/office/s/769b71427d4b3a8efca1519197f857b8?_w_appid=NMLXMHBFPKEGUOJF&_w_contentId=0c982a229968433a9e2dcd55733ec7c8&_w_tokentype=1&_w_signature=t48TdjqkfxZSCLNTZmAy5ZBSD%2bA%3d',
    });
    wps.setToken({
      token:
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdWQiOlsiaGl2ZSJdLCJ1c2VyX2luZm8iOnsic2l0ZSI6IlMxIiwidXNlcklkIjoiMiIsImxvZ2luTmFtZSI6InN5cyIsInVzZXJuYW1lIjoic3lzIiwidXNlckNvZGUiOiJzeXMiLCJ1c2VyVHlwZSI6MCwibmlja05hbWUiOiLns7vnu5_nrqHnkIblkZgiLCJhdmF0YXIiOiIvYnVja2V0LWsvdS13ejMxZTYwa3NiOTcyNndyL3BpY3R1cmUvMjAyMy8zLzIxLzE3OTk4MzU0MzdfM0h1SGFMWmJwY042ZTllNTE0ZGY2ODBjMzhlNjM1NGRkY2I1NDdjODg2OWMucG5nIiwicm9sZXMiOlsicl9zeXNfbWFuYWdlciIsInJfY291cnNlX21hbmFnZXIiLCJyX3Jlc291cmNlX21hbmFnZXIiLCJwdWJsaWNfc3BhY2Vfcm9sZSIsInJfc2Vjb25kX21hbmFnZXIiLCJyX3RlYWNoZXIiXSwibG9ja2VkIjpmYWxzZSwiZGlzYWJsZSI6ZmFsc2UsImJvdW5kVHBhdXRoVHlwZXMiOltdLCJyb290VXNlciI6ZmFsc2UsInBhcmVudENvZGUiOiJhZG1pbiIsInR3b0ZhY3RvckNoZWNrIjpmYWxzZSwicHdkQ2hhbmdlVGltZSI6IjIwMjMtMDctMjAgMTg6MTc6MTMiLCJwd2RDaGFuZ2VQZXJpb2QiOi0xLCJhY2NvdW50Tm9uRXhwaXJlZCI6dHJ1ZSwiY3JlZGVudGlhbHNOb25FeHBpcmVkIjp0cnVlLCJhY2NvdW50Tm9uTG9ja2VkIjp0cnVlLCJlbmFibGVkIjp0cnVlLCJhZG1pbiI6ZmFsc2V9LCJncmFudF90eXBlIjoicGFzc3dvcmQiLCJ1c2VyX25hbWUiOiJzeXMiLCJzY29wZSI6WyJhbGwiXSwiZXhwIjoxNjkyOTQxMjE0LCJhdXRob3JpdGllcyI6WyJST0xFX3JfcmVzb3VyY2VfbWFuYWdlciIsIlJPTEVfcl90ZWFjaGVyIiwiUk9MRV9yX2NvdXJzZV9tYW5hZ2VyIiwiUk9MRV9yX3NlY29uZF9tYW5hZ2VyIiwiUk9MRV9wdWJsaWNfc3BhY2Vfcm9sZSIsIlJPTEVfcl9zeXNfbWFuYWdlciJdLCJqdGkiOiIwMmMyYmYzMS1iNjJiLTQ3NTItOTQ0Ni1mODI0NmQxYTk1NDAiLCJjbGllbnRfaWQiOiJTQ05UTSJ9.pPTMRJh9b3e2oHPDfkoi1cwSQVScoKeO3yxVIYx8Slw',
    });
    let app = wps.Application;
    console.log(JSON.stringify(app));
  };

  useEffect(() => {
    openWps(src);
  }, []);

  return <div id="wps_root" className="entity-wps" />;
};

export default WPS;
