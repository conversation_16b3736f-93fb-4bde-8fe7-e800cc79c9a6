.language-selection {
  width: 100%;
  height: 40px;
  margin: 20px;
  display: flex;

  .select_language{
    width: 358px;
    height: 40px;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #EDEDED;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-right: 20px;
  }

  .rideo_model{
    width: 240px;
    height: 40px;
    background: #FFFFFF;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .rideo_model_select{
      flex:1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;     
      cursor: pointer;
    }

    .active{
      background-color: var(--primary-color);
      color: #fff;
    }
  }

}

