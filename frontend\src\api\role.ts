import request from './request';

async function getAllRole(data: any) {
  return request(`/unifiedplatform/v1/role/search`, {
    method: 'post',
    data,
  });
}

async function addRole(params: any) {
  return request(`/unifiedplatform/v1/role`, {
    method: 'post',
    data: params,
  });
}

async function editRole(params: any) {
  return request(`/unifiedplatform/v1/role`, {
    method: 'patch',
    data: params,
  });
}

async function deleteRole(roleCode: string | number) {
  return request(`/unifiedplatform/v1/role/${roleCode}`, {
    method: 'delete',
  });
}

async function getBindUser(roleCode: string) {
  return request(`/unifiedplatform/v1/role/search/user/bind`, {
    method: 'post',
    data: {
      roleCode,
      page: 1,
      size: 50,
    },
  });
}

async function bindUser(
  roleCode: string,
  actionName: string,
  codes: Array<string>,
) {
  return request(`/unifiedplatform/v1/role/user/${roleCode}/${actionName}`, {
    method: 'post',
    data: codes,
  });
}

async function roleMenu(roleCode: string) {
  return request(`/unifiedplatform/v1/rolemenu/${roleCode}`, {
    method: 'get',
  });
}

async function addRoleMenu(roleCode: string, ids: Array<number>) {
  return request(`/unifiedplatform/v1/rolemenu/${roleCode}`, {
    method: 'post',
    data: ids,
  });
}

async function deleteRoleMenu(ids: Array<number>) {
  return request(`/unifiedplatform/v1/rolemenu`, {
    method: 'delete',
    data: ids,
  });
}
//查询是否有内容权限按钮权限
async function selectAuthority() {
  return request(
    `/unifiedplatform/v1/app/app/module/parameter/list?moduleCode=SystemManagement`,
    {
      method: 'GET',
    },
  );
}

// 查询系统操作日志
async function getSystemLog(params: any) {
  return request(`/learn/v1/uerlogbyes/list`, {
    method: 'post',
    data: params,
  });
}

//导出系统日志
async function exportSystemLog(params: any) {
  return request(`/learn/v1/uerlogbyes/export`, {
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}
//记录用户操作日志
async function userSystemLog(params: any) {
  return request(`/rman/v1/businessLog/add`, {
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

// 根据角色code查询关联的人员
async function getRoleUser(data: any) {
  return request(`/unifiedplatform/v1/organization/origina/users`, {
    method: 'post',
    data,
  });
}

let role = {
  getAllRole,
  addRole,
  editRole,
  deleteRole,
  getBindUser,
  bindUser,
  roleMenu,
  addRoleMenu,
  deleteRoleMenu,
  selectAuthority,
  getSystemLog,
  exportSystemLog,
  userSystemLog,
  getRoleUser
};

export default role;
