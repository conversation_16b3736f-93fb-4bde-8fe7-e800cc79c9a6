import request  from './request';
import { postStream } from '@/utils/gptMessage';
const abortController = new AbortController();
const signal = abortController.signal;

// 通过文本生成教案(重新生成)
export const reqMessage = (data: any) => {
  return postStream(`/aiclass/sse/teaching/plan/generateOneByBrief`, data)
}
// 通过文本生成教案(一次性生成)
export const reqAllMessage = (data: any) => {
  return postStream(`/aiclass/sse/teaching/plan/generateByBrief`, data)
}
// 通过文本生成教案(测试用)
export const reqMessage2 = (data: any) => {
  return postStream(`/unifiedplatform/llm/v1/chat/completions?id=cc5728b4c9ea4fa8b66afad97024b493`,data)
}
// 获取模板字段
export const reqTemplateFieldApi = (name: any) => {
  return request(`/aiclass/v1/teaching/plan/getTemplateField?templateName=${name}`, {
    method:'get'
  })
}
// 获取地图
export const reqMapApi = (data: any) => {
  return request(`/learn/m1/knowledge/query/page`, {
    method:'get',
    params:data
  })
}
// 获取课程列表(班级课和公开课)
export const reqCourseListApi = (data: any) => {
  return request(`/learn/v1/teaching/course/get/courses/list`, {
    method:'get',
    params:data
  })
}
// 获取课程列表(微课)
export const reqCourseListApi2 = (data: any) => {
  return request(`/learn/v1/course/release`, {
    method:'post',
    data:data
  })
}
// 获取课程章节
export const reqCourseChapterApi = (data: any) => {
  return request(`/learn/v1/teaching/course/get/chapter/contents`, {
    method:'get',
    params:data
  })
}
// 通过图谱生成教案
export const reqMapMessage = (data: any) => {
  return postStream(`/aiclass/sse/teaching/plan/generateOneByMapId`, data)
}
// 通过材料生成教案
export const reqFileMessage = (data: any) => {
  return fetch('/aiclass/sse/teaching/plan/generateOneByFile', {
      method: 'POST',
      body: data,
      signal
    })
}
// 通过资源库选择生成教案
export const reqResourseMessage = (data: any) => {
  return postStream(`/aiclass/sse/teaching/plan/generateOneByVideoId`, data)
}
// 通过章节生成教案
export const reqChapterMessage = (data: any) => {
  return postStream(`/aiclass/sse/teaching/plan/generateOneByChapter`, data)
}
// 导出教案
export const reqExportMessage = (data: any) => {
  return request(`/aiclass/v1/teaching/plan/export/teaching/plan`, {
    method:'post',
    data,
    responseType: 'blob'
  })
}
