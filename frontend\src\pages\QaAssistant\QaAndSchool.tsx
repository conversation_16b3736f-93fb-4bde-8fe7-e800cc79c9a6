import { Tabs } from 'antd';
import React, { FC, useEffect, useState } from 'react';
import Assistant from './index';
import ContentPage from './ContentPage/index';
import "./QaAndSchool.less";
import { useLocation } from "umi";

const QaAndSchool: FC = () => {
  const [activeKey, setActiveKey] = useState<string>("school");
  const tabItems = [
    {
      label: "校园服务助手",
      key: "school",
      children: <Assistant type="school" out />
    },
    {
      label: "智能助手",
      key: "qa",
      children: <Assistant type="qa" out />
    }
  ];
  // return <div className='qa-school-container'>
  //   <Tabs
  //     tabPosition="left"
  //     activeKey={activeKey}
  //     onChange={(key: string) => setActiveKey(key)}
  //     type='card'
  //     items={tabItems}
  //   >

  //   </Tabs>
  // </div>;

  const { search } = useLocation();
  const searchParams = new URLSearchParams(search);
  const id = searchParams.get('id');
  const frame_id = searchParams.get('frame_id') || '';

  useEffect(() => {
    const iframe = document.getElementById('chat-iframe');
    iframe && iframe.contentWindow.location.reload(true);
}, [])

  return (
    <div style={{ width: '100%', height: '100vh', overflow: 'hidden' }}>
      <iframe src={`/terminator/aitools/chat?id=${id}&frame_id=${frame_id}`} id='chat-iframe' height="100%" width="100%" style={{ border: 'none' }}></iframe>
    </div>)
};

export default QaAndSchool;