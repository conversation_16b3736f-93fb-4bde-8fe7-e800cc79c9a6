/**
 * OOS云存储SDK
 * 2022-10-08 zhangzhonghai
 * api文档：https://oos-cn.ctyunapi.cn/docs/oos/sdk/js/OOS%20JS%20SDK%E5%BC%80%E5%8F%91%E8%80%85%E6%8C%87%E5%8D%97-V6.pdf
 */
let client:any;
const OOSDK: any = {
  //初始化配置
  init:(params:any)=>{
    const s3Config = {
      accessKeyId: params.access_key_id,
      secretAccessKey: params.secret_access_key,
      endpoint: params.endpoint,//https://oos-cn.ctyunapi.cn/...
      signatureVersion: `v${params.version}`, // 可选v2 或v4
      apiVersion: '2006-03-01',
      s3ForcePathStyle: true,
      sessionToken: params.session_token
    };
     client = new OOS.S3(s3Config);
     return client
     console.log(s3Config,client);
  },
  /**
   * 这个 Get 操作返回 bucket 中部分或者全部（最多 1000）的 object 信息。用户可以在请求元素中设置选择条件来获取 bucket 中的 object 的子集。
   * params:{
   *  ......
   * }
   */
  listObjects:(params:any)=>{
    client.listObjects(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
  /**
   * 获取bucket的索引位置和数据位置
   * params:{
   *  Bucket:'string'
   * }
   */
  getBucketLocation:(params:any)=>{
    client.getBucketLocation(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
  /**
   * DELETE Bucket
   * params:{
   *  Bucket:'string'
   * }
   */
   deleteBucket:(params:any)=>{
    client.deleteBucket(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
  /**
   * Put 操作用来向指定 bucket 中添加一个对象，要求发送请求者对该 bucket 有写权限，用户必须添加完整的对象。
   * params:{
   *  Bucket 用户 bucket 名称 
      Body File 内容 
      Key 文件名
   * }
   */
    putObject:(params:any)=>{
      return new Promise((resolve, reject)=>{
        client.putObject(params,(err:any,data:any)=>{
          if(err){
            console.log(err,err.stack);
            reject(err);
          }
          else{
            console.log(data);
            resolve(data);
          }
        })
      })
    },
  /**
   * GET 操作用来检索在 OOS 中的对象信息，执行 GET 操作，用户必须对 object 所在的 bucket 有读权限。
   * params:{
      Bucket 用户 bucket 名称 
      Key 文件名 
   * }
    注：此方法并不能下载，只能获取到 Object 的 Uint8Array 编码字节
    流，若想下载，可通过 getSignedUrl 方法 return 出可下载的 url
   */
    getObject:(params:any)=>{
    client.getObject(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
  /**
   * Delete 操作移除指定的对象，要求用户要对对象所在的 bucket 拥有写权限
   * params:{
      Bucket 用户 bucket 名称 
      Key 文件名 
   * }
    注：此方法并不能下载，只能获取到 Object 的 Uint8Array 编码字节
    流，若想下载，可通过 getSignedUrl 方法 return 出可下载的 url
   */
    deleteObject:(params:any)=>{
      return new Promise((resolve, reject)=>{
        client.deleteObject(params,(err:any,data:any)=>{
          if(err){
            console.log(err,err.stack);
            reject(err);
          }
          else{
            console.log(data);
            resolve(data)
          }
        })
      })
  },
  /**
   * 本接口初始化一个分片上传（Multipart Upload）操作，并返回一个上传 ID，此 ID用来将此次分片上传操作中上传的所有片段合并成一个对象
   * params:{
     Bucket 用户 bucket 名称 
     Key 文件名 
   * }
   */
   createMultipartUpload:async(params:any,lists:any)=>{
    return new Promise((resolve, reject)=>{
      client.createMultipartUpload(params,(err:any,data:any)=>{
        if(err){
          console.log(err,err.stack);
          reject(err);
        }
        else{
          console.log(data);
          resolve(data)
        }
      })
    })
  },
  /**
   * 文件分片
   * }
   */
  splitFile:(file:any,chunkSize:number) => {
    const fileChunkList = [];
    let curChunkIndex = 0;
    while (curChunkIndex <= file.size) {
      const chunk = file.slice(curChunkIndex, curChunkIndex + chunkSize);
      fileChunkList.push({ chunk: chunk, })
      curChunkIndex += chunkSize;
    }
    return fileChunkList;
  },
  /**
   * 该接口用于实现分片上传操作中片段的上传。
   * params:{
    Bucket 分片所属的 bucket 名称 
    Key 分片的名称 
    PartNumber 分片的 id 
    UploadId 初始化分片后返回的 id
   * }
   */
  uploadPart:(params:any)=>{
    return new Promise((resolve, reject)=>{
      client.uploadPart(params,(err:any,data:any)=>{
        if(err){
          console.log(err,err.stack);
          reject(err);
        }
        else{
          console.log(data);
          resolve(data)
        }
      })
    })
  },
  /**
   * 该接口通过合并之前的上传片段来完成一次分片上传过程。
   * params:{
      Bucket 分片 object 所属 bucket 名称 是
      Key 分片的 object 名称 是
      UploadId 初始化时返回的 uploadID 是
      MultipartUpload 请求的容器。
      类型：object
      子节点：1 个或多个 Part 元素
      是
      Parts 描述 part 的信息 是
   * }
   */
  completeMultipartUpload:(params:any)=>{
    return new Promise((resolve, reject)=>{
      client.completeMultipartUpload(params,(err:any,data:any)=>{
        if(err){
          console.log(err,err.stack);
          reject(err);
        }
        else{
          console.log(data);
          resolve(data)
        }
      })
    })
  },
  /* Abort Multipart Upload
   * 该接口用于终止一次分片上传操作。分片上传操作被终止后，用户不能再通过上传 ID上传其它片段，之前已上传完成的片段所占用的存储空间将被释放。
    如果此时任何片段正 在上传，该上传过程可能会也可能不会成功。
    所以，为了释放所有片段所占用的存储空间，可能需要多次终止分片上传操作。
   * params:{
    Bucket 分片 object 所属 bucket 名称 
    Key 分片的 object 名称 
    UploadId 初始化时返回的 uploadID 
   * }
   */
  abortMultipartUpload:(params:any)=>{
    //需要多次终止
    function func_(client:any,param:any){
      client.abortMultipartUpload(param,(err:any,data:any)=>{});
    }
    func_(client,params);
    setTimeout(()=>func_(client,params),500);
    setTimeout(()=>func_(client,params),1000);
    // return new Promise((resolve, reject)=>{
    //   client.abortMultipartUpload(params,(err:any,data:any)=>{
    //     if(err){
    //       reject(err);
    //     }
    //     else{
    //       resolve(data)
    //     }
    //   })
    // })
  },
  /*List Part
   *该操作用于列出一次分片上传过程中已经上传完成的所有片段
    该操作必须包含一个通过 Initial Multipart Upload 操作获取的上传 ID。该请求最多
    返回 1000 个上传片段信息，默认返回的片段数是 1000。用户可以通过指定 max-parts
    参数来指定一次请求返回的片段数。如果用户的分片上传过程超过 1000 个片段，响应中
    的 IsTruncated 字段的值则被设置成 true，并且指定一个 NextPartNumberMarker 元
    素。用户可以在下一个连续的 List Part 请求中加入 part-number-marker 参数，并把它
    的值设置成上一个请求返回的 NextPartNumberMarker 值。
   * params:{
      Bucket 分片 object 所属 bucket 名称 是
      Key 分片的 object 名称 是
      UploadId 初始化时返回的 uploadID 是
      MaxParts Integer 设置返回的最大的分片数量 否
      PartNumberMarker Intege 设定此值，只会返回分片号大于此值得分片 否(必填标识)
   * }
   */
    listParts:(params:any)=>{
    client.listParts(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
  /**
   * 列出所有已经通过 Initiate Multipart Upload 请求初始化，但未完成或未终止的分片上传过程。
   * params:{
   *  Bucket:'string'
   * }
   */
   listMultiparyUploads:(params:any)=>{
    client.listMultiparyUploads(params,(err:any,data:any)=>{
      if(err){
        console.log(err,err.stack);
        return false;
      }
      else{
        console.log(data);
        return data;
      }
    })
  },
};
export default OOSDK;