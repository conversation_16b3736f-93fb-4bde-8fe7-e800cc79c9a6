/* http://meyerweb.com/eric/tools/css/reset/
   v2.0 | 20110126
   License: none (public domain)
*/

html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
p,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
  box-sizing: border-box;
}
/* HTML5 display-role reset for older browsers */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}
body {
  line-height: 1;
}
ol,
ul {
  list-style: none;
}
blockquote,
q {
  quotes: none;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}

#root {
  height: 100%;
}

section {
  // display: flex;
  height: 100%;
  box-sizing: border-box;
}

//// 头部背景色
//.header-bg {
//  background-color: @primary-color;
//}
// section全局背景色
//section.ant-layout {
//  background-color: @layout-color;
//}

// 侧边导航背景色
//aside.ant-layout-sider {
//  background-color: @primary-color;
//  // background-color: rgb(48, 65, 86);
//}
// #menus-list {
// 	background-color: @primary-color;
// 	color: @normal-text-color;
// 	li {
// 		color: @normal-text-color;
// 		&:hover {
// 			color: @selected-text-color;
// 		}
// 	}

// 	// span {
// 	// 	color: @normal-text-color;
// 	// 	&:hover {
// 	// 		color: @selected-text-color;
// 	// 	}
// 	// }

// }

// ------------------滚动条start定义滚动条的样式
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  background-color: #fff;
  /*滚动条的背景颜色*/
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -webkit-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  background-color: #d9d9d9;
  /*滚动条的背景颜色*/
}

::-moz-scrollbar {
  width: 8px;
  /*滚动条宽度*/
  height: 8px;
  /*滚动条高度*/
}

/*定义滚动条轨道 内阴影+圆角*/
::-moz-scrollbar-track {
  -moz-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  border-radius: 10px;
  /*滚动条的背景区域的圆角*/
  background-color: #fff;
  /*滚动条的背景颜色*/
}

/*定义滑块 内阴影+圆角*/
::-moz-scrollbar-thumb {
  border-radius: 10px;
  /*滚动条的圆角*/
  -moz-box-shadow: inset 0 0 0 rgba(0, 0, 0, 0);
  background-color: #d9d9d9;
  /*滚动条的背景颜色*/
}
// ------------------滚动条end

#root .icon-selected {
  background-color: rgb(17, 216, 17);
}

.ant-table,
.ant-spin-blur {
  clear: initial !important;
}

._inner_page_container {
  overflow: auto;
}

.ant-table-tbody > tr.ant-table-row-selected > td {
  background: #ffffff !important;
  border: initial !important;
}

.hook-pagination {
  display: flex;
  justify-content: center !important;
}
.in-pro-table {
  .ant-pro-card-body {
    padding: 0 !important;
  }
  .ant-pro-table-list-toolbar-container {
    padding-top: 0 !important;
  }
}
.in-pro-search {
  form {
    .ant-pro-query-filter-row {
      // 选择最后一个
      .ant-col:last-child {
        margin-left: 0;
        flex: 1;
        max-width: 100%;
        .ant-space {
          width: 100%;
          .ant-space-item {
            width: 100%;
            > div {
              width: 100%;
              justify-content: space-between;
            }
          }
        }
      }
    }
  }
}
.in-search,
.in-pro-search {
  > form {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
.hook-pagination {
  margin-bottom: 10px !important;
}
#other-app-header {
  height: 100%;
  #translate {
    overflow: hidden;
  }
  #translate-before,
  #translate-after,
  #left {
    max-height: calc(100vh - 70px);
  }
}
#normal-app-header {
  height: 100%;
}

@media (min-width: 1920px) {
  html {
    font-size: 16px;
  }
}
@media (max-width: 1680px) {
  html {
    font-size: 14px;
  }
}
@media (max-width: 1440px) {
  html {
    font-size: 12px;
  }
}
body {
  font-size: 14px;
}
.in-pro-table-search {
  > form {
    padding-left: 0 !important;
    padding-right: 0 !important;
    .ant-col {
      .ant-form-item-control-input-content {
        .ant-space {
          display: flex;
          align-items: flex-start;
        }
      }
    }
  }
}

.in-pro-table-search {
  margin-bottom: 0 !important;
  form {
    .ant-pro-query-filter-row {
      // 选择最后一个
      .ant-col:last-child {
        margin-left: 0;
        flex: 1;
        max-width: 100% !important;
        .ant-space {
          width: 100%;
          justify-content: flex-end;
          .ant-space-item {
            // width: 100%;
            > div {
              width: 100%;
              justify-content: flex-end;
            }
          }
        }
      }
    }
  }
}
