import React, { FC } from 'react';
import './index.less';
import { Avatar, message } from 'antd';
import IconFont from '@/components/iconFont/iconFont';
import { deepCopy } from '../../utils';
import AssistantAvatar from '@/assets/img/qa-assistant.png';
import { IGlobal, useSelector } from 'umi';
import defaultAvatar from '@/assets/img/defualt-avatar.png';
import { copy } from '@/utils/utils';

interface ChatItemProps {
  tag?: string;
  data?: any;
  isMine?: boolean;
  content?: any;
  typing?: boolean;
  create_time?: string;
  children?: any;
}

const ChatItem: FC<ChatItemProps> = ({
  tag,
  data,
  isMine,
  content,
  typing,
  children,
  create_time,
}) => {
  const { userInfo } = useSelector<any, IGlobal>(
    (state) => state.global,
  );
  const handleCopy = () => {
    const data = children ?? content;
    // debugger;
    const text = deepCopy(data);
    // navigator.clipboard.writeText(text).catch(() => {
    //   message.warning('复制失败');
    // });
    copy(text);
  };
  return (
    <div
      className={`chat-item-wrp ${isMine ? 'mine' : ''} ${
        tag ? tag : ''
      }`}>
      <div className="avatar">
        <Avatar
          size={34}
          src={isMine ? userInfo.avatar : AssistantAvatar}
          icon={<img src={defaultAvatar}></img>}
          draggable={false}
        />
      </div>
      <div className="chat-message-box">
        <div className="chat-message-content">
          <div>{children ?? content}</div>
        </div>
        <div className="chat-message-date">
          <div className="copy-btn" onClick={handleCopy}>
            <IconFont type="iconcopy" />
            复制
          </div>
          {!typing && (
            <div className="date">{create_time}</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatItem;
