import React from "react";
import './RecommendBox.less'
import { Checkbox } from "antd";
import { IconFont } from '@/components/iconFont';
import { useSelector } from 'umi';

const RecommendBox = ({ onChange }: any) => {
  const data: any = {
    "standard": "NTSC",
    "intelliState": "2",
    "privilegeUserGroup_": [
      "owner_admin",
      "owner_sys",
      "share_read_72dd663ccca74ac5a1fe91d7d7ca9af2",
      "share_execute_72dd663ccca74ac5a1fe91d7d7ca9af2",
      "share_read_b1e500e0e8864698acbf1f94e84590f6",
      "share_execute_b1e500e0e8864698acbf1f94e84590f6"
    ],
    "asr_status": "0",
    "framerate": 30,
    "source": "Web上传",
    "filesize": 40339079,
    "type": "clip",
    "importuser": "系统管理员",
    "order_": 1,
    "name_": "高等数学 洛必达法则",
    "audioformat": "AAC 2ch-16bits QT_MOV",
    "duration": 2963333333,
    "theme_info": [
      {
        "level": 0,
        "theme_code": "CA03",
        "theme_name": "理学"
      }
    ],
    "lastUpdate_": {
      "site": "S1",
      "user": "admin",
      "timestamp": "2023-10-08 19:31:43"
    },
    "subtype": "av",
    "createDate_": "2022-06-16 17:57:45",
    "theme": [
      "CA03"
    ],
    "superTaskInstanceID": "b516a2a427224ee3b96ea002cdad457e",
    "createDate": "2022-06-16 17:57:45",
    "createUser_": "sys",
    "ocr_status": "0",
    "system_": "SWFBB",
    "creator": "sys",
    "course_name": "高等数学 洛必达法则",
    "ascription": "MAM",
    "usestatus": "false",
    "smart_status": "0",
    "import_review_status": 1,
    "fileext": "MOV",
    "privilege_": "public",
    "contentId_": "b1e500e0e8864698acbf1f94e84590f6",
    "aspectratio": "16:9",
    "hits": 5,
    "keyframe_": "/bucket-k/u-wz31e60ksb9726wr/video/2022/06/16/b1e500e0e8864698acbf1f94e84590f6/f1c3a43ff60e46b080628ce621417764.jpg",
    "name": "高等数学 洛必达法则",
    "schedule_id": "31c1b784dc0c4c76a9763da17338c20f",
    "videoformat": "H264 HD_1920_1080_30P QT_MOV",
    "type_": "biz_sobey_video",
    "site_": "S1",
    "tree_": [
      "global_sobey_defaultclass/public/共享资源/高等数学"
    ],
    "operateCode_": 15,
    "model_sobey_cata_sequencemeta": "例题1",
    "model_sobey_smart_voice_": "",
    "model_sobey_keywords": "",
    "labels": [],
    "damaged": false,
    "shared_state": 0,
    "isCollection": false,
    "total_copy": 0,
    "total_download": 0,
    "total_like": 0,
    "total_hits": 2,
    "total_collect": 0,
    "total_share": 0,
    "total_micr": 0,
    "total_knowledge": 0,
    "isCopy": false,
    "isDownload": false,
    "isLike": false,
    "isHits": true,
    "isCollect": false,
    "isShare": false,
    "isMicr": false,
    "isKnowledge": false
  }
  const baseUrl = 'https://zhiliao.sobeylingyun.com'
  const courseDetail = useSelector<Models.Store, any>(
    state => state.moocCourse.courseDetail,
  );
  const onSelect = (e: any) => {
    if (e.target.checked) {
      onChange({ select: data })
    } else {
      onChange({ unselect: data })
    }
  }
  return (
    <div className="recommend_box">
      <div className="recommend_title">
        <IconFont type="iconzhinengtuijian" />
        <span>智能推荐</span>
      </div>
      <div className="tips">以下为 <span>{courseDetail?.entityData?.name || ''}</span> 课程相关备课资源推荐</div>
      <div className="list">
        <div className="item">
          <img src={baseUrl + data.keyframe_} alt="" />
          <div className="content">
            <div className="name_wrap">
              <Checkbox onChange={onSelect} />
              <span>{data.name}</span>
            </div>
            <div className="info">
              <div>
                <span>关键词：</span>
                <span className="tag">公式</span>
                <span className="tag">幂次</span>
              </div>
              <div>
                <span>文件大小：</span>
                <span>134M</span>
              </div>
              <div>
                <span>所属目录：</span>
                <span>共享文件夹/高等数学</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RecommendBox; 