/**
 * 获取一个递归函数,处理流式数据
 * @param reader  流数据
 * @param stream  是否是流式数据
 */
export const getWrite = (reader: any, stream: boolean, callback: (content: any, config?: any, finish: boolean) => void) => {
    let tempResult = '';
    let responseText = '';
    let configTools: any = null;
    /**
     *
     * @param done  是否结束
     * @param value 值
     */
    const write_stream = ({ done, value }: { done: boolean; value: any }) => {
        try {
            if (done) {
                return
            }
            const decoder = new TextDecoder('utf-8')
            let str = decoder.decode(value, { stream: true })
            // 这里解释一下 start 因为数据流返回流并不是按照后端chunk返回 我们希望得到的chunk是data:{xxx}\n\n 但是它获取到的可能是 data:{ -> xxx}\n\n 总而言之就是 fetch不能保证每个chunk都说以data:开始 \n\n结束
            tempResult += str
            const split = tempResult.match(/data:.*}\n\n/g)
            if (split) {
                str = split.join('')
                tempResult = tempResult.replace(str, '')
            } else {
                return reader.read().then(write_stream)
            }
            // 这里解释一下 end
            if (str && str.startsWith('data:')) {
                if (split) {
                    for (const index in split) {
                        const chunk = JSON?.parse(split[index].replace('data:', ''));
                        const content = chunk?.content;

                        if (chunk && chunk?.role === 'assistant') {
                            responseText += content;
                        }
                        
                        if (chunk && chunk?.role === 'tools') {
                            configTools = chunk;
                        }

                        if (chunk && chunk?.role === 'assistant' && !configTools && chunk?.event === 'finish') {
                            configTools = chunk; // 返回的配置信息
                        }

                        chunk && chunk?.role === 'assistant' && callback(content, configTools, false);
                        if (chunk.event === 'finish') {
                            // 流处理成功 返回成功回调
                            callback(responseText, configTools, true);
                            return Promise.resolve()
                        }
                    }
                }
            }
        } catch (e) {
            return Promise.reject(e)
        }
        return reader.read().then(write_stream)
    }
    /**
     * 处理 json 响应
     * @param param0
     */
    const write_json = ({ done, value }: { done: boolean; value: any }) => {
        if (done) {
            const result_block = JSON.parse(tempResult)
            if (result_block.code === 500) {
                return Promise.reject(result_block.message)
            } else {
                if (result_block.content) {
                    // console.info('content', 'content', result_block.content)
                }
            }
            return
        }
        if (value) {
            const decoder = new TextDecoder('utf-8')
            tempResult += decoder.decode(value)
        }
        return reader.read().then(write_json)
    }
    return stream ? write_stream : write_json
}