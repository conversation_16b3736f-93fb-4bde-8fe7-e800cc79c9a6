import Header from "@/components/header/header";
import React, { FC, useEffect, useState } from "react";
import { Affix } from "antd";
import Left from "./components/Left";
import ChatContent from "./components/ChatContent";
import "./index.less";
import { IGlobal, useSelector } from "umi";

interface AssistantProps {
  type: "school" | "qa";
  out: boolean;
}

const Assistant: FC<AssistantProps> = ({ type, out }) => {
  const [sessionId, setSessionId] = useState<string>("");
  const { userInfo } = useSelector<any, IGlobal>((state) => state.global);
  const [chatUser, setChatUser] = useState<string>("");
  const [isOut, setIsOut] = useState<boolean>(false);
  const [uid, setUid] = useState<number>(0);
  const [isChat, setIsChat] = useState<boolean>(false);

  useEffect(() => {
    if (userInfo.userCode) {
      setChatUser(`${userInfo.userCode}_${type === "school" || location.hash.includes("schoolAssistant") ? 'school' : 'qa'}`);
    }
    setIsOut(out || false);
  }, [userInfo, type]);


  return (
    <div className={`qa-assistant-container ${isOut || window.location.hash.includes("outQaAssistant") ? 'out-container' : ''}`}>
      {!isOut && !window.location.hash.includes("outQaAssistant") && <Affix offsetTop={0}>
        <Header showNav={true} customTitle={`${location.hash.includes("schoolAssistant") ? '校园服务助手' : '知识问答助手'}`} />
      </Affix>}
      <div className="content-wrp">
        <Left chatUser={chatUser} isOut={isOut} onSelect={(id: string) => setSessionId(id)} titleUid={uid} isChat={isChat} />
        <ChatContent chatUser={chatUser} isOut={isOut} sessionId={sessionId} titleChange={() => setUid((pre: number) => pre + 1)} chatStateChange={setIsChat} />
      </div>
    </div>
  );
};

export default Assistant;