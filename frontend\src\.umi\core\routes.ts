// @ts-nocheck
import React from 'react';
import { ApplyPluginsType, dynamic } from 'D:/公司项目/云上川大/aitools/frontend/node_modules/@umijs/runtime';
import * as umiExports from './umiExports';
import { plugin } from './plugin';
import LoadingComponent from '@/components/loading/loading';

export function getRoutes() {
  const routes = [
  {
    "path": "/sanhang",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Layouts__SanHangLayout' */'@/pages/Layouts/SanHangLayout'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/sanhang",
        "exact": true,
        "redirect": "/sanhang/translate"
      },
      {
        "path": "/sanhang/translate",
        "name": "AI工具集",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__SanHangTranslate' */'@/pages/SanHangTranslate'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/sanhang/touches",
        "name": "AI工具集",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Touches' */'@/pages/Touches'), loading: LoadingComponent}),
        "exact": true
      }
    ]
  },
  {
    "path": "/",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Layouts' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Layouts'), loading: LoadingComponent}),
    "routes": [
      {
        "path": "/",
        "exact": true,
        "redirect": "/overview"
      },
      {
        "path": "/translate",
        "name": "翻译",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Translate' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Translate'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/qaAssistant",
        "name": "QaAssistant",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QaAssistant' */'@/pages/QaAssistant'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/outQaAssistant",
        "name": "OutQaAssistant",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QaAssistant' */'@/pages/QaAssistant'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/schoolAssistant",
        "name": "QaAssistant",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QaAssistant' */'@/pages/QaAssistant'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/outChat",
        "name": "OutChat",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QaAssistant__QaAndSchool' */'@/pages/QaAssistant/QaAndSchool.tsx'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/historyChat",
        "name": "HistoryChat",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__QaAssistant__historyChat__index' */'@/pages/QaAssistant/historyChat/index.tsx'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/treasureBox",
        "name": "TreasureBox",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TreasureBox__index' */'@/pages/TreasureBox/index.tsx'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/retrieval",
        "name": "文档信息检索",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Retrieval' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Retrieval'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/summarize",
        "name": "文档内容总结",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Summarize' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Summarize'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/intelligentImage",
        "name": "智能识图工具",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__IntelligentImage' */'D:/公司项目/云上川大/aitools/frontend/src/pages/IntelligentImage'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/toGif",
        "name": "视频转GIF工具",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Togif' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Togif'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/clip",
        "name": "智能剪辑工具",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Clip' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Clip'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/overview",
        "name": "AI工具集",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__Overview' */'D:/公司项目/云上川大/aitools/frontend/src/pages/Overview'), loading: LoadingComponent}),
        "exact": true
      },
      {
        "path": "/teachingPlan",
        "name": "智能教案",
        "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__TeachingPlan' */'D:/公司项目/云上川大/aitools/frontend/src/pages/TeachingPlan'), loading: LoadingComponent}),
        "exact": true
      }
    ]
  },
  {
    "path": "/*",
    "name": "404 not found",
    "component": dynamic({ loader: () => import(/* webpackChunkName: 'p__404' */'D:/公司项目/云上川大/aitools/frontend/src/pages/404'), loading: LoadingComponent}),
    "exact": true
  }
];

  // allow user to extend routes
  plugin.applyPlugins({
    key: 'patchRoutes',
    type: ApplyPluginsType.event,
    args: { routes },
  });

  return routes;
}
