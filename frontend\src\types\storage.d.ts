declare namespace Storage {
  // 标签
  type Mark = {
    mark_name: string;
    mark_code: string;
  };

  type MarkItem = {
    id: string;
    create_code: string;
    create_time: string;
    site_code: string;
  } & Mark;

  type UpdateMark = {
    id: string;
  } & Mark;

  type DeleteMark = {
    id: string;
    mark_code: string;
  };
  // 访问item
  type AccessItem = {
    id: string;
    access_type: string;
    path: string;
    access_user: string;
    access_pwd: string;
    disabled: boolean;
    path_type: string;
    unitCode?: string;
  };

  type ManageItem = {
    fieldName: string;
    code: string;
    name: string;
    type: string;
    value: string;
  };

  // 存储
  type StorageItem = {
    id?: string;
    // 存储code
    storage_code: string;
    // 存储名称
    storage_name: string;
    // 是否超额分配
    assign_over: boolean;
    // 存储大小（单位B）
    storage_size: number;
    // 描述
    description: string;
    // 是否禁用
    disabled: boolean;
    // 站点code
    site_code: string;
    // 分配大小
    assigned_size?: number;
    // 使用大小
    used_size?: number;
    // 存储标签
    storage_marks: Mark[];
    // 访问明细
    access_details: AccessItem[];
    // 存储类型（NAS/OSS）
    storage_type: string;
    // 管理方式
    manage_type: string;
    // 管理方式
    manage_params: ManageItem[];
  };
  // 查询query
  type Query = {
    page: number;
    size: number;
    disabled?: boolean;
  };

  // unit
  type Unit = {
    storage_code: string;
    storage_size: number;
    unit_name?: string;
    unit_code?: string;
    storage_marks: Mark[];
  };

  type Link = {
    link_type: string;
    link_value: string;
  };
  // 创建pool参数
  type PoolLink = {
    shared: boolean;
    link: {
      type: string;
      value: string;
    };
    unitList: Unit[];
    description: string;
  };

  type PoolInfo = {
    id: string;
    pool_code: string;
    pool_name: string;
    description: string;
  };

  type Pool = PoolLink & PoolInfo;

}
